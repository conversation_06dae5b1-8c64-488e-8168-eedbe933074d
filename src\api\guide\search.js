import request from '@/utils/request'

// 搜索物品
export function searchItems(query) {
  return request({
    url: '/guide/search/items',
    method: 'get',
    params: query
  })
}

// 开始导寻
export function startGuide(data) {
  return request({
    url: '/guide/search/start',
    method: 'post',
    data: data
  })
}

// 获取导寻状态
export function getGuideStatus(guideId) {
  return request({
    url: '/guide/search/status/' + guideId,
    method: 'get'
  })
}

// 停止导寻
export function stopGuide(guideId) {
  return request({
    url: '/guide/search/stop/' + guideId,
    method: 'post'
  })
}
