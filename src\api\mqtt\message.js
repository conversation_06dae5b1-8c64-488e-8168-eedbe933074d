import request from '@/utils/request'

// MQTT 消息管理 API

/**
 * 获取消息历史
 */
export function getMessageHistory(params) {
  return request({
    url: '/api/mqtt/messages/history',
    method: 'get',
    params
  })
}

/**
 * 获取保留消息
 */
export function getRetainedMessages(params) {
  return request({
    url: '/api/mqtt/messages/retained',
    method: 'get',
    params
  })
}

/**
 * 删除保留消息
 */
export function deleteRetainedMessage(topic) {
  return request({
    url: `/api/mqtt/messages/retained`,
    method: 'delete',
    params: { topic }
  })
}

/**
 * 清理过期消息
 */
export function cleanupExpiredMessages(maxAge) {
  return request({
    url: '/api/mqtt/messages/cleanup',
    method: 'post',
    data: { maxAge }
  })
}

/**
 * 发布消息
 */
export function publishMessage(data) {
  return request({
    url: '/api/mqtt/messages/publish',
    method: 'post',
    data
  })
}

/**
 * 批量发布消息
 */
export function batchPublishMessages(messages) {
  return request({
    url: '/api/mqtt/messages/batch-publish',
    method: 'post',
    data: { messages }
  })
}

/**
 * 获取消息统计
 */
export function getMessageStatistics(params) {
  return request({
    url: '/api/mqtt/messages/statistics',
    method: 'get',
    params
  })
}

/**
 * 搜索消息
 */
export function searchMessages(params) {
  return request({
    url: '/api/mqtt/messages/search',
    method: 'get',
    params
  })
}

/**
 * 导出消息历史
 */
export function exportMessageHistory(params) {
  return request({
    url: '/api/mqtt/messages/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 获取消息详情
 */
export function getMessageDetails(messageId) {
  return request({
    url: `/api/mqtt/messages/${messageId}`,
    method: 'get'
  })
}

/**
 * 重发消息
 */
export function resendMessage(messageId) {
  return request({
    url: `/api/mqtt/messages/${messageId}/resend`,
    method: 'post'
  })
}

/**
 * 获取主题消息统计
 */
export function getTopicMessageStats(topic, params) {
  return request({
    url: `/api/mqtt/messages/topic-stats`,
    method: 'get',
    params: { topic, ...params }
  })
}
