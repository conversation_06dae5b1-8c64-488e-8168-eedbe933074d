import request from '@/utils/request'

// 查询容器信息列表
export function listContainer(query) {
  return request({
    url: '/warehouse/container/list',
    method: 'get',
    params: query
  })
}

// 查询容器信息详细
export function getContainer(id) {
  return request({
    url: '/warehouse/container/' + id,
    method: 'get'
  })
}

// 新增容器信息
export function addContainer(data) {
  return request({
    url: '/warehouse/container',
    method: 'post',
    data: data
  })
}

// 修改容器信息
export function updateContainer(data) {
  return request({
    url: '/warehouse/container',
    method: 'put',
    data: data
  })
}

// 删除容器信息
export function delContainer(id) {
  return request({
    url: '/warehouse/container/' + id,
    method: 'delete'
  })
}

// 根据容器编号获取仓库信息
export function getWarehouseByContainerCode(containerCode) {
  return request({
    url: '/warehouse/container/warehouse/' + containerCode,
    method: 'get'
  })
}

// 验证容器号唯一性
export function checkContainerNoUnique(containerNo) {
  return request({
    url: '/warehouse/container/checkContainerNoUnique',
    method: 'get',
    params: { containerNo }
  })
}

// 获取容器选择器列表
export function listContainerSelector() {
  return request({
    url: '/warehouse/container/selector',
    method: 'get'
  })
}
