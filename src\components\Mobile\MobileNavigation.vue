<template>
  <div class="mobile-navigation">
    <!-- 移动端菜单 -->
    <div class="mobile-menu" :class="{ 'is-open': menuVisible }">
      <div class="menu-header">
        <div class="user-info">
          <el-avatar :size="50" :src="userInfo.avatar">
            <i class="el-icon-user-solid"></i>
          </el-avatar>
          <div class="user-details">
            <div class="user-name">{{ userInfo.name }}</div>
            <div class="user-role">{{ userInfo.role }}</div>
          </div>
        </div>
        <el-button type="text" icon="el-icon-close" @click="closeMenu" class="close-btn" />
      </div>
      
      <div class="menu-content">
        <div class="menu-section">
          <div class="section-title">主要功能</div>
          <div class="menu-items">
            <div
              class="menu-item"
              v-for="item in mainMenuItems"
              :key="item.path"
              :class="{ active: isActive(item.path) }"
              @click="navigateTo(item.path)"
            >
              <i :class="item.icon"></i>
              <span>{{ item.name }}</span>
              <i class="el-icon-arrow-right" v-if="item.children"></i>
            </div>
          </div>
        </div>
        
        <div class="menu-section">
          <div class="section-title">系统管理</div>
          <div class="menu-items">
            <div
              class="menu-item"
              v-for="item in systemMenuItems"
              :key="item.path"
              :class="{ active: isActive(item.path) }"
              @click="navigateTo(item.path)"
            >
              <i :class="item.icon"></i>
              <span>{{ item.name }}</span>
            </div>
          </div>
        </div>
        
        <div class="menu-section">
          <div class="section-title">快捷操作</div>
          <div class="quick-actions">
            <div class="action-item" @click="handleQuickAction('scan')">
              <i class="el-icon-camera"></i>
              <span>扫码</span>
            </div>
            <div class="action-item" @click="handleQuickAction('search')">
              <i class="el-icon-search"></i>
              <span>搜索</span>
            </div>
            <div class="action-item" @click="handleQuickAction('add')">
              <i class="el-icon-plus"></i>
              <span>新增</span>
            </div>
            <div class="action-item" @click="handleQuickAction('report')">
              <i class="el-icon-document"></i>
              <span>报表</span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="menu-footer">
        <div class="footer-actions">
          <el-button type="text" @click="handleSettings">
            <i class="el-icon-setting"></i>
            设置
          </el-button>
          <el-button type="text" @click="handleLogout">
            <i class="el-icon-switch-button"></i>
            退出
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 底部标签栏 */
    <div class="bottom-tabs" v-if="showBottomTabs">
      <div
        class="tab-item"
        v-for="tab in bottomTabs"
        :key="tab.path"
        :class="{ active: isActive(tab.path) }"
        @click="navigateTo(tab.path)"
      >
        <div class="tab-icon">
          <i :class="tab.icon"></i>
          <el-badge
            v-if="tab.badge"
            :value="tab.badge"
            :max="99"
            class="tab-badge"
          />
        </div>
        <span class="tab-label">{{ tab.name }}</span>
      </div>
    </div>
    
    <!-- 浮动操作按钮 -->
    <div class="floating-action-button" v-if="showFab" @click="handleFabClick">
      <i :class="fabIcon"></i>
    </div>
    
    <!-- 快速操作面板 -->
    <el-drawer
      title="快速操作"
      :visible.sync="quickActionVisible"
      direction="btt"
      size="60%"
      custom-class="quick-action-drawer"
    >
      <div class="quick-action-grid">
        <div
          class="quick-action-card"
          v-for="action in quickActions"
          :key="action.key"
          @click="executeQuickAction(action)"
        >
          <div class="action-icon" :style="{ backgroundColor: action.color }">
            <i :class="action.icon"></i>
          </div>
          <div class="action-info">
            <div class="action-title">{{ action.title }}</div>
            <div class="action-desc">{{ action.description }}</div>
          </div>
        </div>
      </div>
    </el-drawer>
    
    <!-- 扫码组件 -->
    <el-dialog
      title="扫码"
      :visible.sync="scanVisible"
      width="90%"
      custom-class="scan-dialog"
    >
      <div class="scan-container">
        <div class="scan-area">
          <div class="scan-frame"></div>
          <div class="scan-tips">请将二维码/条形码放入框内</div>
        </div>
        <div class="scan-controls">
          <el-button @click="toggleFlash">
            <i :class="flashOn ? 'el-icon-turn-off' : 'el-icon-open'"></i>
            {{ flashOn ? '关闭' : '开启' }}闪光灯
          </el-button>
          <el-button @click="switchCamera">
            <i class="el-icon-refresh"></i>
            切换摄像头
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'MobileNavigation',
  
  props: {
    // 是否显示菜单
    menuVisible: {
      type: Boolean,
      default: false
    },
    // 是否显示底部标签栏
    showBottomTabs: {
      type: Boolean,
      default: true
    },
    // 是否显示浮动操作按钮
    showFab: {
      type: Boolean,
      default: true
    },
    // 浮动按钮图标
    fabIcon: {
      type: String,
      default: 'el-icon-plus'
    },
    // 用户信息
    userInfo: {
      type: Object,
      default: () => ({
        name: '管理员',
        role: '系统管理员',
        avatar: ''
      })
    }
  },
  
  data() {
    return {
      // 快速操作面板显示状态
      quickActionVisible: false,
      // 扫码对话框显示状态
      scanVisible: false,
      // 闪光灯状态
      flashOn: false,
      
      // 主要菜单项
      mainMenuItems: [
        { path: '/dashboard', name: '工作台', icon: 'el-icon-s-home' },
        { path: '/warehouse', name: '仓库管理', icon: 'el-icon-s-shop' },
        { path: '/material', name: '物料管理', icon: 'el-icon-s-goods' },
        { path: '/inbound', name: '入库管理', icon: 'el-icon-download' },
        { path: '/outbound', name: '出库管理', icon: 'el-icon-upload2' },
        { path: '/inventory', name: '库存管理', icon: 'el-icon-s-data' },
        { path: '/monitor', name: '设备监控', icon: 'el-icon-monitor' },
        { path: '/guide', name: '智能导寻', icon: 'el-icon-location' }
      ],
      
      // 系统菜单项
      systemMenuItems: [
        { path: '/system/user', name: '用户管理', icon: 'el-icon-user' },
        { path: '/system/role', name: '角色管理', icon: 'el-icon-s-custom' },
        { path: '/system/menu', name: '菜单管理', icon: 'el-icon-menu' },
        { path: '/system/config', name: '系统配置', icon: 'el-icon-setting' }
      ],
      
      // 底部标签栏
      bottomTabs: [
        { path: '/dashboard', name: '首页', icon: 'el-icon-s-home' },
        { path: '/warehouse', name: '仓库', icon: 'el-icon-s-shop' },
        { path: '/material', name: '物料', icon: 'el-icon-s-goods', badge: 5 },
        { path: '/monitor', name: '监控', icon: 'el-icon-s-data' },
        { path: '/profile', name: '我的', icon: 'el-icon-s-custom' }
      ],
      
      // 快速操作
      quickActions: [
        {
          key: 'inbound',
          title: '快速入库',
          description: '扫码快速入库操作',
          icon: 'el-icon-download',
          color: '#67C23A'
        },
        {
          key: 'outbound',
          title: '快速出库',
          description: '扫码快速出库操作',
          icon: 'el-icon-upload2',
          color: '#E6A23C'
        },
        {
          key: 'inventory',
          title: '库存查询',
          description: '快速查询库存信息',
          icon: 'el-icon-search',
          color: '#409EFF'
        },
        {
          key: 'transfer',
          title: '库存调拨',
          description: '库存转移调拨',
          icon: 'el-icon-sort',
          color: '#909399'
        },
        {
          key: 'check',
          title: '库存盘点',
          description: '库存盘点操作',
          icon: 'el-icon-document-checked',
          color: '#F56C6C'
        },
        {
          key: 'report',
          title: '报表查看',
          description: '查看各类报表',
          icon: 'el-icon-s-data',
          color: '#606266'
        }
      ]
    }
  },
  
  methods: {
    // 关闭菜单
    closeMenu() {
      this.$emit('update:menuVisible', false)
    },
    
    // 导航到指定路径
    navigateTo(path) {
      this.$router.push(path)
      this.closeMenu()
    },
    
    // 检查路径是否激活
    isActive(path) {
      return this.$route.path === path || this.$route.path.startsWith(path + '/')
    },
    
    // 处理快速操作
    handleQuickAction(action) {
      switch (action) {
        case 'scan':
          this.openScanner()
          break
        case 'search':
          this.$emit('show-search')
          break
        case 'add':
          this.quickActionVisible = true
          break
        case 'report':
          this.navigateTo('/report')
          break
      }
      this.closeMenu()
    },
    
    // 处理浮动按钮点击
    handleFabClick() {
      this.quickActionVisible = true
    },
    
    // 执行快速操作
    executeQuickAction(action) {
      this.quickActionVisible = false
      
      switch (action.key) {
        case 'inbound':
          this.navigateTo('/inbound/quick')
          break
        case 'outbound':
          this.navigateTo('/outbound/quick')
          break
        case 'inventory':
          this.navigateTo('/inventory/search')
          break
        case 'transfer':
          this.navigateTo('/inventory/transfer')
          break
        case 'check':
          this.navigateTo('/inventory/check')
          break
        case 'report':
          this.navigateTo('/report')
          break
      }
    },
    
    // 打开扫码器
    openScanner() {
      this.scanVisible = true
      // 这里可以集成扫码库，如 quagga.js 或 zxing-js
    },
    
    // 切换闪光灯
    toggleFlash() {
      this.flashOn = !this.flashOn
      // 控制摄像头闪光灯
    },
    
    // 切换摄像头
    switchCamera() {
      // 切换前后摄像头
    },
    
    // 处理设置
    handleSettings() {
      this.navigateTo('/settings')
    },
    
    // 处理退出登录
    handleLogout() {
      this.$confirm('确认退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 执行退出登录逻辑
        this.$store.dispatch('user/logout').then(() => {
          this.$router.push('/login')
        })
      })
    }
  }
}
</script>

<style scoped>
.mobile-navigation {
  position: relative;
}

/* 移动端菜单 */
.mobile-menu {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  width: 280px;
  background: #fff;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  z-index: 1001;
  display: flex;
  flex-direction: column;
}

.mobile-menu.is-open {
  transform: translateX(0);
}

.menu-header {
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-details {
  margin-left: 15px;
}

.user-name {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 5px;
}

.user-role {
  font-size: 12px;
  opacity: 0.8;
}

.close-btn {
  color: #fff;
  font-size: 18px;
}

.menu-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px 0;
}

.menu-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 12px;
  color: #909399;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 15px;
  padding: 0 20px;
}

.menu-items {
  padding: 0 10px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  margin-bottom: 5px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.menu-item:hover {
  background: #f5f7fa;
}

.menu-item.active {
  background: #ecf5ff;
  color: #409EFF;
}

.menu-item i:first-child {
  width: 20px;
  margin-right: 15px;
  font-size: 16px;
}

.menu-item span {
  flex: 1;
  font-size: 14px;
}

.menu-item .el-icon-arrow-right {
  color: #c0c4cc;
  font-size: 12px;
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
  padding: 0 20px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px 10px;
  background: #f5f7fa;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-item:hover {
  background: #ecf5ff;
  color: #409EFF;
}

.action-item i {
  font-size: 20px;
  margin-bottom: 8px;
}

.action-item span {
  font-size: 12px;
}

.menu-footer {
  padding: 20px;
  border-top: 1px solid #f0f0f0;
}

.footer-actions {
  display: flex;
  justify-content: space-around;
}

/* 底部标签栏 */
.bottom-tabs {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: #fff;
  border-top: 1px solid #e4e7ed;
  display: flex;
  z-index: 1000;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 12px;
  cursor: pointer;
  transition: color 0.3s ease;
  position: relative;
}

.tab-item.active {
  color: #409EFF;
}

.tab-icon {
  position: relative;
  margin-bottom: 4px;
}

.tab-icon i {
  font-size: 20px;
}

.tab-badge {
  position: absolute;
  top: -8px;
  right: -8px;
}

.tab-label {
  font-size: 10px;
}

/* 浮动操作按钮 */
.floating-action-button {
  position: fixed;
  bottom: 80px;
  right: 20px;
  width: 56px;
  height: 56px;
  background: #409EFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 24px;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 999;
}

.floating-action-button:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.6);
}

/* 快速操作抽屉 */
.quick-action-grid {
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.quick-action-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-action-card:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.action-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 18px;
  margin-right: 15px;
}

.action-info {
  flex: 1;
}

.action-title {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.action-desc {
  font-size: 12px;
  color: #909399;
}

/* 扫码对话框 */
.scan-container {
  text-align: center;
}

.scan-area {
  position: relative;
  width: 250px;
  height: 250px;
  margin: 0 auto 20px;
  background: #f5f7fa;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scan-frame {
  width: 200px;
  height: 200px;
  border: 2px solid #409EFF;
  border-radius: 8px;
  position: relative;
}

.scan-frame::before,
.scan-frame::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  border: 3px solid #409EFF;
}

.scan-frame::before {
  top: -3px;
  left: -3px;
  border-right: none;
  border-bottom: none;
}

.scan-frame::after {
  bottom: -3px;
  right: -3px;
  border-left: none;
  border-top: none;
}

.scan-tips {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  color: #909399;
}

.scan-controls {
  display: flex;
  justify-content: center;
  gap: 20px;
}
</style>
