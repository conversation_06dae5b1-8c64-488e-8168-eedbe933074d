<template>
  <div class="material-bill-form">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="清单编号" prop="billCode">
            <el-input v-model="form.billCode" placeholder="自动生成" :disabled="true" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="清单名称" prop="billName">
            <el-input v-model="form.billName" placeholder="请输入物料清单名称" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="form.status" placeholder="请选择状态">
              <el-option label="草稿" value="0" />
              <el-option label="正常" value="1" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="重量容差(kg)" prop="weightTolerance">
            <el-input-number v-model="form.weightTolerance" :precision="3" :step="0.001" :min="0" placeholder="重量容差" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="描述" prop="description">
        <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入描述" />
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" type="textarea" :rows="2" placeholder="请输入备注" />
      </el-form-item>

      <!-- 物料清单明细 -->
      <el-divider content-position="left">物料清单明细</el-divider>
      
      <div class="item-operations">
        <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAddItem" v-hasPermi="['material:item:add']">添加明细</el-button>
        <el-button type="success" size="small" icon="el-icon-upload2" @click="handleBatchAdd" v-hasPermi="['material:item:batch']">批量添加</el-button>
        <el-button type="info" size="small" icon="el-icon-refresh" @click="handleRecalculateWeight" v-hasPermi="['material:item:weight']">重新计算重量</el-button>
        <el-button type="warning" size="small" icon="el-icon-sort" @click="handleSortItems" v-hasPermi="['material:item:sort']">排序</el-button>
      </div>

      <el-table :data="form.materialBillItemList" border style="margin-top: 10px;">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column label="物料编码" prop="materialCode" width="120" />
        <el-table-column label="物料名称" prop="materialName" min-width="150" />
        <el-table-column label="规格" prop="materialSpec" width="120" />
        <el-table-column label="型号" prop="materialModel" width="120" />
        <el-table-column label="单位" prop="unit" width="80" />
        <el-table-column label="数量" prop="quantity" width="100">
          <template slot-scope="scope">
            <el-input-number v-model="scope.row.quantity" :precision="3" :min="0" size="mini" @change="calculateWeight(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column label="单位重量(kg)" prop="unitWeight" width="120">
          <template slot-scope="scope">
            <el-input-number v-model="scope.row.unitWeight" :precision="3" :min="0" size="mini" @change="calculateWeight(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column label="总重量(kg)" prop="totalWeight" width="120" />
        <el-table-column label="存储位置" prop="locationCode" width="120" align="center">
          <template slot-scope="scope">
            <el-input
              :value="scope.row.locationCode || ''"
              @input="updateLocationCode(scope.$index, $event)"
              placeholder="货位号/容器号"
              size="mini"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="mini" icon="el-icon-edit" @click="handleEditItem(scope.$index)" v-hasPermi="['material:item:edit']">编辑</el-button>
            <el-button type="text" size="mini" icon="el-icon-delete" @click="handleDeleteItem(scope.$index)" v-hasPermi="['material:item:remove']">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="form-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-form>

    <!-- 添加明细对话框 -->
    <material-item-dialog
      ref="materialItemDialog"
      :edit-item="currentEditItem"
      @success="handleItemSuccess"
    />
  </div>
</template>

<script>
import { addMaterialBill, updateMaterialBill, generateBillCode } from "@/api/material/bill";
import MaterialItemDialog from "./MaterialItemDialog";

export default {
  name: "MaterialBillForm",
  components: {
    MaterialItemDialog
  },
  data() {
    return {
      form: {
        id: null,
        billCode: null,
        billName: null,
        description: null,
        status: "0",
        weightTolerance: 0.050,
        remark: null,
        materialBillItemList: []
      },
      // 当前编辑的明细项
      currentEditItem: null,
      // 当前编辑的明细索引
      currentEditIndex: -1,
      rules: {
        billName: [
          { required: true, message: "物料清单名称不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ]
      },
      isEdit: false
    };
  },
  methods: {
    // 初始化表单
    init(data) {
      this.reset();
      if (data) {
        this.isEdit = true;
        this.form = { ...data };
        if (!this.form.materialBillItemList) {
          this.form.materialBillItemList = [];
        }
      } else {
        this.isEdit = false;
        this.generateCode();
      }
    },
    
    // 重置表单
    reset() {
      this.form = {
        id: null,
        billCode: null,
        billName: null,
        description: null,
        status: "0",
        weightTolerance: 0.050,
        remark: null,
        materialBillItemList: []
      };
      this.resetForm("form");
    },

    // 生成编号
    generateCode() {
      generateBillCode().then(response => {
        this.form.billCode = response.data;
      });
    },

    // 添加明细
    handleAddItem() {
      this.currentEditItem = null;
      this.currentEditIndex = -1;
      this.$refs.materialItemDialog.open();
    },

    // 批量添加
    handleBatchAdd() {
      this.$message.info("批量添加功能待实现");
    },

    // 编辑明细
    handleEditItem(index) {
      const item = this.form.materialBillItemList[index];
      console.log('编辑明细项数据:', item);
      this.currentEditItem = { ...item };
      this.currentEditIndex = index;
      this.$refs.materialItemDialog.open();
    },

    // 删除明细
    handleDeleteItem(index) {
      this.$confirm('确认删除该明细吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.form.materialBillItemList.splice(index, 1);
        this.calculateTotalWeight();
        this.$message.success('删除成功');
      }).catch(() => {});
    },

    // 重新计算重量
    handleRecalculateWeight() {
      this.form.materialBillItemList.forEach(item => {
        this.calculateWeight(item);
      });
      this.$message.success('重量重新计算完成');
    },

    // 排序明细
    handleSortItems() {
      this.$message.info("排序功能待实现");
    },

    // 明细添加/编辑成功
    handleItemSuccess(item) {
      console.log('接收到的物料数据:', item);

      if (this.currentEditIndex >= 0) {
        // 编辑模式
        const updatedItem = {
          ...item,
          sortOrder: this.currentEditItem.sortOrder || this.currentEditIndex + 1
        };
        console.log('更新的物料数据:', updatedItem);
        this.$set(this.form.materialBillItemList, this.currentEditIndex, updatedItem);
        this.$message.success("明细修改成功");
      } else {
        // 新增模式
        // 检查是否已存在
        const exists = this.form.materialBillItemList.some(existItem =>
          existItem.materialId === item.materialId
        );

        if (exists) {
          this.$message.warning("该物料已存在于清单中");
          return;
        }

        const newItem = {
          ...item,
          sortOrder: this.form.materialBillItemList.length + 1
        };
        console.log('新增的物料数据:', newItem);
        this.form.materialBillItemList.push(newItem);
        this.$message.success("明细添加成功");
      }

      // 强制更新表格
      this.$nextTick(() => {
        this.$forceUpdate();
      });

      // 重置编辑状态
      this.currentEditItem = null;
      this.currentEditIndex = -1;

      this.calculateTotalWeight();
    },

    // 计算单行重量
    calculateWeight(row) {
      if (row.quantity && row.unitWeight) {
        row.totalWeight = (row.quantity * row.unitWeight).toFixed(3);
      } else {
        row.totalWeight = 0;
      }
      this.calculateTotalWeight();
    },

    // 计算总重量
    calculateTotalWeight() {
      let totalWeight = 0;
      let totalQuantity = 0;
      
      this.form.materialBillItemList.forEach(item => {
        if (item.totalWeight) {
          totalWeight += parseFloat(item.totalWeight);
        }
        if (item.quantity) {
          totalQuantity += parseFloat(item.quantity);
        }
      });
      
      this.form.totalWeight = totalWeight.toFixed(3);
      this.form.totalQuantity = totalQuantity.toFixed(3);
      this.form.itemCount = this.form.materialBillItemList.length;
    },

    // 取消
    cancel() {
      this.$emit("cancel");
    },

    // 更新存储位置
    updateLocationCode(index, value) {
      console.log('更新存储位置:', index, value);
      this.$set(this.form.materialBillItemList[index], 'locationCode', value);
      this.$set(this.form.materialBillItemList[index], 'storageType', value ? 'location' : null);
    },

    // 存储位置变化处理
    handleStorageLocationChange(row) {
      console.log('存储位置变化:', row.locationCode);
      // 设置存放类型
      row.storageType = row.locationCode ? 'location' : null;

      // 强制更新当前行
      this.$forceUpdate();

      // 可以在这里添加额外的处理逻辑，比如验证存储位置是否存在等
    },

    // 提交表单
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.calculateTotalWeight();
          
          if (this.isEdit) {
            updateMaterialBill(this.form).then(response => {
              this.$message.success("修改成功");
              this.$emit("success");
            });
          } else {
            addMaterialBill(this.form).then(response => {
              this.$message.success("新增成功");
              this.$emit("success");
            });
          }
        }
      });
    }
  }
};
</script>

<style scoped>
.material-bill-form {
  padding: 20px;
}

.item-operations {
  margin-bottom: 10px;
}

.form-footer {
  text-align: center;
  margin-top: 20px;
}
</style>
