<template>
  <div class="app-container">
    <!-- 实时监控面板 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>今日识别统计</span>
          </div>
          <div class="text item">
            <el-statistic title="总识别次数" :value="todayStats.totalRecognitions" />
          </div>
          <div class="text item">
            <el-statistic title="成功识别" :value="todayStats.successRecognitions" />
          </div>
          <div class="text item">
            <el-statistic title="失败识别" :value="todayStats.failedRecognitions" />
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>设备状态</span>
          </div>
          <div class="text item">
            <el-statistic title="在线设备" :value="deviceStats.onlineDevices" />
          </div>
          <div class="text item">
            <el-statistic title="离线设备" :value="deviceStats.offlineDevices" />
          </div>
          <div class="text item">
            <el-statistic title="总设备数" :value="deviceStats.totalDevices" />
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>异常统计</span>
          </div>
          <div class="text item">
            <el-statistic title="陌生人识别" :value="todayStats.strangerRecognitions" />
          </div>
          <div class="text item">
            <el-statistic title="体温异常" :value="todayStats.temperatureAlarms" />
          </div>
          <div class="text item">
            <el-statistic title="活体检测失败" :value="todayStats.livenessFailures" />
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>系统状态</span>
          </div>
          <div class="text item">
            <el-tag :type="systemStatus.status === 'normal' ? 'success' : 'danger'">
              {{ systemStatus.status === 'normal' ? '正常运行' : '异常状态' }}
            </el-tag>
          </div>
          <div class="text item">
            <span>最后更新：{{ systemStatus.lastUpdate }}</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 实时识别记录 -->
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>实时识别记录</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="refreshRealTimeData">刷新</el-button>
      </div>
      
      <el-table v-loading="realTimeLoading" :data="realTimeRecords" style="width: 100%" height="400">
        <el-table-column label="时间" width="160">
          <template slot-scope="scope">
            {{ formatTime(scope.row.recognitionTime) }}
          </template>
        </el-table-column>
        <el-table-column label="设备" prop="deviceName" width="150" />
        <el-table-column label="人员" width="120">
          <template slot-scope="scope">
            <span v-if="scope.row.personName">{{ scope.row.personName }}</span>
            <el-tag v-else type="warning">陌生人</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="工号" prop="employeeNo" width="100" />
        <el-table-column label="识别结果" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.recognitionResult === '0' ? 'success' : 'danger'">
              {{ scope.row.recognitionResult === '0' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="置信度" width="100">
          <template slot-scope="scope">
            <el-progress :percentage="Math.round(scope.row.confidence * 100)" :stroke-width="6" />
          </template>
        </el-table-column>
        <el-table-column label="体温" width="80">
          <template slot-scope="scope">
            <span v-if="scope.row.temperature">{{ scope.row.temperature }}°C</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="通行结果" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.accessResult === '0' ? 'success' : 'danger'">
              {{ scope.row.accessResult === '0' ? '允许' : '拒绝' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="人脸图片" width="100">
          <template slot-scope="scope">
            <el-image
              v-if="scope.row.faceImagePath"
              style="width: 50px; height: 50px"
              :src="scope.row.faceImagePath"
              :preview-src-list="[scope.row.faceImagePath]"
              fit="cover"
            />
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="viewDetails(scope.row)">详情</el-button>
            <el-button size="mini" type="text" @click="handleProcess(scope.row)">处理</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 识别详情对话框 -->
    <el-dialog title="识别详情" :visible.sync="detailVisible" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="识别时间">{{ formatTime(currentRecord.recognitionTime) }}</el-descriptions-item>
        <el-descriptions-item label="设备名称">{{ currentRecord.deviceName }}</el-descriptions-item>
        <el-descriptions-item label="设备编码">{{ currentRecord.deviceCode }}</el-descriptions-item>
        <el-descriptions-item label="设备IP">{{ currentRecord.deviceIp }}</el-descriptions-item>
        <el-descriptions-item label="人员姓名">{{ currentRecord.personName || '陌生人' }}</el-descriptions-item>
        <el-descriptions-item label="人员工号">{{ currentRecord.employeeNo || '-' }}</el-descriptions-item>
        <el-descriptions-item label="识别置信度">{{ Math.round((currentRecord.confidence || 0) * 100) }}%</el-descriptions-item>
        <el-descriptions-item label="识别阈值">{{ Math.round((currentRecord.threshold || 0) * 100) }}%</el-descriptions-item>
        <el-descriptions-item label="识别结果">
          <el-tag :type="currentRecord.recognitionResult === '0' ? 'success' : 'danger'">
            {{ currentRecord.recognitionResult === '0' ? '成功' : '失败' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="活体检测">
          <el-tag :type="currentRecord.livenessResult === '0' ? 'success' : 'danger'">
            {{ currentRecord.livenessResult === '0' ? '真人' : currentRecord.livenessResult === '1' ? '假人' : '未检测' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="口罩检测">
          <el-tag :type="currentRecord.maskResult === '1' ? 'success' : 'warning'">
            {{ currentRecord.maskResult === '1' ? '戴口罩' : currentRecord.maskResult === '0' ? '未戴口罩' : '未检测' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="体温检测">
          <span v-if="currentRecord.temperature">
            {{ currentRecord.temperature }}°C
            <el-tag :type="currentRecord.temperatureStatus === '0' ? 'success' : 'danger'">
              {{ currentRecord.temperatureStatus === '0' ? '正常' : '异常' }}
            </el-tag>
          </span>
          <span v-else>未检测</span>
        </el-descriptions-item>
        <el-descriptions-item label="通行结果">
          <el-tag :type="currentRecord.accessResult === '0' ? 'success' : 'danger'">
            {{ currentRecord.accessResult === '0' ? '允许通行' : '拒绝通行' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="拒绝原因">{{ currentRecord.rejectReason || '-' }}</el-descriptions-item>
      </el-descriptions>
      
      <div style="margin-top: 20px;">
        <el-row :gutter="20">
          <el-col :span="12">
            <h4>人脸图片</h4>
            <el-image
              v-if="currentRecord.faceImagePath"
              style="width: 200px; height: 200px"
              :src="currentRecord.faceImagePath"
              :preview-src-list="[currentRecord.faceImagePath]"
              fit="cover"
            />
            <div v-else>无图片</div>
          </el-col>
          <el-col :span="12">
            <h4>现场图片</h4>
            <el-image
              v-if="currentRecord.sceneImagePath"
              style="width: 200px; height: 200px"
              :src="currentRecord.sceneImagePath"
              :preview-src-list="[currentRecord.sceneImagePath]"
              fit="cover"
            />
            <div v-else>无图片</div>
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <!-- 处理对话框 -->
    <el-dialog title="处理识别记录" :visible.sync="processVisible" width="500px" append-to-body>
      <el-form ref="processForm" :model="processForm" label-width="100px">
        <el-form-item label="处理状态">
          <el-radio-group v-model="processForm.processStatus">
            <el-radio label="1">已处理</el-radio>
            <el-radio label="0">未处理</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="处理备注">
          <el-input v-model="processForm.remarks" type="textarea" rows="3" placeholder="请输入处理备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmProcess">确 定</el-button>
        <el-button @click="processVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listFaceResult, getFaceResultStatistics } from "@/api/access/faceResult"

export default {
  name: "FaceMonitor",
  data() {
    return {
      // 今日统计
      todayStats: {
        totalRecognitions: 0,
        successRecognitions: 0,
        failedRecognitions: 0,
        strangerRecognitions: 0,
        temperatureAlarms: 0,
        livenessFailures: 0
      },
      // 设备统计
      deviceStats: {
        onlineDevices: 0,
        offlineDevices: 0,
        totalDevices: 0
      },
      // 系统状态
      systemStatus: {
        status: 'normal',
        lastUpdate: new Date().toLocaleString()
      },
      // 实时记录
      realTimeLoading: false,
      realTimeRecords: [],
      // 详情对话框
      detailVisible: false,
      currentRecord: {},
      // 处理对话框
      processVisible: false,
      processForm: {
        resultId: null,
        processStatus: '1',
        remarks: ''
      }
    };
  },
  created() {
    this.loadRealTimeData();
    this.getStatistics();
    this.startAutoRefresh();
  },
  beforeDestroy() {
    this.stopAutoRefresh();
  },
  methods: {
    /** 加载实时数据 */
    loadRealTimeData() {
      this.realTimeLoading = true;

      // 调用API获取实时人脸识别数据
      this.getFaceRecognitionList().then(() => {
        this.realTimeLoading = false;
      }).catch(() => {
        this.realTimeLoading = false;
      });
    },

    /** 获取人脸识别记录列表 */
    getFaceRecognitionList() {
      return new Promise((resolve, reject) => {
        // 调用人脸识别记录API
        listFaceResult({
          pageNum: 1,
          pageSize: 10,
          orderByColumn: 'verify_time',
          isAsc: 'desc'
        }).then(response => {
          this.realTimeRecords = response.rows || [];
          resolve(response);
        }).catch(error => {
          console.error('获取人脸识别记录失败:', error);
          this.realTimeRecords = [];
          reject(error);
        });
      });
    },

    /** 获取统计数据 */
    getStatistics() {
      getFaceResultStatistics().then(response => {
        if (response.data) {
          this.todayStats = {
            totalRecognitions: response.data.totalCount || 0,
            successRecognitions: response.data.successCount || 0,
            failedRecognitions: response.data.failCount || 0,
            strangerRecognitions: response.data.strangerCount || 0,
            temperatureAlarms: response.data.temperatureAlarmCount || 0,
            livenessFailures: response.data.livenessFailCount || 0
          };
        }
      }).catch(error => {
        console.error('获取统计数据失败:', error);
      });
    },
    /** 刷新实时数据 */
    refreshRealTimeData() {
      this.loadRealTimeData();
      this.systemStatus.lastUpdate = new Date().toLocaleString();
    },
    /** 开始自动刷新 */
    startAutoRefresh() {
      this.autoRefreshTimer = setInterval(() => {
        this.refreshRealTimeData();
      }, 30000); // 30秒刷新一次
    },
    /** 停止自动刷新 */
    stopAutoRefresh() {
      if (this.autoRefreshTimer) {
        clearInterval(this.autoRefreshTimer);
      }
    },
    /** 格式化时间 */
    formatTime(time) {
      if (!time) return '-';
      return new Date(time).toLocaleString();
    },
    /** 查看详情 */
    viewDetails(row) {
      this.currentRecord = row;
      this.detailVisible = true;
    },
    /** 处理记录 */
    handleProcess(row) {
      this.processForm.resultId = row.resultId;
      this.processForm.processStatus = row.processStatus || '0';
      this.processForm.remarks = row.remarks || '';
      this.processVisible = true;
    },
    /** 确认处理 */
    confirmProcess() {
      // 调用API处理人脸识别记录
      this.$http.put('/access/faceResult/process', {
        resultId: this.processForm.resultId,
        processStatus: this.processForm.processStatus,
        remarks: this.processForm.remarks
      }).then(response => {
        this.$modal.msgSuccess("处理成功");
        this.processVisible = false;

        // 更新记录状态
        const record = this.realTimeRecords.find(r => r.resultId === this.processForm.resultId);
        if (record) {
          record.processStatus = this.processForm.processStatus;
          record.remarks = this.processForm.remarks;
        }

        // 刷新数据
        this.loadRealTimeData();
      }).catch(error => {
        console.error('处理失败:', error);
        this.$modal.msgError("处理失败");
      });
    }
  }
};
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}

.text {
  font-size: 14px;
  margin-bottom: 10px;
}

.item {
  margin-bottom: 18px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.mb20 {
  margin-bottom: 20px;
}
</style>
