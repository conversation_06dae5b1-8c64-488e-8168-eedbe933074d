import request from '@/utils/request'

// 查询门禁记录列表
export function listAccessRecord(query) {
  return request({
    url: '/dwms/access/list',
    method: 'get',
    params: query
  })
}

// 查询门禁记录详细
export function getAccessRecord(id) {
  return request({
    url: '/dwms/access/' + id,
    method: 'get'
  })
}

// 新增门禁记录
export function addAccessRecord(data) {
  return request({
    url: '/dwms/access',
    method: 'post',
    data: data
  })
}

// 修改门禁记录
export function updateAccessRecord(data) {
  return request({
    url: '/dwms/access',
    method: 'put',
    data: data
  })
}

// 删除门禁记录
export function delAccessRecord(id) {
  return request({
    url: '/dwms/access/' + id,
    method: 'delete'
  })
}

// 根据门禁ID查询记录列表
export function getAccessRecordByDoorId(doorId) {
  return request({
    url: '/dwms/access/door/' + doorId,
    method: 'get'
  })
}

// 根据人员ID查询记录列表
export function getAccessRecordByPersonId(personId) {
  return request({
    url: '/dwms/access/person/' + personId,
    method: 'get'
  })
}

// 根据库房ID查询记录列表
export function getAccessRecordByWarehouseId(warehouseId) {
  return request({
    url: '/dwms/access/warehouse/' + warehouseId,
    method: 'get'
  })
}

// 查询失败的门禁记录
export function getFailedAccessRecords(warehouseId) {
  return request({
    url: '/dwms/access/failed/' + warehouseId,
    method: 'get'
  })
}

// 根据时间范围查询门禁记录
export function getAccessRecordByTimeRange(params) {
  return request({
    url: '/dwms/access/timerange',
    method: 'get',
    params: params
  })
}

// 查询门禁统计数据
export function getAccessStats(params) {
  return request({
    url: '/dwms/access/stats',
    method: 'get',
    params: params
  })
}

// 查询当前在库人员
export function getCurrentPersons(warehouseId) {
  return request({
    url: '/dwms/access/current/' + (warehouseId || ''),
    method: 'get'
  })
}

// 获取门禁设备状态
export function getDeviceStatus(doorId) {
  return request({
    url: '/dwms/access/device/status/' + doorId,
    method: 'get'
  })
}

// 获取实时门禁数据
export function getRealTimeAccessData(warehouseId) {
  return request({
    url: '/dwms/access/realtime/' + (warehouseId || ''),
    method: 'get'
  })
}

// 门禁设备控制
export function controlDevice(data) {
  return request({
    url: '/dwms/access/control',
    method: 'post',
    data: data
  })
}

// 清理过期记录
export function cleanExpiredRecord(retentionDays) {
  return request({
    url: '/dwms/access/clean',
    method: 'delete',
    params: {
      retentionDays: retentionDays
    }
  })
}

// 批量导入门禁记录
export function batchImportAccessRecord(data) {
  return request({
    url: '/dwms/access/import',
    method: 'post',
    data: data
  })
}

// 获取门禁访问趋势
export function getAccessTrend(params) {
  return request({
    url: '/dwms/access/trend',
    method: 'get',
    params: params
  })
}

// 导出门禁记录
export function exportAccessRecord(query) {
  return request({
    url: '/dwms/access/export',
    method: 'get',
    params: query
  })
}
