import request from '@/utils/request'

// EMQX系统概览API

/**
 * 获取系统概览数据
 */
export function getDashboardOverview() {
  return request({
    url: '/emqx/dashboard/overview',
    method: 'get'
  })
}

/**
 * 获取系统状态
 */
export function getSystemStatus() {
  return request({
    url: '/emqx/dashboard/system-status',
    method: 'get'
  })
}

/**
 * 获取最近活动
 */
export function getRecentActivities() {
  return request({
    url: '/emqx/dashboard/recent-activities',
    method: 'get'
  })
}

/**
 * 获取系统统计信息
 */
export function getSystemStatistics() {
  return request({
    url: '/emqx/dashboard/statistics',
    method: 'get'
  })
}

/**
 * 获取快速操作数据
 */
export function getQuickActions() {
  return request({
    url: '/emqx/dashboard/quick-actions',
    method: 'get'
  })
}

/**
 * 执行快速测试连接
 */
export function quickTestConnections() {
  return request({
    url: '/emqx/dashboard/quick-test-connections',
    method: 'post'
  })
}

/**
 * 执行快速检查重复
 */
export function quickCheckDuplicates() {
  return request({
    url: '/emqx/dashboard/quick-check-duplicates',
    method: 'post'
  })
}

/**
 * 执行快速清理
 */
export function quickCleanup() {
  return request({
    url: '/emqx/dashboard/quick-cleanup',
    method: 'post'
  })
}
