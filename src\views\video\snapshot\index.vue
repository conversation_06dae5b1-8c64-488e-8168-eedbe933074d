<template>
  <div class="app-container">
    <!-- 页面标题和统计 -->
    <el-card class="header-card" shadow="never">
      <div class="header-content">
        <div class="title-section">
          <h2><i class="el-icon-camera"></i> 告警截图管理</h2>
          <p>管理视频监控布防联动产生的告警截图，包括人员入侵、异常行为等自动触发的截图</p>
        </div>
        <div class="stats-section">
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-number">{{ totalSnapshots }}</div>
                <div class="stat-label">总截图数</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-number alarm">{{ alarmSnapshots }}</div>
                <div class="stat-label">告警截图</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-number today">{{ todaySnapshots }}</div>
                <div class="stat-label">今日新增</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-number unprocessed">{{ unprocessedSnapshots }}</div>
                <div class="stat-label">待处理</div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-card>

    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="设备名称" prop="deviceName">
        <el-input v-model="queryParams.deviceName" placeholder="请输入设备名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="告警类型" prop="alarmType">
        <el-select v-model="queryParams.alarmType" placeholder="请选择告警类型" clearable>
          <el-option label="人员入侵" value="intrusion"/>
          <el-option label="异常行为" value="behavior"/>
          <el-option label="物体遗留" value="object_left"/>
          <el-option label="区域闯入" value="area_breach"/>
          <el-option label="人脸识别" value="face_detection"/>
          <el-option label="车辆违停" value="vehicle_violation"/>
        </el-select>
      </el-form-item>
      <el-form-item label="处理状态" prop="processStatus">
        <el-select v-model="queryParams.processStatus" placeholder="请选择处理状态" clearable>
          <el-option label="未处理" value="0"/>
          <el-option label="已处理" value="1"/>
          <el-option label="已忽略" value="2"/>
        </el-select>
      </el-form-item>
      <el-form-item label="告警时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-check" size="mini" :disabled="multiple" @click="handleBatchProcess" v-hasPermi="['video:snapshot:process']">批量处理</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-download" size="mini" :disabled="multiple" @click="handleDownload" v-hasPermi="['video:snapshot:download']">下载</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['video:snapshot:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['video:snapshot:export']">导出报告</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-setting" size="mini" @click="showSearch = !showSearch">
          {{ showSearch ? '隐藏' : '显示' }}搜索
        </el-button>
      </el-col>
    </el-row>

    <!-- 告警截图网格显示 -->
    <div class="snapshot-grid">
      <el-card
        v-for="snapshot in snapshotList"
        :key="snapshot.id"
        class="snapshot-card"
        :class="{ 'alarm-card': snapshot.isAlarm, 'unprocessed': snapshot.processStatus === 0 }"
        :body-style="{ padding: '0px' }">

        <!-- 告警标识 -->
        <div v-if="snapshot.isAlarm" class="alarm-badge">
          <el-tag type="danger" size="mini">
            <i class="el-icon-warning"></i> 告警
          </el-tag>
        </div>

        <!-- 处理状态标识 -->
        <div class="status-badge">
          <el-tag
            :type="getStatusType(snapshot.processStatus)"
            size="mini">
            {{ getStatusText(snapshot.processStatus) }}
          </el-tag>
        </div>

        <div class="snapshot-image">
          <el-image
            :src="snapshot.filePath"
            fit="cover"
            style="width: 100%; height: 200px;"
            :preview-src-list="[snapshot.filePath]">
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
              <p>图片加载失败</p>
            </div>
          </el-image>
          <div class="snapshot-overlay">
            <el-checkbox v-model="snapshot.selected" @change="handleSelectionChange"></el-checkbox>
          </div>
        </div>
        <div class="snapshot-info">
          <div class="snapshot-header">
            <div class="snapshot-title">{{ snapshot.alarmType ? getAlarmTypeText(snapshot.alarmType) : snapshot.snapshotName }}</div>
            <div class="snapshot-level">
              <el-tag
                v-if="snapshot.alarmLevel"
                :type="getAlarmLevelType(snapshot.alarmLevel)"
                size="mini">
                {{ getAlarmLevelText(snapshot.alarmLevel) }}
              </el-tag>
            </div>
          </div>

          <div class="snapshot-meta">
            <div class="meta-item">
              <i class="el-icon-monitor"></i>
              <span>{{ snapshot.deviceName }}</span>
            </div>
            <div class="meta-item">
              <i class="el-icon-location"></i>
              <span>{{ snapshot.location || '未知位置' }}</span>
            </div>
          </div>

          <div class="snapshot-time">
            <i class="el-icon-time"></i>
            {{ parseTime(snapshot.captureTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
          </div>

          <div v-if="snapshot.alarmDescription" class="alarm-description">
            <i class="el-icon-warning"></i>
            {{ snapshot.alarmDescription }}
          </div>

          <div class="snapshot-details">
            <span class="file-size">{{ formatFileSize(snapshot.fileSize) }}</span>
            <span class="confidence" v-if="snapshot.confidence">
              置信度: {{ (snapshot.confidence * 100).toFixed(1) }}%
            </span>
          </div>

          <div class="snapshot-actions">
            <el-button-group size="mini">
              <el-button icon="el-icon-view" @click="handlePreview(snapshot)">预览</el-button>
              <el-button
                v-if="snapshot.processStatus === 0"
                type="success"
                icon="el-icon-check"
                @click="handleProcess(snapshot)">
                处理
              </el-button>
              <el-button icon="el-icon-download" @click="handleDownload(snapshot)">下载</el-button>
              <el-button icon="el-icon-delete" @click="handleDelete(snapshot)">删除</el-button>
            </el-button-group>
          </div>
        </div>
      </el-card>
    </div>
    
    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 手动截图对话框 -->
    <el-dialog title="手动截图" :visible.sync="captureOpen" width="500px" append-to-body>
      <el-form ref="captureForm" :model="captureForm" :rules="captureRules" label-width="80px">
        <el-form-item label="设备选择" prop="deviceId">
          <el-select v-model="captureForm.deviceId" placeholder="请选择设备">
            <el-option v-for="device in deviceOptions" :key="device.id" :label="device.deviceName" :value="device.id"/>
          </el-select>
        </el-form-item>
        <el-form-item label="截图名称" prop="snapshotName">
          <el-input v-model="captureForm.snapshotName" placeholder="请输入截图名称" />
        </el-form-item>
        <el-form-item label="图片格式" prop="imageFormat">
          <el-select v-model="captureForm.imageFormat" placeholder="请选择图片格式">
            <el-option label="JPG" value="jpg"/>
            <el-option label="PNG" value="png"/>
            <el-option label="BMP" value="bmp"/>
          </el-select>
        </el-form-item>
        <el-form-item label="分辨率" prop="resolution">
          <el-select v-model="captureForm.resolution" placeholder="请选择分辨率">
            <el-option label="1920x1080" value="1920x1080"/>
            <el-option label="1280x720" value="1280x720"/>
            <el-option label="640x480" value="640x480"/>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitCapture">开始截图</el-button>
        <el-button @click="captureOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog title="图片预览" :visible.sync="previewOpen" width="800px" append-to-body>
      <div v-if="previewSnapshot" class="preview-container">
        <el-image :src="previewSnapshot.filePath" fit="contain" style="width: 100%; max-height: 500px;"></el-image>
        <el-descriptions title="图片信息" :column="2" border style="margin-top: 20px;">
          <el-descriptions-item label="截图名称">{{ previewSnapshot.snapshotName }}</el-descriptions-item>
          <el-descriptions-item label="设备名称">{{ previewSnapshot.deviceName }}</el-descriptions-item>
          <el-descriptions-item label="截图类型">{{ getTypeText(previewSnapshot.snapshotType) }}</el-descriptions-item>
          <el-descriptions-item label="图片格式">{{ previewSnapshot.imageFormat.toUpperCase() }}</el-descriptions-item>
          <el-descriptions-item label="分辨率">{{ previewSnapshot.resolution }}</el-descriptions-item>
          <el-descriptions-item label="文件大小">{{ formatFileSize(previewSnapshot.fileSize) }}</el-descriptions-item>
          <el-descriptions-item label="截图时间">{{ parseTime(previewSnapshot.captureTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
          <el-descriptions-item label="下载次数">{{ previewSnapshot.downloadCount }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "VideoSnapshot",
  data() {
    return {
      loading: true,
      showSearch: true,
      total: 0,
      snapshotList: [],
      captureOpen: false,
      previewOpen: false,
      previewSnapshot: null,
      dateRange: [],
      // 统计数据
      totalSnapshots: 0,
      alarmSnapshots: 0,
      todaySnapshots: 0,
      unprocessedSnapshots: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 12,
        deviceName: null,
        alarmType: null,
        processStatus: null
      },
      captureForm: {
        deviceId: null,
        snapshotName: '',
        imageFormat: 'jpg',
        resolution: '1920x1080'
      },
      deviceOptions: [
        { id: 1, deviceName: '入口监控摄像头' },
        { id: 2, deviceName: '货架区域摄像头1' }
      ],
      captureRules: {
        deviceId: [{ required: true, message: "请选择设备", trigger: "change" }],
        snapshotName: [{ required: true, message: "截图名称不能为空", trigger: "blur" }]
      }
    };
  },
  computed: {
    multiple() {
      return !this.snapshotList.some(item => item.selected);
    }
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      // 模拟数据
      setTimeout(() => {
        this.snapshotList = [
          {
            id: 1,
            snapshotName: '人员入侵告警',
            deviceName: '入口监控摄像头',
            alarmType: 'intrusion',
            alarmLevel: 'high',
            alarmDescription: '检测到未授权人员进入禁区',
            location: '仓库主入口',
            filePath: '/static/images/alarm1.jpg',
            imageFormat: 'jpg',
            resolution: '1920x1080',
            fileSize: 1024*512,
            captureTime: '2025-01-20 14:30:00',
            confidence: 0.95,
            processStatus: 0,
            isAlarm: true,
            selected: false
          },
          {
            id: 2,
            snapshotName: '异常行为检测',
            deviceName: '货架区域摄像头1',
            alarmType: 'behavior',
            alarmLevel: 'medium',
            alarmDescription: '检测到人员在货架区域异常停留',
            location: '货架A区',
            filePath: '/static/images/alarm2.jpg',
            imageFormat: 'jpg',
            resolution: '1920x1080',
            fileSize: 1024*768,
            captureTime: '2025-01-20 15:15:00',
            confidence: 0.87,
            processStatus: 1,
            isAlarm: true,
            selected: false
          },
          {
            id: 3,
            snapshotName: '物体遗留告警',
            deviceName: '出口监控摄像头',
            alarmType: 'object_left',
            alarmLevel: 'low',
            alarmDescription: '检测到出口区域有物体遗留',
            location: '仓库主出口',
            filePath: '/static/images/alarm3.jpg',
            imageFormat: 'jpg',
            resolution: '1920x1080',
            fileSize: 1024*600,
            captureTime: '2025-01-20 16:45:00',
            confidence: 0.78,
            processStatus: 0,
            isAlarm: true,
            selected: false
          },
          {
            id: 4,
            snapshotName: '人脸识别告警',
            deviceName: '门禁摄像头',
            alarmType: 'face_detection',
            alarmLevel: 'high',
            alarmDescription: '检测到黑名单人员',
            location: '门禁通道',
            filePath: '/static/images/alarm4.jpg',
            imageFormat: 'jpg',
            resolution: '1920x1080',
            fileSize: 1024*800,
            captureTime: '2025-01-20 17:20:00',
            confidence: 0.92,
            processStatus: 2,
            isAlarm: true,
            selected: false
          }
        ];

        // 更新统计数据
        this.totalSnapshots = this.snapshotList.length;
        this.alarmSnapshots = this.snapshotList.filter(s => s.isAlarm).length;
        this.todaySnapshots = this.snapshotList.filter(s => s.captureTime.includes('2025-01-20')).length;
        this.unprocessedSnapshots = this.snapshotList.filter(s => s.processStatus === 0).length;
        this.total = this.snapshotList.length;
        this.loading = false;
      }, 1000);
    },
    handleQuery() { this.queryParams.pageNum = 1; this.getList(); },
    resetQuery() { this.resetForm("queryForm"); this.dateRange = []; this.handleQuery(); },
    handleSelectionChange() {
      // 选择变更处理
    },
    handleCapture() {
      this.captureForm = {
        deviceId: null,
        snapshotName: '',
        imageFormat: 'jpg',
        resolution: '1920x1080'
      };
      this.captureOpen = true;
    },
    submitCapture() {
      this.$refs["captureForm"].validate(valid => {
        if (valid) {
          this.$modal.msgSuccess("截图成功");
          this.captureOpen = false;
          this.getList();
        }
      });
    },
    handlePreview(snapshot) {
      this.previewSnapshot = snapshot;
      this.previewOpen = true;
    },
    handleDownload(snapshot) {
      if (snapshot) {
        this.$modal.msgSuccess("下载开始，请稍候...");
      } else {
        const selected = this.snapshotList.filter(item => item.selected);
        if (selected.length === 0) {
          this.$modal.msgWarning("请选择要下载的截图");
          return;
        }
        this.$modal.msgSuccess(`开始下载${selected.length}张截图`);
      }
    },
    handleDelete(snapshot) {
      if (snapshot) {
        this.$modal.confirm('是否确认删除截图"' + snapshot.snapshotName + '"？').then(() => {
          this.$modal.msgSuccess("删除成功");
          this.getList();
        });
      } else {
        const selected = this.snapshotList.filter(item => item.selected);
        if (selected.length === 0) {
          this.$modal.msgWarning("请选择要删除的截图");
          return;
        }
        this.$modal.confirm(`是否确认删除选中的${selected.length}张截图？`).then(() => {
          this.$modal.msgSuccess("删除成功");
          this.getList();
        });
      }
    },
    handleExport() { this.$modal.msgSuccess("导出成功"); },
    getTypeColor(type) {
      const colorMap = {
        'manual': '',
        'auto': 'success',
        'alarm': 'danger'
      };
      return colorMap[type] || '';
    },
    getTypeText(type) {
      const textMap = {
        'manual': '手动',
        'auto': '自动',
        'alarm': '告警'
      };
      return textMap[type] || type;
    },
    formatFileSize(size) {
      if (!size) return '0 B';
      const units = ['B', 'KB', 'MB', 'GB'];
      let index = 0;
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024;
        index++;
      }
      return size.toFixed(2) + ' ' + units[index];
    },
    // 告警相关方法
    handleProcess(snapshot) {
      this.$prompt('请输入处理备注', '处理告警截图', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPlaceholder: '请输入处理结果和备注'
      }).then(({ value }) => {
        snapshot.processStatus = 1;
        snapshot.processNote = value;
        this.unprocessedSnapshots--;
        this.$message.success('处理成功');
      });
    },
    handleBatchProcess() {
      const selectedSnapshots = this.snapshotList.filter(s => s.selected && s.processStatus === 0);
      if (selectedSnapshots.length === 0) {
        this.$message.warning('请选择待处理的告警截图');
        return;
      }

      this.$confirm(`确认批量处理 ${selectedSnapshots.length} 个告警截图？`, '批量处理', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        selectedSnapshots.forEach(snapshot => {
          snapshot.processStatus = 1;
        });
        this.unprocessedSnapshots -= selectedSnapshots.length;
        this.$message.success('批量处理成功');
      });
    },
    getStatusType(status) {
      const types = { 0: 'warning', 1: 'success', 2: 'info' };
      return types[status] || 'info';
    },
    getStatusText(status) {
      const texts = { 0: '待处理', 1: '已处理', 2: '已忽略' };
      return texts[status] || '未知';
    },
    getAlarmTypeText(type) {
      const texts = {
        intrusion: '人员入侵',
        behavior: '异常行为',
        object_left: '物体遗留',
        area_breach: '区域闯入',
        face_detection: '人脸识别',
        vehicle_violation: '车辆违停'
      };
      return texts[type] || '未知告警';
    },
    getAlarmLevelType(level) {
      const types = { low: 'info', medium: 'warning', high: 'danger' };
      return types[level] || 'info';
    },
    getAlarmLevelText(level) {
      const texts = { low: '低', medium: '中', high: '高' };
      return texts[level] || '未知';
    }
  }
};
</script>

<style scoped>
.snapshot-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.snapshot-card {
  position: relative;
  cursor: pointer;
}

.snapshot-image {
  position: relative;
}

.snapshot-overlay {
  position: absolute;
  top: 10px;
  right: 10px;
}

.snapshot-info {
  padding: 15px;
}

.snapshot-title {
  font-weight: bold;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.snapshot-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.device-name {
  color: #666;
  font-size: 12px;
}

.snapshot-time, .snapshot-size {
  color: #999;
  font-size: 12px;
  margin-bottom: 3px;
}

.snapshot-actions {
  margin-top: 10px;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 200px;
  background: #f5f7fa;
  color: #909399;
  font-size: 30px;
}

.preview-container {
  text-align: center;
}

/* 页面头部样式 */
.header-card {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section h2 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 24px;
}

.title-section p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.stats-section {
  min-width: 400px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 5px;
}

.stat-number.alarm {
  color: #F56C6C;
}

.stat-number.today {
  color: #67C23A;
}

.stat-number.unprocessed {
  color: #E6A23C;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

/* 告警截图样式 */
.snapshot-card.alarm-card {
  border-left: 4px solid #F56C6C;
}

.snapshot-card.unprocessed {
  border-top: 3px solid #E6A23C;
}

.alarm-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 10;
}

.status-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
}

.snapshot-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.meta-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
  color: #606266;
}

.meta-item i {
  margin-right: 6px;
  color: #909399;
  width: 14px;
}

.alarm-description {
  display: flex;
  align-items: flex-start;
  background: #FEF0F0;
  border: 1px solid #FDE2E2;
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 8px;
  font-size: 12px;
  color: #F56C6C;
}

.alarm-description i {
  margin-right: 6px;
  margin-top: 1px;
  flex-shrink: 0;
}

.snapshot-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-size: 11px;
  color: #C0C4CC;
}

.confidence {
  color: #67C23A;
  font-weight: 500;
}
</style>
