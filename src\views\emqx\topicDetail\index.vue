<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="page-title">
        <h2>主题详情</h2>
        <p>{{ topicName }}</p>
      </div>
      <div class="page-actions">
        <el-button @click="goBack">返回</el-button>
        <el-button type="primary" @click="refreshData">刷新</el-button>
        <el-button type="success" @click="subscribeToTopic">订阅主题</el-button>
        <el-button type="warning" @click="testTopic">测试连通性</el-button>
      </div>
    </div>

    <!-- 主题基本信息 -->
    <el-card class="info-card" shadow="hover">
      <div slot="header">
        <span>基本信息</span>
      </div>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="info-item">
            <label>主题名称:</label>
            <span class="topic-name">{{ topicInfo.topic }}</span>
          </div>
          <div class="info-item">
            <label>业务模块:</label>
            <el-tag :type="getModuleTagType(topicInfo.module)" size="small">
              {{ topicInfo.module || '未知' }}
            </el-tag>
          </div>
          <div class="info-item">
            <label>主题功能:</label>
            <el-tag :type="getFunctionTagType(topicInfo.function)" size="small">
              {{ topicInfo.function || '其他' }}
            </el-tag>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <label>节点:</label>
            <span>{{ topicInfo.node || 'N/A' }}</span>
          </div>
          <div class="info-item">
            <label>订阅者数量:</label>
            <el-badge :value="subscriberCount" class="item">
              <i class="el-icon-user"></i>
            </el-badge>
          </div>
          <div class="info-item">
            <label>消息数量:</label>
            <span>{{ topicInfo.messages || 0 }}</span>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 订阅者列表 -->
    <el-card class="subscribers-card" shadow="hover">
      <div slot="header">
        <span>订阅者列表</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="refreshSubscribers">刷新</el-button>
      </div>
      <el-table
        v-loading="subscribersLoading"
        :data="subscribers"
        style="width: 100%"
      >
        <el-table-column label="客户端ID" prop="clientid" min-width="200" />
        <el-table-column label="QoS等级" prop="qos" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getQosTagType(scope.row.qos)" size="small">
              QoS {{ scope.row.qos }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="节点" prop="node" width="150" align="center" />
        <el-table-column label="订阅时间" prop="subscribeTime" width="180" align="center">
          <template slot-scope="scope">
            {{ formatTime(scope.row.subscribeTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="primary" @click="viewClient(scope.row)">查看客户端</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 消息监控 -->
    <el-card class="monitoring-card" shadow="hover">
      <div slot="header">
        <span>消息监控</span>
        <el-switch
          v-model="monitoringEnabled"
          active-text="开启监控"
          inactive-text="关闭监控"
          @change="toggleMonitoring"
        />
      </div>
      <div v-if="monitoringEnabled" class="monitoring-content">
        <div class="monitoring-stats">
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ messageStats.received || 0 }}</div>
                <div class="stat-label">接收消息</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ messageStats.sent || 0 }}</div>
                <div class="stat-label">发送消息</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ messageStats.rate || 0 }}</div>
                <div class="stat-label">消息速率/秒</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ messageStats.lastMessage || 'N/A' }}</div>
                <div class="stat-label">最后消息时间</div>
              </div>
            </el-col>
          </el-row>
        </div>
        
        <!-- 实时消息日志 -->
        <div class="message-log">
          <div class="log-header">
            <span>实时消息日志</span>
            <el-button size="mini" @click="clearMessageLog">清空日志</el-button>
          </div>
          <div class="log-content">
            <div v-if="messageLog.length === 0" class="empty-log">
              暂无消息
            </div>
            <div v-else class="log-items">
              <div v-for="(message, index) in messageLog" :key="index" class="log-item">
                <div class="log-time">{{ formatTime(message.timestamp) }}</div>
                <div class="log-payload">{{ message.payload }}</div>
                <div class="log-qos">QoS: {{ message.qos }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="monitoring-disabled">
        <el-empty description="消息监控已关闭"></el-empty>
      </div>
    </el-card>

    <!-- 主题测试 -->
    <el-card class="test-card" shadow="hover">
      <div slot="header">
        <span>主题测试</span>
      </div>
      <el-form :model="testForm" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="消息内容:">
              <el-input
                v-model="testForm.payload"
                type="textarea"
                :rows="4"
                placeholder="请输入测试消息内容"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="QoS等级:">
              <el-select v-model="testForm.qos" style="width: 100%">
                <el-option label="QoS 0" :value="0"></el-option>
                <el-option label="QoS 1" :value="1"></el-option>
                <el-option label="QoS 2" :value="2"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="保留消息:">
              <el-switch v-model="testForm.retain"></el-switch>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="sendTestMessage" :loading="testLoading">发送测试消息</el-button>
              <el-button @click="resetTestForm">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      
      <!-- 测试结果 -->
      <div v-if="testResult" class="test-result">
        <div class="result-header">测试结果</div>
        <div class="result-content">
          <el-alert
            :title="testResult.message"
            :type="testResult.status === 'SUCCESS' ? 'success' : 'error'"
            :closable="false"
            show-icon
          />
          <div v-if="testResult.details" class="result-details">
            <pre>{{ JSON.stringify(testResult.details, null, 2) }}</pre>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import {
  getTopicInfo,
  getTopicSubscribers,
  testTopicConnectivity,
  publishTestMessage
} from '@/api/emqx/topicManagement'

export default {
  name: 'TopicDetail',
  data() {
    return {
      // 主题信息
      topicName: '',
      topicInfo: {},
      
      // 订阅者
      subscribers: [],
      subscriberCount: 0,
      subscribersLoading: false,
      
      // 消息监控
      monitoringEnabled: false,
      messageStats: {},
      messageLog: [],
      monitoringTimer: null,
      
      // 主题测试
      testForm: {
        payload: '{"message": "test", "timestamp": "' + new Date().toISOString() + '"}',
        qos: 1,
        retain: false
      },
      testLoading: false,
      testResult: null
    }
  },
  
  created() {
    this.topicName = this.$route.query.topic || ''
    if (this.topicName) {
      this.init()
    }
  },
  
  beforeDestroy() {
    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer)
    }
  },
  
  methods: {
    // 初始化
    async init() {
      await this.getTopicDetail()
      await this.getSubscribers()
    },
    
    // 获取主题详情
    async getTopicDetail() {
      try {
        const response = await getTopicInfo(this.topicName)
        this.topicInfo = response.data || {}
      } catch (error) {
        this.$message.error('获取主题详情失败')
        console.error(error)
      }
    },
    
    // 获取订阅者列表
    async getSubscribers() {
      this.subscribersLoading = true
      try {
        const response = await getTopicSubscribers(this.topicName)
        this.subscribers = response.data.subscribers || []
        this.subscriberCount = response.data.subscriberCount || 0
      } catch (error) {
        this.$message.error('获取订阅者列表失败')
        console.error(error)
      } finally {
        this.subscribersLoading = false
      }
    },
    
    // 刷新数据
    refreshData() {
      this.init()
      this.$message.success('数据已刷新')
    },
    
    // 刷新订阅者
    refreshSubscribers() {
      this.getSubscribers()
    },
    
    // 返回
    goBack() {
      this.$router.go(-1)
    },
    
    // 订阅主题
    subscribeToTopic() {
      this.$message.info('订阅功能开发中...')
    },
    
    // 测试主题
    async testTopic() {
      try {
        const response = await testTopicConnectivity({
          topic: this.topicName,
          payload: 'connectivity test'
        })
        
        if (response.data.status === 'SUCCESS') {
          this.$message.success('主题连通性测试成功')
        } else {
          this.$message.error('主题连通性测试失败: ' + response.data.message)
        }
      } catch (error) {
        this.$message.error('测试失败')
        console.error(error)
      }
    },
    
    // 切换监控状态
    toggleMonitoring(enabled) {
      if (enabled) {
        this.startMonitoring()
      } else {
        this.stopMonitoring()
      }
    },
    
    // 开始监控
    startMonitoring() {
      this.monitoringTimer = setInterval(() => {
        this.updateMessageStats()
      }, 5000) // 5秒更新一次
      this.$message.success('消息监控已开启')
    },
    
    // 停止监控
    stopMonitoring() {
      if (this.monitoringTimer) {
        clearInterval(this.monitoringTimer)
        this.monitoringTimer = null
      }
      this.$message.info('消息监控已关闭')
    },
    
    // 更新消息统计
    updateMessageStats() {
      // 模拟更新消息统计
      this.messageStats = {
        received: Math.floor(Math.random() * 100),
        sent: Math.floor(Math.random() * 100),
        rate: Math.floor(Math.random() * 10),
        lastMessage: new Date().toLocaleTimeString()
      }
      
      // 模拟添加消息日志
      if (Math.random() > 0.7) {
        this.addMessageLog({
          timestamp: Date.now(),
          payload: `{"data": ${Math.random()}, "timestamp": "${new Date().toISOString()}"}`,
          qos: Math.floor(Math.random() * 3)
        })
      }
    },
    
    // 添加消息日志
    addMessageLog(message) {
      this.messageLog.unshift(message)
      if (this.messageLog.length > 50) {
        this.messageLog = this.messageLog.slice(0, 50)
      }
    },
    
    // 清空消息日志
    clearMessageLog() {
      this.messageLog = []
      this.$message.success('日志已清空')
    },
    
    // 发送测试消息
    async sendTestMessage() {
      this.testLoading = true
      this.testResult = null
      
      try {
        const response = await publishTestMessage({
          topic: this.topicName,
          payload: this.testForm.payload,
          qos: this.testForm.qos,
          retain: this.testForm.retain
        })
        
        this.testResult = {
          status: 'SUCCESS',
          message: '测试消息发送成功',
          details: response.data
        }
      } catch (error) {
        this.testResult = {
          status: 'ERROR',
          message: '测试消息发送失败: ' + error.message,
          details: error
        }
      } finally {
        this.testLoading = false
      }
    },
    
    // 重置测试表单
    resetTestForm() {
      this.testForm = {
        payload: '{"message": "test", "timestamp": "' + new Date().toISOString() + '"}',
        qos: 1,
        retain: false
      }
      this.testResult = null
    },
    
    // 查看客户端
    viewClient(subscriber) {
      this.$router.push({
        path: '/emqx/client-detail',
        query: { clientId: subscriber.clientid }
      })
    },
    
    // 获取模块标签类型
    getModuleTagType(module) {
      const types = {
        'video': 'primary',
        'access': 'success',
        'weight': 'warning',
        'device': 'info',
        'system': 'danger'
      }
      return types[module] || ''
    },
    
    // 获取功能标签类型
    getFunctionTagType(func) {
      const types = {
        'heartbeat': 'success',
        'status': 'primary',
        'data': 'warning',
        'alarm': 'danger',
        'config': 'info'
      }
      return types[func] || ''
    },
    
    // 获取QoS标签类型
    getQosTagType(qos) {
      const types = {
        0: 'info',
        1: 'success',
        2: 'warning'
      }
      return types[qos] || ''
    },
    
    // 格式化时间
    formatTime(timestamp) {
      if (!timestamp) return 'N/A'
      return new Date(timestamp).toLocaleString()
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .page-title {
    h2 {
      margin: 0;
      color: #303133;
    }

    p {
      margin: 5px 0 0 0;
      color: #409eff;
      font-family: 'Courier New', monospace;
      font-size: 16px;
      font-weight: bold;
    }
  }
}

.info-card,
.subscribers-card,
.monitoring-card,
.test-card {
  margin-bottom: 20px;

  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;

    label {
      width: 120px;
      color: #606266;
      font-weight: bold;
    }

    .topic-name {
      font-family: 'Courier New', monospace;
      color: #409eff;
      font-weight: bold;
    }
  }
}

.monitoring-content {
  .monitoring-stats {
    margin-bottom: 20px;

    .stat-item {
      text-align: center;
      padding: 20px;
      background: #f5f7fa;
      border-radius: 8px;

      .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #303133;
        line-height: 1;
      }

      .stat-label {
        font-size: 14px;
        color: #909399;
        margin-top: 5px;
      }
    }
  }

  .message-log {
    .log-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      padding-bottom: 10px;
      border-bottom: 1px solid #e4e7ed;

      span {
        font-weight: bold;
        color: #303133;
      }
    }

    .log-content {
      max-height: 300px;
      overflow-y: auto;
      border: 1px solid #e4e7ed;
      border-radius: 4px;

      .empty-log {
        text-align: center;
        padding: 40px;
        color: #909399;
      }

      .log-items {
        .log-item {
          display: flex;
          align-items: center;
          padding: 10px;
          border-bottom: 1px solid #f5f7fa;

          &:last-child {
            border-bottom: none;
          }

          .log-time {
            width: 120px;
            font-size: 12px;
            color: #909399;
            flex-shrink: 0;
          }

          .log-payload {
            flex: 1;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #303133;
            margin: 0 10px;
            word-break: break-all;
          }

          .log-qos {
            width: 60px;
            font-size: 12px;
            color: #606266;
            flex-shrink: 0;
          }
        }
      }
    }
  }
}

.monitoring-disabled {
  text-align: center;
  padding: 40px;
}

.test-card {
  .test-result {
    margin-top: 20px;

    .result-header {
      font-weight: bold;
      color: #303133;
      margin-bottom: 10px;
    }

    .result-content {
      .result-details {
        margin-top: 10px;

        pre {
          background: #f5f7fa;
          padding: 10px;
          border-radius: 4px;
          font-size: 12px;
          color: #303133;
          overflow-x: auto;
        }
      }
    }
  }
}
</style>
