<template>
  <div class="advanced-gauge" :style="containerStyle">
    <div ref="chartContainer" class="chart-container"></div>
    <div v-if="showValue" class="value-display">
      <span class="value">{{ formatValue(currentValue) }}</span>
      <span class="unit">{{ config.unit || '' }}</span>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'AdvancedGauge',
  props: {
    config: {
      type: Object,
      default: () => ({})
    },
    value: {
      type: Number,
      default: 0
    },
    width: {
      type: Number,
      default: 200
    },
    height: {
      type: Number,
      default: 200
    }
  },
  data() {
    return {
      chart: null,
      currentValue: 0,
      animationTimer: null
    }
  },
  computed: {
    containerStyle() {
      return {
        width: this.width + 'px',
        height: this.height + 'px',
        position: 'relative'
      }
    },
    showValue() {
      return this.config.showValue !== false
    },
    gaugeConfig() {
      return {
        min: this.config.min || 0,
        max: this.config.max || 100,
        splitNumber: this.config.splitNumber || 10,
        axisLine: {
          lineStyle: {
            width: this.config.lineWidth || 20,
            color: this.config.colors || [
              [0.3, '#67e0e3'],
              [0.7, '#37a2da'],
              [1, '#fd666d']
            ]
          }
        },
        pointer: {
          itemStyle: {
            color: this.config.pointerColor || 'auto'
          }
        },
        axisTick: {
          distance: -30,
          length: 8,
          lineStyle: {
            color: '#fff',
            width: 2
          }
        },
        splitLine: {
          distance: -30,
          length: 30,
          lineStyle: {
            color: '#fff',
            width: 4
          }
        },
        axisLabel: {
          color: this.config.labelColor || 'auto',
          distance: 40,
          fontSize: this.config.fontSize || 12
        },
        detail: {
          valueAnimation: true,
          formatter: '{value}',
          color: this.config.valueColor || 'auto',
          fontSize: this.config.valueFontSize || 16,
          offsetCenter: [0, '70%']
        },
        data: [
          {
            value: this.currentValue,
            name: this.config.title || ''
          }
        ]
      }
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.animateToValue(newVal)
      },
      immediate: true
    },
    config: {
      handler() {
        this.updateChart()
      },
      deep: true
    }
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
    if (this.animationTimer) {
      clearInterval(this.animationTimer)
    }
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chartContainer)
      this.updateChart()
      
      // 响应式调整
      window.addEventListener('resize', this.handleResize)
    },
    
    updateChart() {
      if (!this.chart) return
      
      const option = {
        series: [
          {
            name: this.config.title || 'Gauge',
            type: 'gauge',
            radius: this.config.radius || '75%',
            center: this.config.center || ['50%', '60%'],
            startAngle: this.config.startAngle || 200,
            endAngle: this.config.endAngle || -40,
            ...this.gaugeConfig
          }
        ]
      }
      
      // 添加背景圆环
      if (this.config.showBackground) {
        option.series.unshift({
          type: 'gauge',
          radius: (this.config.radius || '75%'),
          center: this.config.center || ['50%', '60%'],
          startAngle: this.config.startAngle || 200,
          endAngle: this.config.endAngle || -40,
          axisLine: {
            lineStyle: {
              width: (this.config.lineWidth || 20) + 5,
              color: [[1, this.config.backgroundColor || '#e6e6e6']]
            }
          },
          axisTick: { show: false },
          splitLine: { show: false },
          axisLabel: { show: false },
          pointer: { show: false },
          detail: { show: false }
        })
      }
      
      this.chart.setOption(option, true)
    },
    
    animateToValue(targetValue) {
      if (this.animationTimer) {
        clearInterval(this.animationTimer)
      }
      
      const startValue = this.currentValue
      const duration = this.config.animationDuration || 1000
      const steps = 60
      const stepValue = (targetValue - startValue) / steps
      const stepTime = duration / steps
      
      let currentStep = 0
      
      this.animationTimer = setInterval(() => {
        currentStep++
        this.currentValue = startValue + (stepValue * currentStep)
        
        if (currentStep >= steps) {
          this.currentValue = targetValue
          clearInterval(this.animationTimer)
          this.animationTimer = null
        }
        
        this.updateGaugeValue()
      }, stepTime)
    },
    
    updateGaugeValue() {
      if (!this.chart) return
      
      this.chart.setOption({
        series: [{
          data: [{
            value: this.currentValue,
            name: this.config.title || ''
          }]
        }]
      })
    },
    
    formatValue(value) {
      if (this.config.formatter) {
        return this.config.formatter(value)
      }
      
      const precision = this.config.precision || 1
      return Number(value).toFixed(precision)
    },
    
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.advanced-gauge {
  position: relative;
  
  .chart-container {
    width: 100%;
    height: 100%;
  }
  
  .value-display {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    
    .value {
      font-size: 18px;
      font-weight: bold;
      color: #333;
    }
    
    .unit {
      font-size: 12px;
      color: #666;
      margin-left: 4px;
    }
  }
}
</style>
