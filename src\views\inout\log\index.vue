<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon">
              <i class="el-icon-document" style="color: #409EFF;"></i>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ statistics.totalLogs || 0 }}</div>
              <div class="statistics-label">总日志数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon">
              <i class="el-icon-upload2" style="color: #67C23A;"></i>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ statistics.inLogs || 0 }}</div>
              <div class="statistics-label">入库日志</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon">
              <i class="el-icon-download" style="color: #E6A23C;"></i>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ statistics.outLogs || 0 }}</div>
              <div class="statistics-label">出库日志</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon">
              <i class="el-icon-warning" style="color: #F56C6C;"></i>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ statistics.errorLogs || 0 }}</div>
              <div class="statistics-label">异常日志</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 查询表单 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="单据编号" prop="billNo">
        <el-input
          v-model="queryParams.billNo"
          placeholder="请输入单据编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="操作类型" prop="operationType">
        <el-select v-model="queryParams.operationType" placeholder="请选择操作类型" clearable>
          <el-option label="入库" value="IN" />
          <el-option label="出库" value="OUT" />
        </el-select>
      </el-form-item>
      <el-form-item label="操作结果" prop="operationResult">
        <el-select v-model="queryParams.operationResult" placeholder="请选择操作结果" clearable>
          <el-option label="成功" value="1" />
          <el-option label="失败" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="操作时间" prop="operationTime">
        <el-date-picker
          v-model="queryParams.operationTime"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          clearable>
        </el-date-picker>
      </el-form-item>
      <el-form-item label="操作人员" prop="operatorName">
        <el-input
          v-model="queryParams.operatorName"
          placeholder="请输入操作人员"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-refresh"
          size="mini"
          @click="handleRefresh"
        >刷新</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-brush"
          size="mini"
          @click="handleCleanLog"
          v-hasPermi="['inout:log:clean']"
        >日志清理</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['inout:log:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['inout:log:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 日志表格 -->
    <el-table v-loading="loading" :data="logList" @selection-change="handleSelectionChange" stripe>
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="60" align="center" />
      <el-table-column label="单据编号" align="center" prop="billNo" min-width="120">
        <template slot-scope="scope">
          <el-link type="primary" @click="viewBillDetail(scope.row)">{{ scope.row.billNo }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="明细ID" align="center" prop="detailId" width="80" />
      <el-table-column label="操作类型" align="center" prop="operationType" width="80">
        <template slot-scope="scope">
          <el-tag :type="scope.row.operationType === 'IN' ? 'success' : 'warning'" size="mini">
            {{ scope.row.operationType === 'IN' ? '入库' : '出库' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作阶段" align="center" prop="operationStage" width="100">
        <template slot-scope="scope">
          <el-tag :type="getStageTagType(scope.row.operationStage)" size="mini">
            {{ getStageText(scope.row.operationStage) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作结果" align="center" prop="operationResult" width="80">
        <template slot-scope="scope">
          <el-tag :type="scope.row.operationResult === '1' ? 'success' : 'danger'" size="mini">
            {{ scope.row.operationResult === '1' ? '成功' : '失败' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作内容" align="left" prop="operationContent" min-width="200" show-overflow-tooltip />
      <el-table-column label="操作人员" align="center" prop="operatorName" width="100" />
      <el-table-column label="操作时间" align="center" prop="createTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['inout:log:remove']"
            style="color: #F56C6C;"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 日志详情对话框 -->
    <el-dialog title="日志详情" :visible.sync="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="单据编号">{{ logDetail.billNo }}</el-descriptions-item>
        <el-descriptions-item label="明细ID">{{ logDetail.detailId }}</el-descriptions-item>
        <el-descriptions-item label="操作类型">
          <el-tag :type="logDetail.operationType === 'IN' ? 'success' : 'warning'" size="small">
            {{ logDetail.operationType === 'IN' ? '入库' : '出库' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="操作阶段">
          <el-tag :type="getStageTagType(logDetail.operationStage)" size="small">
            {{ getStageText(logDetail.operationStage) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="操作结果">
          <el-tag :type="logDetail.operationResult === '1' ? 'success' : 'danger'" size="small">
            {{ logDetail.operationResult === '1' ? '成功' : '失败' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="操作人员">{{ logDetail.operatorName }}</el-descriptions-item>
        <el-descriptions-item label="操作时间" :span="2">
          {{ parseTime(logDetail.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </el-descriptions-item>
        <el-descriptions-item label="操作内容" :span="2">
          <div style="white-space: pre-wrap;">{{ logDetail.operationContent }}</div>
        </el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 日志清理对话框 -->
    <el-dialog title="日志清理" :visible.sync="cleanOpen" width="600px" append-to-body>
      <el-alert
        title="注意：日志清理操作不可逆，请谨慎操作！"
        type="warning"
        :closable="false"
        style="margin-bottom: 20px;">
      </el-alert>

      <el-form ref="cleanForm" :model="cleanForm" :rules="cleanRules" label-width="120px">
        <el-form-item label="清理方式" prop="cleanType">
          <el-radio-group v-model="cleanForm.cleanType">
            <el-radio label="byDate">按日期清理</el-radio>
            <el-radio label="byCount">按数量清理</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="cleanForm.cleanType === 'byDate'" label="保留天数" prop="retentionDays">
          <el-input-number
            v-model="cleanForm.retentionDays"
            :min="1"
            :max="365"
            placeholder="请输入保留天数"
            style="width: 200px;">
          </el-input-number>
          <span style="margin-left: 10px; color: #909399;">天前的日志将被清理</span>
        </el-form-item>

        <el-form-item v-if="cleanForm.cleanType === 'byCount'" label="保留数量" prop="retentionCount">
          <el-input-number
            v-model="cleanForm.retentionCount"
            :min="100"
            :max="100000"
            placeholder="请输入保留数量"
            style="width: 200px;">
          </el-input-number>
          <span style="margin-left: 10px; color: #909399;">仅保留最新的N条日志</span>
        </el-form-item>

        <el-form-item label="操作类型" prop="operationType">
          <el-select v-model="cleanForm.operationType" placeholder="选择要清理的操作类型" clearable>
            <el-option label="全部" value="" />
            <el-option label="入库" value="IN" />
            <el-option label="出库" value="OUT" />
          </el-select>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cleanOpen = false">取 消</el-button>
        <el-button type="primary" @click="handleCleanConfirm" :loading="cleanLoading">确认清理</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listLog, getLog, delLog, executeClean } from "@/api/inout/log"

export default {
  name: "InoutLog",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 出入库日志表格数据
      logList: [],
      // 统计数据
      statistics: {
        totalLogs: 0,
        inLogs: 0,
        outLogs: 0,
        errorLogs: 0
      },
      // 日志详情对话框
      detailOpen: false,
      logDetail: {},
      // 日志清理对话框
      cleanOpen: false,
      cleanLoading: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        billNo: null,
        operationType: null,
        operationResult: null,
        operationTime: null,
        operatorName: null,
      },
      // 清理表单参数
      cleanForm: {
        cleanType: 'byDate',
        retentionDays: 90,
        retentionCount: 10000,
        operationType: ''
      },
      // 清理表单校验
      cleanRules: {
        retentionDays: [
          { required: true, message: "保留天数不能为空", trigger: "blur" },
          { type: 'number', min: 1, max: 365, message: "保留天数必须在1-365之间", trigger: "blur" }
        ],
        retentionCount: [
          { required: true, message: "保留数量不能为空", trigger: "blur" },
          { type: 'number', min: 100, max: 100000, message: "保留数量必须在100-100000之间", trigger: "blur" }
        ]
      }
    }
  },
  created() {
    this.getList()
    this.getStatistics()
  },
  methods: {
    /** 查询出入库日志列表 */
    getList() {
      this.loading = true
      listLog(this.queryParams).then(response => {
        this.logList = response.rows
        this.total = response.total
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    /** 获取统计数据 */
    getStatistics() {
      // 模拟统计数据，实际应该调用API
      this.statistics = {
        totalLogs: 1250,
        inLogs: 680,
        outLogs: 520,
        errorLogs: 50
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    /** 刷新按钮操作 */
    handleRefresh() {
      this.getList()
      this.getStatistics()
      this.$message.success("刷新成功")
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 查看日志详情 */
    handleView(row) {
      this.logDetail = { ...row }
      this.detailOpen = true
    },
    /** 查看单据详情 */
    viewBillDetail(row) {
      // 跳转到单据详情页面
      this.$router.push({
        path: '/inout/detail',
        query: { billNo: row.billNo }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除出入库日志编号为"' + ids + '"的数据项？').then(function() {
        return delLog(ids)
      }).then(() => {
        this.getList()
        this.getStatistics()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 日志清理按钮操作 */
    handleCleanLog() {
      this.cleanOpen = true
    },
    /** 确认清理操作 */
    handleCleanConfirm() {
      this.$refs["cleanForm"].validate(valid => {
        if (valid) {
          this.$modal.confirm('确认要清理日志吗？此操作不可逆！').then(() => {
            this.cleanLoading = true
            const params = {
              cleanType: this.cleanForm.cleanType,
              retentionDays: this.cleanForm.cleanType === 'byDate' ? this.cleanForm.retentionDays : null,
              retentionCount: this.cleanForm.cleanType === 'byCount' ? this.cleanForm.retentionCount : null,
              operationType: this.cleanForm.operationType
            }

            executeClean(params).then(response => {
              this.$modal.msgSuccess("日志清理成功，共清理 " + (response.data?.deletedCount || 0) + " 条记录")
              this.cleanOpen = false
              this.cleanLoading = false
              this.getList()
              this.getStatistics()
            }).catch(() => {
              this.cleanLoading = false
            })
          }).catch(() => {
            this.cleanLoading = false
          })
        }
      })
    },
    /** 获取操作阶段文本 */
    getStageText(stage) {
      const stageMap = {
        'START': '开始',
        'PROGRESS': '进行中',
        'COMPLETE': '完成',
        'ERROR': '异常'
      }
      return stageMap[stage] || stage
    },
    /** 获取操作阶段标签类型 */
    getStageTagType(stage) {
      const typeMap = {
        'START': 'info',
        'PROGRESS': 'warning',
        'COMPLETE': 'success',
        'ERROR': 'danger'
      }
      return typeMap[stage] || 'info'
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('inout/log/export', {
        ...this.queryParams
      }, `inout_log_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}

.mb8 {
  margin-bottom: 8px;
}

.statistics-card {
  cursor: pointer;
  transition: all 0.3s;
}

.statistics-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.statistics-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.statistics-icon {
  font-size: 32px;
  margin-right: 15px;
  width: 50px;
  text-align: center;
}

.statistics-info {
  flex: 1;
}

.statistics-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
  margin-bottom: 5px;
}

.statistics-label {
  font-size: 14px;
  color: #909399;
  line-height: 1;
}

.el-table {
  border-radius: 4px;
  overflow: hidden;
}

.el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
}

.el-descriptions {
  margin-top: 20px;
}

.el-alert {
  border-radius: 4px;
}

.dialog-footer {
  text-align: right;
}

.el-form-item {
  margin-bottom: 18px;
}

.el-input-number {
  width: 100%;
}

.el-radio-group {
  width: 100%;
}

.el-radio {
  margin-right: 20px;
}
</style>
