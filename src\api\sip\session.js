import request from '@/utils/request'

// SIP 会话管理 API

/**
 * 获取会话列表
 */
export function getSipSessions(params) {
  return request({
    url: '/api/sip/sessions',
    method: 'get',
    params
  })
}

/**
 * 获取会话详情
 */
export function getSipSessionDetails(sessionId) {
  return request({
    url: `/api/sip/sessions/${sessionId}`,
    method: 'get'
  })
}

/**
 * 终止会话
 */
export function terminateSession(sessionId, reason) {
  return request({
    url: `/api/sip/sessions/${sessionId}/terminate`,
    method: 'post',
    data: { reason }
  })
}

/**
 * 获取会话统计
 */
export function getSessionStatistics() {
  return request({
    url: '/api/sip/sessions/statistics',
    method: 'get'
  })
}

/**
 * 获取活跃会话
 */
export function getActiveSessions() {
  return request({
    url: '/api/sip/sessions/active',
    method: 'get'
  })
}

/**
 * 获取设备会话
 */
export function getDeviceSessions(deviceId) {
  return request({
    url: `/api/sip/sessions/device/${deviceId}`,
    method: 'get'
  })
}

/**
 * 批量终止会话
 */
export function batchTerminateSessions(sessionIds, reason) {
  return request({
    url: '/api/sip/sessions/batch-terminate',
    method: 'post',
    data: { sessionIds, reason }
  })
}

/**
 * 获取会话历史
 */
export function getSessionHistory(params) {
  return request({
    url: '/api/sip/sessions/history',
    method: 'get',
    params
  })
}

/**
 * 搜索会话
 */
export function searchSessions(keyword, params) {
  return request({
    url: '/api/sip/sessions/search',
    method: 'get',
    params: { keyword, ...params }
  })
}

/**
 * 导出会话列表
 */
export function exportSessions(params) {
  return request({
    url: '/api/sip/sessions/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 获取会话媒体信息
 */
export function getSessionMediaInfo(sessionId) {
  return request({
    url: `/api/sip/sessions/${sessionId}/media`,
    method: 'get'
  })
}

/**
 * 暂停会话
 */
export function pauseSession(sessionId) {
  return request({
    url: `/api/sip/sessions/${sessionId}/pause`,
    method: 'post'
  })
}

/**
 * 恢复会话
 */
export function resumeSession(sessionId) {
  return request({
    url: `/api/sip/sessions/${sessionId}/resume`,
    method: 'post'
  })
}
