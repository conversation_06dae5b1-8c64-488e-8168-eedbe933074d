import request from '@/utils/request'

// EMQX实时监控API

/**
 * 获取EMQX服务器状态
 */
export function getServerStatus() {
  return request({
    url: '/emqx/monitoring/server-status',
    method: 'get'
  })
}

/**
 * 获取实时指标数据
 */
export function getRealTimeMetrics() {
  return request({
    url: '/emqx/monitoring/metrics',
    method: 'get'
  })
}

/**
 * 获取活跃客户端列表
 */
export function getActiveClients(params) {
  return request({
    url: '/emqx/monitoring/clients',
    method: 'get',
    params
  })
}

/**
 * 获取客户端详细信息
 */
export function getClientDetail(clientId) {
  return request({
    url: `/emqx/monitoring/client/${clientId}`,
    method: 'get'
  })
}

/**
 * 断开客户端连接
 */
export function disconnectClient(clientId) {
  return request({
    url: `/emqx/monitoring/client/${clientId}/disconnect`,
    method: 'post'
  })
}

/**
 * 获取系统告警列表
 */
export function getSystemAlerts() {
  return request({
    url: '/emqx/monitoring/alerts',
    method: 'get'
  })
}

/**
 * 确认告警
 */
export function acknowledgeAlert(alertId) {
  return request({
    url: `/emqx/monitoring/alert/${alertId}/acknowledge`,
    method: 'post'
  })
}

/**
 * 获取历史监控数据
 */
export function getHistoricalData(params) {
  return request({
    url: '/emqx/monitoring/historical',
    method: 'get',
    params
  })
}

/**
 * 获取主题统计信息
 */
export function getTopicStatistics() {
  return request({
    url: '/emqx/monitoring/topic-stats',
    method: 'get'
  })
}

/**
 * 获取节点信息
 */
export function getNodeInfo() {
  return request({
    url: '/emqx/monitoring/nodes',
    method: 'get'
  })
}
