<template>
  <div class="mqtt-settings">
    <div class="page-header">
      <h2>MQTT 系统设置</h2>
      <p>配置 MQTT Broker 的系统参数和运行设置</p>
    </div>
    
    <el-card>
      <div class="coming-soon">
        <i class="el-icon-setting" style="font-size: 64px; color: #909399;"></i>
        <h3>功能开发中...</h3>
        <p>MQTT 系统设置功能正在开发中，敬请期待！</p>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'MqttSettings'
}
</script>

<style lang="scss" scoped>
.mqtt-settings {
  padding: 20px;
  
  .page-header {
    margin-bottom: 20px;
    
    h2 {
      margin: 0 0 8px 0;
      color: #303133;
    }
    
    p {
      margin: 0;
      color: #606266;
    }
  }
  
  .coming-soon {
    text-align: center;
    padding: 60px 20px;
    
    h3 {
      margin: 20px 0 10px 0;
      color: #303133;
    }
    
    p {
      color: #606266;
    }
  }
}
</style>
