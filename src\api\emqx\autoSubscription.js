import request from '@/utils/request'

// ==================== EMQX自动订阅管理API ====================

// 扫描EMQX中的所有主题
export function scanAllTopics() {
  return request({
    url: '/emqx/auto-subscription/scan/topics',
    method: 'get'
  })
}

// 按模块分类主题
export function classifyTopicsByModule(topics) {
  return request({
    url: '/emqx/auto-subscription/classify/module',
    method: 'post',
    data: topics
  })
}

// 按设备类型分类主题
export function classifyTopicsByDeviceType(topics) {
  return request({
    url: '/emqx/auto-subscription/classify/device-type',
    method: 'post',
    data: topics
  })
}

// 按功能分类主题
export function classifyTopicsByFunction(topics) {
  return request({
    url: '/emqx/auto-subscription/classify/function',
    method: 'post',
    data: topics
  })
}

// 为指定模块自动订阅
export function autoSubscribeForModule(moduleName, clientId) {
  return request({
    url: `/emqx/auto-subscription/module/${moduleName}/subscribe`,
    method: 'post',
    params: {
      clientId: clientId
    }
  })
}

// 为指定设备类型自动订阅
export function autoSubscribeForDeviceType(deviceType, clientId) {
  return request({
    url: `/emqx/auto-subscription/device-type/${deviceType}/subscribe`,
    method: 'post',
    params: {
      clientId: clientId
    }
  })
}

// 为指定功能自动订阅
export function autoSubscribeForFunction(functionName, clientId) {
  return request({
    url: `/emqx/auto-subscription/function/${functionName}/subscribe`,
    method: 'post',
    params: {
      clientId: clientId
    }
  })
}

// 初始化模块订阅
export function initializeModuleSubscriptions(moduleName) {
  return request({
    url: `/emqx/auto-subscription/module/${moduleName}/initialize`,
    method: 'post'
  })
}

// 获取模块推荐主题
export function getRecommendedTopicsForModule(moduleName) {
  return request({
    url: `/emqx/auto-subscription/module/${moduleName}/recommended`,
    method: 'get'
  })
}

// 验证模块订阅状态
export function verifyModuleSubscriptions(moduleName, clientId) {
  return request({
    url: `/emqx/auto-subscription/module/${moduleName}/verify`,
    method: 'get',
    params: {
      clientId: clientId
    }
  })
}

// 同步模块订阅
export function syncModuleSubscriptions(moduleName) {
  return request({
    url: `/emqx/auto-subscription/module/${moduleName}/sync`,
    method: 'post'
  })
}

// 获取自动订阅配置
export function getAutoSubscriptionConfig() {
  return request({
    url: '/emqx/auto-subscription/config',
    method: 'get'
  })
}

// 获取所有模块配置
export function getAllModuleConfigs() {
  return request({
    url: '/emqx/auto-subscription/modules/config',
    method: 'get'
  })
}

// 批量初始化所有模块
export function initializeAllModules() {
  return request({
    url: '/emqx/auto-subscription/modules/initialize-all',
    method: 'post'
  })
}

// 获取模块订阅统计
export function getModuleSubscriptionStats(moduleName) {
  return request({
    url: `/emqx/auto-subscription/module/${moduleName}/stats`,
    method: 'get'
  })
}

// 扫描并自动分类订阅
export function scanAndClassifyTopics() {
  return request({
    url: '/emqx/auto-subscription/scan-and-classify',
    method: 'post'
  })
}

// ==================== 高级功能API ====================

// 批量操作模块订阅
export function batchOperateModuleSubscriptions(operation, modules) {
  return request({
    url: '/emqx/auto-subscription/batch/operate',
    method: 'post',
    data: {
      operation: operation,
      modules: modules
    }
  })
}

// 获取模块订阅历史
export function getModuleSubscriptionHistory(moduleName, query) {
  return request({
    url: `/emqx/auto-subscription/module/${moduleName}/history`,
    method: 'get',
    params: query
  })
}

// 导出模块订阅配置
export function exportModuleSubscriptionConfig(moduleName) {
  return request({
    url: `/emqx/auto-subscription/module/${moduleName}/export`,
    method: 'get'
  })
}

// 导入模块订阅配置
export function importModuleSubscriptionConfig(moduleName, configData) {
  return request({
    url: `/emqx/auto-subscription/module/${moduleName}/import`,
    method: 'post',
    data: configData
  })
}

// 获取模块订阅健康状态
export function getModuleSubscriptionHealth(moduleName) {
  return request({
    url: `/emqx/auto-subscription/module/${moduleName}/health`,
    method: 'get'
  })
}

// 修复模块订阅问题
export function repairModuleSubscriptions(moduleName, repairOptions) {
  return request({
    url: `/emqx/auto-subscription/module/${moduleName}/repair`,
    method: 'post',
    data: repairOptions
  })
}

// 获取订阅推荐引擎状态
export function getRecommendationEngineStatus() {
  return request({
    url: '/emqx/auto-subscription/recommendation/status',
    method: 'get'
  })
}

// 训练订阅推荐模型
export function trainRecommendationModel(trainingData) {
  return request({
    url: '/emqx/auto-subscription/recommendation/train',
    method: 'post',
    data: trainingData
  })
}

// 获取智能订阅建议
export function getIntelligentSubscriptionSuggestions(context) {
  return request({
    url: '/emqx/auto-subscription/intelligent/suggestions',
    method: 'post',
    data: context
  })
}

// 应用智能订阅建议
export function applyIntelligentSuggestions(suggestions) {
  return request({
    url: '/emqx/auto-subscription/intelligent/apply',
    method: 'post',
    data: suggestions
  })
}

// ==================== 监控和分析API ====================

// 获取订阅性能分析
export function getSubscriptionPerformanceAnalysis(query) {
  return request({
    url: '/emqx/auto-subscription/analysis/performance',
    method: 'get',
    params: query
  })
}

// 获取订阅使用情况报告
export function getSubscriptionUsageReport(query) {
  return request({
    url: '/emqx/auto-subscription/analysis/usage',
    method: 'get',
    params: query
  })
}

// 获取订阅趋势分析
export function getSubscriptionTrendAnalysis(query) {
  return request({
    url: '/emqx/auto-subscription/analysis/trend',
    method: 'get',
    params: query
  })
}

// 获取订阅异常检测结果
export function getSubscriptionAnomalyDetection() {
  return request({
    url: '/emqx/auto-subscription/analysis/anomaly',
    method: 'get'
  })
}

// 生成订阅优化建议
export function generateSubscriptionOptimizationSuggestions() {
  return request({
    url: '/emqx/auto-subscription/optimization/suggestions',
    method: 'post'
  })
}

// 应用订阅优化
export function applySubscriptionOptimization(optimizationPlan) {
  return request({
    url: '/emqx/auto-subscription/optimization/apply',
    method: 'post',
    data: optimizationPlan
  })
}

// ==================== 测试和验证API ====================

// 测试模块订阅连通性
export function testModuleSubscriptionConnectivity(moduleName) {
  return request({
    url: `/emqx/auto-subscription/module/${moduleName}/test`,
    method: 'post'
  })
}

// 模拟订阅场景
export function simulateSubscriptionScenario(scenario) {
  return request({
    url: '/emqx/auto-subscription/simulation/scenario',
    method: 'post',
    data: scenario
  })
}

// 压力测试订阅系统
export function stressTestSubscriptionSystem(testConfig) {
  return request({
    url: '/emqx/auto-subscription/test/stress',
    method: 'post',
    data: testConfig
  })
}

// 验证订阅规则
export function validateSubscriptionRules(rules) {
  return request({
    url: '/emqx/auto-subscription/validation/rules',
    method: 'post',
    data: rules
  })
}

// 检查订阅冲突
export function checkSubscriptionConflicts(subscriptions) {
  return request({
    url: '/emqx/auto-subscription/validation/conflicts',
    method: 'post',
    data: subscriptions
  })
}
