<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon success">
              <i class="el-icon-unlock"></i>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ statistics.successCount || 0 }}</div>
              <div class="statistics-label">成功开门</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon danger">
              <i class="el-icon-lock"></i>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ statistics.failCount || 0 }}</div>
              <div class="statistics-label">开门失败</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon warning">
              <i class="el-icon-warning"></i>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ statistics.abnormalCount || 0 }}</div>
              <div class="statistics-label">异常记录</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon info">
              <i class="el-icon-time"></i>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ statistics.todayCount || 0 }}</div>
              <div class="statistics-label">今日记录</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="设备名称" prop="deviceCode">
        <el-select v-model="queryParams.deviceCode" placeholder="请选择设备" clearable>
          <el-option
            v-for="device in deviceOptions"
            :key="device.deviceCode"
            :label="device.deviceName"
            :value="device.deviceCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="人员姓名" prop="personName">
        <el-input
          v-model="queryParams.personName"
          placeholder="请输入人员姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="进出类型" prop="actionType">
        <el-select v-model="queryParams.actionType" placeholder="请选择进出类型" clearable>
          <el-option label="进入" value="in" />
          <el-option label="离开" value="out" />
        </el-select>
      </el-form-item>
      <el-form-item label="验证方式" prop="verifyType">
        <el-select v-model="queryParams.verifyType" placeholder="请选择验证方式" clearable>
          <el-option label="人脸识别" value="face" />
          <el-option label="门禁卡" value="card" />
          <el-option label="二维码" value="qrcode" />
          <el-option label="远程开门" value="remote" />
        </el-select>
      </el-form-item>
      <el-form-item label="验证结果" prop="verifyResult">
        <el-select v-model="queryParams.verifyResult" placeholder="请选择验证结果" clearable>
          <el-option label="成功" value="1" />
          <el-option label="失败" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="操作时间" prop="actionTime">
        <el-date-picker
          v-model="queryParams.actionTime"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-refresh"
          size="mini"
          @click="handleRefresh"
        >刷新数据</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['access:log:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['access:log:remove']"
        >批量删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-setting"
          size="mini"
          @click="handleCleanup"
          v-hasPermi="['access:log:cleanup']"
        >清理记录</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-video-camera"
          size="mini"
          @click="handleRealTimeMonitor"
        >实时监控</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="logList" @selection-change="handleSelectionChange" row-key="id">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="操作时间" align="center" prop="actionTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.actionTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="设备名称" align="center" prop="deviceName" width="150" />
      <el-table-column label="设备编码" align="center" prop="deviceCode" width="120" />
      <el-table-column label="人员姓名" align="center" prop="personName" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.personName">{{ scope.row.personName }}</span>
          <el-tag v-else type="warning" size="mini">未知</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="工号" align="center" prop="employeeNo" width="100" />
      <el-table-column label="进出类型" align="center" prop="actionType" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.actionType === 'in' ? 'success' : 'info'" size="mini">
            {{ scope.row.actionType === 'in' ? '进入' : '离开' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="验证方式" align="center" prop="verifyType" width="100">
        <template slot-scope="scope">
          <el-tag :type="getVerifyTypeTag(scope.row.verifyType)" size="mini">
            {{ getVerifyTypeText(scope.row.verifyType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="验证结果" align="center" prop="verifyResult" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.verifyResult === '1' ? 'success' : 'danger'" size="mini">
            {{ scope.row.verifyResult === '1' ? '成功' : '失败' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="抓拍图片" align="center" prop="snapshotUrl" width="100">
        <template slot-scope="scope">
          <image-preview v-if="scope.row.snapshotUrl" :src="scope.row.snapshotUrl" :width="50" :height="50"/>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="失败原因" align="center" prop="reason" width="120" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['access:log:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 门禁记录详情对话框 -->
    <el-dialog title="门禁记录详情" :visible.sync="detailVisible" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="操作时间">{{ parseTime(currentRecord.actionTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="设备名称">{{ currentRecord.deviceName }}</el-descriptions-item>
        <el-descriptions-item label="设备编码">{{ currentRecord.deviceCode }}</el-descriptions-item>
        <el-descriptions-item label="设备IP">{{ currentRecord.deviceIp }}</el-descriptions-item>
        <el-descriptions-item label="人员姓名">{{ currentRecord.personName || '未知' }}</el-descriptions-item>
        <el-descriptions-item label="人员工号">{{ currentRecord.employeeNo || '-' }}</el-descriptions-item>
        <el-descriptions-item label="进出类型">
          <el-tag :type="currentRecord.actionType === 'in' ? 'success' : 'info'">
            {{ currentRecord.actionType === 'in' ? '进入' : '离开' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="验证方式">
          <el-tag :type="getVerifyTypeTag(currentRecord.verifyType)">
            {{ getVerifyTypeText(currentRecord.verifyType) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="验证结果">
          <el-tag :type="currentRecord.verifyResult === '1' ? 'success' : 'danger'">
            {{ currentRecord.verifyResult === '1' ? '验证成功' : '验证失败' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="失败原因" v-if="currentRecord.reason">{{ currentRecord.reason }}</el-descriptions-item>
        <el-descriptions-item label="门号" v-if="currentRecord.doorNumber">{{ currentRecord.doorNumber }}</el-descriptions-item>
        <el-descriptions-item label="卡号" v-if="currentRecord.cardNo">{{ currentRecord.cardNo }}</el-descriptions-item>
      </el-descriptions>

      <div class="image-preview-section" style="margin-top: 20px;" v-if="currentRecord.snapshotUrl">
        <h4>抓拍图片</h4>
        <el-image
          :src="currentRecord.snapshotUrl"
          style="width: 300px; height: 200px;"
          fit="cover"
          :preview-src-list="[currentRecord.snapshotUrl]"
        />
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="detailVisible = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 实时监控对话框 -->
    <el-dialog title="实时门禁监控" :visible.sync="monitorVisible" width="1200px" append-to-body>
      <div class="real-time-monitor">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-table :data="realTimeData" height="400" style="width: 100%">
              <el-table-column label="时间" width="160">
                <template slot-scope="scope">
                  {{ parseTime(scope.row.actionTime, '{h}:{i}:{s}') }}
                </template>
              </el-table-column>
              <el-table-column label="设备" prop="deviceName" width="150" />
              <el-table-column label="人员" prop="personName" width="100" />
              <el-table-column label="类型" width="80">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.actionType === 'in' ? 'success' : 'info'" size="mini">
                    {{ scope.row.actionType === 'in' ? '进入' : '离开' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="方式" width="100">
                <template slot-scope="scope">
                  <el-tag :type="getVerifyTypeTag(scope.row.verifyType)" size="mini">
                    {{ getVerifyTypeText(scope.row.verifyType) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="结果" width="80">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.verifyResult === '1' ? 'success' : 'danger'" size="mini">
                    {{ scope.row.verifyResult === '1' ? '成功' : '失败' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="抓拍" width="80">
                <template slot-scope="scope">
                  <image-preview v-if="scope.row.snapshotUrl" :src="scope.row.snapshotUrl" :width="40" :height="40"/>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="monitorVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDoorLog, getDoorLog, delDoorLog, exportDoorLog, cleanupDoorLog, getRealTimeDoorLog, getDoorLogStatistics } from "@/api/access/doorLog"
import { listDevice } from "@/api/access/device"

export default {
  name: "Log",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 门禁记录表格数据
      logList: [],
      // 设备选项
      deviceOptions: [],
      // 统计数据
      statistics: {
        successCount: 0,
        failCount: 0,
        abnormalCount: 0,
        todayCount: 0
      },
      // 详情对话框
      detailVisible: false,
      currentRecord: {},
      // 实时监控对话框
      monitorVisible: false,
      realTimeData: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceCode: null,
        personName: null,
        actionType: null,
        verifyType: null,
        verifyResult: null,
        actionTime: null
      },
    }
  },
  created() {
    this.getList()
    this.getDeviceOptions()
    this.getStatistics()
  },
  methods: {
    /** 查询门禁记录列表 */
    getList() {
      this.loading = true
      // 处理时间范围参数
      const params = { ...this.queryParams }
      if (params.actionTime && params.actionTime.length === 2) {
        params.startTime = params.actionTime[0]
        params.endTime = params.actionTime[1]
        delete params.actionTime
      }

      listDoorLog(params).then(response => {
        this.logList = response.rows || []
        this.total = response.total || 0
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    /** 获取设备选项 */
    getDeviceOptions() {
      listDevice().then(response => {
        this.deviceOptions = response.rows || []
      })
    },
    /** 获取统计数据 */
    getStatistics() {
      // 调用统计接口获取真实数据
      getDoorLogStatistics().then(response => {
        this.statistics = response.data || {
          successCount: 0,
          failCount: 0,
          abnormalCount: 0,
          todayCount: 0
        };
      }).catch(error => {
        console.error('获取统计数据失败:', error);
        this.statistics = {
          successCount: 0,
          failCount: 0,
          abnormalCount: 0,
          todayCount: 0
        };
      });
    },
    /** 验证方式标签类型 */
    getVerifyTypeTag(type) {
      switch (type) {
        case 'face': return 'success'
        case 'card': return 'primary'
        case 'qrcode': return 'warning'
        case 'remote': return 'info'
        default: return 'info'
      }
    },
    /** 验证方式文本 */
    getVerifyTypeText(type) {
      switch (type) {
        case 'face': return '人脸识别'
        case 'card': return '门禁卡'
        case 'qrcode': return '二维码'
        case 'remote': return '远程开门'
        default: return '未知'
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 刷新数据 */
    handleRefresh() {
      this.getList()
      this.getStatistics()
      this.$modal.msgSuccess("数据已刷新")
    },
    /** 查看详情 */
    handleView(row) {
      this.currentRecord = row
      this.detailVisible = true
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('access/doorLog/export', {
        ...this.queryParams
      }, `门禁记录_${new Date().getTime()}.xlsx`)
    },
    /** 实时监控 */
    handleRealTimeMonitor() {
      this.monitorVisible = true
      this.loadRealTimeData()
      // 每5秒刷新一次实时数据
      this.realTimeTimer = setInterval(() => {
        this.loadRealTimeData()
      }, 5000)
    },
    /** 加载实时数据 */
    loadRealTimeData() {
      getRealTimeDoorLog().then(response => {
        this.realTimeData = response.data || []
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除选中的门禁记录？').then(() => {
        return delDoorLog(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 清理记录 */
    handleCleanup() {
      this.$modal.confirm('是否确认清理30天前的门禁记录？此操作不可恢复！').then(() => {
        return cleanupDoorLog({ days: 30 })
      }).then(() => {
        this.getList()
        this.getStatistics()
        this.$modal.msgSuccess("清理成功")
      }).catch(() => {})
    }
  },
  beforeDestroy() {
    if (this.realTimeTimer) {
      clearInterval(this.realTimeTimer)
    }
  }
}
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}

.statistics-card {
  cursor: pointer;
  transition: all 0.3s;
}

.statistics-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.statistics-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.statistics-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.statistics-icon.success {
  background: linear-gradient(135deg, #67C23A, #85CE61);
}

.statistics-icon.danger {
  background: linear-gradient(135deg, #F56C6C, #F78989);
}

.statistics-icon.warning {
  background: linear-gradient(135deg, #E6A23C, #EEBE77);
}

.statistics-icon.info {
  background: linear-gradient(135deg, #409EFF, #66B1FF);
}

.statistics-info {
  flex: 1;
}

.statistics-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.statistics-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.image-preview-section h4 {
  margin-bottom: 10px;
  color: #303133;
  font-size: 14px;
}

.real-time-monitor {
  padding: 10px 0;
}
</style>
