<template>
  <div class="app-container">
    <!-- 统计概览卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <i class="el-icon-data-analysis" style="color: #409EFF;"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.totalRecords || 0 }}</div>
              <div class="stats-label">总防错记录</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <i class="el-icon-success" style="color: #67C23A;"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.successCount || 0 }}</div>
              <div class="stats-label">防错成功</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <i class="el-icon-warning" style="color: #E6A23C;"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.warningCount || 0 }}</div>
              <div class="stats-label">防错预警</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <i class="el-icon-error" style="color: #F56C6C;"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.errorCount || 0 }}</div>
              <div class="stats-label">防错失败</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card>
          <div slot="header">
            <span>防错趋势图</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="refreshCharts">刷新</el-button>
          </div>
          <div id="trendChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <div slot="header">
            <span>防错类型分布</span>
          </div>
          <div id="typeChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细统计表格 -->
    <el-card class="mt20">
      <div slot="header">
        <span>详细统计</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="exportStats">导出统计</el-button>
      </div>
      
      <!-- 查询条件 -->
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
        <el-form-item label="统计日期" prop="dateRange">
          <el-date-picker
            v-model="dateRange"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="设备编号" prop="deviceCode">
          <el-input
            v-model="queryParams.deviceCode"
            placeholder="请输入设备编号"
            clearable
            style="width: 200px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table v-loading="loading" :data="statsList" border>
        <el-table-column label="日期" align="center" prop="statDate" width="120" />
        <el-table-column label="设备编号" align="center" prop="deviceCode" width="150" />
        <el-table-column label="总记录数" align="center" prop="totalCount" width="100" />
        <el-table-column label="成功次数" align="center" prop="successCount" width="100" />
        <el-table-column label="预警次数" align="center" prop="warningCount" width="100" />
        <el-table-column label="失败次数" align="center" prop="errorCount" width="100" />
        <el-table-column label="成功率" align="center" width="100">
          <template slot-scope="scope">
            <span>{{ calculateSuccessRate(scope.row) }}%</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script>
export default {
  name: "OutmisStats",
  data() {
    return {
      // 加载状态
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 统计列表
      statsList: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceCode: null,
        beginTime: null,
        endTime: null
      },
      // 统计数据
      statistics: {
        totalRecords: 0,
        successCount: 0,
        warningCount: 0,
        errorCount: 0
      }
    };
  },
  created() {
    this.getList();
    this.getStatistics();
  },
  mounted() {
    this.initCharts();
  },
  methods: {
    /** 查询统计列表 */
    getList() {
      this.loading = true;
      // 处理日期范围
      if (this.dateRange != null && this.dateRange.length === 2) {
        this.queryParams.beginTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      } else {
        this.queryParams.beginTime = null;
        this.queryParams.endTime = null;
      }
      
      // 模拟数据，实际应该调用API
      setTimeout(() => {
        this.statsList = [
          {
            statDate: '2025-01-20',
            deviceCode: 'WS001',
            totalCount: 156,
            successCount: 142,
            warningCount: 8,
            errorCount: 6,
            remark: '正常运行'
          },
          {
            statDate: '2025-01-19',
            deviceCode: 'WS001',
            totalCount: 134,
            successCount: 125,
            warningCount: 5,
            errorCount: 4,
            remark: '正常运行'
          }
        ];
        this.total = this.statsList.length;
        this.loading = false;
      }, 1000);
    },
    
    /** 获取统计数据 */
    getStatistics() {
      // 模拟统计数据，实际应该调用API
      this.statistics = {
        totalRecords: 1250,
        successCount: 1156,
        warningCount: 67,
        errorCount: 27
      };
    },
    
    /** 计算成功率 */
    calculateSuccessRate(row) {
      if (row.totalCount === 0) return 0;
      return Math.round((row.successCount / row.totalCount) * 100);
    },
    
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    
    /** 初始化图表 */
    initCharts() {
      // 这里应该使用ECharts初始化图表
      // 由于没有引入ECharts，暂时显示占位内容
      const trendChart = document.getElementById('trendChart');
      const typeChart = document.getElementById('typeChart');
      
      if (trendChart) {
        trendChart.innerHTML = '<div style="text-align: center; line-height: 300px; color: #999;">防错趋势图（需要集成ECharts）</div>';
      }
      
      if (typeChart) {
        typeChart.innerHTML = '<div style="text-align: center; line-height: 300px; color: #999;">防错类型分布图（需要集成ECharts）</div>';
      }
    },
    
    /** 刷新图表 */
    refreshCharts() {
      this.initCharts();
      this.$modal.msgSuccess("图表已刷新");
    },
    
    /** 导出统计 */
    exportStats() {
      this.$modal.msgSuccess("导出功能开发中");
    }
  }
};
</script>

<style lang="scss" scoped>
.stats-card {
  .stats-content {
    display: flex;
    align-items: center;
    
    .stats-icon {
      font-size: 40px;
      margin-right: 20px;
    }
    
    .stats-info {
      flex: 1;
      
      .stats-number {
        font-size: 28px;
        font-weight: bold;
        color: #303133;
        line-height: 1;
      }
      
      .stats-label {
        font-size: 14px;
        color: #909399;
        margin-top: 8px;
      }
    }
  }
}

.mb20 {
  margin-bottom: 20px;
}

.mt20 {
  margin-top: 20px;
}
</style>
