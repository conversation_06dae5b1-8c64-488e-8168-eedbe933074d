import request from '@/utils/request'

// 查询消息列表
export function getMessageList(query) {
  return request({
    url: '/emqx/message/list',
    method: 'get',
    params: query
  })
}

// 查询消息详细
export function getMessage(messageId) {
  return request({
    url: '/emqx/message/' + messageId,
    method: 'get'
  })
}

// 发送消息
export function sendMessage(data) {
  return request({
    url: '/emqx/message/send',
    method: 'post',
    data: data
  })
}

// 批量发送消息
export function batchSendMessage(data) {
  return request({
    url: '/emqx/message/batch/send',
    method: 'post',
    data: data
  })
}

// 删除消息
export function deleteMessage(messageId) {
  return request({
    url: '/emqx/message/' + messageId,
    method: 'delete'
  })
}

// 批量删除消息
export function batchDeleteMessage(messageIds) {
  return request({
    url: '/emqx/message/batch/delete',
    method: 'delete',
    data: messageIds
  })
}

// 清理历史消息
export function clearMessages(data) {
  return request({
    url: '/emqx/message/clear',
    method: 'post',
    data: data
  })
}

// 导出消息数据
export function exportMessageData(query) {
  return request({
    url: '/emqx/message/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获取消息统计信息
export function getMessageStats(query) {
  return request({
    url: '/emqx/message/stats',
    method: 'get',
    params: query
  })
}

// 获取实时消息流
export function getRealtimeMessages(query) {
  return request({
    url: '/emqx/message/realtime',
    method: 'get',
    params: query
  })
}

// 搜索消息
export function searchMessages(query) {
  return request({
    url: '/emqx/message/search',
    method: 'get',
    params: query
  })
}

// 获取消息主题列表
export function getMessageTopics() {
  return request({
    url: '/emqx/message/topics',
    method: 'get'
  })
}

// 获取消息客户端列表
export function getMessageClients() {
  return request({
    url: '/emqx/message/clients',
    method: 'get'
  })
}

// 重发消息
export function resendMessage(messageId) {
  return request({
    url: '/emqx/message/' + messageId + '/resend',
    method: 'post'
  })
}

// 验证消息格式
export function validateMessage(data) {
  return request({
    url: '/emqx/message/validate',
    method: 'post',
    data: data
  })
}

// 获取消息模板
export function getMessageTemplates() {
  return request({
    url: '/emqx/message/templates',
    method: 'get'
  })
}

// 保存消息模板
export function saveMessageTemplate(data) {
  return request({
    url: '/emqx/message/template',
    method: 'post',
    data: data
  })
}

// 删除消息模板
export function deleteMessageTemplate(templateId) {
  return request({
    url: '/emqx/message/template/' + templateId,
    method: 'delete'
  })
}
