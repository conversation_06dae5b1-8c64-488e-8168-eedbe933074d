import request from '@/utils/request'

// 查询重量传感器数据列表
export function listWeightData(query) {
  return request({
    url: '/dwms/weight/list',
    method: 'get',
    params: query
  })
}

// 查询重量传感器数据详细
export function getWeightData(id) {
  return request({
    url: '/dwms/weight/' + id,
    method: 'get'
  })
}

// 新增重量传感器数据
export function addWeightData(data) {
  return request({
    url: '/dwms/weight',
    method: 'post',
    data: data
  })
}

// 修改重量传感器数据
export function updateWeightData(data) {
  return request({
    url: '/dwms/weight',
    method: 'put',
    data: data
  })
}

// 删除重量传感器数据
export function delWeightData(id) {
  return request({
    url: '/dwms/weight/' + id,
    method: 'delete'
  })
}

// 根据传感器ID查询最新数据
export function getLatestBySensorId(sensorId) {
  return request({
    url: '/dwms/weight/latest/' + sensorId,
    method: 'get'
  })
}

// 根据库房ID查询传感器数据
export function getWeightDataByWarehouse(warehouseId) {
  return request({
    url: '/dwms/weight/warehouse/' + warehouseId,
    method: 'get'
  })
}

// 查询告警状态的传感器数据
export function getAlertWeightData(warehouseId) {
  return request({
    url: '/dwms/weight/alert/' + warehouseId,
    method: 'get'
  })
}

// 根据时间范围查询传感器数据
export function getWeightDataByTimeRange(params) {
  return request({
    url: '/dwms/weight/timerange',
    method: 'get',
    params: params
  })
}

// 查询传感器统计数据
export function getWeightStats(warehouseId) {
  return request({
    url: '/dwms/weight/stats/' + warehouseId,
    method: 'get'
  })
}

// 获取实时重量数据
export function getRealTimeWeightData(warehouseId) {
  return request({
    url: '/dwms/weight/realtime/' + warehouseId,
    method: 'get'
  })
}

// 获取重量趋势数据
export function getWeightTrendData(sensorId, hours) {
  return request({
    url: '/dwms/weight/trend',
    method: 'get',
    params: {
      sensorId: sensorId,
      hours: hours
    }
  })
}

// 设置重量告警阈值
export function setWeightAlertThreshold(data) {
  return request({
    url: '/dwms/weight/threshold',
    method: 'post',
    data: data
  })
}

// 清理过期数据
export function cleanExpiredData(retentionDays) {
  return request({
    url: '/dwms/weight/clean',
    method: 'delete',
    params: {
      retentionDays: retentionDays
    }
  })
}

// 批量导入重量数据
export function batchImportWeightData(data) {
  return request({
    url: '/dwms/weight/import',
    method: 'post',
    data: data
  })
}

// 导出重量数据
export function exportWeightData(query) {
  return request({
    url: '/dwms/weight/export',
    method: 'get',
    params: query
  })
}
