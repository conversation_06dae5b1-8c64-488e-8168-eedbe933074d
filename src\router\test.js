// 测试路由配置
export const testRoutes = [
  {
    path: '/test',
    component: () => import('@/layout'),
    redirect: '/test/material-detail',
    name: 'Test',
    meta: { title: '测试页面', icon: 'el-icon-s-tools' },
    children: [
      {
        path: 'material-detail',
        name: 'MaterialDetailTest',
        component: () => import('@/views/test/MaterialDetailTest'),
        meta: { title: '物料明细测试', icon: 'el-icon-document' }
      },
      {
        path: 'storage-location',
        name: 'StorageLocationTest',
        component: () => import('@/views/test/StorageLocationTest'),
        meta: { title: '存放位置测试', icon: 'el-icon-location' }
      },
      {
        path: 'material-dialog',
        name: 'MaterialDialogTest',
        component: () => import('@/views/test/MaterialDialogTest'),
        meta: { title: '物料对话框测试', icon: 'el-icon-plus' }
      }
    ]
  }
]
