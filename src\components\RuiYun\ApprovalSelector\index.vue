<template>
  <div class="approval-selector">
    <el-form-item :label="label" :prop="prop">
      <el-select 
        v-model="selectedValue" 
        :placeholder="placeholder" 
        :disabled="disabled" 
        :clearable="clearable"
        @change="handleChange">
        <el-option
          v-for="item in options"
          :key="item.workflowId"
          :label="item.workflowName"
          :value="item.workflowId"
          :disabled="item.status === '1'"
        />
      </el-select>
    </el-form-item>
  </div>
</template>

<script>
import { getWorkflowOptions } from "@/api/approval/workflow";

export default {
  name: "ApprovalSelector",
  props: {
    value: {
      type: [String, Number],
      default: ""
    },
    businessType: {
      type: String,
      default: ""
    },
    label: {
      type: String,
      default: "审批流程"
    },
    prop: {
      type: String,
      default: "workflowId"
    },
    placeholder: {
      type: String,
      default: "请选择审批流程"
    },
    disabled: {
      type: <PERSON><PERSON>an,
      default: false
    },
    clearable: {
      type: <PERSON>olean,
      default: true
    }
  },
  data() {
    return {
      options: [],
      selectedValue: this.value
    };
  },
  watch: {
    value(val) {
      this.selectedValue = val;
    }
  },
  created() {
    this.getOptions();
  },
  methods: {
    getOptions() {
      // 获取审批流程选项，可以根据业务类型过滤
      getWorkflowOptions({ businessType: this.businessType }).then(response => {
        if (response.code === 200 && response.data) {
          // 使用Map对象确保每个工作流只出现一次
          const workflowMap = new Map();
          response.data.forEach(item => {
            if (!workflowMap.has(item.workflowId)) {
              workflowMap.set(item.workflowId, item);
            }
          });
          
          // 将Map转换回数组
          this.options = Array.from(workflowMap.values());
        } else {
          this.options = [];
        }
      });
    },
    handleChange(val) {
      this.$emit("input", val);
      this.$emit("change", val);
    }
  }
};
</script>

<style scoped>
.approval-selector {
  display: inline-block;
  width: 100%;
}
</style> 