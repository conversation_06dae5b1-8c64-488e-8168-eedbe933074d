<template>
  <div class="component-preview">
    <div class="preview-header">
      <h4>组件预览</h4>
      <el-button-group>
        <el-button 
          :type="viewMode === 'design' ? 'primary' : 'default'" 
          size="mini"
          @click="setViewMode('design')"
        >
          设计视图
        </el-button>
        <el-button 
          :type="viewMode === 'preview' ? 'primary' : 'default'" 
          size="mini"
          @click="setViewMode('preview')"
        >
          预览视图
        </el-button>
      </el-button-group>
    </div>
    
    <div class="preview-content" :class="{ 'preview-mode': viewMode === 'preview' }">
      <div 
        v-if="!selectedComponent" 
        class="empty-preview"
      >
        <i class="el-icon-view"></i>
        <p>请选择一个组件进行预览</p>
      </div>
      
      <div v-else class="component-container">
        <!-- 设计视图 - 显示组件边框和信息 -->
        <div v-if="viewMode === 'design'" class="design-view">
          <div class="component-info">
            <span class="component-type">{{ selectedComponent.type }}</span>
            <span class="component-id">ID: {{ selectedComponent.id }}</span>
          </div>
          <component-renderer 
            :component="selectedComponent"
            :is-preview="true"
            @component-click="handleComponentClick"
          />
        </div>
        
        <!-- 预览视图 - 真实效果预览 -->
        <div v-else class="preview-view">
          <component-renderer 
            :component="selectedComponent"
            :is-preview="false"
            @component-click="handleComponentClick"
          />
        </div>
      </div>
    </div>
    
    <!-- 预览工具栏 -->
    <div class="preview-toolbar" v-if="selectedComponent">
      <el-button-group>
        <el-button size="mini" @click="zoomOut">
          <i class="el-icon-zoom-out"></i>
        </el-button>
        <el-button size="mini" @click="resetZoom">
          {{ Math.round(zoomLevel * 100) }}%
        </el-button>
        <el-button size="mini" @click="zoomIn">
          <i class="el-icon-zoom-in"></i>
        </el-button>
      </el-button-group>
      
      <el-button-group>
        <el-button size="mini" @click="refreshPreview">
          <i class="el-icon-refresh"></i>
          刷新
        </el-button>
        <el-button size="mini" @click="fullscreen">
          <i class="el-icon-full-screen"></i>
          全屏
        </el-button>
      </el-button-group>
    </div>
  </div>
</template>

<script>
import ComponentRenderer from './ComponentRenderer'

export default {
  name: 'ComponentPreview',
  components: {
    ComponentRenderer
  },
  props: {
    selectedComponent: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      viewMode: 'design', // design | preview
      zoomLevel: 1,
      isFullscreen: false
    }
  },
  methods: {
    setViewMode(mode) {
      this.viewMode = mode
      this.$emit('view-mode-change', mode)
    },
    
    handleComponentClick(component) {
      this.$emit('component-select', component)
    },
    
    zoomIn() {
      this.zoomLevel = Math.min(this.zoomLevel + 0.1, 3)
      this.updateZoom()
    },
    
    zoomOut() {
      this.zoomLevel = Math.max(this.zoomLevel - 0.1, 0.1)
      this.updateZoom()
    },
    
    resetZoom() {
      this.zoomLevel = 1
      this.updateZoom()
    },
    
    updateZoom() {
      const container = this.$el.querySelector('.component-container')
      if (container) {
        container.style.transform = `scale(${this.zoomLevel})`
      }
    },
    
    refreshPreview() {
      this.$emit('refresh-preview')
    },
    
    fullscreen() {
      this.isFullscreen = !this.isFullscreen
      this.$emit('fullscreen-toggle', this.isFullscreen)
    }
  },
  
  watch: {
    selectedComponent: {
      handler() {
        this.$nextTick(() => {
          this.updateZoom()
        })
      },
      immediate: true
    }
  }
}
</script>

<style scoped>
.component-preview {
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fff;
}

.preview-header {
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f5f7fa;
}

.preview-header h4 {
  margin: 0;
  font-size: 14px;
  color: #303133;
}

.preview-content {
  flex: 1;
  overflow: auto;
  position: relative;
  background: #fff;
}

.preview-content.preview-mode {
  background: #f0f2f5;
}

.empty-preview {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;
}

.empty-preview i {
  font-size: 48px;
  margin-bottom: 16px;
}

.component-container {
  padding: 20px;
  transform-origin: top left;
  transition: transform 0.3s ease;
}

.design-view {
  border: 2px dashed #409eff;
  border-radius: 4px;
  position: relative;
  min-height: 100px;
}

.component-info {
  position: absolute;
  top: -30px;
  left: 0;
  background: #409eff;
  color: white;
  padding: 4px 8px;
  border-radius: 4px 4px 0 0;
  font-size: 12px;
  z-index: 10;
}

.component-type {
  margin-right: 8px;
  font-weight: bold;
}

.component-id {
  opacity: 0.8;
}

.preview-view {
  min-height: 100px;
}

.preview-toolbar {
  padding: 8px 16px;
  border-top: 1px solid #e4e7ed;
  background: #f5f7fa;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .preview-header {
    flex-direction: column;
    gap: 8px;
  }
  
  .preview-toolbar {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
