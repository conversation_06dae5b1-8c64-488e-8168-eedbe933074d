import request from '@/utils/request'

// 查询货架信息列表
export function listRack(query) {
  return request({
    url: '/warehouse/rack/list',
    method: 'get',
    params: query
  })
}

// 查询货架信息详细
export function getRack(id) {
  return request({
    url: '/warehouse/rack/' + id,
    method: 'get'
  })
}

// 新增货架信息
export function addRack(data) {
  console.log("新增货架API请求数据:", JSON.stringify(data));
  return request({
    url: '/warehouse/rack',
    method: 'post',
    data: data
  })
}

// 修改货架信息
export function updateRack(data) {
  console.log("修改货架API请求数据:", JSON.stringify(data));
  return request({
    url: '/warehouse/rack',
    method: 'put',
    data: data
  })
}

// 删除货架信息
export function delRack(id) {
  return request({
    url: '/warehouse/rack/' + id,
    method: 'delete'
  })
}

// 批量生成货位
export function batchGenerateLocation(data) {
  return request({
    url: '/warehouse/location/batchGenerate',
    method: 'post',
    data: data
  })
}

// 根据区域ID查询货架列表
export function listRackByZoneId(zoneId) {
  return request({
    url: '/warehouse/rack/listByZoneId/' + zoneId,
    method: 'get'
  })
}
