<template>
  <div class="dwms-editor">
    <!-- 工具栏 -->
    <div class="editor-toolbar">
      <el-button-group>
        <el-button type="primary" icon="el-icon-folder-opened" @click="openConfig">打开</el-button>
        <el-button type="success" icon="el-icon-document-add" @click="newConfig">新建</el-button>
        <el-button type="info" icon="el-icon-document" @click="saveConfig">保存</el-button>
        <el-button type="warning" icon="el-icon-view" @click="previewConfig">预览</el-button>
      </el-button-group>
      
      <el-button-group style="margin-left: 20px;">
        <el-button icon="el-icon-back" @click="undo" :disabled="!canUndo">撤销</el-button>
        <el-button icon="el-icon-right" @click="redo" :disabled="!canRedo">重做</el-button>
      </el-button-group>
      
      <el-button-group style="margin-left: 20px;">
        <el-button icon="el-icon-zoom-in" @click="zoomIn">放大</el-button>
        <el-button icon="el-icon-zoom-out" @click="zoomOut">缩小</el-button>
        <el-button icon="el-icon-refresh" @click="resetZoom">重置</el-button>
      </el-button-group>
      
      <div style="float: right;">
        <span>缩放: {{ Math.round(zoom * 100) }}%</span>
      </div>
    </div>
    
    <!-- 主编辑区域 -->
    <div class="editor-main">
      <!-- 组件库面板 -->
      <div class="component-panel">
        <div class="panel-header">
          <h4>组件库</h4>
        </div>
        <div class="panel-content">
          <el-collapse v-model="activeCategories" accordion>
            <el-collapse-item 
              v-for="category in componentCategories" 
              :key="category.name"
              :title="category.label" 
              :name="category.name"
            >
              <div class="component-list">
                <div 
                  v-for="component in category.components" 
                  :key="component.id"
                  class="component-item"
                  draggable="true"
                  @dragstart="onComponentDragStart($event, component)"
                >
                  <i :class="component.icon"></i>
                  <span>{{ component.name }}</span>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
      
      <!-- 画布区域 -->
      <div class="canvas-container">
        <div class="canvas-wrapper" :style="canvasWrapperStyle">
          <div 
            ref="canvas"
            class="canvas"
            :style="canvasStyle"
            @drop="onCanvasDrop"
            @dragover="onCanvasDragOver"
            @click="onCanvasClick"
          >
            <!-- 网格背景 -->
            <div class="canvas-grid" v-if="showGrid"></div>
            
            <!-- 组件渲染 -->
            <component-renderer
              v-for="component in canvasComponents"
              :key="component.id"
              :component="component"
              :selected="selectedComponent && selectedComponent.id === component.id"
              :zoom="zoom"
              @select="selectComponent"
              @update="updateComponent"
              @delete="deleteComponent"
            />
            
            <!-- 选择框 -->
            <div 
              v-if="selectedComponent"
              class="selection-box"
              :style="selectionBoxStyle"
            >
              <!-- 调整手柄 -->
              <div class="resize-handle resize-handle-nw" @mousedown="startResize($event, 'nw')"></div>
              <div class="resize-handle resize-handle-ne" @mousedown="startResize($event, 'ne')"></div>
              <div class="resize-handle resize-handle-sw" @mousedown="startResize($event, 'sw')"></div>
              <div class="resize-handle resize-handle-se" @mousedown="startResize($event, 'se')"></div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 属性面板 -->
      <div class="property-panel">
        <div class="panel-header">
          <h4>属性设置</h4>
        </div>
        <div class="panel-content">
          <component-property-editor
            v-if="selectedComponent"
            :component="selectedComponent"
            @update="updateComponentProperty"
          />
          <div v-else class="no-selection">
            <p>请选择一个组件</p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 配置对话框 -->
    <el-dialog title="组态配置" :visible.sync="configDialogVisible" width="600px">
      <el-form ref="configForm" :model="configForm" :rules="configRules" label-width="100px">
        <el-form-item label="配置名称" prop="configName">
          <el-input v-model="configForm.configName" placeholder="请输入配置名称"></el-input>
        </el-form-item>
        <el-form-item label="配置类型" prop="configType">
          <el-select v-model="configForm.configType" placeholder="请选择配置类型">
            <el-option label="仪表板" value="dashboard"></el-option>
            <el-option label="监控面板" value="monitor"></el-option>
            <el-option label="导航地图" value="navigation"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="库房" prop="warehouseId">
          <el-select v-model="configForm.warehouseId" placeholder="请选择库房" clearable>
            <el-option 
              v-for="warehouse in warehouseList" 
              :key="warehouse.id" 
              :label="warehouse.name" 
              :value="warehouse.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="configForm.description" type="textarea" rows="3"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="configDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSaveConfig">确 定</el-button>
      </div>
    </el-dialog>
    
    <!-- 预览对话框 -->
    <el-dialog title="组态预览" :visible.sync="previewDialogVisible" width="90%" top="5vh">
      <div class="preview-container">
        <component-preview
          v-if="previewDialogVisible"
          :config="currentConfig"
          :components="canvasComponents"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listComponent } from '@/api/dwms/component'
import { getConfig, saveConfig as saveConfigApi } from '@/api/dwms/config'
import ComponentRenderer from './components/ComponentRenderer'
import ComponentPropertyEditor from './components/ComponentPropertyEditor'
import ComponentPreview from './components/ComponentPreview'

export default {
  name: 'DwmsEditor',
  components: {
    ComponentRenderer,
    ComponentPropertyEditor,
    ComponentPreview
  },
  data() {
    return {
      // 画布配置
      canvasConfig: {
        width: 1200,
        height: 800,
        background: '#f5f5f5'
      },
      
      // 画布组件
      canvasComponents: [],
      
      // 选中的组件
      selectedComponent: null,
      
      // 缩放比例
      zoom: 1,
      
      // 显示网格
      showGrid: true,
      
      // 组件库分类
      activeCategories: ['gauge'],
      componentCategories: [
        {
          name: 'gauge',
          label: '仪表盘',
          components: []
        },
        {
          name: 'chart',
          label: '图表',
          components: []
        },
        {
          name: 'status',
          label: '状态',
          components: []
        },
        {
          name: 'button',
          label: '控制',
          components: []
        },
        {
          name: 'device',
          label: '设备',
          components: []
        }
      ],
      
      // 历史记录
      history: [],
      historyIndex: -1,
      
      // 对话框
      configDialogVisible: false,
      previewDialogVisible: false,
      
      // 配置表单
      configForm: {
        configName: '',
        configType: 'dashboard',
        warehouseId: null,
        description: ''
      },
      configRules: {
        configName: [
          { required: true, message: '请输入配置名称', trigger: 'blur' }
        ],
        configType: [
          { required: true, message: '请选择配置类型', trigger: 'change' }
        ]
      },
      
      // 当前配置
      currentConfig: null,
      
      // 库房列表
      warehouseList: [],
      
      // 拖拽状态
      dragComponent: null,
      
      // 调整大小状态
      resizing: false,
      resizeDirection: null,
      resizeStartPos: null,
      resizeStartSize: null
    }
  },
  computed: {
    canvasStyle() {
      return {
        width: this.canvasConfig.width + 'px',
        height: this.canvasConfig.height + 'px',
        background: this.canvasConfig.background,
        transform: `scale(${this.zoom})`,
        transformOrigin: 'top left'
      }
    },
    canvasWrapperStyle() {
      return {
        width: (this.canvasConfig.width * this.zoom) + 'px',
        height: (this.canvasConfig.height * this.zoom) + 'px'
      }
    },
    selectionBoxStyle() {
      if (!this.selectedComponent) return {}
      
      return {
        left: (this.selectedComponent.x * this.zoom) + 'px',
        top: (this.selectedComponent.y * this.zoom) + 'px',
        width: (this.selectedComponent.width * this.zoom) + 'px',
        height: (this.selectedComponent.height * this.zoom) + 'px'
      }
    },
    canUndo() {
      return this.historyIndex > 0
    },
    canRedo() {
      return this.historyIndex < this.history.length - 1
    }
  },
  mounted() {
    this.loadComponentLibrary()
    this.initCanvas()
  },
  methods: {
    // 加载组件库
    async loadComponentLibrary() {
      try {
        const response = await listComponent({ isActive: 1 })
        const components = response.rows || []
        
        // 按分类组织组件
        this.componentCategories.forEach(category => {
          category.components = components.filter(comp => 
            comp.category === category.name
          ).map(comp => ({
            id: comp.id,
            name: comp.componentName,
            type: comp.componentType,
            icon: comp.componentIcon || 'el-icon-cpu',
            config: JSON.parse(comp.componentConfig || '{}')
          }))
        })
      } catch (error) {
        console.error('加载组件库失败:', error)
      }
    },
    
    // 初始化画布
    initCanvas() {
      // 添加键盘事件监听
      document.addEventListener('keydown', this.onKeyDown)
      
      // 添加鼠标事件监听
      document.addEventListener('mousemove', this.onMouseMove)
      document.addEventListener('mouseup', this.onMouseUp)
    },
    
    // 组件拖拽开始
    onComponentDragStart(event, component) {
      this.dragComponent = component
      event.dataTransfer.effectAllowed = 'copy'
    },
    
    // 画布拖拽悬停
    onCanvasDragOver(event) {
      event.preventDefault()
      event.dataTransfer.dropEffect = 'copy'
    },
    
    // 画布放置
    onCanvasDrop(event) {
      event.preventDefault()
      
      if (!this.dragComponent) return
      
      const rect = this.$refs.canvas.getBoundingClientRect()
      const x = (event.clientX - rect.left) / this.zoom
      const y = (event.clientY - rect.top) / this.zoom
      
      // 创建新组件实例
      const newComponent = {
        id: 'comp_' + Date.now(),
        type: this.dragComponent.type,
        name: this.dragComponent.name,
        x: Math.max(0, x - 50),
        y: Math.max(0, y - 25),
        width: 100,
        height: 100,
        config: { ...this.dragComponent.config },
        dataSource: null
      }
      
      this.canvasComponents.push(newComponent)
      this.selectComponent(newComponent)
      this.saveHistory()
      
      this.dragComponent = null
    },
    
    // 画布点击
    onCanvasClick(event) {
      if (event.target === this.$refs.canvas) {
        this.selectedComponent = null
      }
    },
    
    // 选择组件
    selectComponent(component) {
      this.selectedComponent = component
    },
    
    // 更新组件
    updateComponent(component) {
      const index = this.canvasComponents.findIndex(c => c.id === component.id)
      if (index !== -1) {
        this.$set(this.canvasComponents, index, component)
        this.saveHistory()
      }
    },
    
    // 删除组件
    deleteComponent(component) {
      const index = this.canvasComponents.findIndex(c => c.id === component.id)
      if (index !== -1) {
        this.canvasComponents.splice(index, 1)
        if (this.selectedComponent && this.selectedComponent.id === component.id) {
          this.selectedComponent = null
        }
        this.saveHistory()
      }
    },
    
    // 更新组件属性
    updateComponentProperty(property, value) {
      if (this.selectedComponent) {
        this.$set(this.selectedComponent, property, value)
        this.saveHistory()
      }
    },
    
    // 保存历史记录
    saveHistory() {
      const state = JSON.stringify({
        components: this.canvasComponents,
        config: this.canvasConfig
      })
      
      // 移除当前位置之后的历史记录
      this.history = this.history.slice(0, this.historyIndex + 1)
      this.history.push(state)
      this.historyIndex = this.history.length - 1
      
      // 限制历史记录数量
      if (this.history.length > 50) {
        this.history.shift()
        this.historyIndex--
      }
    },
    
    // 撤销
    undo() {
      if (this.canUndo) {
        this.historyIndex--
        this.restoreFromHistory()
      }
    },
    
    // 重做
    redo() {
      if (this.canRedo) {
        this.historyIndex++
        this.restoreFromHistory()
      }
    },
    
    // 从历史记录恢复
    restoreFromHistory() {
      if (this.historyIndex >= 0 && this.historyIndex < this.history.length) {
        const state = JSON.parse(this.history[this.historyIndex])
        this.canvasComponents = state.components
        this.canvasConfig = state.config
        this.selectedComponent = null
      }
    },
    
    // 缩放操作
    zoomIn() {
      this.zoom = Math.min(this.zoom + 0.1, 3)
    },
    zoomOut() {
      this.zoom = Math.max(this.zoom - 0.1, 0.1)
    },
    resetZoom() {
      this.zoom = 1
    },
    
    // 键盘事件
    onKeyDown(event) {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'z':
            event.preventDefault()
            if (event.shiftKey) {
              this.redo()
            } else {
              this.undo()
            }
            break
          case 's':
            event.preventDefault()
            this.saveConfig()
            break
        }
      } else if (event.key === 'Delete' && this.selectedComponent) {
        this.deleteComponent(this.selectedComponent)
      }
    },
    
    // 鼠标移动事件
    onMouseMove(event) {
      if (this.resizing && this.selectedComponent) {
        this.handleResize(event)
      }
    },
    
    // 鼠标释放事件
    onMouseUp() {
      if (this.resizing) {
        this.resizing = false
        this.resizeDirection = null
        this.saveHistory()
      }
    },
    
    // 开始调整大小
    startResize(event, direction) {
      event.stopPropagation()
      this.resizing = true
      this.resizeDirection = direction
      this.resizeStartPos = { x: event.clientX, y: event.clientY }
      this.resizeStartSize = {
        width: this.selectedComponent.width,
        height: this.selectedComponent.height,
        x: this.selectedComponent.x,
        y: this.selectedComponent.y
      }
    },
    
    // 处理调整大小
    handleResize(event) {
      if (!this.selectedComponent || !this.resizing) return
      
      const deltaX = (event.clientX - this.resizeStartPos.x) / this.zoom
      const deltaY = (event.clientY - this.resizeStartPos.y) / this.zoom
      
      const component = this.selectedComponent
      const startSize = this.resizeStartSize
      
      switch (this.resizeDirection) {
        case 'se':
          component.width = Math.max(20, startSize.width + deltaX)
          component.height = Math.max(20, startSize.height + deltaY)
          break
        case 'sw':
          component.width = Math.max(20, startSize.width - deltaX)
          component.height = Math.max(20, startSize.height + deltaY)
          component.x = startSize.x + deltaX
          break
        case 'ne':
          component.width = Math.max(20, startSize.width + deltaX)
          component.height = Math.max(20, startSize.height - deltaY)
          component.y = startSize.y + deltaY
          break
        case 'nw':
          component.width = Math.max(20, startSize.width - deltaX)
          component.height = Math.max(20, startSize.height - deltaY)
          component.x = startSize.x + deltaX
          component.y = startSize.y + deltaY
          break
      }
    },
    
    // 新建配置
    newConfig() {
      this.canvasComponents = []
      this.selectedComponent = null
      this.currentConfig = null
      this.canvasConfig = {
        width: 1200,
        height: 800,
        background: '#f5f5f5'
      }
      this.history = []
      this.historyIndex = -1
      this.saveHistory()
    },
    
    // 打开配置
    openConfig() {
      // TODO: 实现打开配置对话框
      this.$message.info('打开配置功能开发中...')
    },
    
    // 保存配置
    saveConfig() {
      this.configDialogVisible = true
    },
    
    // 处理保存配置
    async handleSaveConfig() {
      try {
        await this.$refs.configForm.validate()
        
        const configData = {
          components: this.canvasComponents,
          canvas: this.canvasConfig
        }
        
        const data = {
          ...this.configForm,
          configData: JSON.stringify(configData)
        }
        
        await saveConfigApi(data)
        this.$message.success('保存成功')
        this.configDialogVisible = false
      } catch (error) {
        console.error('保存失败:', error)
        this.$message.error('保存失败')
      }
    },
    
    // 预览配置
    previewConfig() {
      this.previewDialogVisible = true
    }
  },
  beforeDestroy() {
    document.removeEventListener('keydown', this.onKeyDown)
    document.removeEventListener('mousemove', this.onMouseMove)
    document.removeEventListener('mouseup', this.onMouseUp)
  }
}
</script>

<style lang="scss" scoped>
.dwms-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;

  .editor-toolbar {
    height: 50px;
    background: #fff;
    border-bottom: 1px solid #e8e8e8;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  .editor-main {
    flex: 1;
    display: flex;
    overflow: hidden;

    .component-panel {
      width: 250px;
      background: #fff;
      border-right: 1px solid #e8e8e8;
      display: flex;
      flex-direction: column;

      .panel-header {
        height: 40px;
        padding: 0 16px;
        border-bottom: 1px solid #e8e8e8;
        display: flex;
        align-items: center;

        h4 {
          margin: 0;
          font-size: 14px;
          color: #333;
        }
      }

      .panel-content {
        flex: 1;
        overflow-y: auto;

        .component-list {
          padding: 8px;

          .component-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            margin-bottom: 4px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            cursor: grab;
            transition: all 0.2s;

            &:hover {
              background: #e9ecef;
              border-color: #409EFF;
            }

            &:active {
              cursor: grabbing;
            }

            i {
              margin-right: 8px;
              color: #409EFF;
            }

            span {
              font-size: 12px;
              color: #333;
            }
          }
        }
      }
    }

    .canvas-container {
      flex: 1;
      background: #f5f5f5;
      overflow: auto;
      position: relative;

      .canvas-wrapper {
        position: relative;
        margin: 20px;

        .canvas {
          position: relative;
          background: #fff;
          border: 1px solid #ddd;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
          overflow: hidden;

          .canvas-grid {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
              linear-gradient(to right, #f0f0f0 1px, transparent 1px),
              linear-gradient(to bottom, #f0f0f0 1px, transparent 1px);
            background-size: 20px 20px;
            pointer-events: none;
          }
        }
      }

      .selection-box {
        position: absolute;
        border: 2px solid #409EFF;
        pointer-events: none;
        z-index: 1000;

        .resize-handle {
          position: absolute;
          width: 8px;
          height: 8px;
          background: #409EFF;
          border: 1px solid #fff;
          pointer-events: all;

          &.resize-handle-nw {
            top: -4px;
            left: -4px;
            cursor: nw-resize;
          }

          &.resize-handle-ne {
            top: -4px;
            right: -4px;
            cursor: ne-resize;
          }

          &.resize-handle-sw {
            bottom: -4px;
            left: -4px;
            cursor: sw-resize;
          }

          &.resize-handle-se {
            bottom: -4px;
            right: -4px;
            cursor: se-resize;
          }
        }
      }
    }

    .property-panel {
      width: 300px;
      background: #fff;
      border-left: 1px solid #e8e8e8;
      display: flex;
      flex-direction: column;

      .panel-header {
        height: 40px;
        padding: 0 16px;
        border-bottom: 1px solid #e8e8e8;
        display: flex;
        align-items: center;

        h4 {
          margin: 0;
          font-size: 14px;
          color: #333;
        }
      }

      .panel-content {
        flex: 1;
        overflow-y: auto;
        padding: 16px;

        .no-selection {
          text-align: center;
          color: #999;
          margin-top: 50px;
        }
      }
    }
  }

  .preview-container {
    height: 70vh;
    border: 1px solid #e8e8e8;
    background: #f5f5f5;
  }
}

// 全局样式
::v-deep .el-collapse-item__header {
  font-size: 13px;
  padding-left: 16px;
}

::v-deep .el-collapse-item__content {
  padding-bottom: 0;
}
</style>
