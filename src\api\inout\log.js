import request from '@/utils/request'

// 查询采购单同步日志列表
export function listLog(query) {
  return request({
    url: '/inout/log/list',
    method: 'get',
    params: query
  })
}

// 查询采购单同步日志详细
export function getLog(id) {
  return request({
    url: '/inout/log/' + id,
    method: 'get'
  })
}

// 新增采购单同步日志
export function addLog(data) {
  return request({
    url: '/inout/log',
    method: 'post',
    data: data
  })
}

// 修改采购单同步日志
export function updateLog(data) {
  return request({
    url: '/inout/log',
    method: 'put',
    data: data
  })
}

// 删除采购单同步日志
export function delLog(id) {
  return request({
    url: '/inout/log/' + id,
    method: 'delete'
  })
}

// 获取日志清理配置
export function getCleanConfig() {
  return request({
    url: '/inout/log/cleanConfig',
    method: 'get'
  })
}

// 更新日志清理配置
export function updateCleanConfig(data) {
  return request({
    url: '/inout/log/cleanConfig',
    method: 'put',
    data: data
  })
}

// 预览清理数据
export function previewClean(data) {
  return request({
    url: '/inout/log/previewClean',
    method: 'post',
    data: data
  })
}

// 执行清理操作
export function executeClean(data) {
  return request({
    url: '/inout/log/executeClean',
    method: 'post',
    data: data
  })
}

// 获取清理历史记录
export function getCleanHistory(query) {
  return request({
    url: '/inout/log/cleanHistory',
    method: 'get',
    params: query
  })
}
