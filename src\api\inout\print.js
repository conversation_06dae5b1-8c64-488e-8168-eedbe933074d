import request from '@/utils/request'

// 获取出入库单据打印数据
export function getInoutPrintData(inoutId) {
  return request({
    url: '/inout/print/data/' + inoutId,
    method: 'get'
  })
}

// 生成出入库单据打印内容
export function generateInoutPrintContent(inoutId, templateId) {
  return request({
    url: '/inout/print/content/' + inoutId,
    method: 'get',
    params: {
      templateId: templateId
    }
  })
}

// 获取打印预览数据
export function getInoutPrintPreview(inoutId, templateId) {
  return request({
    url: '/inout/print/preview/' + inoutId,
    method: 'get',
    params: {
      templateId: templateId
    }
  })
}

// 记录打印历史
export function recordPrintHistory(data) {
  return request({
    url: '/inout/print/record',
    method: 'post',
    data: data
  })
}

// 打印出入库单据
export function printInoutDocument(inoutId, templateId) {
  return request({
    url: '/inout/print/inout/' + inoutId,
    method: 'post',
    data: {
      templateId: templateId
    }
  })
}

// 获取打印历史
export function getPrintHistory(params) {
  return request({
    url: '/inout/print/history',
    method: 'get',
    params
  })
}

// 获取系统可用打印机列表
export function getPrinters() {
  return request({
    url: '/inout/print/printers',
    method: 'get'
  })
}

// 获取指定打印机的详细信息
export function getPrinterInfo(printerName) {
  return request({
    url: '/inout/print/printer/' + printerName,
    method: 'get'
  })
}

// 测试打印机连接
export function testPrinter(printerName) {
  return request({
    url: '/inout/print/printer/test',
    method: 'post',
    data: {
      printerName: printerName
    }
  })
}
