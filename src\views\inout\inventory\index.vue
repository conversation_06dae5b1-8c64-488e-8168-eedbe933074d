<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon total">
              <i class="el-icon-files"></i>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.total }}</div>
              <div class="stat-label">总盘点任务</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon pending">
              <i class="el-icon-time"></i>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.pending }}</div>
              <div class="stat-label">待盘点</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon processing">
              <i class="el-icon-loading"></i>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.processing }}</div>
              <div class="stat-label">盘点中</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon completed">
              <i class="el-icon-check"></i>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.completed }}</div>
              <div class="stat-label">已完成</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 查询条件 -->
    <el-card class="filter-container" shadow="hover">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">
        <el-row>
          <el-col :span="6">
            <el-form-item label="盘点单号" prop="inventoryNo">
              <el-input
                v-model="queryParams.inventoryNo"
                placeholder="请输入盘点单号"
                clearable
                prefix-icon="el-icon-document"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="盘点类型" prop="inventoryType">
              <el-select v-model="queryParams.inventoryType" placeholder="请选择盘点类型" clearable style="width: 100%">
                <el-option label="全面盘点" value="0" />
                <el-option label="抽样盘点" value="1" />
                <el-option label="动态盘点" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="盘点状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择盘点状态" clearable style="width: 100%">
                <el-option label="未开始" value="0" />
                <el-option label="进行中" value="1" />
                <el-option label="已完成" value="2" />
                <el-option label="已取消" value="3" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="审核状态" prop="auditStatus">
              <el-select v-model="queryParams.auditStatus" placeholder="请选择审核状态" clearable style="width: 100%">
                <el-option label="待审核" value="0" />
                <el-option label="已审核" value="1" />
                <el-option label="已拒绝" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="仓库" prop="warehouseId">
              <el-select v-model="queryParams.warehouseId" placeholder="请选择仓库" clearable filterable style="width: 100%">
                <el-option
                  v-for="warehouse in warehouseOptions"
                  :key="warehouse.id"
                  :label="warehouse.name"
                  :value="warehouse.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="库区" prop="zoneId">
              <el-select v-model="queryParams.zoneId" placeholder="请选择库区" clearable filterable style="width: 100%">
                <el-option
                  v-for="zone in zoneOptions"
                  :key="zone.id"
                  :label="zone.name"
                  :value="zone.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="货架" prop="rackId">
              <el-select v-model="queryParams.rackId" placeholder="请选择货架" clearable filterable style="width: 100%">
                <el-option
                  v-for="rack in rackOptions"
                  :key="rack.id"
                  :label="rack.name"
                  :value="rack.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="盘点时间" prop="dateRange">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                style="width: 100%"
                @change="handleDateRangeChange">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" style="text-align: right;">
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              <el-button type="success" icon="el-icon-refresh" size="mini" @click="refreshStatistics">刷新统计</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 操作工具栏和表格 -->
    <el-card class="table-container" shadow="hover" style="margin-top: 10px;">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['inout:inventory:add']"
          >新增盘点</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            icon="el-icon-magic-stick"
            size="mini"
            @click="handleGenerateTask"
            v-hasPermi="['inout:inventory:add']"
          >生成盘点任务</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="info"
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['inout:inventory:edit']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            icon="el-icon-view"
            size="mini"
            :disabled="single"
            @click="handleViewDetails"
            v-hasPermi="['inout:inventory:query']"
          >查看明细</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['inout:inventory:remove']"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['inout:inventory:export']"
          >导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="inventoryList" @selection-change="handleSelectionChange" border stripe highlight-current-row>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" type="index" width="60" align="center" />
        <el-table-column label="盘点单号" align="center" prop="inventoryNo" min-width="140" show-overflow-tooltip>
          <template slot-scope="scope">
            <span style="color: #409EFF; cursor: pointer; font-weight: bold;" @click="handleView(scope.row)">
              {{ scope.row.inventoryNo }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="盘点类型" align="center" prop="inventoryType" width="100">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.inventoryType == '0'" type="primary" size="mini">全面盘点</el-tag>
            <el-tag v-else-if="scope.row.inventoryType == '1'" type="success" size="mini">抽样盘点</el-tag>
            <el-tag v-else-if="scope.row.inventoryType == '2'" type="warning" size="mini">动态盘点</el-tag>
            <el-tag v-else type="info" size="mini">{{scope.row.inventoryType}}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="仓库位置" align="center" min-width="180" show-overflow-tooltip>
          <template slot-scope="scope">
            <div class="location-info">
              <div class="location-item">
                <i class="el-icon-office-building"></i>
                <span>{{ getWarehouseName(scope.row.warehouseId) || '未指定' }}</span>
              </div>
              <div class="location-item" v-if="scope.row.zoneId">
                <i class="el-icon-map-location"></i>
                <span>{{ getZoneName(scope.row.zoneId) || '区域' + scope.row.zoneId }}</span>
              </div>
              <div class="location-item" v-if="scope.row.rackId">
                <i class="el-icon-box"></i>
                <span>{{ getRackName(scope.row.rackId) || '货架' + scope.row.rackId }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="盘点时间" align="center" min-width="160">
          <template slot-scope="scope">
            <div class="time-info">
              <div class="time-item">
                <i class="el-icon-time" style="color: #67C23A;"></i>
                <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d}') || '未设置' }}</span>
              </div>
              <div class="time-divider">~</div>
              <div class="time-item">
                <i class="el-icon-time" style="color: #E6A23C;"></i>
                <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d}') || '未设置' }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" width="120">
          <template slot-scope="scope">
            <div class="status-info">
              <div class="status-item">
                <span class="status-label">盘点:</span>
                <el-tag v-if="scope.row.status == '0'" type="info" size="mini">未开始</el-tag>
                <el-tag v-else-if="scope.row.status == '1'" type="warning" size="mini">进行中</el-tag>
                <el-tag v-else-if="scope.row.status == '2'" type="success" size="mini">已完成</el-tag>
                <el-tag v-else-if="scope.row.status == '3'" type="danger" size="mini">已取消</el-tag>
                <el-tag v-else size="mini">{{scope.row.status}}</el-tag>
              </div>
              <div class="status-item">
                <span class="status-label">审核:</span>
                <el-tag v-if="scope.row.auditStatus == '0'" type="info" size="mini">待审核</el-tag>
                <el-tag v-else-if="scope.row.auditStatus == '1'" type="success" size="mini">已审核</el-tag>
                <el-tag v-else-if="scope.row.auditStatus == '2'" type="danger" size="mini">已拒绝</el-tag>
                <el-tag v-else size="mini">{{scope.row.auditStatus}}</el-tag>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="盘点进度" align="center" width="120">
          <template slot-scope="scope">
            <div class="progress-info">
              <el-progress
                :percentage="getInventoryProgress(scope.row)"
                :status="getProgressStatus(scope.row)"
                :stroke-width="8"
                :show-text="false">
              </el-progress>
              <div class="progress-text">{{ getInventoryProgress(scope.row) }}%</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作人员" align="center" min-width="140">
          <template slot-scope="scope">
            <div class="operator-info">
              <div class="operator-item">
                <i class="el-icon-user"></i>
                <span>{{ getOperatorName(scope.row.operatorId) || '未指定' }}</span>
              </div>
              <div class="operator-item" v-if="scope.row.auditId">
                <i class="el-icon-s-check"></i>
                <span>{{ getAuditorName(scope.row.auditId) }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
              v-hasPermi="['inout:inventory:query']"
              style="color: #409EFF;"
            >详情</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit-outline"
              @click="handleViewDetails(scope.row)"
              v-hasPermi="['inout:inventory:query']"
              style="color: #67C23A;"
            >明细</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['inout:inventory:edit']"
              style="color: #E6A23C;"
              v-if="scope.row.status != '2'"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-check"
              @click="handleStartInventory(scope.row)"
              v-hasPermi="['inout:inventory:edit']"
              style="color: #67C23A;"
              v-if="scope.row.status == '0'"
            >开始</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-finished"
              @click="handleCompleteInventory(scope.row)"
              v-hasPermi="['inout:inventory:edit']"
              style="color: #409EFF;"
              v-if="scope.row.status == '1'"
            >完成</el-button>
            <el-dropdown v-if="scope.row.status != '3'" @command="(command) => handleMoreAction(command, scope.row)">
              <el-button size="mini" type="text" style="color: #909399;">
                更多<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="copy" icon="el-icon-copy-document">复制</el-dropdown-item>
                <el-dropdown-item command="export" icon="el-icon-download">导出</el-dropdown-item>
                <el-dropdown-item command="cancel" icon="el-icon-close" v-if="scope.row.status != '2'">取消</el-dropdown-item>
                <el-dropdown-item command="delete" icon="el-icon-delete" style="color: #F56C6C;">删除</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改库房盘点对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-tabs v-model="activeTab" type="border-card">
          <!-- 基本信息 -->
          <el-tab-pane label="基本信息" name="basic">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="盘点单号" prop="inventoryNo">
                  <el-input v-model="form.inventoryNo" placeholder="系统自动生成" :disabled="form.id != null" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="盘点类型" prop="inventoryType">
                  <el-select v-model="form.inventoryType" placeholder="请选择盘点类型" style="width: 100%">
                    <el-option label="全面盘点" value="0">
                      <span style="float: left">全面盘点</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">盘点所有物料</span>
                    </el-option>
                    <el-option label="抽样盘点" value="1">
                      <span style="float: left">抽样盘点</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">随机抽样盘点</span>
                    </el-option>
                    <el-option label="动态盘点" value="2">
                      <span style="float: left">动态盘点</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">实时库存盘点</span>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="仓库" prop="warehouseId">
                  <el-select v-model="form.warehouseId" placeholder="请选择仓库" filterable style="width: 100%" @change="handleWarehouseChange">
                    <el-option
                      v-for="warehouse in warehouseOptions"
                      :key="warehouse.id"
                      :label="warehouse.name"
                      :value="warehouse.id">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="库区" prop="zoneId">
                  <el-select v-model="form.zoneId" placeholder="请选择库区" filterable style="width: 100%" @change="handleZoneChange">
                    <el-option
                      v-for="zone in zoneOptions"
                      :key="zone.id"
                      :label="zone.name"
                      :value="zone.id">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="货架" prop="rackId">
                  <el-select v-model="form.rackId" placeholder="请选择货架" filterable style="width: 100%">
                    <el-option
                      v-for="rack in rackOptions"
                      :key="rack.id"
                      :label="rack.name"
                      :value="rack.id">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="盘点开始时间" prop="startTime">
                  <el-date-picker
                    v-model="form.startTime"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    style="width: 100%"
                    placeholder="请选择盘点开始时间">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="盘点结束时间" prop="endTime">
                  <el-date-picker
                    v-model="form.endTime"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    style="width: 100%"
                    placeholder="请选择盘点结束时间">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="盘点说明" prop="remark">
                  <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入盘点说明或备注信息" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>

          <!-- 执行信息 -->
          <el-tab-pane label="执行信息" name="execution">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="盘点状态" prop="status">
                  <el-select v-model="form.status" placeholder="请选择盘点状态" style="width: 100%">
                    <el-option label="未开始" value="0" />
                    <el-option label="进行中" value="1" />
                    <el-option label="已完成" value="2" />
                    <el-option label="已取消" value="3" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="审核状态" prop="auditStatus">
                  <el-select v-model="form.auditStatus" placeholder="请选择审核状态" style="width: 100%">
                    <el-option label="待审核" value="0" />
                    <el-option label="已审核" value="1" />
                    <el-option label="已拒绝" value="2" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="操作人员" prop="operatorId">
                  <el-select v-model="form.operatorId" placeholder="请选择操作人员" filterable style="width: 100%">
                    <el-option
                      v-for="user in userOptions"
                      :key="user.userId"
                      :label="user.nickName"
                      :value="user.userId">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="审核人员" prop="auditId">
                  <el-select v-model="form.auditId" placeholder="请选择审核人员" filterable style="width: 100%">
                    <el-option
                      v-for="user in userOptions"
                      :key="user.userId"
                      :label="user.nickName"
                      :value="user.userId">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" icon="el-icon-check" @click="submitForm">确 定</el-button>
        <el-button icon="el-icon-close" @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listInventory, getInventory, delInventory, addInventory, updateInventory } from "@/api/inout/inventory"

export default {
  name: "InventoryManagement",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 库房盘点表格数据
      inventoryList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 活动标签页
      activeTab: 'basic',
      // 日期范围
      dateRange: [],
      // 统计数据
      statistics: {
        total: 0,
        pending: 0,
        processing: 0,
        completed: 0,
        cancelled: 0
      },
      // 下拉选项
      warehouseOptions: [],
      zoneOptions: [],
      rackOptions: [],
      userOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        inventoryNo: null,
        warehouseId: null,
        zoneId: null,
        rackId: null,
        inventoryType: null,
        startTime: null,
        endTime: null,
        status: null,
        operatorId: null,
        auditId: null,
        auditTime: null,
        auditStatus: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        inventoryNo: [
          { required: true, message: "盘点单号不能为空", trigger: "blur" }
        ],
        warehouseId: [
          { required: true, message: "仓库不能为空", trigger: "change" }
        ],
        inventoryType: [
          { required: true, message: "盘点类型不能为空", trigger: "change" }
        ],
        startTime: [
          { required: true, message: "盘点开始时间不能为空", trigger: "change" }
        ],
        endTime: [
          { required: true, message: "盘点结束时间不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.loadOptions();
    this.loadStatistics();
  },
  methods: {
    /** 查询库房盘点列表 */
    getList() {
      this.loading = true;
      listInventory(this.queryParams).then(response => {
        this.inventoryList = response.rows;
        this.total = response.total;
        this.loading = false;
        this.updateStatistics();
      });
    },
    /** 加载下拉选项 */
    loadOptions() {
      // 加载仓库选项
      this.warehouseOptions = [
        { id: 1, name: '主仓库' },
        { id: 2, name: '备件仓库' },
        { id: 3, name: '工具仓库' }
      ];
      // 加载用户选项
      this.userOptions = [
        { userId: 1, nickName: '管理员' },
        { userId: 2, nickName: '仓库员' }
      ];
    },
    /** 加载统计数据 */
    loadStatistics() {
      // 这里可以调用后端接口获取统计数据
      this.statistics = {
        total: 0,
        pending: 0,
        processing: 0,
        completed: 0,
        cancelled: 0
      };
    },
    /** 更新统计数据 */
    updateStatistics() {
      const stats = {
        total: this.inventoryList.length,
        pending: 0,
        processing: 0,
        completed: 0,
        cancelled: 0
      };

      this.inventoryList.forEach(item => {
        switch(item.status) {
          case '0': stats.pending++; break;
          case '1': stats.processing++; break;
          case '2': stats.completed++; break;
          case '3': stats.cancelled++; break;
        }
      });

      this.statistics = stats;
    },
    /** 刷新统计 */
    refreshStatistics() {
      this.loadStatistics();
      this.getList();
    },
    /** 日期范围变化 */
    handleDateRangeChange(dates) {
      if (dates && dates.length === 2) {
        this.queryParams.startTime = dates[0];
        this.queryParams.endTime = dates[1];
      } else {
        this.queryParams.startTime = null;
        this.queryParams.endTime = null;
      }
    },
    /** 仓库变化 */
    handleWarehouseChange(warehouseId) {
      this.form.zoneId = null;
      this.form.rackId = null;
      // 根据仓库ID加载库区选项
      this.zoneOptions = [
        { id: 1, name: 'A区' },
        { id: 2, name: 'B区' }
      ];
    },
    /** 库区变化 */
    handleZoneChange(zoneId) {
      this.form.rackId = null;
      // 根据库区ID加载货架选项
      this.rackOptions = [
        { id: 1, name: 'A01' },
        { id: 2, name: 'A02' }
      ];
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        inventoryNo: null,
        warehouseId: null,
        zoneId: null,
        rackId: null,
        inventoryType: null,
        startTime: null,
        endTime: null,
        status: "0",
        operatorId: null,
        auditId: null,
        auditTime: null,
        auditStatus: "0",
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      };
      this.activeTab = 'basic';
      this.resetForm("form");
    },
    /** 获取仓库名称 */
    getWarehouseName(warehouseId) {
      const warehouse = this.warehouseOptions.find(w => w.id == warehouseId);
      return warehouse ? warehouse.name : null;
    },
    /** 获取库区名称 */
    getZoneName(zoneId) {
      const zone = this.zoneOptions.find(z => z.id == zoneId);
      return zone ? zone.name : null;
    },
    /** 获取货架名称 */
    getRackName(rackId) {
      const rack = this.rackOptions.find(r => r.id == rackId);
      return rack ? rack.name : null;
    },
    /** 获取操作人姓名 */
    getOperatorName(operatorId) {
      const user = this.userOptions.find(u => u.userId == operatorId);
      return user ? user.nickName : null;
    },
    /** 获取审核人姓名 */
    getAuditorName(auditId) {
      const user = this.userOptions.find(u => u.userId == auditId);
      return user ? user.nickName : null;
    },
    /** 获取盘点进度 */
    getInventoryProgress(row) {
      // 这里可以根据实际业务逻辑计算进度
      if (row.status == '0') return 0;
      if (row.status == '1') return 50;
      if (row.status == '2') return 100;
      if (row.status == '3') return 0;
      return 0;
    },
    /** 获取进度状态 */
    getProgressStatus(row) {
      if (row.status == '2') return 'success';
      if (row.status == '3') return 'exception';
      return null;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length!==1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增库房盘点";
      // 设置默认值
      this.form.inventoryNo = this.generateInventoryNo();
      this.form.operatorId = this.$store.state.user.userId;
    },
    /** 生成盘点单号 */
    generateInventoryNo() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
      return `PD${year}${month}${day}${random}`;
    },
    /** 生成盘点任务 */
    handleGenerateTask() {
      this.$prompt('请输入要生成的盘点任务数量', '生成盘点任务', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^\d+$/,
        inputErrorMessage: '请输入有效的数字'
      }).then(({ value }) => {
        this.$modal.msgSuccess(`已生成 ${value} 个盘点任务`);
        this.getList();
      }).catch(() => {});
    },
    /** 查看明细 */
    handleViewDetails(row) {
      if (!row) {
        row = this.inventoryList.find(item => this.ids.includes(item.id));
      }
      if (row) {
        this.$router.push({
          path: '/inout/inventory-detail',
          query: { inventoryId: row.id, inventoryNo: row.inventoryNo }
        });
      }
    },
    /** 开始盘点 */
    handleStartInventory(row) {
      this.$modal.confirm(`确认开始盘点任务"${row.inventoryNo}"？`).then(() => {
        // 调用开始盘点接口
        this.$modal.msgSuccess("盘点任务已开始");
        this.getList();
      }).catch(() => {});
    },
    /** 完成盘点 */
    handleCompleteInventory(row) {
      this.$modal.confirm(`确认完成盘点任务"${row.inventoryNo}"？`).then(() => {
        // 调用完成盘点接口
        this.$modal.msgSuccess("盘点任务已完成");
        this.getList();
      }).catch(() => {});
    },
    /** 更多操作 */
    handleMoreAction(command, row) {
      switch(command) {
        case 'copy':
          this.handleCopy(row);
          break;
        case 'export':
          this.handleExportSingle(row);
          break;
        case 'cancel':
          this.handleCancel(row);
          break;
        case 'delete':
          this.handleDelete(row);
          break;
      }
    },
    /** 复制盘点任务 */
    handleCopy(row) {
      this.reset();
      this.form = { ...row };
      this.form.id = null;
      this.form.inventoryNo = this.generateInventoryNo();
      this.form.status = '0';
      this.form.auditStatus = '0';
      this.open = true;
      this.title = "复制库房盘点";
    },
    /** 取消盘点任务 */
    handleCancel(row) {
      this.$modal.confirm(`确认取消盘点任务"${row.inventoryNo}"？`).then(() => {
        // 调用取消盘点接口
        this.$modal.msgSuccess("盘点任务已取消");
        this.getList();
      }).catch(() => {});
    },
    /** 导出单个盘点任务 */
    handleExportSingle(row) {
      this.download('inout/inventory/export', {
        id: row.id
      }, `inventory_${row.inventoryNo}_${new Date().getTime()}.xlsx`);
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getInventory(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改库房盘点";
      });
    },
    /** 详情按钮操作 */
    handleView(row) {
      this.reset();
      const id = row.id;
      getInventory(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "查看库房盘点详情";
        // 设置表单只读
        this.$nextTick(() => {
          this.$refs.form.disabled = true;
        });
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateInventory(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInventory(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除库房盘点编号为"' + ids + '"的数据项？').then(function() {
        return delInventory(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('inout/inventory/export', {
        ...this.queryParams
      }, `inventory_${new Date().getTime()}.xlsx`);
    }
  }
};
</script>

<style scoped>
.filter-container {
  margin-bottom: 10px;
}

.mb20 {
  margin-bottom: 20px;
}

/* 统计卡片样式 */
.stat-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.pending {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.processing {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

/* 位置信息样式 */
.location-info {
  text-align: left;
}

.location-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
}

.location-item i {
  margin-right: 4px;
  width: 14px;
}

/* 时间信息样式 */
.time-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.time-item {
  display: flex;
  align-items: center;
  font-size: 12px;
  margin-bottom: 2px;
}

.time-item i {
  margin-right: 4px;
}

.time-divider {
  color: #909399;
  font-size: 12px;
  margin: 2px 0;
}

/* 状态信息样式 */
.status-info {
  text-align: left;
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.status-label {
  font-size: 11px;
  color: #909399;
  margin-right: 4px;
  min-width: 30px;
}

/* 进度信息样式 */
.progress-info {
  text-align: center;
}

.progress-text {
  font-size: 12px;
  color: #606266;
  margin-top: 4px;
}

/* 操作人员样式 */
.operator-info {
  text-align: left;
}

.operator-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
}

.operator-item i {
  margin-right: 4px;
  width: 14px;
}
</style>
