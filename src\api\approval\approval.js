import request from '@/utils/request'

// 查询可用的审批流程列表
export function listWorkflows(businessType) {
  return request({
    url: '/approval/workflow/list',
    method: 'get',
    params: { businessType: businessType, status: '1' } // 只查询已启用的流程
  })
}

// 获取审批实例详情
export function getApprovalDetail(params) {
  // 如果传入的是字符串，则认为是instanceId
  if (typeof params === 'string' || typeof params === 'number') {
    return request({
      url: '/approval/instance/' + params,
      method: 'get'
    })
  }

  // 如果传入的是对象，则根据业务ID和业务类型查询
  if (params && params.businessId && params.businessType) {
    return request({
      url: '/approval/process/detail/business',
      method: 'get',
      params: {
        businessId: params.businessId,
        businessType: params.businessType
      }
    })
  }

  // 兼容旧的调用方式
  return request({
    url: '/approval/instance/' + (params.instanceId || params),
    method: 'get'
  })
}

// 提交审批（同意/驳回）
export function approveTask(data) {
    return request({
        url: '/approval/instance/approve',
        method: 'put',
        data: data
    })
} 