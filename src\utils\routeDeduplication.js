/**
 * 路由去重工具
 * 解决系统中大量路由重复定义的问题
 */

// 已注册的路由名称集合
const registeredRouteNames = new Set()
const registeredRoutePaths = new Set()

/**
 * 重置路由注册记录
 */
export function resetRouteRegistry() {
  registeredRouteNames.clear()
  registeredRoutePaths.clear()
  console.log('[路由去重] 重置路由注册记录')
}

/**
 * 检查路由是否重复
 * @param {Object} route 路由对象
 * @returns {boolean} 是否重复
 */
function isDuplicateRoute(route) {
  if (!route) return true
  
  // 检查路由名称重复
  if (route.name && registeredRouteNames.has(route.name)) {
    console.warn(`[路由去重] 发现重复路由名称: ${route.name}, 路径: ${route.path}`)
    return true
  }
  
  // 检查路径重复（排除根路径和通配符）
  if (route.path && route.path !== '/' && route.path !== '*' && registeredRoutePaths.has(route.path)) {
    console.warn(`[路由去重] 发现重复路由路径: ${route.path}, 名称: ${route.name}`)
    return true
  }
  
  return false
}

/**
 * 注册路由
 * @param {Object} route 路由对象
 */
function registerRoute(route) {
  if (route.name) {
    registeredRouteNames.add(route.name)
  }
  if (route.path && route.path !== '/' && route.path !== '*') {
    registeredRoutePaths.add(route.path)
  }
}

/**
 * 递归去重路由及其子路由
 * @param {Array} routes 路由数组
 * @returns {Array} 去重后的路由数组
 */
function deduplicateRoutesRecursive(routes) {
  if (!Array.isArray(routes)) return []
  
  return routes.filter(route => {
    // 检查当前路由是否重复
    if (isDuplicateRoute(route)) {
      return false
    }
    
    // 注册当前路由
    registerRoute(route)
    
    // 递归处理子路由
    if (route.children && Array.isArray(route.children)) {
      route.children = deduplicateRoutesRecursive(route.children)
    }
    
    return true
  })
}

/**
 * 主要的路由去重函数
 * @param {Array} routes 原始路由数组
 * @returns {Array} 去重后的路由数组
 */
export function deduplicateRoutes(routes) {
  console.log(`[路由去重] 开始处理 ${routes.length} 个路由`)
  
  // 重置注册记录
  resetRouteRegistry()
  
  // 执行去重
  const deduplicatedRoutes = deduplicateRoutesRecursive(routes)
  
  console.log(`[路由去重] 去重完成，保留 ${deduplicatedRoutes.length} 个路由`)
  console.log(`[路由去重] 已注册路由名称数量: ${registeredRouteNames.size}`)
  console.log(`[路由去重] 已注册路由路径数量: ${registeredRoutePaths.size}`)
  
  return deduplicatedRoutes
}

/**
 * 简单的路由去重（用于现有代码兼容）
 * @param {Array} routes 路由数组
 * @returns {Array} 去重后的路由数组
 */
export function simpleRouteDeduplication(routes) {
  return deduplicateRoutes(routes)
}

/**
 * 获取路由统计信息
 * @returns {Object} 统计信息
 */
export function getRouteStats() {
  return {
    registeredNames: registeredRouteNames.size,
    registeredPaths: registeredRoutePaths.size,
    namesList: Array.from(registeredRouteNames),
    pathsList: Array.from(registeredRoutePaths)
  }
}
