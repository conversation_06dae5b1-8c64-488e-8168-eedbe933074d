<template>
  <div 
    class="component-renderer"
    :class="{ 'selected': selected }"
    :style="componentStyle"
    @click.stop="handleClick"
    @mousedown="handleMouseDown"
  >
    <!-- 仪表盘组件 -->
    <gauge-component 
      v-if="component.type === 'gauge'"
      :config="component.config"
      :data="componentData"
      :width="component.width"
      :height="component.height"
    />
    
    <!-- 图表组件 -->
    <chart-component 
      v-else-if="component.type === 'chart'"
      :config="component.config"
      :data="componentData"
      :width="component.width"
      :height="component.height"
    />
    
    <!-- 状态组件 -->
    <status-component 
      v-else-if="component.type === 'status'"
      :config="component.config"
      :data="componentData"
      :width="component.width"
      :height="component.height"
    />
    
    <!-- 按钮组件 -->
    <button-component 
      v-else-if="component.type === 'button'"
      :config="component.config"
      :data="componentData"
      :width="component.width"
      :height="component.height"
      @action="handleAction"
    />
    
    <!-- 设备图标组件 -->
    <device-component 
      v-else-if="component.type === 'device'"
      :config="component.config"
      :data="componentData"
      :width="component.width"
      :height="component.height"
    />
    
    <!-- 默认组件 -->
    <div v-else class="default-component">
      <i class="el-icon-cpu"></i>
      <span>{{ component.name }}</span>
    </div>
    
    <!-- 组件工具栏 -->
    <div v-if="selected" class="component-toolbar">
      <el-button-group size="mini">
        <el-button icon="el-icon-edit" @click.stop="editComponent"></el-button>
        <el-button icon="el-icon-copy-document" @click.stop="copyComponent"></el-button>
        <el-button icon="el-icon-delete" @click.stop="deleteComponent"></el-button>
      </el-button-group>
    </div>
  </div>
</template>

<script>
import GaugeComponent from './widgets/GaugeComponent'
import ChartComponent from './widgets/ChartComponent'
import StatusComponent from './widgets/StatusComponent'
import ButtonComponent from './widgets/ButtonComponent'
import DeviceComponent from './widgets/DeviceComponent'

export default {
  name: 'ComponentRenderer',
  components: {
    GaugeComponent,
    ChartComponent,
    StatusComponent,
    ButtonComponent,
    DeviceComponent
  },
  props: {
    component: {
      type: Object,
      required: true
    },
    selected: {
      type: Boolean,
      default: false
    },
    zoom: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      dragging: false,
      dragStartPos: null,
      componentStartPos: null,
      componentData: null
    }
  },
  computed: {
    componentStyle() {
      return {
        position: 'absolute',
        left: this.component.x + 'px',
        top: this.component.y + 'px',
        width: this.component.width + 'px',
        height: this.component.height + 'px',
        zIndex: this.selected ? 100 : 1
      }
    }
  },
  mounted() {
    this.loadComponentData()
    
    // 添加鼠标事件监听
    document.addEventListener('mousemove', this.handleMouseMove)
    document.addEventListener('mouseup', this.handleMouseUp)
  },
  beforeDestroy() {
    document.removeEventListener('mousemove', this.handleMouseMove)
    document.removeEventListener('mouseup', this.handleMouseUp)
  },
  methods: {
    // 处理点击事件
    handleClick() {
      this.$emit('select', this.component)
    },
    
    // 处理鼠标按下事件
    handleMouseDown(event) {
      if (event.button !== 0) return // 只处理左键
      
      this.dragging = true
      this.dragStartPos = {
        x: event.clientX,
        y: event.clientY
      }
      this.componentStartPos = {
        x: this.component.x,
        y: this.component.y
      }
      
      event.preventDefault()
      this.$emit('select', this.component)
    },
    
    // 处理鼠标移动事件
    handleMouseMove(event) {
      if (!this.dragging) return
      
      const deltaX = (event.clientX - this.dragStartPos.x) / this.zoom
      const deltaY = (event.clientY - this.dragStartPos.y) / this.zoom
      
      const newComponent = {
        ...this.component,
        x: Math.max(0, this.componentStartPos.x + deltaX),
        y: Math.max(0, this.componentStartPos.y + deltaY)
      }
      
      this.$emit('update', newComponent)
    },
    
    // 处理鼠标释放事件
    handleMouseUp() {
      if (this.dragging) {
        this.dragging = false
        this.dragStartPos = null
        this.componentStartPos = null
      }
    },
    
    // 编辑组件
    editComponent() {
      // 触发属性编辑
      this.$emit('select', this.component)
    },
    
    // 复制组件
    copyComponent() {
      const newComponent = {
        ...this.component,
        id: 'comp_' + Date.now(),
        x: this.component.x + 20,
        y: this.component.y + 20
      }
      this.$emit('copy', newComponent)
    },
    
    // 删除组件
    deleteComponent() {
      this.$confirm('确定要删除这个组件吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('delete', this.component)
      }).catch(() => {
        // 取消删除
      })
    },
    
    // 处理组件动作
    handleAction(action) {
      console.log('组件动作:', action)
      // TODO: 实现组件动作处理
    },
    
    // 加载组件数据
    async loadComponentData() {
      if (!this.component.dataSource) return
      
      try {
        // TODO: 根据数据源加载实际数据
        // 这里先使用模拟数据
        this.componentData = this.getMockData()
      } catch (error) {
        console.error('加载组件数据失败:', error)
      }
    },
    
    // 获取模拟数据
    getMockData() {
      switch (this.component.type) {
        case 'gauge':
          return {
            value: Math.random() * 100,
            max: 100,
            min: 0
          }
        case 'chart':
          return {
            xAxis: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
            series: [
              {
                name: '重量',
                data: [120, 132, 101, 134, 90, 230]
              }
            ]
          }
        case 'status':
          return {
            value: Math.random() > 0.5 ? 1 : 0,
            text: Math.random() > 0.5 ? '在线' : '离线'
          }
        case 'device':
          return {
            status: Math.random() > 0.5 ? 'online' : 'offline',
            name: '设备001'
          }
        default:
          return null
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.component-renderer {
  border: 1px solid transparent;
  cursor: move;
  user-select: none;
  
  &.selected {
    border-color: #409EFF;
  }
  
  &:hover {
    border-color: #409EFF;
    
    .component-toolbar {
      opacity: 1;
    }
  }
  
  .default-component {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
    border: 1px dashed #ccc;
    
    i {
      font-size: 24px;
      color: #999;
      margin-bottom: 8px;
    }
    
    span {
      font-size: 12px;
      color: #666;
    }
  }
  
  .component-toolbar {
    position: absolute;
    top: -35px;
    right: 0;
    opacity: 0;
    transition: opacity 0.2s;
    z-index: 1000;
  }
}
</style>
