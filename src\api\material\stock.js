import request from '@/utils/request'

// 查询物料库存列表
export function listStock(query) {
  return request({
    url: '/material/stock/list',
    method: 'get',
    params: query
  })
}

// 查询物料库存详细
export function getStock(id) {
  return request({
    url: '/material/stock/' + id,
    method: 'get'
  })
}

// 新增物料库存
export function addStock(data) {
  return request({
    url: '/material/stock',
    method: 'post',
    data: data
  })
}

// 修改物料库存
export function updateStock(data) {
  return request({
    url: '/material/stock',
    method: 'put',
    data: data
  })
}

// 删除物料库存
export function delStock(id) {
  return request({
    url: '/material/stock/' + id,
    method: 'delete'
  })
}
