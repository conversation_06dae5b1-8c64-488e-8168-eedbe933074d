<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon">
              <i class="el-icon-goods" style="color: #409EFF;"></i>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ statistics.totalCount || 0 }}</div>
              <div class="statistics-label">总明细数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon">
              <i class="el-icon-time" style="color: #E6A23C;"></i>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ statistics.statusStats ? statistics.statusStats.processing : 0 }}</div>
              <div class="statistics-label">处理中</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon">
              <i class="el-icon-check" style="color: #67C23A;"></i>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ statistics.statusStats ? statistics.statusStats.completed : 0 }}</div>
              <div class="statistics-label">已完成</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon">
              <i class="el-icon-warning" style="color: #F56C6C;"></i>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ statistics.statusStats ? statistics.statusStats.cancelled : 0 }}</div>
              <div class="statistics-label">已取消</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索条件 -->
    <el-card class="filter-container" shadow="hover">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">

        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 150px">
            <el-option label="未处理" value="0" />
            <el-option label="处理中" value="1" />
            <el-option label="已完成" value="2" />
            <el-option label="已取消" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <el-card class="operation-container" shadow="hover">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['inout:detail:add']"
          >新增明细</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['inout:detail:edit']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['inout:detail:remove']"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="info"
            plain
            icon="el-icon-copy-document"
            size="mini"
            :disabled="single"
            @click="handleCopy"
            v-hasPermi="['inout:detail:add']"
          >复制</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['inout:detail:export']"
          >导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-container" shadow="hover">
      <el-table v-loading="loading" :data="detailList" @selection-change="handleSelectionChange" border stripe>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" type="index" width="60" align="center" />

        <!-- 单据编号 -->
        <el-table-column label="单据编号" align="center" width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            <span style="color: #409EFF; cursor: pointer;" @click="handleViewBillDetail(scope.row)">
              {{ getBillNoFromDetail(scope.row) }}
            </span>
          </template>
        </el-table-column>

        <!-- 执行方式 -->
        <el-table-column label="执行方式" align="center" prop="executionMode" width="120">
          <template slot-scope="scope">
            <el-tag :type="getExecutionModeTagType(scope.row.executionMode)" size="mini">
              {{ getExecutionModeText(scope.row.executionMode) }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 单据类型 -->
        <el-table-column label="单据类型" align="center" prop="billType" width="120">
          <template slot-scope="scope">
            <el-tag :type="getBillTypeTagType(scope.row.billType)" size="mini">
              {{ getBillTypeText(scope.row.billType) }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 物料清单编号 -->
        <el-table-column label="物料清单编号" align="center" width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            <span
              style="color: #409EFF; cursor: pointer;"
              @click="handleViewMaterialBillDetail(scope.row)"
              v-if="getMaterialBillCodeFromDetail(scope.row) !== '-'"
            >
              {{ getMaterialBillCodeFromDetail(scope.row) }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <!-- 执行时间 -->
        <el-table-column label="执行时间" align="center" width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ getExecutionTimeFromDetail(scope.row) }}</span>
          </template>
        </el-table-column>

        <!-- 领料员 -->
        <el-table-column label="领料员" align="center" prop="executorName" width="100" show-overflow-tooltip>
          <template slot-scope="scope">
            <span
              style="color: #409EFF; cursor: pointer;"
              @click="handleViewExecutorFace(scope.row)"
              v-if="scope.row.executorName"
            >
              {{ scope.row.executorName }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <!-- 录单人员 -->
        <el-table-column label="录单人员" align="center" prop="recorderName" width="100" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.recorderName || '-' }}</span>
          </template>
        </el-table-column>

        <!-- 实际重量 -->
        <el-table-column label="实际重量" align="center" prop="actualWeight" width="100">
          <template slot-scope="scope">
            <span>{{ scope.row.actualWeight ? scope.row.actualWeight + 'kg' : '-' }}</span>
          </template>
        </el-table-column>

        <!-- 执行状态 -->
        <el-table-column label="执行状态" align="center" prop="executionStatus" width="100">
          <template slot-scope="scope">
            <el-tag :type="getExecutionStatusTagType(scope.row.executionStatus)" size="mini">
              {{ getExecutionStatusText(scope.row.executionStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <!-- 操作列 -->
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="160">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['inout:detail:edit']"
              style="color: #409EFF;"
            >修改</el-button>

            <el-button
              size="mini"
              type="text"
              icon="el-icon-copy-document"
              @click="handleCopy(scope.row)"
              v-hasPermi="['inout:detail:add']"
              style="color: #67C23A;"
            >复制</el-button>

            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['inout:detail:remove']"
              style="color: #F56C6C;"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :pageSizes="[10, 20, 30, 50, 100]"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改出入库明细对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-tabs v-model="activeFormTab">
          <!-- 基本信息 -->
          <el-tab-pane label="基本信息" name="basic">
            <!-- 单据编号查询区域 -->
            <el-card class="bill-query-card" shadow="hover" style="margin-bottom: 20px;">
              <div slot="header" class="clearfix">
                <span style="font-weight: bold; color: #409EFF;">
                  <i class="el-icon-search"></i> 单据编号查询
                </span>
              </div>
              <el-row :gutter="20">
                <el-col :span="16">
                  <el-form-item label="单据编号" prop="queryBillNo">
                    <el-input
                      v-model="queryBillNo"
                      placeholder="请输入出入库申请单据编号"
                      clearable
                      prefix-icon="el-icon-document"
                      @keyup.enter.native="handleQueryBillNo"
                    >
                      <el-button slot="append" type="primary" icon="el-icon-search" @click="handleQueryBillNo" :loading="queryLoading">查询</el-button>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-button type="success" icon="el-icon-refresh" @click="handleClearQuery">清空</el-button>
                </el-col>
              </el-row>

              <!-- 查询结果显示 -->
              <div v-if="queryResult" class="query-result">
                <el-alert
                  :title="queryResult && queryResult.success ? '查询成功' : '查询失败'"
                  :type="queryResult && queryResult.success ? 'success' : 'error'"
                  :description="queryResult ? queryResult.message : ''"
                  show-icon
                  :closable="false"
                  style="margin-bottom: 10px;">
                </el-alert>

                <!-- 单据基本信息 -->
                <div v-if="queryResult && queryResult.success && queryResult.data" class="bill-info">
                  <el-descriptions :column="2" border size="small" title="单据基本信息">
                    <el-descriptions-item label="单据编号">{{ getBillDataValue('billNo') }}</el-descriptions-item>
                    <el-descriptions-item label="单据类型">{{ getBillDataValue('billType', getBillTypeText) }}</el-descriptions-item>
                    <el-descriptions-item label="申请人">{{ getBillDataValue('applicantName') }}</el-descriptions-item>
                    <el-descriptions-item label="申请部门">{{ getBillDataValue('applicantDeptName') }}</el-descriptions-item>
                    <el-descriptions-item label="审批状态">
                      <el-tag :type="getBillDataValue('approvalStatus', getApprovalStatusTagType)" size="mini">
                        {{ getBillDataValue('approvalStatus', getApprovalStatusText) }}
                      </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="申请时间">{{ getBillDataValue('applyTime') }}</el-descriptions-item>
                  </el-descriptions>

                  <!-- 操作按钮 -->
                  <div style="margin-top: 15px; text-align: center;">
                    <el-button
                      type="primary"
                      icon="el-icon-view"
                      @click="showBillDetailDialog"
                    >
                      查看完整单据详情
                    </el-button>
                    <el-button
                      type="success"
                      icon="el-icon-edit"
                      @click="handleManualCreateDetail"
                    >
                      手动创建明细
                    </el-button>
                  </div>

                  <!-- 物料清单信息 -->
                  <div v-if="materialBillItems && materialBillItems.length > 0" style="margin-top: 20px;">
                    <el-divider content-position="left">
                      <span style="font-weight: bold; color: #409EFF;">
                        <i class="el-icon-goods"></i> 物料清单 ({{ materialBillItems.length }}项)
                      </span>
                    </el-divider>

                    <el-table
                      ref="materialTable"
                      :data="materialBillItems"
                      border
                      size="small"
                      style="width: 100%"
                      @selection-change="handleMaterialSelectionChange"
                    >
                      <el-table-column type="selection" width="55" align="center" />
                      <el-table-column label="序号" type="index" width="60" align="center" />
                      <el-table-column label="物料编码" prop="materialCode" width="120" show-overflow-tooltip />
                      <el-table-column label="物料名称" prop="materialName" width="150" show-overflow-tooltip />
                      <el-table-column label="规格型号" width="120" show-overflow-tooltip>
                        <template slot-scope="scope">
                          <span>{{ getSpecification(scope.row) }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="申请数量" prop="quantity" width="100" align="center">
                        <template slot-scope="scope">
                          <span>{{ scope.row.quantity || 0 }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="单位" prop="unit" width="80" align="center" />
                      <el-table-column label="单位重量" prop="unitWeight" width="100" align="center">
                        <template slot-scope="scope">
                          <span>{{ scope.row.unitWeight ? scope.row.unitWeight + 'kg' : '-' }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="总重量" prop="totalWeight" width="100" align="center">
                        <template slot-scope="scope">
                          <span>{{ scope.row.totalWeight ? scope.row.totalWeight + 'kg' : '-' }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="存储位置" width="120" show-overflow-tooltip>
                        <template slot-scope="scope">
                          <span>{{ getStorageLocation(scope.row) }}</span>
                        </template>
                      </el-table-column>
                    </el-table>

                    <div style="margin-top: 15px; text-align: right;">
                      <el-button type="primary" icon="el-icon-plus" @click="handleCreateDetailFromMaterials" :disabled="selectedMaterials.length === 0">
                        创建出入库明细 ({{ selectedMaterials.length }})
                      </el-button>
                      <el-button type="success" icon="el-icon-check" @click="handleSelectAllMaterials">全选</el-button>
                      <el-button type="info" icon="el-icon-close" @click="handleClearMaterialSelection">清空选择</el-button>
                    </div>
                  </div>

                  <!-- 如果没有物料清单，显示手动创建选项 -->
                  <div v-else style="margin-top: 15px; text-align: center; padding: 20px; background: #f5f7fa; border-radius: 6px;">
                    <i class="el-icon-info" style="font-size: 24px; color: #909399; margin-bottom: 10px;"></i>
                    <p style="color: #606266; margin-bottom: 15px;">该单据暂无关联的物料清单，您可以手动创建出入库明细</p>
                    <el-button type="primary" icon="el-icon-plus" @click="handleManualCreateDetail">手动创建明细</el-button>
                  </div>
                </div>
              </div>
            </el-card>

            <!-- 明细信息表单 -->
            <el-card class="form-card" shadow="hover">
              <div slot="header" class="clearfix">
                <span style="font-weight: bold; color: #409EFF;">
                  <i class="el-icon-edit"></i> 明细信息
                </span>
              </div>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="关联单据" prop="relatedBillNo">
                    <el-input v-model="form.relatedBillNo" placeholder="通过单据查询自动填充" :disabled="true">
                      <template slot="prepend">
                        <i class="el-icon-document"></i>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="物料清单编号" prop="materialBillCode">
                    <el-input v-model="form.materialBillCode" placeholder="通过单据查询自动填充" :disabled="true">
                      <template slot="prepend">
                        <i class="el-icon-goods"></i>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="执行方式" prop="executionMode">
                    <el-select v-model="form.executionMode" placeholder="自动判断执行方式" style="width: 100%" :disabled="true">
                      <el-option label="Web录入" value="web">
                        <i class="el-icon-monitor"></i> Web录入
                      </el-option>
                      <el-option label="自助机录入" value="self_service">
                        <i class="el-icon-cpu"></i> 自助机录入
                      </el-option>
                      <el-option label="小程序录入" value="miniprogram">
                        <i class="el-icon-mobile-phone"></i> 小程序录入
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="执行时间" prop="executionTime">
                    <el-date-picker
                      v-model="form.executionTime"
                      type="datetime"
                      placeholder="自动填充当前时间"
                      style="width: 100%"
                      :disabled="true"
                      format="yyyy-MM-dd HH:mm:ss"
                      value-format="yyyy-MM-dd HH:mm:ss"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="领料员" prop="executorName">
                    <el-input v-model="form.executorName" placeholder="自动填充领料员信息" :disabled="true">
                      <template slot="append">
                        <el-button icon="el-icon-user" @click="handleViewExecutorFace(form)" :disabled="!form.executorName">查看人脸</el-button>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="录单人员" prop="recorderName">
                    <el-input v-model="form.recorderName" placeholder="自动填充录单人信息" :disabled="true">
                      <template slot="prepend">
                        <i class="el-icon-user-solid"></i>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="单据类型" prop="billType">
                    <el-select v-model="form.billType" placeholder="根据查询单据自动填写" style="width: 100%" :disabled="true">
                      <el-option label="出库" value="0">
                        <i class="el-icon-upload2"></i> 出库
                      </el-option>
                      <el-option label="入库" value="1">
                        <i class="el-icon-download"></i> 入库
                      </el-option>
                      <el-option label="复合" value="2">
                        <i class="el-icon-sort"></i> 复合
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="执行状态" prop="executionStatus">
                    <el-select v-model="form.executionStatus" placeholder="请选择执行状态" style="width: 100%">
                      <el-option label="未执行" value="not_started">
                        <i class="el-icon-time"></i> 未执行
                      </el-option>
                      <el-option label="执行中" value="in_progress">
                        <i class="el-icon-loading"></i> 执行中
                      </el-option>
                      <el-option label="已完成" value="completed">
                        <i class="el-icon-check"></i> 已完成
                      </el-option>
                      <el-option label="已取消" value="cancelled">
                        <i class="el-icon-close"></i> 已取消
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="实际重量" prop="actualWeight">
                    <el-input-number
                      v-model="form.actualWeight"
                      placeholder="请输入实际重量"
                      style="width: 100%"
                      :precision="3"
                      :min="0"
                      controls-position="right"
                    >
                      <template slot="append">kg</template>
                    </el-input-number>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="执行备注" prop="executionRemark">
                    <el-input
                      v-model="form.executionRemark"
                      type="textarea"
                      placeholder="请输入执行备注"
                      :rows="3"
                      maxlength="500"
                      show-word-limit
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-card>

          </el-tab-pane>


        </el-tabs>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="submitLoading">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 物料选择对话框 -->
    <el-dialog title="选择物料" :visible.sync="materialSelectOpen" width="1000px" append-to-body>
      <el-form :model="materialQueryParams" ref="materialQueryForm" size="small" :inline="true" label-width="68px">
        <el-form-item label="物料编码" prop="materialCode">
          <el-input
            v-model="materialQueryParams.materialCode"
            placeholder="请输入物料编码"
            clearable
            @keyup.enter.native="getMaterialList"
          />
        </el-form-item>
        <el-form-item label="物料名称" prop="materialName">
          <el-input
            v-model="materialQueryParams.materialName"
            placeholder="请输入物料名称"
            clearable
            @keyup.enter.native="getMaterialList"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="getMaterialList">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetMaterialQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table v-loading="materialLoading" :data="materialList" @current-change="handleMaterialSelect" border highlight-current-row>
        <el-table-column label="物料编码" align="center" prop="materialCode" />
        <el-table-column label="物料名称" align="center" prop="materialName" show-overflow-tooltip />
        <el-table-column label="规格型号" align="center" prop="specification" show-overflow-tooltip />
        <el-table-column label="单位" align="center" prop="unit" />
        <el-table-column label="类别" align="center" prop="category" />
      </el-table>

      <pagination
        v-show="materialTotal>0"
        :total="materialTotal"
        :page.sync="materialQueryParams.pageNum"
        :limit.sync="materialQueryParams.pageSize"
        :pageSizes="[10, 20, 30, 50]"
        @pagination="getMaterialList"
      />

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmMaterialSelect" :disabled="!selectedMaterial">确 定</el-button>
        <el-button @click="materialSelectOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 出入库申请详情对话框 -->
    <el-dialog
      title="出入库申请详情"
      :visible.sync="billDetailVisible"
      width="1000px"
      append-to-body
      :close-on-click-modal="false"
    >
      <div v-if="billDetailData" v-loading="billDetailLoading">
        <el-tabs v-model="activeBillTab">
          <!-- 基本信息 -->
          <el-tab-pane label="基本信息" name="basic">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="单据编号">{{ billDetailData.billNo }}</el-descriptions-item>
              <el-descriptions-item label="单据类型">{{ getBillTypeText(billDetailData.billType) }}</el-descriptions-item>
              <el-descriptions-item label="业务类型">{{ billDetailData.businessType }}</el-descriptions-item>
              <el-descriptions-item label="外部系统标识">{{ billDetailData.externalSystem || '-' }}</el-descriptions-item>
              <el-descriptions-item label="外部系统单号">{{ billDetailData.externalSystemNo || '-' }}</el-descriptions-item>
              <el-descriptions-item label="申请时间">{{ billDetailData.applyTime }}</el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>

          <!-- 申请信息 -->
          <el-tab-pane label="申请信息" name="application">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="申请人">{{ billDetailData.applicantName }}</el-descriptions-item>
              <el-descriptions-item label="申请部门">{{ billDetailData.applicantDeptName }}</el-descriptions-item>
              <el-descriptions-item label="申请原因">{{ billDetailData.applyReason || '-' }}</el-descriptions-item>
              <el-descriptions-item label="审批状态">
                <el-tag :type="getApprovalStatusTagType(billDetailData.approvalStatus)" size="mini">
                  {{ getApprovalStatusText(billDetailData.approvalStatus) }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>

          <!-- 领料员信息 -->
          <el-tab-pane label="领料员信息" name="receiver">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="领料员">{{ billDetailData.receiverName || '-' }}</el-descriptions-item>
              <el-descriptions-item label="领料部门">{{ billDetailData.receiverDeptName || '-' }}</el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>

          <!-- 物料清单 -->
          <el-tab-pane label="物料清单" name="materials" v-if="billDetailData.materialBillItems && billDetailData.materialBillItems.length > 0">
            <el-table :data="billDetailData.materialBillItems" border stripe>
              <el-table-column type="index" label="序号" width="60" align="center" />
              <el-table-column label="物料编码" prop="materialCode" width="120" />
              <el-table-column label="物料名称" prop="materialName" min-width="150" />
              <el-table-column label="规格型号" prop="specification" width="120" />
              <el-table-column label="申请数量" prop="quantity" width="100" align="center" />
              <el-table-column label="单位" prop="unit" width="80" align="center" />
              <el-table-column label="单价" prop="unitPrice" width="100" align="center">
                <template slot-scope="scope">
                  <span>{{ scope.row.unitPrice ? '¥' + scope.row.unitPrice : '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="总价" prop="totalPrice" width="100" align="center">
                <template slot-scope="scope">
                  <span>{{ scope.row.totalPrice ? '¥' + scope.row.totalPrice : '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="备注" prop="remark" />
            </el-table>
          </el-tab-pane>

          <!-- 审批信息 -->
          <el-tab-pane label="审批信息" name="approval">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="审批实例ID">{{ billDetailData.approvalInstanceId || '-' }}</el-descriptions-item>
              <el-descriptions-item label="审批状态">
                <el-tag :type="getApprovalStatusTagType(billDetailData.approvalStatus)" size="mini">
                  {{ getApprovalStatusText(billDetailData.approvalStatus) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="创建时间">{{ billDetailData.createTime }}</el-descriptions-item>
              <el-descriptions-item label="更新时间">{{ billDetailData.updateTime }}</el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>
        </el-tabs>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="billDetailVisible = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 物料清单详情对话框 -->
    <el-dialog
      title="物料清单详情"
      :visible.sync="materialBillDetailVisible"
      width="1000px"
      append-to-body
      :close-on-click-modal="false"
    >
      <div v-if="materialBillDetailData" v-loading="materialBillDetailLoading">
        <el-tabs v-model="activeMaterialBillTab">
          <!-- 基本信息 -->
          <el-tab-pane label="基本信息" name="basic">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="清单编号">{{ materialBillDetailData.billCode }}</el-descriptions-item>
              <el-descriptions-item label="清单名称">{{ materialBillDetailData.billName }}</el-descriptions-item>
              <el-descriptions-item label="清单类型">{{ materialBillDetailData.billType }}</el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="materialBillDetailData.status === '1' ? 'success' : 'info'" size="mini">
                  {{ materialBillDetailData.status === '1' ? '启用' : '禁用' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="创建人">{{ materialBillDetailData.createBy }}</el-descriptions-item>
              <el-descriptions-item label="创建时间">{{ materialBillDetailData.createTime }}</el-descriptions-item>
              <el-descriptions-item label="备注" :span="2">{{ materialBillDetailData.remark || '-' }}</el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>

          <!-- 物料明细 -->
          <el-tab-pane label="物料明细" name="items">
            <!-- 重量汇总信息 -->
            <el-card class="box-card" style="margin-bottom: 20px;">
              <div slot="header" class="clearfix">
                <span style="font-weight: bold;">重量汇总</span>
              </div>
              <el-row :gutter="20" style="padding: 10px; background-color: #f5f7fa; border: 2px solid #e74c3c; border-radius: 4px;">
                <el-col :span="8">
                  <div class="weight-summary-item">
                    <label>数量：</label>
                    <span style="font-weight: bold; color: #409EFF;">{{ getMaterialBillDetailTotalQuantity() }}</span>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="weight-summary-item">
                    <label>单重(kg)：</label>
                    <span style="font-weight: bold; color: #E6A23C;">{{ getMaterialBillDetailAverageWeight() }}</span>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="weight-summary-item">
                    <label>总重(kg)：</label>
                    <span style="font-weight: bold; color: #67C23A;">{{ getMaterialBillDetailTotalWeight() }}</span>
                  </div>
                </el-col>
              </el-row>
            </el-card>

            <el-table :data="getMaterialBillItems(materialBillDetailData)" border stripe>
              <el-table-column label="序号" type="index" width="60" align="center" />
              <el-table-column label="物料编码" prop="materialCode" width="120" show-overflow-tooltip />
              <el-table-column label="物料名称" prop="materialName" width="150" show-overflow-tooltip />
              <el-table-column label="规格型号" width="120" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span>{{ scope.row.materialSpec || scope.row.specification || '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="数量" prop="quantity" width="80" align="center" />
              <el-table-column label="单位" prop="unit" width="80" align="center" />
              <el-table-column label="单重" width="100" align="center">
                <template slot-scope="scope">
                  <span>{{ formatMaterialBillWeight(scope.row.unitWeight) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="总重" width="100" align="center">
                <template slot-scope="scope">
                  <span>{{ formatMaterialBillWeight(scope.row.totalWeight) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="存储位置" width="120" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span>{{ getStorageLocationText(scope.row) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="单价" prop="unitPrice" width="100" align="center">
                <template slot-scope="scope">
                  <span>{{ scope.row.unitPrice ? '¥' + scope.row.unitPrice : '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="总价" width="100" align="center">
                <template slot-scope="scope">
                  <span>{{ scope.row.unitPrice && scope.row.quantity ? '¥' + (scope.row.unitPrice * scope.row.quantity).toFixed(2) : '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="备注" prop="remark" show-overflow-tooltip />
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="materialBillDetailVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDetail, getDetail, delDetail, addDetail, updateDetail, getApprovedInoutByBillNo, startExecution, completeExecution, getExecutionStatus } from "@/api/inout/detail"
import { getMaterialBillByCode } from "@/api/material/bill"
import { APPROVAL_STATUS, getApprovalStatusText } from "@/utils/approvalStatus"

export default {
  name: "Detail",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 出入库明细表格数据
      detailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 提交加载状态
      submitLoading: false,
      // 当前表单标签页
      activeFormTab: 'basic',
      // 单据查询相关
      queryBillNo: '',
      queryLoading: false,
      queryResult: null,
      // 物料清单相关
      materialBillItems: [],
      selectedMaterials: [],
      // 单据详情对话框相关
      billDetailVisible: false,
      billDetailLoading: false,
      billDetailData: null,
      activeBillTab: 'basic',
      // 物料清单详情对话框相关
      materialBillDetailVisible: false,
      materialBillDetailLoading: false,
      materialBillDetailData: null,
      activeMaterialBillTab: 'basic',
      // 统计数据
      statistics: {
        totalCount: 0,
        statusStats: {
          processing: 0,
          completed: 0,
          cancelled: 0
        }
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        status: null
      },
      // 表单参数
      form: {
        // 必填字段
        billId: null, // 单据ID（必填）
        materialId: null, // 物料ID（必填）
        quantity: 1, // 数量（必填）
        unit: '个', // 单位（必填）
        // 执行相关字段
        executionMode: 'web', // 执行方式（web/self_service/miniprogram）
        executorName: '', // 领料员姓名
        executorId: '', // 领料员ID
        executorFace: '', // 领料员人脸照片
        recorderName: '', // 录单人员姓名
        recorderId: '', // 录单人员ID
        inoutType: '0', // 出入库类型（0-入库，1-出库，2-调拨）
        billType: '0', // 单据类型（来自出入库申请）
        // 默认状态
        executionStatus: 'not_started', // 执行状态
        status: '0', // 处理状态
        // 可编辑字段
        actualWeight: null, // 实际重量
        executionRemark: '' // 执行备注
      },
      // 选项数据
      materialOptions: [], // 物料选项
      // 物料选择相关
      materialSelectOpen: false,
      materialLoading: false,
      materialList: [],
      materialTotal: 0,
      selectedMaterial: null,
      materialQueryParams: {
        pageNum: 1,
        pageSize: 10,
        materialCode: null,
        materialName: null
      },
      // 表单校验
      rules: {
        billId: [
          { required: true, message: "单据ID不能为空", trigger: "change" }
        ],
        materialId: [
          { required: true, message: "物料ID不能为空", trigger: "change" }
        ],
        quantity: [
          { required: true, message: "数量不能为空", trigger: "blur" },
          { type: 'number', min: 0.001, message: "数量必须大于0", trigger: "blur" }
        ],
        unit: [
          { required: true, message: "单位不能为空", trigger: "blur" }
        ],

        executionMode: [
          { required: true, message: "执行方式不能为空", trigger: "change" }
        ],

        executorName: [
          { required: true, message: "执行人不能为空", trigger: "blur" }
        ],
        inoutType: [
          { required: true, message: "出入库类型不能为空", trigger: "change" }
        ],
        billType: [
          { required: true, message: "单据类型不能为空", trigger: "change" }
        ],
        executionStatus: [
          { required: true, message: "执行状态不能为空", trigger: "change" }
        ],
        actualWeight: [
          { type: 'number', min: 0, message: "实际重量必须大于等于0", trigger: "blur" }
        ],
        executionRemark: [
          { max: 500, message: "执行备注不能超过500个字符", trigger: "blur" }
        ]
      }
    }
  },
  computed: {
    // 是否可以导入数据
    canImportData() {
      try {
        return this.queryResult &&
               this.queryResult.success &&
               this.queryResult.data &&
               this.queryResult.data.approvalStatus === '1' // 1表示已通过
      } catch (error) {
        console.error('canImportData error:', error)
        return false
      }
    }
  },
  created() {
    this.getList()
    this.getStatistics()
    this.getMaterialOptions()
  },
  methods: {
    /** 获取完整的图片URL */
    getImageUrl(imagePath) {
      if (!imagePath) {
        return ''
      }
      // 如果已经是完整URL，直接返回
      if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
        return imagePath
      }
      // 否则加上API前缀
      return process.env.VUE_APP_BASE_API + imagePath
    },

    /** 从明细数据中获取单据编号 */
    getBillNoFromDetail(row) {
      // 优先从关联的单据信息中获取
      if (row.inoutApplication && row.inoutApplication.billNo) {
        return row.inoutApplication.billNo;
      }
      // 其次从明细数据中获取
      if (row.billNo) {
        return row.billNo;
      }
      // 最后尝试从其他可能的字段获取
      if (row.relatedBillNo) {
        return row.relatedBillNo;
      }
      return '-';
    },

    /** 从明细数据中获取物料清单编号 */
    getMaterialBillCodeFromDetail(row) {
      // 优先从关联的单据信息中获取
      if (row.inoutApplication && row.inoutApplication.materialBillCode) {
        return row.inoutApplication.materialBillCode;
      }
      // 其次从明细数据中获取
      if (row.materialBillCode) {
        return row.materialBillCode;
      }
      return '-';
    },

    /** 从明细数据中获取执行时间 */
    getExecutionTimeFromDetail(row) {
      // 优先使用执行时间
      if (row.executionTime) {
        return this.parseTime(row.executionTime, '{y}-{m}-{d} {h}:{i}:{s}');
      }
      // 其次使用执行开始时间
      if (row.executionStartTime) {
        return this.parseTime(row.executionStartTime, '{y}-{m}-{d} {h}:{i}:{s}');
      }
      // 最后使用创建时间
      if (row.createTime) {
        return this.parseTime(row.createTime, '{y}-{m}-{d} {h}:{i}:{s}');
      }
      return '-';
    },

    /** 获取执行方式标签类型 */
    getExecutionModeTagType(mode) {
      const typeMap = {
        'web': 'primary',
        'self_service': 'success',
        'miniprogram': 'warning',
        'manual': 'info'
      };
      return typeMap[mode] || 'info';
    },

    /** 获取执行方式文本 */
    getExecutionModeText(mode) {
      const textMap = {
        'web': 'Web录入',
        'self_service': '自助机录入',
        'miniprogram': '小程序录入',
        'manual': '人工补录'
      };
      return textMap[mode] || mode || '-';
    },

    /** 获取出入库类型标签类型 */
    getInoutTypeTagType(type) {
      const typeMap = {
        '0': 'success',  // 入库
        '1': 'warning',  // 出库
        '2': 'info',     // 调拨
        'inbound': 'success',
        'outbound': 'warning',
        'transfer': 'info'
      };
      return typeMap[type] || 'info';
    },

    /** 获取出入库类型文本 */
    getInoutTypeText(type) {
      const textMap = {
        '0': '入库',
        '1': '出库',
        '2': '调拨',
        'inbound': '入库',
        'outbound': '出库',
        'transfer': '调拨'
      };
      return textMap[type] || type || '-';
    },

    /** 获取单据类型文本 */
    getBillTypeText(type) {
      const textMap = {
        '0': '出库',
        '1': '入库',
        '2': '复合'
      };
      return textMap[type] || type || '-';
    },

    /** 获取单据类型标签类型 */
    getBillTypeTagType(type) {
      const typeMap = {
        '0': 'warning',  // 出库
        '1': 'success',  // 入库
        '2': 'info'      // 复合
      };
      return typeMap[type] || 'info';
    },

    /** 获取规格型号显示文本 */
    getSpecification(row) {
      const spec = row.materialSpec || row.specification || '';
      const model = row.materialModel || '';
      if (spec && model) {
        return `${spec} | ${model}`;
      } else if (spec) {
        return spec;
      } else if (model) {
        return model;
      }
      return '-';
    },
    /** 获取存储位置显示文本 */
    getStorageLocation(row) {
      if (row.storageType === 'location' && row.locationCode) {
        return `货位: ${row.locationCode}`;
      } else if (row.storageType === 'container' && row.containerCode) {
        return `容器: ${row.containerCode}`;
      } else if (row.locationCode) {
        return row.locationCode;
      }
      return '-';
    },
    /** 获取物料选项 */
    getMaterialOptions() {
      // 这里调用物料列表接口获取物料选项
      // 暂时使用模拟数据
      this.materialOptions = [
        { id: 1, materialCode: 'MAT001', materialName: '测试物料1' },
        { id: 2, materialCode: 'MAT002', materialName: '测试物料2' },
        { id: 3, materialCode: 'MAT003', materialName: '测试物料3' }
      ]
    },
    /** 查询出入库明细列表 */
    getList() {
      this.loading = true
      listDetail(this.queryParams).then(response => {
        this.detailList = response.rows
        this.total = response.total
        this.loading = false
        this.updateStatistics()
      })
    },
    /** 获取统计数据 */
    getStatistics() {
      // 这里可以调用统计接口
      // 暂时从列表数据中计算
      this.updateStatistics()
    },
    /** 更新统计数据 */
    updateStatistics() {
      this.statistics.totalCount = this.detailList.length
      this.statistics.statusStats = {
        processing: this.detailList.filter(item => item.status === '1').length,
        completed: this.detailList.filter(item => item.status === '2').length,
        cancelled: this.detailList.filter(item => item.status === '3').length
      }
    },

    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        // 必填字段
        billId: null, // 单据ID
        materialId: null, // 物料ID
        quantity: 1, // 数量
        unit: '个', // 单位
        // 执行相关字段
        executionMode: 'web', // 执行方式
        executorName: '', // 领料员姓名
        executorId: '', // 领料员ID
        executorFace: '', // 领料员人脸照片
        recorderName: this.$store.state.user.name || '系统用户', // 录单人员姓名（自动填充当前用户）
        recorderId: this.$store.state.user.userId || '', // 录单人员ID（自动填充当前用户）
        inoutType: '0', // 出入库类型
        billType: '0', // 单据类型
        // 默认状态
        executionStatus: 'not_started', // 执行状态
        status: '0', // 处理状态
        // 可编辑字段
        actualWeight: null, // 实际重量
        executionRemark: '' // 执行备注
      }
      this.activeFormTab = 'basic'
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加出入库明细"
    },
    /** 查询单据编号 */
    handleQueryBillNo() {
      if (!this.queryBillNo || this.queryBillNo.trim() === '') {
        this.$modal.msgWarning('请输入单据编号')
        return
      }

      this.queryLoading = true
      this.queryResult = null
      this.materialBillItems = []
      this.selectedMaterials = []

      getApprovedInoutByBillNo(this.queryBillNo.trim()).then(response => {
        if (response.code === 200) {
          this.queryResult = {
            success: true,
            message: '查询成功，单据审批状态为：' + this.getApprovalStatusText(response.data.inoutApplication.approvalStatus),
            data: response.data
          }

          // 设置物料清单数据
          if (response.data.materialBillItems && response.data.materialBillItems.length > 0) {
            this.materialBillItems = response.data.materialBillItems
          }

          // 自动填充表单数据
          this.autoFillFormData(response.data)

          // 如果审批状态不是已通过，显示警告
          if (response.data.inoutApplication.approvalStatus !== '1') { // 1表示已通过
            this.queryResult.success = false
            this.queryResult.message = '单据审批状态为：' + this.getApprovalStatusText(response.data.inoutApplication.approvalStatus) + '，无法创建明细'
          }
        } else {
          this.queryResult = {
            success: false,
            message: response.msg || '查询失败',
            data: null
          }
        }
      }).catch(error => {
        console.error('查询单据失败:', error)
        this.queryResult = {
          success: false,
          message: '查询失败：' + (error.message || '网络错误'),
          data: null
        }
      }).finally(() => {
        this.queryLoading = false
      })
    },
    /** 自动填充表单数据 */
    autoFillFormData(data) {
      if (!data || !data.inoutApplication) return

      const inoutApp = data.inoutApplication

      // 0. 填充必填的数据库字段
      this.form.billId = inoutApp.id || null
      this.form.materialId = 1 // 默认物料ID，用户需要选择具体物料
      this.form.quantity = 1 // 默认数量
      this.form.unit = '个' // 默认单位

      // 1. 自动判断执行方式（根据当前访问方式）
      this.form.executionMode = this.detectExecutionMode()

      // 2. 自动填充领料员信息（从出入库申请中获取）
      this.form.executorName = inoutApp.receiverName || '未知'
      this.form.executorId = inoutApp.receiverId || ''
      this.form.executorFace = inoutApp.receiverFace || ''

      // 2.1. 自动填充录单人员信息（当前登录用户）
      this.form.recorderName = this.$store.state.user.name || '系统用户'
      this.form.recorderId = this.$store.state.user.userId || ''

      // 3. 根据查询单据的单据类型自动填写
      this.form.billType = inoutApp.billType || '0'
      this.form.inoutType = this.getInoutTypeFromBillType(inoutApp.billType) || '0'

      // 4. 设置默认状态
      this.form.status = '0' // 未处理
      this.form.executionStatus = 'not_started' // 未执行

      console.log('自动填充表单数据完成:', this.form)
    },

    /** 检测执行方式 */
    detectExecutionMode() {
      // 根据用户代理或其他方式判断访问来源
      const userAgent = navigator.userAgent.toLowerCase()

      if (userAgent.includes('micromessenger')) {
        return 'miniprogram' // 微信小程序
      } else if (userAgent.includes('mobile') || userAgent.includes('android') || userAgent.includes('iphone')) {
        return 'self_service' // 移动端，可能是自助机
      } else {
        return 'web' // Web端
      }
    },

    /** 格式化日期时间 */
    formatDateTime(date) {
      if (!date) return ''
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },

    /** 查看领料员人脸照片 */
    handleViewExecutorFace(row) {
      if (!row.executorName) {
        this.$modal.msgWarning('暂无领料员信息')
        return
      }

      // 获取人脸照片URL，优先从明细数据获取，其次从关联的申请数据获取
      let faceUrl = this.getExecutorFaceUrl(row)
      if (!faceUrl) {
        this.$modal.msgWarning('暂无领料员人脸照片')
        return
      }

      // 显示人脸照片对话框
      this.showFaceDialog(row.executorName, faceUrl)
    },

    /** 获取领料员人脸照片URL */
    getExecutorFaceUrl(row) {
      // 优先从明细数据中获取
      if (row.executorFace) {
        return row.executorFace
      }

      // 其次从关联的出入库申请中获取
      if (row.inoutApplication && row.inoutApplication.receiverFace) {
        return row.inoutApplication.receiverFace
      }

      // 最后尝试从其他可能的字段获取
      if (row.receiverFace) {
        return row.receiverFace
      }

      return null
    },

    /** 显示人脸照片对话框 */
    showFaceDialog(name, faceUrl) {
      this.$alert(
        `<div style="text-align: center;">
          <h3>领料员：${name}</h3>
          <img src="${this.getImageUrl(faceUrl)}" alt="人脸照片" style="max-width: 300px; max-height: 300px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);" />
        </div>`,
        '领料员人脸照片',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定',
          customClass: 'face-dialog'
        }
      )
    },

    /** 获取执行方式文本 */
    getExecutionModeText(mode) {
      const modeMap = {
        'web': 'Web录入',
        'self_service': '自助机录入',
        'miniprogram': '小程序录入'
      }
      return modeMap[mode] || mode
    },

    /** 清空查询 */
    handleClearQuery() {
      this.queryBillNo = ''
      this.queryResult = null
      this.materialBillItems = []
      this.selectedMaterials = []
      // 清空自动填充的表单数据
      this.form.executorName = ''
      this.form.executorId = ''
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getDetail(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改出入库明细"
      })
    },


    /** 获取物料列表 */
    getMaterialList() {
      this.materialLoading = true
      // 这里调用物料列表接口
      // 暂时使用模拟数据
      setTimeout(() => {
        this.materialList = [
          { materialCode: 'CS', materialName: '测试数据', specification: '标准规格', unit: '个', category: '原材料' },
          { materialCode: 'ABC123', materialName: '示例物料', specification: '高级规格', unit: 'kg', category: '成品' }
        ]
        this.materialTotal = this.materialList.length
        this.materialLoading = false
      }, 500)
    },
    /** 物料选择 */
    handleMaterialSelect(material) {
      this.selectedMaterial = material
    },
    /** 确认选择物料 */
    confirmMaterialSelect() {
      if (this.selectedMaterial) {
        this.materialSelectOpen = false
        this.selectedMaterial = null
      }
    },
    /** 重置物料查询 */
    resetMaterialQuery() {
      this.resetForm("materialQueryForm")
      this.getMaterialList()
    },




    /** 复制明细 */
    handleCopy(row) {
      this.reset()
      this.form = { ...row }
      this.form.id = null
      this.open = true
      this.title = "复制出入库明细"
    },

    /** 获取状态文本 */
    getStatusText(status) {
      const statusMap = {
        '0': '未处理',
        '1': '处理中',
        '2': '已完成',
        '3': '已取消'
      }
      return statusMap[status] || '未知'
    },
    /** 获取状态标签类型 */
    getStatusTagType(status) {
      const typeMap = {
        '0': 'info',
        '1': 'warning',
        '2': 'success',
        '3': 'danger'
      }
      return typeMap[status] || 'info'
    },


    /** 获取审批状态文本 */
    getApprovalStatusText(status) {
      return getApprovalStatusText(status)
    },
    /** 获取审批状态标签类型 */
    getApprovalStatusTagType(status) {
      const typeMap = {
        '0': 'warning',  // 审批中
        '1': 'success',  // 已通过
        '2': 'danger',   // 已驳回
        '3': 'info',     // 已撤销
        '4': 'info'      // 已终止
      }
      return typeMap[status] || 'info'
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 确保录单人员信息已设置
          if (!this.form.recorderName) {
            this.form.recorderName = this.$store.state.user.name || '系统用户'
          }
          if (!this.form.recorderId) {
            this.form.recorderId = this.$store.state.user.userId || ''
          }

          this.submitLoading = true
          if (this.form.id != null) {
            updateDetail(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            }).finally(() => {
              this.submitLoading = false
            })
          } else {
            addDetail(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            }).finally(() => {
              this.submitLoading = false
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除出入库明细编号为"' + ids + '"的数据项？').then(function() {
        return delDetail(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('inout/detail/export', {
        ...this.queryParams
      }, `detail_${new Date().getTime()}.xlsx`)
    },
    /** 开始执行 */
    handleStartExecution(row) {
      this.$modal.confirm('确认开始执行该出入库明细吗？').then(() => {
        return startExecution(row.id)
      }).then(() => {
        this.$modal.msgSuccess("开始执行成功")
        this.getList()
      }).catch(() => {})
    },
    /** 完成执行 */
    handleCompleteExecution(row) {
      this.$prompt('请输入执行备注（可选）', '完成执行', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'textarea',
        inputPlaceholder: '请输入执行过程中的备注信息...'
      }).then(({ value }) => {
        const executionData = {
          executionRemark: value || ''
        }
        return completeExecution(row.id, executionData)
      }).then(() => {
        this.$modal.msgSuccess("完成执行成功")
        this.getList()
      }).catch(() => {})
    },
    /** 查看执行状态 */
    handleViewExecutionStatus(row) {
      getExecutionStatus(row.id).then(response => {
        const data = response.data
        let statusInfo = `
          <div style="text-align: left;">
            <p><strong>执行方式：</strong>${data.executionMode === 'manual' ? '人工补录' : '流程执行'}</p>
            <p><strong>单据类型：</strong>${this.getBillTypeText(data.billType)}</p>
            <p><strong>执行状态：</strong>${this.getExecutionStatusText(data.executionStatus)}</p>
            <p><strong>开始时间：</strong>${data.executionStartTime || '未开始'}</p>
            <p><strong>完成时间：</strong>${data.executionEndTime || '未完成'}</p>
            <p><strong>执行人员：</strong>${data.executorName || '未指定'}</p>

            <p><strong>实际重量：</strong>${data.actualWeight || '未填写'}</p>
            <p><strong>执行备注：</strong>${data.executionRemark || '无'}</p>
          </div>
        `
        this.$alert(statusInfo, '执行状态详情', {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定'
        })
      }).catch(() => {
        this.$modal.msgError("查询执行状态失败")
      })
    },
    /** 获取出入库类型文本 */
    getInoutTypeText(type) {
      const typeMap = {
        'inbound': '入库',
        'outbound': '出库',
        'transfer': '调拨'
      }
      return typeMap[type] || '未知'
    },
    /** 获取出入库类型标签类型 */
    getInoutTypeTagType(type) {
      const typeMap = {
        'inbound': 'success',
        'outbound': 'warning',
        'transfer': 'info'
      }
      return typeMap[type] || 'info'
    },


    /** 从列表点击查看单据详情 */
    handleViewBillDetail(row) {
      const billNo = this.getBillNoFromDetail(row);
      if (!billNo || billNo === '-') {
        this.$modal.msgWarning('该明细没有关联的单据编号');
        return;
      }

      // 设置查询单据编号并执行查询
      this.queryBillNo = billNo;
      this.queryBillDetailByNo(billNo);
    },

    /** 通过单据编号查询单据详情 */
    queryBillDetailByNo(billNo) {
      this.queryLoading = true;
      this.queryResult = null;

      getApprovedInoutByBillNo(billNo).then(response => {
        if (response.code === 200) {
          this.queryResult = {
            success: true,
            message: '查询成功',
            data: response.data
          };

          // 显示详情对话框
          this.showBillDetailDialog();
        } else {
          this.queryResult = {
            success: false,
            message: response.msg || '查询失败',
            data: null
          };
          this.$modal.msgError('查询单据详情失败：' + response.msg);
        }
      }).catch(error => {
        console.error('查询单据详情失败:', error);
        this.queryResult = {
          success: false,
          message: '查询失败：' + error.message,
          data: null
        };
        this.$modal.msgError('查询单据详情失败：' + error.message);
      }).finally(() => {
        this.queryLoading = false;
      });
    },

    /** 从列表点击查看物料清单详情 */
    handleViewMaterialBillDetail(row) {
      const materialBillCode = this.getMaterialBillCodeFromDetail(row);
      if (!materialBillCode || materialBillCode === '-') {
        this.$modal.msgWarning('该明细没有关联的物料清单编号');
        return;
      }

      // 查询物料清单详情
      this.queryMaterialBillDetailByCode(materialBillCode);
    },

    /** 通过物料清单编号查询详情 */
    queryMaterialBillDetailByCode(billCode) {
      this.materialBillDetailLoading = true;
      this.materialBillDetailData = null;

      getMaterialBillByCode(billCode).then(response => {
        if (response.code === 200) {
          this.materialBillDetailData = response.data;
          this.materialBillDetailVisible = true;
        } else {
          this.$modal.msgError('查询物料清单详情失败：' + response.msg);
        }
      }).catch(error => {
        console.error('查询物料清单详情失败:', error);
        this.$modal.msgError('查询物料清单详情失败：' + error.message);
      }).finally(() => {
        this.materialBillDetailLoading = false;
      });
    },

    /** 获取物料清单明细数据 */
    getMaterialBillItems(materialBillData) {
      if (!materialBillData) {
        return [];
      }

      // 尝试多种可能的数据结构
      if (materialBillData.materialBillItemList && materialBillData.materialBillItemList.length > 0) {
        return materialBillData.materialBillItemList;
      }

      if (materialBillData.items && materialBillData.items.length > 0) {
        return materialBillData.items;
      }

      if (materialBillData.itemList && materialBillData.itemList.length > 0) {
        return materialBillData.itemList;
      }

      return [];
    },

    /** 获取存储位置文本 */
    getStorageLocationText(item) {
      // 优先显示存储位置
      if (item.storageLocation) {
        return item.storageLocation;
      }

      // 其次显示货位编码
      if (item.locationCode) {
        return `货位: ${item.locationCode}`;
      }

      // 再次显示容器编码
      if (item.containerCode) {
        return `容器: ${item.containerCode}`;
      }

      // 最后显示仓库信息
      if (item.warehouseName) {
        return `仓库: ${item.warehouseName}`;
      }

      return '-';
    },

    /** 获取物料单重 */
    getMaterialWeight(item) {
      // 优先显示单重
      if (item.unitWeight !== null && item.unitWeight !== undefined) {
        const weight = parseFloat(item.unitWeight);
        if (!isNaN(weight)) {
          return this.formatWeight(weight);
        }
      }

      // 其次显示重量
      if (item.weight !== null && item.weight !== undefined) {
        const weight = parseFloat(item.weight);
        if (!isNaN(weight)) {
          return this.formatWeight(weight);
        }
      }

      // 最后显示材料重量
      if (item.materialWeight !== null && item.materialWeight !== undefined) {
        const weight = parseFloat(item.materialWeight);
        if (!isNaN(weight)) {
          return this.formatWeight(weight);
        }
      }

      return '-';
    },

    /** 获取总重量 */
    getTotalWeight(item) {
      // 优先使用已计算的总重量
      if (item.totalWeight !== null && item.totalWeight !== undefined) {
        const weight = parseFloat(item.totalWeight);
        if (!isNaN(weight)) {
          return this.formatWeight(weight);
        }
      }

      // 计算总重量：数量 × 单重
      const quantity = parseFloat(item.quantity) || 0;
      let unitWeight = 0;

      if (item.unitWeight !== null && item.unitWeight !== undefined) {
        unitWeight = parseFloat(item.unitWeight) || 0;
      } else if (item.weight !== null && item.weight !== undefined) {
        unitWeight = parseFloat(item.weight) || 0;
      } else if (item.materialWeight !== null && item.materialWeight !== undefined) {
        unitWeight = parseFloat(item.materialWeight) || 0;
      }

      if (quantity > 0 && unitWeight > 0) {
        const totalWeight = quantity * unitWeight;
        return this.formatWeight(totalWeight);
      }

      return '-';
    },

    /** 格式化重量显示 */
    formatWeight(weight) {
      if (weight === null || weight === undefined || isNaN(weight)) {
        return '-';
      }

      const weightNum = parseFloat(weight);

      // 根据重量大小选择合适的单位和精度
      if (weightNum >= 1000) {
        // 大于等于1000g，显示为kg
        return (weightNum / 1000).toFixed(2) + 'kg';
      } else if (weightNum >= 1) {
        // 1g到999g，显示为g
        return weightNum.toFixed(1) + 'g';
      } else if (weightNum > 0) {
        // 小于1g，显示为mg
        return (weightNum * 1000).toFixed(0) + 'mg';
      } else {
        return '0g';
      }
    },

    /** 获取物料清单详情总数量 */
    getMaterialBillDetailTotalQuantity() {
      if (!this.materialBillDetailData) {
        return 0;
      }
      const items = this.getMaterialBillItems(this.materialBillDetailData);
      return items.reduce((total, item) => {
        return total + (parseFloat(item.quantity) || 0);
      }, 0);
    },

    /** 获取物料清单详情平均单重 */
    getMaterialBillDetailAverageWeight() {
      if (!this.materialBillDetailData) {
        return 0;
      }
      const items = this.getMaterialBillItems(this.materialBillDetailData);
      if (items.length === 0) {
        return 0;
      }

      const totalWeight = items.reduce((total, item) => {
        return total + (parseFloat(item.unitWeight) || 0);
      }, 0);

      const averageWeight = totalWeight / items.length;
      return averageWeight.toFixed(2);
    },

    /** 获取物料清单详情总重量 */
    getMaterialBillDetailTotalWeight() {
      if (!this.materialBillDetailData) {
        return 0;
      }
      const items = this.getMaterialBillItems(this.materialBillDetailData);
      return items.reduce((total, item) => {
        const quantity = parseFloat(item.quantity) || 0;
        const unitWeight = parseFloat(item.unitWeight) || 0;
        return total + (quantity * unitWeight);
      }, 0).toFixed(2);
    },

    /** 格式化物料清单重量显示（输入单位为kg） */
    formatMaterialBillWeight(weight) {
      if (weight === null || weight === undefined || isNaN(weight)) {
        return '-';
      }

      const weightNum = parseFloat(weight);

      // 数据库中存储的重量单位是kg，直接显示
      if (weightNum >= 1) {
        return weightNum.toFixed(2) + 'kg';
      } else if (weightNum > 0) {
        // 小于1kg，转换为g显示
        return (weightNum * 1000).toFixed(0) + 'g';
      } else {
        return '0kg';
      }
    },

    /** 获取执行状态文本 */
    getExecutionStatusText(status) {
      const statusMap = {
        'not_started': '未执行',
        'in_progress': '执行中',
        'completed': '已完成',
        'cancelled': '已取消'
      }
      return statusMap[status] || '未知'
    },
    /** 获取执行状态标签类型 */
    getExecutionStatusTagType(status) {
      const typeMap = {
        'not_started': 'info',
        'in_progress': 'warning',
        'completed': 'success',
        'cancelled': 'danger'
      }
      return typeMap[status] || 'info'
    },
    /** 获取单据数据值的安全方法 */
    getBillDataValue(field, formatter = null) {
      try {
        if (!this.queryResult || !this.queryResult.data) {
          return ''
        }

        let value = ''
        if (this.queryResult.data.inoutApplication) {
          // 新的数据结构
          value = this.queryResult.data.inoutApplication[field]
        } else {
          // 兼容旧的数据结构
          value = this.queryResult.data[field]
        }

        if (formatter && typeof formatter === 'function') {
          return formatter(value)
        }
        return value || ''
      } catch (error) {
        console.error('getBillDataValue error:', error)
        return ''
      }
    },
    /** 物料选择变化 */
    handleMaterialSelectionChange(selection) {
      this.selectedMaterials = selection
    },
    /** 全选物料 */
    handleSelectAllMaterials() {
      this.$refs.materialTable && this.$refs.materialTable.toggleAllSelection()
    },
    /** 清空物料选择 */
    handleClearMaterialSelection() {
      this.selectedMaterials = []
      this.$refs.materialTable && this.$refs.materialTable.clearSelection()
    },
    /** 从物料清单创建出入库明细 */
    handleCreateDetailFromMaterials() {
      if (this.selectedMaterials.length === 0) {
        this.$modal.msgWarning('请先选择要创建明细的物料')
        return
      }

      // 获取单据信息
      const billData = this.getBillData()
      if (!billData) {
        this.$modal.msgError('获取单据信息失败')
        return
      }

      // 批量创建明细
      const promises = this.selectedMaterials.map(material => {
        const detailData = {
          // 必填字段
          billId: billData.id,
          materialId: material.materialId || 1, // 如果没有materialId，使用默认值
          quantity: material.quantity || 1,
          unit: material.unit || '个',
          // 执行相关字段
          executionMode: this.detectExecutionMode(),
          executorName: this.$store.state.user.name || '系统用户',
          executorId: this.$store.state.user.userId || '',
          inoutType: this.getInoutTypeFromBillType(billData.billType) || '0',
          billType: billData.billType || '0',
          // 状态字段
          executionStatus: 'not_started',
          status: '0' // 未处理
        }
        return addDetail(detailData)
      })

      Promise.all(promises).then(responses => {
        const successCount = responses.filter(res => res.code === 200).length
        if (successCount === this.selectedMaterials.length) {
          this.$modal.msgSuccess(`成功创建 ${successCount} 个出入库明细`)
          this.getList() // 刷新列表
          this.handleClearQuery() // 清空查询
        } else {
          this.$modal.msgWarning(`创建完成，成功 ${successCount} 个，失败 ${this.selectedMaterials.length - successCount} 个`)
          this.getList() // 刷新列表
        }
      }).catch(error => {
        console.error('批量创建明细失败:', error)
        this.$modal.msgError('创建出入库明细失败')
      })
    },
    /** 手动创建明细 */
    handleManualCreateDetail() {
      const billData = this.getBillData()
      if (!billData) {
        this.$modal.msgError('获取单据信息失败')
        return
      }

      // 预填充表单数据
      this.reset()
      // 自动填充基本数据
      this.form.billId = billData.id
      this.form.materialId = 1 // 默认物料ID
      this.form.quantity = 1
      this.form.unit = '个'
      this.form.executionMode = this.detectExecutionMode()
      this.form.executorName = this.$store.state.user.name || '系统用户'
      this.form.executorId = this.$store.state.user.userId || ''
      this.form.inoutType = this.getInoutTypeFromBillType(billData.billType) || '0'
      this.form.billType = billData.billType || '0'
      this.form.status = '0'
      this.form.executionStatus = 'not_started'

      this.open = true
      this.title = "添加出入库明细"
    },
    /** 显示单据详情对话框 */
    showBillDetailDialog() {
      if (!this.queryResult || !this.queryResult.data) {
        this.$modal.msgError('没有可查看的单据数据')
        return
      }

      this.billDetailLoading = true
      this.billDetailVisible = true
      this.activeBillTab = 'basic'

      // 设置详情数据
      if (this.queryResult.data.inoutApplication) {
        // 新数据结构
        this.billDetailData = {
          ...this.queryResult.data.inoutApplication,
          materialBillItems: this.queryResult.data.materialBillItems || []
        }
      } else {
        // 旧数据结构
        this.billDetailData = {
          ...this.queryResult.data,
          materialBillItems: this.materialBillItems || []
        }
      }

      this.billDetailLoading = false
    },
    /** 获取单据数据 */
    getBillData() {
      try {
        if (!this.queryResult || !this.queryResult.data) {
          return null
        }

        if (this.queryResult.data.inoutApplication) {
          return this.queryResult.data.inoutApplication
        } else {
          return this.queryResult.data
        }
      } catch (error) {
        console.error('getBillData error:', error)
        return null
      }
    },
    /** 根据单据类型获取出入库类型 */
    getInoutTypeFromBillType(billType) {
      const typeMap = {
        '1': 'inbound',  // 申请单 -> 入库
        '2': 'outbound', // 出库单 -> 出库
        '3': 'inbound',  // 入库单 -> 入库
        '4': 'transfer'  // 调拨单 -> 调拨
      }
      return typeMap[billType] || 'inbound'
    }
  }
}
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}

.statistics-card {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.statistics-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.statistics-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.statistics-icon {
  font-size: 32px;
  margin-right: 15px;
  width: 50px;
  text-align: center;
}

.statistics-info {
  flex: 1;
}

.statistics-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.statistics-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.filter-container,
.operation-container,
.table-container {
  margin-bottom: 20px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.filter-container:hover,
.operation-container:hover,
.table-container:hover {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.text-danger {
  color: #F56C6C;
}

.text-muted {
  color: #C0C4CC;
}

.el-table .text-danger {
  color: #F56C6C;
}

.el-button--text.text-danger {
  color: #F56C6C;
}

.el-button--text.text-danger:hover {
  color: #F78989;
}

/* 表单样式优化 */
.el-dialog__body {
  padding: 20px 20px 0;
}

.el-tabs__content {
  padding-top: 20px;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-input-number {
  width: 100%;
}

/* 表格样式优化 */
.el-table {
  border-radius: 8px;
  overflow: hidden;
}

.el-table th {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

.el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: #fafafa;
}

/* 分页样式 */
.el-pagination {
  text-align: center;
  margin-top: 20px;
}

/* 单据查询卡片样式 */
.bill-query-card {
  border: 1px solid #e6f7ff;
  background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
}

.bill-query-card .el-card__header {
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
  border-bottom: 1px solid #d9f7be;
}

.query-result {
  margin-top: 15px;
}

.bill-info {
  margin-top: 10px;
  padding: 15px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

/* 执行方式选择卡片样式 */
.execution-mode-card {
  border: 1px solid #fff2e8;
  background: linear-gradient(135deg, #fff7ed 0%, #fef3ec 100%);
}

.execution-mode-card .el-card__header {
  background: linear-gradient(135deg, #fff2e8 0%, #fef3ec 100%);
  border-bottom: 1px solid #ffd8bf;
}

.execution-mode-card .el-radio {
  display: block;
  margin-bottom: 15px;
  padding: 10px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: #fafafa;
  transition: all 0.3s ease;
}

.execution-mode-card .el-radio:hover {
  border-color: #409EFF;
  background: #f0f9ff;
}

.execution-mode-card .el-radio.is-checked {
  border-color: #409EFF;
  background: #e6f7ff;
}

.execution-mode-card .el-radio__label {
  font-weight: 600;
  color: #303133;
}

.execution-mode-card .el-alert {
  border-radius: 6px;
}

.bill-info .el-descriptions {
  background: white;
}

/* 重量汇总样式 */
.weight-summary-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 0;
}

.weight-summary-item label {
  margin-right: 8px;
  font-weight: 500;
  color: #606266;
}

/* 人脸照片对话框样式 */
.face-dialog .el-message-box__content {
  padding: 20px !important;
}

.face-dialog .el-message-box__message {
  margin: 0 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .statistics-content {
    flex-direction: column;
    text-align: center;
  }

  .statistics-icon {
    margin-right: 0;
    margin-bottom: 10px;
  }

  .el-col {
    margin-bottom: 10px;
  }

  .bill-query-card {
    margin-bottom: 15px;
  }
}
</style>
