<template>
  <div class="material-dialog-test">
    <el-card header="物料明细对话框测试">
      <el-alert
        title="测试说明"
        type="info"
        description="这是物料明细对话框的独立测试页面，用于验证对话框是否能正常打开。已添加存储位置搜索功能。"
        style="margin-bottom: 20px;"
        show-icon
      />
      
      <el-button type="primary" @click="openDialog">打开物料明细对话框</el-button>
      <el-button type="info" @click="openConsole">打开控制台</el-button>
      
      <div style="margin-top: 20px;">
        <el-tag v-if="dialogOpened" type="success">对话框已打开过</el-tag>
        <el-tag v-else type="warning">对话框未打开</el-tag>
      </div>
      
      <!-- 简化版的物料明细对话框 -->
      <el-dialog title="添加物料明细（测试版）" :visible.sync="testDialogVisible" width="600px">
        <el-alert 
          title="测试成功！" 
          type="success" 
          description="如果您能看到这个对话框，说明基本功能正常。"
          show-icon
        />
        
        <el-form :model="testForm" label-width="100px" style="margin-top: 20px;">
          <el-form-item label="物料">
            <el-input v-model="testForm.material" placeholder="测试物料输入" />
          </el-form-item>
          <el-form-item label="数量">
            <el-input-number v-model="testForm.quantity" :min="1" />
          </el-form-item>
          <el-form-item label="存储位置">
            <el-input v-model="testForm.storageLocation" placeholder="测试存储位置输入" />
          </el-form-item>
        </el-form>
        
        <div slot="footer">
          <el-button @click="testDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="testSubmit">确 定</el-button>
        </div>
      </el-dialog>
      
      <!-- 实际的物料明细对话框 -->
      <material-item-dialog ref="materialDialog" @submit="handleSubmit" />
    </el-card>
  </div>
</template>

<script>
import MaterialItemDialog from '@/views/material/bill/components/MaterialItemDialog.vue'

export default {
  name: 'MaterialDialogTest',
  components: {
    MaterialItemDialog
  },
  data() {
    return {
      dialogOpened: false,
      testDialogVisible: false,
      testForm: {
        material: '',
        quantity: 1,
        storageLocation: ''
      }
    }
  },
  methods: {
    openDialog() {
      console.log('MaterialDialogTest: 尝试打开物料明细对话框')
      try {
        this.dialogOpened = true
        this.$refs.materialDialog.open()
        console.log('MaterialDialogTest: 对话框打开成功')
      } catch (error) {
        console.error('MaterialDialogTest: 打开对话框失败:', error)
        this.$message.error('打开对话框失败: ' + error.message)
        
        // 如果实际对话框失败，打开测试对话框
        this.testDialogVisible = true
      }
    },
    
    openConsole() {
      this.$message.info('请按F12打开浏览器开发者工具查看控制台日志')
    },
    
    testSubmit() {
      this.$message.success('测试提交成功！')
      this.testDialogVisible = false
    },
    
    handleSubmit(data) {
      console.log('MaterialDialogTest: 收到提交数据:', data)
      this.$message.success('物料明细提交成功！')
    }
  }
}
</script>

<style scoped>
.material-dialog-test {
  padding: 20px;
}
</style>
