import Layout from '@/layout'

const emqxRouter = {
  path: '/emqx',
  component: Layout,
  redirect: '/emqx/dashboard',
  name: 'Emqx',
  meta: {
    title: 'EMQX管理',
    icon: 'monitor',
    roles: ['admin', 'emqx_admin']
  },
  children: [
    {
      path: 'dashboard',
      component: () => import('@/views/emqx/dashboard/index'),
      name: 'EmqxDashboard',
      meta: {
        title: '系统概览',
        icon: 'dashboard',
        affix: true,
        noCache: false
      }
    },
    {
      path: 'serverRegistry',
      component: () => import('@/views/emqx/serverRegistry/index'),
      name: 'ServerRegistry',
      meta: {
        title: '服务器客户端',
        icon: 'connection',
        perms: ['emqx:server:list']
      }
    },
    {
      path: 'deviceAuth',
      component: () => import('@/views/emqx/deviceAuth/index'),
      name: 'DeviceAuth',
      meta: {
        title: '设备认证',
        icon: 'key',
        perms: ['emqx:device:list']
      }
    },
    {
      path: 'dataStream',
      component: () => import('@/views/emqx/dataStream/index'),
      name: 'DataStream',
      meta: {
        title: '数据分流',
        icon: 'data-line',
        perms: ['emqx:stream:list']
      }
    },
    {
      path: 'cleanup',
      component: () => import('@/views/emqx/cleanup/index'),
      name: 'Cleanup',
      meta: {
        title: '清理管理',
        icon: 'delete',
        perms: ['emqx:cleanup:list']
      }
    },
    {
      path: 'topicSync',
      component: () => import('@/views/emqx/topicSync/index'),
      name: 'TopicSync',
      meta: {
        title: '主题同步',
        icon: 'refresh',
        perms: ['emqx:topic:sync']
      }
    },
    {
      path: 'monitoring',
      component: () => import('@/views/emqx/monitoring/index'),
      name: 'EmqxMonitoring',
      meta: {
        title: '实时监控',
        icon: 'monitor',
        perms: ['emqx:monitor:view']
      }
    },
    {
      path: 'settings',
      component: () => import('@/views/emqx/settings/index'),
      name: 'EmqxSettings',
      meta: {
        title: '系统设置',
        icon: 'setting',
        perms: ['emqx:settings:manage']
      }
    }
  ]
}

export default emqxRouter
