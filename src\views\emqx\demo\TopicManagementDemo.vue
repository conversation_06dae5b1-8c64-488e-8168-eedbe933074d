<template>
  <div class="topic-management-demo">
    <div class="demo-header">
      <h1>EMQX主题动态管理演示</h1>
      <p>这个演示展示了如何使用EMQX主题动态管理功能</p>
    </div>

    <!-- 功能演示卡片 -->
    <el-row :gutter="20">
      <!-- 基础操作演示 -->
      <el-col :span="12">
        <el-card class="demo-card">
          <div slot="header">
            <span>基础操作演示</span>
          </div>
          
          <div class="demo-section">
            <h4>1. 添加主题订阅</h4>
            <el-form :model="demoForm" label-width="100px" size="small">
              <el-form-item label="客户端ID">
                <el-input v-model="demoForm.clientId" placeholder="输入客户端ID" />
              </el-form-item>
              <el-form-item label="主题">
                <el-input v-model="demoForm.topic" placeholder="例如: dwms/device/+/status" />
              </el-form-item>
              <el-form-item label="QoS等级">
                <el-select v-model="demoForm.qos" style="width: 100%">
                  <el-option label="QoS 0" :value="0" />
                  <el-option label="QoS 1" :value="1" />
                  <el-option label="QoS 2" :value="2" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="demoAddSubscription" :loading="demoLoading">
                  添加订阅
                </el-button>
                <el-button @click="resetDemoForm">重置</el-button>
              </el-form-item>
            </el-form>
          </div>

          <div class="demo-section">
            <h4>2. 批量操作演示</h4>
            <el-input
              type="textarea"
              v-model="batchTopics"
              :rows="4"
              placeholder="每行一个主题，例如：&#10;dwms/video/+/alarm&#10;dwms/access/+/event&#10;dwms/weight/+/data"
            />
            <div style="margin-top: 10px;">
              <el-button type="success" @click="demoBatchAdd" :loading="batchLoading">
                批量添加
              </el-button>
              <el-button type="warning" @click="demoBatchRemove" :loading="batchLoading">
                批量删除
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 订阅状态展示 -->
      <el-col :span="12">
        <el-card class="demo-card">
          <div slot="header">
            <span>订阅状态</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="refreshSubscriptions">
              刷新
            </el-button>
          </div>
          
          <div class="subscription-list">
            <div v-if="subscriptions.length === 0" class="empty-state">
              <i class="el-icon-info"></i>
              <p>暂无订阅数据</p>
            </div>
            
            <div v-else>
              <div
                v-for="(sub, index) in subscriptions"
                :key="index"
                class="subscription-item"
              >
                <div class="sub-info">
                  <div class="sub-topic">{{ sub.topic }}</div>
                  <div class="sub-details">
                    <span class="sub-client">客户端: {{ sub.clientId }}</span>
                    <el-tag :type="getQosTagType(sub.qos)" size="mini">
                      QoS {{ sub.qos }}
                    </el-tag>
                  </div>
                </div>
                <div class="sub-actions">
                  <el-button size="mini" type="primary" @click="editSubscription(sub)">
                    编辑
                  </el-button>
                  <el-button size="mini" type="danger" @click="removeSubscription(sub)">
                    删除
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 操作日志 -->
    <el-card class="demo-card" style="margin-top: 20px;">
      <div slot="header">
        <span>操作日志</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="clearLogs">
          清空日志
        </el-button>
      </div>
      
      <div class="operation-logs">
        <div v-if="operationLogs.length === 0" class="empty-state">
          <p>暂无操作记录</p>
        </div>
        
        <el-timeline v-else>
          <el-timeline-item
            v-for="(log, index) in operationLogs"
            :key="index"
            :type="log.success ? 'success' : 'danger'"
            :timestamp="log.timestamp"
          >
            <div class="log-content">
              <div class="log-operation">{{ log.operation }}</div>
              <div class="log-message" :class="log.success ? 'success' : 'error'">
                {{ log.message }}
              </div>
              <div v-if="log.details" class="log-details">
                {{ log.details }}
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-card>

    <!-- 动态主题管理组件 -->
    <DynamicTopicManager
      ref="dynamicTopicManager"
      @refresh="refreshSubscriptions"
    />
  </div>
</template>

<script>
import {
  addDynamicTopic,
  removeDynamicTopic,
  updateDynamicTopic
} from '@/api/emqx/topicManagement'
import {
  getActiveSubscriptions,
  batchAddSubscriptions,
  batchRemoveSubscriptions
} from '@/api/emqx/subscription'
import DynamicTopicManager from '../topicManagement/DynamicTopicManager.vue'

export default {
  name: 'TopicManagementDemo',
  components: {
    DynamicTopicManager
  },
  data() {
    return {
      demoForm: {
        clientId: 'demo_client_001',
        topic: 'dwms/demo/device/001/status',
        qos: 1
      },
      batchTopics: 'dwms/demo/video/001/alarm\ndwms/demo/access/001/event\ndwms/demo/weight/001/data',
      subscriptions: [],
      operationLogs: [],
      demoLoading: false,
      batchLoading: false
    }
  },
  
  created() {
    this.refreshSubscriptions()
    this.addLog('系统初始化', true, '演示页面加载完成')
  },
  
  methods: {
    // 演示添加订阅
    async demoAddSubscription() {
      if (!this.demoForm.clientId || !this.demoForm.topic) {
        this.$message.warning('请填写客户端ID和主题')
        return
      }
      
      this.demoLoading = true
      try {
        const response = await addDynamicTopic(this.demoForm)
        
        if (response.code === 200) {
          this.$message.success('订阅添加成功')
          this.addLog('添加订阅', true, `成功添加订阅: ${this.demoForm.topic}`)
          this.refreshSubscriptions()
        } else {
          this.$message.error('添加失败: ' + response.msg)
          this.addLog('添加订阅', false, response.msg)
        }
      } catch (error) {
        this.$message.error('操作失败')
        this.addLog('添加订阅', false, error.message)
      } finally {
        this.demoLoading = false
      }
    },
    
    // 演示批量添加
    async demoBatchAdd() {
      if (!this.batchTopics.trim()) {
        this.$message.warning('请输入主题列表')
        return
      }
      
      const topics = this.batchTopics
        .split('\n')
        .map(topic => topic.trim())
        .filter(topic => topic.length > 0)
        .map(topic => ({ topic, qos: 1 }))
      
      this.batchLoading = true
      try {
        const response = await batchAddSubscriptions({
          clientId: this.demoForm.clientId,
          subscriptions: topics
        })
        
        if (response.code === 200) {
          this.$message.success(`批量添加成功，共${topics.length}个主题`)
          this.addLog('批量添加', true, `成功添加${topics.length}个订阅`)
          this.refreshSubscriptions()
        } else {
          this.$message.error('批量添加失败: ' + response.msg)
          this.addLog('批量添加', false, response.msg)
        }
      } catch (error) {
        this.$message.error('批量操作失败')
        this.addLog('批量添加', false, error.message)
      } finally {
        this.batchLoading = false
      }
    },
    
    // 演示批量删除
    async demoBatchRemove() {
      if (!this.batchTopics.trim()) {
        this.$message.warning('请输入主题列表')
        return
      }
      
      const topics = this.batchTopics
        .split('\n')
        .map(topic => topic.trim())
        .filter(topic => topic.length > 0)
      
      this.batchLoading = true
      try {
        const response = await batchRemoveSubscriptions({
          clientId: this.demoForm.clientId,
          topics: topics
        })
        
        if (response.code === 200) {
          this.$message.success(`批量删除成功，共${topics.length}个主题`)
          this.addLog('批量删除', true, `成功删除${topics.length}个订阅`)
          this.refreshSubscriptions()
        } else {
          this.$message.error('批量删除失败: ' + response.msg)
          this.addLog('批量删除', false, response.msg)
        }
      } catch (error) {
        this.$message.error('批量操作失败')
        this.addLog('批量删除', false, error.message)
      } finally {
        this.batchLoading = false
      }
    },
    
    // 编辑订阅
    editSubscription(subscription) {
      this.$refs.dynamicTopicManager.showEditDialog(subscription)
    },
    
    // 删除订阅
    async removeSubscription(subscription) {
      try {
        await this.$confirm('确定要删除这个订阅吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const response = await removeDynamicTopic({
          clientId: subscription.clientId,
          topic: subscription.topic
        })
        
        if (response.code === 200) {
          this.$message.success('删除成功')
          this.addLog('删除订阅', true, `成功删除订阅: ${subscription.topic}`)
          this.refreshSubscriptions()
        } else {
          this.$message.error('删除失败: ' + response.msg)
          this.addLog('删除订阅', false, response.msg)
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败')
          this.addLog('删除订阅', false, error.message)
        }
      }
    },
    
    // 刷新订阅列表
    async refreshSubscriptions() {
      try {
        const response = await getActiveSubscriptions({ pageNum: 1, pageSize: 100 })
        if (response.code === 200) {
          this.subscriptions = response.rows || []
          this.addLog('刷新数据', true, `获取到${this.subscriptions.length}条订阅记录`)
        }
      } catch (error) {
        console.error('刷新订阅列表失败:', error)
        this.addLog('刷新数据', false, '获取订阅列表失败')
      }
    },
    
    // 重置表单
    resetDemoForm() {
      this.demoForm = {
        clientId: 'demo_client_001',
        topic: 'dwms/demo/device/001/status',
        qos: 1
      }
    },
    
    // 添加操作日志
    addLog(operation, success, message, details = null) {
      this.operationLogs.unshift({
        operation,
        success,
        message,
        details,
        timestamp: new Date().toLocaleTimeString()
      })
      
      // 限制日志数量
      if (this.operationLogs.length > 50) {
        this.operationLogs = this.operationLogs.slice(0, 50)
      }
    },
    
    // 清空日志
    clearLogs() {
      this.operationLogs = []
      this.$message.success('日志已清空')
    },
    
    // 获取QoS标签类型
    getQosTagType(qos) {
      const typeMap = { 0: 'info', 1: 'success', 2: 'warning' }
      return typeMap[qos] || 'info'
    }
  }
}
</script>

<style scoped>
.topic-management-demo {
  padding: 20px;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
}

.demo-header h1 {
  color: #303133;
  margin-bottom: 10px;
}

.demo-header p {
  color: #909399;
  font-size: 14px;
}

.demo-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.demo-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.demo-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.demo-section h4 {
  color: #303133;
  margin-bottom: 15px;
}

.subscription-list {
  max-height: 400px;
  overflow-y: auto;
}

.subscription-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  margin-bottom: 10px;
  background: #fafafa;
}

.sub-info {
  flex: 1;
}

.sub-topic {
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
  font-family: 'Courier New', monospace;
}

.sub-details {
  display: flex;
  align-items: center;
  gap: 10px;
}

.sub-client {
  color: #909399;
  font-size: 12px;
}

.sub-actions {
  display: flex;
  gap: 5px;
}

.operation-logs {
  max-height: 300px;
  overflow-y: auto;
}

.log-content {
  padding: 5px 0;
}

.log-operation {
  font-weight: bold;
  color: #303133;
}

.log-message.success {
  color: #67c23a;
}

.log-message.error {
  color: #f56c6c;
}

.log-details {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 15px;
}
</style>
