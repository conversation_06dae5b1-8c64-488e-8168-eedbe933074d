import request from '@/utils/request'

/**
 * 审批状态工具类
 * 用于检查和确认审批流程的完成状态
 */

/**
 * 检查审批完成状态
 * @param {Number} instanceId 审批实例ID
 * @returns {Promise} 返回状态检查结果
 */
export function checkApprovalCompletionStatus(instanceId) {
  return request({
    url: `/approval/completion/status/${instanceId}`,
    method: 'get'
  })
}

/**
 * 重新同步审批状态
 * @param {Number} instanceId 审批实例ID
 * @returns {Promise} 返回同步结果
 */
export function resyncApprovalStatus(instanceId) {
  return request({
    url: `/approval/completion/resync/${instanceId}`,
    method: 'post'
  })
}

/**
 * 轮询检查审批完成状态
 * @param {Number} instanceId 审批实例ID
 * @param {Object} options 配置选项
 * @param {Number} options.maxAttempts 最大尝试次数，默认10次
 * @param {Number} options.interval 检查间隔（毫秒），默认2000ms
 * @param {Function} options.onProgress 进度回调函数
 * @returns {Promise} 返回最终状态
 */
export function pollApprovalCompletionStatus(instanceId, options = {}) {
  const {
    maxAttempts = 10,
    interval = 2000,
    onProgress = null
  } = options

  return new Promise((resolve, reject) => {
    let attempts = 0

    const checkStatus = async () => {
      attempts++
      
      try {
        const response = await checkApprovalCompletionStatus(instanceId)
        
        if (response.code === 200) {
          const { completed, message } = response.data
          
          // 调用进度回调
          if (onProgress) {
            onProgress({
              attempts,
              maxAttempts,
              completed,
              message,
              instanceId
            })
          }
          
          if (completed) {
            // 审批已完成并同步
            resolve({
              success: true,
              completed: true,
              message,
              attempts,
              instanceId
            })
            return
          }
          
          if (attempts >= maxAttempts) {
            // 达到最大尝试次数
            resolve({
              success: false,
              completed: false,
              message: `检查超时：已尝试${attempts}次，审批可能仍在处理中`,
              attempts,
              instanceId
            })
            return
          }
          
          // 继续下次检查
          setTimeout(checkStatus, interval)
          
        } else {
          reject(new Error(`状态检查失败: ${response.msg}`))
        }
        
      } catch (error) {
        if (attempts >= maxAttempts) {
          reject(new Error(`状态检查异常: ${error.message}`))
        } else {
          // 出错时也继续重试
          setTimeout(checkStatus, interval)
        }
      }
    }

    // 开始检查
    checkStatus()
  })
}

/**
 * 等待审批完成
 * 这是一个便捷方法，会自动轮询直到审批完成
 * @param {Number} instanceId 审批实例ID
 * @param {Object} options 配置选项
 * @returns {Promise} 返回完成状态
 */
export function waitForApprovalCompletion(instanceId, options = {}) {
  const defaultOptions = {
    maxAttempts: 30, // 默认最多等待1分钟（30次 * 2秒）
    interval: 2000,
    onProgress: (progress) => {
      console.log(`审批状态检查进度: ${progress.attempts}/${progress.maxAttempts}, 完成状态: ${progress.completed}`)
    }
  }
  
  const finalOptions = { ...defaultOptions, ...options }
  
  return pollApprovalCompletionStatus(instanceId, finalOptions)
}

/**
 * 审批状态常量
 */
export const APPROVAL_STATUS = {
  PENDING: '0',    // 审批中
  APPROVED: '1',   // 已通过
  REJECTED: '2',   // 已驳回
  CANCELED: '3'    // 已撤销
}

/**
 * 获取审批状态文本
 * @param {String} status 状态码
 * @returns {String} 状态文本
 */
export function getApprovalStatusText(status) {
  const statusMap = {
    [APPROVAL_STATUS.PENDING]: '审批中',
    [APPROVAL_STATUS.APPROVED]: '已通过',
    [APPROVAL_STATUS.REJECTED]: '已驳回',
    [APPROVAL_STATUS.CANCELED]: '已撤销'
  }
  
  return statusMap[status] || '未知状态'
}

/**
 * 检查审批是否已结束
 * @param {String} status 状态码
 * @returns {Boolean} 是否已结束
 */
export function isApprovalFinished(status) {
  return [APPROVAL_STATUS.APPROVED, APPROVAL_STATUS.REJECTED, APPROVAL_STATUS.CANCELED].includes(status)
}

/**
 * 审批完成状态检查器类
 * 提供更高级的状态检查功能
 */
export class ApprovalStatusChecker {
  constructor(instanceId, options = {}) {
    this.instanceId = instanceId
    this.options = {
      maxAttempts: 30,
      interval: 2000,
      autoStart: false,
      ...options
    }
    this.isRunning = false
    this.callbacks = {
      progress: [],
      completed: [],
      failed: [],
      timeout: []
    }
  }

  /**
   * 添加事件监听器
   */
  on(event, callback) {
    if (this.callbacks[event]) {
      this.callbacks[event].push(callback)
    }
    return this
  }

  /**
   * 触发事件
   */
  emit(event, data) {
    if (this.callbacks[event]) {
      this.callbacks[event].forEach(callback => callback(data))
    }
  }

  /**
   * 开始检查
   */
  async start() {
    if (this.isRunning) {
      return
    }

    this.isRunning = true
    
    try {
      const result = await pollApprovalCompletionStatus(this.instanceId, {
        ...this.options,
        onProgress: (progress) => {
          this.emit('progress', progress)
        }
      })

      if (result.success && result.completed) {
        this.emit('completed', result)
      } else if (!result.success) {
        this.emit('timeout', result)
      }

    } catch (error) {
      this.emit('failed', { error, instanceId: this.instanceId })
    } finally {
      this.isRunning = false
    }
  }

  /**
   * 停止检查
   */
  stop() {
    this.isRunning = false
  }
}

/**
 * 创建审批状态检查器
 * @param {Number} instanceId 审批实例ID
 * @param {Object} options 配置选项
 * @returns {ApprovalStatusChecker} 检查器实例
 */
export function createApprovalStatusChecker(instanceId, options = {}) {
  return new ApprovalStatusChecker(instanceId, options)
}
