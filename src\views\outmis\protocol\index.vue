<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="协议名称" prop="protocolName">
        <el-input
          v-model="queryParams.protocolName"
          placeholder="请输入协议名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="协议类型" prop="protocolType">
        <el-select v-model="queryParams.protocolType" placeholder="请选择协议类型" clearable>
          <el-option
            v-for="dict in dict.type.outmistake_protocol_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="数据格式" prop="dataFormat">
        <el-select v-model="queryParams.dataFormat" placeholder="请选择数据格式" clearable>
          <el-option
            v-for="dict in dict.type.netty_data_format"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="协议状态" clearable>
          <el-option label="启用" value="1" />
          <el-option label="禁用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['outmis:protocol:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['outmis:protocol:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['outmis:protocol:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['outmis:protocol:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['outmis:protocol:add']"
        >导入</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="protocolList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="协议ID" align="center" prop="protocolId" />
      <el-table-column label="协议名称" align="center" prop="protocolName" />
      <el-table-column label="协议类型" align="center" prop="protocolType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.outmistake_protocol_type" :value="scope.row.protocolType"/>
        </template>
      </el-table-column>
      <el-table-column label="协议版本" align="center" prop="protocolVersion" />
      <el-table-column label="数据格式" align="center" prop="dataFormat">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.netty_data_format" :value="scope.row.dataFormat"/>
        </template>
      </el-table-column>
      <el-table-column label="服务器端口" align="center" prop="serverPort" />
      <el-table-column label="认证类型" align="center" prop="authType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.netty_auth_type" :value="scope.row.authType"/>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="服务器状态" align="center" width="120">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.serverRunning" type="success">运行中</el-tag>
          <el-tag v-else type="danger">已停止</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['outmis:protocol:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['outmis:protocol:remove']"
          >删除</el-button>
          <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)" v-hasPermi="['outmis:protocol:test']">
            <el-button size="mini" type="text">
              更多<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="test" icon="el-icon-cpu">测试协议</el-dropdown-item>
              <el-dropdown-item command="start" icon="el-icon-video-play" v-if="!scope.row.serverRunning">启动服务器</el-dropdown-item>
              <el-dropdown-item command="stop" icon="el-icon-video-pause" v-if="scope.row.serverRunning">停止服务器</el-dropdown-item>
              <el-dropdown-item command="restart" icon="el-icon-refresh">重启服务器</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改出库防错协议管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="协议名称" prop="protocolName">
              <el-input v-model="form.protocolName" placeholder="请输入协议名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="协议类型" prop="protocolType">
              <el-select v-model="form.protocolType" placeholder="请选择协议类型">
                <el-option
                  v-for="dict in dict.type.outmistake_protocol_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="协议版本" prop="protocolVersion">
              <el-input v-model="form.protocolVersion" placeholder="请输入协议版本" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据格式" prop="dataFormat">
              <el-select v-model="form.dataFormat" placeholder="请选择数据格式">
                <el-option
                  v-for="dict in dict.type.netty_data_format"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="服务器端口" prop="serverPort">
              <el-input-number v-model="form.serverPort" :min="1" :max="65535" placeholder="请输入服务器端口" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="认证类型" prop="authType">
              <el-select v-model="form.authType" placeholder="请选择认证类型">
                <el-option
                  v-for="dict in dict.type.netty_auth_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="超时时间(秒)" prop="timeoutSeconds">
              <el-input-number v-model="form.timeoutSeconds" :min="1" :max="3600" placeholder="超时时间" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="心跳间隔(秒)" prop="heartbeatInterval">
              <el-input-number v-model="form.heartbeatInterval" :min="1" :max="3600" placeholder="心跳间隔" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最大连接数" prop="maxConnections">
              <el-input-number v-model="form.maxConnections" :min="1" :max="10000" placeholder="最大连接数" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="消息头格式" prop="headerFormat">
          <el-input v-model="form.headerFormat" type="textarea" placeholder="请输入消息头格式" />
        </el-form-item>
        <el-form-item label="消息体格式" prop="bodyFormat">
          <el-input v-model="form.bodyFormat" type="textarea" placeholder="请输入消息体格式" />
        </el-form-item>
        <el-form-item label="解析脚本" prop="parseScript">
          <el-input v-model="form.parseScript" type="textarea" :rows="6" placeholder="请输入JavaScript解析脚本" />
        </el-form-item>
        <el-form-item label="编码脚本" prop="encodeScript">
          <el-input v-model="form.encodeScript" type="textarea" :rows="6" placeholder="请输入JavaScript编码脚本" />
        </el-form-item>
        <el-form-item label="协议描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入协议描述" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 协议测试对话框 -->
    <el-dialog title="协议测试" :visible.sync="testOpen" width="600px" append-to-body>
      <el-tabs v-model="testActiveTab">
        <el-tab-pane label="解析测试" name="parse">
          <el-form label-width="100px">
            <el-form-item label="测试数据">
              <el-input v-model="testData.parseInput" type="textarea" :rows="4" placeholder="请输入要解析的测试数据" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleTestParse">测试解析</el-button>
            </el-form-item>
            <el-form-item label="解析结果" v-if="testData.parseResult">
              <el-input v-model="testData.parseResult" type="textarea" :rows="6" readonly />
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="编码测试" name="encode">
          <el-form label-width="100px">
            <el-form-item label="测试数据">
              <el-input v-model="testData.encodeInput" type="textarea" :rows="4" placeholder="请输入要编码的测试数据" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleTestEncode">测试编码</el-button>
            </el-form-item>
            <el-form-item label="编码结果" v-if="testData.encodeResult">
              <el-input v-model="testData.encodeResult" type="textarea" :rows="6" readonly />
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的协议数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listProtocol, getProtocol, delProtocol, addProtocol, updateProtocol, updateProtocolStatus, testProtocolParse, testProtocolEncode, startProtocolServer, stopProtocolServer, restartProtocolServer } from "@/api/outmis/protocol";
import { getToken } from "@/utils/auth";

export default {
  name: "Protocol",
  dicts: ['outmistake_protocol_type', 'netty_data_format', 'netty_auth_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 出库防错协议管理表格数据
      protocolList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 测试对话框
      testOpen: false,
      // 测试活动标签
      testActiveTab: 'parse',
      // 测试数据
      testData: {
        protocolId: null,
        parseInput: '',
        parseResult: '',
        encodeInput: '',
        encodeResult: ''
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        protocolName: null,
        protocolType: null,
        dataFormat: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        protocolName: [
          { required: true, message: "协议名称不能为空", trigger: "blur" }
        ],
        protocolType: [
          { required: true, message: "协议类型不能为空", trigger: "change" }
        ],
        dataFormat: [
          { required: true, message: "数据格式不能为空", trigger: "change" }
        ],
        serverPort: [
          { required: true, message: "服务器端口不能为空", trigger: "blur" }
        ]
      },
      // 上传参数
      upload: {
        // 是否显示弹出层（导入）
        open: false,
        // 弹出层标题（导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的协议数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/outmis/protocol/importData"
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询出库防错协议管理列表 */
    getList() {
      this.loading = true;
      listProtocol(this.queryParams).then(response => {
        this.protocolList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        protocolId: null,
        protocolName: null,
        protocolType: null,
        protocolVersion: "1.0",
        dataFormat: null,
        parseScript: null,
        encodeScript: null,
        headerFormat: null,
        bodyFormat: null,
        checksumType: null,
        serverPort: null,
        authType: "none",
        authConfig: null,
        timeoutSeconds: 30,
        heartbeatInterval: 60,
        maxConnections: 1000,
        status: 1,
        description: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.protocolId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加出库防错协议管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const protocolId = row.protocolId || this.ids
      getProtocol(protocolId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改出库防错协议管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.protocolId != null) {
            updateProtocol(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addProtocol(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const protocolIds = row.protocolId || this.ids;
      this.$modal.confirm('是否确认删除出库防错协议管理编号为"' + protocolIds + '"的数据项？').then(function() {
        return delProtocol(protocolIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('outmis/protocol/export', {
        ...this.queryParams
      }, `protocol_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "协议导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('outmis/protocol/importTemplate', {}, `protocol_template_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /** 状态修改 */
    handleStatusChange(row) {
      let text = row.status === 1 ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '""' + row.protocolName + '"协议吗？').then(function() {
        return updateProtocolStatus(row.protocolId, row.status);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function() {
        row.status = row.status === 0 ? 1 : 0;
      });
    },
    /** 更多操作 */
    handleCommand(command, row) {
      switch (command) {
        case "test":
          this.handleTest(row);
          break;
        case "start":
          this.handleStartServer(row);
          break;
        case "stop":
          this.handleStopServer(row);
          break;
        case "restart":
          this.handleRestartServer(row);
          break;
      }
    },
    /** 测试协议 */
    handleTest(row) {
      this.testData.protocolId = row.protocolId;
      this.testData.parseInput = '';
      this.testData.parseResult = '';
      this.testData.encodeInput = '';
      this.testData.encodeResult = '';
      this.testActiveTab = 'parse';
      this.testOpen = true;
    },
    /** 测试解析 */
    handleTestParse() {
      if (!this.testData.parseInput) {
        this.$modal.msgError("请输入测试数据");
        return;
      }
      testProtocolParse(this.testData.protocolId, this.testData.parseInput).then(response => {
        if (response.data.success) {
          this.testData.parseResult = JSON.stringify(response.data.data, null, 2);
          this.$modal.msgSuccess("解析测试成功");
        } else {
          this.testData.parseResult = response.data.message;
          this.$modal.msgError("解析测试失败");
        }
      });
    },
    /** 测试编码 */
    handleTestEncode() {
      if (!this.testData.encodeInput) {
        this.$modal.msgError("请输入测试数据");
        return;
      }
      testProtocolEncode(this.testData.protocolId, this.testData.encodeInput).then(response => {
        if (response.data.success) {
          this.testData.encodeResult = JSON.stringify(response.data.data, null, 2);
          this.$modal.msgSuccess("编码测试成功");
        } else {
          this.testData.encodeResult = response.data.message;
          this.$modal.msgError("编码测试失败");
        }
      });
    },
    /** 启动服务器 */
    handleStartServer(row) {
      this.$modal.confirm('确认要启动"' + row.protocolName + '"协议服务器吗？').then(function() {
        return startProtocolServer(row.protocolId);
      }).then(() => {
        this.$modal.msgSuccess("服务器启动成功");
        this.getList();
      }).catch(() => {});
    },
    /** 停止服务器 */
    handleStopServer(row) {
      this.$modal.confirm('确认要停止"' + row.protocolName + '"协议服务器吗？').then(function() {
        return stopProtocolServer(row.protocolId);
      }).then(() => {
        this.$modal.msgSuccess("服务器停止成功");
        this.getList();
      }).catch(() => {});
    },
    /** 重启服务器 */
    handleRestartServer(row) {
      this.$modal.confirm('确认要重启"' + row.protocolName + '"协议服务器吗？').then(function() {
        return restartProtocolServer(row.protocolId);
      }).then(() => {
        this.$modal.msgSuccess("服务器重启成功");
        this.getList();
      }).catch(() => {});
    }
  }
};
</script>
