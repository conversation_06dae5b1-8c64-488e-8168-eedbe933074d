// 图表组件统一导出

import BaseChart from './BaseChart.vue'
import RealtimeChart from './RealtimeChart.vue'
import Chart3D from './Chart3D.vue'

// 图表工具函数
export const ChartUtils = {
  /**
   * 生成颜色数组
   * @param {number} count 颜色数量
   * @param {string} type 颜色类型: 'gradient', 'random', 'theme'
   * @returns {Array} 颜色数组
   */
  generateColors(count, type = 'theme') {
    const themeColors = [
      '#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399',
      '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
      '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#d87a80'
    ]
    
    const gradientColors = [
      '#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8',
      '#ffffcc', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026'
    ]
    
    switch (type) {
      case 'gradient':
        return this.interpolateColors(gradientColors, count)
      case 'random':
        return Array.from({ length: count }, () => this.randomColor())
      case 'theme':
      default:
        return this.repeatColors(themeColors, count)
    }
  },
  
  /**
   * 颜色插值
   * @param {Array} colors 基础颜色数组
   * @param {number} count 目标数量
   * @returns {Array} 插值后的颜色数组
   */
  interpolateColors(colors, count) {
    if (count <= colors.length) {
      return colors.slice(0, count)
    }
    
    const result = []
    const step = (colors.length - 1) / (count - 1)
    
    for (let i = 0; i < count; i++) {
      const index = i * step
      const lowerIndex = Math.floor(index)
      const upperIndex = Math.ceil(index)
      const ratio = index - lowerIndex
      
      if (lowerIndex === upperIndex) {
        result.push(colors[lowerIndex])
      } else {
        result.push(this.blendColors(colors[lowerIndex], colors[upperIndex], ratio))
      }
    }
    
    return result
  },
  
  /**
   * 重复颜色数组
   * @param {Array} colors 基础颜色数组
   * @param {number} count 目标数量
   * @returns {Array} 重复后的颜色数组
   */
  repeatColors(colors, count) {
    const result = []
    for (let i = 0; i < count; i++) {
      result.push(colors[i % colors.length])
    }
    return result
  },
  
  /**
   * 生成随机颜色
   * @returns {string} 十六进制颜色值
   */
  randomColor() {
    return '#' + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0')
  },
  
  /**
   * 混合两个颜色
   * @param {string} color1 颜色1
   * @param {string} color2 颜色2
   * @param {number} ratio 混合比例 (0-1)
   * @returns {string} 混合后的颜色
   */
  blendColors(color1, color2, ratio) {
    const hex1 = color1.replace('#', '')
    const hex2 = color2.replace('#', '')
    
    const r1 = parseInt(hex1.substr(0, 2), 16)
    const g1 = parseInt(hex1.substr(2, 2), 16)
    const b1 = parseInt(hex1.substr(4, 2), 16)
    
    const r2 = parseInt(hex2.substr(0, 2), 16)
    const g2 = parseInt(hex2.substr(2, 2), 16)
    const b2 = parseInt(hex2.substr(4, 2), 16)
    
    const r = Math.round(r1 + (r2 - r1) * ratio)
    const g = Math.round(g1 + (g2 - g1) * ratio)
    const b = Math.round(b1 + (b2 - b1) * ratio)
    
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`
  },
  
  /**
   * 格式化数值
   * @param {number} value 数值
   * @param {string} type 格式类型: 'number', 'percent', 'currency', 'bytes'
   * @param {number} decimals 小数位数
   * @returns {string} 格式化后的字符串
   */
  formatValue(value, type = 'number', decimals = 2) {
    if (typeof value !== 'number' || isNaN(value)) {
      return '-'
    }
    
    switch (type) {
      case 'percent':
        return (value * 100).toFixed(decimals) + '%'
      case 'currency':
        return '¥' + value.toLocaleString('zh-CN', { minimumFractionDigits: decimals })
      case 'bytes':
        return this.formatBytes(value)
      case 'number':
      default:
        return value.toLocaleString('zh-CN', { minimumFractionDigits: decimals })
    }
  },
  
  /**
   * 格式化字节数
   * @param {number} bytes 字节数
   * @returns {string} 格式化后的字符串
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },
  
  /**
   * 生成时间序列
   * @param {Date} start 开始时间
   * @param {Date} end 结束时间
   * @param {string} interval 间隔: 'minute', 'hour', 'day', 'month'
   * @returns {Array} 时间数组
   */
  generateTimeRange(start, end, interval = 'hour') {
    const times = []
    const current = new Date(start)
    
    const intervalMs = {
      minute: 60 * 1000,
      hour: 60 * 60 * 1000,
      day: 24 * 60 * 60 * 1000,
      month: 30 * 24 * 60 * 60 * 1000
    }
    
    const step = intervalMs[interval] || intervalMs.hour
    
    while (current <= end) {
      times.push(new Date(current))
      current.setTime(current.getTime() + step)
    }
    
    return times
  },
  
  /**
   * 数据聚合
   * @param {Array} data 原始数据
   * @param {string} groupBy 分组字段
   * @param {string} aggregateBy 聚合字段
   * @param {string} method 聚合方法: 'sum', 'avg', 'max', 'min', 'count'
   * @returns {Array} 聚合后的数据
   */
  aggregateData(data, groupBy, aggregateBy, method = 'sum') {
    const groups = {}
    
    data.forEach(item => {
      const key = item[groupBy]
      if (!groups[key]) {
        groups[key] = []
      }
      groups[key].push(item[aggregateBy])
    })
    
    const result = []
    for (const [key, values] of Object.entries(groups)) {
      let aggregatedValue
      
      switch (method) {
        case 'sum':
          aggregatedValue = values.reduce((sum, val) => sum + val, 0)
          break
        case 'avg':
          aggregatedValue = values.reduce((sum, val) => sum + val, 0) / values.length
          break
        case 'max':
          aggregatedValue = Math.max(...values)
          break
        case 'min':
          aggregatedValue = Math.min(...values)
          break
        case 'count':
          aggregatedValue = values.length
          break
        default:
          aggregatedValue = values.reduce((sum, val) => sum + val, 0)
      }
      
      result.push({
        [groupBy]: key,
        [aggregateBy]: aggregatedValue
      })
    }
    
    return result
  },
  
  /**
   * 数据平滑处理
   * @param {Array} data 原始数据
   * @param {number} windowSize 窗口大小
   * @returns {Array} 平滑后的数据
   */
  smoothData(data, windowSize = 3) {
    if (data.length < windowSize) {
      return [...data]
    }
    
    const smoothed = []
    const halfWindow = Math.floor(windowSize / 2)
    
    for (let i = 0; i < data.length; i++) {
      const start = Math.max(0, i - halfWindow)
      const end = Math.min(data.length - 1, i + halfWindow)
      
      let sum = 0
      let count = 0
      
      for (let j = start; j <= end; j++) {
        sum += data[j]
        count++
      }
      
      smoothed.push(sum / count)
    }
    
    return smoothed
  }
}

// 图表配置预设
export const ChartPresets = {
  // 线图预设
  line: {
    basic: {
      tooltip: { trigger: 'axis' },
      legend: { data: [] },
      xAxis: { type: 'category', data: [] },
      yAxis: { type: 'value' },
      series: []
    },
    smooth: {
      tooltip: { trigger: 'axis' },
      legend: { data: [] },
      xAxis: { type: 'category', data: [] },
      yAxis: { type: 'value' },
      series: []
    }
  },
  
  // 柱状图预设
  bar: {
    basic: {
      tooltip: { trigger: 'axis' },
      legend: { data: [] },
      xAxis: { type: 'category', data: [] },
      yAxis: { type: 'value' },
      series: []
    },
    stacked: {
      tooltip: { trigger: 'axis' },
      legend: { data: [] },
      xAxis: { type: 'category', data: [] },
      yAxis: { type: 'value' },
      series: []
    }
  },
  
  // 饼图预设
  pie: {
    basic: {
      tooltip: { trigger: 'item' },
      legend: { orient: 'vertical', left: 'left' },
      series: [{
        type: 'pie',
        radius: '50%',
        data: [],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    },
    donut: {
      tooltip: { trigger: 'item' },
      legend: { orient: 'vertical', left: 'left' },
      series: [{
        type: 'pie',
        radius: ['40%', '70%'],
        data: [],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    }
  },
  
  // 散点图预设
  scatter: {
    basic: {
      tooltip: { trigger: 'item' },
      xAxis: { type: 'value' },
      yAxis: { type: 'value' },
      series: [{
        type: 'scatter',
        data: []
      }]
    }
  }
}

// 全局安装函数
export function install(Vue) {
  Vue.component('BaseChart', BaseChart)
  Vue.component('RealtimeChart', RealtimeChart)
  Vue.component('Chart3D', Chart3D)
  
  // 添加全局属性
  Vue.prototype.$chartUtils = ChartUtils
  Vue.prototype.$chartPresets = ChartPresets
}

// 导出组件
export {
  BaseChart,
  RealtimeChart,
  Chart3D
}

// 默认导出
export default {
  install,
  BaseChart,
  RealtimeChart,
  Chart3D,
  ChartUtils,
  ChartPresets
}
