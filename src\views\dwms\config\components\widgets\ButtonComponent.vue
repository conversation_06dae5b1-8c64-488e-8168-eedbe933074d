<template>
  <div class="button-component" :style="{ width: width + 'px', height: height + 'px' }">
    <!-- 普通按钮 -->
    <el-button
      v-if="config.type === 'button'"
      :type="config.buttonType || 'primary'"
      :size="config.size || 'medium'"
      :disabled="config.disabled || false"
      :loading="loading"
      :icon="config.icon"
      :style="buttonStyle"
      @click="handleClick"
    >
      {{ config.text || '按钮' }}
    </el-button>
    
    <!-- 开关按钮 -->
    <div v-else-if="config.type === 'switch'" class="switch-container">
      <div class="switch-title" v-if="config.title">{{ config.title }}</div>
      <el-switch
        v-model="switchValue"
        :active-text="config.onText || '开'"
        :inactive-text="config.offText || '关'"
        :active-color="config.onColor || '#13ce66'"
        :inactive-color="config.offColor || '#ff4949'"
        :disabled="config.disabled || false"
        @change="handleSwitchChange"
      ></el-switch>
    </div>
    
    <!-- 图标按钮 -->
    <el-button
      v-else-if="config.type === 'icon'"
      :type="config.buttonType || 'primary'"
      :size="config.size || 'medium'"
      :disabled="config.disabled || false"
      :loading="loading"
      :icon="config.icon || 'el-icon-setting'"
      :style="buttonStyle"
      circle
      @click="handleClick"
    ></el-button>
    
    <!-- 文字按钮 -->
    <el-button
      v-else-if="config.type === 'text'"
      type="text"
      :disabled="config.disabled || false"
      :loading="loading"
      :style="textButtonStyle"
      @click="handleClick"
    >
      {{ config.text || '文字按钮' }}
    </el-button>
    
    <!-- 下拉按钮 -->
    <el-dropdown v-else-if="config.type === 'dropdown'" @command="handleDropdownCommand">
      <el-button
        :type="config.buttonType || 'primary'"
        :size="config.size || 'medium'"
        :disabled="config.disabled || false"
        :loading="loading"
        :style="buttonStyle"
      >
        {{ config.text || '下拉按钮' }}
        <i class="el-icon-arrow-down el-icon--right"></i>
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item
          v-for="item in config.dropdownItems || []"
          :key="item.value"
          :command="item.value"
          :disabled="item.disabled"
        >
          <i v-if="item.icon" :class="item.icon" style="margin-right: 8px;"></i>
          {{ item.label }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    
    <!-- 按钮组 -->
    <el-button-group v-else-if="config.type === 'group'" :style="{ width: '100%', height: '100%' }">
      <el-button
        v-for="item in config.groupItems || []"
        :key="item.value"
        :type="item.type || 'default'"
        :size="config.size || 'medium'"
        :disabled="item.disabled || config.disabled"
        :icon="item.icon"
        @click="handleGroupClick(item)"
      >
        {{ item.label }}
      </el-button>
    </el-button-group>
    
    <!-- 默认按钮 -->
    <el-button
      v-else
      type="primary"
      :style="buttonStyle"
      @click="handleClick"
    >
      {{ config.text || '按钮' }}
    </el-button>
  </div>
</template>

<script>
export default {
  name: 'ButtonComponent',
  props: {
    config: {
      type: Object,
      default: () => ({})
    },
    data: {
      type: Object,
      default: () => ({})
    },
    width: {
      type: Number,
      default: 100
    },
    height: {
      type: Number,
      default: 40
    }
  },
  data() {
    return {
      loading: false,
      switchValue: false
    }
  },
  computed: {
    buttonStyle() {
      const style = {
        width: '100%',
        height: '100%'
      }
      
      if (this.config.color) {
        style.backgroundColor = this.config.color
        style.borderColor = this.config.color
      }
      
      if (this.config.fontSize) {
        style.fontSize = this.config.fontSize
      }
      
      return style
    },
    textButtonStyle() {
      const style = {
        width: '100%',
        height: '100%',
        padding: '0'
      }
      
      if (this.config.color) {
        style.color = this.config.color
      }
      
      if (this.config.fontSize) {
        style.fontSize = this.config.fontSize
      }
      
      return style
    }
  },
  mounted() {
    // 初始化开关状态
    if (this.config.type === 'switch') {
      this.switchValue = this.data?.value || this.config.defaultValue || false
    }
  },
  watch: {
    'data.value'(newVal) {
      if (this.config.type === 'switch') {
        this.switchValue = newVal
      }
    }
  },
  methods: {
    // 处理按钮点击
    async handleClick() {
      if (this.config.disabled || this.loading) return
      
      // 显示加载状态
      if (this.config.showLoading) {
        this.loading = true
      }
      
      try {
        // 触发动作事件
        this.$emit('action', {
          type: 'click',
          action: this.config.action,
          config: this.config,
          data: this.data
        })
        
        // 模拟异步操作
        if (this.config.showLoading) {
          await new Promise(resolve => setTimeout(resolve, 1000))
        }
      } catch (error) {
        console.error('按钮动作执行失败:', error)
        this.$message.error('操作失败')
      } finally {
        this.loading = false
      }
    },
    
    // 处理开关变化
    handleSwitchChange(value) {
      this.$emit('action', {
        type: 'switch',
        action: this.config.action,
        value: value,
        config: this.config,
        data: this.data
      })
    },
    
    // 处理下拉菜单命令
    handleDropdownCommand(command) {
      const item = (this.config.dropdownItems || []).find(item => item.value === command)
      
      this.$emit('action', {
        type: 'dropdown',
        action: this.config.action,
        command: command,
        item: item,
        config: this.config,
        data: this.data
      })
    },
    
    // 处理按钮组点击
    handleGroupClick(item) {
      this.$emit('action', {
        type: 'group',
        action: this.config.action,
        item: item,
        config: this.config,
        data: this.data
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.button-component {
  display: flex;
  align-items: center;
  justify-content: center;
  
  .switch-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    
    .switch-title {
      font-size: 12px;
      color: #666;
      margin-bottom: 8px;
      text-align: center;
    }
  }
}

// 覆盖Element UI样式
::v-deep .el-button {
  &.is-circle {
    padding: 12px;
  }
}

::v-deep .el-button-group {
  .el-button {
    flex: 1;
  }
}

::v-deep .el-switch {
  .el-switch__label {
    font-size: 12px;
  }
}
</style>
