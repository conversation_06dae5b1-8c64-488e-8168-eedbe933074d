<template>
  <div class="alert-management">
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
        <el-form-item label="设备ID" prop="deviceId">
          <el-input
            v-model="queryParams.deviceId"
            placeholder="请输入设备ID"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="告警类型" prop="alertType">
          <el-select v-model="queryParams.alertType" placeholder="请选择告警类型" clearable style="width: 150px">
            <el-option label="连接异常" value="CONNECTIVITY" />
            <el-option label="性能异常" value="PERFORMANCE" />
            <el-option label="健康异常" value="HEALTH" />
            <el-option label="资源异常" value="RESOURCE" />
            <el-option label="环境异常" value="ENVIRONMENT" />
          </el-select>
        </el-form-item>
        <el-form-item label="告警级别" prop="alertLevel">
          <el-select v-model="queryParams.alertLevel" placeholder="请选择告警级别" clearable style="width: 120px">
            <el-option label="严重" value="CRITICAL" />
            <el-option label="警告" value="WARNING" />
            <el-option label="信息" value="INFO" />
          </el-select>
        </el-form-item>
        <el-form-item label="告警状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 120px">
            <el-option label="活跃" value="ACTIVE" />
            <el-option label="已确认" value="ACKNOWLEDGED" />
            <el-option label="已解决" value="RESOLVED" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 300px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作区域 -->
    <el-card class="operation-card">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-check"
            size="mini"
            :disabled="multiple"
            @click="handleBatchAcknowledge"
          >批量确认</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-circle-check"
            size="mini"
            :disabled="multiple"
            @click="handleBatchResolve"
          >批量解决</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="info"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
          >导出</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-setting"
            size="mini"
            @click="handleRuleManagement"
          >告警规则</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <!-- 统计信息 -->
      <el-row :gutter="20" class="stats-row">
        <el-col :span="6">
          <div class="stat-item critical">
            <div class="stat-icon">
              <i class="el-icon-warning-outline"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ alertStats.criticalCount }}</div>
              <div class="stat-label">严重告警</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item warning">
            <div class="stat-icon">
              <i class="el-icon-warning"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ alertStats.warningCount }}</div>
              <div class="stat-label">警告告警</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item active">
            <div class="stat-icon">
              <i class="el-icon-circle-close"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ alertStats.activeCount }}</div>
              <div class="stat-label">活跃告警</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item resolved">
            <div class="stat-icon">
              <i class="el-icon-circle-check"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ alertStats.resolvedCount }}</div>
              <div class="stat-label">已解决</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 告警列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="alertList"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
        size="small"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="告警ID" prop="alertId" width="120" />
        <el-table-column label="设备ID" prop="deviceId" width="120" />
        <el-table-column label="告警类型" prop="alertType" width="120">
          <template slot-scope="scope">
            <el-tag :type="getAlertTypeColor(scope.row.alertType)" size="small">
              {{ getAlertTypeName(scope.row.alertType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="告警级别" prop="alertLevel" width="100">
          <template slot-scope="scope">
            <el-tag :type="getAlertLevelType(scope.row.alertLevel)" size="small">
              {{ scope.row.alertLevel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="告警消息" prop="message" min-width="200" show-overflow-tooltip />
        <el-table-column label="状态" prop="status" width="100">
          <template slot-scope="scope">
            <el-tag :type="getAlertStatusType(scope.row.status)" size="small">
              {{ getAlertStatusName(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="发生次数" prop="occurrenceCount" width="100" />
        <el-table-column label="发生时间" prop="createTime" width="150" />
        <el-table-column label="确认时间" prop="acknowledgeTime" width="150" />
        <el-table-column label="解决时间" prop="resolveTime" width="150" />
        <el-table-column label="处理人" prop="handler" width="100" />
        <el-table-column label="操作" width="200" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
            >详情</el-button>
            <el-button
              v-if="scope.row.status === 'ACTIVE'"
              size="mini"
              type="text"
              icon="el-icon-check"
              @click="handleAcknowledge(scope.row)"
            >确认</el-button>
            <el-button
              v-if="scope.row.status !== 'RESOLVED'"
              size="mini"
              type="text"
              icon="el-icon-circle-check"
              @click="handleResolve(scope.row)"
            >解决</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 告警详情对话框 -->
    <el-dialog title="告警详情" :visible.sync="detailVisible" width="800px" append-to-body>
      <el-descriptions :column="2" border v-if="currentAlert">
        <el-descriptions-item label="告警ID">{{ currentAlert.alertId }}</el-descriptions-item>
        <el-descriptions-item label="设备ID">{{ currentAlert.deviceId }}</el-descriptions-item>
        <el-descriptions-item label="告警类型">{{ getAlertTypeName(currentAlert.alertType) }}</el-descriptions-item>
        <el-descriptions-item label="告警级别">
          <el-tag :type="getAlertLevelType(currentAlert.alertLevel)" size="small">
            {{ currentAlert.alertLevel }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="告警消息" :span="2">{{ currentAlert.message }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getAlertStatusType(currentAlert.status)" size="small">
            {{ getAlertStatusName(currentAlert.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="发生次数">{{ currentAlert.occurrenceCount }}</el-descriptions-item>
        <el-descriptions-item label="发生时间">{{ currentAlert.createTime }}</el-descriptions-item>
        <el-descriptions-item label="最后发生时间">{{ currentAlert.lastOccurrenceTime }}</el-descriptions-item>
        <el-descriptions-item label="确认时间">{{ currentAlert.acknowledgeTime || '未确认' }}</el-descriptions-item>
        <el-descriptions-item label="解决时间">{{ currentAlert.resolveTime || '未解决' }}</el-descriptions-item>
        <el-descriptions-item label="处理人">{{ currentAlert.handler || '无' }}</el-descriptions-item>
        <el-descriptions-item label="处理备注">{{ currentAlert.handlerNote || '无' }}</el-descriptions-item>
        <el-descriptions-item label="告警详情" :span="2">
          <pre>{{ JSON.stringify(currentAlert.details, null, 2) }}</pre>
        </el-descriptions-item>
      </el-descriptions>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailVisible = false">关闭</el-button>
        <el-button
          v-if="currentAlert && currentAlert.status === 'ACTIVE'"
          type="warning"
          @click="handleAcknowledge(currentAlert)"
        >确认告警</el-button>
        <el-button
          v-if="currentAlert && currentAlert.status !== 'RESOLVED'"
          type="success"
          @click="handleResolve(currentAlert)"
        >解决告警</el-button>
      </div>
    </el-dialog>

    <!-- 处理告警对话框 -->
    <el-dialog :title="processTitle" :visible.sync="processVisible" width="500px" append-to-body>
      <el-form :model="processForm" :rules="processRules" ref="processForm" label-width="80px">
        <el-form-item label="处理备注" prop="note">
          <el-input
            v-model="processForm.note"
            type="textarea"
            :rows="4"
            placeholder="请输入处理备注"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="processVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmProcess">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getAlertList,
  getAlertDetail,
  acknowledgeAlert,
  resolveAlert,
  batchProcessAlerts,
  getAlertStatistics
} from '@/api/monitor/device'

export default {
  name: 'AlertList',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 告警表格数据
      alertList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceId: null,
        alertType: null,
        alertLevel: null,
        status: null
      },
      // 告警统计
      alertStats: {
        criticalCount: 0,
        warningCount: 0,
        activeCount: 0,
        resolvedCount: 0
      },
      // 详情对话框
      detailVisible: false,
      currentAlert: null,
      // 处理对话框
      processVisible: false,
      processTitle: '',
      processType: '',
      processForm: {
        note: ''
      },
      processRules: {
        note: [
          { required: true, message: "处理备注不能为空", trigger: "blur" }
        ]
      }
    }
  },
  created() {
    this.getList()
    this.getAlertStats()
  },
  methods: {
    /** 查询告警列表 */
    getList() {
      this.loading = true
      this.addDateRange(this.queryParams, this.dateRange)
      getAlertList(this.queryParams).then(response => {
        this.alertList = response.rows
        this.total = response.total
        this.loading = false
      }).catch(() => {
        // 使用模拟数据
        this.alertList = [
          {
            alertId: 'ALT001',
            deviceId: 'DEV001',
            alertType: 'CONNECTIVITY',
            alertLevel: 'CRITICAL',
            message: '设备离线',
            status: 'ACTIVE',
            occurrenceCount: 1,
            createTime: '2025-01-27 10:15:00',
            acknowledgeTime: null,
            resolveTime: null,
            handler: null,
            handlerNote: null,
            details: { reason: '网络连接超时' }
          },
          {
            alertId: 'ALT002',
            deviceId: 'DEV002',
            alertType: 'PERFORMANCE',
            alertLevel: 'WARNING',
            message: 'CPU使用率过高',
            status: 'ACKNOWLEDGED',
            occurrenceCount: 3,
            createTime: '2025-01-27 09:45:00',
            acknowledgeTime: '2025-01-27 10:00:00',
            resolveTime: null,
            handler: 'admin',
            handlerNote: '正在处理中',
            details: { cpuUsage: 95.2 }
          }
        ]
        this.total = this.alertList.length
        this.loading = false
      })
    },
    
    /** 获取告警统计 */
    getAlertStats() {
      getAlertStatistics().then(response => {
        this.alertStats = response.data
      }).catch(() => {
        // 使用模拟数据
        this.alertStats = {
          criticalCount: 5,
          warningCount: 12,
          activeCount: 8,
          resolvedCount: 45
        }
      })
    },
    
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm("queryForm")
      this.handleQuery()
    },
    
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.alertId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    
    /** 行点击事件 */
    handleRowClick(row) {
      this.handleView(row)
    },
    
    /** 查看详情 */
    async handleView(row) {
      try {
        const response = await getAlertDetail(row.alertId)
        this.currentAlert = response.data
      } catch (error) {
        this.currentAlert = row
      }
      this.detailVisible = true
    },
    
    /** 确认告警 */
    handleAcknowledge(row) {
      this.processTitle = '确认告警'
      this.processType = 'acknowledge'
      this.currentAlert = row
      this.processForm.note = ''
      this.processVisible = true
    },
    
    /** 解决告警 */
    handleResolve(row) {
      this.processTitle = '解决告警'
      this.processType = 'resolve'
      this.currentAlert = row
      this.processForm.note = ''
      this.processVisible = true
    },
    
    /** 确认处理 */
    confirmProcess() {
      this.$refs.processForm.validate(valid => {
        if (valid) {
          const data = {
            note: this.processForm.note
          }
          
          const apiCall = this.processType === 'acknowledge' 
            ? acknowledgeAlert(this.currentAlert.alertId, data)
            : resolveAlert(this.currentAlert.alertId, data)
          
          apiCall.then(() => {
            this.$message.success(this.processType === 'acknowledge' ? '告警确认成功' : '告警解决成功')
            this.processVisible = false
            this.detailVisible = false
            this.getList()
            this.getAlertStats()
          }).catch(error => {
            this.$message.error('操作失败: ' + error.message)
          })
        }
      })
    },
    
    /** 批量确认 */
    handleBatchAcknowledge() {
      if (this.ids.length === 0) {
        this.$message.warning('请选择要确认的告警')
        return
      }
      
      this.$prompt('请输入确认备注', '批量确认告警', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /.+/,
        inputErrorMessage: '备注不能为空'
      }).then(({ value }) => {
        const data = {
          alertIds: this.ids,
          action: 'acknowledge',
          note: value
        }
        
        batchProcessAlerts(data).then(() => {
          this.$message.success('批量确认成功')
          this.getList()
          this.getAlertStats()
        }).catch(error => {
          this.$message.error('批量确认失败: ' + error.message)
        })
      })
    },
    
    /** 批量解决 */
    handleBatchResolve() {
      if (this.ids.length === 0) {
        this.$message.warning('请选择要解决的告警')
        return
      }
      
      this.$prompt('请输入解决备注', '批量解决告警', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /.+/,
        inputErrorMessage: '备注不能为空'
      }).then(({ value }) => {
        const data = {
          alertIds: this.ids,
          action: 'resolve',
          note: value
        }
        
        batchProcessAlerts(data).then(() => {
          this.$message.success('批量解决成功')
          this.getList()
          this.getAlertStats()
        }).catch(error => {
          this.$message.error('批量解决失败: ' + error.message)
        })
      })
    },
    
    /** 导出 */
    handleExport() {
      this.$message.info('导出功能开发中...')
    },
    
    /** 告警规则管理 */
    handleRuleManagement() {
      this.$router.push('/monitor/alert/rules')
    },
    
    /** 获取告警类型名称 */
    getAlertTypeName(type) {
      const typeMap = {
        'CONNECTIVITY': '连接异常',
        'PERFORMANCE': '性能异常',
        'HEALTH': '健康异常',
        'RESOURCE': '资源异常',
        'ENVIRONMENT': '环境异常'
      }
      return typeMap[type] || type
    },
    
    /** 获取告警类型颜色 */
    getAlertTypeColor(type) {
      const colorMap = {
        'CONNECTIVITY': 'danger',
        'PERFORMANCE': 'warning',
        'HEALTH': 'info',
        'RESOURCE': 'warning',
        'ENVIRONMENT': 'info'
      }
      return colorMap[type] || 'info'
    },
    
    /** 获取告警级别类型 */
    getAlertLevelType(level) {
      const typeMap = {
        'CRITICAL': 'danger',
        'WARNING': 'warning',
        'INFO': 'info'
      }
      return typeMap[level] || 'info'
    },
    
    /** 获取告警状态类型 */
    getAlertStatusType(status) {
      const typeMap = {
        'ACTIVE': 'danger',
        'ACKNOWLEDGED': 'warning',
        'RESOLVED': 'success'
      }
      return typeMap[status] || 'info'
    },
    
    /** 获取告警状态名称 */
    getAlertStatusName(status) {
      const nameMap = {
        'ACTIVE': '活跃',
        'ACKNOWLEDGED': '已确认',
        'RESOLVED': '已解决'
      }
      return nameMap[status] || status
    }
  }
}
</script>

<style scoped>
.alert-management {
  padding: 20px;
}

.search-card, .operation-card, .table-card {
  margin-bottom: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  color: white;
}

.stat-item.critical {
  background: linear-gradient(135deg, #F56C6C, #F78989);
}

.stat-item.warning {
  background: linear-gradient(135deg, #E6A23C, #EEBE77);
}

.stat-item.active {
  background: linear-gradient(135deg, #409EFF, #66B1FF);
}

.stat-item.resolved {
  background: linear-gradient(135deg, #67C23A, #85CE61);
}

.stat-icon {
  font-size: 24px;
  margin-right: 15px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
  margin-top: 5px;
}

.mb8 {
  margin-bottom: 8px;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}
</style>
