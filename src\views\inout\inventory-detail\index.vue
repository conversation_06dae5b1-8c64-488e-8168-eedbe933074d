<template>
  <div class="app-container">
    <!-- 盘点任务信息 -->
    <el-card class="inventory-info-card" shadow="hover" v-if="inventoryInfo">
      <div class="inventory-header">
        <div class="inventory-title">
          <h3>{{ inventoryInfo.inventoryNo }} - 盘点明细</h3>
          <el-tag :type="getInventoryStatusType(inventoryInfo.status)" size="medium">
            {{ getInventoryStatusText(inventoryInfo.status) }}
          </el-tag>
        </div>
        <div class="inventory-actions">
          <el-button size="small" icon="el-icon-back" @click="goBack">返回列表</el-button>
          <el-button size="small" type="primary" icon="el-icon-refresh" @click="refreshData">刷新数据</el-button>
        </div>
      </div>
      <el-row :gutter="20" class="inventory-stats">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-label">盘点类型</div>
            <div class="stat-value">{{ getInventoryTypeText(inventoryInfo.inventoryType) }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-label">盘点范围</div>
            <div class="stat-value">{{ getInventoryScope(inventoryInfo) }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-label">盘点进度</div>
            <div class="stat-value">
              <el-progress :percentage="inventoryProgress" :status="getProgressStatus()" :stroke-width="8"></el-progress>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-label">差异物料</div>
            <div class="stat-value" style="color: #F56C6C;">{{ differenceCount }} 项</div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 查询条件 -->
    <el-card class="filter-container" shadow="hover">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">
        <el-row>
          <el-col :span="6">
            <el-form-item label="物料编码" prop="materialCode">
              <el-input
                v-model="queryParams.materialCode"
                placeholder="请输入物料编码"
                clearable
                prefix-icon="el-icon-goods"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="物料名称" prop="materialName">
              <el-input
                v-model="queryParams.materialName"
                placeholder="请输入物料名称"
                clearable
                prefix-icon="el-icon-collection-tag"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="批次号" prop="batchNo">
              <el-input
                v-model="queryParams.batchNo"
                placeholder="请输入批次号"
                clearable
                prefix-icon="el-icon-postcard"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="盘点状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 100%">
                <el-option label="待盘点" value="0" />
                <el-option label="已盘点" value="1" />
                <el-option label="有差异" value="2" />
                <el-option label="已确认" value="3" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="存储位置" prop="location">
              <el-input
                v-model="queryParams.location"
                placeholder="请输入存储位置"
                clearable
                prefix-icon="el-icon-location"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="差异类型" prop="diffType">
              <el-select v-model="queryParams.diffType" placeholder="请选择差异类型" clearable style="width: 100%">
                <el-option label="无差异" value="0" />
                <el-option label="盘盈" value="1" />
                <el-option label="盘亏" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" style="text-align: right;">
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              <el-button type="success" icon="el-icon-magic-stick" size="mini" @click="handleAutoGenerate">自动生成明细</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 操作工具栏和表格 -->
    <el-card class="table-container" shadow="hover" style="margin-top: 10px;">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['inout:inventory-detail:add']"
          >新增明细</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            icon="el-icon-magic-stick"
            size="mini"
            @click="handleBatchInput"
            v-hasPermi="['inout:inventory-detail:add']"
          >批量录入</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="info"
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['inout:inventory-detail:edit']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            icon="el-icon-check"
            size="mini"
            :disabled="multiple"
            @click="handleBatchConfirm"
            v-hasPermi="['inout:inventory-detail:edit']"
          >批量确认</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['inout:inventory-detail:remove']"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['inout:inventory-detail:export']"
          >导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="inventoryDetailList" @selection-change="handleSelectionChange" border stripe highlight-current-row>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" type="index" width="60" align="center" />
        <el-table-column label="物料信息" align="center" min-width="200" show-overflow-tooltip>
          <template slot-scope="scope">
            <div class="material-info">
              <div class="material-code">
                <i class="el-icon-goods"></i>
                <span style="font-weight: bold; color: #409EFF;">{{ scope.row.materialCode || 'M' + scope.row.materialId }}</span>
              </div>
              <div class="material-name">{{ scope.row.materialName || '物料名称' + scope.row.materialId }}</div>
              <div class="material-spec" v-if="scope.row.specification">{{ scope.row.specification }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="存储位置" align="center" min-width="160">
          <template slot-scope="scope">
            <div class="location-info">
              <div class="location-item">
                <i class="el-icon-office-building"></i>
                <span>{{ getWarehouseName(scope.row.warehouseId) || '仓库' + scope.row.warehouseId }}</span>
              </div>
              <div class="location-item" v-if="scope.row.zoneId">
                <i class="el-icon-map-location"></i>
                <span>{{ getZoneName(scope.row.zoneId) || '区域' + scope.row.zoneId }}</span>
              </div>
              <div class="location-item" v-if="scope.row.rackId">
                <i class="el-icon-box"></i>
                <span>{{ getRackName(scope.row.rackId) || '货架' + scope.row.rackId }}</span>
              </div>
              <div class="location-item" v-if="scope.row.locationId">
                <i class="el-icon-location"></i>
                <span>{{ scope.row.locationId }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="批次号" align="center" prop="batchNo" width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tag v-if="scope.row.batchNo" type="info" size="mini">{{ scope.row.batchNo }}</el-tag>
            <span v-else style="color: #C0C4CC;">无批次</span>
          </template>
        </el-table-column>
        <el-table-column label="数量对比" align="center" min-width="180">
          <template slot-scope="scope">
            <div class="quantity-compare">
              <div class="quantity-item system">
                <span class="quantity-label">账面:</span>
                <span class="quantity-value">{{ scope.row.system_quantity || 0 }}</span>
                <span class="quantity-unit">{{ scope.row.unit || '个' }}</span>
              </div>
              <div class="quantity-item actual">
                <span class="quantity-label">实际:</span>
                <span class="quantity-value">{{ scope.row.actual_quantity || 0 }}</span>
                <span class="quantity-unit">{{ scope.row.unit || '个' }}</span>
              </div>
              <div class="quantity-item difference" :class="getDifferenceClass(scope.row)">
                <span class="quantity-label">差异:</span>
                <span class="quantity-value">{{ getDifferenceQuantity(scope.row) }}</span>
                <span class="quantity-unit">{{ scope.row.unit || '个' }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="盘点状态" align="center" prop="status" width="100">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status == '0'" type="info" size="mini">待盘点</el-tag>
            <el-tag v-else-if="scope.row.status == '1'" type="success" size="mini">已盘点</el-tag>
            <el-tag v-else-if="scope.row.status == '2'" type="warning" size="mini">有差异</el-tag>
            <el-tag v-else-if="scope.row.status == '3'" type="primary" size="mini">已确认</el-tag>
            <el-tag v-else size="mini">{{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作人员" align="center" width="120">
          <template slot-scope="scope">
            <div class="operator-info">
              <div v-if="scope.row.operatorId">
                <i class="el-icon-user"></i>
                <span>{{ getOperatorName(scope.row.operatorId) }}</span>
              </div>
              <div v-if="scope.row.operation_time" class="operation-time">
                {{ parseTime(scope.row.operation_time, '{m}-{d} {h}:{i}') }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleQuickInput(scope.row)"
              v-hasPermi="['inout:inventory-detail:edit']"
              style="color: #409EFF;"
              v-if="scope.row.status == '0'"
            >快速录入</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
              v-hasPermi="['inout:inventory-detail:query']"
              style="color: #67C23A;"
            >详情</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['inout:inventory-detail:edit']"
              style="color: #E6A23C;"
              v-if="scope.row.status != '3'"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-check"
              @click="handleConfirm(scope.row)"
              v-hasPermi="['inout:inventory-detail:edit']"
              style="color: #67C23A;"
              v-if="scope.row.status == '2'"
            >确认</el-button>
            <el-dropdown @command="(command) => handleMoreAction(command, scope.row)">
              <el-button size="mini" type="text" style="color: #909399;">
                更多<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="copy" icon="el-icon-copy-document">复制</el-dropdown-item>
                <el-dropdown-item command="reset" icon="el-icon-refresh-left" v-if="scope.row.status != '0'">重置</el-dropdown-item>
                <el-dropdown-item command="history" icon="el-icon-time">历史记录</el-dropdown-item>
                <el-dropdown-item command="delete" icon="el-icon-delete" style="color: #F56C6C;">删除</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改盘点明细对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="650px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="盘点单据ID" prop="inventoryId">
              <el-input v-model="form.inventoryId" placeholder="请输入盘点单据ID" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物料ID" prop="materialId">
              <el-input v-model="form.materialId" placeholder="请输入物料ID" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="账面数量" prop="quantity">
              <el-input-number v-model="form.quantity" :precision="2" :step="1" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位" prop="unit">
              <el-input v-model="form.unit" placeholder="请输入单位" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="仓库ID" prop="warehouseId">
              <el-input v-model="form.warehouseId" placeholder="请输入仓库ID" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="区域ID" prop="zoneId">
              <el-input v-model="form.zoneId" placeholder="请输入区域ID" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="货架ID" prop="rackId">
              <el-input v-model="form.rackId" placeholder="请输入货架ID" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="货位ID" prop="locationId">
              <el-input v-model="form.locationId" placeholder="请输入货位ID" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="容器ID" prop="containerId">
              <el-input v-model="form.containerId" placeholder="请输入容器ID" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="批次号" prop="batchNo">
              <el-input v-model="form.batchNo" placeholder="请输入批次号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="实际数量" prop="actualQuantity">
              <el-input-number v-model="form.actualQuantity" :precision="2" :step="1" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
                <el-option label="正常" value="0" />
                <el-option label="异常" value="1" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" icon="el-icon-check" @click="submitForm">确 定</el-button>
        <el-button icon="el-icon-close" @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listInventoryDetail, getInventoryDetail, delInventoryDetail, addInventoryDetail, updateInventoryDetail } from "@/api/inout/inventoryDetail"

export default {
  name: "InventoryDetail",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 盘点明细表格数据
      inventoryDetailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 盘点任务信息
      inventoryInfo: null,
      // 盘点进度
      inventoryProgress: 0,
      // 差异数量
      differenceCount: 0,
      // 下拉选项
      warehouseOptions: [],
      zoneOptions: [],
      rackOptions: [],
      userOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        inventoryId: null,
        materialId: null,
        quantity: null,
        unit: null,
        warehouseId: null,
        zoneId: null,
        rackId: null,
        locationId: null,
        containerId: null,
        batchNo: null,
        actualQuantity: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        inventoryId: [
          { required: true, message: "盘点单据ID不能为空", trigger: "blur" }
        ],
        materialId: [
          { required: true, message: "物料ID不能为空", trigger: "blur" }
        ],
        quantity: [
          { required: true, message: "数量不能为空", trigger: "blur" }
        ],
        unit: [
          { required: true, message: "单位不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.loadInventoryInfo();
    this.getList();
    this.loadOptions();
  },
  methods: {
    /** 加载盘点任务信息 */
    loadInventoryInfo() {
      const inventoryId = this.$route.query.inventoryId;
      const inventoryNo = this.$route.query.inventoryNo;
      if (inventoryId) {
        // 这里可以调用接口获取盘点任务详情
        this.inventoryInfo = {
          id: inventoryId,
          inventoryNo: inventoryNo || 'PD202501130001',
          inventoryType: '0',
          status: '1',
          warehouseId: 1,
          zoneId: 1,
          rackId: null
        };
      }
    },
    /** 加载下拉选项 */
    loadOptions() {
      this.warehouseOptions = [
        { id: 1, name: '主仓库' },
        { id: 2, name: '备件仓库' }
      ];
      this.userOptions = [
        { userId: 1, nickName: '管理员' },
        { userId: 2, nickName: '仓库员' }
      ];
    },
    /** 返回列表 */
    goBack() {
      this.$router.push('/inout/inventory');
    },
    /** 刷新数据 */
    refreshData() {
      this.getList();
      this.loadInventoryInfo();
    },
    /** 获取盘点类型文本 */
    getInventoryTypeText(type) {
      const typeMap = {
        '0': '全面盘点',
        '1': '抽样盘点',
        '2': '动态盘点'
      };
      return typeMap[type] || type;
    },
    /** 获取盘点状态类型 */
    getInventoryStatusType(status) {
      const typeMap = {
        '0': 'info',
        '1': 'warning',
        '2': 'success',
        '3': 'danger'
      };
      return typeMap[status] || 'info';
    },
    /** 获取盘点状态文本 */
    getInventoryStatusText(status) {
      const textMap = {
        '0': '未开始',
        '1': '进行中',
        '2': '已完成',
        '3': '已取消'
      };
      return textMap[status] || status;
    },
    /** 获取盘点范围 */
    getInventoryScope(info) {
      let scope = this.getWarehouseName(info.warehouseId) || '全部仓库';
      if (info.zoneId) {
        scope += ' - ' + (this.getZoneName(info.zoneId) || '区域' + info.zoneId);
      }
      if (info.rackId) {
        scope += ' - ' + (this.getRackName(info.rackId) || '货架' + info.rackId);
      }
      return scope;
    },
    /** 获取进度状态 */
    getProgressStatus() {
      if (this.inventoryProgress >= 100) return 'success';
      if (this.inventoryProgress >= 50) return null;
      return 'exception';
    },
    /** 计算差异数量颜色 */
    getDiffColor(row) {
      const diff = parseFloat(row.diffQuantity || 0);
      if (diff > 0) return '#E6A23C'; // 黄色，实际大于账面
      if (diff < 0) return '#F56C6C'; // 红色，账面大于实际
      return '#67C23A'; // 绿色，无差异
    },
    /** 获取差异数量 */
    getDifferenceQuantity(row) {
      const system = parseFloat(row.system_quantity || 0);
      const actual = parseFloat(row.actual_quantity || 0);
      const diff = actual - system;
      return diff > 0 ? '+' + diff : diff.toString();
    },
    /** 获取差异样式类 */
    getDifferenceClass(row) {
      const system = parseFloat(row.system_quantity || 0);
      const actual = parseFloat(row.actual_quantity || 0);
      const diff = actual - system;
      if (diff > 0) return 'surplus'; // 盘盈
      if (diff < 0) return 'deficit'; // 盘亏
      return 'normal'; // 无差异
    },
    /** 获取仓库名称 */
    getWarehouseName(warehouseId) {
      const warehouse = this.warehouseOptions.find(w => w.id == warehouseId);
      return warehouse ? warehouse.name : null;
    },
    /** 获取库区名称 */
    getZoneName(zoneId) {
      return 'A区'; // 简化处理
    },
    /** 获取货架名称 */
    getRackName(rackId) {
      return 'A01'; // 简化处理
    },
    /** 获取操作人姓名 */
    getOperatorName(operatorId) {
      const user = this.userOptions.find(u => u.userId == operatorId);
      return user ? user.nickName : '未知';
    },
    /** 查询盘点明细列表 */
    getList() {
      this.loading = true;
      listInventoryDetail(this.queryParams).then(response => {
        this.inventoryDetailList = response.rows;
        this.total = response.total;
        this.loading = false;
        this.updateStatistics();
      });
    },
    /** 更新统计信息 */
    updateStatistics() {
      const total = this.inventoryDetailList.length;
      const completed = this.inventoryDetailList.filter(item => item.status != '0').length;
      this.inventoryProgress = total > 0 ? Math.round((completed / total) * 100) : 0;
      this.differenceCount = this.inventoryDetailList.filter(item => {
        const system = parseFloat(item.system_quantity || 0);
        const actual = parseFloat(item.actual_quantity || 0);
        return Math.abs(actual - system) > 0.01;
      }).length;
    },
    /** 自动生成明细 */
    handleAutoGenerate() {
      this.$modal.confirm('确认根据库存数据自动生成盘点明细？').then(() => {
        this.$modal.msgSuccess("盘点明细生成成功");
        this.getList();
      }).catch(() => {});
    },
    /** 批量录入 */
    handleBatchInput() {
      this.$modal.msgInfo("批量录入功能开发中...");
    },
    /** 批量确认 */
    handleBatchConfirm() {
      if (this.ids.length === 0) {
        this.$modal.msgError("请选择要确认的明细");
        return;
      }
      this.$modal.confirm(`确认批量确认选中的 ${this.ids.length} 条明细？`).then(() => {
        this.$modal.msgSuccess("批量确认成功");
        this.getList();
      }).catch(() => {});
    },
    /** 快速录入 */
    handleQuickInput(row) {
      this.$prompt('请输入实际数量', '快速录入', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^\d+(\.\d+)?$/,
        inputErrorMessage: '请输入有效的数量',
        inputValue: row.system_quantity || '0'
      }).then(({ value }) => {
        // 更新实际数量
        this.$modal.msgSuccess(`已更新实际数量为 ${value}`);
        this.getList();
      }).catch(() => {});
    },
    /** 查看详情 */
    handleView(row) {
      this.reset();
      const id = row.id;
      getInventoryDetail(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "查看盘点明细";
        // 设置表单只读
        this.$nextTick(() => {
          this.$refs.form.disabled = true;
        });
      });
    },
    /** 确认差异 */
    handleConfirm(row) {
      this.$modal.confirm(`确认盘点明细"${row.materialCode || row.materialId}"的差异？`).then(() => {
        this.$modal.msgSuccess("差异确认成功");
        this.getList();
      }).catch(() => {});
    },
    /** 更多操作 */
    handleMoreAction(command, row) {
      switch(command) {
        case 'copy':
          this.handleCopy(row);
          break;
        case 'reset':
          this.handleReset(row);
          break;
        case 'history':
          this.handleHistory(row);
          break;
        case 'delete':
          this.handleDelete(row);
          break;
      }
    },
    /** 复制明细 */
    handleCopy(row) {
      this.reset();
      this.form = { ...row };
      this.form.id = null;
      this.form.status = '0';
      this.open = true;
      this.title = "复制盘点明细";
    },
    /** 重置明细 */
    handleReset(row) {
      this.$modal.confirm(`确认重置明细"${row.materialCode || row.materialId}"？`).then(() => {
        this.$modal.msgSuccess("明细重置成功");
        this.getList();
      }).catch(() => {});
    },
    /** 查看历史记录 */
    handleHistory(row) {
      this.$modal.msgInfo("历史记录功能开发中...");
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        inventoryId: null,
        materialId: null,
        quantity: null,
        unit: null,
        warehouseId: null,
        zoneId: null,
        rackId: null,
        locationId: null,
        containerId: null,
        batchNo: null,
        actualQuantity: null,
        diffQuantity: null,
        status: "0",
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length!==1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加盘点明细";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getInventoryDetail(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改盘点明细";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateInventoryDetail(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInventoryDetail(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除盘点明细编号为"' + ids + '"的数据项？').then(function() {
        return delInventoryDetail(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('inout/inventory-detail/export', {
        ...this.queryParams
      }, `inventory-detail_${new Date().getTime()}.xlsx`);
    }
  }
};
</script>

<style scoped>
.filter-container {
  margin-bottom: 10px;
}

/* 盘点信息卡片样式 */
.inventory-info-card {
  margin-bottom: 15px;
}

.inventory-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.inventory-title {
  display: flex;
  align-items: center;
  gap: 15px;
}

.inventory-title h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
}

.inventory-actions {
  display: flex;
  gap: 10px;
}

.inventory-stats {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

/* 物料信息样式 */
.material-info {
  text-align: left;
}

.material-code {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 13px;
}

.material-code i {
  margin-right: 4px;
  color: #409EFF;
}

.material-name {
  font-size: 12px;
  color: #606266;
  margin-bottom: 2px;
}

.material-spec {
  font-size: 11px;
  color: #909399;
}

/* 位置信息样式 */
.location-info {
  text-align: left;
}

.location-item {
  display: flex;
  align-items: center;
  margin-bottom: 3px;
  font-size: 12px;
}

.location-item i {
  margin-right: 4px;
  width: 14px;
  color: #67C23A;
}

/* 数量对比样式 */
.quantity-compare {
  text-align: left;
}

.quantity-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
}

.quantity-label {
  min-width: 35px;
  color: #909399;
}

.quantity-value {
  font-weight: bold;
  margin-right: 4px;
}

.quantity-unit {
  color: #909399;
  font-size: 11px;
}

.quantity-item.system .quantity-value {
  color: #409EFF;
}

.quantity-item.actual .quantity-value {
  color: #67C23A;
}

.quantity-item.difference.normal .quantity-value {
  color: #67C23A;
}

.quantity-item.difference.surplus .quantity-value {
  color: #E6A23C;
}

.quantity-item.difference.deficit .quantity-value {
  color: #F56C6C;
}

/* 操作人员样式 */
.operator-info {
  text-align: center;
  font-size: 12px;
}

.operator-info i {
  margin-right: 4px;
  color: #409EFF;
}

.operation-time {
  color: #909399;
  font-size: 11px;
  margin-top: 2px;
}
</style>