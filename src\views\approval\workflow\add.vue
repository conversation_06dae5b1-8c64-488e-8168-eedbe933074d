<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>新增流程定义</span>
      </div>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <!-- 基本信息部分 -->
        <el-divider content-position="left">基本信息</el-divider>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="流程名称" prop="workflowName">
              <el-input v-model="form.workflowName" placeholder="请输入流程名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="流程编码" prop="workflowCode">
              <el-input v-model="form.workflowCode" placeholder="请输入流程编码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="业务类型" prop="businessType">
              <el-select v-model="form.businessType" placeholder="请选择业务类型" style="width: 100%">
                <el-option v-for="item in businessTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio label="0">启用</el-radio>
                <el-radio label="1">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入描述信息" />
        </el-form-item>

        <!-- 多级审批配置 -->
        <el-divider content-position="left">审批流程配置</el-divider>
        <div class="multilevel-config">
          <el-alert
            title="统一流程管理"
            type="info"
            description="现在使用统一的多级审批管理，支持1-5级审批流程。单级审批就是1级的多级审批。"
            show-icon
            :closable="false"
            style="margin-bottom: 20px;">
          </el-alert>

          <el-button type="primary" size="large" @click="openMultilevelDialog" icon="el-icon-setting">
            配置审批流程
          </el-button>

          <div v-if="multilevelConfig.levels && multilevelConfig.levels.length > 0" class="multilevel-preview">
            <h4>审批流程预览：</h4>
            <el-steps :active="multilevelConfig.levels.length" finish-status="success" style="margin-top: 20px;">
              <el-step
                v-for="(level, index) in multilevelConfig.levels"
                :key="index"
                :title="`第${level.level}级`"
                :description="level.levelName"
              ></el-step>
            </el-steps>
            <div class="level-summary" style="margin-top: 15px;">
              <el-tag type="success">{{ multilevelConfig.levels.length }}级审批</el-tag>
              <span style="margin-left: 10px; color: #666;">
                共需 {{ multilevelConfig.levels.reduce((total, level) => total + (level.approvers ? level.approvers.split(',').length : 0), 0) }} 人参与审批
              </span>
            </div>
          </div>
        </div>

        <el-form-item style="margin-top: 40px">
          <el-button type="primary" @click="submitForm">提交</el-button>
          <el-button @click="cancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>

  </div>
</template>

<script>
import { getWorkflowOptions } from "@/api/approval/workflow";
import { createMultilevelWorkflow } from "@/api/approval/multilevel";

export default {
  name: "AddWorkflow",
  data() {
    return {
      // 多级审批配置
      multilevelConfig: {
        levels: []
      },
      // 表单参数
      form: {
        workflowName: undefined,
        workflowCode: undefined,
        businessType: undefined,
        description: undefined,
        status: "0"
      },
      // 表单校验规则
      rules: {
        workflowName: [
          { required: true, message: "流程名称不能为空", trigger: "blur" }
        ],
        workflowCode: [
          { required: true, message: "流程编码不能为空", trigger: "blur" }
        ],
        businessType: [
          { required: true, message: "业务类型不能为空", trigger: "change" }
        ]
      },
      // 业务类型选项
      businessTypeOptions: []
    };
  },
  created() {
    this.getBusinessTypeOptions();
  },
  methods: {
    /** 获取业务类型选项 */
    getBusinessTypeOptions() {
      getWorkflowOptions().then(res => {
        if (res.code === 200 && res.data) {
          // 使用Map对象确保每个业务类型只出现一次
          const businessTypeMap = new Map();
          res.data.forEach(item => {
            if (!businessTypeMap.has(item.businessType)) {
              businessTypeMap.set(item.businessType, {
                value: item.businessType,
                label: item.businessTypeName || item.businessType
              });
            }
          });
          
          // 将Map转换回数组
          this.businessTypeOptions = Array.from(businessTypeMap.values());
        }
      });
    },

    /** 表单提交 */
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 检查是否配置了审批流程
          if (!this.multilevelConfig.levels || this.multilevelConfig.levels.length === 0) {
            this.$modal.msgError("请先配置审批流程");
            return;
          }

          // 使用多级审批API创建工作流
          const submitData = {
            workflowName: this.form.workflowName,
            businessType: this.form.businessType,
            approvalLevels: this.multilevelConfig.levels
          };

          this.createMultilevelWorkflow(submitData);
        }
      });
    },
    /** 创建多级审批工作流 */
    createMultilevelWorkflow(data) {
      createMultilevelWorkflow(data).then(response => {
        this.$modal.msgSuccess("创建成功");
        this.cancel();
      }).catch(error => {
        console.error("创建失败:", error);
        this.$modal.msgError("创建失败: " + (error.message || "未知错误"));
      });
    },
    /** 取消按钮 */
    cancel() {
      this.$router.push("/approval/workflow");
    },
    /** 打开多级审批配置对话框 */
    openMultilevelDialog() {
      // 验证基本信息是否已填写
      if (!this.form.workflowName) {
        this.$modal.msgError("请先填写流程名称");
        return;
      }
      if (!this.form.businessType) {
        this.$modal.msgError("请先选择业务类型");
        return;
      }

      // 跳转到多级审批配置页面，传递已填写的基本信息
      console.log('传递的表单数据:', this.form);
      this.$router.push({
        path: '/approval/multilevel/add',
        query: {
          workflowName: this.form.workflowName,
          workflowCode: this.form.workflowCode,
          businessType: this.form.businessType,
          description: this.form.description,
          status: this.form.status,
          returnUrl: '/approval/workflow/add'
        }
      });
    }
  }
};
</script>

<style scoped>
.multilevel-config {
  margin: 20px 0;
  text-align: center;
}

.multilevel-preview {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  text-align: left;
}

.multilevel-preview h4 {
  margin-bottom: 15px;
  color: #303133;
}

.level-summary {
  display: flex;
  align-items: center;
  margin-top: 15px;
}
</style>