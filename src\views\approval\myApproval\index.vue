<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 我申请的列表 -->
      <el-col :span="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>我申请的列表</span>
          </div>
          <div class="filter-container">
            <el-input v-model="queryParams.title" placeholder="审批标题" clearable style="width: 200px" class="filter-item" @keyup.enter.native="handleQuery" />
            <el-select v-model="queryParams.businessType" placeholder="业务类型" clearable class="filter-item" style="width: 200px">
              <el-option v-for="item in businessTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-select v-model="queryParams.workflowId" placeholder="流程名称" clearable class="filter-item" style="width: 200px">
              <el-option v-for="item in workflowOptions" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
            <el-select v-model="queryParams.status" placeholder="审批状态" clearable class="filter-item" style="width: 200px">
              <el-option label="审批中" value="0" />
              <el-option label="已通过" value="1" />
              <el-option label="已驳回" value="2" />
            </el-select>
            <el-button type="primary" icon="el-icon-search" class="filter-item" @click="handleQuery">搜索</el-button>
            <el-button type="default" icon="el-icon-refresh" class="filter-item" @click="resetQuery">重置</el-button>
          </div>

          <el-table v-loading="loading" :data="myApprovalList" border>
            <el-table-column label="序号" type="index" width="50" align="center" />
            <el-table-column label="审批标题" prop="title" :show-overflow-tooltip="true" />
            <el-table-column label="流程名称" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                {{ scope.row.workflowName || scope.row.workflow_name || '-' }}
              </template>
            </el-table-column>
            <el-table-column label="业务类型" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <el-tag v-if="(scope.row.businessType || scope.row.business_type) === 'MATERIAL_BILL'" type="primary">物料清单</el-tag>
                <el-tag v-else-if="(scope.row.businessType || scope.row.business_type) === 'MATERIAL_APPROVAL'" type="success">物料审批</el-tag>
                <el-tag v-else-if="(scope.row.businessType || scope.row.business_type) === 'MATERIAL_APPLY'" type="warning">物料申请</el-tag>
                <span v-else>{{ scope.row.businessTypeName || scope.row.businessType || scope.row.business_type || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="申请时间" align="center" width="180">
              <template slot-scope="scope">
                {{ parseTime(scope.row.createTime || scope.row.create_time) }}
              </template>
            </el-table-column>
            <el-table-column label="当前环节" align="center" width="100">
              <template slot-scope="scope">
                <el-tooltip :content="getNodeDetail(scope.row)" placement="top">
                  <el-button type="text">{{ scope.row.currentNodeName || scope.row.current_node_name || '-' }}</el-button>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="审批状态" align="center" width="100">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.status == 0 || scope.row.status === '0'" type="warning">审批中</el-tag>
                <el-tag v-else-if="scope.row.status == 1 || scope.row.status === '1'" type="success">已通过</el-tag>
                <el-tag v-else-if="scope.row.status == 2 || scope.row.status === '2'" type="danger">已驳回</el-tag>
                <el-tag v-else-if="scope.row.status == 3 || scope.row.status === '3'" type="info">已撤销</el-tag>
                <el-tag v-else type="info">未知({{ scope.row.status }})</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button type="text" icon="el-icon-view" @click="handleView(scope.row)" v-hasPermi="['approval:my:query']">查看</el-button>
                <el-button v-if="scope.row.status == 0 || scope.row.status === '0'" type="text" icon="el-icon-close" @click="handleCancel(scope.row)" v-hasPermi="['approval:my:cancel']">撤销</el-button>
                <el-button v-if="scope.row.status == 2 || scope.row.status === '2'" type="text" icon="el-icon-refresh" @click="handleReapply(scope.row)" v-hasPermi="['approval:my:reapply']">重新申请</el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { listMyApproval, cancelApproval, getWorkflowOptions, getBusinessTypes } from "@/api/approval/workflow";
import { parseTime } from '@/utils/ruiyun';

export default {
  name: "MyApproval",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 我申请的列表
      myApprovalList: [],
      // 流程类型选项
      workflowOptions: [],
      // 业务类型选项
      businessTypeOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: undefined,
        businessType: undefined,
        workflowId: undefined,
        status: undefined
      }
    };
  },
  created() {
    this.getList();
    this.getWorkflowOptions();
    this.getBusinessTypeOptions();
  },
  methods: {
    parseTime,
    /** 查询我申请的列表 */
    getList() {
      this.loading = true;
      listMyApproval(this.queryParams).then(response => {
        this.myApprovalList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        console.error('获取我的申请列表失败:', error);
        this.loading = false;
      });
    },
    /** 获取流程类型选项 */
    getWorkflowOptions() {
      getWorkflowOptions().then(response => {
        if (response.code === 200 && response.data) {
          this.workflowOptions = response.data.map(item => ({
            id: item.workflowId,
            name: item.workflowName
          }));
        }
      });
    },
    /** 获取业务类型选项 */
    getBusinessTypeOptions() {
      getBusinessTypes().then(response => {
        if (response.code === 200) {
          this.businessTypeOptions = response.data.map(item => ({
            value: item.dictValue,
            label: item.dictLabel
          }));
        }
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        title: undefined,
        businessType: undefined,
        workflowId: undefined,
        status: undefined
      };
      this.handleQuery();
    },
    /** 查看按钮操作 */
    handleView(row) {
      // 兼容不同的字段名：instanceId（驼峰）或 instance_id（下划线）
      const instanceId = row.instanceId || row.instance_id;

      if (!row || !instanceId) {
        this.$message.error("无效的审批实例ID");
        return;
      }
      // 使用路径参数而不是查询参数
      this.$router.push({ path: '/approval/detail/' + instanceId });
    },
    /** 撤销按钮操作 */
    handleCancel(row) {
      // 兼容不同的字段名：instanceId（驼峰）或 instance_id（下划线）
      const instanceId = row.instanceId || row.instance_id;

      if (!row || !instanceId) {
        this.$message.error("无效的审批实例ID");
        return;
      }

      this.$modal.confirm('是否确认撤销申请"' + row.title + '"？').then(() => {
        const params = {
          instanceId: String(instanceId), // 确保转换为字符串
          opinion: '申请人主动撤销'
        };
        return cancelApproval(params);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("撤销申请成功");
      }).catch((error) => {
        console.error('撤销审批失败:', error);
        this.$modal.msgError("撤销申请失败: " + (error.message || error));
      });
    },
    /** 重新申请按钮操作 */
    handleReapply(row) {
      this.$router.push({ path: '/approval/instance/reapply', query: { id: row.instanceId }});
    },
    
    /** 获取节点详情 */
    getNodeDetail(row) {
      if (!row || !row.currentNodeName) return '未知节点';
      
      let detail = `节点名称: ${row.currentNodeName}`;
      
      // 添加节点类型信息
      if (row.currentNodeType) {
        let nodeTypeText = '';
        switch (row.currentNodeType) {
          case '0': nodeTypeText = '开始节点'; break;
          case '1': nodeTypeText = '审批节点'; break;
          case '2': nodeTypeText = '结束节点'; break;
          default: nodeTypeText = '未知类型';
        }
        detail += `\n节点类型: ${nodeTypeText}`;
      }
      
      // 添加审批人信息
      if (row.currentApprovers) {
        detail += `\n审批人: ${row.currentApprovers}`;
      }
      
      // 添加状态信息
      if (row.status !== undefined) {
        let statusText = '';
        switch (row.status) {
          case 0: statusText = '审批中'; break;
          case 1: statusText = '已通过'; break;
          case 2: statusText = '已驳回'; break;
          default: statusText = '未知状态';
        }
        detail += `\n状态: ${statusText}`;
      }
      
      // 添加业务信息
      const businessType = row.businessType || row.business_type;
      if (businessType) {
        const businessTypeText = this.getBusinessTypeName(businessType);
        detail += `\n业务类型: ${businessTypeText}`;
      }
      
      return detail;
    },

    /** 获取业务类型选项 */
    getBusinessTypeOptions() {
      getWorkflowOptions().then(res => {
        if (res.code === 200 && res.data) {
          // 使用Map对象确保每个业务类型只出现一次
          const businessTypeMap = new Map();
          res.data.forEach(item => {
            if (!businessTypeMap.has(item.businessType)) {
              businessTypeMap.set(item.businessType, {
                value: item.businessType,
                label: item.businessTypeName || item.businessType
              });
            }
          });

          // 将Map转换回数组
          this.businessTypeOptions = Array.from(businessTypeMap.values());
        }
      });
    },

    /** 获取业务类型名称 */
    getBusinessTypeName(businessType) {
      const option = this.businessTypeOptions.find(item => item.value === businessType);
      return option ? option.label : businessType;
    }
  }
};
</script> 