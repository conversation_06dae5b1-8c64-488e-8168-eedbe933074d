# MQTT Broker 和 SIP Server 前端模块

## 📋 概述

本文档描述了为 ruiyun-ui 前端项目新增的 MQTT Broker 和 SIP Server 管理模块，这些模块与后端的 mqtt-broker 和 sip-server 模块相对应，提供完整的前端管理界面。

## 🏗️ 模块架构

### MQTT Broker 模块
```
ruiyun-ui/src/
├── api/mqtt/                          # MQTT API 接口
│   ├── broker.js                      # Broker 管理 API
│   ├── client.js                      # 客户端管理 API
│   ├── message.js                     # 消息管理 API
│   └── auth.js                        # 认证管理 API
├── router/modules/
│   └── mqtt.js                        # MQTT 路由配置
├── utils/
│   └── mqtt-menu-config.js            # MQTT 菜单配置
└── views/mqtt/                        # MQTT 视图组件
    ├── dashboard/                     # 系统概览
    ├── clients/                       # 客户端管理
    ├── messages/                      # 消息管理
    ├── auth/                          # 认证管理
    ├── topics/                        # 主题管理
    ├── monitoring/                    # 实时监控
    └── settings/                      # 系统设置
```

### SIP Server 模块
```
ruiyun-ui/src/
├── api/sip/                           # SIP API 接口
│   ├── server.js                      # 服务器管理 API
│   ├── device.js                      # 设备管理 API
│   ├── session.js                     # 会话管理 API
│   └── media.js                       # 媒体管理 API
├── router/modules/
│   └── sip.js                         # SIP 路由配置
├── utils/
│   └── sip-menu-config.js             # SIP 菜单配置
└── views/sip/                         # SIP 视图组件
    ├── dashboard/                     # 系统概览
    ├── devices/                       # 设备管理
    ├── sessions/                      # 会话管理
    ├── media/                         # 媒体管理
    ├── messages/                      # 消息管理
    ├── monitoring/                    # 实时监控
    └── settings/                      # 系统设置
```

## 🚀 核心功能

### MQTT Broker 管理

#### 1. 系统概览 (`/mqtt/dashboard`)
- **功能**: 显示 MQTT Broker 整体状态
- **特性**:
  - Broker 运行状态监控
  - 在线客户端统计
  - 消息流量统计
  - 活跃主题统计
  - 实时图表展示
  - 性能指标监控

#### 2. 客户端管理 (`/mqtt/clients`)
- **功能**: 管理在线 MQTT 客户端
- **特性**:
  - 客户端列表查看
  - 客户端详情查看
  - 强制断开连接
  - 订阅列表查看
  - 批量操作支持
  - 客户端搜索和过滤

#### 3. 消息管理 (`/mqtt/messages`)
- **功能**: 管理 MQTT 消息
- **特性**:
  - 消息历史查询
  - 保留消息管理
  - 消息发布功能
  - 消息重发功能
  - 过期消息清理
  - 消息导出功能

#### 4. 认证管理 (`/mqtt/auth`)
- **功能**: 管理客户端认证凭据
- **状态**: 开发中

#### 5. 主题管理 (`/mqtt/topics`)
- **功能**: 管理 MQTT 主题
- **状态**: 开发中

#### 6. 实时监控 (`/mqtt/monitoring`)
- **功能**: 实时监控系统状态
- **状态**: 开发中

#### 7. 系统设置 (`/mqtt/settings`)
- **功能**: 配置 MQTT Broker 参数
- **状态**: 开发中

### SIP Server 管理

#### 1. 系统概览 (`/sip/dashboard`)
- **功能**: 显示 SIP 服务器整体状态
- **特性**:
  - SIP 服务器运行状态
  - 在线设备统计
  - 活跃会话统计
  - 媒体流统计
  - 设备注册趋势图表
  - 会话统计图表

#### 2. 设备管理 (`/sip/devices`)
- **功能**: 管理 GB28181 设备
- **特性**:
  - 设备列表查看
  - 设备详情查看
  - 设备状态监控
  - 设备控制命令
  - 设备查询功能
  - 强制设备离线
  - PTZ 控制支持

#### 3. 会话管理 (`/sip/sessions`)
- **功能**: 管理 SIP 会话
- **特性**:
  - 会话列表查看
  - 会话详情查看
  - 会话状态监控
  - 媒体信息查看
  - 会话终止功能
  - 批量会话操作

#### 4. 媒体管理 (`/sip/media`)
- **功能**: 管理 RTP 媒体流
- **状态**: 开发中

#### 5. 消息管理 (`/sip/messages`)
- **功能**: 管理 SIP 协议消息
- **状态**: 开发中

#### 6. 实时监控 (`/sip/monitoring`)
- **功能**: 实时监控设备和会话
- **状态**: 开发中

#### 7. 系统设置 (`/sip/settings`)
- **功能**: 配置 SIP 服务器参数
- **状态**: 开发中

## 📖 API 接口

### MQTT Broker API

#### Broker 管理
```javascript
// 获取 Broker 状态
getBrokerStatus()

// 获取 Broker 统计信息
getBrokerStatistics()

// 重启 Broker
restartBroker()
```

#### 客户端管理
```javascript
// 获取在线客户端
getOnlineClients(params)

// 获取客户端详情
getClientDetails(clientId)

// 断开客户端连接
disconnectClient(clientId, reason)

// 批量断开客户端
batchDisconnectClients(clientIds, reason)
```

#### 消息管理
```javascript
// 获取消息历史
getMessageHistory(params)

// 发布消息
publishMessage(data)

// 获取保留消息
getRetainedMessages(params)

// 清理过期消息
cleanupExpiredMessages(maxAge)
```

### SIP Server API

#### 服务器管理
```javascript
// 获取 SIP 服务器状态
getSipServerStatus()

// 获取服务器统计信息
getSipServerStatistics()

// 重启 SIP 服务器
restartSipServer()
```

#### 设备管理
```javascript
// 获取设备列表
getSipDevices(params)

// 获取设备详情
getSipDeviceDetails(deviceId)

// 强制设备离线
forceDeviceOffline(deviceId, reason)

// 设备控制命令
controlDevice(deviceId, data)

// 设备查询命令
queryDevice(deviceId, cmdType)
```

#### 会话管理
```javascript
// 获取会话列表
getSipSessions(params)

// 获取会话详情
getSipSessionDetails(sessionId)

// 终止会话
terminateSession(sessionId, reason)

// 获取会话媒体信息
getSessionMediaInfo(sessionId)
```

## 🔧 配置说明

### 路由配置

路由模块已自动添加到动态路由中：

```javascript
// ruiyun-ui/src/router/index.js
import mqttRouter from './modules/mqtt'
import sipRouter from './modules/sip'
import emqxRouter from './modules/emqx'

// 添加到动态路由
dynamicRoutes.push(mqttRouter, sipRouter, emqxRouter)
```

### 权限配置

#### MQTT 权限
- `mqtt:client:list` - 客户端列表查看
- `mqtt:client:disconnect` - 客户端断开
- `mqtt:message:list` - 消息列表查看
- `mqtt:message:publish` - 消息发布
- `mqtt:auth:list` - 认证管理
- `mqtt:monitor:view` - 监控查看
- `mqtt:settings:manage` - 系统设置

#### SIP 权限
- `sip:device:list` - 设备列表查看
- `sip:device:control` - 设备控制
- `sip:session:list` - 会话列表查看
- `sip:session:terminate` - 会话终止
- `sip:media:list` - 媒体管理
- `sip:monitor:view` - 监控查看
- `sip:settings:manage` - 系统设置

## 🎯 使用指南

### 1. 访问 MQTT 管理
```
http://localhost/mqtt/dashboard
```

### 2. 访问 SIP 管理
```
http://localhost/sip/dashboard
```

### 3. 权限要求
用户需要具备相应的角色权限：
- `admin` - 管理员权限
- `mqtt_admin` - MQTT 管理员权限
- `sip_admin` - SIP 管理员权限

## 🔍 开发状态

### 已完成功能
- ✅ MQTT Dashboard (系统概览)
- ✅ MQTT 客户端管理
- ✅ MQTT 消息管理
- ✅ SIP Dashboard (系统概览)
- ✅ SIP 设备管理
- ✅ SIP 会话管理
- ✅ 路由配置
- ✅ API 接口定义
- ✅ 菜单配置

### 开发中功能
- 🚧 MQTT 认证管理
- 🚧 MQTT 主题管理
- 🚧 MQTT 实时监控
- 🚧 MQTT 系统设置
- 🚧 SIP 媒体管理
- 🚧 SIP 消息管理
- 🚧 SIP 实时监控
- 🚧 SIP 系统设置

## 📊 技术特性

### 前端技术栈
- **Vue.js 2.x** - 前端框架
- **Element UI** - UI 组件库
- **Vue Router** - 路由管理
- **Axios** - HTTP 客户端
- **ECharts** - 图表库

### 设计特点
- **响应式设计** - 适配不同屏幕尺寸
- **实时更新** - 支持数据实时刷新
- **权限控制** - 基于角色的权限管理
- **模块化** - 组件化开发，易于维护
- **国际化** - 支持多语言扩展

## 🚀 快速开始

1. **确保后端服务运行**
   ```bash
   # 启动 ruiyun-server
   cd ruiyun-server
   mvn spring-boot:run
   ```

2. **启动前端开发服务器**
   ```bash
   # 启动 ruiyun-ui
   cd ruiyun-ui
   npm run dev
   ```

3. **访问管理界面**
   - MQTT 管理: http://localhost:8080/mqtt/dashboard
   - SIP 管理: http://localhost:8080/sip/dashboard

这样就完成了 MQTT Broker 和 SIP Server 前端管理模块的集成！🎉
