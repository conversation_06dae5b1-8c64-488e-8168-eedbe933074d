<template>
  <div class="access-monitor">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon">
            <i class="el-icon-key"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.doorCount || 0 }}</div>
            <div class="stat-label">门禁设备</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon success">
            <i class="el-icon-user"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ currentPersons.length || 0 }}</div>
            <div class="stat-label">在库人员</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon warning">
            <i class="el-icon-warning"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.failCount || 0 }}</div>
            <div class="stat-label">失败次数</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon info">
            <i class="el-icon-data-board"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.totalCount || 0 }}</div>
            <div class="stat-label">总访问次数</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 设备状态监控 -->
    <el-card class="device-monitor" shadow="never">
      <div slot="header" class="card-header">
        <span>设备状态</span>
        <div class="header-actions">
          <el-button 
            type="text" 
            icon="el-icon-refresh" 
            @click="refreshDeviceStatus"
            :loading="deviceLoading"
          >
            刷新
          </el-button>
        </div>
      </div>
      
      <el-row :gutter="20">
        <el-col 
          v-for="device in deviceList" 
          :key="device.doorId" 
          :span="8"
          style="margin-bottom: 20px;"
        >
          <div class="device-card" :class="{ 'online': device.status === 'online', 'offline': device.status === 'offline' }">
            <div class="device-header">
              <div class="device-name">{{ device.doorName }}</div>
              <div class="device-status">
                <el-tag 
                  :type="device.status === 'online' ? 'success' : 'danger'" 
                  size="mini"
                >
                  {{ device.status === 'online' ? '在线' : '离线' }}
                </el-tag>
              </div>
            </div>
            <div class="device-content">
              <div class="device-info">
                <div class="info-item">
                  <i class="el-icon-location"></i>
                  <span>{{ device.location }}</span>
                </div>
                <div class="info-item">
                  <i class="el-icon-lock"></i>
                  <span>{{ device.locked ? '已锁定' : '未锁定' }}</span>
                </div>
                <div class="info-item" v-if="device.lastHeartbeat">
                  <i class="el-icon-time"></i>
                  <span>{{ formatTime(device.lastHeartbeat) }}</span>
                </div>
              </div>
            </div>
            <div class="device-actions">
              <el-button 
                type="text" 
                size="mini" 
                @click="controlDevice(device.doorId, 'unlock')"
                :disabled="device.status !== 'online'"
              >
                开锁
              </el-button>
              <el-button 
                type="text" 
                size="mini" 
                @click="controlDevice(device.doorId, 'lock')"
                :disabled="device.status !== 'online'"
              >
                锁定
              </el-button>
              <el-button 
                type="text" 
                size="mini" 
                @click="showDeviceDetail(device)"
              >
                详情
              </el-button>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 实时访问记录 -->
    <el-card class="realtime-access" shadow="never">
      <div slot="header" class="card-header">
        <span>实时访问记录</span>
        <div class="header-actions">
          <el-button 
            type="text" 
            icon="el-icon-refresh" 
            @click="refreshRealTimeData"
            :loading="realtimeLoading"
          >
            刷新
          </el-button>
          <el-switch
            v-model="autoRefresh"
            active-text="自动刷新"
            @change="toggleAutoRefresh"
          ></el-switch>
        </div>
      </div>
      
      <el-table :data="realtimeData" style="width: 100%" max-height="400">
        <el-table-column prop="accessTime" label="时间" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.accessTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="doorName" label="门禁" width="120" />
        <el-table-column prop="personName" label="人员" width="120" />
        <el-table-column prop="accessType" label="类型" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.accessType === 1 ? 'success' : 'info'" size="mini">
              {{ scope.row.accessType === 1 ? '进入' : '离开' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="accessResult" label="结果" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.accessResult === 1 ? 'success' : 'danger'" size="mini">
              {{ scope.row.accessResult === 1 ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="authMethod" label="验证方式" width="100" />
        <el-table-column prop="personDept" label="部门" />
        <el-table-column prop="failureReason" label="失败原因" show-overflow-tooltip />
      </el-table>
      
      <div v-if="realtimeData.length === 0" class="empty-data">
        <el-empty description="暂无实时数据"></el-empty>
      </div>
    </el-card>

    <!-- 在库人员 -->
    <el-card class="current-persons" shadow="never">
      <div slot="header" class="card-header">
        <span>当前在库人员</span>
        <div class="header-actions">
          <el-button 
            type="text" 
            icon="el-icon-refresh" 
            @click="getCurrentPersons"
          >
            刷新
          </el-button>
        </div>
      </div>
      
      <el-row :gutter="16">
        <el-col 
          v-for="person in currentPersons" 
          :key="person.personId" 
          :span="6"
          style="margin-bottom: 16px;"
        >
          <div class="person-card">
            <div class="person-avatar">
              <img 
                v-if="person.personPhoto" 
                :src="person.personPhoto" 
                :alt="person.personName"
                @error="handleImageError"
              />
              <i v-else class="el-icon-user-solid"></i>
            </div>
            <div class="person-info">
              <div class="person-name">{{ person.personName }}</div>
              <div class="person-dept">{{ person.personDept }}</div>
              <div class="person-time">
                <i class="el-icon-time"></i>
                <span>{{ formatTime(person.accessTime) }}</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      
      <div v-if="currentPersons.length === 0" class="empty-data">
        <el-empty description="当前无人员在库"></el-empty>
      </div>
    </el-card>

    <!-- 历史记录 -->
    <el-card class="history-records" shadow="never">
      <div slot="header" class="card-header">
        <span>历史记录</span>
        <div class="header-actions">
          <el-button type="primary" icon="el-icon-search" @click="queryHistoryData">查询</el-button>
          <el-button type="success" icon="el-icon-download" @click="exportData">导出</el-button>
        </div>
      </div>
      
      <!-- 查询条件 -->
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
        <el-form-item label="门禁ID" prop="doorId">
          <el-input
            v-model="queryParams.doorId"
            placeholder="请输入门禁ID"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="人员姓名" prop="personName">
          <el-input
            v-model="queryParams.personName"
            placeholder="请输入人员姓名"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="进出类型" prop="accessType">
          <el-select v-model="queryParams.accessType" placeholder="请选择进出类型" clearable>
            <el-option label="进入" value="1"></el-option>
            <el-option label="离开" value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="验证结果" prop="accessResult">
          <el-select v-model="queryParams.accessResult" placeholder="请选择验证结果" clearable>
            <el-option label="成功" value="1"></el-option>
            <el-option label="失败" value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="访问时间">
          <el-date-picker
            v-model="dateRange"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="accessList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="访问时间" align="center" prop="accessTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.accessTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="门禁名称" align="center" prop="doorName" />
        <el-table-column label="人员姓名" align="center" prop="personName" />
        <el-table-column label="进出类型" align="center" prop="accessType">
          <template slot-scope="scope">
            <el-tag :type="scope.row.accessType === 1 ? 'success' : 'info'">
              {{ scope.row.accessType === 1 ? '进入' : '离开' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="验证结果" align="center" prop="accessResult">
          <template slot-scope="scope">
            <el-tag :type="scope.row.accessResult === 1 ? 'success' : 'danger'">
              {{ scope.row.accessResult === 1 ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="验证方式" align="center" prop="authMethod" />
        <el-table-column label="部门" align="center" prop="personDept" />
        <el-table-column label="职位" align="center" prop="personPosition" />
        <el-table-column label="失败原因" align="center" prop="failureReason" show-overflow-tooltip />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
            >查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 访问记录详情对话框 -->
    <el-dialog title="访问记录详情" :visible.sync="detailDialogVisible" width="600px">
      <el-descriptions :column="2" border v-if="currentRecord">
        <el-descriptions-item label="访问时间">
          {{ parseTime(currentRecord.accessTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </el-descriptions-item>
        <el-descriptions-item label="门禁名称">{{ currentRecord.doorName }}</el-descriptions-item>
        <el-descriptions-item label="人员姓名">{{ currentRecord.personName }}</el-descriptions-item>
        <el-descriptions-item label="人员ID">{{ currentRecord.personId }}</el-descriptions-item>
        <el-descriptions-item label="进出类型">
          <el-tag :type="currentRecord.accessType === 1 ? 'success' : 'info'">
            {{ currentRecord.accessType === 1 ? '进入' : '离开' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="验证结果">
          <el-tag :type="currentRecord.accessResult === 1 ? 'success' : 'danger'">
            {{ currentRecord.accessResult === 1 ? '成功' : '失败' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="验证方式">{{ currentRecord.authMethod }}</el-descriptions-item>
        <el-descriptions-item label="设备IP">{{ currentRecord.deviceIp || '-' }}</el-descriptions-item>
        <el-descriptions-item label="部门">{{ currentRecord.personDept || '-' }}</el-descriptions-item>
        <el-descriptions-item label="职位">{{ currentRecord.personPosition || '-' }}</el-descriptions-item>
        <el-descriptions-item label="失败原因" span="2">
          {{ currentRecord.failureReason || '-' }}
        </el-descriptions-item>
      </el-descriptions>
      
      <!-- 人员照片 -->
      <div v-if="currentRecord.personPhoto" class="person-photo">
        <h4>人员照片</h4>
        <img :src="currentRecord.personPhoto" :alt="currentRecord.personName" />
      </div>
    </el-dialog>

    <!-- 设备详情对话框 -->
    <el-dialog title="设备详情" :visible.sync="deviceDetailVisible" width="500px">
      <el-descriptions :column="2" border v-if="currentDevice">
        <el-descriptions-item label="设备ID">{{ currentDevice.doorId }}</el-descriptions-item>
        <el-descriptions-item label="设备名称">{{ currentDevice.doorName }}</el-descriptions-item>
        <el-descriptions-item label="设备状态">
          <el-tag :type="currentDevice.status === 'online' ? 'success' : 'danger'">
            {{ currentDevice.status === 'online' ? '在线' : '离线' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="锁定状态">
          <el-tag :type="currentDevice.locked ? 'warning' : 'success'">
            {{ currentDevice.locked ? '已锁定' : '未锁定' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="位置" span="2">{{ currentDevice.location }}</el-descriptions-item>
        <el-descriptions-item label="最后心跳" span="2">
          {{ formatTime(currentDevice.lastHeartbeat) }}
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import { 
  listAccessRecord, 
  getAccessStats, 
  getRealTimeAccessData, 
  getCurrentPersons,
  controlDevice,
  getDeviceStatus
} from '@/api/dwms/access'

export default {
  name: 'AccessMonitor',
  data() {
    return {
      // 加载状态
      loading: true,
      realtimeLoading: false,
      deviceLoading: false,
      
      // 列表数据
      accessList: [],
      total: 0,
      
      // 实时数据
      realtimeData: [],
      autoRefresh: false,
      refreshTimer: null,
      
      // 统计数据
      stats: {},
      
      // 当前在库人员
      currentPersons: [],
      
      // 设备列表
      deviceList: [],
      
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        doorId: null,
        personName: null,
        accessType: null,
        accessResult: null
      },
      
      // 日期范围
      dateRange: [],
      
      // 显示搜索条件
      showSearch: true,
      
      // 选中数组
      ids: [],
      single: true,
      multiple: true,
      
      // 对话框
      detailDialogVisible: false,
      deviceDetailVisible: false,
      currentRecord: null,
      currentDevice: null
    }
  },
  created() {
    this.getList()
    this.getStats()
    this.getRealTimeData()
    this.getCurrentPersons()
    this.getDeviceList()
  },
  beforeDestroy() {
    this.clearRefreshTimer()
  },
  methods: {
    /** 查询门禁记录列表 */
    getList() {
      this.loading = true
      this.addDateRange(this.queryParams, this.dateRange)
      listAccessRecord(this.queryParams).then(response => {
        this.accessList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    
    /** 查询统计数据 */
    getStats() {
      getAccessStats().then(response => {
        this.stats = response.data || {}
      })
    },
    
    /** 获取实时数据 */
    getRealTimeData() {
      this.realtimeLoading = true
      getRealTimeAccessData().then(response => {
        this.realtimeData = response.data || []
        this.realtimeLoading = false
      }).catch(() => {
        this.realtimeLoading = false
      })
    },
    
    /** 获取当前在库人员 */
    getCurrentPersons() {
      getCurrentPersons().then(response => {
        this.currentPersons = response.data || []
      })
    },
    
    /** 获取设备列表 */
    getDeviceList() {
      // TODO: 调用获取设备列表的API
      // 这里使用模拟数据
      this.deviceList = [
        {
          doorId: 'AC001',
          doorName: '主入口',
          status: 'online',
          location: '主入口',
          locked: true,
          lastHeartbeat: new Date()
        },
        {
          doorId: 'AC002',
          doorName: '后门',
          status: 'offline',
          location: '后门',
          locked: false,
          lastHeartbeat: new Date(Date.now() - 10 * 60 * 1000)
        }
      ]
    },
    
    /** 刷新实时数据 */
    refreshRealTimeData() {
      this.getRealTimeData()
      this.getStats()
      this.getCurrentPersons()
    },
    
    /** 刷新设备状态 */
    refreshDeviceStatus() {
      this.deviceLoading = true
      // 刷新每个设备的状态
      this.deviceList.forEach(device => {
        getDeviceStatus(device.doorId).then(response => {
          Object.assign(device, response.data)
        })
      })
      this.deviceLoading = false
    },
    
    /** 切换自动刷新 */
    toggleAutoRefresh(enabled) {
      if (enabled) {
        this.refreshTimer = setInterval(() => {
          this.refreshRealTimeData()
        }, 30000) // 30秒刷新一次
      } else {
        this.clearRefreshTimer()
      }
    },
    
    /** 清除刷新定时器 */
    clearRefreshTimer() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },
    
    /** 控制设备 */
    controlDevice(doorId, action) {
      this.$confirm(`确定要${action === 'unlock' ? '开锁' : '锁定'}设备 ${doorId} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        controlDevice({ doorId, action }).then(() => {
          this.$message.success('控制成功')
          this.refreshDeviceStatus()
        })
      })
    },
    
    /** 显示设备详情 */
    showDeviceDetail(device) {
      this.currentDevice = device
      this.deviceDetailVisible = true
    },
    
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    
    /** 查看操作 */
    handleView(row) {
      this.currentRecord = row
      this.detailDialogVisible = true
    },
    
    /** 查询历史数据 */
    queryHistoryData() {
      this.handleQuery()
    },
    
    /** 导出数据 */
    exportData() {
      this.download('dwms/access/export', {
        ...this.queryParams
      }, `access_record_${new Date().getTime()}.xlsx`)
    },
    
    /** 处理图片错误 */
    handleImageError(event) {
      event.target.style.display = 'none'
    },
    
    /** 格式化时间 */
    formatTime(time) {
      if (!time) return '-'
      return this.parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')
    }
  }
}
</script>

<style lang="scss" scoped>
.access-monitor {
  padding: 20px;

  .stats-cards {
    margin-bottom: 20px;

    .stat-card {
      display: flex;
      align-items: center;
      padding: 20px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);

      .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        background: #409EFF;
        color: #fff;
        font-size: 24px;

        &.warning {
          background: #E6A23C;
        }

        &.success {
          background: #67C23A;
        }

        &.info {
          background: #909399;
        }
      }

      .stat-content {
        flex: 1;

        .stat-value {
          font-size: 28px;
          font-weight: bold;
          color: #333;
          line-height: 1;
          margin-bottom: 8px;
        }

        .stat-label {
          font-size: 14px;
          color: #666;
        }
      }
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-actions {
      display: flex;
      align-items: center;
      gap: 16px;
    }
  }

  .device-monitor {
    margin-bottom: 20px;

    .device-card {
      background: #fff;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      padding: 16px;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        transform: translateY(-2px);
      }

      &.online {
        border-color: #67C23A;
        background: #f0f9ff;
      }

      &.offline {
        border-color: #F56C6C;
        background: #fef0f0;
      }

      .device-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .device-name {
          font-size: 16px;
          font-weight: 500;
          color: #333;
        }
      }

      .device-content {
        .device-info {
          margin-bottom: 12px;

          .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
            font-size: 12px;
            color: #666;

            i {
              margin-right: 4px;
              width: 14px;
            }
          }
        }
      }

      .device-actions {
        margin-top: 12px;
        text-align: center;
        border-top: 1px solid #f0f0f0;
        padding-top: 12px;
      }
    }
  }

  .realtime-access {
    margin-bottom: 20px;

    .empty-data {
      text-align: center;
      padding: 40px 0;
    }
  }

  .current-persons {
    margin-bottom: 20px;

    .person-card {
      display: flex;
      align-items: center;
      padding: 12px;
      background: #fff;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      }

      .person-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 12px;
        background: #f5f7fa;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        i {
          font-size: 20px;
          color: #999;
        }
      }

      .person-info {
        flex: 1;

        .person-name {
          font-size: 14px;
          font-weight: 500;
          color: #333;
          margin-bottom: 4px;
        }

        .person-dept {
          font-size: 12px;
          color: #666;
          margin-bottom: 4px;
        }

        .person-time {
          font-size: 11px;
          color: #999;
          display: flex;
          align-items: center;

          i {
            margin-right: 4px;
          }
        }
      }
    }

    .empty-data {
      text-align: center;
      padding: 40px 0;
    }
  }

  .history-records {
    .empty-data {
      text-align: center;
      padding: 40px 0;
    }
  }
}

.person-photo {
  margin-top: 20px;
  text-align: center;

  h4 {
    margin-bottom: 16px;
    color: #333;
  }

  img {
    max-width: 200px;
    max-height: 200px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  }
}

// 全局样式覆盖
::v-deep .el-card__header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

::v-deep .el-card__body {
  padding: 20px;
}

::v-deep .el-table {
  .el-table__row {
    &:hover {
      background-color: #f5f7fa;
    }
  }
}

::v-deep .el-descriptions {
  .el-descriptions__label {
    font-weight: 500;
  }
}
</style>
