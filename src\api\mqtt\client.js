import request from '@/utils/request'

// MQTT 客户端管理 API

/**
 * 获取在线客户端列表
 */
export function getOnlineClients(params) {
  return request({
    url: '/api/mqtt/clients',
    method: 'get',
    params
  })
}

/**
 * 获取客户端详情
 */
export function getClientDetails(clientId) {
  return request({
    url: `/api/mqtt/clients/${clientId}`,
    method: 'get'
  })
}

/**
 * 强制断开客户端连接
 */
export function disconnectClient(clientId, reason) {
  return request({
    url: `/api/mqtt/clients/${clientId}/disconnect`,
    method: 'post',
    data: { reason }
  })
}

/**
 * 获取客户端订阅列表
 */
export function getClientSubscriptions(clientId) {
  return request({
    url: `/api/mqtt/clients/${clientId}/subscriptions`,
    method: 'get'
  })
}

/**
 * 获取客户端会话信息
 */
export function getClientSession(clientId) {
  return request({
    url: `/api/mqtt/clients/${clientId}/session`,
    method: 'get'
  })
}

/**
 * 清理客户端会话
 */
export function clearClientSession(clientId) {
  return request({
    url: `/api/mqtt/clients/${clientId}/session`,
    method: 'delete'
  })
}

/**
 * 获取客户端统计信息
 */
export function getClientStatistics(clientId) {
  return request({
    url: `/api/mqtt/clients/${clientId}/statistics`,
    method: 'get'
  })
}

/**
 * 向客户端发送消息
 */
export function sendMessageToClient(clientId, data) {
  return request({
    url: `/api/mqtt/clients/${clientId}/send`,
    method: 'post',
    data
  })
}

/**
 * 获取客户端连接历史
 */
export function getClientHistory(clientId, params) {
  return request({
    url: `/api/mqtt/clients/${clientId}/history`,
    method: 'get',
    params
  })
}

/**
 * 批量断开客户端
 */
export function batchDisconnectClients(clientIds, reason) {
  return request({
    url: '/api/mqtt/clients/batch-disconnect',
    method: 'post',
    data: { clientIds, reason }
  })
}

/**
 * 搜索客户端
 */
export function searchClients(keyword, params) {
  return request({
    url: '/api/mqtt/clients/search',
    method: 'get',
    params: { keyword, ...params }
  })
}

/**
 * 导出客户端列表
 */
export function exportClients(params) {
  return request({
    url: '/api/mqtt/clients/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
