<template>
  <div class="navigation-container">
    <!-- 地图容器 -->
    <div class="map-container">
      <div ref="mapCanvas" class="map-canvas" @click="handleMapClick">
        <!-- 地图背景 -->
        <div class="map-background" :style="mapStyle">
          <!-- 区域 -->
          <div 
            v-for="area in areas" 
            :key="area.id"
            class="map-area"
            :class="'area-' + area.type"
            :style="getAreaStyle(area)"
            @click.stop="selectArea(area)"
          >
            <span class="area-label">{{ area.name }}</span>
          </div>
          
          <!-- 设备 -->
          <div 
            v-for="device in devices" 
            :key="device.deviceId"
            class="map-device"
            :class="'device-' + device.deviceType"
            :style="getDeviceStyle(device)"
            @click.stop="selectDevice(device)"
          >
            <i :class="getDeviceIcon(device.deviceType)"></i>
            <span class="device-label">{{ device.deviceName }}</span>
          </div>
          
          <!-- 路径 -->
          <svg v-if="currentPath" class="path-overlay" :width="mapWidth" :height="mapHeight">
            <path 
              :d="getPathSvg(currentPath)" 
              stroke="#409EFF" 
              stroke-width="3" 
              fill="none"
              stroke-dasharray="5,5"
            >
              <animate 
                attributeName="stroke-dashoffset" 
                values="0;10" 
                dur="1s" 
                repeatCount="indefinite"
              />
            </path>
          </svg>
          
          <!-- 当前位置 -->
          <div 
            v-if="currentLocation" 
            class="current-location"
            :style="getCurrentLocationStyle()"
          >
            <i class="el-icon-location"></i>
          </div>
          
          <!-- 地图标注 -->
          <div 
            v-for="marker in markers" 
            :key="marker.id"
            class="map-marker"
            :class="'marker-' + marker.type"
            :style="getMarkerStyle(marker)"
            @click.stop="selectMarker(marker)"
          >
            <i :class="marker.icon || 'el-icon-location-information'"></i>
          </div>
        </div>
      </div>
      
      <!-- 地图控制器 -->
      <div class="map-controls">
        <el-button-group>
          <el-button size="mini" icon="el-icon-plus" @click="zoomIn"></el-button>
          <el-button size="mini" icon="el-icon-minus" @click="zoomOut"></el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="resetView"></el-button>
        </el-button-group>
      </div>
    </div>
    
    <!-- 侧边栏 -->
    <div class="sidebar">
      <!-- 导航面板 -->
      <el-card class="navigation-panel" shadow="never">
        <div slot="header" class="panel-header">
          <span>智能导航</span>
          <el-button type="text" icon="el-icon-location" @click="getCurrentLocation">定位</el-button>
        </div>
        
        <!-- 路径规划 -->
        <div class="path-planning">
          <el-form :model="pathForm" size="small">
            <el-form-item label="起点">
              <el-select 
                v-model="pathForm.startPoint" 
                placeholder="选择起点"
                filterable
                @change="updatePath"
              >
                <el-option 
                  v-for="location in allLocations" 
                  :key="location.id"
                  :label="location.name" 
                  :value="location.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="终点">
              <el-select 
                v-model="pathForm.endPoint" 
                placeholder="选择终点"
                filterable
                @change="updatePath"
              >
                <el-option 
                  v-for="location in allLocations" 
                  :key="location.id"
                  :label="location.name" 
                  :value="location.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="算法">
              <el-select v-model="pathForm.algorithm" @change="updatePath">
                <el-option label="最短路径" value="dijkstra"></el-option>
                <el-option label="A*算法" value="astar"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-checkbox v-model="pathForm.avoidCongestion" @change="updatePath">
                避开拥堵
              </el-checkbox>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="planPath" :loading="pathLoading">
                规划路径
              </el-button>
              <el-button @click="clearPath">清除路径</el-button>
            </el-form-item>
          </el-form>
        </div>
        
        <!-- 路径信息 -->
        <div v-if="currentPath" class="path-info">
          <h4>路径信息</h4>
          <div class="info-item">
            <span>距离：</span>
            <span>{{ formatDistance(currentPath.distance) }}</span>
          </div>
          <div class="info-item">
            <span>预计时间：</span>
            <span>{{ currentPath.estimatedTime }}分钟</span>
          </div>
          <div class="info-item">
            <span>算法：</span>
            <span>{{ getAlgorithmName(currentPath.algorithm) }}</span>
          </div>
        </div>
        
        <!-- 导航指引 */
        <div v-if="navigationGuidance.length > 0" class="navigation-guidance">
          <h4>导航指引</h4>
          <div class="guidance-list">
            <div 
              v-for="(step, index) in navigationGuidance" 
              :key="index"
              class="guidance-step"
              :class="{ 'current-step': index === currentStep }"
            >
              <div class="step-number">{{ step.step }}</div>
              <div class="step-content">
                <div class="step-instruction">{{ step.instruction }}</div>
                <div class="step-distance">{{ formatDistance(step.distance) }}</div>
              </div>
            </div>
          </div>
        </div>
      </el-card>
      
      <!-- 搜索面板 -->
      <el-card class="search-panel" shadow="never">
        <div slot="header">位置搜索</div>
        
        <el-input
          v-model="searchKeyword"
          placeholder="搜索位置、设备、区域"
          prefix-icon="el-icon-search"
          @input="searchLocation"
          clearable
        ></el-input>
        
        <div v-if="searchResults.length > 0" class="search-results">
          <div 
            v-for="result in searchResults" 
            :key="result.id"
            class="search-result-item"
            @click="selectSearchResult(result)"
          >
            <i :class="getSearchResultIcon(result.type)"></i>
            <span>{{ result.name }}</span>
          </div>
        </div>
      </el-card>
      
      <!-- 热门目的地 -->
      <el-card class="popular-destinations" shadow="never">
        <div slot="header">热门目的地</div>
        
        <div class="destination-list">
          <div 
            v-for="destination in popularDestinations" 
            :key="destination.name"
            class="destination-item"
            @click="navigateToDestination(destination.name)"
          >
            <span class="destination-name">{{ destination.name }}</span>
            <span class="destination-count">{{ destination.visitCount }}次</span>
          </div>
        </div>
      </el-card>
      
      <!-- 导航历史 -->
      <el-card class="navigation-history" shadow="never">
        <div slot="header">导航历史</div>
        
        <div class="history-list">
          <div 
            v-for="record in navigationHistory" 
            :key="record.id"
            class="history-item"
            @click="repeatNavigation(record)"
          >
            <div class="history-route">
              <span>{{ record.startPoint }}</span>
              <i class="el-icon-right"></i>
              <span>{{ record.endPoint }}</span>
            </div>
            <div class="history-time">{{ formatTime(record.navigationTime) }}</div>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 详情对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="detailDialogVisible" width="500px">
      <div v-if="selectedItem">
        <el-descriptions :column="2" border>
          <el-descriptions-item v-for="(value, key) in selectedItem" :key="key" :label="key">
            {{ value }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  getWarehouseMap,
  getWarehouseAreas,
  getDeviceLocations,
  planPath,
  getNavigationGuidance,
  searchLocation,
  getCurrentLocation,
  getPopularDestinations,
  getNavigationHistory
} from '@/api/dwms/navigation'

export default {
  name: 'NavigationIndex',
  data() {
    return {
      // 地图相关
      mapWidth: 800,
      mapHeight: 600,
      mapScale: 1,
      mapOffsetX: 0,
      mapOffsetY: 0,
      
      // 地图数据
      warehouseMap: null,
      areas: [],
      devices: [],
      markers: [],
      allLocations: [],
      
      // 路径规划
      pathForm: {
        startPoint: '',
        endPoint: '',
        algorithm: 'dijkstra',
        avoidCongestion: false
      },
      currentPath: null,
      pathLoading: false,
      
      // 导航指引
      navigationGuidance: [],
      currentStep: 0,
      
      // 当前位置
      currentLocation: null,
      
      // 搜索
      searchKeyword: '',
      searchResults: [],
      
      // 热门目的地
      popularDestinations: [],
      
      // 导航历史
      navigationHistory: [],
      
      // 选中项
      selectedItem: null,
      dialogTitle: '',
      detailDialogVisible: false
    }
  },
  computed: {
    mapStyle() {
      return {
        width: this.mapWidth + 'px',
        height: this.mapHeight + 'px',
        transform: `scale(${this.mapScale}) translate(${this.mapOffsetX}px, ${this.mapOffsetY}px)`
      }
    }
  },
  mounted() {
    this.initMap()
    this.loadMapData()
    this.loadPopularDestinations()
    this.loadNavigationHistory()
  },
  methods: {
    // 初始化地图
    initMap() {
      const container = this.$refs.mapCanvas
      this.mapWidth = container.clientWidth
      this.mapHeight = container.clientHeight
    },
    
    // 加载地图数据
    async loadMapData() {
      try {
        // 获取库房地图
        const mapResponse = await getWarehouseMap(1) // 假设库房ID为1
        this.warehouseMap = mapResponse.data
        
        // 获取区域
        const areasResponse = await getWarehouseAreas(1)
        this.areas = areasResponse.data || []
        
        // 获取设备
        const devicesResponse = await getDeviceLocations(1)
        this.devices = devicesResponse.data || []
        
        // 构建所有位置列表
        this.buildAllLocations()
        
      } catch (error) {
        console.error('加载地图数据失败:', error)
      }
    },
    
    // 构建所有位置列表
    buildAllLocations() {
      this.allLocations = []
      
      // 添加区域
      this.areas.forEach(area => {
        this.allLocations.push({
          id: area.id,
          name: area.name,
          type: 'area'
        })
      })
      
      // 添加设备
      this.devices.forEach(device => {
        this.allLocations.push({
          id: device.deviceId,
          name: device.deviceName,
          type: 'device'
        })
      })
    },
    
    // 规划路径
    async planPath() {
      if (!this.pathForm.startPoint || !this.pathForm.endPoint) {
        this.$message.warning('请选择起点和终点')
        return
      }
      
      this.pathLoading = true
      try {
        const response = await planPath({
          warehouseId: 1,
          startPoint: this.pathForm.startPoint,
          endPoint: this.pathForm.endPoint,
          algorithm: this.pathForm.algorithm
        })
        
        this.currentPath = response.data
        await this.loadNavigationGuidance()
        
      } catch (error) {
        console.error('路径规划失败:', error)
        this.$message.error('路径规划失败')
      } finally {
        this.pathLoading = false
      }
    },
    
    // 更新路径
    updatePath() {
      if (this.pathForm.startPoint && this.pathForm.endPoint) {
        this.planPath()
      }
    },
    
    // 清除路径
    clearPath() {
      this.currentPath = null
      this.navigationGuidance = []
      this.currentStep = 0
    },
    
    // 加载导航指引
    async loadNavigationGuidance() {
      if (!this.currentPath) return
      
      try {
        const response = await getNavigationGuidance({
          warehouseId: 1,
          currentLocation: this.pathForm.startPoint,
          destination: this.pathForm.endPoint
        })
        
        this.navigationGuidance = response.data || []
        
      } catch (error) {
        console.error('加载导航指引失败:', error)
      }
    },
    
    // 搜索位置
    async searchLocation() {
      if (!this.searchKeyword.trim()) {
        this.searchResults = []
        return
      }
      
      try {
        const response = await searchLocation(1, this.searchKeyword)
        this.searchResults = response.data || []
        
      } catch (error) {
        console.error('搜索位置失败:', error)
      }
    },
    
    // 选择搜索结果
    selectSearchResult(result) {
      this.pathForm.endPoint = result.id
      this.searchResults = []
      this.searchKeyword = ''
      this.updatePath()
    },
    
    // 获取当前位置
    async getCurrentLocation() {
      try {
        const response = await getCurrentLocation()
        this.currentLocation = response.data
        
      } catch (error) {
        console.error('获取当前位置失败:', error)
      }
    },
    
    // 加载热门目的地
    async loadPopularDestinations() {
      try {
        const response = await getPopularDestinations(1)
        this.popularDestinations = response.data || []
        
      } catch (error) {
        console.error('加载热门目的地失败:', error)
      }
    },
    
    // 加载导航历史
    async loadNavigationHistory() {
      try {
        const response = await getNavigationHistory()
        this.navigationHistory = response.data || []
        
      } catch (error) {
        console.error('加载导航历史失败:', error)
      }
    },
    
    // 导航到目的地
    navigateToDestination(destination) {
      this.pathForm.endPoint = destination
      this.updatePath()
    },
    
    // 重复导航
    repeatNavigation(record) {
      this.pathForm.startPoint = record.startPoint
      this.pathForm.endPoint = record.endPoint
      this.updatePath()
    },
    
    // 地图交互
    handleMapClick(event) {
      const rect = event.target.getBoundingClientRect()
      const x = event.clientX - rect.left
      const y = event.clientY - rect.top
      
      console.log('地图点击位置:', { x, y })
    },
    
    selectArea(area) {
      this.selectedItem = area
      this.dialogTitle = '区域详情'
      this.detailDialogVisible = true
    },
    
    selectDevice(device) {
      this.selectedItem = device
      this.dialogTitle = '设备详情'
      this.detailDialogVisible = true
    },
    
    selectMarker(marker) {
      this.selectedItem = marker
      this.dialogTitle = '标注详情'
      this.detailDialogVisible = true
    },
    
    // 地图控制
    zoomIn() {
      this.mapScale = Math.min(this.mapScale * 1.2, 3)
    },
    
    zoomOut() {
      this.mapScale = Math.max(this.mapScale / 1.2, 0.5)
    },
    
    resetView() {
      this.mapScale = 1
      this.mapOffsetX = 0
      this.mapOffsetY = 0
    },
    
    // 样式计算
    getAreaStyle(area) {
      // TODO: 根据区域坐标计算样式
      return {
        left: '10px',
        top: '10px',
        width: '100px',
        height: '100px'
      }
    },
    
    getDeviceStyle(device) {
      return {
        left: device.x + 'px',
        top: device.y + 'px'
      }
    },
    
    getMarkerStyle(marker) {
      return {
        left: marker.x + 'px',
        top: marker.y + 'px'
      }
    },
    
    getCurrentLocationStyle() {
      if (!this.currentLocation) return {}
      
      return {
        left: this.currentLocation.x + 'px',
        top: this.currentLocation.y + 'px'
      }
    },
    
    getPathSvg(path) {
      if (!path || !path.pathPoints) return ''
      
      try {
        const points = JSON.parse(path.pathPoints)
        let pathData = ''
        
        points.forEach((point, index) => {
          if (index === 0) {
            pathData += `M ${point.x} ${point.y}`
          } else {
            pathData += ` L ${point.x} ${point.y}`
          }
        })
        
        return pathData
      } catch (error) {
        console.error('解析路径数据失败:', error)
        return ''
      }
    },
    
    // 图标和文本
    getDeviceIcon(deviceType) {
      const iconMap = {
        weight: 'el-icon-scale-to-original',
        access: 'el-icon-key',
        camera: 'el-icon-camera',
        beacon: 'el-icon-position'
      }
      return iconMap[deviceType] || 'el-icon-cpu'
    },
    
    getSearchResultIcon(type) {
      const iconMap = {
        area: 'el-icon-office-building',
        device: 'el-icon-cpu',
        marker: 'el-icon-location-information'
      }
      return iconMap[type] || 'el-icon-location'
    },
    
    getAlgorithmName(algorithm) {
      const nameMap = {
        dijkstra: '最短路径',
        astar: 'A*算法'
      }
      return nameMap[algorithm] || algorithm
    },
    
    // 格式化
    formatDistance(distance) {
      if (!distance) return '-'
      return distance.toFixed(1) + 'm'
    },
    
    formatTime(timestamp) {
      if (!timestamp) return '-'
      return this.parseTime(timestamp, '{y}-{m}-{d} {h}:{i}')
    }
  }
}
</script>

<style lang="scss" scoped>
.navigation-container {
  display: flex;
  height: calc(100vh - 84px);
  background: #f5f7fa;

  .map-container {
    flex: 1;
    position: relative;
    background: #fff;
    border-radius: 8px;
    margin: 20px;
    margin-right: 10px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);

    .map-canvas {
      width: 100%;
      height: 100%;
      position: relative;
      overflow: hidden;
      cursor: grab;

      &:active {
        cursor: grabbing;
      }

      .map-background {
        position: relative;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        transform-origin: 0 0;
        transition: transform 0.3s ease;

        .map-area {
          position: absolute;
          border: 2px solid #409EFF;
          border-radius: 4px;
          background: rgba(64, 158, 255, 0.1);
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background: rgba(64, 158, 255, 0.2);
            transform: scale(1.05);
          }

          &.area-storage {
            border-color: #67C23A;
            background: rgba(103, 194, 58, 0.1);

            &:hover {
              background: rgba(103, 194, 58, 0.2);
            }
          }

          &.area-office {
            border-color: #E6A23C;
            background: rgba(230, 162, 60, 0.1);

            &:hover {
              background: rgba(230, 162, 60, 0.2);
            }
          }

          .area-label {
            font-size: 12px;
            font-weight: 500;
            color: #333;
            text-align: center;
          }
        }

        .map-device {
          position: absolute;
          width: 24px;
          height: 24px;
          border-radius: 50%;
          background: #409EFF;
          color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            transform: scale(1.2);
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
          }

          &.device-weight {
            background: #67C23A;
          }

          &.device-access {
            background: #E6A23C;
          }

          &.device-camera {
            background: #F56C6C;
          }

          i {
            font-size: 12px;
          }

          .device-label {
            position: absolute;
            top: 26px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 10px;
            color: #333;
            white-space: nowrap;
            background: rgba(255,255,255,0.9);
            padding: 2px 4px;
            border-radius: 2px;
            display: none;
          }

          &:hover .device-label {
            display: block;
          }
        }

        .current-location {
          position: absolute;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background: #F56C6C;
          color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
          animation: pulse 2s infinite;

          i {
            font-size: 12px;
          }
        }

        .map-marker {
          position: absolute;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background: #909399;
          color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;

          &.marker-emergency {
            background: #F56C6C;
          }

          i {
            font-size: 10px;
          }
        }
      }

      .path-overlay {
        position: absolute;
        top: 0;
        left: 0;
        pointer-events: none;
        z-index: 10;
      }
    }

    .map-controls {
      position: absolute;
      top: 20px;
      right: 20px;
      z-index: 20;
    }
  }

  .sidebar {
    width: 350px;
    padding: 20px;
    padding-left: 10px;
    overflow-y: auto;

    .el-card {
      margin-bottom: 20px;

      .panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }

    .navigation-panel {
      .path-planning {
        margin-bottom: 20px;
      }

      .path-info {
        padding: 16px;
        background: #f5f7fa;
        border-radius: 4px;
        margin-bottom: 20px;

        h4 {
          margin: 0 0 12px 0;
          color: #333;
        }

        .info-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          font-size: 14px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }

      .navigation-guidance {
        h4 {
          margin: 0 0 12px 0;
          color: #333;
        }

        .guidance-list {
          max-height: 200px;
          overflow-y: auto;

          .guidance-step {
            display: flex;
            align-items: flex-start;
            padding: 8px;
            border-radius: 4px;
            margin-bottom: 8px;
            transition: all 0.3s ease;

            &.current-step {
              background: #e6f7ff;
              border: 1px solid #409EFF;
            }

            .step-number {
              width: 24px;
              height: 24px;
              border-radius: 50%;
              background: #409EFF;
              color: #fff;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 12px;
              margin-right: 12px;
              flex-shrink: 0;
            }

            .step-content {
              flex: 1;

              .step-instruction {
                font-size: 14px;
                color: #333;
                margin-bottom: 4px;
              }

              .step-distance {
                font-size: 12px;
                color: #666;
              }
            }
          }
        }
      }
    }

    .search-panel {
      .search-results {
        margin-top: 12px;
        max-height: 200px;
        overflow-y: auto;

        .search-result-item {
          display: flex;
          align-items: center;
          padding: 8px;
          border-radius: 4px;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background: #f5f7fa;
          }

          i {
            margin-right: 8px;
            color: #666;
          }
        }
      }
    }

    .popular-destinations {
      .destination-list {
        .destination-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px;
          border-radius: 4px;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background: #f5f7fa;
          }

          .destination-name {
            font-size: 14px;
            color: #333;
          }

          .destination-count {
            font-size: 12px;
            color: #666;
          }
        }
      }
    }

    .navigation-history {
      .history-list {
        max-height: 200px;
        overflow-y: auto;

        .history-item {
          padding: 8px;
          border-radius: 4px;
          cursor: pointer;
          transition: all 0.3s ease;
          margin-bottom: 8px;

          &:hover {
            background: #f5f7fa;
          }

          .history-route {
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #333;
            margin-bottom: 4px;

            i {
              margin: 0 8px;
              color: #666;
            }
          }

          .history-time {
            font-size: 12px;
            color: #666;
          }
        }
      }
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

// 全局样式覆盖
::v-deep .el-card__header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

::v-deep .el-card__body {
  padding: 20px;
}

::v-deep .el-form-item {
  margin-bottom: 16px;
}

::v-deep .el-form-item__label {
  font-weight: 500;
}
</style>
