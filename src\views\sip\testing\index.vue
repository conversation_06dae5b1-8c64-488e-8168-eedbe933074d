<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>SIP视频服务集成测试</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="refreshAll">刷新全部</el-button>
      </div>
      
      <!-- 服务状态检测 -->
      <el-row :gutter="20" style="margin-bottom: 20px;">
        <el-col :span="6">
          <el-card shadow="hover">
            <div class="status-card">
              <i :class="sipStatus.icon" :style="{color: sipStatus.color}"></i>
              <div>
                <h3>SIP服务器</h3>
                <p>{{ sipStatus.text }}</p>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card shadow="hover">
            <div class="status-card">
              <i :class="zlmStatus.icon" :style="{color: zlmStatus.color}"></i>
              <div>
                <h3>ZLMediaKit</h3>
                <p>{{ zlmStatus.text }}</p>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card shadow="hover">
            <div class="status-card">
              <i class="el-icon-setting" :style="{ color: getConfigStatusColor() }"></i>
              <div>
                <h3>配置状态</h3>
                <p :style="{ color: getConfigStatusColor() }">{{ configStatus }}</p>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card shadow="hover">
            <div class="status-card">
              <i class="el-icon-link" style="color: #E6A23C;"></i>
              <div>
                <h3>集成状态</h3>
                <p>{{ integrationStatus }}</p>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 测试功能区域 -->
      <el-tabs v-model="activeTab">
        <!-- SIP测试 -->
        <el-tab-pane label="SIP测试" name="sip">
          <el-card style="margin-bottom: 20px;">
            <div slot="header">
              <span>SIP服务器测试</span>
            </div>
            
            <!-- SIP配置信息 -->
            <el-descriptions :column="2" border style="margin-bottom: 20px;">
              <el-descriptions-item label="SIP服务器">{{ sipConfig.host }}:{{ sipConfig.port }}</el-descriptions-item>
              <el-descriptions-item label="协议版本">{{ sipConfig.version || 'SIP/2.0' }}</el-descriptions-item>
              <el-descriptions-item label="传输协议">{{ sipConfig.transport || 'UDP' }}</el-descriptions-item>
              <el-descriptions-item label="认证方式">{{ sipConfig.auth || 'Digest' }}</el-descriptions-item>
            </el-descriptions>
            
            <!-- SIP测试操作 -->
            <el-row :gutter="10">
              <el-col :span="4">
                <el-button type="primary" @click="testSipConnection" :loading="testing.sipConnection">
                  连接测试
                </el-button>
              </el-col>
              <el-col :span="4">
                <el-button type="success" @click="testSipRegister" :loading="testing.sipRegister">
                  注册测试
                </el-button>
              </el-col>
              <el-col :span="4">
                <el-button type="warning" @click="testSipInvite" :loading="testing.sipInvite">
                  呼叫测试
                </el-button>
              </el-col>
              <el-col :span="4">
                <el-button type="info" @click="testSipMessage" :loading="testing.sipMessage">
                  消息测试
                </el-button>
              </el-col>
            </el-row>
          </el-card>
        </el-tab-pane>
        
        <!-- ZLMediaKit测试 -->
        <el-tab-pane label="ZLMediaKit测试" name="zlm">
          <el-card style="margin-bottom: 20px;">
            <div slot="header">
              <span>ZLMediaKit流媒体测试</span>
            </div>
            
            <!-- ZLM配置信息 -->
            <el-descriptions :column="2" border style="margin-bottom: 20px;">
              <el-descriptions-item label="服务器地址">{{ zlmConfig.host }}:{{ zlmConfig.port }}</el-descriptions-item>
              <el-descriptions-item label="API密钥">{{ zlmConfig.apiSecret ? '已配置' : '未配置' }}</el-descriptions-item>
              <el-descriptions-item label="HTTPS启用">{{ zlmConfig.enableHttps ? '是' : '否' }}</el-descriptions-item>
              <el-descriptions-item label="版本信息">{{ zlmConfig.version || '获取中...' }}</el-descriptions-item>
            </el-descriptions>
            
            <!-- ZLM测试操作 -->
            <el-row :gutter="10">
              <el-col :span="4">
                <el-button type="primary" @click="testZlmConnection" :loading="testing.zlmConnection">
                  连接测试
                </el-button>
              </el-col>
              <el-col :span="4">
                <el-button type="success" @click="testZlmApi" :loading="testing.zlmApi">
                  API测试
                </el-button>
              </el-col>
              <el-col :span="4">
                <el-button type="warning" @click="testZlmStream" :loading="testing.zlmStream">
                  推流测试
                </el-button>
              </el-col>
              <el-col :span="4">
                <el-button type="info" @click="testZlmRecord" :loading="testing.zlmRecord">
                  录制测试
                </el-button>
              </el-col>
            </el-row>
          </el-card>
        </el-tab-pane>
        
        <!-- 集成测试 -->
        <el-tab-pane label="集成测试" name="integration">
          <el-card style="margin-bottom: 20px;">
            <div slot="header">
              <span>SIP + ZLMediaKit 集成测试</span>
            </div>
            
            <el-alert
              title="集成测试说明"
              type="info"
              description="此测试将验证SIP设备通过SIP协议注册后，能否正常通过ZLMediaKit进行视频流推送和播放。"
              style="margin-bottom: 20px;"
              show-icon>
            </el-alert>
            
            <!-- 集成测试步骤 -->
            <el-steps :active="integrationStep" finish-status="success" style="margin-bottom: 20px;">
              <el-step title="SIP设备注册" description="测试设备SIP注册"></el-step>
              <el-step title="媒体协商" description="SDP协商和媒体参数"></el-step>
              <el-step title="流媒体推送" description="视频流推送到ZLMediaKit"></el-step>
              <el-step title="播放验证" description="验证流媒体播放"></el-step>
            </el-steps>
            
            <!-- 集成测试操作 -->
            <el-row :gutter="10">
              <el-col :span="6">
                <el-button type="primary" @click="startIntegrationTest" :loading="testing.integration">
                  开始集成测试
                </el-button>
              </el-col>
              <el-col :span="6">
                <el-button type="warning" @click="stopIntegrationTest" :disabled="!testing.integration">
                  停止测试
                </el-button>
              </el-col>
              <el-col :span="6">
                <el-button type="info" @click="resetIntegrationTest">
                  重置测试
                </el-button>
              </el-col>
            </el-row>
          </el-card>
        </el-tab-pane>
      </el-tabs>

      <!-- 测试结果日志 -->
      <el-card>
        <div slot="header">
          <span>测试日志</span>
          <el-button style="float: right;" size="mini" @click="clearLogs">清空日志</el-button>
        </div>
        <div class="log-container">
          <div v-for="(log, index) in testLogs" :key="index" :class="['log-item', log.type]">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
          <div v-if="testLogs.length === 0" class="no-logs">
            暂无测试日志
          </div>
        </div>
      </el-card>
    </el-card>
  </div>
</template>

<script>
import {
  getSipTestStatus,
  getZlmTestStatus,
  testSipConnection,
  testSipRegister,
  testSipInvite,
  testSipMessage,
  testZlmConnection,
  testZlmApi,
  testZlmStream,
  testZlmRecord,
  startIntegrationTest,
  stopIntegrationTest
} from '@/api/sip/testing'

export default {
  name: 'SipSystemTesting',
  data() {
    return {
      activeTab: 'sip',
      integrationStep: 0,
      
      // 服务状态
      sipStatus: {
        icon: 'el-icon-loading',
        color: '#909399',
        text: '检测中...'
      },
      zlmStatus: {
        icon: 'el-icon-loading',
        color: '#909399',
        text: '检测中...'
      },
      configStatus: '检测中...',
      integrationStatus: '未测试',
      
      // 配置信息
      sipConfig: {
        host: 'localhost',
        port: 5060,
        version: 'SIP/2.0',
        transport: 'UDP',
        auth: 'Digest'
      },
      zlmConfig: {
        host: 'localhost',
        port: 8080,
        apiSecret: '',
        enableHttps: false,
        version: ''
      },
      
      // 测试状态
      testing: {
        sipConnection: false,
        sipRegister: false,
        sipInvite: false,
        sipMessage: false,
        zlmConnection: false,
        zlmApi: false,
        zlmStream: false,
        zlmRecord: false,
        integration: false
      },
      
      // 测试日志
      testLogs: []
    }
  },
  
  created() {
    this.checkAllStatus()
  },
  
  methods: {
    async checkAllStatus() {
      await Promise.all([
        this.checkSipStatus(),
        this.checkZlmStatus()
      ])
    },
    
    async checkSipStatus() {
      try {
        const response = await getSipTestStatus()
        if (response.code === 200) {
          this.sipStatus = {
            icon: 'el-icon-success',
            color: '#67C23A',
            text: '在线'
          }
          this.sipConfig = { ...this.sipConfig, ...response.data }
        } else {
          this.sipStatus = {
            icon: 'el-icon-warning',
            color: '#E6A23C',
            text: '未授权'
          }
        }
      } catch (error) {
        this.sipStatus = {
          icon: 'el-icon-error',
          color: '#F56C6C',
          text: '离线'
        }
        this.configStatus = '异常'
        console.warn('SIP状态检查异常:', error.message)
      }
    },

    async checkZlmStatus() {
      try {
        const response = await getZlmTestStatus()
        if (response.code === 200) {
          this.zlmStatus = {
            icon: 'el-icon-success',
            color: '#67C23A',
            text: '在线'
          }
          this.zlmConfig = { ...this.zlmConfig, ...response.data }
        } else {
          this.zlmStatus = {
            icon: 'el-icon-warning',
            color: '#E6A23C',
            text: '未授权'
          }
        }
      } catch (error) {
        this.zlmStatus = {
          icon: 'el-icon-error',
          color: '#F56C6C',
          text: '离线'
        }
        this.configStatus = '异常'
        console.warn('ZLM状态检查异常:', error.message)
      }
    },
    
    getConfigStatusColor() {
      if (this.configStatus === '正常') return '#67C23A'
      if (this.configStatus === '异常') return '#F56C6C'
      return '#E6A23C'
    },
    
    // SIP测试方法
    async testSipConnection() {
      this.testing.sipConnection = true
      this.addLog('info', '开始SIP连接测试...')

      try {
        const response = await testSipConnection()
        if (response.code === 200) {
          this.addLog('success', 'SIP连接测试成功')
        } else {
          this.addLog('error', `SIP连接测试失败: ${response.msg}`)
        }
      } catch (error) {
        this.addLog('error', `SIP连接测试异常: ${error.message}`)
      } finally {
        this.testing.sipConnection = false
      }
    },
    
    async testSipRegister() {
      this.testing.sipRegister = true
      this.addLog('info', '开始SIP注册测试...')

      try {
        const response = await testSipRegister()
        if (response.code === 200) {
          this.addLog('success', 'SIP注册测试成功')
        } else {
          this.addLog('error', `SIP注册测试失败: ${response.msg}`)
        }
      } catch (error) {
        this.addLog('error', `SIP注册测试异常: ${error.message}`)
      } finally {
        this.testing.sipRegister = false
      }
    },

    async testSipInvite() {
      this.testing.sipInvite = true
      this.addLog('info', '开始SIP呼叫测试...')

      try {
        const response = await testSipInvite()
        if (response.code === 200) {
          this.addLog('success', 'SIP呼叫测试成功')
        } else {
          this.addLog('error', `SIP呼叫测试失败: ${response.msg}`)
        }
      } catch (error) {
        this.addLog('error', `SIP呼叫测试异常: ${error.message}`)
      } finally {
        this.testing.sipInvite = false
      }
    },

    async testSipMessage() {
      this.testing.sipMessage = true
      this.addLog('info', '开始SIP消息测试...')

      try {
        const response = await testSipMessage()
        if (response.code === 200) {
          this.addLog('success', 'SIP消息测试成功')
        } else {
          this.addLog('error', `SIP消息测试失败: ${response.msg}`)
        }
      } catch (error) {
        this.addLog('error', `SIP消息测试异常: ${error.message}`)
      } finally {
        this.testing.sipMessage = false
      }
    },
    
    // ZLMediaKit测试方法
    async testZlmConnection() {
      this.testing.zlmConnection = true
      this.addLog('info', '开始ZLMediaKit连接测试...')

      try {
        const response = await testZlmConnection()
        if (response.code === 200) {
          this.addLog('success', 'ZLMediaKit连接测试成功')
        } else {
          this.addLog('error', `ZLMediaKit连接测试失败: ${response.msg}`)
        }
      } catch (error) {
        this.addLog('error', `ZLMediaKit连接测试异常: ${error.message}`)
      } finally {
        this.testing.zlmConnection = false
      }
    },

    async testZlmApi() {
      this.testing.zlmApi = true
      this.addLog('info', '开始ZLMediaKit API测试...')

      try {
        const response = await testZlmApi()
        if (response.code === 200) {
          this.addLog('success', 'ZLMediaKit API测试成功')
        } else {
          this.addLog('error', `ZLMediaKit API测试失败: ${response.msg}`)
        }
      } catch (error) {
        this.addLog('error', `ZLMediaKit API测试异常: ${error.message}`)
      } finally {
        this.testing.zlmApi = false
      }
    },

    async testZlmStream() {
      this.testing.zlmStream = true
      this.addLog('info', '开始ZLMediaKit推流测试...')

      try {
        const response = await testZlmStream()
        if (response.code === 200) {
          this.addLog('success', 'ZLMediaKit推流测试成功')
        } else {
          this.addLog('error', `ZLMediaKit推流测试失败: ${response.msg}`)
        }
      } catch (error) {
        this.addLog('error', `ZLMediaKit推流测试异常: ${error.message}`)
      } finally {
        this.testing.zlmStream = false
      }
    },

    async testZlmRecord() {
      this.testing.zlmRecord = true
      this.addLog('info', '开始ZLMediaKit录制测试...')

      try {
        const response = await testZlmRecord()
        if (response.code === 200) {
          this.addLog('success', 'ZLMediaKit录制测试成功')
        } else {
          this.addLog('error', `ZLMediaKit录制测试失败: ${response.msg}`)
        }
      } catch (error) {
        this.addLog('error', `ZLMediaKit录制测试异常: ${error.message}`)
      } finally {
        this.testing.zlmRecord = false
      }
    },
    
    // 集成测试方法
    async startIntegrationTest() {
      this.testing.integration = true
      this.integrationStep = 0
      this.addLog('info', '开始SIP+ZLMediaKit集成测试...')
      
      try {
        // 步骤1: SIP设备注册
        this.integrationStep = 1
        this.addLog('info', '步骤1: 执行SIP设备注册...')
        await this.delay(2000)
        
        // 步骤2: 媒体协商
        this.integrationStep = 2
        this.addLog('info', '步骤2: 执行媒体协商...')
        await this.delay(2000)
        
        // 步骤3: 流媒体推送
        this.integrationStep = 3
        this.addLog('info', '步骤3: 执行流媒体推送...')
        await this.delay(3000)
        
        // 步骤4: 播放验证
        this.integrationStep = 4
        this.addLog('info', '步骤4: 执行播放验证...')
        await this.delay(2000)
        
        this.addLog('success', '集成测试完成！')
        this.integrationStatus = '测试通过'
        
      } catch (error) {
        this.addLog('error', `集成测试失败: ${error.message}`)
        this.integrationStatus = '测试失败'
      } finally {
        this.testing.integration = false
      }
    },
    
    stopIntegrationTest() {
      this.testing.integration = false
      this.addLog('warning', '集成测试已停止')
      this.integrationStatus = '测试中断'
    },
    
    resetIntegrationTest() {
      this.integrationStep = 0
      this.integrationStatus = '未测试'
      this.addLog('info', '集成测试已重置')
    },
    
    // 工具方法
    addLog(type, message) {
      const now = new Date()
      const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
      
      this.testLogs.unshift({
        type,
        time,
        message
      })
      
      // 限制日志数量
      if (this.testLogs.length > 100) {
        this.testLogs = this.testLogs.slice(0, 100)
      }
    },
    
    clearLogs() {
      this.testLogs = []
    },
    
    refreshAll() {
      this.checkAllStatus()
      this.addLog('info', '已刷新所有状态')
    },
    
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.status-card {
  display: flex;
  align-items: center;
  padding: 20px;

  i {
    font-size: 32px;
    margin-right: 15px;
  }

  h3 {
    margin: 0 0 5px 0;
    font-size: 16px;
    color: #303133;
  }

  p {
    margin: 0;
    font-size: 14px;
    color: #606266;
  }
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  padding: 10px;
  background: #fafafa;

  .log-item {
    display: flex;
    margin-bottom: 8px;
    padding: 5px 8px;
    border-radius: 4px;
    font-size: 13px;

    .log-time {
      color: #909399;
      margin-right: 10px;
      min-width: 60px;
    }

    .log-message {
      flex: 1;
    }

    &.info {
      background: #f0f9ff;
      border-left: 3px solid #409EFF;

      .log-message {
        color: #409EFF;
      }
    }

    &.success {
      background: #f0f9f0;
      border-left: 3px solid #67C23A;

      .log-message {
        color: #67C23A;
      }
    }

    &.warning {
      background: #fdf6ec;
      border-left: 3px solid #E6A23C;

      .log-message {
        color: #E6A23C;
      }
    }

    &.error {
      background: #fef0f0;
      border-left: 3px solid #F56C6C;

      .log-message {
        color: #F56C6C;
      }
    }
  }

  .no-logs {
    text-align: center;
    color: #909399;
    padding: 20px;
    font-style: italic;
  }
}

.el-steps {
  margin: 20px 0;
}

.el-tabs {
  .el-tab-pane {
    min-height: 400px;
  }
}

.el-descriptions {
  margin-bottom: 20px;
}

.el-alert {
  margin-bottom: 20px;
}
</style>
