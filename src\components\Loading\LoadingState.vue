<template>
  <div class="loading-state" :class="loadingClasses" v-if="visible">
    <!-- 企业化全屏加载 -->
    <div class="loading-overlay" v-if="type === 'fullscreen'">
      <!-- 动态背景 -->
      <div class="loading-background">
        <!-- 化工分子动画 -->
        <div class="molecule-container">
          <div class="molecule molecule-1">
            <div class="atom atom-center"></div>
            <div class="atom atom-1"></div>
            <div class="atom atom-2"></div>
            <div class="atom atom-3"></div>
            <div class="bond bond-1"></div>
            <div class="bond bond-2"></div>
            <div class="bond bond-3"></div>
          </div>
          <div class="molecule molecule-2">
            <div class="atom atom-center"></div>
            <div class="atom atom-1"></div>
            <div class="atom atom-2"></div>
            <div class="bond bond-1"></div>
            <div class="bond bond-2"></div>
          </div>
        </div>

        <!-- 数据流动画 -->
        <div class="data-flow">
          <div class="flow-line flow-1"></div>
          <div class="flow-line flow-2"></div>
          <div class="flow-line flow-3"></div>
          <div class="flow-dot dot-1"></div>
          <div class="flow-dot dot-2"></div>
          <div class="flow-dot dot-3"></div>
        </div>

        <!-- 网格背景 -->
        <div class="grid-bg"></div>
      </div>

      <div class="loading-content">
        <!-- 企业LOGO -->
        <div class="loading-logo">
          <svg-icon icon-class="ruiyun-logo-large" class="logo-svg" />
        </div>

        <!-- 主加载动画 -->
        <div class="loading-spinner" :class="spinnerType">
          <div v-if="spinnerType === 'chemical'" class="chemical-spinner">
            <div class="chemical-core">
              <div class="core-center"></div>
              <div class="electron electron-1"></div>
              <div class="electron electron-2"></div>
              <div class="electron electron-3"></div>
              <div class="orbit orbit-1"></div>
              <div class="orbit orbit-2"></div>
              <div class="orbit orbit-3"></div>
            </div>
          </div>
          <div v-else-if="spinnerType === 'enterprise'" class="enterprise-spinner">
            <div class="enterprise-ring ring-1"></div>
            <div class="enterprise-ring ring-2"></div>
            <div class="enterprise-ring ring-3"></div>
            <div class="enterprise-center">
              <div class="center-dot"></div>
            </div>
          </div>
          <div v-else-if="spinnerType === 'dots'" class="dots-spinner">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
          </div>
          <div v-else-if="spinnerType === 'circle'" class="circle-spinner">
            <div class="circle"></div>
          </div>
          <div v-else-if="spinnerType === 'wave'" class="wave-spinner">
            <div class="wave"></div>
            <div class="wave"></div>
            <div class="wave"></div>
            <div class="wave"></div>
            <div class="wave"></div>
          </div>
          <div v-else-if="spinnerType === 'pulse'" class="pulse-spinner">
            <div class="pulse"></div>
          </div>
          <div v-else class="default-spinner">
            <i class="el-icon-loading"></i>
          </div>
        </div>

        <!-- 加载文本 -->
        <div class="loading-text" v-if="text">{{ text }}</div>
        <div class="loading-subtitle">正在为您加载睿云管理系统</div>

        <!-- 进度条 -->
        <div class="loading-progress" v-if="showProgress">
          <div class="progress-container">
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: progress + '%' }"></div>
              <div class="progress-glow" :style="{ left: progress + '%' }"></div>
            </div>
            <div class="progress-text">{{ progress }}%</div>
          </div>
        </div>

        <!-- 加载状态指示 -->
        <div class="loading-status">
          <div class="status-dots">
            <div class="status-dot" :class="{ active: progress > 0 }"></div>
            <div class="status-dot" :class="{ active: progress > 33 }"></div>
            <div class="status-dot" :class="{ active: progress > 66 }"></div>
            <div class="status-dot" :class="{ active: progress > 99 }"></div>
          </div>
          <div class="status-text">
            <span v-if="progress <= 33">初始化系统</span>
            <span v-else-if="progress <= 66">加载核心模块</span>
            <span v-else-if="progress <= 99">准备用户界面</span>
            <span v-else>即将完成</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 内联加载 -->
    <div class="loading-inline" v-else-if="type === 'inline'">
      <div class="inline-spinner" :class="spinnerType">
        <i class="el-icon-loading" v-if="spinnerType === 'default'"></i>
        <div v-else-if="spinnerType === 'dots'" class="mini-dots">
          <div class="mini-dot"></div>
          <div class="mini-dot"></div>
          <div class="mini-dot"></div>
        </div>
      </div>
      <span class="inline-text" v-if="text">{{ text }}</span>
    </div>
    
    <!-- 按钮加载 -->
    <div class="loading-button" v-else-if="type === 'button'">
      <i class="el-icon-loading button-spinner"></i>
      <span v-if="text">{{ text }}</span>
    </div>
    
    <!-- 卡片加载 -->
    <div class="loading-card" v-else-if="type === 'card'">
      <div class="card-spinner">
        <div class="skeleton-line" v-for="n in skeletonLines" :key="n"></div>
      </div>
    </div>
    
    <!-- 表格加载 */
    <div class="loading-table" v-else-if="type === 'table'">
      <div class="table-skeleton">
        <div class="skeleton-row" v-for="n in skeletonRows" :key="n">
          <div class="skeleton-cell" v-for="m in skeletonCols" :key="m"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LoadingState',
  
  props: {
    // 是否显示
    visible: {
      type: Boolean,
      default: true
    },
    // 加载类型
    type: {
      type: String,
      default: 'fullscreen',
      validator: value => ['fullscreen', 'inline', 'button', 'card', 'table'].includes(value)
    },
    // 加载动画类型
    spinnerType: {
      type: String,
      default: 'default',
      validator: value => ['default', 'dots', 'circle', 'wave', 'pulse'].includes(value)
    },
    // 加载文本
    text: {
      type: String,
      default: ''
    },
    // 是否显示进度
    showProgress: {
      type: Boolean,
      default: false
    },
    // 进度值
    progress: {
      type: Number,
      default: 0,
      validator: value => value >= 0 && value <= 100
    },
    // 骨架屏行数
    skeletonLines: {
      type: Number,
      default: 3
    },
    // 骨架屏表格行数
    skeletonRows: {
      type: Number,
      default: 5
    },
    // 骨架屏表格列数
    skeletonCols: {
      type: Number,
      default: 4
    },
    // 自定义样式类
    customClass: {
      type: String,
      default: ''
    },
    // 背景色
    background: {
      type: String,
      default: 'rgba(255, 255, 255, 0.9)'
    },
    // 主题色
    color: {
      type: String,
      default: '#409EFF'
    }
  },
  
  computed: {
    loadingClasses() {
      return [
        `loading-${this.type}`,
        `spinner-${this.spinnerType}`,
        this.customClass
      ]
    }
  },
  
  mounted() {
    // 如果是全屏加载，阻止页面滚动
    if (this.type === 'fullscreen' && this.visible) {
      document.body.style.overflow = 'hidden'
    }
  },
  
  beforeDestroy() {
    // 恢复页面滚动
    if (this.type === 'fullscreen') {
      document.body.style.overflow = ''
    }
  },
  
  watch: {
    visible(newVal) {
      if (this.type === 'fullscreen') {
        document.body.style.overflow = newVal ? 'hidden' : ''
      }
    }
  }
}
</script>

<style scoped>
.loading-state {
  position: relative;
}

/* 全屏加载 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: v-bind(background);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  text-align: center;
  padding: 20px;
}

.loading-spinner {
  margin-bottom: 15px;
}

.loading-text {
  font-size: 14px;
  color: #606266;
  margin-bottom: 15px;
}

.loading-progress {
  width: 200px;
  margin: 0 auto;
}

.progress-text {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

/* 默认加载动画 */
.default-spinner i {
  font-size: 32px;
  color: v-bind(color);
  animation: rotate 2s linear infinite;
}

/* 点状加载动画 */
.dots-spinner {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: v-bind(color);
  animation: dotPulse 1.4s ease-in-out infinite both;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }

/* 圆形加载动画 */
.circle-spinner .circle {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid v-bind(color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 波浪加载动画 */
.wave-spinner {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.wave {
  width: 6px;
  height: 40px;
  background: v-bind(color);
  animation: wave 1.2s ease-in-out infinite;
}

.wave:nth-child(1) { animation-delay: -1.2s; }
.wave:nth-child(2) { animation-delay: -1.1s; }
.wave:nth-child(3) { animation-delay: -1.0s; }
.wave:nth-child(4) { animation-delay: -0.9s; }
.wave:nth-child(5) { animation-delay: -0.8s; }

/* 脉冲加载动画 */
.pulse-spinner .pulse {
  width: 40px;
  height: 40px;
  background: v-bind(color);
  border-radius: 50%;
  animation: pulse 1.5s ease-in-out infinite;
}

/* 内联加载 */
.loading-inline {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.inline-spinner i {
  font-size: 16px;
  color: v-bind(color);
  animation: rotate 2s linear infinite;
}

.mini-dots {
  display: flex;
  gap: 3px;
}

.mini-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: v-bind(color);
  animation: dotPulse 1.4s ease-in-out infinite both;
}

.mini-dot:nth-child(1) { animation-delay: -0.32s; }
.mini-dot:nth-child(2) { animation-delay: -0.16s; }

.inline-text {
  font-size: 14px;
  color: #606266;
}

/* 按钮加载 */
.loading-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.button-spinner {
  font-size: 14px;
  animation: rotate 2s linear infinite;
}

/* 卡片加载（骨架屏） */
.loading-card {
  padding: 20px;
}

.skeleton-line {
  height: 16px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton 1.5s ease-in-out infinite;
  border-radius: 4px;
  margin-bottom: 12px;
}

.skeleton-line:nth-child(1) { width: 100%; }
.skeleton-line:nth-child(2) { width: 80%; }
.skeleton-line:nth-child(3) { width: 60%; }

/* 表格加载（骨架屏） */
.loading-table {
  padding: 20px;
}

.table-skeleton {
  width: 100%;
}

.skeleton-row {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.skeleton-cell {
  flex: 1;
  height: 20px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton 1.5s ease-in-out infinite;
  border-radius: 4px;
}

/* 动画定义 */
@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes wave {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

@keyframes pulse {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

@keyframes skeleton {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
</style>
