<template>
  <div class="app-container">
    <!-- 实时状态概览 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon success">
            <i class="el-icon-connection"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ onlineDevices.length }}</div>
            <div class="stat-label">在线设备</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon danger">
            <i class="el-icon-warning"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ offlineDevices.length }}</div>
            <div class="stat-label">离线设备</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon warning">
            <i class="el-icon-time"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ timeoutDevices.length }}</div>
            <div class="stat-label">超时设备</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon info">
            <i class="el-icon-refresh"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ lastUpdateTime }}</div>
            <div class="stat-label">最后更新</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 设备状态列表 -->
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>设备状态监控</span>
        <el-button-group style="float: right;">
          <el-button size="mini" type="primary" icon="el-icon-refresh" @click="refreshData">刷新</el-button>
          <el-button size="mini" type="success" icon="el-icon-download" @click="exportData">导出</el-button>
          <el-button size="mini" :type="autoRefresh ? 'warning' : 'info'" @click="toggleAutoRefresh">
            {{ autoRefresh ? '停止自动刷新' : '开启自动刷新' }}
          </el-button>
        </el-button-group>
      </div>

      <!-- 筛选条件 -->
      <el-form :inline="true" :model="queryParams" class="demo-form-inline">
        <el-form-item label="设备类型">
          <el-select v-model="queryParams.deviceType" placeholder="请选择设备类型" clearable>
            <el-option label="人脸门禁" value="face_access"></el-option>
            <el-option label="IP磁力锁" value="ip_lock"></el-option>
            <el-option label="刷卡器" value="card_reader"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="在线状态">
          <el-select v-model="queryParams.onlineStatus" placeholder="请选择在线状态" clearable>
            <el-option label="在线" :value="1"></el-option>
            <el-option label="离线" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="库房">
          <el-select v-model="queryParams.warehouseId" placeholder="请选择库房" clearable>
            <el-option
              v-for="item in warehouseOptions"
              :key="item.warehouseId"
              :label="item.warehouseName"
              :value="item.warehouseId">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 设备状态表格 -->
      <el-table :data="filteredDeviceList" style="width: 100%" v-loading="loading">
        <el-table-column prop="deviceCode" label="设备编号" width="120"></el-table-column>
        <el-table-column prop="deviceName" label="设备名称" width="150" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="deviceType" label="设备类型" width="120">
          <template slot-scope="scope">
            <el-tag :type="getDeviceTypeColor(scope.row.deviceType)">
              {{ getDeviceTypeName(scope.row.deviceType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="installLocation" label="安装位置" width="150" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="onlineStatus" label="在线状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.onlineStatus === 1 ? 'success' : 'danger'">
              <i :class="scope.row.onlineStatus === 1 ? 'el-icon-success' : 'el-icon-error'"></i>
              {{ scope.row.onlineStatus === 1 ? '在线' : '离线' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="ipAddress" label="IP地址" width="120"></el-table-column>
        <el-table-column prop="lastHeartbeat" label="最后心跳" width="160">
          <template slot-scope="scope">
            <span :class="getHeartbeatClass(scope.row.lastHeartbeat)">
              {{ formatTime(scope.row.lastHeartbeat) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="lastOnlineTime" label="最后上线" width="160">
          <template slot-scope="scope">
            <span>{{ formatTime(scope.row.lastOnlineTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="连接时长" width="120">
          <template slot-scope="scope">
            <span>{{ getConnectionDuration(scope.row.lastOnlineTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-view" @click="showDeviceDetail(scope.row)">详情</el-button>
            <el-button size="mini" type="text" icon="el-icon-connection" @click="testConnection(scope.row)">测试</el-button>
            <el-button size="mini" type="text" icon="el-icon-refresh" @click="restartDevice(scope.row)">重启</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 设备详情对话框 -->
    <el-dialog title="设备详情" :visible.sync="detailDialogVisible" width="800px">
      <el-descriptions :column="2" border v-if="selectedDevice">
        <el-descriptions-item label="设备编号">{{ selectedDevice.deviceCode }}</el-descriptions-item>
        <el-descriptions-item label="设备名称">{{ selectedDevice.deviceName }}</el-descriptions-item>
        <el-descriptions-item label="设备类型">{{ getDeviceTypeName(selectedDevice.deviceType) }}</el-descriptions-item>
        <el-descriptions-item label="安装位置">{{ selectedDevice.installLocation }}</el-descriptions-item>
        <el-descriptions-item label="在线状态">
          <el-tag :type="selectedDevice.onlineStatus === 1 ? 'success' : 'danger'">
            {{ selectedDevice.onlineStatus === 1 ? '在线' : '离线' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="IP地址">{{ selectedDevice.ipAddress }}</el-descriptions-item>
        <el-descriptions-item label="端口">{{ selectedDevice.port }}</el-descriptions-item>
        <el-descriptions-item label="MQTT客户端ID">{{ selectedDevice.mqttClientId }}</el-descriptions-item>
        <el-descriptions-item label="设备厂商">{{ selectedDevice.manufacturer }}</el-descriptions-item>
        <el-descriptions-item label="设备型号">{{ selectedDevice.deviceModel }}</el-descriptions-item>
        <el-descriptions-item label="固件版本">{{ selectedDevice.firmwareVersion }}</el-descriptions-item>
        <el-descriptions-item label="最后心跳">{{ formatTime(selectedDevice.lastHeartbeat) }}</el-descriptions-item>
        <el-descriptions-item label="最后上线">{{ formatTime(selectedDevice.lastOnlineTime) }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatTime(selectedDevice.createTime) }}</el-descriptions-item>
      </el-descriptions>
      
      <div style="margin-top: 20px;">
        <h4>设备配置</h4>
        <el-input
          type="textarea"
          :rows="6"
          placeholder="设备配置信息"
          :value="formatDeviceConfig(selectedDevice.deviceConfig)"
          readonly>
        </el-input>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDevice, testDevice, restartDevice } from "@/api/access/device";
import { listWarehouse } from "@/api/warehouse/warehouse";

export default {
  name: "AccessDeviceMonitor",
  data() {
    return {
      loading: false,
      autoRefresh: false,
      refreshTimer: null,
      detailDialogVisible: false,
      selectedDevice: null,
      lastUpdateTime: '',
      deviceList: [],
      warehouseOptions: [],
      queryParams: {
        deviceType: null,
        onlineStatus: null,
        warehouseId: null
      }
    };
  },
  computed: {
    onlineDevices() {
      return this.deviceList.filter(device => device.onlineStatus === 1);
    },
    offlineDevices() {
      return this.deviceList.filter(device => device.onlineStatus === 0);
    },
    timeoutDevices() {
      const now = new Date();
      const timeoutMinutes = 5; // 5分钟超时
      return this.deviceList.filter(device => {
        if (!device.lastHeartbeat) return false;
        const heartbeatTime = new Date(device.lastHeartbeat);
        const diffMinutes = (now - heartbeatTime) / (1000 * 60);
        return diffMinutes > timeoutMinutes && device.onlineStatus === 1;
      });
    },
    filteredDeviceList() {
      let list = this.deviceList;
      
      if (this.queryParams.deviceType) {
        list = list.filter(device => device.deviceType === this.queryParams.deviceType);
      }
      
      if (this.queryParams.onlineStatus !== null) {
        list = list.filter(device => device.onlineStatus === this.queryParams.onlineStatus);
      }
      
      if (this.queryParams.warehouseId) {
        list = list.filter(device => device.warehouseId === this.queryParams.warehouseId);
      }
      
      return list;
    }
  },
  created() {
    this.getDeviceList();
    this.getWarehouseOptions();
  },
  beforeDestroy() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
    }
  },
  methods: {
    // 获取设备列表
    getDeviceList() {
      this.loading = true;
      listDevice().then(response => {
        this.deviceList = response.rows;
        this.lastUpdateTime = this.formatTime(new Date());
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    // 获取库房选项
    getWarehouseOptions() {
      listWarehouse().then(response => {
        this.warehouseOptions = response.rows;
      });
    },
    // 刷新数据
    refreshData() {
      this.getDeviceList();
      this.$message.success('数据已刷新');
    },
    // 切换自动刷新
    toggleAutoRefresh() {
      this.autoRefresh = !this.autoRefresh;
      
      if (this.autoRefresh) {
        this.refreshTimer = setInterval(() => {
          this.getDeviceList();
        }, 30000); // 30秒刷新一次
        this.$message.success('已开启自动刷新（30秒间隔）');
      } else {
        if (this.refreshTimer) {
          clearInterval(this.refreshTimer);
          this.refreshTimer = null;
        }
        this.$message.info('已停止自动刷新');
      }
    },
    // 查询
    handleQuery() {
      // 筛选在computed中处理
    },
    // 重置查询
    resetQuery() {
      this.queryParams = {
        deviceType: null,
        onlineStatus: null,
        warehouseId: null
      };
    },
    // 显示设备详情
    showDeviceDetail(device) {
      this.selectedDevice = device;
      this.detailDialogVisible = true;
    },
    // 测试连接
    testConnection(device) {
      this.$confirm('确定要测试设备"' + device.deviceName + '"的连接吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(() => {
        testDevice(device.deviceCode).then(response => {
          this.$message.success('连接测试成功');
        }).catch(() => {
          this.$message.error('连接测试失败');
        });
      });
    },
    // 重启设备
    restartDevice(device) {
      this.$confirm('确定要重启设备"' + device.deviceName + '"吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        restartDevice(device.deviceCode).then(response => {
          this.$message.success('重启指令已发送');
        }).catch(() => {
          this.$message.error('重启指令发送失败');
        });
      });
    },
    // 导出数据
    exportData() {
      this.download('access/device/monitor/export', {
        ...this.queryParams
      }, `设备状态监控_${new Date().getTime()}.xlsx`);
    },
    // 获取设备类型名称
    getDeviceTypeName(type) {
      const typeMap = {
        'face_access': '人脸门禁',
        'ip_lock': 'IP磁力锁',
        'card_reader': '刷卡器'
      };
      return typeMap[type] || type;
    },
    // 获取设备类型颜色
    getDeviceTypeColor(type) {
      const colorMap = {
        'face_access': 'primary',
        'ip_lock': 'success',
        'card_reader': 'info'
      };
      return colorMap[type] || '';
    },
    // 获取心跳时间样式
    getHeartbeatClass(heartbeat) {
      if (!heartbeat) return 'text-muted';
      
      const now = new Date();
      const heartbeatTime = new Date(heartbeat);
      const diffMinutes = (now - heartbeatTime) / (1000 * 60);
      
      if (diffMinutes > 5) return 'text-danger';
      if (diffMinutes > 2) return 'text-warning';
      return 'text-success';
    },
    // 获取连接时长
    getConnectionDuration(onlineTime) {
      if (!onlineTime) return '-';
      
      const now = new Date();
      const startTime = new Date(onlineTime);
      const diffMs = now - startTime;
      
      const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
      const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
      
      if (days > 0) return `${days}天${hours}小时`;
      if (hours > 0) return `${hours}小时${minutes}分钟`;
      return `${minutes}分钟`;
    },
    // 格式化时间
    formatTime(time) {
      if (!time) return '-';
      return this.parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}');
    },
    // 格式化设备配置
    formatDeviceConfig(config) {
      if (!config) return '无配置信息';
      try {
        return JSON.stringify(JSON.parse(config), null, 2);
      } catch (e) {
        return config;
      }
    }
  }
};
</script>

<style scoped>
.stats-cards {
  margin-bottom: 20px;
}
.stat-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  align-items: center;
}
.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #409EFF;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}
.stat-icon i {
  font-size: 30px;
}
.stat-content {
  flex: 1;
}
.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}
.stat-label {
  font-size: 14px;
  color: #606266;
}
.stat-icon.success {
  background-color: #67C23A;
}
.stat-icon.warning {
  background-color: #E6A23C;
}
.stat-icon.danger {
  background-color: #F56C6C;
}
.stat-icon.info {
  background-color: #909399;
}
.text-success {
  color: #67C23A;
}
.text-warning {
  color: #E6A23C;
}
.text-danger {
  color: #F56C6C;
}
.text-muted {
  color: #909399;
}
</style>
