<template>
  <div class="app-container">
    <!-- 预警统计卡片 -->
    <el-row :gutter="20" class="stats-cards" style="margin-bottom: 20px;">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total">
              <i class="el-icon-warning"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.totalWarns || 0 }}</div>
              <div class="stats-label">预警总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon pending">
              <i class="el-icon-time"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.pendingWarns || 0 }}</div>
              <div class="stats-label">待处理</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon today">
              <i class="el-icon-date"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.todayWarns || 0 }}</div>
              <div class="stats-label">今日预警</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon rate">
              <i class="el-icon-success"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.handleRate || '0%' }}</div>
              <div class="stats-label">处理率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="预警类型" prop="warnType">
        <el-select v-model="queryParams.warnType" placeholder="请选择预警类型" clearable>
          <el-option label="重量异常" value="weight_abnormal" />
          <el-option label="数量不符" value="quantity_mismatch" />
          <el-option label="物料错误" value="material_error" />
          <el-option label="权限不足" value="permission_denied" />
          <el-option label="设备故障" value="device_fault" />
        </el-select>
      </el-form-item>
      <el-form-item label="预警级别" prop="warnLevel">
        <el-select v-model="queryParams.warnLevel" placeholder="请选择预警级别" clearable>
          <el-option label="严重" value="critical" />
          <el-option label="重要" value="major" />
          <el-option label="一般" value="minor" />
          <el-option label="提示" value="info" />
        </el-select>
      </el-form-item>
      <el-form-item label="处理状态" prop="handleStatus">
        <el-select v-model="queryParams.handleStatus" placeholder="请选择处理状态" clearable>
          <el-option label="待处理" value="pending" />
          <el-option label="处理中" value="processing" />
          <el-option label="已处理" value="handled" />
          <el-option label="已忽略" value="ignored" />
        </el-select>
      </el-form-item>
      <el-form-item label="预警时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-check"
          size="mini"
          :disabled="multiple"
          @click="handleBatchProcess"
          v-hasPermi="['outmis:warn:handle']"
        >批量处理</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-setting"
          size="mini"
          @click="handleConfig"
          v-hasPermi="['outmis:warn:config']"
        >预警配置</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-pie-chart"
          size="mini"
          @click="handleStats"
          v-hasPermi="['outmis:warn:stats']"
        >统计分析</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 预警列表 -->
    <el-table v-loading="loading" :data="warnList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="预警类型" align="center" prop="warnType">
        <template slot-scope="scope">
          <el-tag :type="getWarnTypeTag(scope.row.warnType)">
            {{ getWarnTypeLabel(scope.row.warnType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="预警级别" align="center" prop="warnLevel">
        <template slot-scope="scope">
          <el-tag :type="getWarnLevelTag(scope.row.warnLevel)">
            {{ getWarnLevelLabel(scope.row.warnLevel) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="预警内容" align="center" prop="warnContent" :show-overflow-tooltip="true" />
      <el-table-column label="相关设备" align="center" prop="deviceCode" />
      <el-table-column label="物料信息" align="center" prop="materialInfo" :show-overflow-tooltip="true" />
      <el-table-column label="处理状态" align="center" prop="handleStatus">
        <template slot-scope="scope">
          <el-tag :type="getHandleStatusTag(scope.row.handleStatus)">
            {{ getHandleStatusLabel(scope.row.handleStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="预警时间" align="center" prop="warnTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.warnTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="处理人" align="center" prop="handler" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['outmis:warn:query']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleProcess(scope.row)"
            v-hasPermi="['outmis:warn:handle']"
            v-if="scope.row.handleStatus === 'pending'"
          >处理</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-close"
            @click="handleIgnore(scope.row)"
            v-if="scope.row.handleStatus === 'pending'"
          >忽略</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 预警详情对话框 -->
    <el-dialog title="预警详情" :visible.sync="detailOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="预警ID">{{ detailData.warnId }}</el-descriptions-item>
        <el-descriptions-item label="预警类型">{{ getWarnTypeLabel(detailData.warnType) }}</el-descriptions-item>
        <el-descriptions-item label="预警级别">{{ getWarnLevelLabel(detailData.warnLevel) }}</el-descriptions-item>
        <el-descriptions-item label="相关设备">{{ detailData.deviceCode }}</el-descriptions-item>
        <el-descriptions-item label="物料信息">{{ detailData.materialInfo }}</el-descriptions-item>
        <el-descriptions-item label="处理状态">{{ getHandleStatusLabel(detailData.handleStatus) }}</el-descriptions-item>
        <el-descriptions-item label="预警时间">{{ parseTime(detailData.warnTime) }}</el-descriptions-item>
        <el-descriptions-item label="处理人">{{ detailData.handler || '未处理' }}</el-descriptions-item>
      </el-descriptions>
      <el-divider content-position="left">预警内容</el-divider>
      <p>{{ detailData.warnContent }}</p>
      <el-divider content-position="left" v-if="detailData.handleRemark">处理备注</el-divider>
      <p v-if="detailData.handleRemark">{{ detailData.handleRemark }}</p>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 处理预警对话框 -->
    <el-dialog title="处理预警" :visible.sync="processOpen" width="500px" append-to-body>
      <el-form ref="processForm" :model="processForm" :rules="processRules" label-width="80px">
        <el-form-item label="处理方式" prop="handleType">
          <el-radio-group v-model="processForm.handleType">
            <el-radio label="resolve">解决</el-radio>
            <el-radio label="ignore">忽略</el-radio>
            <el-radio label="escalate">升级</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="处理备注" prop="handleRemark">
          <el-input v-model="processForm.handleRemark" type="textarea" placeholder="请输入处理备注" :rows="4" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitProcess">确 定</el-button>
        <el-button @click="processOpen = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "OutmisWarn",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 预警表格数据
      warnList: [],
      // 日期范围
      dateRange: [],
      // 详情对话框
      detailOpen: false,
      // 详情数据
      detailData: {},
      // 处理对话框
      processOpen: false,
      // 处理表单
      processForm: {},
      // 处理表单校验
      processRules: {
        handleType: [
          { required: true, message: "处理方式不能为空", trigger: "change" }
        ],
        handleRemark: [
          { required: true, message: "处理备注不能为空", trigger: "blur" }
        ]
      },
      // 统计信息
      statistics: {
        totalWarns: 0,
        pendingWarns: 0,
        todayWarns: 0,
        handleRate: '0%'
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        warnType: null,
        warnLevel: null,
        handleStatus: null
      }
    };
  },
  created() {
    this.getList();
    this.getStatistics();
  },
  methods: {
    /** 查询预警列表 */
    getList() {
      this.loading = true;
      // 模拟数据
      this.warnList = [
        {
          warnId: 1,
          warnType: "weight_abnormal",
          warnLevel: "major",
          warnContent: "称重值125.6kg超出标准范围(100-120kg)",
          deviceCode: "WEIGHT_001",
          materialInfo: "钢材A型 (MAT001)",
          handleStatus: "pending",
          warnTime: new Date(),
          handler: null,
          handleRemark: null
        },
        {
          warnId: 2,
          warnType: "material_error",
          warnLevel: "critical",
          warnContent: "扫描物料编码MAT002与计划物料MAT001不匹配",
          deviceCode: "RFID_001",
          materialInfo: "铝材B型 (MAT002)",
          handleStatus: "handled",
          warnTime: new Date(Date.now() - 3600000),
          handler: "张三",
          handleRemark: "已确认物料正确，更新计划"
        }
      ];
      this.total = this.warnList.length;
      this.loading = false;
    },
    /** 获取统计数据 */
    getStatistics() {
      this.statistics = {
        totalWarns: 156,
        pendingWarns: 23,
        todayWarns: 12,
        handleRate: '85.3%'
      };
    },
    /** 获取预警类型标签 */
    getWarnTypeTag(type) {
      const tags = {
        'weight_abnormal': 'warning',
        'quantity_mismatch': 'danger',
        'material_error': 'danger',
        'permission_denied': 'info',
        'device_fault': 'danger'
      };
      return tags[type] || 'info';
    },
    /** 获取预警类型标签 */
    getWarnTypeLabel(type) {
      const labels = {
        'weight_abnormal': '重量异常',
        'quantity_mismatch': '数量不符',
        'material_error': '物料错误',
        'permission_denied': '权限不足',
        'device_fault': '设备故障'
      };
      return labels[type] || type;
    },
    /** 获取预警级别标签 */
    getWarnLevelTag(level) {
      const tags = {
        'critical': 'danger',
        'major': 'warning',
        'minor': 'primary',
        'info': 'info'
      };
      return tags[level] || 'info';
    },
    /** 获取预警级别标签 */
    getWarnLevelLabel(level) {
      const labels = {
        'critical': '严重',
        'major': '重要',
        'minor': '一般',
        'info': '提示'
      };
      return labels[level] || level;
    },
    /** 获取处理状态标签 */
    getHandleStatusTag(status) {
      const tags = {
        'pending': 'warning',
        'processing': 'primary',
        'handled': 'success',
        'ignored': 'info'
      };
      return tags[status] || 'info';
    },
    /** 获取处理状态标签 */
    getHandleStatusLabel(status) {
      const labels = {
        'pending': '待处理',
        'processing': '处理中',
        'handled': '已处理',
        'ignored': '已忽略'
      };
      return labels[status] || status;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.warnId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 批量处理按钮操作 */
    handleBatchProcess() {
      const warnIds = this.ids;
      this.$modal.confirm('是否确认批量处理选中的预警?').then(function() {
        console.log('批量处理预警:', warnIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("批量处理成功");
      }).catch(() => {});
    },
    /** 预警配置按钮操作 */
    handleConfig() {
      this.$modal.msgInfo("预警配置功能开发中...");
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$modal.confirm('是否确认导出所有预警数据项?').then(() => {
        console.log('导出预警数据');
        this.$modal.msgSuccess("导出成功");
      }).catch(() => {});
    },
    /** 统计分析按钮操作 */
    handleStats() {
      this.$modal.msgInfo("统计分析功能开发中...");
    },
    /** 查看详情按钮操作 */
    handleView(row) {
      this.detailData = row;
      this.detailOpen = true;
    },
    /** 处理预警按钮操作 */
    handleProcess(row) {
      this.processForm = {
        warnId: row.warnId,
        handleType: 'resolve',
        handleRemark: ''
      };
      this.processOpen = true;
    },
    /** 忽略预警按钮操作 */
    handleIgnore(row) {
      this.$modal.confirm('是否确认忽略此预警?').then(function() {
        console.log('忽略预警:', row.warnId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("预警已忽略");
      }).catch(() => {});
    },
    /** 提交处理 */
    submitProcess() {
      this.$refs["processForm"].validate(valid => {
        if (valid) {
          console.log('处理预警:', this.processForm);
          this.processOpen = false;
          this.getList();
          this.$modal.msgSuccess("预警处理成功");
        }
      });
    }
  }
};
</script>

<style scoped>
.stats-cards {
  margin-bottom: 20px;
}
.stats-card {
  border-radius: 8px;
}
.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}
.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}
.stats-icon.total { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.stats-icon.pending { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.stats-icon.today { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.stats-icon.rate { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
.stats-info {
  flex: 1;
}
.stats-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}
.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}
</style>
