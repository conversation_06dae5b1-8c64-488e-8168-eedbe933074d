<template>
  <div class="app-container">
    <div class="page-header">
      <h2>导寻执行日志</h2>
      <p>查看和管理智能导寻系统的执行日志记录</p>
    </div>

    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>执行日志列表</span>
        <el-button style="float: right; padding: 3px 0" type="text">清理日志</el-button>
      </div>

      <div class="table-container">
        <el-table :data="[]" style="width: 100%">
          <el-table-column prop="executionId" label="执行ID" width="180"></el-table-column>
          <el-table-column prop="materialCode" label="物料编码" width="120"></el-table-column>
          <el-table-column prop="locationCodes" label="位置编码" width="150"></el-table-column>
          <el-table-column prop="triggerUser" label="触发用户" width="100"></el-table-column>
          <el-table-column prop="executionStatus" label="执行状态" width="100"></el-table-column>
          <el-table-column prop="startTime" label="开始时间" width="180"></el-table-column>
          <el-table-column label="操作" width="120">
            <template slot-scope="scope">
              <el-button size="mini">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: "GuideExecutionLog",
  data() {
    return {
      // 基本数据
    };
  },
  created() {
    // 初始化
  },
  methods: {
    // 方法
  }
};
</script>

<style scoped>
.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 20px;
}

.page-header p {
  margin: 0;
  color: #909399;
}

.table-container {
  margin-top: 20px;
}
</style>