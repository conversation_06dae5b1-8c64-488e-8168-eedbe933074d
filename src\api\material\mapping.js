import request from '@/utils/request'

// 查询物料编码映射列表
export function listMapping(query) {
  return request({
    url: '/material/mapping/list',
    method: 'get',
    params: query
  })
}

// 查询物料编码映射详细
export function getMapping(id) {
  return request({
    url: '/material/mapping/' + id,
    method: 'get'
  })
}

// 新增物料编码映射
export function addMapping(data) {
  return request({
    url: '/material/mapping',
    method: 'post',
    data: data
  })
}

// 修改物料编码映射
export function updateMapping(data) {
  return request({
    url: '/material/mapping',
    method: 'put',
    data: data
  })
}

// 删除物料编码映射
export function delMapping(id) {
  return request({
    url: '/material/mapping/' + id,
    method: 'delete'
  })
}
