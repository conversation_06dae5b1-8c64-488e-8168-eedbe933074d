<template>
  <div class="multilevel-form">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <!-- 基本信息 -->
      <el-card class="box-card" style="margin-bottom: 20px;">
        <div slot="header" class="clearfix">
          <span>基本信息</span>
        </div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="流程名称" prop="workflowName">
              <el-input v-model="form.workflowName" placeholder="请输入流程名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="流程编码" prop="workflowCode">
              <el-input v-model="form.workflowCode" placeholder="请输入流程编码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="业务类型" prop="businessType">
              <el-select v-model="form.businessType" placeholder="请选择业务类型" style="width: 100%">
                <el-option v-for="item in businessTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio label="0">启用</el-radio>
                <el-radio label="1">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="流程描述" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入流程描述" />
        </el-form-item>
      </el-card>

      <!-- 审批级别配置 -->
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>审批级别配置</span>
          <div style="float: right;">
            <el-button size="mini" @click="loadTemplate">加载模板</el-button>
            <el-button size="mini" type="primary" @click="addLevel">添加级别</el-button>
          </div>
        </div>

        <!-- 模板选择 -->
        <el-row v-if="showTemplate" style="margin-bottom: 20px;">
          <el-col :span="24">
            <el-alert
              title="选择审批级别模板"
              type="info"
              :closable="false"
              style="margin-bottom: 10px;">
            </el-alert>
            <el-radio-group v-model="templateLevels" @change="handleTemplateChange">
              <el-radio :label="1">一级审批</el-radio>
              <el-radio :label="2">二级审批</el-radio>
              <el-radio :label="3">三级审批</el-radio>
              <el-radio :label="4">四级审批</el-radio>
              <el-radio :label="5">五级审批</el-radio>
            </el-radio-group>
          </el-col>
        </el-row>

        <!-- 审批级别列表 -->
        <div v-if="form.levels && form.levels.length > 0">
          <div v-for="(level, index) in form.levels" :key="index" class="level-item">
            <el-card class="level-card">
              <div slot="header" class="level-header">
                <span>第{{ level.level }}级审批 - {{ level.levelName }}</span>
                <el-button
                  type="text"
                  icon="el-icon-delete"
                  @click="removeLevel(index)"
                  style="color: #f56c6c;"
                >删除</el-button>
              </div>
              <level-config
                :level-data="level"
                :level-index="index"
                @update="updateLevel"
              />
            </el-card>
          </div>
        </div>

        <!-- 空状态 -->
        <el-empty v-else description="暂无审批级别，请添加审批级别" :image-size="100">
          <el-button type="primary" @click="addLevel">添加第一级审批</el-button>
        </el-empty>
      </el-card>

      <!-- 预览区域 -->
      <el-card class="box-card" style="margin-top: 20px;">
        <div slot="header" class="clearfix">
          <span>流程预览</span>
          <el-button style="float: right;" size="mini" @click="previewFlow">刷新预览</el-button>
        </div>
        <workflow-preview :levels="form.levels" />
      </el-card>

      <!-- 操作按钮 -->
      <div style="text-align: center; margin-top: 30px;">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </el-form>

    <!-- 加载模板对话框 -->
    <el-dialog title="选择审批模板" :visible.sync="templateDialogVisible" width="600px">
      <template-selector @select="handleTemplateSelect" @cancel="templateDialogVisible = false" />
    </el-dialog>
  </div>
</template>

<script>
import { createMultilevelWorkflow, updateWorkflow, getTemplate } from "@/api/approval/multilevel";
import { getWorkflowOptions } from "@/api/approval/workflow";
import LevelConfig from './LevelConfig';
import WorkflowPreview from './WorkflowPreview';
import TemplateSelector from './TemplateSelector';

export default {
  name: "MultilevelForm",
  components: {
    LevelConfig,
    WorkflowPreview,
    TemplateSelector
  },
  props: {
    formData: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        workflowName: '',
        workflowCode: '',
        businessType: '',
        description: '',
        status: '0',
        levels: []
      },
      rules: {
        workflowName: [
          { required: true, message: '请输入流程名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        workflowCode: [
          { required: true, message: '请输入流程编码', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        businessType: [
          { required: true, message: '请选择业务类型', trigger: 'change' }
        ]
      },
      activeNames: ['0'],
      showTemplate: false,
      templateLevels: 1,
      templateDialogVisible: false,
      submitting: false,
      // 业务类型选项
      businessTypeOptions: []
    };
  },
  created() {
    this.getBusinessTypeOptions();
    // 初始化时检查是否有传入的数据
    this.initFormData();
  },
  watch: {
    formData: {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          console.log('MultilevelForm 接收到 formData:', newVal);
          this.form = {
            workflowName: newVal.workflowName || '',
            workflowCode: newVal.workflowCode || '',
            businessType: newVal.businessType || '',
            description: newVal.description || '',
            status: newVal.status || '0',
            levels: newVal.levels || []
          };
          console.log('MultilevelForm 更新后的 form:', this.form);
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    /** 初始化表单数据 */
    initFormData() {
      if (this.formData && Object.keys(this.formData).length > 0) {
        console.log('初始化时接收到 formData:', this.formData);
        this.form = {
          workflowName: this.formData.workflowName || '',
          workflowCode: this.formData.workflowCode || '',
          businessType: this.formData.businessType || '',
          description: this.formData.description || '',
          status: this.formData.status || '0',
          levels: this.formData.levels || []
        };
        console.log('初始化后的 form:', this.form);
      }
    },

    /** 获取业务类型选项 */
    getBusinessTypeOptions() {
      getWorkflowOptions().then(res => {
        if (res.code === 200 && res.data) {
          // 使用Map对象确保每个业务类型只出现一次
          const businessTypeMap = new Map();
          res.data.forEach(item => {
            if (!businessTypeMap.has(item.businessType)) {
              businessTypeMap.set(item.businessType, {
                value: item.businessType,
                label: item.businessTypeName || item.businessType
              });
            }
          });

          // 将Map转换回数组
          this.businessTypeOptions = Array.from(businessTypeMap.values());
        }
      });
    },
    /** 添加审批级别 */
    addLevel() {
      const newLevel = {
        level: this.form.levels.length + 1,
        levelName: `第${this.form.levels.length + 1}级审批`,
        approvalType: '0', // 指定人员
        approvers: '',
        roleId: null,
        deptId: null,
        approvalCondition: 'ANY',
        timeoutAction: '0',
        timeoutHours: 24
      };
      this.form.levels.push(newLevel);
      this.activeNames = [(this.form.levels.length - 1).toString()];
    },

    /** 更新审批级别 */
    updateLevel(index, levelData) {
      this.$set(this.form.levels, index, levelData);
    },

    /** 删除审批级别 */
    removeLevel(index) {
      this.form.levels.splice(index, 1);
      // 重新编号
      this.form.levels.forEach((level, idx) => {
        level.level = idx + 1;
        level.levelName = level.levelName.replace(/第\d+级/, `第${idx + 1}级`);
      });
    },

    /** 加载模板 */
    loadTemplate() {
      this.showTemplate = !this.showTemplate;
    },

    /** 模板变化处理 */
    handleTemplateChange(levels) {
      getTemplate(levels).then(response => {
        this.form.levels = response.data;
        this.showTemplate = false;
        this.$message.success(`已加载${levels}级审批模板`);
      }).catch(error => {
        this.$message.error('加载模板失败: ' + error.message);
      });
    },

    /** 模板选择处理 */
    handleTemplateSelect(template) {
      this.form.levels = template.levels;
      this.templateDialogVisible = false;
      this.$message.success('模板加载成功');
    },

    /** 预览流程 */
    previewFlow() {
      // 触发预览组件刷新
      this.$nextTick(() => {
        this.$forceUpdate();
      });
    },

    /** 提交表单 */
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (!valid) {
          return;
        }

        if (this.form.levels.length === 0) {
          this.$message.error('请至少添加一个审批级别');
          return;
        }

        this.submitting = true;
        const submitData = {
          workflowName: this.form.workflowName,
          workflowCode: this.form.workflowCode,
          businessType: this.form.businessType,
          description: this.form.description,
          status: this.form.status,
          approvalLevels: this.form.levels
        };

        const submitPromise = this.isEdit 
          ? updateWorkflow(this.form.workflowId, this.form.levels)
          : createMultilevelWorkflow(submitData);

        submitPromise.then(response => {
          console.log('API响应:', response);
          this.$message.success(this.isEdit ? '更新成功' : '创建成功');
          this.$emit('submit', submitData);
        }).catch(error => {
          console.error('API错误:', error);
          console.error('错误详情:', error.response);
          const errorMsg = error.response?.data?.msg || error.message || '未知错误';
          this.$message.error((this.isEdit ? '更新' : '创建') + '失败: ' + errorMsg);
        }).finally(() => {
          this.submitting = false;
        });
      });
    },

    /** 取消操作 */
    handleCancel() {
      this.$emit('cancel');
    }
  }
};
</script>

<style scoped>
.multilevel-form {
  max-height: 80vh;
  overflow-y: auto;
}

.box-card {
  margin-bottom: 20px;
}

.el-collapse {
  border: none;
}

.el-collapse-item {
  margin-bottom: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.level-item {
  margin-bottom: 20px;
}

.level-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.level-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}
</style>
