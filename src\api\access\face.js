import request from '@/utils/request'

// 查询用户人脸信息列表
export function listFace(query) {
  return request({
    url: '/access/face/list',
    method: 'get',
    params: query
  })
}

// 查询用户人脸信息详细
export function getFace(id) {
  return request({
    url: '/access/face/' + id,
    method: 'get'
  })
}

// 新增用户人脸信息
export function addFace(data) {
  return request({
    url: '/access/face',
    method: 'post',
    data: data
  })
}

// 修改用户人脸信息
export function updateFace(data) {
  return request({
    url: '/access/face',
    method: 'put',
    data: data
  })
}

// 删除用户人脸信息
export function delFace(id) {
  return request({
    url: '/access/face/' + id,
    method: 'delete'
  })
}

// 从出入库申请同步领料员信息
export function syncFromInout() {
  return request({
    url: '/access/face/syncFromInout',
    method: 'post'
  })
}

// 下发人脸信息到门禁设备
export function syncToDevice(data) {
  return request({
    url: '/access/face/syncToDevice',
    method: 'post',
    data: data
  })
}

// 检查同步状态
export function checkSyncStatus() {
  return request({
    url: '/access/face/checkSyncStatus',
    method: 'post'
  })
}

// 获取人脸信息统计
export function getFaceStatistics() {
  return request({
    url: '/access/face/statistics',
    method: 'get'
  })
}

// 获取用户同步状态详情
export function getUserSyncDetails(userId) {
  return request({
    url: '/access/face/syncDetails/' + userId,
    method: 'get'
  })
}

// 批量删除人脸信息
export function batchDelFace(ids) {
  return request({
    url: '/access/face/batch/' + ids,
    method: 'delete'
  })
}

// 重新下发失败的人脸信息
export function resyncFailedFaces() {
  return request({
    url: '/access/face/resyncFailed',
    method: 'post'
  })
}
