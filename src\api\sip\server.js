import request from '@/utils/request'

// SIP 服务器管理 API

/**
 * 获取 SIP 服务器状态
 */
export function getSipServerStatus() {
  return request({
    url: '/api/sip/status',
    method: 'get'
  })
}

/**
 * 获取 SIP 服务器统计信息
 */
export function getSipServerStatistics() {
  return request({
    url: '/api/sip/statistics',
    method: 'get'
  })
}

/**
 * 获取 SIP 服务器配置
 */
export function getSipServerConfig() {
  return request({
    url: '/api/sip/config',
    method: 'get'
  })
}

/**
 * 更新 SIP 服务器配置
 */
export function updateSipServerConfig(data) {
  return request({
    url: '/api/sip/config',
    method: 'put',
    data
  })
}

/**
 * 重启 SIP 服务器
 */
export function restartSipServer() {
  return request({
    url: '/api/sip/restart',
    method: 'post'
  })
}

/**
 * 停止 SIP 服务器
 */
export function stopSipServer() {
  return request({
    url: '/api/sip/stop',
    method: 'post'
  })
}

/**
 * 启动 SIP 服务器
 */
export function startSipServer() {
  return request({
    url: '/api/sip/start',
    method: 'post'
  })
}

/**
 * 获取系统健康状态
 */
export function getSipHealthStatus() {
  return request({
    url: '/api/sip/health',
    method: 'get'
  })
}

/**
 * 获取系统日志
 */
export function getSipSystemLogs(params) {
  return request({
    url: '/api/sip/logs',
    method: 'get',
    params
  })
}

/**
 * 清理系统日志
 */
export function clearSipSystemLogs() {
  return request({
    url: '/api/sip/logs/clear',
    method: 'delete'
  })
}
