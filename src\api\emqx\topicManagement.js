import request from '@/utils/request'

// ==================== 主题管理API ====================

// 获取所有主题列表
export function getTopics(query) {
  return request({
    url: '/emqx/topic-management/topics',
    method: 'get',
    params: query
  })
}

// 获取特定主题详细信息
export function getTopicInfo(topic) {
  return request({
    url: `/emqx/topic-management/topics/${encodeURIComponent(topic)}`,
    method: 'get'
  })
}

// 为客户端订阅主题
export function subscribeTopicForClient(data) {
  return request({
    url: '/emqx/topic-management/subscribe',
    method: 'post',
    data: data
  })
}

// 为客户端取消订阅主题
export function unsubscribeTopicForClient(data) {
  return request({
    url: '/emqx/topic-management/unsubscribe',
    method: 'delete',
    data: data
  })
}

// 批量订阅主题
export function batchSubscribeTopics(data) {
  return request({
    url: '/emqx/topic-management/batch-subscribe',
    method: 'post',
    data: data
  })
}

// 批量取消订阅主题
export function batchUnsubscribeTopics(data) {
  return request({
    url: '/emqx/topic-management/batch-unsubscribe',
    method: 'post',
    data: data
  })
}

// 获取主题订阅者列表
export function getTopicSubscribers(topic) {
  return request({
    url: `/emqx/topic-management/topics/${encodeURIComponent(topic)}/subscribers`,
    method: 'get'
  })
}

// 获取实时主题统计
export function getRealTimeTopicStats() {
  return request({
    url: '/emqx/topic-management/stats/realtime',
    method: 'get'
  })
}

// 测试主题连通性
export function testTopicConnectivity(data) {
  return request({
    url: '/emqx/topic-management/test',
    method: 'post',
    data: data
  })
}

// ==================== 动态主题管理API ====================

// 动态添加主题订阅
export function addDynamicTopic(data) {
  return request({
    url: '/emqx/topic-management/dynamic/add',
    method: 'post',
    data: data
  })
}

// 动态删除主题订阅
export function removeDynamicTopic(data) {
  return request({
    url: '/emqx/topic-management/dynamic/remove',
    method: 'delete',
    data: data
  })
}

// 修改主题订阅参数
export function updateDynamicTopic(data) {
  return request({
    url: '/emqx/topic-management/dynamic/update',
    method: 'post',
    data: data
  })
}

// ==================== 场景化管理API ====================

// 获取设备类型分组的主题配置
export function getTopicsByDeviceType(deviceType) {
  return request({
    url: `/emqx/topic-scenario/device-type/${deviceType}`,
    method: 'get'
  })
}

// 获取业务模块分组的主题配置
export function getTopicsByModule(module) {
  return request({
    url: `/emqx/topic-scenario/module/${module}`,
    method: 'get'
  })
}

// 获取主题功能分组的配置
export function getTopicsByFunction(func) {
  return request({
    url: `/emqx/topic-scenario/function/${func}`,
    method: 'get'
  })
}

// 批量订阅设备类型相关主题
export function batchSubscribeDeviceTopics(data) {
  return request({
    url: '/emqx/topic-scenario/subscribe/device-type',
    method: 'post',
    data: data
  })
}

// 批量取消订阅设备类型相关主题
export function batchUnsubscribeDeviceTopics(data) {
  return request({
    url: '/emqx/topic-scenario/unsubscribe/device-type',
    method: 'post',
    data: data
  })
}

// 批量订阅业务模块相关主题
export function batchSubscribeModuleTopics(data) {
  return request({
    url: '/emqx/topic-scenario/subscribe/module',
    method: 'post',
    data: data
  })
}

// 批量取消订阅业务模块相关主题
export function batchUnsubscribeModuleTopics(data) {
  return request({
    url: '/emqx/topic-scenario/unsubscribe/module',
    method: 'post',
    data: data
  })
}

// 创建主题场景配置
export function createTopicScenario(data) {
  return request({
    url: '/emqx/topic-scenario/scenario',
    method: 'post',
    data: data
  })
}

// 应用主题场景配置
export function applyTopicScenario(data) {
  return request({
    url: '/emqx/topic-scenario/scenario/apply',
    method: 'post',
    data: data
  })
}

// 获取所有主题场景配置
export function getAllTopicScenarios() {
  return request({
    url: '/emqx/topic-scenario/scenario/list',
    method: 'get'
  })
}

// 删除主题场景配置
export function deleteTopicScenario(scenarioName) {
  return request({
    url: `/emqx/topic-scenario/scenario/${scenarioName}`,
    method: 'delete'
  })
}

// 获取主题统计分析
export function getTopicAnalytics() {
  return request({
    url: '/emqx/topic-scenario/analytics',
    method: 'get'
  })
}

// 获取设备类型统计
export function getDeviceTypeStatistics() {
  return request({
    url: '/emqx/topic-scenario/statistics/device-type',
    method: 'get'
  })
}

// 获取业务模块统计
export function getModuleStatistics() {
  return request({
    url: '/emqx/topic-scenario/statistics/module',
    method: 'get'
  })
}

// 获取主题活跃度分析
export function getTopicActivityAnalysis() {
  return request({
    url: '/emqx/topic-scenario/analytics/activity',
    method: 'get'
  })
}

// 智能推荐主题订阅
export function recommendTopicsForClient(clientId, deviceType) {
  return request({
    url: '/emqx/topic-scenario/recommend',
    method: 'get',
    params: {
      clientId: clientId,
      deviceType: deviceType
    }
  })
}

// 检查主题配置冲突
export function checkTopicConflicts(data) {
  return request({
    url: '/emqx/topic-scenario/check-conflicts',
    method: 'post',
    data: data
  })
}

// 优化主题订阅配置
export function optimizeTopicSubscriptions(clientId) {
  return request({
    url: `/emqx/topic-scenario/optimize/${clientId}`,
    method: 'get'
  })
}

// ==================== 辅助功能API ====================

// 发布测试消息
export function publishTestMessage(data) {
  return request({
    url: '/emqx/manage/publish',
    method: 'post',
    data: data
  })
}

// 获取客户端列表
export function getClientList(query) {
  return request({
    url: '/emqx/monitor/clients',
    method: 'get',
    params: query
  })
}

// 获取客户端订阅信息
export function getClientSubscriptions(clientId) {
  return request({
    url: `/emqx/monitor/clients/${clientId}/subscriptions`,
    method: 'get'
  })
}
