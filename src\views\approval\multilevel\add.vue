<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>{{ isEdit ? '编辑多级审批' : '新增多级审批' }}</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回</el-button>
      </div>
      
      <multilevel-form
        ref="multilevelForm"
        :is-edit="isEdit"
        :form-data="initialData"
        @submit="handleSubmit"
        @cancel="goBack"
      />
    </el-card>
  </div>
</template>

<script>
import MultilevelForm from './components/MultilevelForm'

export default {
  name: "AddMultilevelWorkflow",
  components: {
    MultilevelForm
  },
  data() {
    return {
      isEdit: false,
      workflowId: null,
      initialData: {}
    };
  },
  created() {
    this.initPage();
  },
  methods: {
    /** 初始化页面 */
    initPage() {
      // 检查是否是编辑模式
      this.workflowId = this.$route.query.workflowId;
      this.isEdit = !!this.workflowId;

      // 如果有传入的初始数据，设置到表单中
      if (this.$route.query.workflowName) {
        this.initialData = {
          workflowName: this.$route.query.workflowName,
          workflowCode: this.$route.query.workflowCode,
          businessType: this.$route.query.businessType,
          description: this.$route.query.description,
          status: this.$route.query.status,
          levels: [] // 确保包含levels数组
        };

        console.log('接收到的初始数据:', this.initialData);
      }
    },
    
    /** 处理提交 */
    handleSubmit(data) {
      // 不在这里显示成功消息，因为MultilevelForm组件已经显示了
      // 直接跳转回列表页面
      this.goBack();
    },
    
    /** 返回上一页 */
    goBack() {
      // 检查是否有returnUrl参数，如果有则跳转到指定页面，否则跳转到流程定义列表页面
      const returnUrl = this.$route.query.returnUrl || '/approval/workflow';
      console.log('返回到:', returnUrl);
      this.$router.push(returnUrl);
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
