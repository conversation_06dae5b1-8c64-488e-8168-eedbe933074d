<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>流程详情</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回</el-button>
      </div>
      
      <!-- 基本信息部分 -->
      <el-descriptions title="基本信息" :column="2" border>
        <el-descriptions-item label="流程名称">{{ workflow.workflowName || '未设置' }}</el-descriptions-item>
        <el-descriptions-item label="流程编码">{{ workflow.workflowCode || '未设置' }}</el-descriptions-item>
        <el-descriptions-item label="业务类型">{{ workflow.businessType || '未设置' }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag v-if="workflow.status === '0'" type="success">启用</el-tag>
          <el-tag v-else type="info">禁用</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ workflow.createTime || '未设置' }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ workflow.updateTime || '未设置' }}</el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">{{ workflow.description || '无' }}</el-descriptions-item>
      </el-descriptions>
      
      <!-- 流程图部分 -->
      <div class="workflow-diagram">
        <h4>流程图</h4>
        <div id="diagram-container" style="width: 100%; height: 300px; border: 1px solid #ddd; background-color: #f5f7fa;"></div>
      </div>
      
      <!-- 节点列表部分 -->
      <div class="workflow-nodes">
        <h4>节点列表</h4>
        <el-table :data="workflow.nodes || []" border>
          <el-table-column label="序号" type="index" width="50" align="center" />
          <el-table-column label="节点名称" prop="nodeName" min-width="120">
            <template slot-scope="scope">
              {{ scope.row.nodeName || '未命名节点' }}
            </template>
          </el-table-column>
          <el-table-column label="节点类型" align="center" width="100">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.nodeType === '0'" type="primary">开始</el-tag>
              <el-tag v-else-if="scope.row.nodeType === '1'" type="info">审批</el-tag>
              <el-tag v-else-if="scope.row.nodeType === '2'" type="success">结束</el-tag>
              <el-tag v-else type="warning">未知</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="审批人" min-width="150" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              <span v-if="scope.row.approverNames && scope.row.approverNames.length > 0">{{ scope.row.approverNames.join(', ') }}</span>
              <span v-else-if="scope.row.approverName">{{ scope.row.approverName }}</span>
              <span v-else-if="scope.row.userIds">用户ID: {{ scope.row.userIds }}</span>
              <span v-else>未设置</span>
            </template>
          </el-table-column>
          <el-table-column label="审批条件" align="center" width="100">
            <template slot-scope="scope">
              <span v-if="scope.row.approvalConditionDisplay">{{ scope.row.approvalConditionDisplay }}</span>
              <span v-else-if="scope.row.approvalCondition === 'L' || scope.row.approvalCondition === 'ALL'">所有人</span>
              <span v-else-if="scope.row.approvalCondition === 'A' || scope.row.approvalCondition === 'ANY'">任意一人</span>
              <span v-else>{{ scope.row.approvalCondition || '未设置' }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getWorkflow, getWorkflowDiagram } from "@/api/approval/workflow";
import * as d3 from 'd3';

export default {
  name: "WorkflowDetail",
  data() {
    return {
      // 流程ID
      workflowId: undefined,
      // 流程详情
      workflow: {
        workflowName: "",
        workflowCode: "",
        businessType: "",
        status: "",
        description: "",
        createTime: "",
        updateTime: "",
        nodes: []
      },
      // 流程图数据
      diagramData: null
    };
  },
  created() {
    this.getWorkflowId();
  },
  mounted() {
    // 页面挂载后初始化
    this.initPage();
  },
  methods: {
    /** 页面初始化 */
    initPage() {
      // 检查URL参数
      const workflowId = this.$route.query.workflowId;
      if (!workflowId) {
        this.$message.error('缺少流程ID参数');
        return;
      }
      
      this.workflowId = workflowId;
      
      // 加载数据
      this.loadData();
    },
    /** 加载所有数据 */
    loadData() {
      // 显示加载提示
      const loading = this.$loading({
        lock: true,
        text: '正在加载流程数据...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      
      // 同时获取流程详情和流程图数据
      Promise.all([
        this.getDetail(),
        this.getDiagramData()
      ]).finally(() => {
        // 无论成功失败都关闭加载提示
        loading.close();
      });
    },
    /** 获取流程ID */
    getWorkflowId() {
      const workflowId = this.$route.query.workflowId;
      if (workflowId) {
        this.workflowId = workflowId;
        this.loadData();
      }
    },
    /** 获取流程详情 */
    getDetail() {
      return new Promise((resolve, reject) => {
        getWorkflow(this.workflowId).then(res => {
          if (res.code === 200) {
            // 先检查返回的数据结构
            console.log('流程详情数据:', res.data);
            if (res.data) {
              // 处理数据
              let workflowData = res.data;
              let nodesData = [];
              
              // 检查是否包含workflow字段
              if (res.data.workflow) {
                // 如果返回的是嵌套结构，提取出workflow对象
                workflowData = res.data.workflow;
                // 同时提取nodes数组
                nodesData = res.data.nodes || [];
              } else if (res.data.nodes) {
                // 如果节点信息在顶层nodes字段
                nodesData = res.data.nodes;
              }
              
              // 确保所有ID是字符串类型
              if (workflowData.workflowId) {
                workflowData.workflowId = String(workflowData.workflowId);
              }
              
              // 处理节点数据
              if (nodesData && Array.isArray(nodesData)) {
                // 处理审批人显示
                nodesData = nodesData.map(node => {
                  // 确保节点ID是字符串
                  if (node.nodeId) {
                    node.nodeId = String(node.nodeId);
                  }
                  
                  // 补充审批人显示信息
                  if (node.nodeType === '1') {
                    // 优先使用approverNames，如果没有则使用userIds
                    if (!node.approverNames && !node.approverName && node.userIds) {
                      node.approverName = `用户ID: ${node.userIds}`;
                    }

                    // 统一审批条件显示
                    if (node.approvalCondition === 'A' || node.approvalCondition === 'ANY') {
                      node.approvalConditionDisplay = '任意一人';
                    } else if (node.approvalCondition === 'L' || node.approvalCondition === 'ALL') {
                      node.approvalConditionDisplay = '所有人';
                    } else {
                      node.approvalConditionDisplay = node.approvalCondition || '未设置';
                    }
                  }
                  
                  return node;
                });
              }
              
              // 更新组件数据
              this.workflow = {
                ...workflowData,
                nodes: nodesData
              };
              
              resolve(this.workflow);
            } else {
              // 数据为空时的处理
              this.$message.warning('获取流程详情失败，数据为空');
              reject(new Error('数据为空'));
            }
          } else {
            this.$message.error(res.msg || '获取流程详情失败');
            reject(new Error(res.msg || '获取失败'));
          }
        }).catch(err => {
          console.error('获取流程详情出错:', err);
          this.$message.error('获取流程详情出错');
          reject(err);
        });
      });
    },
    /** 获取流程图数据 */
    getDiagramData() {
      return new Promise((resolve, reject) => {
        getWorkflowDiagram(this.workflowId).then(res => {
          if (res.code === 200) {
            this.diagramData = res.data;
            console.log('流程图数据:', this.diagramData);
            this.$nextTick(() => {
              this.renderDiagram();
              resolve(this.diagramData);
            });
          } else {
            this.$message.error(res.msg || '获取流程图数据失败');
            reject(new Error(res.msg || '获取失败'));
          }
        }).catch(err => {
          console.error('获取流程图数据出错:', err);
          this.$message.error('获取流程图数据出错');
          reject(err);
        });
      });
    },
    /** 渲染流程图 */
    renderDiagram() {
      // 检查图数据是否存在
      if (!this.diagramData || !this.diagramData.nodes || this.diagramData.nodes.length === 0) {
        console.warn('无法渲染流程图：数据为空');

        // 如果有节点数据但没有图数据，尝试从节点构建图数据
        if (this.workflow && this.workflow.nodes && this.workflow.nodes.length > 0) {
          console.log('尝试从节点数据构建流程图');
          this.buildDiagramFromNodes();
        }
        return;
      }

      // 清空容器
      const container = document.getElementById('diagram-container');
      if (!container) {
        console.error('找不到图容器元素');
        return;
      }

      container.innerHTML = '';

      // 使用D3.js渲染流程图 - 圆点样式
      const width = container.clientWidth;
      const height = container.clientHeight;
      const nodeWidth = 120;
      const nodeHeight = 80;  // 增加高度以容纳文字和圆点
      const circleRadius = 18; // 圆点半径
      const textHeight = 30;   // 文字区域高度
      const circleY = textHeight + 25; // 圆点Y位置（文字下方）
      
      const svg = d3.select('#diagram-container')
        .append('svg')
        .attr('width', width)
        .attr('height', height);
        
      const nodes = this.diagramData.nodes;
      const links = this.diagramData.links || [];
      
      // 计算节点位置
      const nodePositions = [];
      const xGap = Math.min(200, width / (nodes.length + 1));
      
      nodes.forEach((node, index) => {
        nodePositions.push({
          x: xGap * (index + 1),
          y: height / 2
        });
      });
      
      // 添加箭头标记
      svg.append('defs').append('marker')
        .attr('id', 'arrowhead')
        .attr('viewBox', '0 -5 10 10')
        .attr('refX', 8)
        .attr('refY', 0)
        .attr('orient', 'auto')
        .attr('markerWidth', 6)
        .attr('markerHeight', 6)
        .append('path')
        .attr('d', 'M0,-5L10,0L0,5')
        .attr('fill', '#999');
      
      // 绘制连线
      const lineGenerator = d3.line()
        .curve(d3.curveBasis);

      links.forEach((link, index) => {
        const sourceIndex = nodes.findIndex(n => String(n.nodeId) === String(link.source));
        const targetIndex = nodes.findIndex(n => String(n.nodeId) === String(link.target));

        if (sourceIndex > -1 && targetIndex > -1) {
          const sourcePos = nodePositions[sourceIndex];
          const targetPos = nodePositions[targetIndex];

          // 连接线从圆点中心到圆点中心
          const sourceCircleX = sourcePos.x;
          const targetCircleX = targetPos.x;
          const sourceCircleY = sourcePos.y + circleY;
          const targetCircleY = targetPos.y + circleY;

          const points = [
            [sourceCircleX + circleRadius, sourceCircleY],
            [sourceCircleX + (targetCircleX - sourceCircleX) / 2, sourceCircleY],
            [sourceCircleX + (targetCircleX - sourceCircleX) / 2, targetCircleY],
            [targetCircleX - circleRadius, targetCircleY]
          ];

          svg.append('path')
            .attr('d', lineGenerator(points))
            .attr('stroke', '#999')
            .attr('stroke-width', 2)
            .attr('fill', 'none')
            .attr('marker-end', 'url(#arrowhead)');
        }
      });

      // 绘制节点（文字在上，圆点在下）
      nodes.forEach((node, index) => {
        const g = svg.append('g')
          .attr('transform', `translate(${nodePositions[index].x - nodeWidth / 2}, ${nodePositions[index].y - nodeHeight / 2})`);

        // 节点文本（上方）
        g.append('text')
          .attr('x', nodeWidth / 2)
          .attr('y', 15)
          .attr('text-anchor', 'middle')
          .attr('dominant-baseline', 'middle')
          .attr('fill', '#333')
          .attr('font-size', '12px')
          .attr('font-weight', 'bold')
          .text(node.nodeName);

        // 圆点（下方）
        const circle = g.append('circle')
          .attr('cx', nodeWidth / 2)
          .attr('cy', circleY)
          .attr('r', circleRadius)
          .attr('fill', this.getNodeColor(node.nodeType))
          .attr('stroke', '#fff')
          .attr('stroke-width', 2);

        // 为开始节点添加脉冲动画效果
        if (node.nodeType === '0') {
          // 添加脉冲外圈
          const pulseRing = g.append('circle')
            .attr('cx', nodeWidth / 2)
            .attr('cy', circleY)
            .attr('r', circleRadius)
            .attr('fill', 'none')
            .attr('stroke', this.getNodeColor(node.nodeType))
            .attr('stroke-width', 2)
            .attr('opacity', 0.8);

          // 创建脉冲动画函数
          const createPulseAnimation = (element) => {
            element
              .transition()
              .duration(1500)
              .ease(d3.easeLinear)
              .attr('r', circleRadius + 8)
              .attr('opacity', 0)
              .on('end', () => {
                // 动画结束后重新开始
                element
                  .attr('r', circleRadius)
                  .attr('opacity', 0.8);
                // 递归调用创建循环动画
                createPulseAnimation(element);
              });
          };

          // 启动脉冲动画
          createPulseAnimation(pulseRing);
        }

        // 为审批节点添加旋转动画效果
        if (node.nodeType === '1') {
          // 添加旋转外圈
          const rotateRing = g.append('circle')
            .attr('cx', nodeWidth / 2)
            .attr('cy', circleY)
            .attr('r', circleRadius + 3)
            .attr('fill', 'none')
            .attr('stroke', this.getNodeColor(node.nodeType))
            .attr('stroke-width', 2)
            .attr('stroke-dasharray', '5,3')
            .attr('opacity', 0.6);

          // 创建旋转动画函数
          const createRotateAnimation = (element) => {
            element
              .transition()
              .duration(3000)
              .ease(d3.easeLinear)
              .attrTween('transform', () => {
                return d3.interpolateString(
                  `rotate(0 ${nodeWidth / 2} ${circleY})`,
                  `rotate(360 ${nodeWidth / 2} ${circleY})`
                );
              })
              .on('end', () => {
                // 动画结束后重新开始
                createRotateAnimation(element);
              });
          };

          // 启动旋转动画
          createRotateAnimation(rotateRing);
        }

        // 状态图标（圆点内）
        g.append('text')
          .attr('x', nodeWidth / 2)
          .attr('y', circleY + 1)
          .attr('text-anchor', 'middle')
          .attr('dominant-baseline', 'middle')
          .attr('fill', '#fff')
          .attr('font-size', '12px')
          .attr('font-weight', 'bold')
          .text(this.getNodeIcon(node.nodeType));
      });
    },
    /** 从节点数据构建流程图 */
    buildDiagramFromNodes() {
      if (!this.workflow || !this.workflow.nodes || this.workflow.nodes.length === 0) {
        return;
      }
      
      // 构建图数据
      const nodes = this.workflow.nodes.map(node => ({
        nodeId: node.nodeId,
        nodeName: node.nodeName,
        nodeType: node.nodeType
      }));
      
      // 构建连线数据
      const links = [];
      for (let i = 0; i < nodes.length - 1; i++) {
        links.push({
          source: nodes[i].nodeId,
          target: nodes[i + 1].nodeId
        });
      }
      
      this.diagramData = {
        nodes: nodes,
        links: links
      };
      
      console.log('从节点构建的流程图数据:', this.diagramData);
      
      // 重新渲染图
      this.$nextTick(() => {
        this.renderDiagram();
      });
    },
    /** 获取节点颜色 */
    getNodeColor(nodeType) {
      switch (nodeType) {
        case '0': return '#409EFF'; // 开始节点
        case '1': return '#909399'; // 审批节点
        case '2': return '#67C23A'; // 结束节点
        default: return '#909399';
      }
    },
    /** 获取节点图标 */
    getNodeIcon(nodeType) {
      switch (nodeType) {
        case '0': return '▶'; // 开始节点
        case '1': return '●'; // 审批节点
        case '2': return '■'; // 结束节点
        default: return '●';
      }
    },
    /** 返回按钮 */
    goBack() {
      this.$router.push('/approval/workflow');
    }
  }
};
</script>

<style scoped>
.workflow-diagram,
.workflow-nodes {
  margin-top: 20px;
}
</style> 