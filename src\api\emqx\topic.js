import request from '@/utils/request'

// 查询MQTT主题配置列表
export function listTopic(query) {
  return request({
    url: '/emqx/topic/list',
    method: 'get',
    params: query
  })
}

// 查询MQTT主题配置详细
export function getTopic(topicId) {
  return request({
    url: '/emqx/topic/' + topicId,
    method: 'get'
  })
}

// 新增MQTT主题配置
export function addTopic(data) {
  return request({
    url: '/emqx/topic',
    method: 'post',
    data: data
  })
}

// 修改MQTT主题配置
export function updateTopic(data) {
  return request({
    url: '/emqx/topic',
    method: 'put',
    data: data
  })
}

// 删除MQTT主题配置
export function delTopic(topicId) {
  return request({
    url: '/emqx/topic/' + topicId,
    method: 'delete'
  })
}

// 获取主题统计信息
export function getTopicStatistics() {
  return request({
    url: '/emqx/topic/statistics',
    method: 'get'
  })
}

// 根据设备类型查询主题配置
export function getTopicsByDeviceType(deviceType) {
  return request({
    url: '/emqx/topic/device/' + deviceType,
    method: 'get'
  })
}

// 获取设备类型列表
export function getDeviceTypes() {
  return request({
    url: '/emqx/topic/device-types',
    method: 'get'
  })
}

// 批量更新主题状态
export function updateTopicStatus(data) {
  return request({
    url: '/emqx/topic/status',
    method: 'put',
    data: data
  })
}

// 验证主题模式
export function validateTopicPattern(data) {
  return request({
    url: '/emqx/topic/validate',
    method: 'post',
    data: data
  })
}

// 根据模块类型查询主题配置
export function getTopicsByModuleType(moduleType) {
  return request({
    url: '/emqx/topic/module/' + moduleType,
    method: 'get'
  })
}

// 测试主题连接
export function testTopic(data) {
  return request({
    url: '/emqx/topic/test',
    method: 'post',
    data: data
  })
}

// 导出主题配置
export function exportTopic(query) {
  return request({
    url: '/emqx/topic/export',
    method: 'post',
    params: query
  })
}
