import request from '@/utils/request'

// 查询打印模板配置列表
export function listPrintTemplate(query) {
  return request({
    url: '/inout/printTemplate/list',
    method: 'get',
    params: query
  })
}

// 查询打印模板配置详细
export function getPrintTemplate(id) {
  return request({
    url: '/inout/printTemplate/' + id,
    method: 'get'
  })
}

// 根据模板编码查询模板
export function getPrintTemplateByCode(templateCode) {
  return request({
    url: '/inout/printTemplate/code/' + templateCode,
    method: 'get'
  })
}

// 根据模板类型查询默认模板
export function getDefaultTemplateByType(templateType) {
  return request({
    url: '/inout/printTemplate/default/' + templateType,
    method: 'get'
  })
}

// 新增打印模板配置
export function addPrintTemplate(data) {
  return request({
    url: '/inout/printTemplate',
    method: 'post',
    data: data
  })
}

// 修改打印模板配置
export function updatePrintTemplate(data) {
  return request({
    url: '/inout/printTemplate',
    method: 'put',
    data: data
  })
}

// 删除打印模板配置
export function delPrintTemplate(id) {
  return request({
    url: '/inout/printTemplate/' + id,
    method: 'delete'
  })
}

// 设置默认模板
export function setDefaultTemplate(id) {
  return request({
    url: '/inout/printTemplate/setDefault/' + id,
    method: 'put'
  })
}
