import request from '@/utils/request'

// 查询门禁记录列表
export function listDoorLog(query) {
  return request({
    url: '/access/log/list',
    method: 'get',
    params: query
  })
}

// 查询门禁记录详细
export function getDoorLog(id) {
  return request({
    url: '/access/log/' + id,
    method: 'get'
  })
}

// 删除门禁记录
export function delDoorLog(id) {
  return request({
    url: '/access/log/' + id,
    method: 'delete'
  })
}

// 导出门禁记录
export function exportDoorLog(query) {
  return request({
    url: '/access/log/export',
    method: 'post',
    params: query
  })
}

// 清理门禁记录
export function cleanupDoorLog(data) {
  return request({
    url: '/access/log/cleanup',
    method: 'post',
    data: data
  })
}

// 获取门禁统计数据
export function getDoorLogStatistics(query) {
  return request({
    url: '/access/log/statistics',
    method: 'get',
    params: query
  })
}

// 获取实时门禁数据
export function getRealTimeDoorLog(query) {
  return request({
    url: '/access/log/realtime',
    method: 'get',
    params: query
  })
}

// 根据设备查询门禁记录
export function getDoorLogByDevice(deviceCode, query) {
  return request({
    url: '/access/doorLog/device/' + deviceCode,
    method: 'get',
    params: query
  })
}

// 根据人员查询门禁记录
export function getDoorLogByPerson(personId, query) {
  return request({
    url: '/access/doorLog/person/' + personId,
    method: 'get',
    params: query
  })
}

// 获取当前在库人员
export function getCurrentPersonsInWarehouse(warehouseId) {
  return request({
    url: '/access/doorLog/current/' + warehouseId,
    method: 'get'
  })
}
