import request from '@/utils/request'

// 获取监控布局列表
export function getLayoutList(query) {
  return request({
    url: '/video/layout/list',
    method: 'get',
    params: query
  })
}

// 查询布局详细
export function getLayout(id) {
  return request({
    url: '/video/layout/' + id,
    method: 'get'
  })
}

// 新增布局
export function addLayout(data) {
  return request({
    url: '/video/layout',
    method: 'post',
    data: data
  })
}

// 修改布局
export function updateLayout(data) {
  return request({
    url: '/video/layout',
    method: 'put',
    data: data
  })
}

// 删除布局
export function delLayout(id) {
  return request({
    url: '/video/layout/' + id,
    method: 'delete'
  })
}

// 保存监控布局
export function saveLayout(data) {
  return request({
    url: '/video/layout/save',
    method: 'post',
    data: data
  })
}

// 设置默认布局
export function setDefaultLayout(id) {
  return request({
    url: '/video/layout/default/' + id,
    method: 'put'
  })
}

// 复制布局
export function copyLayout(id, name) {
  return request({
    url: '/video/layout/copy/' + id,
    method: 'post',
    data: { layoutName: name }
  })
}
