<template>
  <div class="icon-showcase">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="page-title">
            <menu-icon icon="system-tools" size="large" theme="primary" />
            <h1>图标系统</h1>
          </div>
          <p class="page-desc">企业化工科技风格图标库 - 专为智能仓储管理系统设计</p>
        </div>
      </div>
    </div>

    <!-- 搜索区域 -->
    <el-card class="search-card" shadow="hover">
      <div slot="header">
        <span class="card-title">
          <menu-icon icon="search" size="medium" theme="primary" />
          图标搜索
        </span>
      </div>
      <el-input
        v-model="searchKeyword"
        placeholder="搜索图标名称或关键词..."
        prefix-icon="el-icon-search"
        clearable
        class="search-input"
      />
    </el-card>

    <!-- 主菜单图标 -->
    <el-card class="icon-section" shadow="hover">
      <div slot="header">
        <span class="card-title">
          <menu-icon icon="menu-management" size="medium" theme="primary" />
          主菜单图标
        </span>
        <span class="icon-count">{{ filteredMainIcons.length }} 个图标</span>
      </div>
      <div class="icon-grid">
        <div 
          v-for="(iconName, key) in filteredMainIcons" 
          :key="key"
          class="icon-item"
          @click="copyIconName(iconName)"
        >
          <div class="icon-preview">
            <menu-icon :icon="key" size="xlarge" theme="primary" animated />
          </div>
          <div class="icon-info">
            <div class="icon-name">{{ key }}</div>
            <div class="icon-code">{{ iconName }}</div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 子菜单图标 -->
    <el-card class="icon-section" shadow="hover">
      <div slot="header">
        <span class="card-title">
          <menu-icon icon="menu-management" size="medium" theme="secondary" />
          子菜单图标
        </span>
        <span class="icon-count">{{ filteredSubIcons.length }} 个图标</span>
      </div>
      <div class="icon-grid">
        <div 
          v-for="(iconName, key) in filteredSubIcons" 
          :key="key"
          class="icon-item"
          @click="copyIconName(iconName)"
        >
          <div class="icon-preview">
            <menu-icon :icon="key" size="xlarge" theme="secondary" animated />
          </div>
          <div class="icon-info">
            <div class="icon-name">{{ key }}</div>
            <div class="icon-code">{{ iconName }}</div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 主题色展示 -->
    <el-card class="theme-section" shadow="hover">
      <div slot="header">
        <span class="card-title">
          <menu-icon icon="settings" size="medium" theme="info" />
          主题色彩
        </span>
      </div>
      <div class="theme-grid">
        <div 
          v-for="(color, theme) in iconThemes" 
          :key="theme"
          class="theme-item"
        >
          <div class="theme-preview">
            <menu-icon icon="system-setting" size="xlarge" :theme="theme" animated />
          </div>
          <div class="theme-info">
            <div class="theme-name">{{ theme }}</div>
            <div class="theme-color" :style="{ backgroundColor: color }">{{ color }}</div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 使用说明 -->
    <el-card class="usage-section" shadow="hover">
      <div slot="header">
        <span class="card-title">
          <menu-icon icon="api-docs" size="medium" theme="info" />
          使用说明
        </span>
      </div>
      <div class="usage-content">
        <h3>基本用法</h3>
        <pre><code>&lt;menu-icon icon="system-setting" size="medium" theme="primary" /&gt;</code></pre>
        
        <h3>属性说明</h3>
        <el-table :data="propData" border stripe>
          <el-table-column prop="prop" label="属性" width="120" />
          <el-table-column prop="type" label="类型" width="100" />
          <el-table-column prop="default" label="默认值" width="100" />
          <el-table-column prop="description" label="说明" />
        </el-table>
        
        <h3>尺寸选项</h3>
        <div class="size-demo">
          <div class="size-item">
            <menu-icon icon="system-setting" size="small" theme="primary" />
            <span>small (16px)</span>
          </div>
          <div class="size-item">
            <menu-icon icon="system-setting" size="medium" theme="primary" />
            <span>medium (20px)</span>
          </div>
          <div class="size-item">
            <menu-icon icon="system-setting" size="large" theme="primary" />
            <span>large (24px)</span>
          </div>
          <div class="size-item">
            <menu-icon icon="system-setting" size="xlarge" theme="primary" />
            <span>xlarge (32px)</span>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import MenuIcon from '@/components/MenuIcon'
import { mainMenuIcons, subMenuIcons, iconThemes } from '@/utils/menu-icons'

export default {
  name: 'IconShowcase',
  components: {
    MenuIcon
  },
  data() {
    return {
      searchKeyword: '',
      mainMenuIcons,
      subMenuIcons,
      iconThemes,
      propData: [
        {
          prop: 'icon',
          type: 'String',
          default: "''",
          description: '图标名称或菜单名称'
        },
        {
          prop: 'menuPath',
          type: 'String',
          default: "''",
          description: '菜单路径（用于自动匹配图标）'
        },
        {
          prop: 'size',
          type: 'String',
          default: 'medium',
          description: '图标大小：small | medium | large | xlarge'
        },
        {
          prop: 'theme',
          type: 'String',
          default: 'primary',
          description: '图标主题色：primary | secondary | success | warning | danger | info | light | dark'
        },
        {
          prop: 'color',
          type: 'String',
          default: "''",
          description: '自定义颜色'
        },
        {
          prop: 'animated',
          type: 'Boolean',
          default: false,
          description: '是否启用动画'
        },
        {
          prop: 'fallback',
          type: 'String',
          default: 'el-icon-menu',
          description: '后备图标（Element UI图标）'
        }
      ]
    }
  },
  computed: {
    filteredMainIcons() {
      if (!this.searchKeyword) {
        return this.mainMenuIcons
      }
      const keyword = this.searchKeyword.toLowerCase()
      const filtered = {}
      Object.keys(this.mainMenuIcons).forEach(key => {
        if (key.toLowerCase().includes(keyword) || 
            this.mainMenuIcons[key].toLowerCase().includes(keyword)) {
          filtered[key] = this.mainMenuIcons[key]
        }
      })
      return filtered
    },
    
    filteredSubIcons() {
      if (!this.searchKeyword) {
        return this.subMenuIcons
      }
      const keyword = this.searchKeyword.toLowerCase()
      const filtered = {}
      Object.keys(this.subMenuIcons).forEach(key => {
        if (key.toLowerCase().includes(keyword) || 
            this.subMenuIcons[key].toLowerCase().includes(keyword)) {
          filtered[key] = this.subMenuIcons[key]
        }
      })
      return filtered
    }
  },
  methods: {
    copyIconName(iconName) {
      const text = `<menu-icon icon="${iconName}" />`
      if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
          this.$message.success('图标代码已复制到剪贴板')
        })
      } else {
        // 兼容旧浏览器
        const textArea = document.createElement('textarea')
        textArea.value = text
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        this.$message.success('图标代码已复制到剪贴板')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// 主色调定义
$primary-blue: #1e3a8a;
$secondary-blue: #3b82f6;
$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-500: #6b7280;
$gray-700: #374151;
$gray-900: #111827;

.icon-showcase {
  padding: 20px;
  background: #f8fafc;
  min-height: calc(100vh - 84px);

  // 页面头部
  .page-header {
    margin-bottom: 24px;

    .header-content {
      background: white;
      padding: 24px 32px;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      .page-title {
        display: flex;
        align-items: center;
        margin-bottom: 8px;

        h1 {
          font-size: 28px;
          font-weight: 700;
          color: $gray-900;
          margin: 0 0 0 12px;
        }
      }

      .page-desc {
        color: $gray-500;
        font-size: 14px;
        margin: 0;
      }
    }
  }

  // 搜索卡片
  .search-card {
    margin-bottom: 24px;
    border-radius: 12px;
    border: none;

    .search-input {
      max-width: 400px;
    }
  }

  // 图标区域
  .icon-section,
  .theme-section,
  .usage-section {
    margin-bottom: 24px;
    border-radius: 12px;
    border: none;

    .card-title {
      font-weight: 600;
      color: $gray-700;
      display: flex;
      align-items: center;
    }

    .icon-count {
      color: $gray-500;
      font-size: 14px;
      margin-left: auto;
    }
  }

  // 图标网格
  .icon-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;

    .icon-item {
      padding: 20px;
      border: 2px solid #e5e7eb;
      border-radius: 12px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      background: white;

      &:hover {
        border-color: $primary-blue;
        box-shadow: 0 4px 12px rgba(30, 58, 138, 0.15);
        transform: translateY(-2px);
      }

      .icon-preview {
        margin-bottom: 12px;
      }

      .icon-info {
        .icon-name {
          font-weight: 600;
          color: $gray-900;
          margin-bottom: 4px;
          font-size: 14px;
        }

        .icon-code {
          font-size: 12px;
          color: $gray-500;
          font-family: 'Courier New', monospace;
        }
      }
    }
  }

  // 主题网格
  .theme-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 16px;

    .theme-item {
      padding: 20px;
      border: 2px solid #e5e7eb;
      border-radius: 12px;
      text-align: center;
      background: white;

      .theme-preview {
        margin-bottom: 12px;
      }

      .theme-info {
        .theme-name {
          font-weight: 600;
          color: $gray-900;
          margin-bottom: 8px;
          text-transform: capitalize;
        }

        .theme-color {
          height: 24px;
          border-radius: 6px;
          color: white;
          font-size: 12px;
          line-height: 24px;
          font-family: 'Courier New', monospace;
        }
      }
    }
  }

  // 使用说明
  .usage-content {
    h3 {
      color: $gray-900;
      margin: 0 0 16px 0;
      font-size: 18px;
      font-weight: 600;

      &:not(:first-child) {
        margin-top: 32px;
      }
    }

    pre {
      background: $gray-50;
      padding: 16px;
      border-radius: 8px;
      border: 1px solid #e5e7eb;
      overflow-x: auto;
      margin-bottom: 24px;

      code {
        font-family: 'Courier New', monospace;
        color: $primary-blue;
      }
    }

    .size-demo {
      display: flex;
      gap: 24px;
      align-items: center;
      flex-wrap: wrap;

      .size-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 16px;
        background: $gray-50;
        border-radius: 8px;
        border: 1px solid #e5e7eb;

        span {
          font-size: 14px;
          color: $gray-700;
          font-family: 'Courier New', monospace;
        }
      }
    }
  }
}

// 全局样式覆盖
:deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: $gray-50;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background: $gray-50 !important;
  color: $gray-700 !important;
  font-weight: 600 !important;
}

// 响应式设计
@media (max-width: 768px) {
  .icon-showcase {
    padding: 12px;

    .icon-grid {
      grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
      gap: 12px;
    }

    .theme-grid {
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
      gap: 12px;
    }

    .size-demo {
      flex-direction: column;
      align-items: flex-start;
    }
  }
}
</style>
