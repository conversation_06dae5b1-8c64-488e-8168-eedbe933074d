/**
 * 路由唯一性辅助工具
 * 用于确保所有路由名称和路径的唯一性
 */

// 全局路由注册表
const globalRouteRegistry = {
  names: new Set(),
  paths: new Set(),
  fullPaths: new Set()
}

/**
 * 重置路由注册表
 */
export function resetRouteRegistry() {
  globalRouteRegistry.names.clear()
  globalRouteRegistry.paths.clear()
  globalRouteRegistry.fullPaths.clear()
  console.log('[路由唯一性] 路由注册表已重置')
}

/**
 * 生成唯一的路由名称
 */
export function generateUniqueRouteName(baseName, existingNames = new Set()) {
  if (!baseName) return null
  
  // 清理基础名称
  let cleanName = baseName.replace(/[^a-zA-Z0-9]/g, '')
  if (!cleanName) cleanName = 'Route'
  
  // 首字母大写
  cleanName = cleanName.charAt(0).toUpperCase() + cleanName.slice(1)
  
  // 检查全局和局部重复
  const allNames = new Set([...globalRouteRegistry.names, ...existingNames])
  
  if (!allNames.has(cleanName)) {
    return cleanName
  }
  
  // 生成唯一名称
  let counter = 1
  let uniqueName = `${cleanName}${counter}`
  
  while (allNames.has(uniqueName)) {
    counter++
    uniqueName = `${cleanName}${counter}`
  }
  
  return uniqueName
}

/**
 * 生成完整路径
 */
function generateFullPath(route, parentPath = '') {
  if (!route.path) return parentPath
  
  let fullPath = route.path
  if (parentPath && !route.path.startsWith('/')) {
    fullPath = `${parentPath}/${route.path}`
  }
  
  return fullPath.replace(/\/+/g, '/') // 去除多余的斜杠
}

/**
 * 深度清理路由重复
 */
export function deepCleanRoutes(routes, parentPath = '') {
  if (!Array.isArray(routes)) return []
  
  const cleanedRoutes = []
  const localNames = new Set()
  const localPaths = new Set()
  
  routes.forEach(route => {
    if (!route || typeof route !== 'object') return
    
    const cleanedRoute = { ...route }
    const fullPath = generateFullPath(route, parentPath)
    
    // 跳过无效路由
    if (!route.path || route.path === '#') {
      if (route.children && route.children.length > 0) {
        cleanedRoute.children = deepCleanRoutes(route.children, fullPath)
      }
      cleanedRoutes.push(cleanedRoute)
      return
    }
    
    // 检查路径重复
    if (globalRouteRegistry.fullPaths.has(fullPath) || localPaths.has(fullPath)) {
      console.log(`[路由唯一性] 跳过重复路径: ${fullPath}`)
      return
    }
    
    // 处理路由名称
    if (route.name) {
      if (globalRouteRegistry.names.has(route.name) || localNames.has(route.name)) {
        const newName = generateUniqueRouteName(route.name, new Set([...globalRouteRegistry.names, ...localNames]))
        console.log(`[路由唯一性] 重命名路由: ${route.name} -> ${newName}`)
        cleanedRoute.name = newName
      }
    } else if (route.path && route.path !== '#') {
      // 为没有名称的路由生成名称
      const pathBasedName = generateUniqueRouteName(route.path, new Set([...globalRouteRegistry.names, ...localNames]))
      cleanedRoute.name = pathBasedName
      console.log(`[路由唯一性] 生成路由名称: ${pathBasedName} for ${route.path}`)
    }
    
    // 注册路由信息
    if (cleanedRoute.name) {
      globalRouteRegistry.names.add(cleanedRoute.name)
      localNames.add(cleanedRoute.name)
    }
    globalRouteRegistry.fullPaths.add(fullPath)
    localPaths.add(fullPath)
    
    // 递归处理子路由
    if (route.children && route.children.length > 0) {
      cleanedRoute.children = deepCleanRoutes(route.children, fullPath)
    }
    
    cleanedRoutes.push(cleanedRoute)
  })
  
  return cleanedRoutes
}

/**
 * 过滤特殊路由
 */
export function filterSpecialRoutes(routes) {
  return routes.filter(route => {
    // 过滤外部链接
    if (route.path && /^https?:\/\//.test(route.path)) {
      console.log(`[路由唯一性] 过滤外部链接: ${route.path}`)
      return false
    }
    
    // 过滤特定的重复菜单
    if (route.path === 'navigation' && route.component === 'dwms/navigation/index') {
      console.log(`[路由唯一性] 过滤重复的导寻管理菜单`)
      return false
    }
    
    // 过滤空路由
    if (!route.path && (!route.children || route.children.length === 0)) {
      console.log(`[路由唯一性] 过滤空路由`)
      return false
    }
    
    return true
  })
}

/**
 * 主要的路由清理函数
 */
export function cleanAndUniqueRoutes(routes) {
  console.log(`[路由唯一性] 开始清理 ${routes.length} 个路由`)
  
  // 1. 过滤特殊路由
  let cleanedRoutes = filterSpecialRoutes(routes)
  console.log(`[路由唯一性] 过滤后剩余 ${cleanedRoutes.length} 个路由`)
  
  // 2. 深度清理重复
  cleanedRoutes = deepCleanRoutes(cleanedRoutes)
  console.log(`[路由唯一性] 深度清理后剩余 ${cleanedRoutes.length} 个路由`)
  
  // 3. 输出统计信息
  console.log(`[路由唯一性] 已注册路由名称: ${globalRouteRegistry.names.size} 个`)
  console.log(`[路由唯一性] 已注册路径: ${globalRouteRegistry.fullPaths.size} 个`)
  
  return cleanedRoutes
}

/**
 * 获取路由注册表状态
 */
export function getRouteRegistryStatus() {
  return {
    namesCount: globalRouteRegistry.names.size,
    pathsCount: globalRouteRegistry.fullPaths.size,
    names: Array.from(globalRouteRegistry.names),
    paths: Array.from(globalRouteRegistry.fullPaths)
  }
}
