<template>
  <div class="app-container">
    <el-card class="filter-container" shadow="hover">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
        <el-form-item label="仓库" prop="warehouseId">
          <el-select v-model="queryParams.warehouseId" placeholder="请选择仓库" clearable style="width: 240px">
            <el-option
              v-for="item in warehouseOptions"
              :key="item.id"
              :label="item.warehouseName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="区域编号" prop="zoneCode">
          <el-input
            v-model="queryParams.zoneCode"
            placeholder="请输入区域编号"
            clearable
            prefix-icon="el-icon-document"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="区域名称" prop="zoneName">
          <el-input
            v-model="queryParams.zoneName"
            placeholder="请输入区域名称"
            clearable
            prefix-icon="el-icon-office-building"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="类型" prop="zoneType">
          <el-select v-model="queryParams.zoneType" placeholder="请选择区域类型" clearable>
            <el-option label="普通区" value="0" />
            <el-option label="冷藏区" value="1" />
            <el-option label="危险品区" value="2" />
            <el-option label="贵重物品区" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option label="正常" value="0" />
            <el-option label="停用" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="table-container" shadow="hover" style="margin-top: 10px;">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['warehouse:zone:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['warehouse:zone:edit']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['warehouse:zone:remove']"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['warehouse:zone:export']"
          >导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="zoneList" @selection-change="handleSelectionChange" border stripe highlight-current-row>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="区域ID" align="center" prop="id" width="80" />
        <el-table-column label="仓库名称" align="center" prop="warehouseName" min-width="120" show-overflow-tooltip />
        <el-table-column label="区域编号" align="center" prop="zoneCode" min-width="120" />
        <el-table-column label="区域名称" align="center" prop="zoneName" min-width="120" show-overflow-tooltip />
        <el-table-column label="区域类型" align="center" prop="zoneType" width="100">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.zoneType == '0'" type="success">普通区</el-tag>
            <el-tag v-else-if="scope.row.zoneType == '1'" type="info">冷藏区</el-tag>
            <el-tag v-else-if="scope.row.zoneType == '2'" type="danger">危险品区</el-tag>
            <el-tag v-else-if="scope.row.zoneType == '3'" type="warning">贵重物品区</el-tag>
            <el-tag v-else>{{ scope.row.zoneType }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="面积(㎡)" align="center" prop="area" width="100" />
        <el-table-column label="温度℃" align="center" prop="temperature" width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.temperature">{{ scope.row.temperature }}℃</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="湿度(%)" align="center" prop="humidity" width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.humidity">{{ scope.row.humidity }}%</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status" width="80">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="'0'"
              :inactive-value="'1'"
              @change="handleStatusChange(scope.row)"
              active-color="#13ce66"
              inactive-color="#ff4949">
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
              v-hasPermi="['warehouse:zone:query']"
            >详情</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['warehouse:zone:edit']"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['warehouse:zone:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改仓库区域对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="所属仓库" prop="warehouseId">
          <el-select v-model="form.warehouseId" placeholder="请选择仓库" style="width: 100%" @change="handleWarehouseChange">
            <el-option
              v-for="item in warehouseOptions"
              :key="item.id"
              :label="item.warehouseName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="区域编号" prop="zoneCode">
              <el-input v-model="form.zoneCode" placeholder="请输入区域编号" :disabled="formDisabled" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="区域名称" prop="zoneName">
              <el-input v-model="form.zoneName" placeholder="请输入区域名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="区域类型" prop="zoneType">
              <el-select v-model="form.zoneType" placeholder="请选择区域类型" style="width: 100%">
                <el-option label="普通区" value="0" />
                <el-option label="冷藏区" value="1" />
                <el-option label="危险品区" value="2" />
                <el-option label="贵重物品区" value="3" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="面积(平方米)" prop="area">
              <el-input-number v-model="form.area" :min="0" :precision="2" :step="10" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="温度(℃)" prop="temperature">
              <el-input-number v-model="form.temperature" :precision="1" :step="0.5" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="湿度(%)" prop="humidity">
              <el-input-number v-model="form.humidity" :min="0" :max="100" :precision="1" :step="1" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio label="0">正常</el-radio>
                <el-radio label="1">停用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 仓库区域详情对话框 -->
    <el-dialog title="区域详情" :visible.sync="viewDialog" width="700px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="所属仓库">{{ viewForm.warehouseName }}</el-descriptions-item>
        <el-descriptions-item label="区域编号">{{ viewForm.zoneCode }}</el-descriptions-item>
        <el-descriptions-item label="区域名称">{{ viewForm.zoneName }}</el-descriptions-item>
        <el-descriptions-item label="区域类型">
          <el-tag v-if="viewForm.zoneType == '0'" type="success">普通区</el-tag>
          <el-tag v-else-if="viewForm.zoneType == '1'" type="info">冷藏区</el-tag>
          <el-tag v-else-if="viewForm.zoneType == '2'" type="danger">危险品区</el-tag>
          <el-tag v-else-if="viewForm.zoneType == '3'" type="warning">贵重物品区</el-tag>
          <el-tag v-else>{{ viewForm.zoneType }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="面积(平方米)">{{ viewForm.area }}</el-descriptions-item>
        <el-descriptions-item label="温度">{{ viewForm.temperature ? viewForm.temperature + '℃' : '-' }}</el-descriptions-item>
        <el-descriptions-item label="湿度">{{ viewForm.humidity ? viewForm.humidity + '%' : '-' }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag v-if="viewForm.status == '0'" type="success">正常</el-tag>
          <el-tag v-else-if="viewForm.status == '1'" type="danger">停用</el-tag>
          <el-tag v-else>{{ viewForm.status }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ viewForm.remark }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewDialog = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listZone, getZone, delZone, addZone, updateZone, changeStatus } from "@/api/warehouse/zone"
import { listWarehouse, getWarehouse } from "@/api/warehouse/warehouse"

export default {
  name: "Zone",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 仓库区域表格数据
      zoneList: [],
      // 仓库选项
      warehouseOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示详情弹出层
      viewDialog: false,
      // 详情表单
      viewForm: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        warehouseId: null,
        zoneCode: null,
        zoneName: null,
        zoneType: null,
        area: null,
        temperature: null,
        humidity: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        warehouseId: [
          { required: true, message: "所属仓库不能为空", trigger: "change" }
        ],
        zoneCode: [
          { required: true, message: "区域编号不能为空", trigger: "blur" }
        ],
        zoneName: [
          { required: true, message: "区域名称不能为空", trigger: "blur" }
        ],
        zoneType: [
          { required: true, message: "区域类型不能为空", trigger: "change" }
        ],
        area: [
          { required: true, message: "面积不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ],
      },
      // 表单禁用状态
      formDisabled: false,
      // 当前选中的仓库编码
      currentWarehouseCode: '',
      // 区域原始编码（不带仓库前缀）
      originalZoneCode: '',
    }
  },
  created() {
    this.getList()
    this.getWarehouseOptions()
  },
  methods: {
    /** 查询仓库区域列表 */
    getList() {
      this.loading = true
      listZone(this.queryParams).then(response => {
        console.log("获取区域列表响应:", response.rows)
        this.zoneList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 查询仓库选项 */
    getWarehouseOptions() {
      listWarehouse().then(response => {
        this.warehouseOptions = response.rows
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        warehouseId: null,
        warehouseName: null,
        zoneCode: null,
        zoneName: null,
        zoneType: "0",
        area: 0,
        temperature: null,
        humidity: null,
        status: "0",
        remark: null
      }
      this.formDisabled = false
      this.currentWarehouseCode = ''
      this.originalZoneCode = ''
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加仓库区域"
      this.formDisabled = false
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getZone(id).then(response => {
        console.log("获取区域信息响应:", response.data)
        this.form = response.data
        // 保存原始的区域编码
        if (this.form.warehouseId && this.form.zoneCode) {
          // 查询当前仓库的编码
          this.getWarehouseCode(this.form.warehouseId)
        }
        this.open = true
        this.title = "修改仓库区域"
        this.formDisabled = true
      })
    },
    /** 获取仓库编码 */
    getWarehouseCode(warehouseId) {
      getWarehouse(warehouseId).then(response => {
        console.log("获取仓库信息响应:", response.data)
        if (response.data && response.data.warehouseCode) {
          this.currentWarehouseCode = response.data.warehouseCode
          // 确保仓库名称被保存
          this.form.warehouseName = response.data.warehouseName
          console.log("设置仓库名称:", this.form.warehouseName)
          // 如果区域编码以仓库编码开头，则提取原始区域编码
          if (this.form.zoneCode && this.form.zoneCode.startsWith(this.currentWarehouseCode)) {
            this.originalZoneCode = this.form.zoneCode.substring(this.currentWarehouseCode.length)
          } else {
            this.originalZoneCode = this.form.zoneCode
          }
        }
      })
    },
    /** 仓库选择变更 */
    handleWarehouseChange(warehouseId) {
      if (!warehouseId) {
        this.currentWarehouseCode = ''
        this.form.warehouseName = ''
        return
      }
      // 获取选择的仓库编码和名称
      getWarehouse(warehouseId).then(response => {
        if (response.data) {
          // 保存仓库编码
          if (response.data.warehouseCode) {
            this.currentWarehouseCode = response.data.warehouseCode
            // 如果已有区域编码，则自动组合
            if (this.originalZoneCode) {
              this.form.zoneCode = this.currentWarehouseCode + this.originalZoneCode
            } else {
              this.form.zoneCode = this.currentWarehouseCode
            }
          }
          
          // 保存仓库名称
          if (response.data.warehouseName) {
            this.form.warehouseName = response.data.warehouseName
          }
        }
      })
    },
    /** 查看详情按钮操作 */
    handleView(row) {
      this.viewForm = {}
      getZone(row.id).then(response => {
        this.viewForm = response.data
        // 仓库名称已经从后端返回，不需要再查询
        this.viewDialog = true
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 确保区域编码包含仓库编码前缀
          if (this.currentWarehouseCode && !this.form.zoneCode.startsWith(this.currentWarehouseCode)) {
            this.form.zoneCode = this.currentWarehouseCode + this.form.zoneCode
          }
          
          // 确保仓库名称已设置
          if (this.form.warehouseId && (!this.form.warehouseName || this.form.warehouseName === '')) {
            // 如果仓库名称未设置，则从仓库选项中获取
            const selectedWarehouse = this.warehouseOptions.find(item => item.id === this.form.warehouseId)
            if (selectedWarehouse) {
              this.form.warehouseName = selectedWarehouse.warehouseName
              console.log("从仓库选项中设置仓库名称:", this.form.warehouseName)
            }
          }
          
          // 创建表单数据副本
          const formData = JSON.parse(JSON.stringify(this.form))
          console.log("提交的表单数据:", formData)
          
          if (formData.id != null) {
            updateZone(formData).then(response => {
              console.log("更新区域响应:", response)
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            }).catch(error => {
              console.error("修改失败:", error)
              this.$modal.msgError("修改失败：" + (error.message || "未知错误"))
            })
          } else {
            addZone(formData).then(response => {
              console.log("新增区域响应:", response)
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            }).catch(error => {
              console.error("新增失败:", error)
              this.$modal.msgError("新增失败：" + (error.message || "未知错误"))
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除仓库区域编号为"' + ids + '"的数据项？').then(() => {
        return delZone(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('warehouse/zone/export', {
        ...this.queryParams
      }, `zone_${new Date().getTime()}.xlsx`)
    },
    /** 状态修改 */
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用"
      this.$modal.confirm('确认要"' + text + '""' + row.zoneName + '"区域吗?').then(() => {
        return changeStatus(row.id, row.status)
      }).then(() => {
        this.$modal.msgSuccess(text + "成功")
      }).catch(() => {
        row.status = row.status === "0" ? "1" : "0"
      })
    }
  }
}
</script>

<style scoped>
.filter-container {
  margin-bottom: 10px;
}
.table-container {
  margin-bottom: 20px;
}
.el-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>
