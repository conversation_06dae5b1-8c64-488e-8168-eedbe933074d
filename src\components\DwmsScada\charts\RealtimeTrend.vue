<template>
  <div class="realtime-trend" :style="containerStyle">
    <div ref="chartContainer" class="chart-container"></div>
    <div v-if="showControls" class="chart-controls">
      <el-button-group size="mini">
        <el-button :type="timeRange === '1m' ? 'primary' : ''" @click="setTimeRange('1m')">1分钟</el-button>
        <el-button :type="timeRange === '5m' ? 'primary' : ''" @click="setTimeRange('5m')">5分钟</el-button>
        <el-button :type="timeRange === '15m' ? 'primary' : ''" @click="setTimeRange('15m')">15分钟</el-button>
        <el-button :type="timeRange === '1h' ? 'primary' : ''" @click="setTimeRange('1h')">1小时</el-button>
      </el-button-group>
      <el-button size="mini" icon="el-icon-refresh" @click="refreshData"></el-button>
      <el-button size="mini" :icon="isPlaying ? 'el-icon-video-pause' : 'el-icon-video-play'" @click="togglePlay"></el-button>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'RealtimeTrend',
  props: {
    config: {
      type: Object,
      default: () => ({})
    },
    dataSource: {
      type: String,
      default: ''
    },
    width: {
      type: Number,
      default: 400
    },
    height: {
      type: Number,
      default: 300
    },
    maxDataPoints: {
      type: Number,
      default: 100
    }
  },
  data() {
    return {
      chart: null,
      chartData: [],
      timeRange: '5m',
      isPlaying: true,
      updateTimer: null,
      websocket: null
    }
  },
  computed: {
    containerStyle() {
      return {
        width: this.width + 'px',
        height: this.height + 'px',
        position: 'relative'
      }
    },
    showControls() {
      return this.config.showControls !== false
    },
    updateInterval() {
      const intervals = {
        '1m': 1000,   // 1秒更新
        '5m': 5000,   // 5秒更新
        '15m': 15000, // 15秒更新
        '1h': 60000   // 1分钟更新
      }
      return intervals[this.timeRange] || 5000
    },
    maxPoints() {
      const points = {
        '1m': 60,    // 60个点，1分钟
        '5m': 60,    // 60个点，5分钟
        '15m': 60,   // 60个点，15分钟
        '1h': 60     // 60个点，1小时
      }
      return points[this.timeRange] || 60
    }
  },
  watch: {
    dataSource: {
      handler() {
        this.initWebSocket()
      },
      immediate: true
    },
    timeRange() {
      this.restartUpdate()
    },
    isPlaying() {
      if (this.isPlaying) {
        this.startUpdate()
      } else {
        this.stopUpdate()
      }
    }
  },
  mounted() {
    this.initChart()
    this.initData()
    this.startUpdate()
  },
  beforeDestroy() {
    this.cleanup()
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chartContainer)
      
      const option = {
        title: {
          text: this.config.title || '',
          left: 'center',
          textStyle: {
            fontSize: 14,
            color: this.config.titleColor || '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            let result = params[0].axisValueLabel + '<br/>'
            params.forEach(param => {
              result += `${param.seriesName}: ${param.value[1]} ${this.config.unit || ''}<br/>`
            })
            return result
          }
        },
        legend: {
          data: this.config.series?.map(s => s.name) || ['数据'],
          bottom: 10
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: this.showControls ? '15%' : '10%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'time',
          boundaryGap: false,
          axisLabel: {
            formatter: (value) => {
              const date = new Date(value)
              return date.getHours().toString().padStart(2, '0') + ':' + 
                     date.getMinutes().toString().padStart(2, '0') + ':' +
                     date.getSeconds().toString().padStart(2, '0')
            }
          }
        },
        yAxis: {
          type: 'value',
          name: this.config.yAxisName || '',
          nameTextStyle: {
            color: this.config.axisColor || '#666'
          },
          axisLabel: {
            formatter: (value) => {
              return value + (this.config.unit || '')
            }
          }
        },
        series: this.createSeries()
      }
      
      this.chart.setOption(option)
      
      // 响应式调整
      window.addEventListener('resize', this.handleResize)
    },
    
    createSeries() {
      const series = []
      const seriesConfig = this.config.series || [{ name: '数据', color: '#409EFF' }]
      
      seriesConfig.forEach((config, index) => {
        series.push({
          name: config.name,
          type: 'line',
          smooth: config.smooth !== false,
          symbol: config.showSymbol ? 'circle' : 'none',
          symbolSize: config.symbolSize || 4,
          lineStyle: {
            color: config.color || this.getDefaultColor(index),
            width: config.lineWidth || 2
          },
          itemStyle: {
            color: config.color || this.getDefaultColor(index)
          },
          areaStyle: config.showArea ? {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: config.color || this.getDefaultColor(index) },
              { offset: 1, color: 'transparent' }
            ])
          } : null,
          data: []
        })
      })
      
      return series
    },
    
    getDefaultColor(index) {
      const colors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399']
      return colors[index % colors.length]
    },
    
    initData() {
      // 初始化一些历史数据
      const now = Date.now()
      const interval = this.updateInterval
      
      for (let i = this.maxPoints - 1; i >= 0; i--) {
        const time = now - (i * interval)
        const dataPoint = {
          time: time,
          values: this.generateRandomData()
        }
        this.chartData.push(dataPoint)
      }
      
      this.updateChart()
    },
    
    generateRandomData() {
      const seriesConfig = this.config.series || [{ name: '数据' }]
      const values = []
      
      seriesConfig.forEach((config, index) => {
        const baseValue = config.baseValue || 50
        const variance = config.variance || 20
        const value = baseValue + (Math.random() - 0.5) * variance
        values.push(Math.max(0, value))
      })
      
      return values
    },
    
    addDataPoint(data) {
      const now = Date.now()
      let values
      
      if (data && Array.isArray(data)) {
        values = data
      } else if (data && typeof data === 'object') {
        // 从WebSocket数据中提取值
        const seriesConfig = this.config.series || [{ name: '数据' }]
        values = seriesConfig.map(config => data[config.field] || 0)
      } else {
        values = this.generateRandomData()
      }
      
      const dataPoint = {
        time: now,
        values: values
      }
      
      this.chartData.push(dataPoint)
      
      // 保持数据点数量在限制内
      if (this.chartData.length > this.maxPoints) {
        this.chartData.shift()
      }
      
      this.updateChart()
    },
    
    updateChart() {
      if (!this.chart) return
      
      const seriesData = []
      const seriesConfig = this.config.series || [{ name: '数据' }]
      
      // 为每个系列准备数据
      seriesConfig.forEach((config, index) => {
        const data = this.chartData.map(point => [point.time, point.values[index] || 0])
        seriesData.push(data)
      })
      
      // 更新图表数据
      const option = {
        series: seriesData.map((data, index) => ({
          data: data
        }))
      }
      
      this.chart.setOption(option)
    },
    
    initWebSocket() {
      if (!this.dataSource) return
      
      this.closeWebSocket()
      
      try {
        const wsUrl = this.dataSource.startsWith('ws') ? this.dataSource : `ws://localhost:8080/ws/${this.dataSource}`
        this.websocket = new WebSocket(wsUrl)
        
        this.websocket.onopen = () => {
          console.log('WebSocket连接已建立')
        }
        
        this.websocket.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)
            this.addDataPoint(data)
          } catch (error) {
            console.error('解析WebSocket数据失败:', error)
          }
        }
        
        this.websocket.onerror = (error) => {
          console.error('WebSocket错误:', error)
        }
        
        this.websocket.onclose = () => {
          console.log('WebSocket连接已关闭')
          // 自动重连
          setTimeout(() => {
            if (this.isPlaying) {
              this.initWebSocket()
            }
          }, 5000)
        }
      } catch (error) {
        console.error('创建WebSocket连接失败:', error)
      }
    },
    
    closeWebSocket() {
      if (this.websocket) {
        this.websocket.close()
        this.websocket = null
      }
    },
    
    startUpdate() {
      if (this.updateTimer) return
      
      this.updateTimer = setInterval(() => {
        if (!this.websocket || this.websocket.readyState !== WebSocket.OPEN) {
          // 如果没有WebSocket连接，生成模拟数据
          this.addDataPoint()
        }
      }, this.updateInterval)
    },
    
    stopUpdate() {
      if (this.updateTimer) {
        clearInterval(this.updateTimer)
        this.updateTimer = null
      }
    },
    
    restartUpdate() {
      this.stopUpdate()
      if (this.isPlaying) {
        this.startUpdate()
      }
    },
    
    setTimeRange(range) {
      this.timeRange = range
      this.chartData = [] // 清空数据
      this.initData() // 重新初始化数据
    },
    
    refreshData() {
      this.chartData = []
      this.initData()
    },
    
    togglePlay() {
      this.isPlaying = !this.isPlaying
    },
    
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    
    cleanup() {
      this.stopUpdate()
      this.closeWebSocket()
      
      if (this.chart) {
        this.chart.dispose()
      }
      
      window.removeEventListener('resize', this.handleResize)
    }
  }
}
</script>

<style lang="scss" scoped>
.realtime-trend {
  position: relative;
  
  .chart-container {
    width: 100%;
    height: 100%;
  }
  
  .chart-controls {
    position: absolute;
    bottom: 5px;
    right: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.9);
    padding: 4px 8px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}
</style>
