<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="协议名称" prop="protocolName">
        <el-input
          v-model="queryParams.protocolName"
          placeholder="请输入协议名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="协议类型" prop="protocolType">
        <el-select v-model="queryParams.protocolType" placeholder="请选择协议类型" clearable>
          <el-option label="TCP" value="tcp" />
          <el-option label="UDP" value="udp" />
          <el-option label="HTTP" value="http" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="启用" value="1" />
          <el-option label="禁用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['access:protocol:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['access:protocol:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['access:protocol:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-unlock"
          size="mini"
          :disabled="multiple"
          @click="handleRemoteOpen"
          v-hasPermi="['access:protocol:open']"
        >远程开门</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="protocolList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="协议名称" align="center" prop="protocolName" />
      <el-table-column label="协议类型" align="center" prop="protocolType">
        <template slot-scope="scope">
          <el-tag :type="scope.row.protocolType === 'tcp' ? 'primary' : scope.row.protocolType === 'udp' ? 'success' : 'warning'">
            {{ scope.row.protocolType.toUpperCase() }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="协议版本" align="center" prop="protocolVersion" />
      <el-table-column label="数据格式" align="center" prop="dataFormat" />
      <el-table-column label="服务器端口" align="center" prop="serverPort" />
      <el-table-column label="最大连接数" align="center" prop="maxConnections" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['access:protocol:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['access:protocol:remove']"
          >删除</el-button>
          <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)" v-hasPermi="['access:protocol:open', 'access:protocol:log', 'access:protocol:sync']">
            <el-button size="mini" type="text" icon="el-icon-d-arrow-right">更多</el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="open" icon="el-icon-unlock">远程开门</el-dropdown-item>
              <el-dropdown-item command="log" icon="el-icon-document">事件日志</el-dropdown-item>
              <el-dropdown-item command="sync" icon="el-icon-refresh">权限同步</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改协议对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="协议名称" prop="protocolName">
          <el-input v-model="form.protocolName" placeholder="请输入协议名称" />
        </el-form-item>
        <el-form-item label="协议类型" prop="protocolType">
          <el-select v-model="form.protocolType" placeholder="请选择协议类型">
            <el-option label="TCP" value="tcp" />
            <el-option label="UDP" value="udp" />
            <el-option label="HTTP" value="http" />
          </el-select>
        </el-form-item>
        <el-form-item label="协议版本" prop="protocolVersion">
          <el-input v-model="form.protocolVersion" placeholder="请输入协议版本" />
        </el-form-item>
        <el-form-item label="数据格式" prop="dataFormat">
          <el-select v-model="form.dataFormat" placeholder="请选择数据格式">
            <el-option label="JSON" value="json" />
            <el-option label="XML" value="xml" />
            <el-option label="二进制" value="binary" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </el-form-item>
        <el-form-item label="服务器端口" prop="serverPort">
          <el-input-number v-model="form.serverPort" :min="1" :max="65535" />
        </el-form-item>
        <el-form-item label="最大连接数" prop="maxConnections">
          <el-input-number v-model="form.maxConnections" :min="1" :max="10000" />
        </el-form-item>
        <el-form-item label="超时时间" prop="timeoutSeconds">
          <el-input-number v-model="form.timeoutSeconds" :min="1" :max="300" />
          <span style="margin-left: 10px;">秒</span>
        </el-form-item>
        <el-form-item label="心跳间隔" prop="heartbeatInterval">
          <el-input-number v-model="form.heartbeatInterval" :min="10" :max="600" />
          <span style="margin-left: 10px;">秒</span>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "AccessProtocol",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 协议表格数据
      protocolList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        protocolName: null,
        protocolType: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        protocolName: [
          { required: true, message: "协议名称不能为空", trigger: "blur" }
        ],
        protocolType: [
          { required: true, message: "协议类型不能为空", trigger: "change" }
        ],
        serverPort: [
          { required: true, message: "服务器端口不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询协议列表 */
    getList() {
      this.loading = true;
      // 模拟数据
      this.protocolList = [
        {
          protocolId: 1,
          protocolName: "门禁TCP协议",
          protocolType: "tcp",
          protocolVersion: "1.0",
          dataFormat: "json",
          serverPort: 8001,
          maxConnections: 100,
          timeoutSeconds: 30,
          heartbeatInterval: 60,
          status: 1,
          description: "门禁设备TCP通信协议"
        },
        {
          protocolId: 2,
          protocolName: "门禁HTTP协议",
          protocolType: "http",
          protocolVersion: "1.0",
          dataFormat: "json",
          serverPort: 8002,
          maxConnections: 50,
          timeoutSeconds: 60,
          heartbeatInterval: 120,
          status: 1,
          description: "门禁设备HTTP通信协议"
        }
      ];
      this.total = this.protocolList.length;
      this.loading = false;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        protocolId: null,
        protocolName: null,
        protocolType: null,
        protocolVersion: "1.0",
        dataFormat: "json",
        serverPort: null,
        maxConnections: 100,
        timeoutSeconds: 30,
        heartbeatInterval: 60,
        status: 1,
        description: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.protocolId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加门禁协议";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.form = Object.assign({}, row);
      this.open = true;
      this.title = "修改门禁协议";
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.protocolId != null) {
            console.log('修改门禁协议:', this.form);
            this.$modal.msgSuccess("修改成功");
          } else {
            console.log('新增门禁协议:', this.form);
            this.$modal.msgSuccess("新增成功");
          }
          this.open = false;
          this.getList();
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const protocolIds = row.protocolId || this.ids;
      this.$modal.confirm('是否确认删除协议编号为"' + protocolIds + '"的数据项？').then(function() {
        console.log('删除门禁协议:', protocolIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 远程开门按钮操作 */
    handleRemoteOpen() {
      const protocolIds = this.ids;
      this.$modal.confirm('是否确认对选中的门禁设备执行远程开门操作?').then(function() {
        console.log('远程开门:', protocolIds);
      }).then(() => {
        this.$modal.msgSuccess("远程开门指令发送成功");
      }).catch(() => {});
    },
    /** 更多操作 */
    handleCommand(command, row) {
      switch (command) {
        case 'open':
          this.$modal.confirm('是否确认对"' + row.protocolName + '"执行远程开门操作?').then(function() {
            console.log('远程开门:', row.protocolId);
          }).then(() => {
            this.$modal.msgSuccess("远程开门指令发送成功");
          }).catch(() => {});
          break;
        case 'log':
          this.$router.push({
            path: '/netty/message-log',
            query: { protocolId: row.protocolId }
          });
          break;
        case 'sync':
          this.$modal.confirm('是否确认对"' + row.protocolName + '"执行权限同步操作?').then(function() {
            console.log('权限同步:', row.protocolId);
          }).then(() => {
            this.$modal.msgSuccess("权限同步完成");
          }).catch(() => {});
          break;
      }
    }
  }
};
</script>
