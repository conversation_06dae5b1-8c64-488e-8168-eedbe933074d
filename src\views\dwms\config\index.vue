<template>
  <div class="scada-container">
    <!-- 工具栏 -->
    <div class="scada-toolbar">
      <div class="toolbar-left">
        <el-button-group>
          <el-button size="small" @click="newCanvas">
            <i class="el-icon-document"></i> 新建
          </el-button>
          <el-button size="small" @click="openCanvas">
            <i class="el-icon-folder-opened"></i> 打开
          </el-button>
          <el-button size="small" @click="saveCanvas">
            <i class="el-icon-document-checked"></i> 保存
          </el-button>
        </el-button-group>

        <el-divider direction="vertical"></el-divider>

        <el-button-group>
          <el-button size="small" @click="undo">
            <i class="el-icon-refresh-left"></i> 撤销
          </el-button>
          <el-button size="small" @click="redo">
            <i class="el-icon-refresh-right"></i> 重做
          </el-button>
        </el-button-group>

        <el-divider direction="vertical"></el-divider>

        <el-button-group>
          <el-button size="small" @click="zoomIn">
            <i class="el-icon-zoom-in"></i>
          </el-button>
          <el-button size="small" @click="resetZoom">
            {{ Math.round(scale * 100) }}%
          </el-button>
          <el-button size="small" @click="zoomOut">
            <i class="el-icon-zoom-out"></i>
          </el-button>
        </el-button-group>

        <el-divider direction="vertical"></el-divider>

        <el-button-group>
          <el-button size="small" :type="mode === 'edit' ? 'primary' : 'default'" @click="setMode('edit')">
            <i class="el-icon-edit"></i> 编辑
          </el-button>
          <el-button size="small" :type="mode === 'preview' ? 'primary' : 'default'" @click="setMode('preview')">
            <i class="el-icon-view"></i> 预览
          </el-button>
          <el-button size="small" :type="mode === 'monitor' ? 'primary' : 'default'" @click="setMode('monitor')">
            <i class="el-icon-monitor"></i> 监控
          </el-button>
        </el-button-group>
      </div>

      <div class="toolbar-right">
        <!-- 状态信息 -->
        <span class="status-info">
          组件: {{ canvasNodes.length }} |
          画布: {{ currentCanvas.name || '未命名' }}
        </span>

        <el-divider direction="vertical"></el-divider>

        <el-button size="small" @click="showSettings = true">
          <i class="el-icon-setting"></i> 设置
        </el-button>
        <el-button size="small" @click="toggleFullscreen">
          <i class="el-icon-full-screen"></i> 全屏
        </el-button>
      </div>
    </div>

    <!-- 主要工作区 -->
    <div class="scada-workspace">
      <!-- 左侧组件库 -->
      <div class="component-panel" v-show="mode === 'edit'">
        <div class="panel-header">
          <h4>组件库</h4>
        </div>
        <el-collapse v-model="activeCollapse" accordion>
          <!-- 基础图形 -->
          <el-collapse-item title="基础图形" name="basic">
            <div class="component-grid">
              <div
                v-for="component in basicComponents"
                :key="component.type"
                class="component-item"
                @click="addComponent(component)"
                :title="component.name"
              >
                <i :class="component.icon"></i>
                <span>{{ component.name }}</span>
              </div>
            </div>
          </el-collapse-item>

          <!-- 仓储设备 -->
          <el-collapse-item title="仓储设备" name="warehouse">
            <div class="component-grid">
              <div
                v-for="component in warehouseComponents"
                :key="component.type"
                class="component-item"
                @click="addComponent(component)"
                :title="component.name"
              >
                <i :class="component.icon"></i>
                <span>{{ component.name }}</span>
              </div>
            </div>
          </el-collapse-item>

          <!-- 传感器设备 -->
          <el-collapse-item title="传感器设备" name="sensor">
            <div class="component-grid">
              <div
                v-for="component in sensorComponents"
                :key="component.type"
                class="component-item"
                @click="addComponent(component)"
                :title="component.name"
              >
                <i :class="component.icon"></i>
                <span>{{ component.name }}</span>
              </div>
            </div>
          </el-collapse-item>

          <!-- 监控设备 -->
          <el-collapse-item title="监控设备" name="monitor">
            <div class="component-grid">
              <div
                v-for="component in monitorComponents"
                :key="component.type"
                class="component-item"
                @click="addComponent(component)"
                :title="component.name"
              >
                <i :class="component.icon"></i>
                <span>{{ component.name }}</span>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>

      <!-- 中央画布区域 -->
      <div class="canvas-container">
        <div class="canvas-header">
          <div class="canvas-info">
            <span>{{ currentCanvas.name || '未命名画布' }}</span>
            <span class="canvas-size">{{ canvasWidth }} × {{ canvasHeight }}</span>
          </div>
          <div class="canvas-controls">
            <el-button-group size="mini">
              <el-button @click="alignLeft" :disabled="!selectedNodes.length">
                <i class="el-icon-d-arrow-left"></i>
              </el-button>
              <el-button @click="alignCenter" :disabled="!selectedNodes.length">
                <i class="el-icon-remove"></i>
              </el-button>
              <el-button @click="alignRight" :disabled="!selectedNodes.length">
                <i class="el-icon-d-arrow-right"></i>
              </el-button>
            </el-button-group>
          </div>
        </div>

        <!-- 画布主体 -->
        <div class="canvas-wrapper" ref="canvasWrapper">
          <div
            class="canvas-main"
            ref="canvasMain"
            :style="{
              width: canvasWidth + 'px',
              height: canvasHeight + 'px',
              transform: `scale(${scale})`,
              transformOrigin: 'top left'
            }"
            @contextmenu.prevent="showContextMenu"
          >
            <!-- 网格背景 -->
            <div class="canvas-grid" v-show="showGrid"></div>

            <!-- 画布内容 -->
            <div id="scada-canvas" ref="scadaCanvas"></div>

            <!-- 右键菜单 -->
            <div
              v-show="contextMenuVisible"
              class="context-menu"
              :style="{ left: contextMenuX + 'px', top: contextMenuY + 'px' }"
              @click.stop
            >
              <div class="context-menu-item" @click="copyNode" v-if="selectedNodes.length > 0">
                <i class="el-icon-document-copy"></i> 复制
              </div>
              <div class="context-menu-item" @click="deleteSelectedNodes" v-if="selectedNodes.length > 0">
                <i class="el-icon-delete"></i> 删除
              </div>
              <div class="context-menu-divider" v-if="selectedNodes.length > 0"></div>
              <div class="context-menu-item" @click="bringToFront" v-if="selectedNodes.length > 0">
                <i class="el-icon-top"></i> 置于顶层
              </div>
              <div class="context-menu-item" @click="sendToBack" v-if="selectedNodes.length > 0">
                <i class="el-icon-bottom"></i> 置于底层
              </div>
              <div class="context-menu-divider"></div>
              <div class="context-menu-item" @click="selectAll">
                <i class="el-icon-circle-check"></i> 全选
              </div>
            </div>

            <!-- 实时数据覆盖层 -->
            <div class="data-overlay" v-show="mode === 'monitor'">
              <div
                v-for="dataPoint in realtimeData"
                :key="dataPoint.id"
                class="data-point"
                :style="{
                  left: dataPoint.x + 'px',
                  top: dataPoint.y + 'px'
                }"
              >
                <div class="data-value" :class="getDataStatus(dataPoint)">
                  {{ formatDataValue(dataPoint) }}
                </div>
                <div class="data-label">{{ dataPoint.label }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 状态栏 -->
        <div class="canvas-status">
          <div class="status-left">
            <span>模式: {{ getModeText() }}</span>
            <span>选中: {{ selectedNodes.length }} 个对象</span>
            <span v-if="mode === 'monitor'">连接状态:
              <el-tag :type="mqttConnected ? 'success' : 'danger'" size="mini">
                {{ mqttConnected ? '已连接' : '未连接' }}
              </el-tag>
            </span>
          </div>
          <div class="status-right">
            <span>坐标: ({{ mousePosition.x }}, {{ mousePosition.y }})</span>
            <span>画布: {{ canvasWidth }} × {{ canvasHeight }}</span>
          </div>
        </div>
      </div>

      <!-- 右侧属性面板 -->
      <div class="property-panel" v-show="mode === 'edit'">
        <div class="panel-header">
          <h4>属性面板</h4>
        </div>

        <div v-if="!selectedNodes.length" class="no-selection">
          <p>请选择一个组件</p>
        </div>

        <div v-else class="property-content">
          <el-tabs v-model="activePropertyTab" type="border-card">
            <!-- 基础属性 -->
            <el-tab-pane label="基础" name="basic">
              <el-form size="mini" label-width="60px">
                <el-form-item label="名称">
                  <el-input v-model="currentNode.text" @change="updateNodeProperty" />
                </el-form-item>
                <el-form-item label="X坐标">
                  <el-input-number v-model="currentNode.x" @change="updateNodeProperty" />
                </el-form-item>
                <el-form-item label="Y坐标">
                  <el-input-number v-model="currentNode.y" @change="updateNodeProperty" />
                </el-form-item>
                <el-form-item label="宽度">
                  <el-input-number v-model="currentNode.width" @change="updateNodeProperty" />
                </el-form-item>
                <el-form-item label="高度">
                  <el-input-number v-model="currentNode.height" @change="updateNodeProperty" />
                </el-form-item>
                <el-form-item label="旋转">
                  <el-input-number v-model="currentNode.rotate" @change="updateNodeProperty" />
                </el-form-item>
              </el-form>
            </el-tab-pane>

            <!-- 样式属性 -->
            <el-tab-pane label="样式" name="style">
              <el-form size="mini" label-width="60px">
                <el-form-item label="填充色">
                  <el-color-picker v-model="currentNode.background" @change="updateNodeProperty" />
                </el-form-item>
                <el-form-item label="边框色">
                  <el-color-picker v-model="currentNode.color" @change="updateNodeProperty" />
                </el-form-item>
                <el-form-item label="边框宽度">
                  <el-input-number v-model="currentNode.lineWidth" @change="updateNodeProperty" />
                </el-form-item>
                <el-form-item label="透明度">
                  <el-slider v-model="currentNode.globalAlpha" :min="0" :max="1" :step="0.1" @change="updateNodeProperty" />
                </el-form-item>
              </el-form>
            </el-tab-pane>

            <!-- 数据绑定 -->
            <el-tab-pane label="数据" name="data">
              <el-form size="mini" label-width="60px">
                <el-form-item label="数据源">
                  <el-select v-model="currentNode.dataSource" @change="updateNodeProperty">
                    <el-option label="重量传感器" value="weight_sensor" />
                    <el-option label="温度传感器" value="temperature_sensor" />
                    <el-option label="湿度传感器" value="humidity_sensor" />
                    <el-option label="门禁状态" value="access_status" />
                  </el-select>
                </el-form-item>
                <el-form-item label="设备ID">
                  <el-input v-model="currentNode.deviceId" @change="updateNodeProperty" />
                </el-form-item>
                <el-form-item label="数据字段">
                  <el-input v-model="currentNode.dataField" @change="updateNodeProperty" />
                </el-form-item>
                <el-form-item label="更新频率">
                  <el-input-number v-model="currentNode.updateInterval" @change="updateNodeProperty" />
                </el-form-item>
              </el-form>
            </el-tab-pane>

            <!-- 动画效果 -->
            <el-tab-pane label="动画" name="animation">
              <el-form size="mini" label-width="60px">
                <el-form-item label="动画类型">
                  <el-select v-model="currentNode.animationType" @change="updateNodeProperty">
                    <el-option label="无" value="none" />
                    <el-option label="闪烁" value="blink" />
                    <el-option label="旋转" value="rotate" />
                    <el-option label="缩放" value="scale" />
                  </el-select>
                </el-form-item>
                <el-form-item label="动画速度">
                  <el-input-number v-model="currentNode.animationSpeed" @change="updateNodeProperty" />
                </el-form-item>
                <el-form-item label="触发条件">
                  <el-input v-model="currentNode.animationCondition" @change="updateNodeProperty" />
                </el-form-item>
              </el-form>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>

    <!-- 画布设置对话框 -->
    <el-dialog title="画布设置" :visible.sync="showSettings" width="500px">
      <el-form :model="canvasSettings" label-width="100px">
        <el-form-item label="画布名称">
          <el-input v-model="canvasSettings.name" />
        </el-form-item>
        <el-form-item label="画布宽度">
          <el-input-number v-model="canvasSettings.width" :min="800" :max="5000" />
        </el-form-item>
        <el-form-item label="画布高度">
          <el-input-number v-model="canvasSettings.height" :min="600" :max="3000" />
        </el-form-item>
        <el-form-item label="背景颜色">
          <el-color-picker v-model="canvasSettings.backgroundColor" />
        </el-form-item>
        <el-form-item label="显示网格">
          <el-switch v-model="canvasSettings.showGrid" />
        </el-form-item>
        <el-form-item label="网格大小">
          <el-input-number v-model="canvasSettings.gridSize" :min="10" :max="50" />
        </el-form-item>
        <el-form-item label="MQTT服务器">
          <el-input v-model="canvasSettings.mqttUrl" placeholder="ws://localhost:8083/mqtt" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showSettings = false">取 消</el-button>
        <el-button type="primary" @click="applySettings">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 保存画布对话框 -->
    <el-dialog title="保存画布" :visible.sync="showSaveDialog" width="400px">
      <el-form :model="saveForm" label-width="80px">
        <el-form-item label="画布名称" required>
          <el-input v-model="saveForm.name" placeholder="请输入画布名称" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="saveForm.description" type="textarea" placeholder="请输入描述" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showSaveDialog = false">取 消</el-button>
        <el-button type="primary" @click="confirmSave">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 打开画布对话框 -->
    <el-dialog title="打开画布" :visible.sync="showOpenDialog" width="600px">
      <el-table :data="canvasList" @row-click="selectCanvas" highlight-current-row>
        <el-table-column prop="name" label="画布名称" />
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="updateTime" label="修改时间" width="180" />
        <el-table-column label="操作" width="150">
          <template slot-scope="scope">
            <el-button size="mini" @click="loadCanvas(scope.row)">打开</el-button>
            <el-button size="mini" type="danger" @click.stop="deleteCanvas(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showOpenDialog = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import { listConfig, getConfig, delConfig, addConfig, updateConfig } from "@/api/dwms/config";

export default {
  name: "ScadaEditor",
  data() {
    return {
      // 编辑器模式: edit, preview, monitor
      mode: 'edit',
      // 画布缩放比例
      scale: 1,
      // 画布尺寸
      canvasWidth: 1200,
      canvasHeight: 800,
      // 显示网格
      showGrid: true,
      // 鼠标位置
      mousePosition: { x: 0, y: 0 },
      // 选中的节点
      selectedNodes: [],
      // 当前编辑的节点
      currentNode: {},
      // 组件库折叠状态
      activeCollapse: 'basic',
      // 属性面板标签页
      activePropertyTab: 'basic',
      // 对话框显示状态
      showSettings: false,
      showSaveDialog: false,
      showOpenDialog: false,
      // 当前画布信息
      currentCanvas: {
        id: null,
        name: '未命名画布',
        data: null
      },
      // 画布设置
      canvasSettings: {
        name: '仓库监控画布',
        width: 1200,
        height: 800,
        backgroundColor: '#f0f2f5',
        showGrid: true,
        gridSize: 20,
        mqttUrl: 'ws://localhost:8083/mqtt'
      },
      // 保存表单
      saveForm: {
        name: '',
        description: ''
      },
      // 画布列表
      canvasList: [],
      // 选中的画布ID
      selectedCanvasId: null,
      // MQTT连接状态
      mqttConnected: false,
      // 拖拽状态
      isDragging: false,
      dragStartX: 0,
      dragStartY: 0,
      dragOffsetX: 0,
      dragOffsetY: 0,
      dragElement: null,
      dragNode: null,
      // 右键菜单
      contextMenuVisible: false,
      contextMenuX: 0,
      contextMenuY: 0,
      // 实时数据
      realtimeData: [],
      // 画布节点数据
      canvasNodes: [],
      // 基础组件库
      basicComponents: [
        { type: 'rect', name: '矩形', icon: 'el-icon-crop' },
        { type: 'circle', name: '圆形', icon: 'el-icon-pie-chart' },
        { type: 'line', name: '直线', icon: 'el-icon-minus' },
        { type: 'text', name: '文本', icon: 'el-icon-edit-outline' },
        { type: 'image', name: '图片', icon: 'el-icon-picture' }
      ],
      // 仓储设备组件
      warehouseComponents: [
        { type: 'shelf', name: '货架', icon: 'el-icon-menu' },
        { type: 'forklift', name: '叉车', icon: 'el-icon-truck' },
        { type: 'conveyor', name: '传送带', icon: 'el-icon-right' },
        { type: 'door', name: '门', icon: 'el-icon-switch-button' },
        { type: 'area', name: '区域', icon: 'el-icon-crop' }
      ],
      // 传感器组件
      sensorComponents: [
        { type: 'weight_sensor', name: '重量传感器', icon: 'el-icon-scale-to-original' },
        { type: 'temp_sensor', name: '温度传感器', icon: 'el-icon-sunny' },
        { type: 'humidity_sensor', name: '湿度传感器', icon: 'el-icon-cloudy' },
        { type: 'smoke_sensor', name: '烟雾传感器', icon: 'el-icon-warning' }
      ],
      // 监控设备组件
      monitorComponents: [
        { type: 'camera', name: '摄像头', icon: 'el-icon-video-camera' },
        { type: 'access_control', name: '门禁', icon: 'el-icon-key' },
        { type: 'alarm', name: '报警器', icon: 'el-icon-bell' },
        { type: 'display', name: '显示屏', icon: 'el-icon-monitor' }
      ]
    };
  },
  mounted() {
    this.initCanvas();
    // 延迟加载画布列表，避免初始化时的API错误
    this.$nextTick(() => {
      this.loadCanvasList();
    });

    // 添加键盘事件监听
    document.addEventListener('keydown', this.onKeyDown);
  },
  methods: {
    // 初始化画布
    initCanvas() {
      // 这里可以集成 Le5le Topology 或其他绘图库
      // 由于需要外部依赖，这里先用简单的实现
      this.setupCanvasEvents();
    },

    // 设置画布事件
    setupCanvasEvents() {
      const canvas = this.$refs.scadaCanvas;
      if (canvas) {
        canvas.addEventListener('mousemove', this.onMouseMove);
        canvas.addEventListener('click', this.onCanvasClick);
      }
    },

    // 鼠标移动事件
    onMouseMove(event) {
      const rect = event.target.getBoundingClientRect();
      this.mousePosition.x = Math.round((event.clientX - rect.left) / this.scale);
      this.mousePosition.y = Math.round((event.clientY - rect.top) / this.scale);
    },

    // 画布点击事件
    onCanvasClick(event) {
      if (this.mode === 'edit') {
        // 如果点击的是画布空白区域，清除选择
        if (event.target === this.$refs.scadaCanvas) {
          this.clearSelection();
        }
        // 隐藏右键菜单
        this.contextMenuVisible = false;
      }
    },

    // 新建画布
    newCanvas() {
      this.$confirm('新建画布将清空当前内容，是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.clearCanvas();
        this.currentCanvas = {
          id: null,
          name: '未命名画布',
          data: null
        };
      });
    },

    // 打开画布
    openCanvas() {
      this.loadCanvasList();
      this.showOpenDialog = true;
    },

    // 保存画布列表到本地存储
    saveCanvasListToLocal() {
      try {
        localStorage.setItem('dwms_canvas_list', JSON.stringify(this.canvasList));
        console.log('画布列表已保存到本地存储');
      } catch (error) {
        console.error('保存画布列表到本地存储失败:', error);
      }
    },

    // 保存画布
    saveCanvas() {
      if (this.currentCanvas.id) {
        this.doSave();
      } else {
        this.saveForm.name = this.currentCanvas.name;
        this.showSaveDialog = true;
      }
    },

    // 确认保存
    confirmSave() {
      if (!this.saveForm.name) {
        this.$message.warning('请输入画布名称');
        return;
      }
      this.currentCanvas.name = this.saveForm.name;
      this.doSave();
      this.showSaveDialog = false;
    },

    // 执行保存
    doSave() {
      const canvasData = this.getCanvasData();
      console.log('保存画布数据:', canvasData); // 调试日志

      const saveData = {
        id: this.currentCanvas.id,
        configName: this.currentCanvas.name,
        configType: 'web_scada',
        configData: JSON.stringify(canvasData),
        warehouseId: 1,
        isActive: 1,
        description: this.saveForm.description
      };

      // 模拟保存成功
      if (this.currentCanvas.id) {
        // 更新现有画布
        const index = this.canvasList.findIndex(item => item.id === this.currentCanvas.id);
        if (index !== -1) {
          this.canvasList[index].name = this.currentCanvas.name;
          this.canvasList[index].description = this.saveForm.description;
          this.canvasList[index].updateTime = new Date().toLocaleString();
          this.canvasList[index].data = JSON.stringify(canvasData); // 重要：保存画布数据
        }
        this.$message.success(`保存成功！已保存 ${canvasData.nodes.length} 个组件`);
      } else {
        // 新建画布
        const newId = Date.now();
        this.currentCanvas.id = newId;
        this.canvasList.unshift({
          id: newId,
          name: this.currentCanvas.name,
          description: this.saveForm.description,
          updateTime: new Date().toLocaleString(),
          data: JSON.stringify(canvasData)
        });
        this.$message.success(`保存成功！已保存 ${canvasData.nodes.length} 个组件`);
      }

      // 保存到本地存储
      this.saveCanvasListToLocal();
      console.log('画布数据已保存到本地存储，包含', this.canvasList.length, '个画布');

      // 实际API调用（当后端API准备好时启用）
      /*
      if (this.currentCanvas.id) {
        updateConfig(saveData).then(() => {
          this.$message.success('保存成功');
        }).catch(error => {
          this.$message.error('保存失败');
        });
      } else {
        addConfig(saveData).then(response => {
          this.currentCanvas.id = response.data.id;
          this.$message.success('保存成功');
        }).catch(error => {
          this.$message.error('保存失败');
        });
      }
      */
    },

    // 删除画布
    deleteCanvas(canvas) {
      this.$confirm(`确定要删除画布 "${canvas.name}" 吗？`, '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 从列表中移除
        const index = this.canvasList.findIndex(item => item.id === canvas.id);
        if (index !== -1) {
          this.canvasList.splice(index, 1);
          // 保存到本地存储
          this.saveCanvasListToLocal();
          this.$message.success('删除成功');

          // 如果删除的是当前画布，清空当前画布
          if (this.currentCanvas.id === canvas.id) {
            this.newCanvas();
          }
        }
      }).catch(() => {
        // 用户取消删除
      });
    },

    // 获取画布数据
    getCanvasData() {
      return {
        nodes: this.canvasNodes || [],
        settings: this.canvasSettings,
        version: '1.0',
        timestamp: new Date().toISOString()
      };
    },

    // 加载画布列表
    loadCanvasList() {
      // 从 localStorage 加载保存的画布数据
      const savedCanvasList = localStorage.getItem('dwms_canvas_list');
      if (savedCanvasList) {
        try {
          this.canvasList = JSON.parse(savedCanvasList);
          console.log('从本地存储加载画布列表:', this.canvasList);
          return;
        } catch (error) {
          console.error('解析本地存储的画布数据失败:', error);
        }
      }

      // 如果没有本地数据，使用默认模拟数据
      this.canvasList = [
        {
          id: 1,
          name: '仓库1号区域监控',
          description: '1号仓库区域的实时监控画布',
          updateTime: '2024-01-15 10:30:00',
          data: JSON.stringify({
            nodes: [
              {
                id: 'demo_shelf_1',
                type: 'shelf',
                text: '货架A1',
                x: 100,
                y: 100,
                width: 80,
                height: 120,
                background: '#8c8c8c',
                color: '#ffffff',
                lineWidth: 1,
                rotate: 0,
                globalAlpha: 1,
                deviceId: 'SHELF_A1',
                dataField: 'status'
              },
              {
                id: 'demo_weight_1',
                type: 'weight_sensor',
                text: '重量传感器',
                x: 250,
                y: 150,
                width: 60,
                height: 60,
                background: '#1890ff',
                color: '#ffffff',
                lineWidth: 1,
                rotate: 0,
                globalAlpha: 1,
                deviceId: 'WS001',
                dataField: 'weight'
              }
            ],
            settings: {
              width: 1200,
              height: 800,
              showGrid: true,
              gridSize: 20
            }
          })
        },
        {
          id: 2,
          name: '温湿度监控画布',
          description: '环境温湿度传感器监控',
          updateTime: '2024-01-14 16:20:00',
          data: JSON.stringify({
            nodes: [
              {
                id: 'demo_temp_1',
                type: 'temp_sensor',
                text: '温度传感器',
                x: 150,
                y: 100,
                width: 60,
                height: 60,
                background: '#ff4d4f',
                color: '#ffffff',
                lineWidth: 1,
                rotate: 0,
                globalAlpha: 1,
                deviceId: 'TS001',
                dataField: 'temperature'
              },
              {
                id: 'demo_humidity_1',
                type: 'humidity_sensor',
                text: '湿度传感器',
                x: 300,
                y: 100,
                width: 60,
                height: 60,
                background: '#13c2c2',
                color: '#ffffff',
                lineWidth: 1,
                rotate: 0,
                globalAlpha: 1,
                deviceId: 'HS001',
                dataField: 'humidity'
              }
            ],
            settings: {
              width: 1200,
              height: 800,
              showGrid: true,
              gridSize: 20
            }
          })
        },
        {
          id: 3,
          name: '门禁系统监控',
          description: '出入口门禁设备监控',
          updateTime: '2024-01-13 09:15:00',
          data: JSON.stringify({
            nodes: [
              {
                id: 'demo_door_1',
                type: 'door',
                text: '主入口',
                x: 200,
                y: 150,
                width: 40,
                height: 80,
                background: '#52c41a',
                color: '#ffffff',
                lineWidth: 1,
                rotate: 0,
                globalAlpha: 1,
                deviceId: 'DOOR_001',
                dataField: 'status'
              },
              {
                id: 'demo_access_1',
                type: 'access_control',
                text: '门禁设备',
                x: 150,
                y: 200,
                width: 50,
                height: 50,
                background: '#52c41a',
                color: '#ffffff',
                lineWidth: 1,
                rotate: 0,
                globalAlpha: 1,
                deviceId: 'AC001',
                dataField: 'access_status'
              }
            ],
            settings: {
              width: 1200,
              height: 800,
              showGrid: true,
              gridSize: 20
            }
          })
        }
      ];

      // 保存默认数据到本地存储
      this.saveCanvasListToLocal();

      // 实际API调用（当后端API准备好时启用）
      /*
      listConfig({ configType: 'web_scada' }).then(response => {
        this.canvasList = response.rows.map(item => ({
          id: item.id,
          name: item.configName,
          description: item.description,
          updateTime: item.updateTime,
          data: item.configData
        }));
      }).catch(error => {
        console.error('加载画布列表失败:', error);
        this.$message.error('加载画布列表失败');
      });
      */
    },

    // 选择画布
    selectCanvas(row) {
      // 高亮选中的行
      this.selectedCanvasId = row.id;
    },

    // 加载画布
    loadCanvas(canvas) {
      try {
        console.log('加载画布:', canvas); // 调试日志
        const data = JSON.parse(canvas.data || '{"nodes":[],"settings":{}}');
        this.currentCanvas = { ...canvas };
        this.loadCanvasData(data);
        this.showOpenDialog = false;
        this.$message.success('画布加载成功');
      } catch (error) {
        console.error('画布数据解析错误:', error); // 调试日志
        this.$message.error('画布数据格式错误');
      }
    },

    // 加载画布数据
    loadCanvasData(data) {
      console.log('加载画布数据:', data); // 调试日志

      if (data.settings) {
        this.canvasSettings = { ...this.canvasSettings, ...data.settings };
        this.canvasWidth = this.canvasSettings.width;
        this.canvasHeight = this.canvasSettings.height;
        this.showGrid = this.canvasSettings.showGrid;
      }

      // 加载节点数据
      if (data.nodes && data.nodes.length > 0) {
        console.log('加载节点数据:', data.nodes); // 调试日志
        this.canvasNodes = [...data.nodes]; // 使用展开运算符确保数据更新

        // 清空当前画布上的DOM元素
        const canvas = this.$refs.scadaCanvas;
        if (canvas) {
          const existingNodes = canvas.querySelectorAll('.canvas-node');
          existingNodes.forEach(node => node.remove());
        }

        // 重新创建所有节点
        this.$nextTick(() => {
          this.canvasNodes.forEach(node => {
            this.createVisualNode(node);
          });
          this.$message.info(`已加载 ${this.canvasNodes.length} 个组件`);
        });
      } else {
        console.log('没有节点数据需要加载'); // 调试日志
        this.canvasNodes = [];
      }
    },

    // 清空画布
    clearCanvas() {
      this.selectedNodes = [];
      this.currentNode = {};
      this.canvasNodes = [];

      // 清空画布上的所有节点元素
      const canvas = this.$refs.scadaCanvas;
      if (canvas) {
        const nodes = canvas.querySelectorAll('.canvas-node');
        nodes.forEach(node => node.remove());
      }
    },

    // 清空选择
    clearSelection() {
      this.selectedNodes = [];
      this.currentNode = {};
    },

    // 撤销
    undo() {
      // 实现撤销逻辑
      this.$message.info('撤销功能开发中');
    },

    // 重做
    redo() {
      // 实现重做逻辑
      this.$message.info('重做功能开发中');
    },

    // 放大
    zoomIn() {
      this.scale = Math.min(this.scale + 0.1, 3);
    },

    // 缩小
    zoomOut() {
      this.scale = Math.max(this.scale - 0.1, 0.1);
    },

    // 重置缩放
    resetZoom() {
      this.scale = 1;
    },

    // 设置模式
    setMode(mode) {
      this.mode = mode;
      if (mode === 'monitor') {
        this.startMonitoring();
      } else {
        this.stopMonitoring();
      }
    },

    // 开始监控
    startMonitoring() {
      this.connectMqtt();
      this.startDataUpdate();
    },

    // 停止监控
    stopMonitoring() {
      this.disconnectMqtt();
      this.stopDataUpdate();
    },

    // 连接MQTT
    connectMqtt() {
      // 实现MQTT连接逻辑
      setTimeout(() => {
        this.mqttConnected = true;
        this.$message.success('MQTT连接成功');
      }, 1000);
    },

    // 断开MQTT
    disconnectMqtt() {
      this.mqttConnected = false;
    },

    // 开始数据更新
    startDataUpdate() {
      this.dataUpdateTimer = setInterval(() => {
        this.updateRealtimeData();
      }, 1000);
    },

    // 停止数据更新
    stopDataUpdate() {
      if (this.dataUpdateTimer) {
        clearInterval(this.dataUpdateTimer);
      }
    },

    // 更新实时数据
    updateRealtimeData() {
      // 模拟实时数据更新
      this.realtimeData = [
        {
          id: 'sensor1',
          x: 100,
          y: 100,
          label: '重量传感器1',
          value: (Math.random() * 1000).toFixed(2),
          unit: 'kg',
          status: 'normal'
        },
        {
          id: 'sensor2',
          x: 300,
          y: 200,
          label: '温度传感器1',
          value: (Math.random() * 30 + 10).toFixed(1),
          unit: '°C',
          status: 'normal'
        }
      ];
    },

    // 添加组件
    addComponent(component) {
      const newNode = {
        id: 'node_' + Date.now(),
        type: component.type,
        text: component.name,
        x: Math.random() * 300 + 100, // 随机位置避免重叠
        y: Math.random() * 200 + 100,
        width: this.getComponentDefaultWidth(component.type),
        height: this.getComponentDefaultHeight(component.type),
        background: this.getComponentDefaultColor(component.type),
        color: '#333333',
        lineWidth: 1,
        rotate: 0,
        globalAlpha: 1,
        dataSource: '',
        deviceId: this.generateDeviceId(component.type),
        dataField: this.getDefaultDataField(component.type),
        updateInterval: 1000,
        animationType: 'none',
        animationSpeed: 1,
        animationCondition: ''
      };

      // 添加到画布节点列表
      if (!this.canvasNodes) {
        this.canvasNodes = [];
      }
      this.canvasNodes.push(newNode);

      // 选中新添加的节点
      this.selectedNodes = [newNode];
      this.currentNode = { ...newNode };

      // 在画布上创建可视化元素
      this.createVisualNode(newNode);

      this.$message.success(`已添加${component.name}`);
    },

    // 获取组件默认宽度
    getComponentDefaultWidth(type) {
      const widthMap = {
        'rect': 120,
        'circle': 80,
        'line': 100,
        'text': 100,
        'image': 100,
        'shelf': 80,
        'forklift': 60,
        'conveyor': 150,
        'door': 40,
        'area': 200,
        'weight_sensor': 60,
        'temp_sensor': 60,
        'humidity_sensor': 60,
        'smoke_sensor': 60,
        'camera': 50,
        'access_control': 50,
        'alarm': 50,
        'display': 120
      };
      return widthMap[type] || 100;
    },

    // 获取组件默认高度
    getComponentDefaultHeight(type) {
      const heightMap = {
        'rect': 80,
        'circle': 80,
        'line': 2,
        'text': 30,
        'image': 80,
        'shelf': 120,
        'forklift': 40,
        'conveyor': 30,
        'door': 80,
        'area': 150,
        'weight_sensor': 60,
        'temp_sensor': 60,
        'humidity_sensor': 60,
        'smoke_sensor': 60,
        'camera': 50,
        'access_control': 50,
        'alarm': 50,
        'display': 80
      };
      return heightMap[type] || 60;
    },

    // 获取组件默认颜色
    getComponentDefaultColor(type) {
      const colorMap = {
        'rect': '#f0f2f5',
        'circle': '#e6f7ff',
        'line': '#d9d9d9',
        'text': '#ffffff',
        'image': '#ffffff',
        'shelf': '#8c8c8c',
        'forklift': '#faad14',
        'conveyor': '#722ed1',
        'door': '#52c41a',
        'area': '#f6ffed',
        'weight_sensor': '#1890ff',
        'temp_sensor': '#ff4d4f',
        'humidity_sensor': '#13c2c2',
        'smoke_sensor': '#fa8c16',
        'camera': '#2f54eb',
        'access_control': '#52c41a',
        'alarm': '#ff4d4f',
        'display': '#595959'
      };
      return colorMap[type] || '#ffffff';
    },

    // 生成设备ID
    generateDeviceId(type) {
      const prefixMap = {
        'weight_sensor': 'WS',
        'temp_sensor': 'TS',
        'humidity_sensor': 'HS',
        'smoke_sensor': 'SS',
        'camera': 'CAM',
        'access_control': 'AC',
        'alarm': 'AL',
        'display': 'DIS'
      };
      const prefix = prefixMap[type] || 'DEV';
      const number = String(Date.now()).slice(-3);
      return `${prefix}${number}`;
    },

    // 获取默认数据字段
    getDefaultDataField(type) {
      const fieldMap = {
        'weight_sensor': 'weight',
        'temp_sensor': 'temperature',
        'humidity_sensor': 'humidity',
        'smoke_sensor': 'smoke_level',
        'camera': 'status',
        'access_control': 'access_status',
        'alarm': 'alarm_status',
        'display': 'display_content'
      };
      return fieldMap[type] || 'value';
    },

    // 创建可视化节点
    createVisualNode(node) {
      // 在实际的画布容器中创建DOM元素
      const canvas = this.$refs.scadaCanvas;
      if (!canvas) return;

      const nodeElement = document.createElement('div');
      nodeElement.id = node.id;
      nodeElement.className = 'canvas-node';
      nodeElement.setAttribute('data-type', node.type);
      nodeElement.style.cssText = `
        position: absolute;
        left: ${node.x}px;
        top: ${node.y}px;
        width: ${node.width}px;
        height: ${node.height}px;
        background: ${node.background};
        border: ${node.lineWidth}px solid ${node.color};
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: move;
        user-select: none;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: box-shadow 0.3s ease;
        z-index: 1;
      `;

      // 添加图标和文本
      const icon = this.getComponentIcon(node.type);
      nodeElement.innerHTML = `
        <div style="text-align: center; font-size: 12px; color: ${node.color}; pointer-events: none;">
          <i class="${icon}" style="font-size: 16px; display: block; margin-bottom: 4px;"></i>
          <span>${node.text}</span>
        </div>
      `;

      // 添加拖拽功能
      this.makeDraggable(nodeElement, node);

      // 添加点击事件
      nodeElement.addEventListener('click', (e) => {
        e.stopPropagation();
        this.selectNode(node);
      });

      // 添加鼠标悬停效果
      nodeElement.addEventListener('mouseenter', () => {
        if (!this.isDragging) {
          nodeElement.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
          nodeElement.style.zIndex = '10';
        }
      });

      nodeElement.addEventListener('mouseleave', () => {
        if (!this.isDragging) {
          nodeElement.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
          nodeElement.style.zIndex = '1';
        }
      });

      canvas.appendChild(nodeElement);
    },

    // 获取组件图标
    getComponentIcon(type) {
      const iconMap = {
        'rect': 'el-icon-crop',
        'circle': 'el-icon-pie-chart',
        'line': 'el-icon-minus',
        'text': 'el-icon-edit-outline',
        'image': 'el-icon-picture',
        'shelf': 'el-icon-menu',
        'forklift': 'el-icon-truck',
        'conveyor': 'el-icon-right',
        'door': 'el-icon-switch-button',
        'area': 'el-icon-crop',
        'weight_sensor': 'el-icon-scale-to-original',
        'temp_sensor': 'el-icon-sunny',
        'humidity_sensor': 'el-icon-cloudy',
        'smoke_sensor': 'el-icon-warning',
        'camera': 'el-icon-video-camera',
        'access_control': 'el-icon-key',
        'alarm': 'el-icon-bell',
        'display': 'el-icon-monitor'
      };
      return iconMap[type] || 'el-icon-setting';
    },

    // 选中节点
    selectNode(node) {
      this.selectedNodes = [node];
      this.currentNode = { ...node };

      // 高亮选中的节点
      this.highlightSelectedNode(node.id);
    },

    // 高亮选中的节点
    highlightSelectedNode(nodeId) {
      // 清除之前的高亮
      const allNodes = document.querySelectorAll('.canvas-node');
      allNodes.forEach(node => {
        node.classList.remove('selected');
      });

      // 高亮当前选中的节点
      const selectedNode = document.getElementById(nodeId);
      if (selectedNode) {
        selectedNode.classList.add('selected');
      }
    },

    // 使元素可拖拽
    makeDraggable(element, node) {
      let startX, startY, initialX, initialY;

      const onMouseDown = (e) => {
        if (this.mode !== 'edit') return;

        e.preventDefault();
        e.stopPropagation();

        this.isDragging = true;
        this.dragElement = element;
        this.dragNode = node;

        // 获取鼠标位置
        startX = e.clientX;
        startY = e.clientY;

        // 获取元素当前位置
        initialX = parseInt(element.style.left) || 0;
        initialY = parseInt(element.style.top) || 0;

        // 计算偏移量
        this.dragOffsetX = startX - initialX;
        this.dragOffsetY = startY - initialY;

        // 设置拖拽样式
        element.style.zIndex = '1000';
        element.style.cursor = 'grabbing';
        element.style.transform = 'scale(1.05)';
        element.style.boxShadow = '0 8px 16px rgba(0,0,0,0.3)';

        // 选中当前节点
        this.selectNode(node);

        // 添加全局事件监听
        document.addEventListener('mousemove', onMouseMove);
        document.addEventListener('mouseup', onMouseUp);
      };

      const onMouseMove = (e) => {
        if (!this.isDragging || !this.dragElement) return;

        e.preventDefault();

        // 计算新位置
        const newX = e.clientX - this.dragOffsetX;
        const newY = e.clientY - this.dragOffsetY;

        // 获取画布边界
        const canvas = this.$refs.scadaCanvas;
        const canvasRect = canvas.getBoundingClientRect();
        const elementRect = this.dragElement.getBoundingClientRect();

        // 限制在画布范围内
        const minX = 0;
        const minY = 0;
        const maxX = this.canvasWidth - parseInt(this.dragElement.style.width);
        const maxY = this.canvasHeight - parseInt(this.dragElement.style.height);

        let constrainedX = Math.max(minX, Math.min(maxX, newX));
        let constrainedY = Math.max(minY, Math.min(maxY, newY));

        // 网格对齐（按住Shift键时禁用）
        if (this.showGrid && !e.shiftKey) {
          constrainedX = this.snapToGrid(constrainedX, this.canvasSettings.gridSize);
          constrainedY = this.snapToGrid(constrainedY, this.canvasSettings.gridSize);
        }

        // 更新元素位置
        this.dragElement.style.left = constrainedX + 'px';
        this.dragElement.style.top = constrainedY + 'px';

        // 更新节点数据
        this.dragNode.x = constrainedX;
        this.dragNode.y = constrainedY;

        // 更新属性面板
        if (this.currentNode.id === this.dragNode.id) {
          this.currentNode.x = constrainedX;
          this.currentNode.y = constrainedY;
        }

        // 更新画布节点数据
        const nodeIndex = this.canvasNodes.findIndex(n => n.id === this.dragNode.id);
        if (nodeIndex !== -1) {
          this.canvasNodes[nodeIndex].x = constrainedX;
          this.canvasNodes[nodeIndex].y = constrainedY;
        }
      };

      const onMouseUp = (e) => {
        if (!this.isDragging) return;

        // 恢复样式
        if (this.dragElement) {
          this.dragElement.style.zIndex = '1';
          this.dragElement.style.cursor = 'move';
          this.dragElement.style.transform = 'scale(1)';
          this.dragElement.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
        }

        // 重置拖拽状态
        this.isDragging = false;
        this.dragElement = null;
        this.dragNode = null;

        // 移除全局事件监听
        document.removeEventListener('mousemove', onMouseMove);
        document.removeEventListener('mouseup', onMouseUp);
      };

      // 添加鼠标按下事件
      element.addEventListener('mousedown', onMouseDown);
    },

    // 更新节点属性
    updateNodeProperty() {
      if (!this.currentNode.id) return;

      // 更新画布节点数据
      const nodeIndex = this.canvasNodes.findIndex(n => n.id === this.currentNode.id);
      if (nodeIndex !== -1) {
        this.canvasNodes[nodeIndex] = { ...this.currentNode };
      }

      // 更新可视化元素
      const element = document.getElementById(this.currentNode.id);
      if (element) {
        element.style.left = this.currentNode.x + 'px';
        element.style.top = this.currentNode.y + 'px';
        element.style.width = this.currentNode.width + 'px';
        element.style.height = this.currentNode.height + 'px';
        element.style.background = this.currentNode.background;
        element.style.borderColor = this.currentNode.color;
        element.style.borderWidth = this.currentNode.lineWidth + 'px';
        element.style.opacity = this.currentNode.globalAlpha;
        element.style.transform = `rotate(${this.currentNode.rotate}deg)`;

        // 更新文本内容
        const textSpan = element.querySelector('span');
        if (textSpan) {
          textSpan.textContent = this.currentNode.text;
        }
      }
    },

    // 对齐操作
    alignLeft() {
      this.$message.info('左对齐功能开发中');
    },

    alignCenter() {
      this.$message.info('居中对齐功能开发中');
    },

    alignRight() {
      this.$message.info('右对齐功能开发中');
    },

    // 应用设置
    applySettings() {
      this.canvasWidth = this.canvasSettings.width;
      this.canvasHeight = this.canvasSettings.height;
      this.showGrid = this.canvasSettings.showGrid;
      this.showSettings = false;
      this.$message.success('设置已应用');
    },

    // 显示右键菜单
    showContextMenu(event) {
      if (this.mode !== 'edit') return;

      event.preventDefault();
      this.contextMenuX = event.offsetX;
      this.contextMenuY = event.offsetY;
      this.contextMenuVisible = true;

      // 点击其他地方隐藏菜单
      const hideMenu = () => {
        this.contextMenuVisible = false;
        document.removeEventListener('click', hideMenu);
      };
      setTimeout(() => {
        document.addEventListener('click', hideMenu);
      }, 100);
    },

    // 复制节点
    copyNode() {
      if (this.selectedNodes.length === 0) return;

      const copiedNodes = this.selectedNodes.map(node => ({
        ...node,
        id: 'node_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
        x: node.x + 20,
        y: node.y + 20
      }));

      copiedNodes.forEach(node => {
        this.canvasNodes.push(node);
        this.createVisualNode(node);
      });

      this.contextMenuVisible = false;
      this.$message.success(`已复制 ${copiedNodes.length} 个组件`);
    },

    // 置于顶层
    bringToFront() {
      this.selectedNodes.forEach(node => {
        const element = document.getElementById(node.id);
        if (element) {
          element.style.zIndex = '100';
        }
      });
      this.contextMenuVisible = false;
    },

    // 置于底层
    sendToBack() {
      this.selectedNodes.forEach(node => {
        const element = document.getElementById(node.id);
        if (element) {
          element.style.zIndex = '1';
        }
      });
      this.contextMenuVisible = false;
    },

    // 全选
    selectAll() {
      this.selectedNodes = [...this.canvasNodes];
      this.canvasNodes.forEach(node => {
        this.highlightSelectedNode(node.id);
      });
      this.contextMenuVisible = false;
      this.$message.info(`已选中 ${this.canvasNodes.length} 个组件`);
    },

    // 全屏切换
    toggleFullscreen() {
      const element = this.$el;
      if (!document.fullscreenElement) {
        element.requestFullscreen();
      } else {
        document.exitFullscreen();
      }
    },

    // 获取模式文本
    getModeText() {
      const modeMap = {
        edit: '编辑模式',
        preview: '预览模式',
        monitor: '监控模式'
      };
      return modeMap[this.mode] || '未知模式';
    },

    // 获取数据状态
    getDataStatus(dataPoint) {
      if (dataPoint.status === 'normal') return 'normal';
      if (dataPoint.status === 'warning') return 'warning';
      if (dataPoint.status === 'error') return 'error';
      return 'normal';
    },

    // 格式化数据值
    formatDataValue(dataPoint) {
      return `${dataPoint.value} ${dataPoint.unit || ''}`;
    },

    // 键盘事件处理
    onKeyDown(event) {
      if (this.mode !== 'edit') return;

      // Delete键删除选中的节点
      if (event.key === 'Delete' && this.selectedNodes.length > 0) {
        this.deleteSelectedNodes();
        event.preventDefault();
      }

      // Ctrl+Z 撤销
      if (event.ctrlKey && event.key === 'z') {
        this.undo();
        event.preventDefault();
      }

      // Ctrl+Y 重做
      if (event.ctrlKey && event.key === 'y') {
        this.redo();
        event.preventDefault();
      }

      // 方向键移动选中的节点
      if (this.selectedNodes.length > 0 && ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
        this.moveSelectedNodes(event.key);
        event.preventDefault();
      }
    },

    // 删除选中的节点
    deleteSelectedNodes() {
      if (this.selectedNodes.length === 0) return;

      const deleteCount = this.selectedNodes.length;

      this.selectedNodes.forEach(node => {
        // 从画布节点数组中移除
        const index = this.canvasNodes.findIndex(n => n.id === node.id);
        if (index !== -1) {
          this.canvasNodes.splice(index, 1);
        }

        // 从DOM中移除
        const element = document.getElementById(node.id);
        if (element) {
          element.remove();
        }
      });

      // 清空选择
      this.selectedNodes = [];
      this.currentNode = {};

      this.$message.success(`已删除 ${deleteCount} 个组件`);
    },

    // 移动选中的节点
    moveSelectedNodes(direction) {
      const step = 5; // 每次移动5像素

      this.selectedNodes.forEach(node => {
        let newX = node.x;
        let newY = node.y;

        switch (direction) {
          case 'ArrowUp':
            newY = Math.max(0, node.y - step);
            break;
          case 'ArrowDown':
            newY = Math.min(this.canvasHeight - node.height, node.y + step);
            break;
          case 'ArrowLeft':
            newX = Math.max(0, node.x - step);
            break;
          case 'ArrowRight':
            newX = Math.min(this.canvasWidth - node.width, node.x + step);
            break;
        }

        // 更新节点位置
        node.x = newX;
        node.y = newY;

        // 更新DOM元素位置
        const element = document.getElementById(node.id);
        if (element) {
          element.style.left = newX + 'px';
          element.style.top = newY + 'px';
        }

        // 更新画布节点数据
        const nodeIndex = this.canvasNodes.findIndex(n => n.id === node.id);
        if (nodeIndex !== -1) {
          this.canvasNodes[nodeIndex].x = newX;
          this.canvasNodes[nodeIndex].y = newY;
        }

        // 更新当前节点数据
        if (this.currentNode.id === node.id) {
          this.currentNode.x = newX;
          this.currentNode.y = newY;
        }
      });
    },

    // 网格对齐
    snapToGrid(value, gridSize = 10) {
      return Math.round(value / gridSize) * gridSize;
    }
  },

  beforeDestroy() {
    this.stopDataUpdate();
    this.disconnectMqtt();
    // 移除键盘事件监听
    document.removeEventListener('keydown', this.onKeyDown);
  }
};
</script>

<style scoped>
.scada-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
}

.scada-toolbar {
  height: 50px;
  background: #ffffff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-info {
  font-size: 12px;
  color: #666;
  padding: 0 8px;
  background: #f5f7fa;
  border-radius: 4px;
  line-height: 24px;
}

.scada-workspace {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.component-panel {
  width: 250px;
  background: #ffffff;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
}

.property-panel {
  width: 300px;
  background: #ffffff;
  border-left: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
}

.panel-header {
  height: 40px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  padding: 0 16px;
}

.panel-header h4 {
  margin: 0;
  font-size: 14px;
  color: #303133;
}

.component-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  padding: 12px;
}

.component-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  background: #ffffff;
}

.component-item:hover {
  border-color: #409eff;
  background: #ecf5ff;
  transform: translateY(-2px);
}

.component-item i {
  font-size: 24px;
  color: #606266;
  margin-bottom: 4px;
}

.component-item span {
  font-size: 12px;
  color: #606266;
  text-align: center;
}

.canvas-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}

.canvas-header {
  height: 40px;
  background: #ffffff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}

.canvas-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.canvas-size {
  font-size: 12px;
  color: #909399;
}

.canvas-wrapper {
  flex: 1;
  overflow: auto;
  position: relative;
  background: #f0f2f5;
}

.canvas-main {
  position: relative;
  background: #ffffff;
  margin: 20px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.canvas-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(to right, #e4e7ed 1px, transparent 1px),
    linear-gradient(to bottom, #e4e7ed 1px, transparent 1px);
  background-size: 20px 20px;
  opacity: 0.5;
  pointer-events: none;
}

#scada-canvas {
  width: 100%;
  height: 100%;
  position: relative;
}

.data-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.data-point {
  position: absolute;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 4px 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transform: translate(-50%, -50%);
}

.data-value {
  font-size: 14px;
  font-weight: bold;
  text-align: center;
}

.data-value.normal {
  color: #67c23a;
}

.data-value.warning {
  color: #e6a23c;
}

.data-value.error {
  color: #f56c6c;
}

.data-label {
  font-size: 12px;
  color: #909399;
  text-align: center;
  margin-top: 2px;
}

.canvas-status {
  height: 30px;
  background: #f5f7fa;
  border-top: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  font-size: 12px;
  color: #606266;
}

.status-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.property-content {
  flex: 1;
  overflow-y: auto;
}

.no-selection {
  padding: 40px 20px;
  text-align: center;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .component-panel {
    width: 200px;
  }

  .property-panel {
    width: 250px;
  }
}

@media (max-width: 768px) {
  .scada-toolbar {
    flex-direction: column;
    height: auto;
    padding: 8px;
  }

  .toolbar-left,
  .toolbar-right {
    width: 100%;
    justify-content: center;
  }

  .component-panel,
  .property-panel {
    display: none;
  }
}

/* 动画效果 */
.component-item {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.data-point {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* 滚动条样式 */
.canvas-wrapper::-webkit-scrollbar,
.property-content::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.canvas-wrapper::-webkit-scrollbar-track,
.property-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.canvas-wrapper::-webkit-scrollbar-thumb,
.property-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.canvas-wrapper::-webkit-scrollbar-thumb:hover,
.property-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 画布节点样式 */
.canvas-node {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  transition: all 0.2s ease;
}

.canvas-node:hover {
  z-index: 10;
}

.canvas-node.selected {
  border: 2px solid #409eff !important;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
}

.canvas-node.dragging {
  z-index: 1000 !important;
  transform: scale(1.05) !important;
  box-shadow: 0 8px 16px rgba(0,0,0,0.3) !important;
  cursor: grabbing !important;
  transition: none !important;
}

/* 拖拽时的辅助线 */
.drag-guide-line {
  position: absolute;
  background: #409eff;
  pointer-events: none;
  z-index: 999;
}

.drag-guide-line.horizontal {
  height: 1px;
  width: 100%;
  top: 0;
  left: 0;
}

.drag-guide-line.vertical {
  width: 1px;
  height: 100%;
  top: 0;
  left: 0;
}

/* 组件特定样式 */
.canvas-node[data-type="shelf"] {
  background: linear-gradient(135deg, #8c8c8c, #bfbfbf) !important;
  color: white;
}

.canvas-node[data-type="forklift"] {
  background: linear-gradient(135deg, #faad14, #ffc53d) !important;
  color: white;
}

.canvas-node[data-type="weight_sensor"] {
  background: linear-gradient(135deg, #1890ff, #40a9ff) !important;
  color: white;
}

.canvas-node[data-type="temp_sensor"] {
  background: linear-gradient(135deg, #ff4d4f, #ff7875) !important;
  color: white;
}

.canvas-node[data-type="camera"] {
  background: linear-gradient(135deg, #2f54eb, #597ef7) !important;
  color: white;
}

/* 实时数据动画 */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.canvas-node.has-data {
  animation: pulse 2s infinite;
}

.canvas-node.status-warning {
  border-color: #faad14 !important;
  box-shadow: 0 0 10px rgba(250, 173, 20, 0.5) !important;
}

.canvas-node.status-error {
  border-color: #ff4d4f !important;
  box-shadow: 0 0 10px rgba(255, 77, 79, 0.5) !important;
}

.canvas-node.status-normal {
  border-color: #52c41a !important;
  box-shadow: 0 0 10px rgba(82, 196, 26, 0.3) !important;
}

/* 右键菜单样式 */
.context-menu {
  position: absolute;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 2000;
  min-width: 120px;
  padding: 4px 0;
}

.context-menu-item {
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  color: #606266;
  display: flex;
  align-items: center;
  transition: background-color 0.3s;
}

.context-menu-item:hover {
  background-color: #f5f7fa;
  color: #409eff;
}

.context-menu-item i {
  margin-right: 8px;
  width: 16px;
}

.context-menu-divider {
  height: 1px;
  background-color: #e4e7ed;
  margin: 4px 0;
}

/* 选中多个节点时的样式 */
.canvas-node.multi-selected {
  border: 2px solid #67c23a !important;
  box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.2) !important;
}
</style>
