import request from '@/utils/request'

// 查询人脸识别记录列表
export function listRecognition(query) {
  return request({
    url: '/access/recognition/list',
    method: 'get',
    params: query
  })
}

// 查询人脸识别记录详细
export function getRecognition(id) {
  return request({
    url: '/access/recognition/' + id,
    method: 'get'
  })
}

// 新增人脸识别记录
export function addRecognition(data) {
  return request({
    url: '/access/recognition',
    method: 'post',
    data: data
  })
}

// 修改人脸识别记录
export function updateRecognition(data) {
  return request({
    url: '/access/recognition',
    method: 'put',
    data: data
  })
}

// 删除人脸识别记录
export function delRecognition(id) {
  return request({
    url: '/access/recognition/' + id,
    method: 'delete'
  })
}

// 获取人脸识别记录统计数据
export function getRecognitionStatistics() {
  return request({
    url: '/access/recognition/statistics',
    method: 'get'
  })
}

// 导出人脸识别记录
export function exportRecognition(query) {
  return request({
    url: '/access/recognition/export',
    method: 'get',
    params: query
  })
}

// 清理人脸识别记录
export function cleanupRecognition(days) {
  return request({
    url: '/access/recognition/cleanup/' + days,
    method: 'post'
  })
}
