<template>
  <div class="status-component" :style="{ width: width + 'px', height: height + 'px' }">
    <!-- 状态指示灯 -->
    <div v-if="config.type === 'light'" class="status-light">
      <div class="light-title" v-if="config.title">{{ config.title }}</div>
      <div class="light-container">
        <div 
          class="light-indicator"
          :class="{ 'active': isActive }"
          :style="{ 
            backgroundColor: isActive ? (config.onColor || '#67C23A') : (config.offColor || '#F56C6C'),
            boxShadow: isActive ? `0 0 20px ${config.onColor || '#67C23A'}` : 'none'
          }"
        ></div>
        <span class="light-text">{{ statusText }}</span>
      </div>
    </div>
    
    <!-- 数字显示器 -->
    <div v-else-if="config.type === 'number'" class="status-number">
      <div class="number-title" v-if="config.title">{{ config.title }}</div>
      <div 
        class="number-value"
        :style="{ 
          fontSize: config.fontSize || '24px',
          color: config.color || '#409EFF'
        }"
      >
        {{ displayValue }}
        <span class="number-unit" v-if="config.unit">{{ config.unit }}</span>
      </div>
    </div>
    
    <!-- 文本标签 -->
    <div v-else-if="config.type === 'text'" class="status-text">
      <div 
        class="text-content"
        :style="{ 
          fontSize: config.fontSize || '16px',
          color: config.color || '#303133'
        }"
      >
        {{ displayText }}
      </div>
    </div>
    
    <!-- 进度条 -->
    <div v-else-if="config.type === 'progress'" class="status-progress">
      <div class="progress-title" v-if="config.title">{{ config.title }}</div>
      <div class="progress-container">
        <el-progress 
          :percentage="percentage"
          :color="config.color || '#409EFF'"
          :show-text="config.showText !== false"
          :stroke-width="config.strokeWidth || 6"
        ></el-progress>
      </div>
      <div class="progress-value" v-if="config.showValue">
        {{ displayValue }}
        <span class="progress-unit" v-if="config.unit">{{ config.unit }}</span>
      </div>
    </div>
    
    <!-- 状态卡片 -->
    <div v-else-if="config.type === 'card'" class="status-card">
      <div class="card-header">
        <i :class="config.icon || 'el-icon-info'" :style="{ color: config.iconColor || '#409EFF' }"></i>
        <span class="card-title">{{ config.title || '状态' }}</span>
      </div>
      <div class="card-content">
        <div class="card-value" :style="{ color: config.valueColor || '#333' }">
          {{ displayValue }}
          <span class="card-unit" v-if="config.unit">{{ config.unit }}</span>
        </div>
        <div class="card-status" :style="{ color: getStatusColor() }">
          {{ statusText }}
        </div>
      </div>
    </div>
    
    <!-- 默认状态 -->
    <div v-else class="status-default">
      <div class="default-icon">
        <i class="el-icon-info"></i>
      </div>
      <div class="default-text">{{ displayValue || '状态' }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StatusComponent',
  props: {
    config: {
      type: Object,
      default: () => ({})
    },
    data: {
      type: Object,
      default: () => ({})
    },
    width: {
      type: Number,
      default: 150
    },
    height: {
      type: Number,
      default: 100
    }
  },
  computed: {
    currentValue() {
      return this.data?.value !== undefined ? this.data.value : 0
    },
    isActive() {
      if (typeof this.currentValue === 'boolean') {
        return this.currentValue
      }
      if (typeof this.currentValue === 'number') {
        return this.currentValue > 0
      }
      if (typeof this.currentValue === 'string') {
        return ['1', 'true', 'on', 'online', 'active'].includes(this.currentValue.toLowerCase())
      }
      return false
    },
    statusText() {
      if (this.data?.text) {
        return this.data.text
      }
      
      switch (this.config.type) {
        case 'light':
          return this.isActive ? (this.config.onText || '在线') : (this.config.offText || '离线')
        case 'card':
          return this.isActive ? '正常' : '异常'
        default:
          return this.isActive ? '开启' : '关闭'
      }
    },
    displayValue() {
      if (this.config.type === 'text') {
        return this.data?.text || this.config.defaultText || ''
      }
      
      if (typeof this.currentValue === 'number') {
        const decimals = this.config.decimals !== undefined ? this.config.decimals : 1
        return this.currentValue.toFixed(decimals)
      }
      
      return this.currentValue
    },
    displayText() {
      return this.data?.text || this.config.text || this.displayValue
    },
    percentage() {
      if (this.config.type !== 'progress') return 0
      
      const min = this.config.min || 0
      const max = this.config.max || 100
      const value = this.currentValue
      
      return Math.min(100, Math.max(0, ((value - min) / (max - min)) * 100))
    }
  },
  methods: {
    getStatusColor() {
      if (this.isActive) {
        return this.config.activeColor || '#67C23A'
      } else {
        return this.config.inactiveColor || '#F56C6C'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.status-component {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 8px;
  
  .status-light {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 100%;
    justify-content: center;
    
    .light-title {
      font-size: 12px;
      color: #666;
      margin-bottom: 8px;
      text-align: center;
    }
    
    .light-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .light-indicator {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        margin-bottom: 8px;
        transition: all 0.3s ease;
        
        &.active {
          animation: pulse 2s infinite;
        }
      }
      
      .light-text {
        font-size: 12px;
        color: #333;
      }
    }
  }
  
  .status-number {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    
    .number-title {
      font-size: 12px;
      color: #666;
      margin-bottom: 8px;
      text-align: center;
    }
    
    .number-value {
      font-weight: bold;
      text-align: center;
      
      .number-unit {
        font-size: 0.8em;
        font-weight: normal;
        margin-left: 4px;
      }
    }
  }
  
  .status-text {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    
    .text-content {
      text-align: center;
      word-break: break-word;
    }
  }
  
  .status-progress {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 100%;
    height: 100%;
    
    .progress-title {
      font-size: 12px;
      color: #666;
      margin-bottom: 8px;
      text-align: center;
    }
    
    .progress-container {
      margin-bottom: 8px;
    }
    
    .progress-value {
      font-size: 14px;
      color: #333;
      text-align: center;
      
      .progress-unit {
        font-size: 12px;
        margin-left: 4px;
      }
    }
  }
  
  .status-card {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    overflow: hidden;
    
    .card-header {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      background: #f5f7fa;
      border-bottom: 1px solid #e4e7ed;
      
      i {
        margin-right: 8px;
        font-size: 16px;
      }
      
      .card-title {
        font-size: 12px;
        color: #333;
        font-weight: 500;
      }
    }
    
    .card-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 12px;
      
      .card-value {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 4px;
        
        .card-unit {
          font-size: 12px;
          font-weight: normal;
          margin-left: 4px;
        }
      }
      
      .card-status {
        font-size: 12px;
        font-weight: 500;
      }
    }
  }
  
  .status-default {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: #f5f5f5;
    border: 1px dashed #ccc;
    border-radius: 4px;
    
    .default-icon {
      font-size: 24px;
      color: #999;
      margin-bottom: 8px;
    }
    
    .default-text {
      font-size: 12px;
      color: #666;
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
