import request from '@/utils/request'

// 查询录制记录列表
export function listRecord(query) {
  return request({
    url: '/video/record/list',
    method: 'get',
    params: query
  })
}

// 查询录制记录详细
export function getRecord(id) {
  return request({
    url: '/video/record/' + id,
    method: 'get'
  })
}

// 新增录制记录
export function addRecord(data) {
  return request({
    url: '/video/record',
    method: 'post',
    data: data
  })
}

// 修改录制记录
export function updateRecord(data) {
  return request({
    url: '/video/record',
    method: 'put',
    data: data
  })
}

// 删除录制记录
export function delRecord(id) {
  return request({
    url: '/video/record/' + id,
    method: 'delete'
  })
}

// 开始录制
export function startRecord(data) {
  return request({
    url: '/video/record/start',
    method: 'post',
    params: {
      deviceId: data.deviceId,
      recordName: data.recordName,
      recordType: data.recordType,
      duration: data.duration,
      fileFormat: data.fileFormat,
      quality: data.quality,
      autoStop: data.autoStop,
      remark: data.remark
    }
  })
}

// 停止录制
export function stopRecord(id) {
  return request({
    url: '/video/record/stop/' + id,
    method: 'post'
  })
}

// 获取录制状态
export function getRecordStatus(id) {
  return request({
    url: '/video/record/status/' + id,
    method: 'get'
  })
}

// 下载录制文件
export function downloadRecord(id) {
  return request({
    url: '/video/record/download/' + id,
    method: 'get',
    responseType: 'blob'
  })
}

// 获取回放地址
export function getPlaybackUrl(id) {
  return request({
    url: '/video/record/playback/' + id,
    method: 'get'
  })
}
