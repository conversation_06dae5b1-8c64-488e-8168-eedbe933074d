import request from '@/utils/request'

// 查询导寻配置模板列表
export function listTemplate(query) {
  return request({
    url: '/guide/config/template/list',
    method: 'get',
    params: query
  })
}

// 查询启用的导寻配置模板列表
export function listEnabledTemplate() {
  return request({
    url: '/guide/config/template/enabled',
    method: 'get'
  })
}

// 查询导寻配置模板详细
export function getTemplate(templateId) {
  return request({
    url: '/guide/config/template/' + templateId,
    method: 'get'
  })
}

// 根据模板编码查询导寻配置模板详细
export function getTemplateByCode(templateCode) {
  return request({
    url: '/guide/config/template/code/' + templateCode,
    method: 'get'
  })
}

// 获取默认导寻配置模板
export function getDefaultTemplate() {
  return request({
    url: '/guide/config/template/default',
    method: 'get'
  })
}

// 新增导寻配置模板
export function addTemplate(data) {
  return request({
    url: '/guide/config/template',
    method: 'post',
    data: data
  })
}

// 修改导寻配置模板
export function updateTemplate(data) {
  return request({
    url: '/guide/config/template',
    method: 'put',
    data: data
  })
}

// 删除导寻配置模板
export function delTemplate(templateId) {
  return request({
    url: '/guide/config/template/' + templateId,
    method: 'delete'
  })
}

// 设置默认模板
export function setDefaultTemplate(templateId) {
  return request({
    url: '/guide/config/template/setDefault/' + templateId,
    method: 'put'
  })
}

// 复制模板
export function copyTemplate(templateId, data) {
  return request({
    url: '/guide/config/template/copy/' + templateId,
    method: 'post',
    data: data
  })
}

// 校验模板编码
export function checkTemplateCodeUnique(templateCode, templateId) {
  return request({
    url: '/guide/config/template/checkTemplateCodeUnique',
    method: 'get',
    params: {
      templateCode: templateCode,
      templateId: templateId
    }
  })
}

// 导出导寻配置模板
export function exportTemplate(query) {
  return request({
    url: '/guide/config/template/export',
    method: 'post',
    params: query
  })
}

// 查询导寻配置规则列表
export function listRule(query) {
  return request({
    url: '/guide/config/rule/list',
    method: 'get',
    params: query
  })
}

// 查询激活的导寻配置规则列表
export function listActiveRule() {
  return request({
    url: '/guide/config/rule/active',
    method: 'get'
  })
}

// 查询导寻配置规则详细
export function getRule(ruleId) {
  return request({
    url: '/guide/config/rule/' + ruleId,
    method: 'get'
  })
}

// 根据模板ID查询关联的规则列表
export function getRuleByTemplateId(templateId) {
  return request({
    url: '/guide/config/rule/template/' + templateId,
    method: 'get'
  })
}

// 新增导寻配置规则
export function addRule(data) {
  return request({
    url: '/guide/config/rule',
    method: 'post',
    data: data
  })
}

// 修改导寻配置规则
export function updateRule(data) {
  return request({
    url: '/guide/config/rule',
    method: 'put',
    data: data
  })
}

// 删除导寻配置规则
export function delRule(ruleId) {
  return request({
    url: '/guide/config/rule/' + ruleId,
    method: 'delete'
  })
}

// 批量激活/停用规则
export function batchUpdateRuleStatus(ruleIds, isActive) {
  return request({
    url: '/guide/config/rule/batchStatus',
    method: 'put',
    data: {
      ruleIds: ruleIds,
      isActive: isActive
    }
  })
}

// 复制规则
export function copyRule(ruleId, data) {
  return request({
    url: '/guide/config/rule/copy/' + ruleId,
    method: 'post',
    data: data
  })
}

// 校验规则编码
export function checkRuleCodeUnique(ruleCode, ruleId) {
  return request({
    url: '/guide/config/rule/checkRuleCodeUnique',
    method: 'get',
    params: {
      ruleCode: ruleCode,
      ruleId: ruleId
    }
  })
}

// 查询导寻执行日志列表
export function listExecutionLog(query) {
  return request({
    url: '/guide/config/log/list',
    method: 'get',
    params: query
  })
}

// 查询最近的执行日志
export function listRecentExecutionLog(limit) {
  return request({
    url: '/guide/config/log/recent',
    method: 'get',
    params: { limit: limit }
  })
}

// 查询导寻执行日志详细
export function getExecutionLog(logId) {
  return request({
    url: '/guide/config/log/' + logId,
    method: 'get'
  })
}

// 删除导寻执行日志
export function delExecutionLog(logId) {
  return request({
    url: '/guide/config/log/' + logId,
    method: 'delete'
  })
}

// 清理过期的执行日志
export function cleanExpiredExecutionLog(days) {
  return request({
    url: '/guide/config/log/clean',
    method: 'delete',
    params: { days: days }
  })
}

// 统计执行状态分布
export function getExecutionStatusStatistics() {
  return request({
    url: '/guide/config/log/statistics/status',
    method: 'get'
  })
}

// 统计模板使用频率
export function getTemplateUsageStatistics() {
  return request({
    url: '/guide/config/log/statistics/template',
    method: 'get'
  })
}

// 统计执行成功率
export function getExecutionSuccessRate() {
  return request({
    url: '/guide/config/log/statistics/successRate',
    method: 'get'
  })
}

// 获取执行趋势数据
export function getExecutionTrendData(days) {
  return request({
    url: '/guide/config/log/trend',
    method: 'get',
    params: { days: days }
  })
}

// 获取热门物料排行
export function getPopularMaterialRanking(limit) {
  return request({
    url: '/guide/config/log/ranking/material',
    method: 'get',
    params: { limit: limit }
  })
}

// 获取执行性能统计
export function getExecutionPerformanceStatistics() {
  return request({
    url: '/guide/config/log/performance',
    method: 'get'
  })
}

// ==================== 设备参数配置相关API ====================

// 查询智能导寻设备参数配置列表
export function listGuideConfig(query) {
  return request({
    url: '/guide/config/params/list',
    method: 'get',
    params: query
  })
}

// 查询智能导寻设备参数配置详细
export function getGuideConfig(configId) {
  return request({
    url: '/guide/config/params/' + configId,
    method: 'get'
  })
}

// 新增智能导寻设备参数配置
export function addGuideConfig(data) {
  return request({
    url: '/guide/config/params',
    method: 'post',
    data: data
  })
}

// 修改智能导寻设备参数配置
export function updateGuideConfig(data) {
  return request({
    url: '/guide/config/params',
    method: 'put',
    data: data
  })
}

// 删除智能导寻设备参数配置
export function delGuideConfig(configId) {
  return request({
    url: '/guide/config/params/' + configId,
    method: 'delete'
  })
}

// 根据设备ID查询参数配置
export function getGuideConfigByDeviceId(deviceId) {
  return request({
    url: '/guide/config/params/device/' + deviceId,
    method: 'get'
  })
}

// 重置设备参数配置为默认值
export function resetGuideConfig(deviceId) {
  return request({
    url: '/guide/config/params/reset/' + deviceId,
    method: 'post'
  })
}

// 导出设备参数配置
export function exportGuideConfig(deviceId) {
  return request({
    url: '/guide/config/params/export/' + deviceId,
    method: 'get'
  })
}

// 导入设备参数配置
export function importGuideConfig(deviceId, configData) {
  return request({
    url: '/guide/config/params/import/' + deviceId,
    method: 'post',
    data: configData
  })
}

// 批量更新设备参数配置
export function batchUpdateGuideConfig(configList) {
  return request({
    url: '/guide/config/params/batch',
    method: 'put',
    data: configList
  })
}
