<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="清单编号" prop="billCode">
        <el-input
          v-model="queryParams.billCode"
          placeholder="请输入物料清单编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="清单名称" prop="billName">
        <el-input
          v-model="queryParams.billName"
          placeholder="请输入物料清单名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="草稿" value="0" />
          <el-option label="正常" value="1" />
          <el-option label="锁定" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="录入方式" prop="inputMethod">
        <el-select v-model="queryParams.inputMethod" placeholder="请选择录入方式" clearable>
          <el-option label="网页" value="web" />
          <el-option label="自助机" value="kiosk" />
          <el-option label="小程序" value="miniapp" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['material:bill:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['material:bill:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['material:bill:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-lock"
          size="mini"
          :disabled="multiple"
          @click="handleLock"
          v-hasPermi="['material:bill:lock']"
        >锁定</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-unlock"
          size="mini"
          :disabled="multiple"
          @click="handleUnlock"
          v-hasPermi="['material:bill:unlock']"
        >解锁</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['material:bill:export']"
        >导出</el-button>
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['material:bill:import']"
        >导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-refresh"
          size="mini"
          @click="handleRefresh"
        >刷新</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="materialBillList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="清单编号" align="center" prop="billCode" width="180">
        <template slot-scope="scope">
          <el-link type="primary" @click="handleViewDetail(scope.row)">{{ scope.row.billCode }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="清单名称" align="center" prop="billName" :show-overflow-tooltip="true" />
      <el-table-column label="描述" align="center" prop="description" :show-overflow-tooltip="true" />
      <el-table-column label="明细条数" align="center" prop="itemCount" width="80" />
      <el-table-column label="总数量" align="center" prop="totalQuantity" width="100" />
      <el-table-column label="总重量(kg)" align="center" prop="totalWeight" width="120" />
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.material_bill_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="录入方式" align="center" prop="inputMethod" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.material_input_method" :value="scope.row.inputMethod"/>
        </template>
      </el-table-column>
      <el-table-column label="锁定人" align="center" prop="lockedBy" width="100" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleViewDetail(scope.row)"
            v-hasPermi="['material:bill:query']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['material:bill:edit']"
            :disabled="scope.row.status === '2'"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['material:bill:remove']"
            :disabled="scope.row.status === '2'"
          >删除</el-button>
          <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)">
            <span class="el-dropdown-link">
              更多<i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                command="lock"
                icon="el-icon-lock"
                :disabled="scope.row.status === '2'"
                v-hasPermi="['material:bill:lock']"
              >锁定</el-dropdown-item>
              <el-dropdown-item
                command="unlock"
                icon="el-icon-unlock"
                :disabled="scope.row.status !== '2'"
                v-hasPermi="['material:bill:unlock']"
              >解锁</el-dropdown-item>
              <el-dropdown-item
                command="copy"
                icon="el-icon-copy-document"
                v-hasPermi="['material:bill:copy']"
              >复制</el-dropdown-item>
              <el-dropdown-item
                command="validate"
                icon="el-icon-scale-to-original"
                v-hasPermi="['material:bill:validate']"
              >重量校验</el-dropdown-item>
              <el-dropdown-item
                command="statistics"
                icon="el-icon-data-analysis"
                v-hasPermi="['material:bill:query']"
              >统计信息</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改物料清单对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <material-bill-form ref="materialBillForm" @success="handleFormSuccess" @cancel="open = false" />
    </el-dialog>

    <!-- 物料清单详情对话框 -->
    <el-dialog title="物料清单详情" :visible.sync="detailOpen" width="1400px" append-to-body>
      <material-bill-detail
        ref="materialBillDetail"
        @close="detailOpen = false"
        @editBill="handleEditFromDetail"
        @refresh="getList"
      />
    </el-dialog>

    <!-- 物料清单导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :on-error="handleFileError"
        :before-upload="beforeUpload"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的物料清单数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMaterialBill, getMaterialBill, delMaterialBill, lockMaterialBill, unlockMaterialBill, exportMaterialBill } from "@/api/material/bill";
import MaterialBillForm from "./components/MaterialBillForm";
import MaterialBillDetail from "./components/MaterialBillDetail";

export default {
  name: "MaterialBill",
  dicts: ['material_bill_status', 'material_input_method'],
  components: {
    MaterialBillForm,
    MaterialBillDetail
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 物料清单表格数据
      materialBillList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示详情弹出层
      detailOpen: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        billCode: null,
        billName: null,
        description: null,
        status: null,
        inputMethod: null,
        lockedBy: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 导入参数
      upload: {
        // 是否显示弹出层（导入）
        open: false,
        // 弹出层标题（导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的数据
        updateSupport: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + this.$store.getters.token },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/material/bill/importData"
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询物料清单列表 */
    getList() {
      this.loading = true;
      listMaterialBill(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.materialBillList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        billCode: null,
        billName: null,
        description: null,
        totalWeight: null,
        totalQuantity: null,
        itemCount: null,
        status: "0",
        inputMethod: null,
        lockedBy: null,
        lockedTime: null,
        weightTolerance: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加物料清单";
      this.$nextTick(() => {
        this.$refs.materialBillForm.init();
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getMaterialBill(id).then(response => {
        this.open = true;
        this.title = "修改物料清单";
        this.$nextTick(() => {
          this.$refs.materialBillForm.init(response.data);
        });
      });
    },
    /** 查看详情 */
    handleViewDetail(row) {
      this.detailOpen = true;
      this.$nextTick(() => {
        this.$refs.materialBillDetail.init(row.id);
      });
    },
    /** 表单提交成功 */
    handleFormSuccess() {
      this.open = false;
      this.getList();
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除物料清单编号为"' + ids + '"的数据项？').then(function() {
        return delMaterialBill(ids);
      }).then(() => {
        this.getList();
        this.$message.success("删除成功");
      }).catch(() => {});
    },
    /** 锁定按钮操作 */
    handleLock() {
      const ids = this.ids;
      this.$modal.confirm('是否确认锁定选中的物料清单？').then(() => {
        const promises = ids.map(id => lockMaterialBill(id));
        return Promise.all(promises);
      }).then(() => {
        this.getList();
        this.$message.success("锁定成功");
      }).catch(() => {});
    },
    /** 解锁按钮操作 */
    handleUnlock() {
      const ids = this.ids;
      this.$modal.confirm('是否确认解锁选中的物料清单？').then(() => {
        const promises = ids.map(id => unlockMaterialBill(id));
        return Promise.all(promises);
      }).then(() => {
        this.getList();
        this.$message.success("解锁成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('material/bill/export', {
        ...this.queryParams
      }, `物料清单_${new Date().getTime()}.xlsx`)
    },

    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "物料清单导入";
      this.upload.open = true;
    },

    /** 下载模板操作 */
    importTemplate() {
      this.download('material/bill/importTemplate', {}, `物料清单导入模板_${new Date().getTime()}.xlsx`)
    },

    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },

    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();

      // 检查响应格式
      if (response && response.code !== undefined) {
        if (response.code === 200) {
          this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
          this.getList();
        } else {
          this.$message.error(response.msg || "导入失败");
        }
      } else {
        // 处理非标准响应格式
        this.$message.error("导入响应格式异常");
      }
    },

    // 文件上传失败处理
    handleFileError(err, file, fileList) {
      this.upload.isUploading = false;
      this.$message.error("导入失败，请检查文件格式或联系管理员");
      console.error("文件上传错误:", err);
    },

    // 上传前验证
    beforeUpload(file) {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                     file.type === 'application/vnd.ms-excel';
      const isLt10M = file.size / 1024 / 1024 < 10;

      if (!isExcel) {
        this.$message.error('只能上传 Excel 格式的文件!');
        return false;
      }
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过 10MB!');
        return false;
      }
      return true;
    },

    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /** 更多操作 */
    handleCommand(command, row) {
      switch (command) {
        case "lock":
          this.handleSingleLock(row);
          break;
        case "unlock":
          this.handleSingleUnlock(row);
          break;
        case "copy":
          this.handleCopy(row);
          break;
        case "validate":
          this.handleValidateWeight(row);
          break;
        case "statistics":
          this.handleStatistics(row);
          break;
      }
    },
    /** 单个锁定 */
    handleSingleLock(row) {
      this.$modal.confirm('是否确认锁定物料清单"' + row.billName + '"？').then(() => {
        return lockMaterialBill(row.id);
      }).then(() => {
        this.getList();
        this.$message.success("锁定成功");
      }).catch(() => {});
    },
    /** 单个解锁 */
    handleSingleUnlock(row) {
      this.$modal.confirm('是否确认解锁物料清单"' + row.billName + '"？').then(() => {
        return unlockMaterialBill(row.id);
      }).then(() => {
        this.getList();
        this.$message.success("解锁成功");
      }).catch(() => {});
    },
    /** 复制 */
    handleCopy(row) {
      this.$prompt('请输入新的物料清单名称', '复制物料清单', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /\S/,
        inputErrorMessage: '物料清单名称不能为空'
      }).then(({ value }) => {
        // TODO: 实现复制功能
        this.$message.success("复制功能待实现");
      }).catch(() => {});
    },

    /** 刷新 */
    handleRefresh() {
      this.getList();
      this.$message.success("刷新成功");
    },

    /** 重量校验 */
    handleValidateWeight(row) {
      this.$prompt('请输入实际重量(kg)', '重量校验', {
        confirmButtonText: '校验',
        cancelButtonText: '取消',
        inputPattern: /^\d+(\.\d{1,3})?$/,
        inputErrorMessage: '请输入正确的重量格式'
      }).then(({ value }) => {
        // TODO: 实现重量校验功能
        this.$message.success("重量校验功能待实现");
      }).catch(() => {});
    },

    /** 统计信息 */
    handleStatistics(row) {
      this.$message({
        message: `物料清单统计信息：
清单编号：${row.billCode}
明细条数：${row.itemCount || 0}
总数量：${row.totalQuantity || 0}
总重量：${row.totalWeight || 0} kg
重量容差：${row.weightTolerance || 0} kg`,
        type: 'info',
        duration: 5000
      });
    },

    /** 从详情页面编辑 */
    handleEditFromDetail(billInfo) {
      // 关闭详情对话框
      this.detailOpen = false;
      // 打开编辑对话框
      this.title = "修改物料清单";
      this.open = true;
      this.$nextTick(() => {
        this.$refs.materialBillForm.init(billInfo);
      });
    }
  }
};
</script>
