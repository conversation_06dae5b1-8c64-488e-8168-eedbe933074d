<template>
  <div class="app-container">
    <!-- 搜索区域 - 卡片式布局 -->
    <el-card class="search-card" shadow="hover">
      <div slot="header" class="clearfix">
        <span><i class="el-icon-search"></i> 物料映射查询</span>
      </div>
      
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="120px">
        <el-form-item label="系统物料编码" prop="materialCode">
          <el-input
            v-model="queryParams.materialCode"
            placeholder="请输入系统物料编码"
            clearable
            prefix-icon="el-icon-document"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="外部系统编码" prop="externalCode">
          <el-input
            v-model="queryParams.externalCode"
            placeholder="请输入外部系统编码"
            clearable
            prefix-icon="el-icon-document-copy"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="外部系统标识" prop="externalSystem">
          <el-select
            v-model="queryParams.externalSystem"
            placeholder="请选择外部系统"
            clearable
            style="width: 100%"
          >
            <el-option label="SAP" value="SAP" />
            <el-option label="ERP" value="ERP" />
            <el-option label="WMS" value="WMS" />
            <el-option label="MES" value="MES" />
            <el-option label="OTHER" value="OTHER" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否主要映射" prop="isMain">
          <el-select
            v-model="queryParams.isMain"
            placeholder="请选择"
            clearable
            style="width: 100%"
          >
            <el-option label="是" value="1" />
            <el-option label="否" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          <el-button type="info" icon="el-icon-search" @click="handleQuickSearch">快速查询</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <el-row :gutter="10" class="mt10 mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['material:mapping:add']"
        >新增映射</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['material:mapping:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['material:mapping:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['material:mapping:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格区域 -->
    <el-card shadow="hover">
      <el-table
        v-loading="loading"
        :data="mappingList"
        border
        stripe
        highlight-current-row
        @selection-change="handleSelectionChange"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" align="center" fixed="left" />
        <el-table-column type="index" label="序号" width="60" align="center" fixed="left" />
        
        <!-- 系统物料信息 -->
        <el-table-column label="系统物料信息" align="center">
          <el-table-column label="编码" prop="materialCode" width="120" align="center" :show-overflow-tooltip="true" />
          <el-table-column label="ID" prop="materialId" width="80" align="center" :show-overflow-tooltip="true" />
        </el-table-column>
        
        <!-- 外部系统信息 -->
        <el-table-column label="外部系统信息" align="center">
          <el-table-column label="系统标识" prop="externalSystem" width="100" align="center" />
          <el-table-column label="物料编码" prop="externalCode" width="120" align="center" :show-overflow-tooltip="true" />
          <el-table-column label="物料名称" prop="externalName" width="150" align="center" :show-overflow-tooltip="true" />
        </el-table-column>
        
        <!-- 映射状态 -->
        <el-table-column label="是否主要映射" align="center" width="120">
          <template slot-scope="scope">
            <el-tag :type="scope.row.isMain === '1' ? 'success' : 'info'">
              {{ scope.row.isMain === '1' ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="映射状态" align="center" width="120">
          <template slot-scope="scope">
            <div class="mapping-status-container">
              <el-switch
                v-model="scope.row.mappingStatus"
                :active-value="'0'"
                :inactive-value="'1'"
                @change="handleStatusChange(scope.row)"
                v-hasPermi="['material:mapping:edit']"
                class="mapping-status-switch"
              >
              </el-switch>
              <span :class="['mapping-status-text', scope.row.mappingStatus === '0' ? 'status-normal' : 'status-disabled']">
                {{ scope.row.mappingStatus === '0' ? '正常' : '停用' }}
              </span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="备注" align="center" prop="remark" width="150" :show-overflow-tooltip="true" />
        
        <el-table-column label="操作" align="center" width="150" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
            >查看</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['material:mapping:edit']"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['material:mapping:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 查看详情对话框 -->
    <el-dialog title="物料映射详情" :visible.sync="viewDialogVisible" width="700px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="系统物料ID">{{ form.materialId }}</el-descriptions-item>
        <el-descriptions-item label="系统物料编码">{{ form.materialCode }}</el-descriptions-item>
        <el-descriptions-item label="外部系统标识">{{ form.externalSystem }}</el-descriptions-item>
        <el-descriptions-item label="外部系统物料编码">{{ form.externalCode }}</el-descriptions-item>
        <el-descriptions-item label="外部系统物料名称">{{ form.externalName }}</el-descriptions-item>
        <el-descriptions-item label="是否主要映射">
          <el-tag :type="form.isMain === '1' ? 'success' : 'info'">
            {{ form.isMain === '1' ? '是' : '否' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="映射状态">
          <el-tag :type="form.mappingStatus === '0' ? 'success' : 'danger'">
            {{ form.mappingStatus === '0' ? '正常' : '停用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="备注">{{ form.remark }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(form.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ parseTime(form.updateTime) }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 添加或修改物料编码映射对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="650px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <!-- 系统物料信息区域 -->
        <el-divider content-position="left">系统物料信息</el-divider>
        <el-row>
          <el-col :span="15">
            <el-form-item label="系统物料编码" prop="materialCode">
              <el-input 
                v-model="form.materialCode" 
                placeholder="请输入系统物料编码" 
                clearable 
                style="width: 80%"
              >
                <el-button slot="append" icon="el-icon-search" @click="handleSearchMaterial"></el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item label="系统物料ID" prop="materialId">
              <el-input v-model="form.materialId" placeholder="系统生成" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 外部系统信息区域 -->
        <el-divider content-position="left">外部系统信息</el-divider>
        <el-row>
          <el-col :span="12">
            <el-form-item label="外部系统标识" prop="externalSystem">
              <el-select v-model="form.externalSystem" placeholder="请选择外部系统" style="width: 100%">
                <el-option label="SAP" value="SAP" />
                <el-option label="ERP" value="ERP" />
                <el-option label="WMS" value="WMS" />
                <el-option label="MES" value="MES" />
                <el-option label="OTHER" value="OTHER" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="外部系统编码" prop="externalCode">
              <el-input v-model="form.externalCode" placeholder="请输入外部系统物料编码">
                <el-button slot="append" icon="el-icon-search" @click="handleSearchByExternalCode" v-if="!form.id"></el-button>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="外部系统名称" prop="externalName">
              <el-input v-model="form.externalName" placeholder="请输入外部系统物料名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否主要映射" prop="isMain">
              <el-radio-group v-model="form.isMain">
                <el-radio label="1">是</el-radio>
                <el-radio label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="映射状态" prop="mappingStatus">
          <div class="mapping-status-container">
            <el-switch
              v-model="form.mappingStatus"
              :active-value="'0'"
              :inactive-value="'1'"
              class="mapping-status-switch"
            >
            </el-switch>
            <span :class="['mapping-status-text', form.mappingStatus === '0' ? 'status-normal' : 'status-disabled']">
              {{ form.mappingStatus === '0' ? '正常' : '停用' }}
            </span>
          </div>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 物料选择对话框 -->
    <el-dialog title="选择物料" :visible.sync="materialSelectVisible" width="800px" append-to-body>
      <el-form :model="materialQuery" ref="materialQueryForm" size="small" :inline="true" label-width="90px">
        <el-form-item label="物料编码" prop="materialCode">
          <el-input v-model="materialQuery.materialCode" placeholder="请输入物料编码" clearable />
        </el-form-item>
        <el-form-item label="物料名称" prop="materialName">
          <el-input v-model="materialQuery.materialName" placeholder="请输入物料名称" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="searchMaterials">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetMaterialQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table 
        v-loading="materialTableLoading" 
        :data="materialList" 
        border 
        stripe 
        highlight-current-row
        @row-click="handleMaterialSelect"
        style="width: 100%">
        <el-table-column type="index" label="序号" width="50" align="center" />
        <el-table-column label="物料ID" prop="id" width="80" align="center" />
        <el-table-column label="物料编码" prop="materialCode" width="120" align="center" :show-overflow-tooltip="true" />
        <el-table-column label="物料名称" prop="materialName" width="150" align="center" :show-overflow-tooltip="true" />
        <el-table-column label="物料类别" prop="categoryName" width="120" align="center" :show-overflow-tooltip="true" />
        <el-table-column label="规格型号" prop="specification" width="120" align="center" :show-overflow-tooltip="true" />
        <el-table-column label="单位" prop="unit" width="80" align="center" />
        <el-table-column label="操作" align="center" width="100">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-check" @click.stop="confirmSelectMaterial(scope.row)">选择</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="materialTotal>0"
        :total="materialTotal"
        :page.sync="materialQuery.pageNum"
        :limit.sync="materialQuery.pageSize"
        @pagination="searchMaterials"
      />

      <div slot="footer" class="dialog-footer">
        <el-button @click="materialSelectVisible = false">取 消</el-button>
      </div>
    </el-dialog>
    
    <!-- 快速查询对话框 -->
    <el-dialog title="快速查询物料映射" :visible.sync="quickSearchVisible" width="600px" append-to-body>
      <el-form :model="quickSearch" ref="quickSearchForm" size="small" label-width="120px">
        <el-form-item label="外部系统编码" prop="externalCode" required>
          <el-input 
            v-model="quickSearch.externalCode" 
            placeholder="请输入外部系统编码" 
            clearable
            @keyup.enter.native="handleQuickSearchSubmit"
          />
        </el-form-item>
        
        <el-form-item label="外部系统标识" prop="externalSystem">
          <el-select
            v-model="quickSearch.externalSystem"
            placeholder="请选择外部系统"
            clearable
            style="width: 100%"
          >
            <el-option label="SAP" value="SAP" />
            <el-option label="ERP" value="ERP" />
            <el-option label="WMS" value="WMS" />
            <el-option label="MES" value="MES" />
            <el-option label="OTHER" value="OTHER" />
            <el-option label="全部系统" value="" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <!-- 查询结果 -->
      <div v-if="quickSearchResult.length > 0" style="margin-top: 20px">
        <el-divider content-position="left">查询结果</el-divider>
        <el-table
          :data="quickSearchResult"
          border
          stripe
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column type="index" label="序号" width="50" align="center" />
          <el-table-column label="物料编码" prop="materialCode" width="120" align="center" :show-overflow-tooltip="true" />
          <el-table-column label="外部系统" prop="externalSystem" width="100" align="center" />
          <el-table-column label="外部编码" prop="externalCode" width="120" align="center" :show-overflow-tooltip="true" />
          <el-table-column label="外部名称" prop="externalName" width="150" align="center" :show-overflow-tooltip="true" />
          <el-table-column label="操作" align="center" width="80">
            <template slot-scope="scope">
              <el-button 
                size="mini" 
                type="text" 
                icon="el-icon-view" 
                @click="viewQuickSearchDetail(scope.row)"
              >查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <div v-else-if="quickSearchSubmitted" style="text-align: center; margin-top: 20px; color: #909399;">
        <i class="el-icon-warning" style="font-size: 24px;"></i>
        <p>未找到任何匹配的物料映射记录</p>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleQuickSearchSubmit">搜 索</el-button>
        <el-button @click="quickSearchVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMapping, getMapping, delMapping, addMapping, updateMapping } from "@/api/material/mapping"
import { listMaterial, getMaterialByCode, getMaterialByMapping } from "@/api/material/material"

export default {
  name: "Mapping",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 物料编码映射表格数据
      mappingList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查看详情对话框是否可见
      viewDialogVisible: false,
      // 物料选择对话框是否可见
      materialSelectVisible: false,
      // 物料表格加载状态
      materialTableLoading: false,
      // 物料列表数据
      materialList: [],
      // 物料总条数
      materialTotal: 0,
      // 物料查询参数
      materialQuery: {
        pageNum: 1,
        pageSize: 10,
        materialCode: null,
        materialName: null
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        materialId: null,
        materialCode: null,
        externalCode: null,
        externalSystem: null,
        externalName: null,
        isMain: null,
        mappingStatus: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        materialCode: [
          { required: true, message: "系统物料编码不能为空", trigger: "blur" }
        ],
        externalCode: [
          { required: true, message: "外部系统物料编码不能为空", trigger: "blur" }
        ],
        externalSystem: [
          { required: true, message: "外部系统标识不能为空", trigger: "blur" }
        ],
      },
      // 快速查询对话框是否可见
      quickSearchVisible: false,
      // 快速查询参数
      quickSearch: {
        externalCode: null,
        externalSystem: null
      },
      // 快速查询结果
      quickSearchResult: [],
      // 是否已提交查询
      quickSearchSubmitted: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询物料编码映射列表 */
    getList() {
      this.loading = true
      listMapping(this.queryParams).then(response => {
        // 确保所有状态都是字符串类型
        if (response.rows && response.rows.length > 0) {
          response.rows.forEach(row => {
            row.mappingStatus = row.mappingStatus !== undefined ? row.mappingStatus.toString() : '0'
            row.isMain = row.isMain !== undefined ? row.isMain.toString() : '0'
          })
        }
        this.mappingList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        materialId: null,
        materialCode: null,
        externalCode: null,
        externalSystem: null,
        externalName: null,
        isMain: "0",
        mappingStatus: "0",
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加物料编码映射"
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.reset()
      const id = row.id
      getMapping(id).then(response => {
        if (response.data) {
          // 确保状态是字符串类型
          response.data.mappingStatus = response.data.mappingStatus !== undefined ? 
            response.data.mappingStatus.toString() : '0'
          response.data.isMain = response.data.isMain !== undefined ? 
            response.data.isMain.toString() : '0'
          this.form = response.data
          this.viewDialogVisible = true
        }
      })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getMapping(id).then(response => {
        if (response.data) {
          // 确保状态是字符串类型
          response.data.mappingStatus = response.data.mappingStatus !== undefined ? 
            response.data.mappingStatus.toString() : '0'
          response.data.isMain = response.data.isMain !== undefined ? 
            response.data.isMain.toString() : '0'
          this.form = response.data
          this.open = true
          this.title = "修改物料编码映射"
        }
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 确保表单数据中的状态是字符串类型
          this.form.mappingStatus = this.form.mappingStatus.toString()
          this.form.isMain = this.form.isMain.toString()
          
          if (this.form.id != null) {
            updateMapping(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
              // 如果是主要映射，同步到物料信息
              if (this.form.isMain === '1') {
                this.syncToMaterial(this.form)
              }
            })
          } else {
            addMapping(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
              // 如果是主要映射，同步到物料信息
              if (this.form.isMain === '1') {
                this.syncToMaterial(this.form)
              }
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除物料编码映射编号为"' + ids + '"的数据项？').then(function() {
        return delMapping(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('material/mapping/export', {
        ...this.queryParams
      }, `物料映射_${new Date().getTime()}.xlsx`)
    },
    /** 状态修改 */
    handleStatusChange(row) {
      // 确保状态是字符串类型
      const currentStatus = row.mappingStatus.toString()
      const newStatus = currentStatus === '0' ? '1' : '0'
      const text = newStatus === '0' ? "启用" : "停用"
      
      this.$modal.confirm('确认要' + text + '该映射吗？').then(() => {
        // 更新为新的状态（字符串类型）
        row.mappingStatus = newStatus
        
        updateMapping(row).then(() => {
          this.$modal.msgSuccess(text + "成功")
          // 如果启用且为主要映射，同步到物料信息
          if (newStatus === '0' && row.isMain === '1') {
            this.syncToMaterial(row)
          }
          
          // 重新获取列表，确保状态一致
          this.getList()
        }).catch(() => {
          // 恢复原状态（字符串类型）
          row.mappingStatus = currentStatus
        })
      }).catch(() => {
        // 恢复原状态（字符串类型）
        row.mappingStatus = currentStatus
      })
    },
    /** 根据外部编码查询物料 */
    handleSearchByExternalCode() {
      if (!this.form.externalCode || !this.form.externalSystem) {
        this.$modal.msgWarning("请输入外部系统编码和选择外部系统")
        return
      }

      getMaterialByMapping(this.form.externalCode, this.form.externalSystem).then(response => {
        if (response.data) {
          // 找到了映射关系，填充物料信息
          this.form.materialId = response.data.materialId
          this.form.materialCode = response.data.materialCode
          this.$modal.msgSuccess("找到已有映射关系")
        } else {
          this.$modal.msgWarning("未找到映射关系，请手动选择物料")
        }
      }).catch(() => {
        this.$modal.msgError("查询映射关系失败")
      })
    },
    
    /** 快速查询物料映射 */
    handleQuickSearch() {
      // 打开快速查询对话框
      this.quickSearchVisible = true
      this.quickSearchSubmitted = false
      this.quickSearchResult = []
    },
    
    /** 提交快速查询 */
    handleQuickSearchSubmit() {
      if (!this.quickSearch.externalCode) {
        this.$modal.msgWarning("请输入外部系统编码")
        return
      }
      
      // 构造查询参数
      const params = {
        externalCode: this.quickSearch.externalCode,
        pageSize: 999 // 获取所有匹配记录
      }
      
      // 如果选择了特定系统，添加系统筛选条件
      if (this.quickSearch.externalSystem) {
        params.externalSystem = this.quickSearch.externalSystem
      }
      
      // 调用映射列表API查询
      listMapping(params).then(response => {
        this.quickSearchResult = response.rows || []
        this.quickSearchSubmitted = true
      }).catch(error => {
        console.error("快速查询失败", error)
        this.$modal.msgError("查询失败，请重试")
      })
    },
    
    /** 查看快速查询结果详情 */
    viewQuickSearchDetail(row) {
      this.reset()
      const id = row.id
      getMapping(id).then(response => {
        this.form = response.data
        this.viewDialogVisible = true
        // 关闭快速查询对话框
        this.quickSearchVisible = false
      })
    },

    /** 搜索物料 */
    handleSearchMaterial() {
      if (!this.form.materialCode) {
        this.$modal.msgWarning("请输入系统物料编码")
        return
      }
      getMaterialByCode(this.form.materialCode).then(response => {
        if (response.data) {
          this.form.materialId = response.data.id
          this.form.materialCode = response.data.materialCode
          
          // 如果物料中已有外部系统信息，可以自动填充
          if (response.data.externalCode && !this.form.externalCode && this.form.id == null) {
            this.form.externalCode = response.data.externalCode
            this.form.externalSystem = response.data.externalSystem
          }
          
          this.$modal.msgSuccess("物料信息查询成功")
        } else {
          this.$modal.msgWarning("未找到物料信息，请先创建物料或从列表中选择")
          this.materialSelectVisible = true
          this.materialQuery.materialCode = this.form.materialCode
          this.searchMaterials()
        }
      }).catch(() => {
        this.$modal.msgError("物料信息查询失败")
      })
    },
    /** 打开物料选择对话框 */
    openMaterialSelect() {
      this.materialSelectVisible = true
      this.searchMaterials()
    },
    /** 搜索物料列表 */
    searchMaterials() {
      this.materialTableLoading = true
      listMaterial(this.materialQuery).then(response => {
        this.materialList = response.rows
        this.materialTotal = response.total
        this.materialTableLoading = false
      })
    },
    /** 重置物料查询 */
    resetMaterialQuery() {
      this.materialQuery = {
        pageNum: 1,
        pageSize: 10,
        materialCode: null,
        materialName: null
      }
      this.searchMaterials()
    },
    /** 处理物料选择 */
    handleMaterialSelect(row) {
      this.confirmSelectMaterial(row)
    },
    /** 确认选择物料 */
    confirmSelectMaterial(row) {
      this.form.materialId = row.id
      this.form.materialCode = row.materialCode
      this.materialSelectVisible = false
      this.$modal.msgSuccess("已选择物料：" + row.materialCode)
    },
    
    /** 同步映射数据到物料信息 */
    syncToMaterial(mappingData) {
      // 先获取物料详情
      getMaterial(mappingData.materialId).then(response => {
        if (response.data) {
          // 更新物料的外部系统信息
          const materialData = response.data
          materialData.externalCode = mappingData.externalCode
          materialData.externalSystem = mappingData.externalSystem
          
          // 调用更新物料API
          updateMaterial(materialData).then(res => {
            this.$modal.msgSuccess("已同步映射信息到物料基础信息")
          }).catch(err => {
            console.error("同步到物料信息失败", err)
            this.$modal.msgError("同步到物料基础信息失败")
          })
        }
      }).catch(err => {
        console.error("获取物料详情失败", err)
        this.$modal.msgError("无法获取物料详情，同步失败")
      })
    }
  }
}
</script>

<style scoped>
.search-card {
  margin-bottom: 15px;
}
.mt10 {
  margin-top: 10px;
}
.mb8 {
  margin-bottom: 8px;
}
.mapping-status-container {
  display: flex;
  align-items: center;
  justify-content: center;
}
.mapping-status-switch {
  margin-right: 10px;
}
.mapping-status-text {
  font-size: 14px;
  padding: 2px 8px;
  border-radius: 4px;
}
.status-normal {
  color: #67c23a;
  background-color: #f0f9eb;
}
.status-disabled {
  color: #909399;
  background-color: #f4f4f5;
}
.table-empty-text {
  padding: 20px 0;
  text-align: center;
  color: #909399;
}
</style>
