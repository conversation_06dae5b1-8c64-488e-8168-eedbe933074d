import request from '@/utils/request'

// 查询分析记录列表
export function listAnalysis(query) {
  return request({
    url: '/video/analysis/list',
    method: 'get',
    params: query
  })
}

// 查询分析记录详细
export function getAnalysis(id) {
  return request({
    url: '/video/analysis/' + id,
    method: 'get'
  })
}

// 新增分析记录
export function addAnalysis(data) {
  return request({
    url: '/video/analysis',
    method: 'post',
    data: data
  })
}

// 修改分析记录
export function updateAnalysis(data) {
  return request({
    url: '/video/analysis',
    method: 'put',
    data: data
  })
}

// 删除分析记录
export function delAnalysis(id) {
  return request({
    url: '/video/analysis/' + id,
    method: 'delete'
  })
}

// 开始分析
export function startAnalysis(data) {
  return request({
    url: '/video/analysis/start',
    method: 'post',
    data: data
  })
}

// 停止分析
export function stopAnalysis(id) {
  return request({
    url: '/video/analysis/stop/' + id,
    method: 'post'
  })
}

// 获取分析状态
export function getAnalysisStatus(id) {
  return request({
    url: '/video/analysis/status/' + id,
    method: 'get'
  })
}

// 获取分析结果
export function getAnalysisResult(id) {
  return request({
    url: '/video/analysis/result/' + id,
    method: 'get'
  })
}

// 人脸识别
export function faceRecognition(data) {
  return request({
    url: '/video/analysis/face/recognition',
    method: 'post',
    data: data
  })
}

// 行为分析
export function behaviorAnalysis(data) {
  return request({
    url: '/video/analysis/behavior',
    method: 'post',
    data: data
  })
}

// 物体检测
export function objectDetection(data) {
  return request({
    url: '/video/analysis/object/detection',
    method: 'post',
    data: data
  })
}

// 运动检测
export function motionDetection(data) {
  return request({
    url: '/video/analysis/motion/detection',
    method: 'post',
    data: data
  })
}
