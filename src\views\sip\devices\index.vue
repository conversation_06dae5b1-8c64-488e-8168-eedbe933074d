<template>
  <div class="sip-devices">
    <!-- 搜索和操作栏 -->
    <div class="search-bar">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="设备ID">
          <el-input
            v-model="searchForm.deviceId"
            placeholder="请输入设备ID"
            clearable
            @keyup.enter.native="handleSearch">
          </el-input>
        </el-form-item>
        <el-form-item label="设备名称">
          <el-input
            v-model="searchForm.deviceName"
            placeholder="请输入设备名称"
            clearable
            @keyup.enter.native="handleSearch">
          </el-input>
        </el-form-item>
        <el-form-item label="IP地址">
          <el-input
            v-model="searchForm.ip"
            placeholder="请输入IP地址"
            clearable
            @keyup.enter.native="handleSearch">
          </el-input>
        </el-form-item>
        <el-form-item label="设备状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="在线" value="ONLINE"></el-option>
            <el-option label="离线" value="OFFLINE"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
      
      <div class="action-buttons">
        <el-button type="success" icon="el-icon-refresh" @click="refreshData">刷新</el-button>
        <el-button type="info" icon="el-icon-search" @click="queryAllDevices">查询目录</el-button>
        <el-button type="warning" icon="el-icon-download" @click="exportData">导出</el-button>
      </div>
    </div>

    <!-- 设备列表 -->
    <el-table
      v-loading="loading"
      :data="deviceList"
      style="width: 100%">
      
      <el-table-column prop="deviceId" label="设备ID" min-width="150">
        <template slot-scope="scope">
          <el-link type="primary" @click="showDeviceDetails(scope.row)">
            {{ scope.row.deviceId }}
          </el-link>
        </template>
      </el-table-column>
      
      <el-table-column prop="deviceName" label="设备名称" min-width="120">
        <template slot-scope="scope">
          {{ scope.row.deviceName || 'N/A' }}
        </template>
      </el-table-column>
      
      <el-table-column prop="ip" label="IP地址" width="120"></el-table-column>
      
      <el-table-column prop="port" label="端口" width="80"></el-table-column>
      
      <el-table-column prop="transport" label="传输协议" width="80">
        <template slot-scope="scope">
          <el-tag size="mini" :type="scope.row.transport === 'UDP' ? 'success' : 'info'">
            {{ scope.row.transport }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="status" label="状态" width="80">
        <template slot-scope="scope">
          <el-tag size="mini" :type="scope.row.status === 'ONLINE' ? 'success' : 'danger'">
            {{ scope.row.status === 'ONLINE' ? '在线' : '离线' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="deviceType" label="设备类型" width="100">
        <template slot-scope="scope">
          <el-tag size="mini" type="info">
            {{ getDeviceTypeText(scope.row.deviceType) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="manufacturer" label="制造商" width="100">
        <template slot-scope="scope">
          {{ scope.row.manufacturer || 'N/A' }}
        </template>
      </el-table-column>
      
      <el-table-column prop="registerTime" label="注册时间" width="160">
        <template slot-scope="scope">
          {{ formatTime(scope.row.registerTime) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="lastKeepaliveTime" label="最后保活" width="160">
        <template slot-scope="scope">
          {{ formatTime(scope.row.lastKeepaliveTime) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="expires" label="过期时间" width="100" align="center">
        <template slot-scope="scope">
          {{ scope.row.expires }}秒
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="280" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="primary" @click="showDeviceDetails(scope.row)">详情</el-button>
          <el-button size="mini" type="info" @click="queryDeviceInfo(scope.row)">查询</el-button>
          <el-button size="mini" type="success" @click="showControlDialog(scope.row)">控制</el-button>
          <el-button 
            size="mini" 
            type="danger" 
            @click="forceOffline(scope.row)"
            :disabled="scope.row.status !== 'ONLINE'">
            离线
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total">
      </el-pagination>
    </div>

    <!-- 设备详情对话框 -->
    <el-dialog title="设备详情" :visible.sync="detailDialogVisible" width="800px">
      <div v-if="selectedDevice" class="device-details">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="设备ID">{{ selectedDevice.deviceId }}</el-descriptions-item>
          <el-descriptions-item label="设备名称">{{ selectedDevice.deviceName || 'N/A' }}</el-descriptions-item>
          <el-descriptions-item label="IP地址">{{ selectedDevice.ip }}</el-descriptions-item>
          <el-descriptions-item label="端口">{{ selectedDevice.port }}</el-descriptions-item>
          <el-descriptions-item label="传输协议">{{ selectedDevice.transport }}</el-descriptions-item>
          <el-descriptions-item label="设备状态">
            <el-tag :type="selectedDevice.status === 'ONLINE' ? 'success' : 'danger'">
              {{ selectedDevice.status === 'ONLINE' ? '在线' : '离线' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="设备类型">{{ getDeviceTypeText(selectedDevice.deviceType) }}</el-descriptions-item>
          <el-descriptions-item label="制造商">{{ selectedDevice.manufacturer || 'N/A' }}</el-descriptions-item>
          <el-descriptions-item label="设备型号">{{ selectedDevice.model || 'N/A' }}</el-descriptions-item>
          <el-descriptions-item label="固件版本">{{ selectedDevice.firmware || 'N/A' }}</el-descriptions-item>
          <el-descriptions-item label="注册时间">{{ formatTime(selectedDevice.registerTime) }}</el-descriptions-item>
          <el-descriptions-item label="最后保活">{{ formatTime(selectedDevice.lastKeepaliveTime) }}</el-descriptions-item>
          <el-descriptions-item label="过期时间">{{ selectedDevice.expires }}秒</el-descriptions-item>
          <el-descriptions-item label="用户代理">{{ selectedDevice.userAgent || 'N/A' }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="device-actions" style="margin-top: 20px;">
          <el-button type="primary" @click="queryDeviceInfo(selectedDevice)">查询设备信息</el-button>
          <el-button type="success" @click="queryDeviceCatalog(selectedDevice)">查询目录</el-button>
          <el-button type="info" @click="queryDeviceStatus(selectedDevice)">查询状态</el-button>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 设备控制对话框 -->
    <el-dialog title="设备控制" :visible.sync="controlDialogVisible" width="500px">
      <el-form :model="controlForm" label-width="100px">
        <el-form-item label="控制类型">
          <el-select v-model="controlForm.controlType" placeholder="请选择控制类型">
            <el-option label="PTZ控制" value="PTZ"></el-option>
            <el-option label="录像控制" value="Record"></el-option>
            <el-option label="设备重启" value="Reboot"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="controlForm.controlType === 'PTZ'" label="PTZ命令">
          <el-select v-model="controlForm.ptzCommand" placeholder="请选择PTZ命令">
            <el-option label="上" value="up"></el-option>
            <el-option label="下" value="down"></el-option>
            <el-option label="左" value="left"></el-option>
            <el-option label="右" value="right"></el-option>
            <el-option label="放大" value="zoom_in"></el-option>
            <el-option label="缩小" value="zoom_out"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="controlForm.controlType === 'PTZ'" label="速度">
          <el-slider v-model="controlForm.speed" :min="1" :max="10"></el-slider>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="controlDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="sendControlCommand">发送命令</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  getSipDevices, 
  getSipDeviceDetails, 
  forceDeviceOffline,
  queryDevice,
  controlDevice,
  exportDevices 
} from '@/api/sip/device'

export default {
  name: 'SipDevices',
  data() {
    return {
      loading: false,
      deviceList: [],
      selectedDevice: null,
      searchForm: {
        deviceId: '',
        deviceName: '',
        ip: '',
        status: ''
      },
      controlForm: {
        controlType: '',
        ptzCommand: '',
        speed: 5
      },
      pagination: {
        page: 1,
        size: 20,
        total: 0
      },
      detailDialogVisible: false,
      controlDialogVisible: false
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    async loadData() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.page,
          size: this.pagination.size,
          ...this.searchForm
        }
        const response = await getSipDevices(params)
        this.deviceList = response.data.data
        this.pagination.total = response.data.total
      } catch (error) {
        this.$message.error('获取设备列表失败: ' + error.message)
      } finally {
        this.loading = false
      }
    },
    
    handleSearch() {
      this.pagination.page = 1
      this.loadData()
    },
    
    resetSearch() {
      this.searchForm = {
        deviceId: '',
        deviceName: '',
        ip: '',
        status: ''
      }
      this.handleSearch()
    },
    
    refreshData() {
      this.loadData()
    },
    
    handleSizeChange(val) {
      this.pagination.size = val
      this.loadData()
    },
    
    handleCurrentChange(val) {
      this.pagination.page = val
      this.loadData()
    },
    
    async showDeviceDetails(device) {
      try {
        const response = await getSipDeviceDetails(device.deviceId)
        this.selectedDevice = response.data
        this.detailDialogVisible = true
      } catch (error) {
        this.$message.error('获取设备详情失败: ' + error.message)
      }
    },
    
    showControlDialog(device) {
      this.selectedDevice = device
      this.controlForm = {
        controlType: '',
        ptzCommand: '',
        speed: 5
      }
      this.controlDialogVisible = true
    },
    
    async forceOffline(device) {
      try {
        await this.$confirm(`确定要强制设备 ${device.deviceId} 离线吗？`, '确认操作', {
          type: 'warning'
        })
        
        await forceDeviceOffline(device.deviceId, '管理员强制离线')
        this.$message.success('设备已强制离线')
        this.loadData()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('强制离线失败: ' + error.message)
        }
      }
    },
    
    async queryDeviceInfo(device) {
      try {
        await queryDevice(device.deviceId, 'DeviceInfo')
        this.$message.success('设备信息查询命令已发送')
      } catch (error) {
        this.$message.error('发送查询命令失败: ' + error.message)
      }
    },
    
    async queryDeviceCatalog(device) {
      try {
        await queryDevice(device.deviceId, 'Catalog')
        this.$message.success('设备目录查询命令已发送')
      } catch (error) {
        this.$message.error('发送查询命令失败: ' + error.message)
      }
    },
    
    async queryDeviceStatus(device) {
      try {
        await queryDevice(device.deviceId, 'DeviceStatus')
        this.$message.success('设备状态查询命令已发送')
      } catch (error) {
        this.$message.error('发送查询命令失败: ' + error.message)
      }
    },
    
    async queryAllDevices() {
      try {
        const onlineDevices = this.deviceList.filter(device => device.status === 'ONLINE')
        for (const device of onlineDevices) {
          await queryDevice(device.deviceId, 'Catalog')
        }
        this.$message.success(`已向 ${onlineDevices.length} 个在线设备发送目录查询命令`)
      } catch (error) {
        this.$message.error('批量查询失败: ' + error.message)
      }
    },
    
    async sendControlCommand() {
      try {
        const parameters = {}
        if (this.controlForm.controlType === 'PTZ') {
          parameters.command = this.controlForm.ptzCommand
          parameters.speed = this.controlForm.speed
        }
        
        await controlDevice(this.selectedDevice.deviceId, {
          controlType: this.controlForm.controlType,
          parameters
        })
        
        this.$message.success('控制命令已发送')
        this.controlDialogVisible = false
      } catch (error) {
        this.$message.error('发送控制命令失败: ' + error.message)
      }
    },
    
    async exportData() {
      try {
        const params = { ...this.searchForm }
        await exportDevices(params)
        this.$message.success('导出成功')
      } catch (error) {
        this.$message.error('导出失败: ' + error.message)
      }
    },
    
    getDeviceTypeText(type) {
      const typeMap = {
        'CAMERA': '摄像头',
        'NVR': '网络录像机',
        'DVR': '数字录像机',
        'PLATFORM': '平台',
        'ALARM': '报警设备',
        'OTHER': '其他'
      }
      return typeMap[type] || type
    },
    
    formatTime(timestamp) {
      if (!timestamp) return 'N/A'
      return new Date(timestamp).toLocaleString()
    }
  }
}
</script>

<style lang="scss" scoped>
.sip-devices {
  padding: 20px;
  
  .search-bar {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    
    .search-form {
      flex: 1;
    }
    
    .action-buttons {
      margin-left: 20px;
    }
  }
  
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
  
  .device-details {
    margin-bottom: 20px;
  }
  
  .device-actions {
    text-align: center;
  }
}
</style>
