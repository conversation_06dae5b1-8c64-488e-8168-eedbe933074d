import request from '@/utils/request'

// 获取可用的EMQX客户端列表
export function getAvailableClients() {
  return request({
    url: '/emqx/topic/sync/clients',
    method: 'get'
  })
}

// 为主题配置绑定客户端
export function bindClientToTopic(topicConfigId, clientId) {
  return request({
    url: '/emqx/topic/sync/bindClient',
    method: 'post',
    params: {
      topicConfigId,
      clientId
    }
  })
}

// 解除主题配置的客户端绑定
export function unbindClientFromTopic(topicConfigId, clientId) {
  return request({
    url: '/emqx/topic/sync/unbindClient',
    method: 'post',
    params: {
      topicConfigId,
      clientId
    }
  })
}

// 为主题配置订阅到指定客户端
export function subscribeTopicToClient(topicConfigId, clientId, topic, qos = 1) {
  return request({
    url: '/emqx/topic/sync/subscribeToClient',
    method: 'post',
    params: {
      topicConfigId,
      clientId,
      topic,
      qos
    }
  })
}

// 取消主题配置的客户端订阅
export function unsubscribeTopicFromClient(topicConfigId, clientId, topic) {
  return request({
    url: '/emqx/topic/sync/unsubscribeFromClient',
    method: 'post',
    params: {
      topicConfigId,
      clientId,
      topic
    }
  })
}

// 批量订阅主题到客户端
export function batchSubscribeTopicsToClient(topicConfigIds, clientId) {
  return request({
    url: '/emqx/topic/sync/batchSubscribe',
    method: 'post',
    data: {
      topicConfigIds,
      clientId
    }
  })
}

// 获取主题配置的完整状态
export function getTopicConfigFullStatus(topicConfigId) {
  return request({
    url: '/emqx/topic/sync/topicStatus',
    method: 'get',
    params: {
      topicConfigId
    }
  })
}

// 推荐合适的客户端
export function recommendClients(topic, deviceType) {
  return request({
    url: '/emqx/topic/sync/recommendClients',
    method: 'get',
    params: {
      topic,
      deviceType
    }
  })
}

// 同步主题配置状态到EMQX
export function syncTopicConfigStatus(topicConfigId) {
  return request({
    url: '/emqx/topic/sync/syncTopicStatus',
    method: 'post',
    params: {
      topicConfigId
    }
  })
}

// 获取或创建系统客户端
export function getOrCreateSystemClient() {
  return request({
    url: '/emqx/topic/sync/systemClient',
    method: 'get'
  })
}
