import request from '@/utils/request'

// 查询盘点明细列表
export function listInventoryDetail(query) {
  return request({
    url: '/inout/inventory-detail/list',
    method: 'get',
    params: query
  })
}

// 查询盘点明细详细
export function getInventoryDetail(id) {
  return request({
    url: '/inout/inventory-detail/' + id,
    method: 'get'
  })
}

// 新增盘点明细
export function addInventoryDetail(data) {
  return request({
    url: '/inout/inventory-detail',
    method: 'post',
    data: data
  })
}

// 修改盘点明细
export function updateInventoryDetail(data) {
  return request({
    url: '/inout/inventory-detail',
    method: 'put',
    data: data
  })
}

// 删除盘点明细
export function delInventoryDetail(id) {
  return request({
    url: '/inout/inventory-detail/' + id,
    method: 'delete'
  })
}