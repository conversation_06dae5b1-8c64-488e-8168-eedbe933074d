<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 已处理列表 -->
      <el-col :span="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>已处理列表</span>
          </div>
          <div class="filter-container">
            <el-input v-model="queryParams.title" placeholder="审批标题" clearable style="width: 200px" class="filter-item" @keyup.enter.native="handleQuery" />
            <el-select v-model="queryParams.businessType" placeholder="业务类型" clearable class="filter-item" style="width: 200px">
              <el-option v-for="item in businessTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-select v-model="queryParams.workflowId" placeholder="流程名称" clearable class="filter-item" style="width: 200px">
              <el-option v-for="item in workflowOptions" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
            <el-select v-model="queryParams.approveResult" placeholder="审批结果" clearable class="filter-item" style="width: 200px">
              <el-option label="已通过" value="1" />
              <el-option label="已驳回" value="0" />
            </el-select>
            <el-button type="primary" icon="el-icon-search" class="filter-item" @click="handleQuery">搜索</el-button>
            <el-button type="default" icon="el-icon-refresh" class="filter-item" @click="resetQuery">重置</el-button>
          </div>

          <el-table v-loading="loading" :data="doneList" border :row-key="getRowKey">
            <el-table-column label="序号" type="index" width="50" align="center" />
            <el-table-column label="审批标题" prop="title" :show-overflow-tooltip="true" />
            <el-table-column label="流程名称" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                {{ scope.row.workflowName || scope.row.workflow_name || '-' }}
              </template>
            </el-table-column>
            <el-table-column label="业务类型" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <el-tag v-if="(scope.row.businessType || scope.row.business_type) === 'MATERIAL_BILL'" type="primary">物料清单</el-tag>
                <el-tag v-else-if="(scope.row.businessType || scope.row.business_type) === 'MATERIAL_APPROVAL'" type="success">物料审批</el-tag>
                <el-tag v-else-if="(scope.row.businessType || scope.row.business_type) === 'MATERIAL_APPLY'" type="warning">物料申请</el-tag>
                <span v-else>{{ scope.row.businessTypeName || scope.row.businessType || scope.row.business_type || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="申请人" align="center" width="100">
              <template slot-scope="scope">
                {{ scope.row.applicantName || scope.row.apply_user_name || '-' }}
              </template>
            </el-table-column>
            <el-table-column label="审批节点" align="center" width="100">
              <template slot-scope="scope">
                {{ scope.row.approvalNode || scope.row.nodeName || '-' }}
              </template>
            </el-table-column>
            <el-table-column label="审批时间" align="center" width="180">
              <template slot-scope="scope">
                {{ parseTime(scope.row.approvalTime || scope.row.approveTime) }}
              </template>
            </el-table-column>
            <el-table-column label="审批结果" align="center" width="100">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.approvalResult === '通过'" type="success">通过</el-tag>
                <el-tag v-else-if="scope.row.approvalResult === '驳回'" type="danger">驳回</el-tag>
                <el-tag v-else type="info">{{ scope.row.approvalResult || '未知' }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="审批意见" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                {{ scope.row.approvalOpinion || scope.row.comment || '-' }}
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="100" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button type="text" icon="el-icon-view" @click="handleView(scope.row)" v-hasPermi="['approval:done:query']">查看</el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { listDoneApproval, getWorkflowOptions, getBusinessTypes } from "@/api/approval/workflow";
import { parseTime } from '@/utils/ruiyun';

export default {
  name: "DoneApproval",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 已处理列表
      doneList: [],
      // 流程类型选项
      workflowOptions: [],
      // 业务类型选项
      businessTypeOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: undefined,
        businessType: undefined,
        workflowId: undefined,
        approveResult: undefined
      }
    };
  },
  created() {
    this.getList();
    this.getWorkflowOptions();
    this.getBusinessTypeOptions();
  },
  methods: {
    parseTime,
    /** 获取表格行的唯一键 */
    getRowKey(row) {
      return row.instanceId || row.instance_id || row.id || Math.random().toString(36).substr(2, 9);
    },
    /** 查询已处理列表 */
    getList() {
      this.loading = true;
      listDoneApproval(this.queryParams).then(response => {
        // 确保数据唯一性，使用instanceId作为唯一标识
        const uniqueMap = new Map();
        if (response.rows && Array.isArray(response.rows)) {
          response.rows.forEach(item => {
            const key = item.instanceId || item.instance_id || item.id;
            if (key && !uniqueMap.has(key)) {
              uniqueMap.set(key, item);
            }
          });
          this.doneList = Array.from(uniqueMap.values());
        } else {
          this.doneList = [];
        }
        this.total = response.total || 0;
        this.loading = false;
      }).catch(error => {
        console.error('获取已处理列表失败:', error);
        this.doneList = [];
        this.total = 0;
        this.loading = false;
      });
    },
    /** 获取流程类型选项 */
    getWorkflowOptions() {
      getWorkflowOptions().then(response => {
        if (response.code === 200 && response.data) {
          // 使用Map对象确保每个工作流只出现一次
          const workflowMap = new Map();
          response.data.forEach(item => {
            if (!workflowMap.has(item.workflowId)) {
              workflowMap.set(item.workflowId, {
                id: item.workflowId,
                name: item.workflowName
              });
            }
          });

          // 将Map转换回数组
          this.workflowOptions = Array.from(workflowMap.values());
        }
      });
    },
    /** 获取业务类型选项 */
    getBusinessTypeOptions() {
      getBusinessTypes().then(response => {
        if (response.code === 200) {
          // 使用Map对象确保每个业务类型只出现一次
          const businessTypeMap = new Map();
          response.data.forEach(item => {
            if (!businessTypeMap.has(item.dictValue)) {
              businessTypeMap.set(item.dictValue, {
                value: item.dictValue,
                label: item.dictLabel
              });
            }
          });

          // 将Map转换回数组
          this.businessTypeOptions = Array.from(businessTypeMap.values());
        }
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        title: undefined,
        businessType: undefined,
        workflowId: undefined,
        approveResult: undefined
      };
      this.handleQuery();
    },
    /** 查看按钮操作 */
    handleView(row) {
      // 兼容不同的字段名：instanceId（驼峰）或 instance_id（下划线）
      const instanceId = row.instanceId || row.instance_id;

      if (!row || !instanceId) {
        this.$message.error("无效的审批实例ID");
        return;
      }
      // 使用路径参数而不是查询参数
      this.$router.push({ path: '/approval/detail/' + instanceId });
    }
  }
};
</script> 