<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="设备ID" prop="deviceId">
        <el-input
          v-model="queryParams.deviceId"
          placeholder="请输入设备ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户ID" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入用户ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="访问类型" prop="accessType">
        <el-select v-model="queryParams.accessType" placeholder="请选择访问类型" clearable>
          <el-option label="进入" value="entry" />
          <el-option label="离开" value="exit" />
        </el-select>
      </el-form-item>
      <el-form-item label="时间范围">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['dwms:access:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['dwms:access:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['dwms:access:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-view"
          size="mini"
          @click="handleRealtime"
        >实时监控</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 实时监控面板 -->
    <el-card v-if="showRealtime" class="mb8">
      <div slot="header">
        <span>实时门禁监控</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="showRealtime = false">关闭</el-button>
      </div>
      <el-row :gutter="20">
        <el-col :span="8" v-for="device in realtimeDevices" :key="device.deviceId">
          <el-card class="device-card">
            <div class="device-info">
              <h4>{{ device.deviceId }}</h4>
              <div class="device-status" :class="device.status">
                {{ device.status === 'online' ? '在线' : '离线' }}
              </div>
              <div class="last-access">
                最后访问: {{ device.lastAccess || '无' }}
              </div>
              <div class="access-count">
                今日访问: {{ device.todayCount }} 次
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>

    <el-table v-loading="loading" :data="accessList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="记录ID" align="center" prop="id" />
      <el-table-column label="设备ID" align="center" prop="deviceId" />
      <el-table-column label="用户ID" align="center" prop="userId" />
      <el-table-column label="用户名称" align="center" prop="userName" />
      <el-table-column label="访问类型" align="center" prop="accessType">
        <template slot-scope="scope">
          <el-tag :type="scope.row.accessType === 'entry' ? 'success' : 'warning'">
            {{ scope.row.accessType === 'entry' ? '进入' : '离开' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="库房ID" align="center" prop="warehouseId" />
      <el-table-column label="验证方式" align="center" prop="authMethod">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.dwms_auth_method" :value="scope.row.authMethod"/>
        </template>
      </el-table-column>
      <el-table-column label="访问状态" align="center" prop="accessResult">
        <template slot-scope="scope">
          <el-tag :type="scope.row.accessResult === 'success' ? 'success' : 'danger'">
            {{ scope.row.accessResult === 'success' ? '成功' : '失败' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="访问时间" align="center" prop="accessTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.accessTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['dwms:access:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加门禁记录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="设备ID" prop="deviceId">
          <el-input v-model="form.deviceId" placeholder="请输入设备ID" />
        </el-form-item>
        <el-form-item label="用户ID" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户ID" />
        </el-form-item>
        <el-form-item label="用户名称" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入用户名称" />
        </el-form-item>
        <el-form-item label="访问类型" prop="accessType">
          <el-select v-model="form.accessType" placeholder="请选择访问类型">
            <el-option label="进入" value="entry" />
            <el-option label="离开" value="exit" />
          </el-select>
        </el-form-item>
        <el-form-item label="库房ID" prop="warehouseId">
          <el-input-number v-model="form.warehouseId" placeholder="请输入库房ID" />
        </el-form-item>
        <el-form-item label="验证方式" prop="authMethod">
          <el-select v-model="form.authMethod" placeholder="请选择验证方式">
            <el-option label="刷卡" value="card" />
            <el-option label="指纹" value="fingerprint" />
            <el-option label="人脸识别" value="face" />
            <el-option label="密码" value="password" />
          </el-select>
        </el-form-item>
        <el-form-item label="访问结果" prop="accessResult">
          <el-select v-model="form.accessResult" placeholder="请选择访问结果">
            <el-option label="成功" value="success" />
            <el-option label="失败" value="failed" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog title="门禁记录详情" :visible.sync="viewOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="记录ID">{{ viewData.id }}</el-descriptions-item>
        <el-descriptions-item label="设备ID">{{ viewData.deviceId }}</el-descriptions-item>
        <el-descriptions-item label="用户ID">{{ viewData.userId }}</el-descriptions-item>
        <el-descriptions-item label="用户名称">{{ viewData.userName }}</el-descriptions-item>
        <el-descriptions-item label="访问类型">
          <el-tag :type="viewData.accessType === 'entry' ? 'success' : 'warning'">
            {{ viewData.accessType === 'entry' ? '进入' : '离开' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="库房ID">{{ viewData.warehouseId }}</el-descriptions-item>
        <el-descriptions-item label="验证方式">{{ viewData.authMethod }}</el-descriptions-item>
        <el-descriptions-item label="访问结果">
          <el-tag :type="viewData.accessResult === 'success' ? 'success' : 'danger'">
            {{ viewData.accessResult === 'success' ? '成功' : '失败' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="访问时间">{{ parseTime(viewData.accessTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(viewData.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
// import { listAccess, getAccess, delAccess, addAccess } from "@/api/dwms/access";

export default {
  name: "Access",
  dicts: ['dwms_auth_method'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 门禁记录表格数据
      accessList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查看详情弹出层
      viewOpen: false,
      // 查看数据
      viewData: {},
      // 显示实时监控
      showRealtime: false,
      // 实时设备数据
      realtimeDevices: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceId: null,
        userId: null,
        accessType: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        deviceId: [
          { required: true, message: "设备ID不能为空", trigger: "blur" }
        ],
        userId: [
          { required: true, message: "用户ID不能为空", trigger: "blur" }
        ],
        accessType: [
          { required: true, message: "访问类型不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询门禁记录列表 */
    getList() {
      this.loading = true;

      // 使用模拟数据
      setTimeout(() => {
        this.accessList = [
          {
            id: 1,
            deviceId: 'AC001',
            userId: 'U001',
            userName: '张三',
            accessType: 'entry',
            warehouseId: 1,
            authMethod: 'card',
            accessResult: 'success',
            accessTime: new Date().toISOString(),
            createTime: new Date().toISOString()
          },
          {
            id: 2,
            deviceId: 'AC001',
            userId: 'U002',
            userName: '李四',
            accessType: 'exit',
            warehouseId: 1,
            authMethod: 'face',
            accessResult: 'success',
            accessTime: new Date(Date.now() - 300000).toISOString(),
            createTime: new Date(Date.now() - 300000).toISOString()
          },
          {
            id: 3,
            deviceId: 'AC002',
            userId: 'U003',
            userName: '王五',
            accessType: 'entry',
            warehouseId: 1,
            authMethod: 'fingerprint',
            accessResult: 'failed',
            accessTime: new Date(Date.now() - 600000).toISOString(),
            createTime: new Date(Date.now() - 600000).toISOString()
          }
        ];
        this.total = this.accessList.length;
        this.loading = false;
      }, 500);

      // 实际API调用（当后端准备好时启用）
      /*
      listAccess(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.accessList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        console.error('获取门禁记录失败:', error);
        this.loading = false;
        this.$message.error('获取门禁记录失败');
      });
      */
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        deviceId: null,
        userId: null,
        userName: null,
        accessType: null,
        warehouseId: null,
        authMethod: null,
        accessResult: "success"
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加门禁记录";
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 模拟新增成功
          this.$message.success("新增成功");
          this.open = false;
          this.getList();

          // 实际API调用（当后端准备好时启用）
          /*
          addAccess(this.form).then(response => {
            this.$message.success("新增成功");
            this.open = false;
            this.getList();
          }).catch(error => {
            this.$message.error("新增失败");
          });
          */
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('是否确认删除门禁记录编号为"' + ids + '"的数据项？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 模拟删除成功
        this.getList();
        this.$message.success("删除成功");

        // 实际API调用（当后端准备好时启用）
        /*
        return delAccess(ids);
        }).then(() => {
          this.getList();
          this.$message.success("删除成功");
        }).catch(error => {
          this.$message.error("删除失败");
        */
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('dwms/access/export', {
        ...this.queryParams
      }, `access_${new Date().getTime()}.xlsx`)
    },
    /** 查看详情 */
    handleView(row) {
      this.viewData = row;
      this.viewOpen = true;
    },
    /** 实时监控 */
    handleRealtime() {
      this.showRealtime = !this.showRealtime;
      if (this.showRealtime) {
        this.loadRealtimeData();
        // 每10秒刷新一次实时数据
        this.realtimeTimer = setInterval(() => {
          this.loadRealtimeData();
        }, 10000);
      } else {
        if (this.realtimeTimer) {
          clearInterval(this.realtimeTimer);
        }
      }
    },
    /** 加载实时数据 */
    loadRealtimeData() {
      // 模拟实时数据，实际应该调用API
      this.realtimeDevices = [
        {
          deviceId: 'AC001',
          status: 'online',
          lastAccess: '张三 - 进入',
          todayCount: Math.floor(Math.random() * 50) + 10
        },
        {
          deviceId: 'AC002',
          status: 'online',
          lastAccess: '李四 - 离开',
          todayCount: Math.floor(Math.random() * 50) + 10
        },
        {
          deviceId: 'AC003',
          status: Math.random() > 0.8 ? 'offline' : 'online',
          lastAccess: '王五 - 进入',
          todayCount: Math.floor(Math.random() * 50) + 10
        }
      ];
    }
  },
  beforeDestroy() {
    if (this.realtimeTimer) {
      clearInterval(this.realtimeTimer);
    }
  }
};
</script>

<style scoped>
.device-card {
  margin-bottom: 10px;
}

.device-info {
  text-align: center;
}

.device-status.online {
  color: #67C23A;
  font-weight: bold;
}

.device-status.offline {
  color: #F56C6C;
  font-weight: bold;
}

.last-access {
  font-size: 12px;
  color: #606266;
  margin: 5px 0;
}

.access-count {
  font-size: 14px;
  color: #409EFF;
  font-weight: bold;
}
</style>
