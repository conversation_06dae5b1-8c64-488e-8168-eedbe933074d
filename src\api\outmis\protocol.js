import request from '@/utils/request'

// 查询出库防错协议管理列表
export function listProtocol(query) {
  return request({
    url: '/outmis/protocol/list',
    method: 'get',
    params: query
  })
}

// 查询出库防错协议管理详细
export function getProtocol(protocolId) {
  return request({
    url: '/outmis/protocol/' + protocolId,
    method: 'get'
  })
}

// 新增出库防错协议管理
export function addProtocol(data) {
  return request({
    url: '/outmis/protocol',
    method: 'post',
    data: data
  })
}

// 修改出库防错协议管理
export function updateProtocol(data) {
  return request({
    url: '/outmis/protocol',
    method: 'put',
    data: data
  })
}

// 删除出库防错协议管理
export function delProtocol(protocolId) {
  return request({
    url: '/outmis/protocol/' + protocolId,
    method: 'delete'
  })
}

// 检查协议名称是否唯一
export function checkProtocolNameUnique(data) {
  return request({
    url: '/outmis/protocol/checkProtocolNameUnique',
    method: 'post',
    data: data
  })
}

// 检查端口是否被占用
export function checkPortOccupied(data) {
  return request({
    url: '/outmis/protocol/checkPortOccupied',
    method: 'post',
    data: data
  })
}

// 更新协议状态
export function updateProtocolStatus(protocolId, status) {
  return request({
    url: '/outmis/protocol/status',
    method: 'put',
    params: {
      protocolId: protocolId,
      status: status
    }
  })
}

// 测试协议解析
export function testProtocolParse(protocolId, testData) {
  return request({
    url: '/outmis/protocol/testParse',
    method: 'post',
    params: {
      protocolId: protocolId,
      testData: testData
    }
  })
}

// 测试协议编码
export function testProtocolEncode(protocolId, testData) {
  return request({
    url: '/outmis/protocol/testEncode',
    method: 'post',
    params: {
      protocolId: protocolId,
      testData: testData
    }
  })
}

// 获取协议统计信息
export function getProtocolStatistics() {
  return request({
    url: '/outmis/protocol/statistics',
    method: 'get'
  })
}

// 启动协议服务器
export function startProtocolServer(protocolId) {
  return request({
    url: '/outmis/protocol/start/' + protocolId,
    method: 'post'
  })
}

// 停止协议服务器
export function stopProtocolServer(protocolId) {
  return request({
    url: '/outmis/protocol/stop/' + protocolId,
    method: 'post'
  })
}

// 重启协议服务器
export function restartProtocolServer(protocolId) {
  return request({
    url: '/outmis/protocol/restart/' + protocolId,
    method: 'post'
  })
}

// 获取协议服务器状态
export function getProtocolServerStatus(protocolId) {
  return request({
    url: '/outmis/protocol/serverStatus/' + protocolId,
    method: 'get'
  })
}

// 查询启用的协议列表
export function getEnabledProtocols() {
  return request({
    url: '/outmis/protocol/enabled',
    method: 'get'
  })
}

// 根据协议名称查询协议
export function getProtocolByName(protocolName) {
  return request({
    url: '/outmis/protocol/name/' + protocolName,
    method: 'get'
  })
}
