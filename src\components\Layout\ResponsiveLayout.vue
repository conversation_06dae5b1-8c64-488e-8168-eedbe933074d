<template>
  <div class="responsive-layout" :class="layoutClasses">
    <!-- 移动端头部 -->
    <div class="mobile-header" v-if="isMobile">
      <div class="mobile-header-left">
        <el-button
          type="text"
          icon="el-icon-s-unfold"
          @click="toggleMobileSidebar"
          class="mobile-menu-btn"
        />
        <span class="mobile-title">{{ title }}</span>
      </div>
      <div class="mobile-header-right">
        <slot name="mobile-header-actions">
          <el-button type="text" icon="el-icon-search" @click="showMobileSearch = true" />
          <el-button type="text" icon="el-icon-bell" @click="showNotifications = true" />
        </slot>
      </div>
    </div>

    <!-- 侧边栏 -->
    <div class="layout-sidebar" :class="sidebarClasses" v-if="showSidebar">
      <div class="sidebar-header" v-if="!isMobile">
        <slot name="sidebar-header">
          <div class="logo">
            <img src="@/assets/logo.png" alt="Logo" />
            <span class="logo-text">瑞云WMS</span>
          </div>
        </slot>
      </div>
      
      <div class="sidebar-content">
        <slot name="sidebar-content">
          <!-- 默认菜单内容 -->
        </slot>
      </div>
      
      <div class="sidebar-footer" v-if="!isMobile">
        <slot name="sidebar-footer">
          <el-button type="text" @click="toggleSidebarCollapse">
            <i :class="sidebarCollapsed ? 'el-icon-s-unfold' : 'el-icon-s-fold'"></i>
          </el-button>
        </slot>
      </div>
    </div>

    <!-- 移动端侧边栏遮罩 -->
    <div
      class="mobile-sidebar-overlay"
      v-if="isMobile && mobileSidebarVisible"
      @click="closeMobileSidebar"
    ></div>

    <!-- 主内容区域 -->
    <div class="layout-main" :class="mainClasses">
      <!-- 桌面端头部 -->
      <div class="desktop-header" v-if="!isMobile && showHeader">
        <div class="header-left">
          <slot name="header-left">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item
                v-for="item in breadcrumbs"
                :key="item.path"
                :to="item.path"
              >
                {{ item.name }}
              </el-breadcrumb-item>
            </el-breadcrumb>
          </slot>
        </div>
        
        <div class="header-right">
          <slot name="header-right">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索..."
              prefix-icon="el-icon-search"
              size="small"
              style="width: 200px; margin-right: 15px;"
            />
            <el-badge :value="notificationCount" class="notification-badge">
              <el-button type="text" icon="el-icon-bell" @click="showNotifications = true" />
            </el-badge>
          </slot>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="layout-content" :class="contentClasses">
        <slot name="content">
          <router-view />
        </slot>
      </div>

      <!-- 底部 -->
      <div class="layout-footer" v-if="showFooter">
        <slot name="footer">
          <div class="footer-content">
            <span>&copy; 2025 瑞云科技. All rights reserved.</span>
          </div>
        </slot>
      </div>
    </div>

    <!-- 移动端搜索弹窗 -->
    <el-dialog
      title="搜索"
      :visible.sync="showMobileSearch"
      width="90%"
      :show-close="false"
      custom-class="mobile-search-dialog"
    >
      <el-input
        v-model="mobileSearchKeyword"
        placeholder="请输入搜索关键词"
        prefix-icon="el-icon-search"
        size="large"
        @keyup.enter.native="handleMobileSearch"
      />
      <div slot="footer">
        <el-button @click="showMobileSearch = false">取消</el-button>
        <el-button type="primary" @click="handleMobileSearch">搜索</el-button>
      </div>
    </el-dialog>

    <!-- 通知弹窗 -->
    <el-drawer
      title="通知"
      :visible.sync="showNotifications"
      :direction="isMobile ? 'btt' : 'rtl'"
      :size="isMobile ? '70%' : '400px'"
    >
      <div class="notifications-content">
        <slot name="notifications">
          <div class="notification-item" v-for="item in notifications" :key="item.id">
            <div class="notification-icon">
              <i :class="getNotificationIcon(item.type)"></i>
            </div>
            <div class="notification-content">
              <div class="notification-title">{{ item.title }}</div>
              <div class="notification-message">{{ item.message }}</div>
              <div class="notification-time">{{ item.time }}</div>
            </div>
          </div>
        </slot>
      </div>
    </el-drawer>

    <!-- 移动端底部导航 -->
    <div class="mobile-bottom-nav" v-if="isMobile && showBottomNav">
      <slot name="bottom-nav">
        <div
          class="nav-item"
          v-for="item in bottomNavItems"
          :key="item.path"
          :class="{ active: $route.path === item.path }"
          @click="$router.push(item.path)"
        >
          <i :class="item.icon"></i>
          <span>{{ item.name }}</span>
        </div>
      </slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ResponsiveLayout',
  
  props: {
    // 页面标题
    title: {
      type: String,
      default: '瑞云WMS'
    },
    // 是否显示侧边栏
    showSidebar: {
      type: Boolean,
      default: true
    },
    // 是否显示头部
    showHeader: {
      type: Boolean,
      default: true
    },
    // 是否显示底部
    showFooter: {
      type: Boolean,
      default: true
    },
    // 是否显示移动端底部导航
    showBottomNav: {
      type: Boolean,
      default: true
    },
    // 侧边栏宽度
    sidebarWidth: {
      type: String,
      default: '250px'
    },
    // 侧边栏折叠宽度
    sidebarCollapsedWidth: {
      type: String,
      default: '64px'
    },
    // 移动端断点
    mobileBreakpoint: {
      type: Number,
      default: 768
    },
    // 面包屑导航
    breadcrumbs: {
      type: Array,
      default: () => []
    },
    // 底部导航项
    bottomNavItems: {
      type: Array,
      default: () => [
        { path: '/dashboard', name: '首页', icon: 'el-icon-s-home' },
        { path: '/warehouse', name: '仓库', icon: 'el-icon-s-shop' },
        { path: '/material', name: '物料', icon: 'el-icon-s-goods' },
        { path: '/monitor', name: '监控', icon: 'el-icon-s-data' },
        { path: '/profile', name: '我的', icon: 'el-icon-s-custom' }
      ]
    }
  },
  
  data() {
    return {
      // 屏幕宽度
      screenWidth: window.innerWidth,
      // 侧边栏折叠状态
      sidebarCollapsed: false,
      // 移动端侧边栏显示状态
      mobileSidebarVisible: false,
      // 搜索相关
      searchKeyword: '',
      showMobileSearch: false,
      mobileSearchKeyword: '',
      // 通知相关
      showNotifications: false,
      notificationCount: 3,
      notifications: [
        {
          id: 1,
          type: 'info',
          title: '系统通知',
          message: '系统将于今晚22:00进行维护',
          time: '2小时前'
        },
        {
          id: 2,
          type: 'warning',
          title: '设备告警',
          message: 'RFID设备001连接异常',
          time: '1小时前'
        },
        {
          id: 3,
          type: 'success',
          title: '任务完成',
          message: '库存盘点任务已完成',
          time: '30分钟前'
        }
      ]
    }
  },
  
  computed: {
    // 是否为移动端
    isMobile() {
      return this.screenWidth < this.mobileBreakpoint
    },
    
    // 布局样式类
    layoutClasses() {
      return {
        'is-mobile': this.isMobile,
        'sidebar-collapsed': this.sidebarCollapsed && !this.isMobile,
        'mobile-sidebar-visible': this.mobileSidebarVisible
      }
    },
    
    // 侧边栏样式类
    sidebarClasses() {
      return {
        'is-collapsed': this.sidebarCollapsed && !this.isMobile,
        'is-mobile': this.isMobile,
        'is-visible': !this.isMobile || this.mobileSidebarVisible
      }
    },
    
    // 主内容区样式类
    mainClasses() {
      return {
        'sidebar-collapsed': this.sidebarCollapsed && !this.isMobile,
        'no-sidebar': !this.showSidebar,
        'is-mobile': this.isMobile
      }
    },
    
    // 内容区样式类
    contentClasses() {
      return {
        'has-footer': this.showFooter,
        'has-bottom-nav': this.isMobile && this.showBottomNav
      }
    }
  },
  
  mounted() {
    this.initResponsive()
  },
  
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  
  methods: {
    // 初始化响应式
    initResponsive() {
      window.addEventListener('resize', this.handleResize)
      this.handleResize()
    },
    
    // 处理窗口大小变化
    handleResize() {
      this.screenWidth = window.innerWidth
      
      // 移动端自动关闭侧边栏
      if (this.isMobile) {
        this.mobileSidebarVisible = false
      }
    },
    
    // 切换侧边栏折叠状态
    toggleSidebarCollapse() {
      this.sidebarCollapsed = !this.sidebarCollapsed
      this.$emit('sidebar-collapse-change', this.sidebarCollapsed)
    },
    
    // 切换移动端侧边栏
    toggleMobileSidebar() {
      this.mobileSidebarVisible = !this.mobileSidebarVisible
    },
    
    // 关闭移动端侧边栏
    closeMobileSidebar() {
      this.mobileSidebarVisible = false
    },
    
    // 处理移动端搜索
    handleMobileSearch() {
      this.$emit('search', this.mobileSearchKeyword)
      this.showMobileSearch = false
      this.mobileSearchKeyword = ''
    },
    
    // 获取通知图标
    getNotificationIcon(type) {
      const iconMap = {
        info: 'el-icon-info',
        warning: 'el-icon-warning',
        success: 'el-icon-success',
        error: 'el-icon-error'
      }
      return iconMap[type] || 'el-icon-info'
    },
    
    // 设置侧边栏状态
    setSidebarCollapsed(collapsed) {
      this.sidebarCollapsed = collapsed
    },
    
    // 设置移动端侧边栏状态
    setMobileSidebarVisible(visible) {
      this.mobileSidebarVisible = visible
    }
  }
}
</script>

<style scoped>
.responsive-layout {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

/* 移动端头部 */
.mobile-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 50px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  z-index: 1000;
}

.mobile-header-left {
  display: flex;
  align-items: center;
}

.mobile-menu-btn {
  margin-right: 10px;
  font-size: 18px;
}

.mobile-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.mobile-header-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 侧边栏 */
.layout-sidebar {
  width: v-bind(sidebarWidth);
  background: #304156;
  color: #fff;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  z-index: 999;
}

.layout-sidebar.is-collapsed {
  width: v-bind(sidebarCollapsedWidth);
}

.layout-sidebar.is-mobile {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  width: 280px;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.layout-sidebar.is-mobile.is-visible {
  transform: translateX(0);
}

.sidebar-header {
  height: 60px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  border-bottom: 1px solid #434a50;
}

.logo {
  display: flex;
  align-items: center;
}

.logo img {
  width: 32px;
  height: 32px;
  margin-right: 10px;
}

.logo-text {
  font-size: 18px;
  font-weight: bold;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
}

.sidebar-footer {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-top: 1px solid #434a50;
}

/* 移动端侧边栏遮罩 */
.mobile-sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 998;
}

/* 主内容区 */
.layout-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin-left: 0;
  transition: margin-left 0.3s ease;
}

.layout-main.is-mobile {
  margin-left: 0;
  padding-top: 50px;
}

.layout-main:not(.no-sidebar):not(.is-mobile) {
  margin-left: v-bind(sidebarWidth);
}

.layout-main.sidebar-collapsed:not(.is-mobile) {
  margin-left: v-bind(sidebarCollapsedWidth);
}

/* 桌面端头部 */
.desktop-header {
  height: 60px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
}

.notification-badge {
  margin-right: 15px;
}

/* 内容区 */
.layout-content {
  flex: 1;
  overflow: auto;
  padding: 20px;
}

.layout-content.has-bottom-nav {
  padding-bottom: 70px;
}

/* 底部 */
.layout-footer {
  height: 50px;
  background: #f5f7fa;
  border-top: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: center;
}

.footer-content {
  color: #909399;
  font-size: 12px;
}

/* 移动端底部导航 */
.mobile-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px;
  background: #fff;
  border-top: 1px solid #e4e7ed;
  display: flex;
  z-index: 1000;
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 12px;
  cursor: pointer;
  transition: color 0.3s ease;
}

.nav-item.active {
  color: #409EFF;
}

.nav-item i {
  font-size: 18px;
  margin-bottom: 2px;
}

/* 通知内容 */
.notifications-content {
  padding: 20px;
}

.notification-item {
  display: flex;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  background: #f5f7fa;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.notification-message {
  color: #606266;
  font-size: 14px;
  margin-bottom: 5px;
}

.notification-time {
  color: #909399;
  font-size: 12px;
}

/* 移动端搜索对话框 */
.mobile-search-dialog {
  border-radius: 8px;
}

/* 响应式断点 */
@media (max-width: 768px) {
  .layout-content {
    padding: 15px;
  }
  
  .desktop-header {
    display: none;
  }
}

@media (max-width: 480px) {
  .layout-content {
    padding: 10px;
  }
  
  .mobile-header {
    padding: 0 10px;
  }
}
</style>
