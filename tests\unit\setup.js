import Vue from 'vue'
import Element<PERSON> from 'element-ui'
import VueRouter from 'vue-router'
import Vuex from 'vuex'

// 配置Vue
Vue.config.productionTip = false

// 使用插件
Vue.use(ElementUI)
Vue.use(VueRouter)
Vue.use(Vuex)

// 全局模拟
global.console = {
  ...console,
  // 在测试中静默某些日志
  warn: jest.fn(),
  error: jest.fn()
}

// 模拟window对象
Object.defineProperty(window, 'location', {
  value: {
    href: 'http://localhost:8080',
    origin: 'http://localhost:8080',
    pathname: '/',
    search: '',
    hash: ''
  },
  writable: true
})

// 模拟localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
}
global.localStorage = localStorageMock

// 模拟sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
}
global.sessionStorage = sessionStorageMock

// 模拟IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
}

// 模拟ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
}

// 模拟matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn()
  }))
})

// 模拟requestAnimationFrame
global.requestAnimationFrame = callback => {
  setTimeout(callback, 0)
}

// 模拟cancelAnimationFrame
global.cancelAnimationFrame = id => {
  clearTimeout(id)
}

// 模拟fetch
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    status: 200,
    json: () => Promise.resolve({}),
    text: () => Promise.resolve('')
  })
)

// Element UI 消息提示模拟
Vue.prototype.$message = {
  success: jest.fn(),
  warning: jest.fn(),
  info: jest.fn(),
  error: jest.fn()
}

Vue.prototype.$confirm = jest.fn(() => Promise.resolve())
Vue.prototype.$prompt = jest.fn(() => Promise.resolve({ value: 'test' }))
Vue.prototype.$notify = jest.fn()
Vue.prototype.$loading = jest.fn(() => ({
  close: jest.fn()
}))

// 路由模拟
Vue.prototype.$router = {
  push: jest.fn(),
  replace: jest.fn(),
  go: jest.fn(),
  back: jest.fn(),
  forward: jest.fn()
}

Vue.prototype.$route = {
  path: '/',
  params: {},
  query: {},
  hash: '',
  fullPath: '/',
  matched: [],
  name: null,
  meta: {}
}

// 测试工具函数
export const createWrapper = (component, options = {}) => {
  const localVue = createLocalVue()
  localVue.use(ElementUI)
  localVue.use(VueRouter)
  localVue.use(Vuex)
  
  const router = new VueRouter({
    routes: []
  })
  
  const store = new Vuex.Store({
    modules: {}
  })
  
  return mount(component, {
    localVue,
    router,
    store,
    ...options
  })
}

// 异步组件测试工具
export const flushPromises = () => {
  return new Promise(resolve => setImmediate(resolve))
}

// 等待DOM更新
export const nextTick = () => {
  return Vue.nextTick()
}

// 模拟API响应
export const mockApiResponse = (data, status = 200) => {
  return Promise.resolve({
    ok: status >= 200 && status < 300,
    status,
    data,
    json: () => Promise.resolve(data),
    text: () => Promise.resolve(JSON.stringify(data))
  })
}

// 模拟API错误
export const mockApiError = (message = 'API Error', status = 500) => {
  return Promise.reject({
    message,
    status,
    response: {
      status,
      data: { message }
    }
  })
}

// 测试数据生成器
export const generateTestData = {
  user: (overrides = {}) => ({
    id: 1,
    username: 'testuser',
    name: '测试用户',
    email: '<EMAIL>',
    role: 'admin',
    ...overrides
  }),
  
  device: (overrides = {}) => ({
    deviceId: 'DEV001',
    deviceName: '测试设备',
    deviceType: 'RFID',
    isOnline: true,
    healthScore: 95,
    ...overrides
  }),
  
  warehouse: (overrides = {}) => ({
    id: 1,
    warehouseName: '测试仓库',
    warehouseCode: 'WH001',
    address: '测试地址',
    ...overrides
  }),
  
  material: (overrides = {}) => ({
    id: 1,
    materialName: '测试物料',
    materialCode: 'MAT001',
    specification: '规格1',
    unit: '个',
    ...overrides
  })
}

// 时间相关的测试工具
export const mockDate = (dateString) => {
  const mockDate = new Date(dateString)
  const originalDate = Date
  
  global.Date = class extends Date {
    constructor(...args) {
      if (args.length === 0) {
        return mockDate
      }
      return new originalDate(...args)
    }
    
    static now() {
      return mockDate.getTime()
    }
  }
  
  return () => {
    global.Date = originalDate
  }
}

// 清理函数
export const cleanup = () => {
  jest.clearAllMocks()
  localStorage.clear()
  sessionStorage.clear()
}
