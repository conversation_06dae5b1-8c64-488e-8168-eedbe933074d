<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="page-title">
        <h2><i class="el-icon-monitor"></i> 系统状态诊断</h2>
        <p>检查EMQX客户端管理系统的各项服务状态</p>
      </div>
      <div class="page-actions">
        <el-button type="primary" icon="el-icon-refresh" @click="runHealthCheck" :loading="checking">
          重新检查
        </el-button>
        <el-button type="info" icon="el-icon-download" @click="downloadReport">
          下载报告
        </el-button>
      </div>
    </div>

    <!-- 总体状态 -->
    <el-card class="overall-status-card">
      <div class="overall-status">
        <div class="status-icon" :class="getOverallStatusClass()">
          <i :class="getOverallStatusIcon()"></i>
        </div>
        <div class="status-info">
          <h3>{{ getOverallStatusText() }}</h3>
          <p>{{ getOverallStatusDescription() }}</p>
          <div class="status-time">
            最后检查时间: {{ formatTime(lastCheckTime) }}
          </div>
        </div>
      </div>
    </el-card>

    <!-- 详细检查结果 -->
    <el-row :gutter="20">
      <el-col :span="8" v-for="(check, name) in healthResults.checks" :key="name">
        <el-card class="check-card" :class="getCheckCardClass(check.status)">
          <div class="check-header">
            <div class="check-icon" :class="getCheckStatusClass(check.status)">
              <i :class="getCheckStatusIcon(check.status)"></i>
            </div>
            <div class="check-title">
              <h4>{{ getCheckName(name) }}</h4>
              <span class="check-status">{{ getStatusText(check.status) }}</span>
            </div>
          </div>
          
          <div class="check-content">
            <p class="check-message">{{ check.message }}</p>
            <div class="check-metrics">
              <div class="metric">
                <span class="metric-label">响应时间:</span>
                <span class="metric-value">{{ check.responseTime }}ms</span>
              </div>
            </div>
          </div>
          
          <div class="check-actions" v-if="check.status !== 'healthy'">
            <el-button size="mini" type="primary" @click="showFixSuggestions(name)">
              查看解决方案
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统信息 -->
    <el-card class="system-info-card">
      <div slot="header">
        <span><i class="el-icon-info"></i> 系统信息</span>
      </div>
      
      <el-descriptions :column="2" border>
        <el-descriptions-item label="前端版本">{{ systemInfo.frontendVersion }}</el-descriptions-item>
        <el-descriptions-item label="API地址">{{ systemInfo.apiUrl }}</el-descriptions-item>
        <el-descriptions-item label="浏览器">{{ systemInfo.browser }}</el-descriptions-item>
        <el-descriptions-item label="操作系统">{{ systemInfo.os }}</el-descriptions-item>
        <el-descriptions-item label="屏幕分辨率">{{ systemInfo.screenResolution }}</el-descriptions-item>
        <el-descriptions-item label="网络状态">{{ systemInfo.networkStatus }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 解决方案对话框 -->
    <el-dialog
      title="解决方案"
      :visible.sync="solutionDialogVisible"
      width="600px"
    >
      <div v-if="currentSolutions" class="solutions-content">
        <h4>{{ currentSolutions.issue }}</h4>
        <ul class="solutions-list">
          <li v-for="(solution, index) in currentSolutions.suggestions" :key="index">
            {{ solution }}
          </li>
        </ul>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="solutionDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="runHealthCheck">重新检查</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { systemHealthCheck } from '@/utils/systemHealthCheck'

export default {
  name: 'SystemStatus',
  data() {
    return {
      checking: false,
      healthResults: {
        overall: 'unknown',
        checks: {},
        timestamp: null
      },
      lastCheckTime: null,
      solutionDialogVisible: false,
      currentSolutions: null,
      
      systemInfo: {
        frontendVersion: '1.0.0',
        apiUrl: process.env.VUE_APP_BASE_API,
        browser: '',
        os: '',
        screenResolution: '',
        networkStatus: 'unknown'
      }
    }
  },
  
  created() {
    this.initSystemInfo()
    this.runHealthCheck()
  },
  
  methods: {
    async runHealthCheck() {
      this.checking = true
      try {
        this.healthResults = await systemHealthCheck.performFullHealthCheck()
        this.lastCheckTime = new Date()
      } catch (error) {
        console.error('健康检查失败:', error)
        this.$message.error('健康检查失败')
      } finally {
        this.checking = false
      }
    },
    
    initSystemInfo() {
      // 获取浏览器信息
      const userAgent = navigator.userAgent
      if (userAgent.includes('Chrome')) {
        this.systemInfo.browser = 'Chrome'
      } else if (userAgent.includes('Firefox')) {
        this.systemInfo.browser = 'Firefox'
      } else if (userAgent.includes('Safari')) {
        this.systemInfo.browser = 'Safari'
      } else {
        this.systemInfo.browser = '未知'
      }
      
      // 获取操作系统信息
      if (userAgent.includes('Windows')) {
        this.systemInfo.os = 'Windows'
      } else if (userAgent.includes('Mac')) {
        this.systemInfo.os = 'macOS'
      } else if (userAgent.includes('Linux')) {
        this.systemInfo.os = 'Linux'
      } else {
        this.systemInfo.os = '未知'
      }
      
      // 获取屏幕分辨率
      this.systemInfo.screenResolution = `${screen.width}x${screen.height}`
      
      // 检查网络状态
      this.systemInfo.networkStatus = navigator.onLine ? '在线' : '离线'
    },
    
    showFixSuggestions(checkName) {
      const suggestions = systemHealthCheck.getFixSuggestions(this.healthResults)
      const checkNameMap = {
        'backend': '后端服务异常',
        'emqx': 'EMQX API异常',
        'network': '网络连接异常'
      }
      
      this.currentSolutions = suggestions.find(s => s.issue === checkNameMap[checkName])
      this.solutionDialogVisible = true
    },
    
    downloadReport() {
      const report = systemHealthCheck.generateHealthReport(this.healthResults)
      const blob = new Blob([report], { type: 'text/plain;charset=utf-8' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `system-health-report-${new Date().toISOString().slice(0, 19)}.txt`
      link.click()
      URL.revokeObjectURL(url)
    },
    
    // 状态相关方法
    getOverallStatusClass() {
      const classMap = {
        'healthy': 'status-healthy',
        'partial': 'status-warning',
        'unhealthy': 'status-error',
        'unknown': 'status-unknown'
      }
      return classMap[this.healthResults.overall] || 'status-unknown'
    },
    
    getOverallStatusIcon() {
      const iconMap = {
        'healthy': 'el-icon-success',
        'partial': 'el-icon-warning',
        'unhealthy': 'el-icon-error',
        'unknown': 'el-icon-question'
      }
      return iconMap[this.healthResults.overall] || 'el-icon-question'
    },
    
    getOverallStatusText() {
      const textMap = {
        'healthy': '系统正常',
        'partial': '部分异常',
        'unhealthy': '系统异常',
        'unknown': '状态未知'
      }
      return textMap[this.healthResults.overall] || '状态未知'
    },
    
    getOverallStatusDescription() {
      const descMap = {
        'healthy': '所有服务运行正常，系统可以正常使用',
        'partial': '部分服务存在问题，可能影响某些功能',
        'unhealthy': '多个服务存在问题，系统功能受到严重影响',
        'unknown': '无法确定系统状态，请重新检查'
      }
      return descMap[this.healthResults.overall] || '无法确定系统状态'
    },
    
    getCheckCardClass(status) {
      const classMap = {
        'healthy': 'card-healthy',
        'unhealthy': 'card-warning',
        'error': 'card-error',
        'unknown': 'card-unknown'
      }
      return classMap[status] || 'card-unknown'
    },
    
    getCheckStatusClass(status) {
      const classMap = {
        'healthy': 'check-healthy',
        'unhealthy': 'check-warning',
        'error': 'check-error',
        'unknown': 'check-unknown'
      }
      return classMap[status] || 'check-unknown'
    },
    
    getCheckStatusIcon(status) {
      const iconMap = {
        'healthy': 'el-icon-success',
        'unhealthy': 'el-icon-warning',
        'error': 'el-icon-error',
        'unknown': 'el-icon-question'
      }
      return iconMap[status] || 'el-icon-question'
    },
    
    getStatusText(status) {
      const textMap = {
        'healthy': '正常',
        'unhealthy': '异常',
        'error': '错误',
        'unknown': '未知'
      }
      return textMap[status] || '未知'
    },
    
    getCheckName(name) {
      const nameMap = {
        'backend': '后端服务',
        'emqx': 'EMQX API',
        'network': '网络连接'
      }
      return nameMap[name] || name
    },
    
    formatTime(time) {
      if (!time) return '未知'
      return time.toLocaleString()
    }
  }
}
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title h2 {
  margin: 0;
  color: #303133;
}

.page-title p {
  margin: 5px 0 0 0;
  color: #909399;
  font-size: 14px;
}

.overall-status-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.overall-status {
  display: flex;
  align-items: center;
  padding: 20px 0;
}

.status-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36px;
  color: white;
  margin-right: 20px;
}

.status-icon.status-healthy { background: #67c23a; }
.status-icon.status-warning { background: #e6a23c; }
.status-icon.status-error { background: #f56c6c; }
.status-icon.status-unknown { background: #909399; }

.status-info h3 {
  margin: 0 0 10px 0;
  color: #303133;
}

.status-info p {
  margin: 0 0 10px 0;
  color: #606266;
}

.status-time {
  font-size: 12px;
  color: #909399;
}

.check-card {
  margin-bottom: 20px;
  transition: all 0.3s;
}

.check-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
}

.card-healthy { border-left: 4px solid #67c23a; }
.card-warning { border-left: 4px solid #e6a23c; }
.card-error { border-left: 4px solid #f56c6c; }
.card-unknown { border-left: 4px solid #909399; }

.check-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.check-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
  margin-right: 15px;
}

.check-icon.check-healthy { background: #67c23a; }
.check-icon.check-warning { background: #e6a23c; }
.check-icon.check-error { background: #f56c6c; }
.check-icon.check-unknown { background: #909399; }

.check-title h4 {
  margin: 0;
  color: #303133;
}

.check-status {
  font-size: 12px;
  color: #909399;
}

.check-message {
  color: #606266;
  margin-bottom: 10px;
}

.check-metrics {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.metric {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.metric-label {
  font-size: 12px;
  color: #909399;
}

.metric-value {
  font-weight: bold;
  color: #303133;
}

.system-info-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.solutions-content h4 {
  margin-bottom: 15px;
  color: #303133;
}

.solutions-list {
  margin: 0;
  padding-left: 20px;
}

.solutions-list li {
  margin-bottom: 8px;
  color: #606266;
}
</style>
