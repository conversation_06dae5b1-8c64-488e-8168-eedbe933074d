<template>
  <div class="permission-management">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>数字库房权限管理</span>
        <div style="float: right;">
          <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增权限</el-button>
          <el-button type="success" icon="el-icon-refresh" @click="syncPermissions">同步权限</el-button>
          <el-button type="warning" icon="el-icon-download" @click="exportPermissions">导出配置</el-button>
        </div>
      </div>
      
      <!-- 搜索区域 -->
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
        <el-form-item label="权限名称" prop="permissionName">
          <el-input
            v-model="queryParams.permissionName"
            placeholder="请输入权限名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="权限代码" prop="permissionCode">
          <el-input
            v-model="queryParams.permissionCode"
            placeholder="请输入权限代码"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="权限类型" prop="permissionType">
          <el-select v-model="queryParams.permissionType" placeholder="请选择权限类型" clearable>
            <el-option label="菜单" value="menu"></el-option>
            <el-option label="按钮" value="button"></el-option>
            <el-option label="数据" value="data"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="模块" prop="moduleName">
          <el-select v-model="queryParams.moduleName" placeholder="请选择模块" clearable>
            <el-option label="数字库房" value="dwms"></el-option>
            <el-option label="组态配置" value="scada"></el-option>
            <el-option label="重量监控" value="weight"></el-option>
            <el-option label="门禁管理" value="access"></el-option>
            <el-option label="设备监控" value="device"></el-option>
            <el-option label="智能导寻" value="navigation"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option label="启用" value="1"></el-option>
            <el-option label="禁用" value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 权限树表格 -->
      <el-table
        v-loading="loading"
        :data="permissionList"
        row-key="id"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
        :expand-all="expandAll"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="permissionName" label="权限名称" width="200" show-overflow-tooltip>
          <template slot-scope="scope">
            <i :class="scope.row.icon" style="margin-right: 5px;" v-if="scope.row.icon"></i>
            {{ scope.row.permissionName }}
          </template>
        </el-table-column>
        <el-table-column prop="permissionCode" label="权限代码" width="180" show-overflow-tooltip />
        <el-table-column prop="permissionType" label="权限类型" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getPermissionTypeTag(scope.row.permissionType)" size="mini">
              {{ getPermissionTypeText(scope.row.permissionType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="moduleName" label="所属模块" width="120" align="center" />
        <el-table-column prop="orderNum" label="排序" width="80" align="center" />
        <el-table-column prop="routePath" label="路由地址" width="150" show-overflow-tooltip />
        <el-table-column prop="componentPath" label="组件路径" width="150" show-overflow-tooltip />
        <el-table-column prop="perms" label="权限标识" width="150" show-overflow-tooltip />
        <el-table-column prop="visible" label="显示状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.visible === 1 ? 'success' : 'danger'" size="mini">
              {{ scope.row.visible === 1 ? '显示' : '隐藏' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="权限状态" width="100" align="center">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(scope.row)"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160" align="center">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-plus"
              @click="handleAdd(scope.row)"
            >新增</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-copy-document"
              @click="handleCopy(scope.row)"
            >复制</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              style="color: #f56c6c;"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加或修改权限对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="680px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="上级权限" prop="parentId">
              <treeselect
                v-model="form.parentId"
                :options="permissionOptions"
                :normalizer="normalizer"
                :show-count="true"
                placeholder="选择上级权限"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="权限类型" prop="permissionType">
              <el-radio-group v-model="form.permissionType" @change="handlePermissionTypeChange">
                <el-radio label="menu">菜单</el-radio>
                <el-radio label="button">按钮</el-radio>
                <el-radio label="data">数据</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="权限名称" prop="permissionName">
              <el-input v-model="form.permissionName" placeholder="请输入权限名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="权限代码" prop="permissionCode">
              <el-input v-model="form.permissionCode" placeholder="请输入权限代码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示排序" prop="orderNum">
              <el-input-number v-model="form.orderNum" controls-position="right" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属模块" prop="moduleName">
              <el-select v-model="form.moduleName" placeholder="请选择模块">
                <el-option label="数字库房" value="dwms"></el-option>
                <el-option label="组态配置" value="scada"></el-option>
                <el-option label="重量监控" value="weight"></el-option>
                <el-option label="门禁管理" value="access"></el-option>
                <el-option label="设备监控" value="device"></el-option>
                <el-option label="智能导寻" value="navigation"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.permissionType === 'menu'">
            <el-form-item label="路由地址" prop="routePath">
              <el-input v-model="form.routePath" placeholder="请输入路由地址" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.permissionType === 'menu'">
            <el-form-item label="组件路径" prop="componentPath">
              <el-input v-model="form.componentPath" placeholder="请输入组件路径" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.permissionType === 'menu'">
            <el-form-item label="菜单图标" prop="icon">
              <el-popover
                placement="bottom-start"
                width="540"
                trigger="click"
                @show="$refs['iconSelect'].reset()"
              >
                <IconSelect ref="iconSelect" @selected="selected" />
                <el-input slot="reference" v-model="form.icon" placeholder="点击选择图标" readonly>
                  <i slot="prefix" :class="form.icon" style="height: 32px;width: 16px;"></i>
                </el-input>
              </el-popover>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.permissionType !== 'button'">
            <el-form-item label="权限标识" prop="perms">
              <el-input v-model="form.perms" placeholder="请输入权限标识" maxlength="100" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.permissionType === 'menu'">
            <el-form-item>
              <span slot="label">
                <el-tooltip content="选择是外链则路由地址需要以`http(s)://`开头" placement="top">
                  <i class="el-icon-question"></i>
                </el-tooltip>
                是否外链
              </span>
              <el-radio-group v-model="form.isFrame">
                <el-radio :label="0">否</el-radio>
                <el-radio :label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.permissionType === 'menu'">
            <el-form-item>
              <span slot="label">
                <el-tooltip content="选择隐藏则路由将不会出现在侧边栏，但仍然可以访问" placement="top">
                  <i class="el-icon-question"></i>
                </el-tooltip>
                显示状态
              </span>
              <el-radio-group v-model="form.visible">
                <el-radio :label="0">隐藏</el-radio>
                <el-radio :label="1">显示</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.permissionType === 'menu'">
            <el-form-item>
              <span slot="label">
                <el-tooltip content="选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致" placement="top">
                  <i class="el-icon-question"></i>
                </el-tooltip>
                是否缓存
              </span>
              <el-radio-group v-model="form.isCache">
                <el-radio :label="0">否</el-radio>
                <el-radio :label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="权限状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio :label="0">禁用</el-radio>
                <el-radio :label="1">启用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="功能描述" prop="description">
              <el-input v-model="form.description" type="textarea" placeholder="请输入功能描述"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  listPermissions, 
  getPermission, 
  delPermission, 
  addPermission, 
  updatePermission,
  getPermissionTree,
  syncSystemPermissions,
  exportPermissions,
  copyPermission
} from '@/api/dwms/permission'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import IconSelect from '@/components/IconSelect'

export default {
  name: 'DwmsPermission',
  components: {
    Treeselect,
    IconSelect
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 权限表格树数据
      permissionList: [],
      // 权限树选项
      permissionOptions: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 是否展开，默认全部展开
      expandAll: true,
      // 查询参数
      queryParams: {
        permissionName: null,
        permissionCode: null,
        permissionType: null,
        moduleName: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        permissionName: [
          { required: true, message: '权限名称不能为空', trigger: 'blur' }
        ],
        permissionCode: [
          { required: true, message: '权限代码不能为空', trigger: 'blur' }
        ],
        permissionType: [
          { required: true, message: '权限类型不能为空', trigger: 'change' }
        ],
        orderNum: [
          { required: true, message: '权限顺序不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询权限列表 */
    getList() {
      this.loading = true
      listPermissions(this.queryParams).then(response => {
        this.permissionList = this.handleTree(response.data, 'id', 'parentId')
        this.loading = false
      })
    },
    
    /** 转换权限数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        label: node.permissionName,
        children: node.children
      }
    },
    
    /** 查询权限下拉树结构 */
    getTreeselect() {
      getPermissionTree().then(response => {
        this.permissionOptions = []
        const permission = { id: 0, permissionName: '主类目', children: [] }
        permission.children = this.handleTree(response.data, 'id', 'parentId')
        this.permissionOptions.push(permission)
      })
    },
    
    /** 图标选择 */
    selected(name) {
      this.form.icon = name
    },
    
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList()
    },
    
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset()
      this.getTreeselect()
      if (row != null && row.id) {
        this.form.parentId = row.id
      } else {
        this.form.parentId = 0
      }
      this.open = true
      this.title = '添加权限'
    },
    
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.getTreeselect()
      getPermission(row.id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改权限'
      })
    },
    
    /** 复制按钮操作 */
    handleCopy(row) {
      this.$confirm('是否确认复制名称为"' + row.permissionName + '"的数据项？').then(function() {
        return copyPermission(row.id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('复制成功')
      }).catch(() => {})
    },
    
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updatePermission(this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addPermission(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm('是否确认删除名称为"' + row.permissionName + '"的数据项？').then(function() {
        return delPermission(row.id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    
    /** 权限状态修改 */
    handleStatusChange(row) {
      let text = row.status === 0 ? '启用' : '停用'
      this.$confirm('确认要"' + text + '""' + row.permissionName + '"权限吗？').then(function() {
        return updatePermission(row)
      }).then(() => {
        this.$modal.msgSuccess(text + '成功')
      }).catch(function() {
        row.status = row.status === 0 ? 1 : 0
      })
    },
    
    /** 权限类型变化 */
    handlePermissionTypeChange(value) {
      if (value === 'button') {
        this.form.isFrame = 0
        this.form.isCache = 0
        this.form.visible = 1
      }
    },
    
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    
    /** 同步权限 */
    syncPermissions() {
      this.$confirm('确认要同步系统权限吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        return syncSystemPermissions()
      }).then((response) => {
        this.$modal.msgSuccess(response.msg)
        this.getList()
      })
    },
    
    /** 导出权限配置 */
    exportPermissions() {
      this.$confirm('是否确认导出所有权限数据项？').then(() => {
        this.download('dwms/permission/export', {
          ...this.queryParams
        }, `permission_${new Date().getTime()}.xlsx`)
      })
    },
    
    /** 表单重置 */
    reset() {
      this.form = {
        id: null,
        parentId: 0,
        permissionName: null,
        permissionCode: null,
        permissionType: 'menu',
        orderNum: 0,
        routePath: null,
        componentPath: null,
        icon: null,
        isFrame: 0,
        isCache: 0,
        visible: 1,
        status: 1,
        moduleName: 'dwms',
        description: null,
        perms: null
      }
      this.resetForm('form')
    },
    
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    
    /** 获取权限类型标签 */
    getPermissionTypeTag(type) {
      const typeMap = {
        menu: 'primary',
        button: 'success',
        data: 'warning'
      }
      return typeMap[type] || 'info'
    },
    
    /** 获取权限类型文本 */
    getPermissionTypeText(type) {
      const textMap = {
        menu: '菜单',
        button: '按钮',
        data: '数据'
      }
      return textMap[type] || type
    }
  }
}
</script>
