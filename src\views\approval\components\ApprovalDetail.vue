<template>
  <div class="approval-detail">
    <el-card v-loading="loading" class="box-card">
      <div slot="header" class="clearfix">
        <span>审批详情</span>
        <el-tag :type="getStatusType(detail.status)" style="float: right;">
          {{ getStatusText(detail.status) }}
        </el-tag>
      </div>
      
      <!-- 基本信息 -->
      <el-descriptions title="基本信息" :column="2" border>
        <el-descriptions-item label="审批标题">{{ detail.businessTitle }}</el-descriptions-item>
        <el-descriptions-item label="业务类型">{{ detail.businessType }}</el-descriptions-item>
        <el-descriptions-item label="申请人">{{ detail.applyUserName }}</el-descriptions-item>
        <el-descriptions-item label="申请时间">{{ detail.createTime }}</el-descriptions-item>
        <el-descriptions-item label="当前节点">{{ detail.currentNodeName }}</el-descriptions-item>
        <el-descriptions-item label="流程名称">{{ detail.workflowName }}</el-descriptions-item>
      </el-descriptions>

      <!-- 审批流程 -->
      <el-divider content-position="left">审批流程</el-divider>
      <el-timeline>
        <el-timeline-item
          v-for="(record, index) in approvalRecords"
          :key="index"
          :type="getTimelineType(record.action)"
          :icon="getTimelineIcon(record.action)"
        >
          <el-card>
            <div slot="header" class="clearfix">
              <span>{{ record.nodeName }}</span>
              <el-tag :type="getActionType(record.action)" size="small" style="float: right;">
                {{ getActionText(record.action) }}
              </el-tag>
            </div>
            <div>
              <p><strong>操作人：</strong>{{ record.operationUserName }}</p>
              <p><strong>操作时间：</strong>{{ record.operationTime }}</p>
              <p v-if="record.opinion"><strong>审批意见：</strong>{{ record.opinion }}</p>
              <p v-if="record.approvalOpinion"><strong>详细意见：</strong>{{ record.approvalOpinion }}</p>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>

      <!-- 业务数据 -->
      <el-divider content-position="left">业务数据</el-divider>
      <div v-if="businessData">
        <pre>{{ JSON.stringify(businessData, null, 2) }}</pre>
      </div>
      <div v-else>
        <el-empty description="暂无业务数据"></el-empty>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getApprovalDetail } from "@/api/approval/approval";

export default {
  name: "ApprovalDetail",
  props: {
    instanceId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      loading: false,
      detail: {},
      approvalRecords: [],
      businessData: null
    };
  },
  created() {
    this.getDetail();
  },
  watch: {
    instanceId: {
      handler() {
        this.getDetail();
      },
      immediate: true
    }
  },
  methods: {
    /** 获取审批详情 */
    getDetail() {
      if (!this.instanceId) return;
      
      this.loading = true;
      getApprovalDetail(this.instanceId).then(response => {
        this.detail = response.data.instance || {};
        this.approvalRecords = response.data.records || [];
        this.businessData = response.data.businessData || null;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    
    /** 获取状态类型 */
    getStatusType(status) {
      const typeMap = {
        '0': 'warning',  // 审批中
        '1': 'success',  // 已通过
        '2': 'danger',   // 已驳回
        '3': 'info',     // 已撤销
        '4': 'info'      // 已终止
      };
      return typeMap[status] || 'info';
    },
    
    /** 获取状态文本 */
    getStatusText(status) {
      const textMap = {
        '0': '审批中',
        '1': '已通过',
        '2': '已驳回',
        '3': '已撤销',
        '4': '已终止'
      };
      return textMap[status] || '未知';
    },
    
    /** 获取时间线类型 */
    getTimelineType(action) {
      const typeMap = {
        '0': 'primary',  // 提交
        '1': 'success',  // 通过
        '2': 'danger',   // 驳回
        '3': 'warning',  // 撤销
        '4': 'info'      // 转交
      };
      return typeMap[action] || 'primary';
    },
    
    /** 获取时间线图标 */
    getTimelineIcon(action) {
      const iconMap = {
        '0': 'el-icon-upload',     // 提交
        '1': 'el-icon-check',      // 通过
        '2': 'el-icon-close',      // 驳回
        '3': 'el-icon-refresh',    // 撤销
        '4': 'el-icon-share'       // 转交
      };
      return iconMap[action] || 'el-icon-time';
    },
    
    /** 获取操作类型 */
    getActionType(action) {
      const typeMap = {
        '0': 'primary',  // 提交
        '1': 'success',  // 通过
        '2': 'danger',   // 驳回
        '3': 'warning',  // 撤销
        '4': 'info'      // 转交
      };
      return typeMap[action] || 'info';
    },
    
    /** 获取操作文本 */
    getActionText(action) {
      const textMap = {
        '0': '提交',
        '1': '通过',
        '2': '驳回',
        '3': '撤销',
        '4': '转交'
      };
      return textMap[action] || '未知';
    }
  }
};
</script>

<style scoped>
.approval-detail {
  padding: 20px;
}

.el-descriptions {
  margin-bottom: 20px;
}

.el-timeline {
  padding-left: 20px;
}

.el-timeline-item {
  padding-bottom: 20px;
}

pre {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.5;
}
</style>
