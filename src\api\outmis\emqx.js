import request from '@/utils/request'

// 查询出库防错设备EMQX参数列表
export function listEmqx(query) {
  return request({
    url: '/outmis/emqx/list',
    method: 'get',
    params: query
  })
}

// 查询出库防错设备EMQX参数详细
export function getEmqx(emqxId) {
  return request({
    url: '/outmis/emqx/' + emqxId,
    method: 'get'
  })
}

// 根据设备ID查询EMQX参数
export function getEmqxByDeviceId(deviceId) {
  return request({
    url: '/outmis/emqx/device/' + deviceId,
    method: 'get'
  })
}

// 新增出库防错设备EMQX参数
export function addEmqx(data) {
  return request({
    url: '/outmis/emqx',
    method: 'post',
    data: data
  })
}

// 修改出库防错设备EMQX参数
export function updateEmqx(data) {
  return request({
    url: '/outmis/emqx',
    method: 'put',
    data: data
  })
}

// 删除出库防错设备EMQX参数
export function delEmqx(emqxId) {
  return request({
    url: '/outmis/emqx/' + emqxId,
    method: 'delete'
  })
}

// 测试EMQX连接
export function testEmqxConnection(data) {
  return request({
    url: '/outmis/emqx/test',
    method: 'post',
    data: data
  })
}
