<template>
  <div class="guide-dashboard">
    <!-- 顶部导航和信息概览 -->
    <el-row :gutter="20" class="guide-header">
      <el-col :span="12">
        <h2 class="guide-title">智能寻导系统</h2>
        <div class="guide-subtitle">帮助用户快速高效地寻找和到达目标位置</div>
      </el-col>
      <el-col :span="12" class="stats-overview">
        <div class="stat-item">
          <span class="stat-num">{{ configCount }}</span>
          <span class="stat-label">配置总数</span>
        </div>
        <div class="stat-item">
          <span class="stat-num">{{ deviceCount }}</span>
          <span class="stat-label">涉及设备</span>
        </div>
        <div class="stat-item">
          <span class="stat-num">{{ todayOps }}</span>
          <span class="stat-label">今日操作</span>
        </div>
      </el-col>
    </el-row>

    <!-- 主要功能卡片 -->
    <el-row :gutter="20" class="guide-cards">
      <el-col :span="8">
        <el-card shadow="hover" class="guide-card" @click.native="goToConfig">
          <div class="card-icon config-icon">
            <i class="el-icon-setting"></i>
          </div>
          <div class="card-content">
            <h3>寻导配置</h3>
            <p>设置导寻方法、触发条件和响应时间</p>
          </div>
          <div class="card-action">
            <el-button type="primary" size="small" plain>进入配置</el-button>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card shadow="hover" class="guide-card" @click.native="goToLog">
          <div class="card-icon log-icon">
            <i class="el-icon-document"></i>
          </div>
          <div class="card-content">
            <h3>操作日志</h3>
            <p>查看历史操作记录和执行结果</p>
          </div>
          <div class="card-action">
            <el-button type="primary" size="small" plain>查看日志</el-button>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card shadow="hover" class="guide-card" @click.native="goToMonitor">
          <div class="card-icon monitor-icon">
            <i class="el-icon-monitor"></i>
          </div>
          <div class="card-content">
            <h3>实时监控</h3>
            <p>实时查看设备状态和控制情况</p>
          </div>
          <div class="card-action">
            <el-button type="primary" size="small" plain>进入监控</el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最新动态和统计信息 -->
    <el-row :gutter="20" class="guide-stats">
      <el-col :span="16">
        <el-card shadow="hover" class="latest-activities">
          <div slot="header" class="clearfix">
            <span>最新操作动态</span>
            <el-button style="float: right" type="text" @click="refreshActivities">刷新</el-button>
          </div>
          <div v-loading="activitiesLoading">
            <el-timeline>
              <el-timeline-item
                v-for="(activity, index) in latestActivities"
                :key="index"
                :timestamp="formatTime(activity.createTime)"
                :type="getActivityType(activity.type)"
                size="large"
              >
                {{ activity.content }}
                <span v-if="activity.result" :class="['activity-result', activity.result === '成功' ? 'success' : 'fail']">{{ activity.result }}</span>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card shadow="hover" class="method-stats">
          <div slot="header" class="clearfix">
            <span>寻导方法统计</span>
          </div>
          <div class="chart-container" ref="methodChart" style="height: 300px"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: "GuideDashboard",
  data() {
    return {
      configCount: 0,
      deviceCount: 0,
      todayOps: 0,
      activitiesLoading: true,
      latestActivities: [],
      methodChart: null
    }
  },
  mounted() {
    this.getStatistics()
    this.getLatestActivities()
    this.$nextTick(() => {
      this.initMethodChart()
      window.addEventListener('resize', this.resizeChart)
    })
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeChart)
    if (this.methodChart) {
      this.methodChart.dispose()
    }
  },
  methods: {
    goToConfig() {
      this.$router.push('/guide/config')
    },
    goToLog() {
      this.$router.push('/guide/log')
    },
    goToMonitor() {
      this.$router.push('/guide/monitor')
    },
    getStatistics() {
      // 模拟获取统计数据，实际项目中应替换为真实接口调用
      setTimeout(() => {
        this.configCount = 58
        this.deviceCount = 127
        this.todayOps = 42
      }, 500)
    },
    getLatestActivities() {
      this.activitiesLoading = true
      // 模拟获取最近活动数据，实际项目中应替换为真实接口调用
      setTimeout(() => {
        this.latestActivities = [
          { 
            createTime: new Date().getTime() - 5 * 60000, 
            type: 'voice',
            content: '用户张三触发了语音播报',
            result: '成功'
          },
          { 
            createTime: new Date().getTime() - 17 * 60000, 
            type: 'light',
            content: '系统自动触发了货架灯光指示',
            result: '成功'
          },
          { 
            createTime: new Date().getTime() - 45 * 60000, 
            type: 'ar',
            content: '用户李四使用了AR导航',
            result: '失败'
          },
          { 
            createTime: new Date().getTime() - 120 * 60000, 
            type: 'system',
            content: '管理员更新了导寻配置',
            result: ''
          }
        ]
        this.activitiesLoading = false
      }, 800)
    },
    refreshActivities() {
      this.getLatestActivities()
    },
    formatTime(timestamp) {
      const date = new Date(timestamp)
      return date.getHours().toString().padStart(2, '0') + ':' + 
             date.getMinutes().toString().padStart(2, '0')
    },
    getActivityType(type) {
      const typeMap = {
        'voice': 'primary',
        'light': 'warning',
        'ar': 'info',
        'system': 'success'
      }
      return typeMap[type] || 'primary'
    },
    initMethodChart() {
      this.methodChart = echarts.init(this.$refs.methodChart)
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: ['语音播报', '货架灯亮', 'AR导航']
        },
        series: [
          {
            name: '寻导方法',
            type: 'pie',
            radius: '60%',
            center: ['50%', '60%'],
            data: [
              { value: 35, name: '语音播报' },
              { value: 18, name: '货架灯亮' },
              { value: 5, name: 'AR导航' }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            itemStyle: {
              normal: {
                color: function(params) {
                  const colorList = ['#409EFF', '#E6A23C', '#909399']
                  return colorList[params.dataIndex]
                }
              }
            }
          }
        ]
      }
      this.methodChart.setOption(option)
    },
    resizeChart() {
      if (this.methodChart) {
        this.methodChart.resize()
      }
    }
  }
}
</script>

<style scoped>
.guide-dashboard {
  padding: 20px 0;
}
.guide-header {
  margin-bottom: 30px;
}
.guide-title {
  margin: 0 0 5px;
  font-size: 28px;
  line-height: 1.2;
}
.guide-subtitle {
  color: #909399;
  margin-top: 5px;
}
.stats-overview {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 100%;
}
.stat-item {
  text-align: center;
  margin-left: 30px;
}
.stat-num {
  display: block;
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
  color: #606266;
}
.stat-label {
  display: block;
  margin-top: 5px;
  font-size: 14px;
  color: #909399;
}
.guide-cards {
  margin-bottom: 30px;
}
.guide-card {
  height: 180px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
}
.guide-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0,0,0,0.1);
}
.card-icon {
  font-size: 36px;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
}
.config-icon {
  background-color: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}
.log-icon {
  background-color: rgba(103, 194, 58, 0.1);
  color: #67C23A;
}
.monitor-icon {
  background-color: rgba(230, 162, 60, 0.1);
  color: #E6A23C;
}
.card-content {
  flex-grow: 1;
}
.card-content h3 {
  margin: 0 0 10px;
  font-size: 18px;
}
.card-content p {
  color: #909399;
  margin: 0;
  font-size: 14px;
}
.card-action {
  margin-top: 15px;
}
.guide-stats {
  margin-bottom: 20px;
}
.latest-activities, .method-stats {
  height: 400px;
}
.activity-result {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 5px;
}
.activity-result.success {
  background-color: #f0f9eb;
  color: #67C23A;
}
.activity-result.fail {
  background-color: #fef0f0;
  color: #F56C6C;
}
</style> 