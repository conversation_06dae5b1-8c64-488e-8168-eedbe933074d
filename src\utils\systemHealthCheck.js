// 系统健康检查工具

import axios from 'axios'

/**
 * 系统健康检查类
 */
export class SystemHealthCheck {
  constructor() {
    this.baseURL = process.env.VUE_APP_BASE_API
    this.timeout = 10000 // 10秒超时
  }

  /**
   * 检查后端API健康状态
   */
  async checkBackendHealth() {
    const result = {
      status: 'unknown',
      message: '',
      responseTime: 0,
      details: {}
    }

    const startTime = Date.now()
    
    try {
      // 尝试调用健康检查接口
      const response = await axios.get(`${this.baseURL}/actuator/health`, {
        timeout: this.timeout
      })
      
      result.responseTime = Date.now() - startTime
      
      if (response.status === 200) {
        result.status = 'healthy'
        result.message = '后端服务正常'
        result.details = response.data
      } else {
        result.status = 'unhealthy'
        result.message = `后端服务异常，状态码: ${response.status}`
      }
    } catch (error) {
      result.responseTime = Date.now() - startTime
      result.status = 'error'
      
      if (error.code === 'ECONNABORTED') {
        result.message = `连接超时 (${this.timeout}ms)`
      } else if (error.code === 'ECONNREFUSED') {
        result.message = '连接被拒绝，后端服务可能未启动'
      } else if (error.code === 'ENOTFOUND') {
        result.message = '无法解析服务器地址'
      } else {
        result.message = `连接错误: ${error.message}`
      }
      
      result.details = {
        code: error.code,
        message: error.message
      }
    }

    return result
  }

  /**
   * 检查EMQX API健康状态
   */
  async checkEmqxHealth() {
    const result = {
      status: 'unknown',
      message: '',
      responseTime: 0,
      details: {}
    }

    const startTime = Date.now()
    
    try {
      // 尝试调用EMQX概览接口
      const response = await axios.get(`${this.baseURL}/emqx/dashboard/overview`, {
        timeout: this.timeout
      })
      
      result.responseTime = Date.now() - startTime
      
      if (response.status === 200 && response.data.code === 200) {
        result.status = 'healthy'
        result.message = 'EMQX API正常'
        result.details = response.data
      } else {
        result.status = 'unhealthy'
        result.message = 'EMQX API响应异常'
        result.details = response.data
      }
    } catch (error) {
      result.responseTime = Date.now() - startTime
      result.status = 'error'
      result.message = `EMQX API错误: ${error.message}`
      result.details = {
        code: error.code,
        message: error.message
      }
    }

    return result
  }

  /**
   * 检查网络连接
   */
  async checkNetworkConnectivity() {
    const result = {
      status: 'unknown',
      message: '',
      responseTime: 0
    }

    const startTime = Date.now()
    
    try {
      // 检查是否能访问公网
      await axios.get('https://www.baidu.com', {
        timeout: 5000,
        mode: 'no-cors'
      })
      
      result.responseTime = Date.now() - startTime
      result.status = 'healthy'
      result.message = '网络连接正常'
    } catch (error) {
      result.responseTime = Date.now() - startTime
      result.status = 'error'
      result.message = '网络连接异常'
    }

    return result
  }

  /**
   * 执行完整的系统健康检查
   */
  async performFullHealthCheck() {
    const results = {
      timestamp: new Date().toISOString(),
      overall: 'unknown',
      checks: {}
    }

    console.log('开始系统健康检查...')

    // 并行执行所有检查
    const [backendHealth, emqxHealth, networkHealth] = await Promise.all([
      this.checkBackendHealth(),
      this.checkEmqxHealth(),
      this.checkNetworkConnectivity()
    ])

    results.checks.backend = backendHealth
    results.checks.emqx = emqxHealth
    results.checks.network = networkHealth

    // 计算总体状态
    const healthyCount = Object.values(results.checks).filter(check => check.status === 'healthy').length
    const totalChecks = Object.keys(results.checks).length

    if (healthyCount === totalChecks) {
      results.overall = 'healthy'
    } else if (healthyCount > 0) {
      results.overall = 'partial'
    } else {
      results.overall = 'unhealthy'
    }

    console.log('系统健康检查完成:', results)
    return results
  }

  /**
   * 生成健康检查报告
   */
  generateHealthReport(healthCheckResults) {
    const { overall, checks, timestamp } = healthCheckResults
    
    let report = `系统健康检查报告\n`
    report += `检查时间: ${new Date(timestamp).toLocaleString()}\n`
    report += `总体状态: ${this.getStatusText(overall)}\n\n`

    Object.entries(checks).forEach(([name, result]) => {
      report += `${this.getCheckName(name)}:\n`
      report += `  状态: ${this.getStatusText(result.status)}\n`
      report += `  消息: ${result.message}\n`
      report += `  响应时间: ${result.responseTime}ms\n\n`
    })

    return report
  }

  /**
   * 获取状态文本
   */
  getStatusText(status) {
    const statusMap = {
      'healthy': '✅ 正常',
      'unhealthy': '⚠️ 异常',
      'error': '❌ 错误',
      'partial': '⚠️ 部分异常',
      'unknown': '❓ 未知'
    }
    return statusMap[status] || status
  }

  /**
   * 获取检查项名称
   */
  getCheckName(name) {
    const nameMap = {
      'backend': '后端服务',
      'emqx': 'EMQX API',
      'network': '网络连接'
    }
    return nameMap[name] || name
  }

  /**
   * 获取修复建议
   */
  getFixSuggestions(healthCheckResults) {
    const suggestions = []
    const { checks } = healthCheckResults

    if (checks.backend?.status !== 'healthy') {
      suggestions.push({
        issue: '后端服务异常',
        suggestions: [
          '检查后端服务是否正常启动',
          '查看后端服务日志',
          '确认端口8080是否被占用',
          '检查数据库连接是否正常'
        ]
      })
    }

    if (checks.emqx?.status !== 'healthy') {
      suggestions.push({
        issue: 'EMQX API异常',
        suggestions: [
          '检查EMQX服务是否正常运行',
          '确认EMQX API端口18083是否可访问',
          '检查EMQX配置文件',
          '查看EMQX日志'
        ]
      })
    }

    if (checks.network?.status !== 'healthy') {
      suggestions.push({
        issue: '网络连接异常',
        suggestions: [
          '检查网络连接',
          '确认防火墙设置',
          '检查代理配置',
          '尝试重启网络服务'
        ]
      })
    }

    return suggestions
  }
}

// 创建全局实例
export const systemHealthCheck = new SystemHealthCheck()

// 导出便捷方法
export const quickHealthCheck = () => systemHealthCheck.performFullHealthCheck()

export default SystemHealthCheck
