<template>
  <span 
    class="menu-icon" 
    :class="[
      `menu-icon--${size}`,
      `menu-icon--${theme}`,
      { 'menu-icon--animated': animated }
    ]"
    :style="customStyle"
  >
    <svg 
      v-if="svgContent" 
      :width="iconSize" 
      :height="iconSize" 
      viewBox="0 0 24 24"
      class="menu-icon__svg"
    >
      <g v-html="svgContent"></g>
    </svg>
    <i 
      v-else 
      :class="fallbackIcon"
      class="menu-icon__fallback"
    ></i>
  </span>
</template>

<script>
import { getSvgIcon, getMenuIcon, iconThemes } from '@/utils/menu-icons'

export default {
  name: 'MenuIcon',
  props: {
    // 图标名称或菜单名称
    icon: {
      type: String,
      default: ''
    },
    // 菜单路径（用于自动匹配图标）
    menuPath: {
      type: String,
      default: ''
    },
    // 图标大小
    size: {
      type: String,
      default: 'medium',
      validator: value => ['small', 'medium', 'large', 'xlarge'].includes(value)
    },
    // 图标主题色
    theme: {
      type: String,
      default: 'primary',
      validator: value => Object.keys(iconThemes).includes(value)
    },
    // 自定义颜色
    color: {
      type: String,
      default: ''
    },
    // 是否启用动画
    animated: {
      type: Boolean,
      default: false
    },
    // 后备图标（Element UI图标）
    fallback: {
      type: String,
      default: 'el-icon-menu'
    }
  },
  computed: {
    // 获取图标名称
    iconName() {
      if (this.icon) {
        return getMenuIcon(this.icon, this.menuPath)
      }
      return getMenuIcon('', this.menuPath)
    },
    
    // 获取SVG内容
    svgContent() {
      const content = getSvgIcon(this.iconName)
      // 移除外层的svg标签，只保留path内容
      return content.replace(/<svg[^>]*>|<\/svg>/g, '')
    },
    
    // 图标尺寸
    iconSize() {
      const sizes = {
        small: 16,
        medium: 20,
        large: 24,
        xlarge: 32
      }
      return sizes[this.size]
    },
    
    // 后备图标
    fallbackIcon() {
      return this.fallback
    },
    
    // 自定义样式
    customStyle() {
      const style = {}
      
      if (this.color) {
        style.color = this.color
      } else {
        style.color = iconThemes[this.theme]
      }
      
      return style
    }
  }
}
</script>

<style lang="scss" scoped>
.menu-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  
  &__svg {
    fill: currentColor;
    transition: all 0.3s ease;
  }
  
  &__fallback {
    font-size: inherit;
    transition: all 0.3s ease;
  }
  
  // 尺寸变体
  &--small {
    width: 16px;
    height: 16px;
    font-size: 16px;
  }
  
  &--medium {
    width: 20px;
    height: 20px;
    font-size: 20px;
  }
  
  &--large {
    width: 24px;
    height: 24px;
    font-size: 24px;
  }
  
  &--xlarge {
    width: 32px;
    height: 32px;
    font-size: 32px;
  }
  
  // 主题色变体
  &--primary {
    color: #1e3a8a;
  }
  
  &--secondary {
    color: #3b82f6;
  }
  
  &--success {
    color: #10b981;
  }
  
  &--warning {
    color: #f59e0b;
  }
  
  &--danger {
    color: #ef4444;
  }
  
  &--info {
    color: #6b7280;
  }
  
  &--light {
    color: #f8fafc;
  }
  
  &--dark {
    color: #1f2937;
  }
  
  // 动画效果
  &--animated {
    &:hover {
      transform: scale(1.1);
      filter: brightness(1.2);
    }
  }
  
  // 在菜单中的特殊样式
  .el-menu-item &,
  .el-submenu__title & {
    margin-right: 8px;
    
    &--animated:hover {
      transform: scale(1.05);
    }
  }
  
  // 在按钮中的特殊样式
  .el-button & {
    margin-right: 4px;
    
    &--small {
      width: 14px;
      height: 14px;
      font-size: 14px;
    }
  }
  
  // 在卡片标题中的特殊样式
  .el-card__header & {
    margin-right: 8px;
    color: #1e3a8a;
  }
  
  // 在表格中的特殊样式
  .el-table & {
    &--small {
      width: 14px;
      height: 14px;
      font-size: 14px;
    }
  }
}

// 深色主题适配
.dark-theme {
  .menu-icon {
    &--primary {
      color: #60a5fa;
    }
    
    &--secondary {
      color: #93c5fd;
    }
    
    &--info {
      color: #d1d5db;
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .menu-icon {
    &__svg,
    &__fallback {
      filter: contrast(1.5);
    }
  }
}

// 减少动画模式
@media (prefers-reduced-motion: reduce) {
  .menu-icon {
    &--animated {
      transition: none;
      
      &:hover {
        transform: none;
      }
    }
  }
}
</style>
