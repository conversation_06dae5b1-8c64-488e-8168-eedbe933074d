<template>
  <div class="app-container">
    <!-- 顶部数据统计卡片 -->
    <el-row :gutter="20" class="dashboard-stats">
      <el-col :span="6">
        <el-card shadow="hover" class="stats-card">
          <div class="stats-icon today"><i class="el-icon-date"></i></div>
          <div class="stats-info">
            <div class="stats-title">今日操作</div>
            <div class="stats-number">{{ todayOperations }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stats-card">
          <div class="stats-icon success"><i class="el-icon-success"></i></div>
          <div class="stats-info">
            <div class="stats-title">成功操作</div>
            <div class="stats-number">{{ successOperations }}</div>
            <div class="stats-rate">{{ successRate }}%</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stats-card">
          <div class="stats-icon fail"><i class="el-icon-error"></i></div>
          <div class="stats-info">
            <div class="stats-title">失败操作</div>
            <div class="stats-number">{{ failOperations }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stats-card">
          <div class="stats-icon device"><i class="el-icon-cpu"></i></div>
          <div class="stats-info">
            <div class="stats-title">涉及设备</div>
            <div class="stats-number">{{ deviceCount }}</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 日志趋势图 -->
    <el-card shadow="hover" class="chart-card">
      <div slot="header" class="clearfix">
        <span>操作日志趋势</span>
        <el-radio-group v-model="chartTimeRange" size="small" style="float: right">
          <el-radio-button label="today">今日</el-radio-button>
          <el-radio-button label="week">本周</el-radio-button>
          <el-radio-button label="month">本月</el-radio-button>
        </el-radio-group>
      </div>
      <div class="chart-container" ref="logChart" style="height: 300px"></div>
    </el-card>

    <!-- 搜索组件优化 -->
    <el-card shadow="hover" class="search-card">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="88px">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handleDateRangeChange"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
        <el-form-item label="操作类型">
          <el-select v-model="queryParams.operationType" placeholder="选择操作类型" clearable>
            <el-option label="语音播报" value="语音播报" />
            <el-option label="灯光控制" value="灯光控制" />
            <el-option label="AR导航" value="AR导航" />
          </el-select>
        </el-form-item>
        <el-form-item label="设备ID">
          <el-input v-model="queryParams.deviceId" placeholder="请输入目标设备ID" clearable />
        </el-form-item>
        <el-form-item label="执行结果">
          <el-select v-model="queryParams.executionResult" placeholder="选择执行结果" clearable>
            <el-option label="成功" value="成功" />
            <el-option label="失败" value="失败" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 日志列表优化 -->
    <el-card shadow="hover" class="table-card">
      <div slot="header" class="clearfix">
        <span>操作日志列表</span>
        <el-button-group style="float: right">
          <el-button type="primary" icon="el-icon-refresh" size="small" @click="getList">刷新</el-button>
          <el-button type="danger" icon="el-icon-delete" size="small" :disabled="multiple" @click="handleBatchDelete" v-hasPermi="['guide:log:remove']">批量删除</el-button>
          <el-button type="warning" icon="el-icon-download" size="small" @click="handleExport" v-hasPermi="['guide:log:export']">导出</el-button>
        </el-button-group>
      </div>

      <el-table
        v-loading="loading"
        :data="logList"
        @selection-change="handleSelectionChange"
        border
        stripe
        highlight-current-row
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="操作时间" align="center" width="180" sortable>
          <template slot-scope="scope">
            <div class="time-column">
              <div>{{ parseDate(scope.row.operationTime) }}</div>
              <div class="time-detail">{{ parseTime(scope.row.operationTime) }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作类型" align="center" width="120">
          <template slot-scope="scope">
            <el-tag :type="getOperationTypeTag(scope.row.operationType)" size="medium">{{ scope.row.operationType }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="目标设备" align="center" min-width="120">
          <template slot-scope="scope">
            <el-link type="primary" @click="openDeviceDetail(scope.row.deviceId)">{{ scope.row.deviceId }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="控制内容" align="center" min-width="180" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <el-popover
              placement="top-start"
              title="详细内容"
              width="300"
              trigger="hover"
              :content="scope.row.controlContent"
            >
              <el-button slot="reference" type="text">{{ truncateContent(scope.row.controlContent) }}</el-button>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column label="执行结果" align="center" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.executionResult === '成功' ? 'success' : 'danger'">{{ scope.row.executionResult }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="关联单据" align="center" width="120">
          <template slot-scope="scope">
            <el-link type="info" @click="openBillDetail(scope.row.billId)" :disabled="!scope.row.billId">{{ scope.row.billId || '无' }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="操作人" align="center" width="120">
          <template slot-scope="scope">
            <el-popover
              placement="top"
              width="200"
              trigger="hover"
            >
              <div class="user-card">
                <div class="avatar">
                  <img :src="getUserAvatar(scope.row.operatorId)" alt="用户头像">
                </div>
                <div class="user-info">
                  <div class="username">{{ getUserName(scope.row.operatorId) }}</div>
                  <div class="department">{{ getUserDepartment(scope.row.operatorId) }}</div>
                </div>
              </div>
              <el-button slot="reference" type="text">{{ getUserName(scope.row.operatorId) }}</el-button>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="180">
          <template slot-scope="scope">
            <el-button-group>
              <el-button size="mini" type="primary" icon="el-icon-view" @click="handleView(scope.row)">查看</el-button>
              <el-button v-if="scope.row.executionResult === '失败'" size="mini" type="warning" icon="el-icon-refresh-left" @click="handleRetry(scope.row)">重试</el-button>
              <el-button size="mini" type="danger" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['guide:log:remove']">删除</el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 日志详情弹窗 -->
    <el-dialog title="操作日志详情" :visible.sync="logDetailOpen" width="700px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="日志ID">{{ viewForm.id }}</el-descriptions-item>
        <el-descriptions-item label="操作时间">{{ formatDateTime(viewForm.operationTime) }}</el-descriptions-item>
        <el-descriptions-item label="操作类型">
          <el-tag :type="getOperationTypeTag(viewForm.operationType)">{{ viewForm.operationType }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="执行结果">
          <el-tag :type="viewForm.executionResult === '成功' ? 'success' : 'danger'">{{ viewForm.executionResult }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="目标设备">
          <el-link type="primary" @click="openDeviceDetail(viewForm.deviceId)">{{ viewForm.deviceId }}</el-link>
        </el-descriptions-item>
        <el-descriptions-item label="关联单据">
          <el-link type="info" @click="openBillDetail(viewForm.billId)" :disabled="!viewForm.billId">{{ viewForm.billId || '无' }}</el-link>
        </el-descriptions-item>
        <el-descriptions-item label="操作人">
          <div class="user-inline">
            <div class="avatar-small">
              <img :src="getUserAvatar(viewForm.operatorId)" alt="用户头像">
            </div>
            <span>{{ getUserName(viewForm.operatorId) }}</span>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="操作IP">{{ viewForm.operatorIp || '未记录' }}</el-descriptions-item>
        <el-descriptions-item :span="2" label="控制内容">
          <div v-html="formatContent(viewForm.controlContent)"></div>
        </el-descriptions-item>
        <el-descriptions-item :span="2" label="执行详情">
          <pre class="execution-detail">{{ viewForm.executionDetail || '无详细信息' }}</pre>
        </el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="logDetailOpen = false">关 闭</el-button>
        <el-button v-if="viewForm.executionResult === '失败'" type="warning" @click="handleRetry(viewForm)">重试操作</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listLog, getLog, delLog, addLog, updateLog } from "@/api/guide/log"
import * as echarts from 'echarts'

export default {
  name: "Log",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 管控日志表格数据
      logList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        operationTime: null,
        operationType: null,
        deviceId: null,
        controlContent: null,
        executionResult: null,
        billId: null,
        operatorId: null
      },
      // 表单参数
      form: {},
      // 日期范围
      dateRange: [],
      // 趋势图时间范围
      chartTimeRange: 'week',
      // 日志详情弹窗
      logDetailOpen: false,
      viewForm: {},
      // 统计数据
      todayOperations: 0,
      successOperations: 0,
      failOperations: 0,
      successRate: 0,
      deviceCount: 0,
      // 趋势图实例
      logChart: null
    }
  },
  created() {
    this.getList();
    this.getStatistics();
  },
  mounted() {
    this.$nextTick(() => {
      this.initLogChart();
      window.addEventListener('resize', this.resizeChart);
    });
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeChart);
    if (this.logChart) {
      this.logChart.dispose();
    }
  },
  methods: {
    /** 查询管控日志列表 */
    getList() {
      this.loading = true
      listLog(this.queryParams).then(response => {
        this.logList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 获取统计数据
    getStatistics() {
      // 这里是模拟数据，实际项目中应该通过API获取
      this.todayOperations = 42;
      this.successOperations = 38;
      this.failOperations = 4;
      this.successRate = Math.round((this.successOperations / this.todayOperations) * 100);
      this.deviceCount = 17;
    },
    // 初始化趋势图
    initLogChart() {
      this.logChart = echarts.init(this.$refs.logChart);
      this.updateLogChart();
    },
    // 更新趋势图数据
    updateLogChart() {
      let xData = [];
      let successData = [];
      let failData = [];
      
      // 根据选择的时间范围生成不同的图表数据
      if (this.chartTimeRange === 'today') {
        // 今日数据，按小时统计
        for (let i = 0; i < 24; i++) {
          xData.push(i + ':00');
          // 模拟数据
          successData.push(Math.floor(Math.random() * 5));
          failData.push(Math.floor(Math.random() * 2));
        }
      } else if (this.chartTimeRange === 'week') {
        // 本周数据，按天统计
        const days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
        xData = days;
        
        // 模拟数据
        successData = [12, 8, 10, 15, 9, 3, 6];
        failData = [1, 1, 2, 0, 0, 0, 1];
      } else {
        // 本月数据，按天统计
        const daysInMonth = 30;
        for (let i = 1; i <= daysInMonth; i++) {
          xData.push(i + '日');
          // 模拟数据
          successData.push(Math.floor(Math.random() * 15 + 5));
          failData.push(Math.floor(Math.random() * 3));
        }
      }
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['成功操作', '失败操作']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: xData,
            axisTick: {
              alignWithLabel: true
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '操作次数'
          }
        ],
        series: [
          {
            name: '成功操作',
            type: 'bar',
            stack: '操作',
            emphasis: {
              focus: 'series'
            },
            data: successData,
            itemStyle: {
              color: '#67C23A'
            },
          },
          {
            name: '失败操作',
            type: 'bar',
            stack: '操作',
            emphasis: {
              focus: 'series'
            },
            data: failData,
            itemStyle: {
              color: '#F56C6C'
            },
          }
        ]
      };
      
      this.logChart.setOption(option);
    },
    // 重置图表大小
    resizeChart() {
      if (this.logChart) {
        this.logChart.resize();
      }
    },
    // 日期范围变更处理
    handleDateRangeChange(dates) {
      if (dates) {
        this.queryParams.startDate = dates[0];
        this.queryParams.endDate = dates[1];
      } else {
        this.queryParams.startDate = null;
        this.queryParams.endDate = null;
      }
    },
    // 批量删除
    handleBatchDelete() {
      const ids = this.ids.join(',');
      this.$modal.confirm('确认删除选中的' + this.ids.length + '条日志记录吗？').then(() => {
        return delLog(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    // 查看日志详情
    handleView(row) {
      getLog(row.id).then(response => {
        this.viewForm = response.data;
        // 添加一些额外字段示例
        this.viewForm.executionDetail = this.viewForm.executionDetail || 
          `操作发生时间: ${this.formatDateTime(this.viewForm.operationTime)}
执行设备: ${this.viewForm.deviceId}
控制内容: ${this.viewForm.controlContent}
执行状态: ${this.viewForm.executionResult}
执行耗时: 189ms`; 
          
        this.viewForm.operatorIp = '192.168.1.' + Math.floor(Math.random() * 255);
        this.logDetailOpen = true;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        operationTime: null,
        operationType: null,
        deviceId: null,
        controlContent: null,
        executionResult: null,
        billId: null,
        operatorId: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.dateRange = []
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加管控日志"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getLog(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改管控日志"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateLog(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addLog(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除管控日志编号为"' + ids + '"的数据项？').then(function() {
        return delLog(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('guide/log/export', {
        ...this.queryParams
      }, `log_${new Date().getTime()}.xlsx`)
    },
    // 日期格式化
    parseDate(timestamp) {
      return this.parseTime(timestamp, '{y}-{m}-{d}')
    },
    parseTime(timestamp, pattern) {
      if (!timestamp) return ''
      const date = new Date(timestamp)
      const formatObj = {
        y: date.getFullYear(),
        m: (date.getMonth() + 1).toString().padStart(2, '0'),
        d: date.getDate().toString().padStart(2, '0'),
        h: date.getHours().toString().padStart(2, '0'),
        i: date.getMinutes().toString().padStart(2, '0'),
        s: date.getSeconds().toString().padStart(2, '0')
      }
      const timeStr = pattern.replace(/{(y|m|d|h|i|s)+}/g, (result, key) => {
        return formatObj[key]
      })
      return timeStr || '{h}:{i}:{s}'
    },
    formatDateTime(timestamp) {
      return this.parseTime(timestamp, '{y}-{m}-{d} {h}:{i}:{s}')
    },
    // 获取操作类型对应的标签类型
    getOperationTypeTag(type) {
      const typeMap = {
        '语音播报': 'primary',
        '灯光控制': 'warning',
        'AR导航': 'info'
      }
      return typeMap[type] || 'info'
    },
    // 截断内容文本
    truncateContent(content) {
      if (!content) return '无内容'
      return content.length > 20 ? content.substr(0, 20) + '...' : content
    },
    // 格式化内容
    formatContent(content) {
      if (!content) return '无内容'
      // 替换换行符为<br>
      return content.replace(/\n/g, '<br>')
    },
    // 打开设备详情
    openDeviceDetail(deviceId) {
      if (!deviceId) return
      this.$message.info(`查看设备详情: ${deviceId}`)
      // 实际项目中这里应该跳转到设备详情页
    },
    // 打开单据详情
    openBillDetail(billId) {
      if (!billId) return
      this.$message.info(`查看单据详情: ${billId}`)
      // 实际项目中这里应该跳转到单据详情页
    },
    // 获取用户头像
    getUserAvatar(userId) {
      // 这里返回一个随机的头像URL，实际项目中应该根据用户ID获取
      return `https://i.pravatar.cc/100?u=${userId || '0'}`
    },
    // 获取用户名称
    getUserName(userId) {
      // 模拟数据
      const userMap = {
        '1': '管理员',
        '2': '张三',
        '3': '李四',
        '4': '王五'
      }
      return userMap[userId] || `用户${userId}`
    },
    // 获取用户部门
    getUserDepartment(userId) {
      // 模拟数据
      const deptMap = {
        '1': '系统管理部',
        '2': '仓储部',
        '3': '物流部',
        '4': '运营部'
      }
      return deptMap[userId] || '未知部门'
    },
    // 重试操作
    handleRetry(row) {
      this.$confirm('确定要重新执行该操作吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 模拟重试操作
        this.$message({
          type: 'success',
          message: `已重新发送操作指令至设备 ${row.deviceId}`
        })
        // 如果是查看详情页面中的重试，则关闭详情页面
        if (this.logDetailOpen) {
          this.logDetailOpen = false
        }
      }).catch(() => {})
    }
  }
}
</script>

<style scoped>
.dashboard-stats {
  margin-bottom: 20px;
}
.stats-card {
  display: flex;
  align-items: center;
  padding: 10px;
}
.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30px;
  margin-right: 15px;
}
.stats-icon.today { background-color: rgba(64, 158, 255, 0.2); color: #409EFF; }
.stats-icon.success { background-color: rgba(103, 194, 58, 0.2); color: #67C23A; }
.stats-icon.fail { background-color: rgba(245, 108, 108, 0.2); color: #F56C6C; }
.stats-icon.device { background-color: rgba(144, 147, 153, 0.2); color: #909399; }
.stats-info .stats-title { font-size: 14px; color: #909399; }
.stats-info .stats-number { font-size: 20px; font-weight: bold; margin-top: 5px; }
.stats-info .stats-rate { font-size: 12px; color: #67C23A; }

.chart-card, .search-card, .table-card {
  margin-bottom: 20px;
}

.time-column {
  display: flex;
  flex-direction: column;
}
.time-detail {
  font-size: 12px;
  color: #909399;
  margin-top: 3px;
}

.user-card {
  display: flex;
  align-items: center;
}
.avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 10px;
}
.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.user-info .username {
  font-weight: bold;
  margin-bottom: 5px;
}
.user-info .department {
  font-size: 12px;
  color: #909399;
}

.user-inline {
  display: flex;
  align-items: center;
}
.avatar-small {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 5px;
}
.avatar-small img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.execution-detail {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  font-family: monospace;
  max-height: 200px;
  overflow-y: auto;
  font-size: 12px;
}
</style>
