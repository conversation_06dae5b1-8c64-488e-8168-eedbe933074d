import request from '@/utils/request'

// EMQX系统设置API

/**
 * 获取系统设置
 */
export function getSystemSettings() {
  return request({
    url: '/emqx/settings',
    method: 'get'
  })
}

/**
 * 保存系统设置
 */
export function saveSystemSettings(data) {
  return request({
    url: '/emqx/settings',
    method: 'post',
    data
  })
}

/**
 * 获取基础配置
 */
export function getBasicSettings() {
  return request({
    url: '/emqx/settings/basic',
    method: 'get'
  })
}

/**
 * 保存基础配置
 */
export function saveBasicSettings(data) {
  return request({
    url: '/emqx/settings/basic',
    method: 'post',
    data
  })
}

/**
 * 获取客户端配置
 */
export function getClientSettings() {
  return request({
    url: '/emqx/settings/client',
    method: 'get'
  })
}

/**
 * 保存客户端配置
 */
export function saveClientSettings(data) {
  return request({
    url: '/emqx/settings/client',
    method: 'post',
    data
  })
}

/**
 * 获取监控配置
 */
export function getMonitoringSettings() {
  return request({
    url: '/emqx/settings/monitoring',
    method: 'get'
  })
}

/**
 * 保存监控配置
 */
export function saveMonitoringSettings(data) {
  return request({
    url: '/emqx/settings/monitoring',
    method: 'post',
    data
  })
}

/**
 * 获取数据分流配置
 */
export function getDatastreamSettings() {
  return request({
    url: '/emqx/settings/datastream',
    method: 'get'
  })
}

/**
 * 保存数据分流配置
 */
export function saveDatastreamSettings(data) {
  return request({
    url: '/emqx/settings/datastream',
    method: 'post',
    data
  })
}

/**
 * 获取清理配置
 */
export function getCleanupSettings() {
  return request({
    url: '/emqx/settings/cleanup',
    method: 'get'
  })
}

/**
 * 保存清理配置
 */
export function saveCleanupSettings(data) {
  return request({
    url: '/emqx/settings/cleanup',
    method: 'post',
    data
  })
}

/**
 * 测试EMQX连接
 */
export function testEmqxConnection(config) {
  return request({
    url: '/emqx/settings/test-connection',
    method: 'post',
    data: config
  })
}

/**
 * 测试数据库连接
 */
export function testDatabaseConnection(config) {
  return request({
    url: '/emqx/settings/test-database',
    method: 'post',
    data: config
  })
}

/**
 * 测试邮件配置
 */
export function testEmailConfig(config) {
  return request({
    url: '/emqx/settings/test-email',
    method: 'post',
    data: config
  })
}

/**
 * 获取系统信息
 */
export function getSystemInfo() {
  return request({
    url: '/emqx/settings/system-info',
    method: 'get'
  })
}

/**
 * 重置设置到默认值
 */
export function resetToDefaults(category) {
  return request({
    url: '/emqx/settings/reset-defaults',
    method: 'post',
    data: { category }
  })
}

/**
 * 导出系统配置
 */
export function exportSettings() {
  return request({
    url: '/emqx/settings/export',
    method: 'get',
    responseType: 'blob'
  })
}

/**
 * 导入系统配置
 */
export function importSettings(file) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/emqx/settings/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
