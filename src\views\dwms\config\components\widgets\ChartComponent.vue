<template>
  <div class="chart-component" :style="{ width: width + 'px', height: height + 'px' }">
    <div ref="chart" :style="{ width: '100%', height: '100%' }"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'ChartComponent',
  props: {
    config: {
      type: Object,
      default: () => ({})
    },
    data: {
      type: Object,
      default: () => ({})
    },
    width: {
      type: Number,
      default: 400
    },
    height: {
      type: Number,
      default: 300
    }
  },
  data() {
    return {
      chart: null
    }
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
  },
  watch: {
    data: {
      handler() {
        this.updateChart()
      },
      deep: true
    },
    width() {
      this.$nextTick(() => {
        if (this.chart) {
          this.chart.resize()
        }
      })
    },
    height() {
      this.$nextTick(() => {
        if (this.chart) {
          this.chart.resize()
        }
      })
    }
  },
  methods: {
    // 初始化图表
    initChart() {
      if (!this.$refs.chart) return
      
      this.chart = echarts.init(this.$refs.chart)
      this.updateChart()
    },
    
    // 更新图表
    updateChart() {
      if (!this.chart) return
      
      let option = {}
      
      switch (this.config.type) {
        case 'line':
          option = this.getLineChartOption()
          break
        case 'bar':
          option = this.getBarChartOption()
          break
        case 'pie':
          option = this.getPieChartOption()
          break
        case 'area':
          option = this.getAreaChartOption()
          break
        default:
          option = this.getLineChartOption()
      }
      
      this.chart.setOption(option, true)
    },
    
    // 折线图配置
    getLineChartOption() {
      return {
        title: {
          text: this.config.title || '',
          left: 'center',
          textStyle: {
            fontSize: 14,
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        legend: {
          bottom: 0,
          textStyle: {
            fontSize: 12
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.data?.xAxis || [],
          axisLabel: {
            fontSize: 10
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 10
          }
        },
        series: (this.data?.series || []).map((item, index) => ({
          name: item.name,
          type: 'line',
          data: item.data,
          smooth: true,
          lineStyle: {
            color: this.getSeriesColor(index)
          },
          itemStyle: {
            color: this.getSeriesColor(index)
          }
        }))
      }
    },
    
    // 柱状图配置
    getBarChartOption() {
      return {
        title: {
          text: this.config.title || '',
          left: 'center',
          textStyle: {
            fontSize: 14,
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          bottom: 0,
          textStyle: {
            fontSize: 12
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.data?.xAxis || [],
          axisLabel: {
            fontSize: 10
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 10
          }
        },
        series: (this.data?.series || []).map((item, index) => ({
          name: item.name,
          type: 'bar',
          data: item.data,
          itemStyle: {
            color: this.getSeriesColor(index)
          }
        }))
      }
    },
    
    // 饼图配置
    getPieChartOption() {
      return {
        title: {
          text: this.config.title || '',
          left: 'center',
          textStyle: {
            fontSize: 14,
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          bottom: 0,
          textStyle: {
            fontSize: 12
          }
        },
        series: [
          {
            name: this.config.title || '数据',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['50%', '45%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '16',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: this.data?.series || []
          }
        ]
      }
    },
    
    // 面积图配置
    getAreaChartOption() {
      return {
        title: {
          text: this.config.title || '',
          left: 'center',
          textStyle: {
            fontSize: 14,
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        legend: {
          bottom: 0,
          textStyle: {
            fontSize: 12
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.data?.xAxis || [],
          axisLabel: {
            fontSize: 10
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 10
          }
        },
        series: (this.data?.series || []).map((item, index) => ({
          name: item.name,
          type: 'line',
          data: item.data,
          smooth: true,
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0, color: this.getSeriesColor(index, 0.8)
              }, {
                offset: 1, color: this.getSeriesColor(index, 0.1)
              }]
            }
          },
          lineStyle: {
            color: this.getSeriesColor(index)
          },
          itemStyle: {
            color: this.getSeriesColor(index)
          }
        }))
      }
    },
    
    // 获取系列颜色
    getSeriesColor(index, alpha = 1) {
      const colors = this.config.color || [
        '#409EFF', '#67C23A', '#E6A23C', '#F56C6C', 
        '#909399', '#C0C4CC', '#606266', '#303133'
      ]
      
      const color = Array.isArray(colors) ? colors[index % colors.length] : colors
      
      if (alpha < 1) {
        // 转换为rgba格式
        const hex = color.replace('#', '')
        const r = parseInt(hex.substr(0, 2), 16)
        const g = parseInt(hex.substr(2, 2), 16)
        const b = parseInt(hex.substr(4, 2), 16)
        return `rgba(${r}, ${g}, ${b}, ${alpha})`
      }
      
      return color
    }
  }
}
</script>

<style lang="scss" scoped>
.chart-component {
  background: #fff;
  border-radius: 4px;
  overflow: hidden;
}
</style>
