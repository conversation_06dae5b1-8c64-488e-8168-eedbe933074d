<template>
  <div class="app-container">
    <el-card class="filter-container" shadow="hover">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
        <el-form-item label="货架编号" prop="rackCode">
          <el-input
            v-model="queryParams.rackCode"
            placeholder="请输入货架编号"
            clearable
            prefix-icon="el-icon-document"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="货架名称" prop="rackName">
          <el-input
            v-model="queryParams.rackName"
            placeholder="请输入货架名称"
            clearable
            prefix-icon="el-icon-goods"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="所属区域" prop="zoneId">
          <el-select v-model="queryParams.zoneId" placeholder="请选择所属区域" clearable style="width: 200px">
            <el-option
              v-for="item in zoneOptions"
              :key="item.id"
              :label="item.zoneName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="货架状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option label="正常" value="0" />
            <el-option label="停用" value="1" />
            <el-option label="故障" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="table-container" shadow="hover" style="margin-top: 10px;">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['warehouse:rack:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['warehouse:rack:edit']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['warehouse:rack:remove']"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['warehouse:rack:export']"
          >导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="rackList" @selection-change="handleSelectionChange" border stripe highlight-current-row>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="货架ID" align="center" prop="id" width="80" />
        <el-table-column label="货架编号" align="center" prop="rackCode" min-width="100" />
        <el-table-column label="货架名称" align="center" prop="rackName" min-width="120" show-overflow-tooltip />
        <el-table-column label="所属区域" align="center" prop="zoneName" min-width="120" show-overflow-tooltip />
        <el-table-column label="位置" align="center" width="100">
          <template slot-scope="scope">
            {{ scope.row.rowNum }}行{{ scope.row.columnNum }}列
          </template>
        </el-table-column>
        <el-table-column label="层数" align="center" prop="layerCount" width="80" />
        <el-table-column label="尺寸(cm)" align="center" width="120">
          <template slot-scope="scope">
            {{ scope.row.length }}×{{ scope.row.width }}×{{ scope.row.height }}
          </template>
        </el-table-column>
        <el-table-column label="最大承重(kg)" align="center" prop="maxWeight" width="120" />
        <el-table-column label="状态" align="center" prop="status" width="80">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status == '0'" type="success">正常</el-tag>
            <el-tag v-else-if="scope.row.status == '1'" type="info">停用</el-tag>
            <el-tag v-else-if="scope.row.status == '2'" type="danger">故障</el-tag>
            <el-tag v-else>{{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
              v-hasPermi="['warehouse:rack:query']"
            >详情</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['warehouse:rack:edit']"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['warehouse:rack:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改货架信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="货架编号" prop="rackCode">
              <el-input v-model="form.rackCode" placeholder="请输入货架编号" :disabled="formDisabled" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="货架名称" prop="rackName">
              <el-input v-model="form.rackName" placeholder="请输入货架名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属区域" prop="zoneId">
              <el-select v-model="form.zoneId" placeholder="请选择所属区域" style="width: 100%" @change="handleZoneChange">
                <el-option
                  v-for="item in zoneOptions"
                  :key="item.id"
                  :label="`${item.zoneCode} - ${item.zoneName}`"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="货架状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择货架状态" style="width: 100%">
                <el-option label="正常" value="0" />
                <el-option label="停用" value="1" />
                <el-option label="故障" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="行号" prop="rowNum">
              <el-input-number v-model="form.rowNum" :min="1" :precision="0" :step="1" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="列号" prop="columnNum">
              <el-input-number v-model="form.columnNum" :min="1" :precision="0" :step="1" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="层数" prop="layerCount">
              <el-input-number v-model="form.layerCount" :min="1" :precision="0" :step="1" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最大承重(kg)" prop="maxWeight">
              <el-input-number v-model="form.maxWeight" :min="0" :precision="1" :step="100" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="长度(cm)" prop="length">
              <el-input-number v-model.number="form.length" :min="0" :precision="1" :step="10" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="宽度(cm)" prop="width">
              <el-input-number v-model.number="form.width" :min="0" :precision="1" :step="10" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="高度(cm)" prop="height">
              <el-input-number v-model.number="form.height" :min="0" :precision="1" :step="10" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="智能灯控ID" prop="deviceId">
          <el-input v-model="form.deviceId" placeholder="请输入智能灯控ID" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 货架详情对话框 -->
    <el-dialog title="货架详情" :visible.sync="viewDialog" width="700px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="货架编号">{{ viewForm.rackCode }}</el-descriptions-item>
        <el-descriptions-item label="货架名称">{{ viewForm.rackName }}</el-descriptions-item>
        <el-descriptions-item label="所属区域">{{ viewForm.zoneName }}</el-descriptions-item>
        <el-descriptions-item label="位置">{{ viewForm.rowNum }}行{{ viewForm.columnNum }}列</el-descriptions-item>
        <el-descriptions-item label="层数">{{ viewForm.layerCount }}层</el-descriptions-item>
        <el-descriptions-item label="最大承重">{{ viewForm.maxWeight }} kg</el-descriptions-item>
        <el-descriptions-item label="尺寸(长×宽×高)">{{ viewForm.length || 0 }}×{{ viewForm.width || 0 }}×{{ viewForm.height || 0 }} cm</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag v-if="viewForm.status == '0'" type="success">正常</el-tag>
          <el-tag v-else-if="viewForm.status == '1'" type="info">停用</el-tag>
          <el-tag v-else-if="viewForm.status == '2'" type="danger">故障</el-tag>
          <el-tag v-else>{{ viewForm.status }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="智能灯控ID">{{ viewForm.deviceId || '-' }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ viewForm.createTime ? parseTime(viewForm.createTime) : '-' }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ viewForm.remark }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewDialog = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listRack, getRack, delRack, addRack, updateRack } from "@/api/warehouse/rack"
import { listZone } from "@/api/warehouse/zone"

export default {
  name: "Rack",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 货架信息表格数据
      rackList: [],
      // 库区选项
      zoneOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示详情弹出层
      viewDialog: false,
      // 详情表单
      viewForm: {},
      // 表单禁用状态
      formDisabled: false,
      // 记录区域编号和名称
      selectedZoneCode: '',
      // 原始货架编号（不带区域前缀）
      originalRackCode: '',
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        rackCode: null,
        rackName: null,
        zoneId: null,
        rowNum: null,
        columnNum: null,
        layerCount: null,
        maxWeight: null,
        length: null,
        width: null,
        height: null,
        deviceId: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        rackCode: [
          { required: true, message: "货架编号不能为空", trigger: "blur" }
        ],
        rackName: [
          { required: true, message: "货架名称不能为空", trigger: "blur" }
        ],
        zoneId: [
          { required: true, message: "所属区域不能为空", trigger: "change" }
        ],
        rowNum: [
          { required: true, message: "行号不能为空", trigger: "blur" }
        ],
        columnNum: [
          { required: true, message: "列号不能为空", trigger: "blur" }
        ],
        layerCount: [
          { required: true, message: "层数不能为空", trigger: "blur" }
        ]
      },
    };
  },
  created() {
    this.getList();
    this.getZoneOptions();
  },
  methods: {
    /** 查询货架信息列表 */
    getList() {
      this.loading = true;
      listRack(this.queryParams).then(response => {
        this.rackList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询库区选项 */
    getZoneOptions() {
      listZone().then(response => {
        this.zoneOptions = response.rows;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        rackCode: null,
        rackName: null,
        zoneId: null,
        rowNum: 1,
        columnNum: 1,
        layerCount: 1,
        maxWeight: 1000,
        length: 100,
        width: 100,
        height: 200,
        deviceId: null,
        status: "0",
        remark: null
      };
      this.formDisabled = false;
      this.selectedZoneCode = '';
      this.originalRackCode = '';
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加货架信息";
      this.formDisabled = false;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getRack(id).then(response => {
        this.form = response.data;
        
        // 如果货架编号包含分隔符，记录原始部分
        if (this.form.rackCode && this.form.rackCode.includes('-')) {
          const parts = this.form.rackCode.split('-', 2);
          if (parts.length > 1) {
            this.selectedZoneCode = parts[0];
            this.originalRackCode = parts[1];
          }
        }
        
        this.open = true;
        this.title = "修改货架信息";
        this.formDisabled = true;
      });
    },
    /** 查看详情按钮操作 */
    handleView(row) {
      this.viewForm = {};
      getRack(row.id).then(response => {
        this.viewForm = response.data;
        this.viewDialog = true;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 确保设置区域名称
          if (this.form.zoneId) {
            const selectedZone = this.zoneOptions.find(zone => zone.id === this.form.zoneId);
            if (selectedZone) {
              this.form.zoneName = selectedZone.zoneName;
              this.form.zoneCode = selectedZone.zoneCode;
              console.log("设置区域名称:", this.form.zoneName);
            }
          }
          
          // 确保长宽高字段有值
          this.form.length = this.form.length || 0;
          this.form.width = this.form.width || 0;
          this.form.height = this.form.height || 0;
          
          console.log("提交的表单数据:", this.form);
          
          if (this.form.id != null) {
            updateRack(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              console.error("修改失败:", error);
            });
          } else {
            addRack(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              console.error("新增失败:", error);
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除货架信息编号为"' + ids + '"的数据项？').then(() => {
        return delRack(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('warehouse/rack/export', {
        ...this.queryParams
      }, `rack_${new Date().getTime()}.xlsx`)
    },
    /** 区域选择变更处理 */
    handleZoneChange(zoneId) {
      if (!zoneId) {
        return;
      }
      
      // 查找选中的区域信息
      const selectedZone = this.zoneOptions.find(zone => zone.id === zoneId);
      if (!selectedZone) {
        return;
      }
      
      // 记录区域编号和名称
      this.selectedZoneCode = selectedZone.zoneCode;
      
      // 处理货架编号前缀
      if (this.form.rackCode) {
        // 如果已有货架编号，检查是否需要更新前缀
        if (this.form.rackCode.includes('-')) {
          // 如果包含分隔符，说明已经有前缀，需要替换
          const parts = this.form.rackCode.split('-');
          if (parts.length > 1) {
            this.form.rackCode = selectedZone.zoneCode + '-' + parts[1];
          }
        } else {
          // 没有分隔符，直接添加前缀
          this.form.rackCode = selectedZone.zoneCode + '-' + this.form.rackCode;
        }
      } else {
        // 如果没有货架编号，设置默认值
        this.form.rackCode = selectedZone.zoneCode + '-R';
      }
    }
  }
};
</script>

<style scoped>
.filter-container {
  margin-bottom: 10px;
}
.table-container {
  margin-bottom: 20px;
}
.el-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>
