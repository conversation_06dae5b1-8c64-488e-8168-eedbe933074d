// MQTT Broker 管理系统菜单配置

export const mqttMenuConfig = {
  // 主菜单配置
  mainMenu: {
    path: '/mqtt',
    name: 'Mqtt',
    title: 'MQTT Broker',
    icon: 'message',
    roles: ['admin', 'mqtt_admin'],
    redirect: '/mqtt/dashboard'
  },
  
  // 子菜单配置
  subMenus: [
    {
      path: 'dashboard',
      name: 'MqttDashboard',
      title: '系统概览',
      icon: 'dashboard',
      component: '@/views/mqtt/dashboard/index',
      meta: {
        affix: true,
        noCache: false,
        description: '查看 MQTT Broker 系统整体状态和关键指标'
      }
    },
    {
      path: 'clients',
      name: 'MqttClients',
      title: '客户端管理',
      icon: 'connection',
      component: '@/views/mqtt/clients/index',
      meta: {
        perms: ['mqtt:client:list'],
        description: '管理在线 MQTT 客户端的连接和状态'
      }
    },
    {
      path: 'messages',
      name: 'MqttMessages',
      title: '消息管理',
      icon: 'message',
      component: '@/views/mqtt/messages/index',
      meta: {
        perms: ['mqtt:message:list'],
        description: '管理 MQTT 消息历史、保留消息和发布'
      }
    },
    {
      path: 'auth',
      name: 'MqttAuth',
      title: '认证管理',
      icon: 'key',
      component: '@/views/mqtt/auth/index',
      meta: {
        perms: ['mqtt:auth:list'],
        description: '管理 MQTT 客户端的认证凭据和权限'
      }
    },
    {
      path: 'topics',
      name: 'MqttTopics',
      title: '主题管理',
      icon: 'tree-table',
      component: '@/views/mqtt/topics/index',
      meta: {
        perms: ['mqtt:topic:list'],
        description: '管理 MQTT 主题的订阅和发布统计'
      }
    },
    {
      path: 'monitoring',
      name: 'MqttMonitoring',
      title: '实时监控',
      icon: 'monitor',
      component: '@/views/mqtt/monitoring/index',
      meta: {
        perms: ['mqtt:monitor:view'],
        description: '实时监控 MQTT 服务器性能和连接状态'
      }
    },
    {
      path: 'settings',
      name: 'MqttSettings',
      title: '系统设置',
      icon: 'setting',
      component: '@/views/mqtt/settings/index',
      meta: {
        perms: ['mqtt:settings:manage'],
        description: '配置 MQTT Broker 系统参数和运行设置'
      }
    }
  ]
}

// 权限配置
export const mqttPermissions = {
  // 客户端管理权限
  client: {
    list: 'mqtt:client:list',
    view: 'mqtt:client:view',
    disconnect: 'mqtt:client:disconnect',
    export: 'mqtt:client:export'
  },
  
  // 消息管理权限
  message: {
    list: 'mqtt:message:list',
    view: 'mqtt:message:view',
    publish: 'mqtt:message:publish',
    delete: 'mqtt:message:delete',
    export: 'mqtt:message:export'
  },
  
  // 认证管理权限
  auth: {
    list: 'mqtt:auth:list',
    create: 'mqtt:auth:create',
    edit: 'mqtt:auth:edit',
    delete: 'mqtt:auth:delete',
    export: 'mqtt:auth:export'
  },
  
  // 主题管理权限
  topic: {
    list: 'mqtt:topic:list',
    view: 'mqtt:topic:view',
    manage: 'mqtt:topic:manage'
  },
  
  // 监控权限
  monitor: {
    view: 'mqtt:monitor:view',
    export: 'mqtt:monitor:export'
  },
  
  // 系统设置权限
  settings: {
    view: 'mqtt:settings:view',
    manage: 'mqtt:settings:manage'
  }
}

// 菜单图标配置
export const mqttMenuIcons = {
  dashboard: 'el-icon-dashboard',
  connection: 'el-icon-connection',
  message: 'el-icon-message',
  key: 'el-icon-key',
  'tree-table': 'el-icon-menu',
  monitor: 'el-icon-monitor',
  setting: 'el-icon-setting'
}

// 快速操作配置
export const mqttQuickActions = {
  dashboard: [
    {
      name: '查看客户端',
      icon: 'el-icon-connection',
      path: '/mqtt/clients',
      permission: 'mqtt:client:list'
    },
    {
      name: '发布消息',
      icon: 'el-icon-message',
      path: '/mqtt/messages',
      permission: 'mqtt:message:publish'
    },
    {
      name: '管理认证',
      icon: 'el-icon-key',
      path: '/mqtt/auth',
      permission: 'mqtt:auth:list'
    },
    {
      name: '系统设置',
      icon: 'el-icon-setting',
      path: '/mqtt/settings',
      permission: 'mqtt:settings:manage'
    }
  ]
}

export default mqttMenuConfig
