<template>
  <div class="workflow-preview">
    <div v-if="levels && levels.length > 0" class="flow-container">
      <!-- 开始节点 -->
      <div class="flow-node start-node">
        <div class="node-content">
          <i class="el-icon-user"></i>
          <div class="node-title">发起申请</div>
        </div>
      </div>

      <!-- 连接线 -->
      <div class="flow-arrow">
        <i class="el-icon-arrow-right"></i>
      </div>

      <!-- 审批节点 -->
      <div v-for="(level, index) in levels" :key="index" class="flow-section">
        <div class="flow-node approval-node">
          <div class="node-content">
            <i class="el-icon-s-check"></i>
            <div class="node-title">{{ level.levelName }}</div>
            <div class="node-subtitle">
              <span v-if="level.approvalType === '0'">指定人员</span>
              <span v-else-if="level.approvalType === '1'">指定角色</span>
              <span v-else-if="level.approvalType === '2'">指定部门</span>
              <span v-else-if="level.approvalType === '3'">部门主管</span>
              <span v-else-if="level.approvalType === '4'">上级主管</span>
            </div>
            <div class="node-detail">
              <el-tag size="mini" :type="getConditionType(level.approvalCondition)">
                {{ getConditionText(level.approvalCondition) }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 连接线（非最后一个节点） -->
        <div v-if="index < levels.length - 1" class="flow-arrow">
          <i class="el-icon-arrow-right"></i>
        </div>
      </div>

      <!-- 连接线到结束 -->
      <div class="flow-arrow">
        <i class="el-icon-arrow-right"></i>
      </div>

      <!-- 结束节点 -->
      <div class="flow-node end-node">
        <div class="node-content">
          <i class="el-icon-circle-check"></i>
          <div class="node-title">审批完成</div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-flow">
      <el-empty description="暂无审批流程" :image-size="80">
        <span>请添加审批级别来构建流程</span>
      </el-empty>
    </div>

    <!-- 流程统计 -->
    <div v-if="levels && levels.length > 0" class="flow-stats">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ levels.length }}</div>
            <div class="stat-label">审批级别</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ totalApprovers }}</div>
            <div class="stat-label">审批人数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ estimatedTime }}</div>
            <div class="stat-label">预计时长(小时)</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ complexityLevel }}</div>
            <div class="stat-label">复杂度</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 流程详情 -->
    <div v-if="levels && levels.length > 0" class="flow-details">
      <el-collapse>
        <el-collapse-item title="查看详细配置" name="details">
          <el-table :data="levels" size="small">
            <el-table-column prop="level" label="级别" width="60" align="center" />
            <el-table-column prop="levelName" label="名称" width="120" />
            <el-table-column label="审批类型" width="100">
              <template slot-scope="scope">
                <span v-if="scope.row.approvalType === '0'">指定人员</span>
                <span v-else-if="scope.row.approvalType === '1'">指定角色</span>
                <span v-else-if="scope.row.approvalType === '2'">指定部门</span>
                <span v-else-if="scope.row.approvalType === '3'">部门主管</span>
                <span v-else-if="scope.row.approvalType === '4'">上级主管</span>
              </template>
            </el-table-column>
            <el-table-column label="审批条件" width="100">
              <template slot-scope="scope">
                <el-tag size="mini" :type="getConditionType(scope.row.approvalCondition)">
                  {{ getConditionText(scope.row.approvalCondition) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="timeoutHours" label="超时时间" width="80" align="center">
              <template slot-scope="scope">
                {{ scope.row.timeoutHours }}h
              </template>
            </el-table-column>
            <el-table-column label="超时处理" width="100">
              <template slot-scope="scope">
                <span v-if="scope.row.timeoutAction === '0'">无操作</span>
                <span v-else-if="scope.row.timeoutAction === '1'">自动通过</span>
                <span v-else-if="scope.row.timeoutAction === '2'">自动拒绝</span>
                <span v-else-if="scope.row.timeoutAction === '3'">转交上级</span>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="描述" show-overflow-tooltip />
          </el-table>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script>
export default {
  name: "WorkflowPreview",
  props: {
    levels: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    /** 总审批人数 */
    totalApprovers() {
      return this.levels.reduce((total, level) => {
        if (level.approvalType === '0' && level.approvers) {
          return total + level.approvers.split(',').length;
        }
        return total + 1; // 角色、部门等按1人计算
      }, 0);
    },

    /** 预计总时长 */
    estimatedTime() {
      return this.levels.reduce((total, level) => {
        return total + (level.timeoutHours || 24);
      }, 0);
    },

    /** 复杂度等级 */
    complexityLevel() {
      const levelCount = this.levels.length;
      if (levelCount <= 1) return '简单';
      if (levelCount <= 3) return '中等';
      if (levelCount <= 5) return '复杂';
      return '极复杂';
    }
  },
  methods: {
    /** 获取条件类型 */
    getConditionType(condition) {
      switch (condition) {
        case 'ANY': return 'success';
        case 'ALL': return 'warning';
        case 'RATIO': return 'info';
        default: return '';
      }
    },

    /** 获取条件文本 */
    getConditionText(condition) {
      switch (condition) {
        case 'ANY': return '任意一人';
        case 'ALL': return '全部同意';
        case 'RATIO': return '按比例';
        default: return '未知';
      }
    }
  }
};
</script>

<style scoped>
.workflow-preview {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.flow-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 30px;
}

.flow-section {
  display: flex;
  align-items: center;
  gap: 20px;
}

.flow-node {
  position: relative;
  min-width: 120px;
  min-height: 80px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.flow-node:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.start-node {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  color: white;
}

.approval-node {
  background: linear-gradient(135deg, #409eff, #66b1ff);
  color: white;
}

.end-node {
  background: linear-gradient(135deg, #e6a23c, #ebb563);
  color: white;
}

.node-content {
  text-align: center;
  padding: 10px;
}

.node-content i {
  font-size: 24px;
  margin-bottom: 8px;
  display: block;
}

.node-title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 4px;
}

.node-subtitle {
  font-size: 12px;
  opacity: 0.9;
  margin-bottom: 4px;
}

.node-detail {
  font-size: 10px;
}

.flow-arrow {
  color: #909399;
  font-size: 20px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

.empty-flow {
  text-align: center;
  padding: 40px;
  color: #909399;
}

.flow-stats {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.flow-details {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

@media (max-width: 768px) {
  .flow-container {
    flex-direction: column;
  }
  
  .flow-section {
    flex-direction: column;
  }
  
  .flow-arrow {
    transform: rotate(90deg);
  }
}
</style>
