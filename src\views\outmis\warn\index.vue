<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="物料编码" prop="materialCode">
        <el-input
          v-model="queryParams.materialCode"
          placeholder="请输入物料编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="物料名称" prop="materialName">
        <el-input
          v-model="queryParams.materialName"
          placeholder="请输入物料名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="处理状态" prop="processStatus">
        <el-select v-model="queryParams.processStatus" placeholder="请选择处理状态" clearable>
          <el-option label="未处理" value="0" />
          <el-option label="已处理" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="预警时间">
        <el-date-picker
          v-model="daterangeWarnTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-check"
          size="mini"
          :disabled="multiple"
          @click="handleBatchProcess"
          v-hasPermi="['outmis:warn:process']"
        >批量处理</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['outmis:warn:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['outmis:warn:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 预警列表 -->
    <el-table v-loading="loading" :data="warnList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="物料编码" align="center" prop="materialCode" />
      <el-table-column label="物料名称" align="center" prop="materialName" />
      <el-table-column label="实际重量(kg)" align="center" prop="weight" />
      <el-table-column label="标准重量(kg)" align="center" prop="standard" />
      <el-table-column label="差异(kg)" align="center" prop="diff">
        <template slot-scope="scope">
          <span :class="scope.row.diff > 0 ? 'text-danger' : 'text-success'">
            {{ scope.row.diff > 0 ? '+' : '' }}{{ scope.row.diff }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="预警时间" align="center" prop="warnTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.warnTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="处理状态" align="center" prop="processStatus">
        <template slot-scope="scope">
          <el-tag :type="scope.row.processStatus === '0' ? 'danger' : 'success'">
            {{ scope.row.processStatus === '0' ? '未处理' : '已处理' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="处理人" align="center" prop="processBy" />
      <el-table-column label="处理时间" align="center" prop="processTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.processTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.processStatus === '0'"
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleProcess(scope.row)"
            v-hasPermi="['outmis:warn:process']"
          >处理</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['outmis:warn:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 处理预警对话框 -->
    <el-dialog title="处理预警" :visible.sync="processOpen" width="500px" append-to-body>
      <el-form ref="processForm" :model="processForm" :rules="processRules" label-width="80px">
        <el-form-item label="物料编码">
          <el-input v-model="processForm.materialCode" disabled />
        </el-form-item>
        <el-form-item label="物料名称">
          <el-input v-model="processForm.materialName" disabled />
        </el-form-item>
        <el-form-item label="实际重量">
          <el-input v-model="processForm.weight" disabled />
        </el-form-item>
        <el-form-item label="标准重量">
          <el-input v-model="processForm.standard" disabled />
        </el-form-item>
        <el-form-item label="差异">
          <el-input v-model="processForm.diff" disabled />
        </el-form-item>
        <el-form-item label="处理意见" prop="processRemark">
          <el-input v-model="processForm.processRemark" type="textarea" placeholder="请输入处理意见" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitProcess">确 定</el-button>
        <el-button @click="cancelProcess">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 批量处理对话框 -->
    <el-dialog title="批量处理预警" :visible.sync="batchProcessOpen" width="400px" append-to-body>
      <el-form ref="batchProcessForm" :model="batchProcessForm" :rules="batchProcessRules" label-width="80px">
        <el-form-item label="处理意见" prop="processRemark">
          <el-input v-model="batchProcessForm.processRemark" type="textarea" placeholder="请输入处理意见" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitBatchProcess">确 定</el-button>
        <el-button @click="cancelBatchProcess">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog title="预警详情" :visible.sync="viewOpen" width="500px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="物料编码">{{ viewForm.materialCode }}</el-descriptions-item>
        <el-descriptions-item label="物料名称">{{ viewForm.materialName }}</el-descriptions-item>
        <el-descriptions-item label="实际重量">{{ viewForm.weight }} kg</el-descriptions-item>
        <el-descriptions-item label="标准重量">{{ viewForm.standard }} kg</el-descriptions-item>
        <el-descriptions-item label="差异">
          <span :class="viewForm.diff > 0 ? 'text-danger' : 'text-success'">
            {{ viewForm.diff > 0 ? '+' : '' }}{{ viewForm.diff }} kg
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="预警时间">{{ parseTime(viewForm.warnTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="处理状态">
          <el-tag :type="viewForm.processStatus === '0' ? 'danger' : 'success'">
            {{ viewForm.processStatus === '0' ? '未处理' : '已处理' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="处理人">{{ viewForm.processBy || '-' }}</el-descriptions-item>
        <el-descriptions-item label="处理时间">{{ parseTime(viewForm.processTime, '{y}-{m}-{d} {h}:{i}:{s}') || '-' }}</el-descriptions-item>
        <el-descriptions-item label="处理意见" :span="2">{{ viewForm.processRemark || '-' }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ viewForm.remark || '-' }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import { 
  listWarn, 
  getWarn, 
  delWarn, 
  processWarn,
  batchProcessWarn
} from "@/api/outmis/warn";

export default {
  name: "Warn",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 预警表格数据
      warnList: [],
      // 预警时间时间范围
      daterangeWarnTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        materialCode: null,
        materialName: null,
        processStatus: null,
      },
      // 处理表单参数
      processForm: {},
      // 处理表单显示
      processOpen: false,
      // 处理表单校验
      processRules: {
        processRemark: [
          { required: true, message: "处理意见不能为空", trigger: "blur" }
        ],
      },
      // 批量处理表单参数
      batchProcessForm: {},
      // 批量处理表单显示
      batchProcessOpen: false,
      // 批量处理表单校验
      batchProcessRules: {
        processRemark: [
          { required: true, message: "处理意见不能为空", trigger: "blur" }
        ],
      },
      // 查看表单参数
      viewForm: {},
      // 查看表单显示
      viewOpen: false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询预警列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeWarnTime && '' != this.daterangeWarnTime) {
        this.queryParams.params["beginWarnTime"] = this.daterangeWarnTime[0];
        this.queryParams.params["endWarnTime"] = this.daterangeWarnTime[1];
      }
      listWarn(this.queryParams).then(response => {
        this.warnList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeWarnTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.warnId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 处理按钮操作 */
    handleProcess(row) {
      this.processForm = Object.assign({}, row);
      this.processForm.processRemark = "";
      this.processOpen = true;
    },
    /** 提交处理 */
    submitProcess() {
      this.$refs["processForm"].validate(valid => {
        if (valid) {
          processWarn(this.processForm.warnId, this.processForm.processRemark).then(response => {
            this.$modal.msgSuccess("处理成功");
            this.processOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 取消处理 */
    cancelProcess() {
      this.processOpen = false;
      this.processForm = {};
    },
    /** 批量处理按钮操作 */
    handleBatchProcess() {
      this.batchProcessForm = { processRemark: "" };
      this.batchProcessOpen = true;
    },
    /** 提交批量处理 */
    submitBatchProcess() {
      this.$refs["batchProcessForm"].validate(valid => {
        if (valid) {
          batchProcessWarn(this.ids, this.batchProcessForm.processRemark).then(response => {
            this.$modal.msgSuccess("批量处理成功");
            this.batchProcessOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 取消批量处理 */
    cancelBatchProcess() {
      this.batchProcessOpen = false;
      this.batchProcessForm = {};
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.viewForm = Object.assign({}, row);
      this.viewOpen = true;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const warnIds = row.warnId || this.ids;
      this.$modal.confirm('是否确认删除预警编号为"' + warnIds + '"的数据项？').then(function() {
        return delWarn(warnIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('outmis/warn/export', {
        ...this.queryParams
      }, `warn_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped>
.text-danger {
  color: #f56c6c;
}
.text-success {
  color: #67c23a;
}
</style>
