<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>物料审批示例</span>
      </div>
      
      <!-- 物料表单 -->
      <el-form ref="materialForm" :model="materialForm" :rules="rules" label-width="120px">
        <el-form-item label="物料名称" prop="name">
          <el-input v-model="materialForm.name" placeholder="请输入物料名称" :disabled="isViewMode"/>
        </el-form-item>
        
        <el-form-item label="物料代码" prop="code">
          <el-input v-model="materialForm.code" placeholder="请输入物料代码" :disabled="isViewMode"/>
        </el-form-item>
        
        <el-form-item label="物料分类" prop="categoryId">
          <el-select v-model="materialForm.categoryId" placeholder="请选择物料分类" :disabled="isViewMode">
            <el-option v-for="item in categoryOptions" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        
        <el-form-item label="规格型号" prop="model">
          <el-input v-model="materialForm.model" placeholder="请输入规格型号" :disabled="isViewMode"/>
        </el-form-item>
        
        <el-form-item label="单位" prop="unit">
          <el-input v-model="materialForm.unit" placeholder="请输入单位" :disabled="isViewMode"/>
        </el-form-item>
        
        <el-form-item label="单价" prop="price">
          <el-input-number v-model="materialForm.price" :min="0" :precision="2" :step="0.1" :disabled="isViewMode"/>
        </el-form-item>
        
        <el-form-item label="审批状态">
          <el-tag v-if="materialForm.approvalStatus === 'pending'" type="warning">审批中</el-tag>
          <el-tag v-else-if="materialForm.approvalStatus === 'approved'" type="success">已通过</el-tag>
          <el-tag v-else-if="materialForm.approvalStatus === 'rejected'" type="danger">已驳回</el-tag>
          <el-tag v-else-if="materialForm.approvalStatus === 'draft'" type="info">草稿</el-tag>
          <el-tag v-else type="info">未提交</el-tag>
          
          <!-- 审批组件 - 集成方式1：使用状态显示模式 -->
          <approval 
            v-if="materialForm.id"
            mode="display" 
            :businessId="materialForm.id.toString()" 
            :businessType="'material'" />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="submitForm" :disabled="isViewMode || isApprovalPending">保存</el-button>
          
          <!-- 审批组件 - 集成方式2：使用表单模式发起审批 -->
          <approval 
            v-if="materialForm.id && !isApprovalPending && materialForm.approvalStatus !== 'approved'"
            mode="form" 
            :businessId="materialForm.id.toString()" 
            :businessType="'material'"
            buttonText="发起审批"
            :approvedStatus="'approved'"
            :rejectedStatus="'rejected'"
            :autoUpdateStatus="true"
            :businessData="{title: `物料审批 - ${materialForm.name}`, data: materialForm}"
            @before-approval="validateForm"
            @approval-success="handleApprovalSuccess"
            @status-updated="handleStatusUpdated">
            <!-- 添加自定义业务表单 -->
            <template v-slot:business-form>
              <el-form-item label="物料名称">
                <span>{{ materialForm.name }}</span>
              </el-form-item>
              <el-form-item label="物料代码">
                <span>{{ materialForm.code }}</span>
              </el-form-item>
              <el-form-item label="规格型号">
                <span>{{ materialForm.model }}</span>
              </el-form-item>
              <el-form-item label="单价">
                <span>{{ materialForm.price }}</span>
              </el-form-item>
            </template>
          </approval>
          
          <el-button @click="cancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 审批记录卡片 -->
    <el-card class="box-card approval-record-card" v-if="materialForm.id">
      <div slot="header" class="clearfix">
        <span>审批记录</span>
      </div>
      
      <!-- 审批组件 - 集成方式3：审批历史记录和流程图 -->
      <el-button type="text" icon="el-icon-view" @click="showApprovalHistory">查看审批记录</el-button>
      
      <el-timeline v-if="approvalRecords.length > 0">
        <el-timeline-item
          v-for="(record, index) in approvalRecords"
          :key="index"
          :type="getTimelineItemType(record.operationType)"
          :timestamp="record.operationTime">
          <el-card shadow="hover" size="small">
            <h4>{{ getOperationTypeText(record.operationType) }}</h4>
            <p>操作人: {{ record.operationUserName }}</p>
            <p v-if="record.approvalOpinion">意见: {{ record.approvalOpinion }}</p>
          </el-card>
        </el-timeline-item>
      </el-timeline>
      
      <div v-else class="empty-tip">
        暂无审批记录
      </div>
    </el-card>
  </div>
</template>

<script>
import { getApprovalHistory } from "@/api/approval/workflow";
import Approval from '@/components/RuiYun/Approval';

export default {
  name: "MaterialApprovalDemo",
  components: {
    Approval
  },
  data() {
    return {
      // 是否为查看模式
      isViewMode: false,
      // 物料表单数据
      materialForm: {
        id: '1001', // 模拟数据，实际应用中可能需要从路由参数或API获取
        name: '聚合氯化铝',
        code: 'MAT10001',
        categoryId: '1',
        model: 'PAC-01',
        unit: 'kg',
        price: 3.5,
        approvalStatus: 'draft'
      },
      // 表单验证规则
      rules: {
        name: [
          { required: true, message: '请输入物料名称', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入物料代码', trigger: 'blur' }
        ],
        categoryId: [
          { required: true, message: '请选择物料分类', trigger: 'change' }
        ],
        unit: [
          { required: true, message: '请输入单位', trigger: 'blur' }
        ]
      },
      // 分类选项
      categoryOptions: [
        { value: '1', label: '化学品' },
        { value: '2', label: '原材料' },
        { value: '3', label: '包装材料' },
        { value: '4', label: '设备耗材' }
      ],
      // 审批记录
      approvalRecords: []
    };
  },
  computed: {
    // 是否在审批中
    isApprovalPending() {
      return this.materialForm.approvalStatus === 'pending';
    }
  },
  created() {
    // 初始化时加载审批记录
    this.loadApprovalHistory();
  },
  methods: {
    /**
     * 提交表单
     */
    submitForm() {
      this.$refs.materialForm.validate((valid) => {
        if (valid) {
          // 实际应用中这里会调用API保存物料数据
          this.$modal.msgSuccess('保存成功');
        }
      });
    },
    
    /**
     * 表单验证
     */
    validateForm() {
      this.$refs.materialForm.validate((valid) => {
        if (!valid) {
          this.$modal.msgError('请完善表单信息后再发起审批');
          return false;
        }
        return true;
      });
    },
    
    /**
     * 取消操作
     */
    cancel() {
      this.$router.go(-1);
    },
    
    /**
     * 处理审批成功
     */
    handleApprovalSuccess(data) {
      console.log('审批发起成功', data);
      // 更新物料状态为审批中
      this.materialForm.approvalStatus = 'pending';
      // 重新加载审批记录
      this.loadApprovalHistory();
    },
    
    /**
     * 处理状态更新
     */
    handleStatusUpdated(data) {
      console.log('状态已更新', data);
      // 更新物料状态
      this.materialForm.approvalStatus = data.status;
      // 重新加载审批记录
      this.loadApprovalHistory();
    },
    
    /**
     * 加载审批记录
     */
    loadApprovalHistory() {
      if (!this.materialForm.id) return;
      
      getApprovalHistory({
        businessId: this.materialForm.id.toString(),
        businessType: 'material'
      }).then(res => {
        if (res.code === 200 && res.data) {
          this.approvalRecords = res.data.records || [];
        }
      });
    },
    
    /**
     * 显示审批历史记录
     */
    showApprovalHistory() {
      // 这里可以跳转到审批详情页或者打开一个对话框显示详细的审批历史和流程图
      this.$router.push({
        path: '/approval/process/detail',
        query: {
          businessId: this.materialForm.id.toString(),
          businessType: 'material'
        }
      });
    },
    
    /**
     * 获取操作类型文本
     */
    getOperationTypeText(operationType) {
      switch (operationType) {
        case '0': return '提交申请';
        case '1': return '审批通过';
        case '2': return '审批驳回';
        case '3': return '撤销申请';
        case '4': return '转交审批';
        case '5': return '催办';
        default: return '未知操作';
      }
    },
    
    /**
     * 获取时间线项目类型
     */
    getTimelineItemType(operationType) {
      switch (operationType) {
        case '0': return 'primary';
        case '1': return 'success';
        case '2': return 'danger';
        case '3': return 'info';
        case '4': return 'warning';
        case '5': return '';
        default: return '';
      }
    }
  }
};
</script>

<style scoped>
.approval-record-card {
  margin-top: 20px;
}
.empty-tip {
  color: #909399;
  font-size: 14px;
  padding: 20px 0;
  text-align: center;
}
</style> 