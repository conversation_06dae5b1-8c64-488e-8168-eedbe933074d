<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="命令名称" prop="commandName">
        <el-input
          v-model="queryParams.commandName"
          placeholder="请输入命令名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="命令编码" prop="commandCode">
        <el-input
          v-model="queryParams.commandCode"
          placeholder="请输入命令编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="命令类型" prop="commandType">
        <el-select v-model="queryParams.commandType" placeholder="请选择命令类型" clearable>
          <el-option label="控制命令" value="control" />
          <el-option label="配置命令" value="config" />
          <el-option label="查询命令" value="query" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="启用" value="1" />
          <el-option label="禁用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['outmis:command:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['outmis:command:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['outmis:command:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['outmis:command:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="commandList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="命令名称" align="center" prop="commandName" />
      <el-table-column label="命令编码" align="center" prop="commandCode" />
      <el-table-column label="命令类型" align="center" prop="commandType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.outmis_command_type" :value="scope.row.commandType"/>
        </template>
      </el-table-column>
      <el-table-column label="协议名称" align="center" prop="protocolName" />
      <el-table-column label="优先级" align="center" prop="priority" />
      <el-table-column label="超时时间" align="center" prop="timeoutSeconds">
        <template slot-scope="scope">
          {{ scope.row.timeoutSeconds }}秒
        </template>
      </el-table-column>
      <el-table-column label="重试次数" align="center" prop="retryCount" />
      <el-table-column label="是否异步" align="center" prop="isAsync">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_yes_no" :value="scope.row.isAsync"/>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['outmis:command:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['outmis:command:remove']"
          >删除</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-caret-right"
            @click="handleExecute(scope.row)"
            v-hasPermi="['outmis:command:execute']"
          >执行</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改命令对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="命令名称" prop="commandName">
          <el-input v-model="form.commandName" placeholder="请输入命令名称" />
        </el-form-item>
        <el-form-item label="命令编码" prop="commandCode">
          <el-input v-model="form.commandCode" placeholder="请输入命令编码" />
        </el-form-item>
        <el-form-item label="命令类型" prop="commandType">
          <el-select v-model="form.commandType" placeholder="请选择命令类型">
            <el-option label="控制命令" value="control" />
            <el-option label="配置命令" value="config" />
            <el-option label="查询命令" value="query" />
          </el-select>
        </el-form-item>
        <el-form-item label="协议ID" prop="protocolId">
          <el-select v-model="form.protocolId" placeholder="请选择协议">
            <el-option
              v-for="protocol in protocolOptions"
              :key="protocol.protocolId"
              :label="protocol.protocolName"
              :value="protocol.protocolId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-input-number v-model="form.priority" :min="1" :max="10" />
        </el-form-item>
        <el-form-item label="超时时间" prop="timeoutSeconds">
          <el-input-number v-model="form.timeoutSeconds" :min="1" :max="300" />
          <span style="margin-left: 10px;">秒</span>
        </el-form-item>
        <el-form-item label="重试次数" prop="retryCount">
          <el-input-number v-model="form.retryCount" :min="0" :max="10" />
        </el-form-item>
        <el-form-item label="是否异步" prop="isAsync">
          <el-radio-group v-model="form.isAsync">
            <el-radio :label="0">同步</el-radio>
            <el-radio :label="1">异步</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="命令格式" prop="commandFormat">
          <el-input v-model="form.commandFormat" type="textarea" placeholder="请输入命令格式" />
        </el-form-item>
        <el-form-item label="响应格式" prop="responseFormat">
          <el-input v-model="form.responseFormat" type="textarea" placeholder="请输入响应格式" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "OutmisCommand",
  dicts: ['outmis_command_type', 'sys_yes_no', 'sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 命令表格数据
      commandList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 协议选项
      protocolOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        commandName: null,
        commandCode: null,
        commandType: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        commandName: [
          { required: true, message: "命令名称不能为空", trigger: "blur" }
        ],
        commandCode: [
          { required: true, message: "命令编码不能为空", trigger: "blur" }
        ],
        commandType: [
          { required: true, message: "命令类型不能为空", trigger: "change" }
        ],
        protocolId: [
          { required: true, message: "协议不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getProtocolOptions();
  },
  methods: {
    /** 查询命令列表 */
    getList() {
      this.loading = true;
      // 模拟数据
      this.commandList = [
        {
          commandId: 1,
          commandName: "获取重量数据",
          commandCode: "GET_WEIGHT",
          commandType: "query",
          protocolId: 1,
          protocolName: "出库防错TCP协议",
          priority: 5,
          timeoutSeconds: 30,
          retryCount: 3,
          isAsync: 0,
          status: 1,
          description: "获取称重传感器重量数据"
        },
        {
          commandId: 2,
          commandName: "设置零点校准",
          commandCode: "SET_ZERO",
          commandType: "control",
          protocolId: 1,
          protocolName: "出库防错TCP协议",
          priority: 8,
          timeoutSeconds: 60,
          retryCount: 2,
          isAsync: 1,
          status: 1,
          description: "设置称重传感器零点校准"
        }
      ];
      this.total = this.commandList.length;
      this.loading = false;
    },
    /** 获取协议选项 */
    getProtocolOptions() {
      // 模拟协议数据
      this.protocolOptions = [
        { protocolId: 1, protocolName: "出库防错TCP协议" },
        { protocolId: 2, protocolName: "出库防错HTTP协议" }
      ];
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        commandId: null,
        commandName: null,
        commandCode: null,
        commandType: null,
        protocolId: null,
        priority: 5,
        timeoutSeconds: 30,
        retryCount: 3,
        isAsync: 0,
        status: 1,
        description: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.commandId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加命令";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const commandId = row.commandId || this.ids
      this.form = Object.assign({}, row);
      this.open = true;
      this.title = "修改命令";
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.commandId != null) {
            // 修改命令
            console.log('修改命令:', this.form);
            this.$modal.msgSuccess("修改成功");
          } else {
            // 新增命令
            console.log('新增命令:', this.form);
            this.$modal.msgSuccess("新增成功");
          }
          this.open = false;
          this.getList();
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const commandIds = row.commandId || this.ids;
      this.$modal.confirm('是否确认删除命令编号为"' + commandIds + '"的数据项？').then(function() {
        // 调用删除API
        console.log('删除命令:', commandIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$modal.confirm('是否确认导出所有命令数据项?').then(() => {
        // 调用导出API
        console.log('导出命令');
        this.$modal.msgSuccess("导出成功");
      }).catch(() => {});
    },
    /** 执行命令按钮操作 */
    handleExecute(row) {
      this.$modal.confirm('是否确认执行命令"' + row.commandName + '"?').then(function() {
        // 调用执行API
        console.log('执行命令:', row.commandId);
      }).then(() => {
        this.$modal.msgSuccess("命令执行成功");
      }).catch(() => {});
    }
  }
};
</script>
