import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect')
      }
    ]
  },

  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true
  },
  {
    path: '/register',
    component: () => import('@/views/register'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true
  },
  // 审批流程测试页面已删除
  // {
  //   path: '/test/approval-flow',
  //   component: () => import('@/views/test/ApprovalFlowTest'),
  //   hidden: true
  // },
  {
    path: '',
    component: Layout,
    redirect: 'index',
    children: [
      {
        path: 'index',
        component: () => import('@/views/index'),
        name: 'Index',
        meta: { title: '首页', icon: 'dashboard', affix: true }
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: () => import('@/views/system/user/profile/index'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' }
      }
    ]
  },
  {
    path: '/outmis',
    component: Layout,
    name: 'OutmisModule',
    hidden: true,
    meta: { title: '出库防错', icon: 'scale' },
    children: [
      {
        path: 'device',
        name: 'OutmisDevice',
        component: () => import('@/views/outmis/device/index'),
        meta: { title: '设备管理', icon: 'monitor' }
      },
      {
        path: 'rule',
        name: 'OutmisRule',
        component: () => import('@/views/outmis/rule/index'),
        meta: { title: '防错规则', icon: 'build' }
      },
      {
        path: 'record',
        name: 'OutmisRecord',
        component: () => import('@/views/outmis/record/index'),
        meta: { title: '称重记录', icon: 'documentation' }
      },
      {
        path: 'warn',
        name: 'OutmisWarn',
        component: () => import('@/views/outmis/warn/index'),
        meta: { title: '出库预警', icon: 'alert' }
      },
      {
        path: 'config',
        name: 'OutmisConfig',
        component: () => import('@/views/outmis/config/index'),
        meta: { title: '设备配置', icon: 'setting' }
      }
    ]
  },

  // 审批相关的隐藏路由
  {
    path: '/approval',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'detail/:instanceId',
        name: 'ApprovalDetail',
        component: () => import('@/views/approval/detail'),
        meta: { title: '审批详情', activeMenu: '/approval/todoApproval' },
        hidden: true
      },
      {
        path: 'workflow/add',
        name: 'AddWorkflow',
        component: () => import('@/views/approval/workflow/add'),
        meta: { title: '新增流程', activeMenu: '/approval/workflow' },
        hidden: true
      },
      {
        path: 'workflow/edit',
        name: 'EditWorkflow',
        component: () => import('@/views/approval/workflow/edit'),
        meta: { title: '编辑流程', activeMenu: '/approval/workflow' },
        hidden: true
      },
      {
        path: 'workflow/detail',
        name: 'WorkflowDetail',
        component: () => import('@/views/approval/workflow/detail'),
        meta: { title: '流程详情', activeMenu: '/approval/workflow' },
        hidden: true
      },
      {
        path: 'management/instances',
        name: 'ApprovalInstances',
        component: () => import('@/views/approval/management/instances'),
        meta: { title: '审批实例管理', activeMenu: '/approval/management' },
        hidden: true
      },
      {
        path: 'multilevel/add',
        name: 'AddMultilevelWorkflow',
        component: () => import('@/views/approval/multilevel/add'),
        meta: { title: '新增多级审批', activeMenu: '/approval/workflow' },
        hidden: true
      },
      {
        path: 'multilevel/edit',
        name: 'EditMultilevelWorkflow',
        component: () => import('@/views/approval/multilevel/add'),
        meta: { title: '编辑多级审批', activeMenu: '/approval/workflow' },
        hidden: true
      }
    ]
  }

]

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  {
    path: '/system',
    component: Layout,
    hidden: false,
    permissions: ['system:user:list', 'system:role:list', 'system:menu:list', 'system:dept:list', 'system:post:list', 'system:dict:list', 'system:config:list', 'system:notice:list', 'monitor:operlog:list', 'monitor:logininfor:list'],
    redirect: 'noRedirect',
    alwaysShow: true,
    meta: { title: '系统管理', icon: 'system', noCache: false },
    children: [
      {
        path: '/system/user-auth',
        component: Layout,
        hidden: true,
        permissions: ['system:user:edit'],
        children: [
          {
            path: 'role/:userId(\\d+)',
            component: () => import('@/views/system/user/authRole'),
            name: 'AuthRole',
            meta: { title: '分配角色', activeMenu: '/system/user' }
          }
        ]
      },

      // Guide module routes (智能寻导) - 注释掉静态路由，使用动态路由
      // {
      //   path: '/guide',
      //   component: Layout,
      //   alwaysShow: true,
      //   name: 'Guide',
      //   meta: { title: '智能寻导', icon: 'guide' },
      //   children: [
      //     {
      //       path: 'dashboard',
      //       component: () => import('@/views/guide/dashboard/index'),
      //       name: 'GuideDashboard',
      //       meta: { title: '寻导概览', icon: 'dashboard' }
      //     },
      //     {
      //       path: 'config',
      //       component: () => import('@/views/guide/config/index'),
      //       name: 'GuideConfig',
      //       meta: { title: '寻导配置', icon: 'edit' },
      //       permissions: ['guide:config:list']
      //     },
      //     {
      //       path: 'log',
      //       component: () => import('@/views/guide/config/log/index'),
      //       name: 'GuideLog',
      //       meta: { title: '管控日志', icon: 'log' },
      //       permissions: ['guide:log:list']
      //     },
      //     {
      //       path: 'monitor',
      //       component: () => import('@/views/guide/monitor/index'),
      //       name: 'GuideMonitor',
      //       meta: { title: '实时监控', icon: 'monitor' },
      //       permissions: ['guide:monitor:list']
      //     }
      //   ]
      // },
      {
        path: '/system/role-auth',
        component: Layout,
        hidden: true,
        permissions: ['system:role:edit'],
        children: [
          {
            path: 'user/:roleId(\\d+)',
            component: () => import('@/views/system/role/authUser'),
            name: 'AuthUser',
            meta: { title: '分配用户', activeMenu: '/system/role' }
          }
        ]
      },
      {
        path: '/system/dict-data',
        component: Layout,
        hidden: true,
        permissions: ['system:dict:list'],
        children: [
          {
            path: 'index/:dictId(\\d+)',
            component: () => import('@/views/system/dict/data'),
            name: 'Data',
            meta: { title: '字典数据', activeMenu: '/system/dict' }
          }
        ]
      },
      {
        path: '/monitor/job-log',
        component: Layout,
        hidden: true,
        permissions: ['monitor:job:list'],
        children: [
          {
            path: 'index/:jobId(\\d+)',
            component: () => import('@/views/monitor/job/log'),
            name: 'JobLog',
            meta: { title: '调度日志', activeMenu: '/monitor/job' }
          }
        ]
      },
      {
        path: '/tool/gen-edit',
        component: Layout,
        hidden: true,
        permissions: ['tool:gen:edit'],
        children: [
          {
            path: 'index/:tableId(\\d+)',
            component: () => import('@/views/tool/gen/editTable'),
            name: 'GenEdit',
            meta: { title: '修改生成配置', activeMenu: '/tool/gen' }
          }
        ]
      }
    ]
  },
  
  // Material module routes (物料清单功能已删除)
  {
    path: '/material',
    component: Layout,
    hidden: false,
    permissions: ['material:material:list'],
    redirect: 'noRedirect',
    alwaysShow: true,
    meta: { title: '物料管理', icon: 'shopping', noCache: false },
    children: [
      // 物料清单相关路由已删除
      // other material routes would be here
    ]
  },

  // Add the approval module routes
  {
    path: '/approval',
    component: Layout,
    hidden: false,
    permissions: ['approval:workflow:list', 'approval:multilevel:list'],
    redirect: 'noRedirect',
    alwaysShow: true,
    meta: { title: '审批管理', icon: 'skill', noCache: false },
    children: [
      {
        path: 'workflow',
        component: () => import('@/views/approval/workflow/index'),
        name: 'WorkflowDefinition',
        meta: { title: '流程定义', icon: 'tree-table', noCache: false }
      },
      {
        path: 'todoApproval',
        component: () => import('@/views/approval/todoApproval/index'),
        name: 'TodoApproval',
        meta: { title: '待我审批', icon: 'edit', noCache: false }
      },
      {
        path: 'myApproval',
        component: () => import('@/views/approval/myApproval/index'),
        name: 'MyApproval',
        meta: { title: '我的申请', icon: 'user', noCache: false }
      },
      {
        path: 'management',
        component: () => import('@/views/approval/management/instances'),
        name: 'ApprovalManagement',
        meta: { title: '审批管理', icon: 'system', noCache: false }
      }
    ]
  },

  // 统一注册管理模块已移至动态路由，由后端API提供
  // 避免与数据库菜单重复
]

// 导入模块路由
import mqttRouter from './modules/mqtt'
import sipRouter from './modules/sip'
import emqxRouter from './modules/emqx'

// 添加模块路由到动态路由
dynamicRoutes.push(mqttRouter, sipRouter, emqxRouter)

// 防止连续点击多次路由报错
let routerPush = Router.prototype.push
let routerReplace = Router.prototype.replace
// push
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch(err => err)
}
// replace
Router.prototype.replace = function push(location) {
  return routerReplace.call(this, location).catch(err => err)
}

// 处理外部链接路由
const originalAddRoutes = Router.prototype.addRoutes
Router.prototype.addRoutes = function(routes) {
  const processedRoutes = routes.map(route => {
    // 修复外部链接检测逻辑
    if (route.path && /^(https?:\/\/|mailto:|tel:)/.test(route.path)) {
      // 对外部链接特殊处理
      route.meta = route.meta || {}
      route.meta.isExternal = true
    }
    return route
  })
  return originalAddRoutes.call(this, processedRoutes)
}

export default new Router({
  mode: 'history', // 去掉url中的#
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

