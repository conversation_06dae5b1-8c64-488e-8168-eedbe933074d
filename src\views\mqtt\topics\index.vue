<template>
  <div class="mqtt-topics">
    <div class="page-header">
      <h2>MQTT 主题管理</h2>
      <p>管理 MQTT 主题的订阅、发布和统计信息</p>
    </div>
    
    <el-card>
      <div class="coming-soon">
        <i class="el-icon-menu" style="font-size: 64px; color: #67C23A;"></i>
        <h3>功能开发中...</h3>
        <p>MQTT 主题管理功能正在开发中，敬请期待！</p>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'MqttTopics'
}
</script>

<style lang="scss" scoped>
.mqtt-topics {
  padding: 20px;
  
  .page-header {
    margin-bottom: 20px;
    
    h2 {
      margin: 0 0 8px 0;
      color: #303133;
    }
    
    p {
      margin: 0;
      color: #606266;
    }
  }
  
  .coming-soon {
    text-align: center;
    padding: 60px 20px;
    
    h3 {
      margin: 20px 0 10px 0;
      color: #303133;
    }
    
    p {
      color: #606266;
    }
  }
}
</style>
