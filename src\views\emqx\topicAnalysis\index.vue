<template>
  <div class="topic-analysis-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="page-title">
        <h2><i class="el-icon-data-analysis"></i> EMQX主题分析</h2>
        <p>分析EMQX中的主题格式和结构</p>
      </div>
      <div class="page-actions">
        <el-button type="primary" icon="el-icon-search" @click="analyzeTopics" :loading="analyzing">
          分析主题
        </el-button>
        <el-button type="success" icon="el-icon-refresh" @click="refreshData">
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 主题输入和分析 -->
    <el-card class="analysis-card">
      <div slot="header">
        <span>主题格式分析</span>
      </div>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="input-section">
            <h4>输入主题进行分析</h4>
            <el-input
              v-model="inputTopic"
              placeholder="输入MQTT主题，如：dwms/video/camera/001/alarm"
              @keyup.enter.native="analyzeSingleTopic"
            />
            <div class="input-actions">
              <el-button type="primary" @click="analyzeSingleTopic" :loading="singleAnalyzing">
                分析单个主题
              </el-button>
              <el-button @click="clearInput">清空</el-button>
            </div>
            
            <!-- 示例主题 -->
            <div class="example-topics">
              <h5>示例主题：</h5>
              <el-tag
                v-for="example in exampleTopics"
                :key="example"
                class="example-tag"
                @click="inputTopic = example"
              >
                {{ example }}
              </el-tag>
            </div>
          </div>
        </el-col>
        
        <el-col :span="12">
          <div class="analysis-result" v-if="singleAnalysisResult">
            <h4>分析结果</h4>
            <div class="result-content">
              <div class="result-item">
                <span class="label">主题:</span>
                <span class="value topic-value">{{ singleAnalysisResult.topic }}</span>
              </div>
              <div class="result-item">
                <span class="label">有效性:</span>
                <el-tag :type="singleAnalysisResult.valid ? 'success' : 'danger'" size="small">
                  {{ singleAnalysisResult.valid ? '有效' : '无效' }}
                </el-tag>
              </div>
              <div class="result-item">
                <span class="label">模块:</span>
                <span class="value">{{ singleAnalysisResult.module || '-' }}</span>
              </div>
              <div class="result-item">
                <span class="label">设备类型:</span>
                <span class="value">{{ singleAnalysisResult.deviceType || '-' }}</span>
              </div>
              <div class="result-item">
                <span class="label">设备ID:</span>
                <span class="value">{{ singleAnalysisResult.deviceId || '-' }}</span>
              </div>
              <div class="result-item">
                <span class="label">功能:</span>
                <span class="value">{{ singleAnalysisResult.function || '-' }}</span>
              </div>
              <div class="result-item">
                <span class="label">类型:</span>
                <el-tag :type="getTopicTypeColor(singleAnalysisResult.topicType)" size="small">
                  {{ singleAnalysisResult.topicType }}
                </el-tag>
              </div>
              <div class="result-item">
                <span class="label">优先级:</span>
                <el-tag :type="getPriorityColor(singleAnalysisResult.priority)" size="small">
                  {{ getPriorityText(singleAnalysisResult.priority) }}
                </el-tag>
              </div>
              <div class="result-item">
                <span class="label">通配符:</span>
                <span class="value">{{ singleAnalysisResult.wildcardType || '无' }}</span>
              </div>
              <div class="result-item">
                <span class="label">描述:</span>
                <span class="value">{{ singleAnalysisResult.description || '-' }}</span>
              </div>
              <div class="result-item" v-if="singleAnalysisResult.tags">
                <span class="label">标签:</span>
                <div class="tags-container">
                  <el-tag
                    v-for="tag in singleAnalysisResult.tags"
                    :key="tag"
                    size="mini"
                    class="tag-item"
                  >
                    {{ tag }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 批量分析结果 -->
    <el-card class="batch-analysis-card" v-if="batchAnalysisResults.length > 0">
      <div slot="header">
        <span>批量分析结果 ({{ batchAnalysisResults.length }}个主题)</span>
        <div style="float: right;">
          <el-button type="text" @click="exportAnalysisResults">导出结果</el-button>
        </div>
      </div>
      
      <!-- 统计信息 -->
      <div class="statistics-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.totalTopics }}</div>
              <div class="stat-label">总主题数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.validTopics }}</div>
              <div class="stat-label">有效主题</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.wildcardTopics }}</div>
              <div class="stat-label">通配符主题</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.moduleCount }}</div>
              <div class="stat-label">模块数量</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 分析结果表格 -->
      <el-table
        :data="paginatedResults"
        style="width: 100%"
        :default-sort="{prop: 'priority', order: 'ascending'}"
      >
        <el-table-column prop="topic" label="主题" min-width="300">
          <template slot-scope="scope">
            <span class="topic-name">{{ scope.row.topic }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="valid" label="有效性" width="80" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.valid ? 'success' : 'danger'" size="mini">
              {{ scope.row.valid ? '有效' : '无效' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="module" label="模块" width="100" />
        
        <el-table-column prop="deviceType" label="设备类型" width="100" />
        
        <el-table-column prop="function" label="功能" width="100" />
        
        <el-table-column prop="topicType" label="类型" width="100">
          <template slot-scope="scope">
            <el-tag :type="getTopicTypeColor(scope.row.topicType)" size="mini">
              {{ scope.row.topicType }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="priority" label="优先级" width="100" align="center" sortable>
          <template slot-scope="scope">
            <el-tag :type="getPriorityColor(scope.row.priority)" size="mini">
              {{ getPriorityText(scope.row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="hasWildcard" label="通配符" width="80" align="center">
          <template slot-scope="scope">
            <i v-if="scope.row.hasWildcard" class="el-icon-check" style="color: #67c23a;"></i>
            <i v-else class="el-icon-close" style="color: #f56c6c;"></i>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="primary" @click="viewTopicDetail(scope.row)">
              详情
            </el-button>
            <el-button size="mini" type="success" @click="subscribeToTopic(scope.row)">
              订阅
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="batchAnalysisResults.length"
        />
      </div>
    </el-card>

    <!-- 主题详情对话框 -->
    <el-dialog
      title="主题详情"
      :visible.sync="detailDialogVisible"
      width="800px"
    >
      <div v-if="selectedTopic" class="topic-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="主题">{{ selectedTopic.topic }}</el-descriptions-item>
          <el-descriptions-item label="有效性">
            <el-tag :type="selectedTopic.valid ? 'success' : 'danger'">
              {{ selectedTopic.valid ? '有效' : '无效' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="模块">{{ selectedTopic.module || '-' }}</el-descriptions-item>
          <el-descriptions-item label="设备类型">{{ selectedTopic.deviceType || '-' }}</el-descriptions-item>
          <el-descriptions-item label="设备ID">{{ selectedTopic.deviceId || '-' }}</el-descriptions-item>
          <el-descriptions-item label="功能">{{ selectedTopic.function || '-' }}</el-descriptions-item>
          <el-descriptions-item label="类型">{{ selectedTopic.topicType || '-' }}</el-descriptions-item>
          <el-descriptions-item label="优先级">{{ getPriorityText(selectedTopic.priority) }}</el-descriptions-item>
          <el-descriptions-item label="层级数">{{ selectedTopic.levels || 0 }}</el-descriptions-item>
          <el-descriptions-item label="通配符类型">{{ selectedTopic.wildcardType || '无' }}</el-descriptions-item>
          <el-descriptions-item label="分类">{{ selectedTopic.category || '-' }}</el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">{{ selectedTopic.description || '-' }}</el-descriptions-item>
        </el-descriptions>
        
        <div v-if="selectedTopic.tags" class="tags-section">
          <h4>标签</h4>
          <el-tag
            v-for="tag in selectedTopic.tags"
            :key="tag"
            size="small"
            class="tag-item"
          >
            {{ tag }}
          </el-tag>
        </div>
        
        <div v-if="selectedTopic.parts" class="parts-section">
          <h4>主题结构</h4>
          <el-tag
            v-for="(part, index) in selectedTopic.parts"
            :key="index"
            :type="getPartTagType(index)"
            class="part-tag"
          >
            {{ getPartLabel(index) }}: {{ part }}
          </el-tag>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { scanAllTopics } from '@/api/emqx/autoSubscription'

export default {
  name: 'TopicAnalysis',
  data() {
    return {
      analyzing: false,
      singleAnalyzing: false,
      inputTopic: '',
      singleAnalysisResult: null,
      batchAnalysisResults: [],
      
      // 分页
      currentPage: 1,
      pageSize: 20,
      
      // 统计信息
      statistics: {
        totalTopics: 0,
        validTopics: 0,
        wildcardTopics: 0,
        moduleCount: 0
      },
      
      // 详情对话框
      detailDialogVisible: false,
      selectedTopic: null,
      
      // 示例主题
      exampleTopics: [
        'dwms/video/camera/001/alarm',
        'dwms/access/door/002/event',
        'dwms/weight/scale/003/data',
        'dwms/device/+/status',
        'dwms/system/heartbeat',
        'dwms/video/+/+',
        'dwms/+/+/+/alarm'
      ]
    }
  },
  
  computed: {
    paginatedResults() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.batchAnalysisResults.slice(start, end)
    }
  },
  
  created() {
    this.refreshData()
  },
  
  methods: {
    // 刷新数据
    async refreshData() {
      // 可以在这里加载一些初始数据
    },

    // 分析单个主题
    async analyzeSingleTopic() {
      if (!this.inputTopic.trim()) {
        this.$message.warning('请输入主题')
        return
      }

      this.singleAnalyzing = true
      try {
        // 模拟主题分析（实际项目中调用后端API）
        this.singleAnalysisResult = this.analyzeTopicStructure(this.inputTopic)
        this.$message.success('主题分析完成')
      } catch (error) {
        this.$message.error('分析失败')
        console.error(error)
      } finally {
        this.singleAnalyzing = false
      }
    },

    // 批量分析主题
    async analyzeTopics() {
      this.analyzing = true
      try {
        // 获取EMQX中的主题
        const response = await scanAllTopics()
        if (response.code === 200) {
          const topics = response.data || []

          // 分析每个主题
          this.batchAnalysisResults = topics.map(topicData => {
            const topic = topicData.topic || topicData
            return this.analyzeTopicStructure(topic)
          })

          // 计算统计信息
          this.calculateStatistics()

          this.$message.success(`分析完成，共分析${this.batchAnalysisResults.length}个主题`)
        } else {
          this.$message.error('获取主题失败: ' + response.msg)
        }
      } catch (error) {
        this.$message.error('分析失败')
        console.error(error)
      } finally {
        this.analyzing = false
      }
    },

    // 分析主题结构（前端实现）
    analyzeTopicStructure(topic) {
      const analysis = {
        topic: topic,
        valid: false,
        isDwmsTopic: false,
        hasWildcard: false,
        wildcardType: 'none',
        topicType: 'general',
        priority: 3,
        category: 'unknown',
        tags: [],
        description: '',
        levels: 0,
        parts: []
      }

      if (!topic || typeof topic !== 'string') {
        analysis.error = '主题为空或格式错误'
        return analysis
      }

      const parts = topic.split('/')
      analysis.parts = parts
      analysis.levels = parts.length

      // 基础结构分析
      if (parts.length >= 1) analysis.prefix = parts[0]
      if (parts.length >= 2) analysis.module = parts[1]
      if (parts.length >= 3) analysis.deviceType = parts[2]
      if (parts.length >= 4) analysis.deviceId = parts[3]
      if (parts.length >= 5) analysis.function = parts[4]

      // 判断是否为DWMS主题
      analysis.isDwmsTopic = topic.startsWith('dwms/')

      // 判断有效性
      analysis.valid = this.isValidDwmsTopic(topic, parts)

      // 判断通配符
      analysis.hasWildcard = topic.includes('+') || topic.includes('#')
      if (topic.includes('#')) {
        analysis.wildcardType = 'multi-level'
      } else if (topic.includes('+')) {
        analysis.wildcardType = 'single-level'
      }

      // 确定主题类型
      analysis.topicType = this.determineTopicType(topic)

      // 计算优先级
      analysis.priority = this.calculatePriority(topic)

      // 按功能分类
      analysis.category = this.categorizeByFunction(topic)

      // 生成标签
      analysis.tags = this.generateTags(topic, parts)

      // 生成描述
      analysis.description = this.generateDescription(topic, parts)

      return analysis
    },

    // 判断是否为有效的DWMS主题
    isValidDwmsTopic(topic, parts) {
      if (!topic.startsWith('dwms/')) return false
      if (parts.length < 4) return false

      const validModules = ['video', 'access', 'weight', 'device', 'system']
      const module = parts[1]
      if (!validModules.includes(module) && module !== '+' && module !== '#') {
        return false
      }

      return true
    },

    // 确定主题类型
    determineTopicType(topic) {
      if (topic.includes('/alarm')) return 'alarm'
      if (topic.includes('/event')) return 'event'
      if (topic.includes('/status')) return 'status'
      if (topic.includes('/data')) return 'data'
      if (topic.includes('/config')) return 'config'
      if (topic.includes('/stream')) return 'stream'
      if (topic.includes('/heartbeat')) return 'heartbeat'
      if (topic.includes('/log')) return 'log'
      return 'general'
    },

    // 计算优先级
    calculatePriority(topic) {
      if (topic.includes('/alarm') || topic.includes('/emergency')) return 1
      if (topic.includes('/event') || topic.includes('/warning')) return 2
      if (topic.includes('/status') || topic.includes('/config')) return 3
      if (topic.includes('/data') || topic.includes('/info')) return 4
      if (topic.includes('/log') || topic.includes('/debug')) return 5
      return 3
    },

    // 按功能分类
    categorizeByFunction(topic) {
      const parts = topic.split('/')
      if (parts.length >= 5) {
        const func = parts[4]
        if (['alarm', 'warning', 'emergency'].includes(func)) return 'alert'
        if (['event', 'action'].includes(func)) return 'event'
        if (['status', 'state'].includes(func)) return 'status'
        if (['data', 'measurement'].includes(func)) return 'data'
        if (['config', 'setting'].includes(func)) return 'config'
        if (['stream', 'video'].includes(func)) return 'media'
        return 'other'
      }
      return 'unknown'
    },

    // 生成标签
    generateTags(topic, parts) {
      const tags = []

      if (parts.length >= 2) tags.push(`module:${parts[1]}`)
      if (parts.length >= 3) tags.push(`device:${parts[2]}`)
      if (parts.length >= 5) tags.push(`function:${parts[4]}`)

      tags.push(`type:${this.determineTopicType(topic)}`)
      tags.push(`category:${this.categorizeByFunction(topic)}`)

      const priority = this.calculatePriority(topic)
      tags.push(`priority:${priority}`)
      if (priority <= 2) tags.push('critical')

      if (topic.includes('+') || topic.includes('#')) {
        tags.push('wildcard')
      }

      if (topic.startsWith('dwms/system/')) tags.push('system')

      return tags
    },

    // 生成描述
    generateDescription(topic, parts) {
      let desc = ''

      if (parts.length >= 2) {
        const moduleDesc = this.getModuleDescription(parts[1])
        desc += moduleDesc
      }

      if (parts.length >= 3) {
        const deviceDesc = this.getDeviceTypeDescription(parts[2])
        if (parts[2] === '+') {
          desc += ' - 所有设备'
        } else {
          desc += ` - ${deviceDesc}`
        }
      }

      if (parts.length >= 5) {
        const funcDesc = this.getFunctionDescription(parts[4])
        desc += ` - ${funcDesc}`
      }

      return desc
    },

    // 获取模块描述
    getModuleDescription(module) {
      const descriptions = {
        'video': '视频监控',
        'access': '门禁管理',
        'weight': '重量监控',
        'device': '设备管理',
        'system': '系统管理'
      }
      return descriptions[module] || module
    },

    // 获取设备类型描述
    getDeviceTypeDescription(deviceType) {
      const descriptions = {
        'camera': '摄像头',
        'door': '门禁设备',
        'scale': '称重设备',
        'sensor': '传感器',
        'nvr': '网络录像机'
      }
      return descriptions[deviceType] || deviceType
    },

    // 获取功能描述
    getFunctionDescription(func) {
      const descriptions = {
        'alarm': '告警信息',
        'event': '事件信息',
        'status': '状态信息',
        'data': '数据信息',
        'config': '配置信息',
        'stream': '视频流',
        'heartbeat': '心跳信息',
        'log': '日志信息'
      }
      return descriptions[func] || func
    },

    // 计算统计信息
    calculateStatistics() {
      this.statistics.totalTopics = this.batchAnalysisResults.length
      this.statistics.validTopics = this.batchAnalysisResults.filter(r => r.valid).length
      this.statistics.wildcardTopics = this.batchAnalysisResults.filter(r => r.hasWildcard).length

      const modules = new Set()
      this.batchAnalysisResults.forEach(r => {
        if (r.module) modules.add(r.module)
      })
      this.statistics.moduleCount = modules.size
    },

    // 清空输入
    clearInput() {
      this.inputTopic = ''
      this.singleAnalysisResult = null
    },

    // 查看主题详情
    viewTopicDetail(topic) {
      this.selectedTopic = topic
      this.detailDialogVisible = true
    },

    // 订阅主题
    subscribeToTopic(topic) {
      this.$message.info(`订阅主题: ${topic.topic}`)
      // 这里可以调用订阅API
    },

    // 导出分析结果
    exportAnalysisResults() {
      const data = JSON.stringify(this.batchAnalysisResults, null, 2)
      const blob = new Blob([data], { type: 'application/json' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `topic_analysis_${new Date().getTime()}.json`
      link.click()
      window.URL.revokeObjectURL(url)
      this.$message.success('分析结果已导出')
    },

    // 分页处理
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
    },

    handleCurrentChange(val) {
      this.currentPage = val
    },

    // 获取主题类型颜色
    getTopicTypeColor(type) {
      const colors = {
        'alarm': 'danger',
        'event': 'warning',
        'status': 'success',
        'data': 'info',
        'config': 'primary',
        'stream': '',
        'heartbeat': 'info',
        'log': '',
        'general': ''
      }
      return colors[type] || ''
    },

    // 获取优先级颜色
    getPriorityColor(priority) {
      if (priority <= 1) return 'danger'
      if (priority <= 2) return 'warning'
      if (priority <= 3) return 'success'
      return 'info'
    },

    // 获取优先级文本
    getPriorityText(priority) {
      const texts = {
        1: '最高',
        2: '高',
        3: '中',
        4: '低',
        5: '最低'
      }
      return texts[priority] || '中'
    },

    // 获取部分标签类型
    getPartTagType(index) {
      const types = ['', 'success', 'warning', 'info', 'primary']
      return types[index] || ''
    },

    // 获取部分标签
    getPartLabel(index) {
      const labels = ['前缀', '模块', '设备类型', '设备ID', '功能']
      return labels[index] || `层级${index + 1}`
    }
  }
}
</script>

<style scoped>
.topic-analysis-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title h2 {
  margin: 0;
  color: #303133;
}

.page-title p {
  margin: 5px 0 0 0;
  color: #909399;
  font-size: 14px;
}

.analysis-card, .batch-analysis-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.input-section h4 {
  margin-bottom: 15px;
  color: #303133;
}

.input-actions {
  margin: 15px 0;
}

.example-topics {
  margin-top: 20px;
}

.example-topics h5 {
  margin-bottom: 10px;
  color: #606266;
}

.example-tag {
  margin: 5px 5px 5px 0;
  cursor: pointer;
}

.example-tag:hover {
  opacity: 0.8;
}

.analysis-result {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 20px;
  background: #fafafa;
}

.analysis-result h4 {
  margin-bottom: 15px;
  color: #303133;
}

.result-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.result-item .label {
  width: 80px;
  color: #909399;
  font-size: 14px;
}

.result-item .value {
  color: #303133;
  font-size: 14px;
}

.topic-value {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.tag-item {
  margin: 2px;
}

.statistics-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.topic-name {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.topic-detail {
  padding: 20px 0;
}

.tags-section, .parts-section {
  margin-top: 20px;
}

.tags-section h4, .parts-section h4 {
  margin-bottom: 10px;
  color: #303133;
}

.part-tag {
  margin: 5px 5px 5px 0;
}
</style>
