import request from '@/utils/request'

// SIP 设备管理 API

/**
 * 获取设备列表
 */
export function getSipDevices(params) {
  return request({
    url: '/api/sip/devices',
    method: 'get',
    params
  })
}

/**
 * 获取设备详情
 */
export function getSipDeviceDetails(deviceId) {
  return request({
    url: `/api/sip/devices/${deviceId}`,
    method: 'get'
  })
}

/**
 * 强制设备离线
 */
export function forceDeviceOffline(deviceId, reason) {
  return request({
    url: `/api/sip/devices/${deviceId}/offline`,
    method: 'post',
    data: { reason }
  })
}

/**
 * 发送设备查询命令
 */
export function queryDevice(deviceId, cmdType) {
  return request({
    url: `/api/sip/devices/${deviceId}/query`,
    method: 'post',
    params: { cmdType }
  })
}

/**
 * 发送设备控制命令
 */
export function controlDevice(deviceId, data) {
  return request({
    url: `/api/sip/devices/${deviceId}/control`,
    method: 'post',
    data
  })
}

/**
 * 获取设备通道列表
 */
export function getDeviceChannels(deviceId) {
  return request({
    url: `/api/sip/devices/${deviceId}/channels`,
    method: 'get'
  })
}

/**
 * 获取设备状态
 */
export function getDeviceStatus(deviceId) {
  return request({
    url: `/api/sip/devices/${deviceId}/status`,
    method: 'get'
  })
}

/**
 * 获取设备信息
 */
export function getDeviceInfo(deviceId) {
  return request({
    url: `/api/sip/devices/${deviceId}/info`,
    method: 'get'
  })
}

/**
 * 获取设备目录
 */
export function getDeviceCatalog(deviceId) {
  return request({
    url: `/api/sip/devices/${deviceId}/catalog`,
    method: 'get'
  })
}

/**
 * PTZ 控制
 */
export function ptzControl(deviceId, channelId, data) {
  return request({
    url: `/api/sip/devices/${deviceId}/channels/${channelId}/ptz`,
    method: 'post',
    data
  })
}

/**
 * 设备录像查询
 */
export function queryDeviceRecord(deviceId, channelId, params) {
  return request({
    url: `/api/sip/devices/${deviceId}/channels/${channelId}/records`,
    method: 'get',
    params
  })
}

/**
 * 开始录像回放
 */
export function startPlayback(deviceId, channelId, data) {
  return request({
    url: `/api/sip/devices/${deviceId}/channels/${channelId}/playback`,
    method: 'post',
    data
  })
}

/**
 * 停止录像回放
 */
export function stopPlayback(deviceId, channelId) {
  return request({
    url: `/api/sip/devices/${deviceId}/channels/${channelId}/playback`,
    method: 'delete'
  })
}

/**
 * 搜索设备
 */
export function searchDevices(keyword, params) {
  return request({
    url: '/api/sip/devices/search',
    method: 'get',
    params: { keyword, ...params }
  })
}

/**
 * 导出设备列表
 */
export function exportDevices(params) {
  return request({
    url: '/api/sip/devices/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
