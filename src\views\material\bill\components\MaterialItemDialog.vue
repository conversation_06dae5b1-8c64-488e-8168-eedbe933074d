<template>
  <el-dialog title="添加物料明细" :visible.sync="visible" width="800px" append-to-body>
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="物料" prop="materialId">
            <el-select
              v-model="form.materialId"
              placeholder="请选择物料"
              filterable
              remote
              :remote-method="searchMaterials"
              :loading="materialLoading"
              @change="handleMaterialChange"
              @focus="handleMaterialFocus"
              style="width: 100%"
            >
              <el-option
                v-for="item in materialOptions"
                :key="item.id"
                :label="`${item.materialCode} - ${item.materialName}`"
                :value="item.id"
              >
                <span style="float: left">{{ item.materialCode }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.materialName }}</span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="数量" prop="quantity">
            <el-input-number 
              v-model="form.quantity" 
              :precision="3" 
              :min="0" 
              placeholder="请输入数量"
              style="width: 100%"
              @change="calculateTotalWeight"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="单位重量(kg)" prop="unitWeight">
            <el-input-number 
              v-model="form.unitWeight" 
              :precision="3" 
              :min="0" 
              placeholder="请输入单位重量"
              style="width: 100%"
              @change="calculateTotalWeight"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="总重量(kg)">
            <el-input v-model="form.totalWeight" :disabled="true" placeholder="自动计算" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="存储位置">
            <el-select
              v-model="form.storageLocation"
              placeholder="请输入或选择货位号/容器号"
              filterable
              clearable
              allow-create
              default-first-option
              remote
              :remote-method="searchStorageLocation"
              :loading="storageLocationLoading"
              @change="handleStorageLocationChange"
              style="width: 100%"
            >
              <el-option
                v-for="item in storageLocationOptions"
                :key="item.code"
                :label="item.code"
                :value="item.code"
              >
                <div style="display: flex; justify-content: space-between; align-items: center;">
                  <span>{{ item.description }}</span>
                  <span style="color: #8492a6; font-size: 12px;">{{ item.fullInfo }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="备注">
            <el-input v-model="form.remark" placeholder="请输入备注" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 物料信息显示 -->
      <el-card v-if="selectedMaterial" class="material-info-card">
        <div slot="header">
          <span>物料信息</span>
        </div>
        <el-descriptions :column="2" border size="small">
          <el-descriptions-item label="物料编码">{{ selectedMaterial.materialCode }}</el-descriptions-item>
          <el-descriptions-item label="物料名称">{{ selectedMaterial.materialName }}</el-descriptions-item>
          <el-descriptions-item label="规格">{{ selectedMaterial.materialSpec || '无' }}</el-descriptions-item>
          <el-descriptions-item label="型号">{{ selectedMaterial.materialModel || '无' }}</el-descriptions-item>
          <el-descriptions-item label="单位">{{ selectedMaterial.unit || '无' }}</el-descriptions-item>
          <el-descriptions-item label="类别">{{ selectedMaterial.materialType || '无' }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="cancel">取 消</el-button>
      <el-button type="primary" @click="submitForm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { listMaterial } from "@/api/material/material";
import { searchStorageLocationCombined } from "@/api/warehouse/storageSearch";

export default {
  name: "MaterialItemDialog",
  props: {
    // 编辑的明细项
    editItem: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      visible: false,
      form: {
        materialId: null,
        materialCode: null,
        materialName: null,
        materialSpec: null,
        materialModel: null,
        unit: null,
        quantity: 1,
        unitWeight: 0,
        totalWeight: 0,
        remark: null,
        storageLocation: null
      },
      rules: {
        materialId: [
          { required: true, message: "请选择物料", trigger: "change" }
        ],
        quantity: [
          { required: true, message: "请输入数量", trigger: "blur" }
        ],
        unitWeight: [
          { required: true, message: "请输入单位重量", trigger: "blur" }
        ]
      },
      materialOptions: [],
      materialLoading: false,
      storageLocationOptions: [],
      storageLocationLoading: false,
      selectedMaterial: null
    };
  },
  watch: {
    // 监听编辑项变化
    editItem: {
      handler(newVal) {
        if (newVal) {
          console.log('MaterialItemDialog接收到编辑数据:', newVal);
          console.log('locationCode值:', newVal.locationCode);

          // 填充表单数据
          this.form = {
            ...newVal,
            // 从locationCode字段恢复存储位置
            storageLocation: newVal.locationCode || null,
            // 确保规格和型号字段正确设置
            materialSpec: newVal.materialSpec || null,
            materialModel: newVal.materialModel || null
          };

          console.log('设置后的表单数据:', this.form);
          console.log('storageLocation值:', this.form.storageLocation);

          // 如果有存储位置，添加到选项中
          if (this.form.storageLocation) {
            this.storageLocationOptions = [{
              value: this.form.storageLocation,
              label: this.form.storageLocation,
              type: 'location'
            }];
          }

          // 设置选中的物料
          if (newVal.materialId) {
            this.selectedMaterial = {
              id: newVal.materialId,
              materialCode: newVal.materialCode,
              materialName: newVal.materialName,
              materialSpec: newVal.materialSpec,
              materialModel: newVal.materialModel,
              unit: newVal.unit,
              unitWeight: newVal.unitWeight
            };
          }
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 打开对话框
    open() {
      console.log('MaterialItemDialog: 打开对话框')
      console.log('当前editItem:', this.editItem);

      // 如果不是编辑模式，才重置表单
      if (!this.editItem) {
        console.log('新增模式，重置表单');
        this.reset();
      } else {
        console.log('编辑模式，保留表单数据');
      }

      this.visible = true;
      // 不在打开时立即加载物料，改为用户搜索时才加载
      console.log('MaterialItemDialog: 对话框已打开，使用懒加载模式')
    },

    // 重置表单
    reset() {
      this.form = {
        materialId: null,
        materialCode: null,
        materialName: null,
        materialSpec: null,
        materialModel: null,
        unit: null,
        quantity: 1,
        unitWeight: 0,
        totalWeight: 0,
        remark: null,
        storageLocation: null
      };
      this.selectedMaterial = null;
      this.storageLocationOptions = [];
      // 安全地重置表单
      if (this.$refs.form) {
        this.$refs.form.resetFields();
      }
    },

    // 加载物料列表
    async loadMaterials(query = '') {
      console.log('MaterialItemDialog: 开始加载物料列表，查询条件:', query)

      // 防止重复加载
      if (this.materialLoading) {
        console.log('MaterialItemDialog: 正在加载中，跳过重复请求')
        return
      }

      this.materialLoading = true;

      try {
        const params = {
          pageNum: 1,
          pageSize: 50
        };

        if (query) {
          params.materialCode = query;
          params.materialName = query;
        }

        console.log('MaterialItemDialog: 发送物料API请求，参数:', params)
        const response = await listMaterial(params)
        console.log('MaterialItemDialog: 物料API响应:', response)

        if (response && response.code === 200) {
          this.materialOptions = response.rows || [];
          console.log('MaterialItemDialog: 物料列表加载成功，数量:', this.materialOptions.length)
        } else {
          console.error('MaterialItemDialog: 物料API返回错误:', response)
          this.materialOptions = [];
          if (response && response.msg) {
            this.$message.warning('加载物料列表失败: ' + response.msg)
          }
        }
      } catch (error) {
        console.error('MaterialItemDialog: 加载物料列表异常:', error)
        this.materialOptions = [];
        this.$message.warning('加载物料列表失败，请稍后重试')
      } finally {
        this.materialLoading = false;
      }
    },

    // 搜索物料
    searchMaterials(query) {
      console.log('MaterialItemDialog: 搜索物料，查询条件:', query)
      if (query !== '') {
        this.loadMaterials(query);
      } else {
        // 如果查询为空，加载默认的物料列表
        this.loadMaterials('');
      }
    },

    // 物料选择器获得焦点
    handleMaterialFocus() {
      console.log('MaterialItemDialog: 物料选择器获得焦点')
      // 如果还没有加载过物料，则加载默认列表
      if (this.materialOptions.length === 0 && !this.materialLoading) {
        console.log('MaterialItemDialog: 首次获得焦点，加载默认物料列表')
        this.loadMaterials('')
      }
    },

    // 物料选择变化
    handleMaterialChange(materialId) {
      const material = this.materialOptions.find(item => item.id === materialId);
      if (material) {
        // 创建selectedMaterial对象，确保包含正确的规格型号字段
        this.selectedMaterial = {
          ...material,
          materialSpec: material.materialSpec || material.specification || null,
          materialModel: material.materialModel || null
        };
        this.form.materialCode = material.materialCode;
        this.form.materialName = material.materialName;

        // 处理规格型号字段映射
        // 物料信息表中使用specification字段，需要映射到物料清单明细的materialSpec和materialModel
        if (material.specification) {
          // 如果specification包含分隔符，尝试分割为规格和型号
          const specParts = material.specification.split(/[,，\s]+/);
          this.form.materialSpec = specParts[0] || material.specification;
          this.form.materialModel = specParts[1] || null;
        } else {
          // 如果有单独的materialSpec和materialModel字段，直接使用
          this.form.materialSpec = material.materialSpec || material.specification || null;
          this.form.materialModel = material.materialModel || null;
        }

        this.form.unit = material.unit;

        // 如果物料有默认重量，自动填充
        if (material.unitWeight) {
          this.form.unitWeight = material.unitWeight;
          this.calculateTotalWeight();
        } else if (material.weight) {
          this.form.unitWeight = material.weight;
          this.calculateTotalWeight();
        }

        console.log('物料选择后的表单数据:', {
          materialCode: this.form.materialCode,
          materialName: this.form.materialName,
          materialSpec: this.form.materialSpec,
          materialModel: this.form.materialModel,
          unit: this.form.unit,
          unitWeight: this.form.unitWeight
        });
      }
    },

    // 计算总重量
    calculateTotalWeight() {
      if (this.form.quantity && this.form.unitWeight) {
        this.form.totalWeight = (this.form.quantity * this.form.unitWeight).toFixed(3);
      } else {
        this.form.totalWeight = 0;
      }
    },

    // 搜索存储位置
    searchStorageLocation(query) {
      if (!query || query.trim() === '') {
        this.storageLocationOptions = [];
        return;
      }

      this.storageLocationLoading = true;
      searchStorageLocationCombined(query.trim()).then(response => {
        this.storageLocationOptions = response.data || [];
      }).catch(error => {
        console.error('搜索存储位置失败:', error);
        this.storageLocationOptions = [];
        this.$message.error('搜索存储位置失败');
      }).finally(() => {
        this.storageLocationLoading = false;
      });
    },

    // 存储位置选择变化处理
    handleStorageLocationChange(value) {
      console.log('存储位置选择变化:', value);
      // 可以在这里添加额外的处理逻辑
    },

    // 取消
    cancel() {
      this.visible = false;
      this.reset();
    },

    // 提交表单
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.calculateTotalWeight();

          // 提交表单数据，包含存储位置信息
          const formData = {
            ...this.form,
            // 将存储位置存储到locationCode字段，并设置存放类型为货位
            locationCode: this.form.storageLocation,
            storageType: this.form.storageLocation ? 'location' : null,
            // 确保规格和型号信息正确传递
            materialSpec: this.selectedMaterial ? this.selectedMaterial.materialSpec : this.form.materialSpec,
            materialModel: this.selectedMaterial ? this.selectedMaterial.materialModel : this.form.materialModel
          };

          console.log('MaterialItemDialog提交的数据:', formData);
          console.log('存储位置字段:', {
            storageLocation: this.form.storageLocation,
            locationCode: formData.locationCode,
            storageType: formData.storageType
          });

          this.$emit("success", formData);
          this.cancel();
        }
      });
    }
  }
};
</script>

<style scoped>
.material-info-card {
  margin-top: 20px;
  background: #f8f9fa;
}

.dialog-footer {
  text-align: center;
}
</style>
