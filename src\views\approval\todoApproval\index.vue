<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 待我审批列表 -->
      <el-col :span="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>待我审批列表</span>
            <el-badge :value="unreadCount" :max="99" class="unread-badge" v-if="unreadCount > 0">
              <el-button type="text" icon="el-icon-bell" @click="showNotifications">通知</el-button>
            </el-badge>
          </div>
          <div class="filter-container">
            <el-input v-model="queryParams.title" placeholder="审批标题" clearable style="width: 200px" class="filter-item" @keyup.enter.native="handleQuery" />
            <el-select v-model="queryParams.businessType" placeholder="业务类型" clearable class="filter-item" style="width: 200px">
              <el-option v-for="item in businessTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-select v-model="queryParams.priority" placeholder="优先级" clearable class="filter-item" style="width: 120px">
              <el-option v-for="item in priorityOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-button type="primary" icon="el-icon-search" class="filter-item" @click="handleQuery">搜索</el-button>
            <el-button type="default" icon="el-icon-refresh" class="filter-item" @click="resetQuery">重置</el-button>
          </div>

          <el-table v-loading="loading" :data="todoList" border>
            <el-table-column label="序号" type="index" width="50" align="center" />
            <el-table-column label="审批标题" prop="title" :show-overflow-tooltip="true" />
            <el-table-column label="流程名称" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                {{ scope.row.workflowName || scope.row.workflow_name || '-' }}
              </template>
            </el-table-column>
            <el-table-column label="业务类型" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <el-tag v-if="(scope.row.businessType || scope.row.business_type) === 'MATERIAL_BILL'" type="primary">物料清单</el-tag>
                <el-tag v-else-if="(scope.row.businessType || scope.row.business_type) === 'MATERIAL_APPROVAL'" type="success">物料审批</el-tag>
                <el-tag v-else-if="(scope.row.businessType || scope.row.business_type) === 'MATERIAL_APPLY'" type="warning">物料申请</el-tag>
                <span v-else>{{ scope.row.businessTypeName || scope.row.businessType || scope.row.business_type || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="申请人" align="center" width="100">
              <template slot-scope="scope">
                {{ scope.row.applicantName || scope.row.applyUserName || scope.row.apply_user_name || '-' }}
              </template>
            </el-table-column>
            <el-table-column label="申请时间" align="center" width="150">
              <template slot-scope="scope">
                {{ parseTime(scope.row.createTime || scope.row.create_time || scope.row.applyTime) }}
              </template>
            </el-table-column>
            <el-table-column label="当前环节" align="center" width="120">
              <template slot-scope="scope">
                <el-tooltip placement="top">
                  <div slot="content">
                    <div v-if="scope.row.currentNodeName || scope.row.current_node_name">节点名称: {{ scope.row.currentNodeName || scope.row.current_node_name }}</div>
                    <div v-if="scope.row.currentApprovers">审批人: {{ scope.row.currentApprovers }}</div>
                    <div v-if="scope.row.createTime || scope.row.create_time">创建时间: {{ parseTime(scope.row.createTime || scope.row.create_time) }}</div>
                  </div>
                  <el-button type="text">{{ scope.row.currentNodeName || scope.row.current_node_name || '未知节点' }}</el-button>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="优先级" align="center" width="80">
              <template slot-scope="scope">
                <el-tag v-if="(scope.row.priority || scope.row.priorityName) === '3' || scope.row.priorityName === '非常紧急'" type="danger">非常紧急</el-tag>
                <el-tag v-else-if="(scope.row.priority || scope.row.priorityName) === '2' || scope.row.priorityName === '紧急'" type="warning">紧急</el-tag>
                <el-tag v-else-if="(scope.row.priority || scope.row.priorityName) === '1' || scope.row.priorityName === '普通'" type="info">普通</el-tag>
                <el-tag v-else type="info">{{ scope.row.priorityName || '普通' }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="状态" align="center" width="100">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.status === '0'" type="warning">{{ scope.row.statusName || '审批中' }}</el-tag>
                <el-tag v-else-if="scope.row.status === '1'" type="success">{{ scope.row.statusName || '已通过' }}</el-tag>
                <el-tag v-else-if="scope.row.status === '2'" type="danger">{{ scope.row.statusName || '已驳回' }}</el-tag>
                <el-tag v-else-if="scope.row.status === '3'" type="info">{{ scope.row.statusName || '已撤销' }}</el-tag>
                <el-tag v-else type="info">{{ scope.row.statusName || '未知' }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <!-- 只有状态为审批中(0)的项目才显示审批和转交按钮 -->
                <template v-if="scope.row.status === '0'">
                  <el-button type="text" icon="el-icon-view" @click="handleApprove(scope.row)" v-hasPermi="['approval:todo:handle']">审批</el-button>
                  <el-button type="text" icon="el-icon-refresh-right" @click="handleTransfer(scope.row)" v-hasPermi="['approval:todo:transfer']">转交</el-button>
                </template>
                <!-- 已结束的流程只显示查看按钮 -->
                <template v-else>
                  <el-button type="text" icon="el-icon-view" @click="handleView(scope.row)">查看</el-button>
                  <el-tag v-if="scope.row.status === '1'" type="success" size="mini">已完成</el-tag>
                  <el-tag v-else-if="scope.row.status === '2'" type="danger" size="mini">已驳回</el-tag>
                  <el-tag v-else-if="scope.row.status === '3'" type="info" size="mini">已撤销</el-tag>
                </template>
              </template>
            </el-table-column>
          </el-table>
          
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
        </el-card>
      </el-col>
    </el-row>

    <!-- 审批对话框 -->
    <el-dialog :title="'审批处理 - ' + approveForm.title" :visible.sync="approveOpen" width="700px" append-to-body>
      <el-tabs v-model="approveActiveTab">
        <el-tab-pane label="基本信息" name="info">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="业务类型">{{ currentInstance.businessType }}</el-descriptions-item>
            <el-descriptions-item label="业务标题">{{ currentInstance.businessTitle }}</el-descriptions-item>
            <el-descriptions-item label="申请人">{{ currentInstance.applyUserName }}</el-descriptions-item>
            <el-descriptions-item label="申请时间">{{ currentInstance.applyTime }}</el-descriptions-item>
            <el-descriptions-item label="申请部门" :span="2">{{ currentInstance.applyDeptName }}</el-descriptions-item>
            <el-descriptions-item label="备注" :span="2">
              <el-input type="textarea" v-model="currentInstance.remark" :rows="2" readonly></el-input>
            </el-descriptions-item>
          </el-descriptions>
          
          <!-- 业务数据展示区域 -->
          <div v-if="businessData" class="business-data">
            <h4>业务数据</h4>
            <el-form label-width="120px" size="small">
              <el-form-item v-for="(value, key, index) in businessData" :key="`business_${key}_${index}`" :label="key">
                {{ value }}
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
        
        <!-- 流程图标签页 -->
        <el-tab-pane label="流程图" name="diagram">
          <div id="instance-diagram" style="width: 100%; height: 300px; border: 1px solid #ddd;"></div>
        </el-tab-pane>
        
        <!-- 审批记录标签页 -->
        <el-tab-pane label="审批记录" name="records">
          <el-timeline>
            <el-timeline-item v-for="(record, index) in approvalRecords" :key="index" 
              :type="getTimelineItemType(record.operationType)" 
              :timestamp="record.operationTime">
              <el-card shadow="hover" size="small">
                <h4>{{ getOperationTypeText(record.operationType) }}</h4>
                <p>操作人: {{ record.operationUserName }}</p>
                <p v-if="record.approvalOpinion">意见: {{ record.approvalOpinion }}</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </el-tab-pane>
        
        <el-tab-pane label="审批操作" name="action">
          <el-form ref="approveForm" :model="approveForm" :rules="approveRules" label-width="100px">
            <el-form-item label="审批结果" prop="action">
              <el-radio-group v-model="approveForm.action">
                <el-radio label="agree">同意</el-radio>
                <el-radio label="reject">驳回</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="审批意见" prop="opinion">
              <el-input v-model="approveForm.opinion" type="textarea" placeholder="请输入审批意见" :rows="3" />
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="approveOpen = false">取 消</el-button>
        <el-button type="primary" @click="submitApprove">提 交</el-button>
      </div>
    </el-dialog>
    
    <!-- 转交对话框 -->
    <el-dialog title="转交审批" :visible.sync="transferOpen" width="500px" append-to-body>
      <el-form ref="transferForm" :model="transferForm" :rules="transferRules" label-width="100px">
        <el-form-item label="转交给" prop="targetUserId">
          <el-select v-model="transferForm.targetUserId" placeholder="请选择转交人" style="width: 100%" filterable>
            <el-option
              v-for="item in userOptions"
              :key="item.userId"
              :label="item.nickName"
              :value="item.userId">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="转交理由" prop="opinion">
          <el-input v-model="transferForm.opinion" type="textarea" placeholder="请输入转交理由" :rows="3" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="transferOpen = false">取 消</el-button>
        <el-button type="primary" @click="submitTransfer">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 通知对话框 -->
    <el-dialog title="审批通知" :visible.sync="notificationOpen" width="600px" append-to-body>
      <el-tabs v-model="notifyActiveTab">
        <el-tab-pane label="未读通知" name="unread">
          <el-table :data="notifications.filter(item => item.status === '0')" border size="small">
            <el-table-column label="标题" prop="title" :show-overflow-tooltip="true" />
            <el-table-column label="内容" prop="content" :show-overflow-tooltip="true" />
            <el-table-column label="类型" width="100">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.notifyType === '0'" type="primary">待审批</el-tag>
                <el-tag v-else-if="scope.row.notifyType === '1'" type="success">已通过</el-tag>
                <el-tag v-else-if="scope.row.notifyType === '2'" type="danger">已驳回</el-tag>
                <el-tag v-else-if="scope.row.notifyType === '3'" type="info">已撤销</el-tag>
                <el-tag v-else-if="scope.row.notifyType === '4'" type="warning">已转交</el-tag>
                <el-tag v-else-if="scope.row.notifyType === '5'" type="warning">催办</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="时间" prop="createTime" width="140" />
            <el-table-column label="操作" width="100" align="center">
              <template slot-scope="scope">
                <el-button type="text" @click="markRead(scope.row)">标记已读</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="已读通知" name="read">
          <el-table :data="notifications.filter(item => item.status === '1')" border size="small">
            <el-table-column label="标题" prop="title" :show-overflow-tooltip="true" />
            <el-table-column label="内容" prop="content" :show-overflow-tooltip="true" />
            <el-table-column label="类型" width="100">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.notifyType === '0'" type="primary">待审批</el-tag>
                <el-tag v-else-if="scope.row.notifyType === '1'" type="success">已通过</el-tag>
                <el-tag v-else-if="scope.row.notifyType === '2'" type="danger">已驳回</el-tag>
                <el-tag v-else-if="scope.row.notifyType === '3'" type="info">已撤销</el-tag>
                <el-tag v-else-if="scope.row.notifyType === '4'" type="warning">已转交</el-tag>
                <el-tag v-else-if="scope.row.notifyType === '5'" type="warning">催办</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="时间" prop="createTime" width="140" />
            <el-table-column label="已读时间" prop="readTime" width="140" />
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>
</template>

<script>
import { parseTime } from "@/utils/ruiyun";
import { 
  listTodoApproval, 
  approve, 
  transferApproval,
  getApprovalDetail, 
  getInstanceDiagram,
  getApprovalNotifications,
  markNotificationRead,
  getBusinessTypes
} from "@/api/approval/workflow";
import { listUser } from "@/api/system/user";
import * as d3 from 'd3';

export default {
  name: "TodoApproval",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 待我审批列表
      todoList: [],
      // 业务类型选项
      businessTypeOptions: [],
      // 业务类型选项加载状态
      businessTypeLoading: false,
      // 优先级选项
      priorityOptions: [
        { value: "high", label: "高" },
        { value: "medium", label: "中" },
        { value: "low", label: "低" }
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: undefined,
        businessType: undefined,
        priority: undefined
      },
      // 审批对话框
      approveOpen: false,
      approveActiveTab: 'info',
      // 当前实例数据
      currentInstance: {},
      // 业务数据
      businessData: null,
      // 审批记录
      approvalRecords: [],
      // 审批表单
      approveForm: {
        instanceId: undefined,
        title: '',
        action: 'agree',
        opinion: ''
      },
      // 表单校验
      approveRules: {
        action: [
          { required: true, message: "请选择审批结果", trigger: "change" }
        ],
        opinion: [
          { required: true, message: "请输入审批意见", trigger: "blur" }
        ]
      },
      // 转交对话框
      transferOpen: false,
      // 转交表单
      transferForm: {
        instanceId: undefined,
        targetUserId: undefined,
        opinion: ''
      },
      // 转交表单校验
      transferRules: {
        targetUserId: [
          { required: true, message: "请选择转交人", trigger: "change" }
        ],
        opinion: [
          { required: true, message: "请输入转交理由", trigger: "blur" }
        ]
      },
      // 用户选项
      userOptions: [],
      // 流程图数据
      diagramData: null,
      // 通知相关
      notificationOpen: false,
      notifications: [],
      unreadCount: 0,
      notifyActiveTab: 'unread'
    };
  },
  created() {
    this.getList();
    this.getUserOptions();
    this.getNotifications();
    this.getBusinessTypeOptions();
  },
  mounted() {
    // 只有在有权限时才定时刷新通知
    if (this.$auth && this.$auth.hasPermi && this.$auth.hasPermi('approval:notification:list')) {
      this.notificationTimer = setInterval(() => {
        this.getNotifications();
      }, 60000); // 一分钟刷新一次
    }
  },
  beforeDestroy() {
    // 清除定时器
    if (this.notificationTimer) {
      clearInterval(this.notificationTimer);
    }
  },
  methods: {
    /** 查询待我审批列表 */
    getList() {
      this.loading = true;

      // 添加时间戳防止缓存
      const params = {
        ...this.queryParams,
        _t: Date.now()
      };

      listTodoApproval(params).then(response => {
        console.log('=== 待审批列表API返回数据 ===');
        console.log('完整响应:', response);
        console.log('数据行数:', response.rows ? response.rows.length : 0);
        if (response.rows && response.rows.length > 0) {
          console.log('第一条数据:', response.rows[0]);
          console.log('字段检查:');
          const firstRow = response.rows[0];
          console.log('- workflowName:', firstRow.workflowName);
          console.log('- workflow_name:', firstRow.workflow_name);
          console.log('- businessType:', firstRow.businessType);
          console.log('- business_type:', firstRow.business_type);
          console.log('- applicantName:', firstRow.applicantName);
          console.log('- apply_user_name:', firstRow.apply_user_name);
          console.log('- createTime:', firstRow.createTime);
          console.log('- create_time:', firstRow.create_time);
          console.log('- currentNodeName:', firstRow.currentNodeName);
          console.log('- current_node_name:', firstRow.current_node_name);
          console.log('- priority:', firstRow.priority);
          console.log('- priorityName:', firstRow.priorityName);
          console.log('- status:', firstRow.status);
          console.log('- statusName:', firstRow.statusName);
        }

        this.todoList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        console.error('获取待审批列表失败:', error);
        this.loading = false;
      });
    },
    /** 获取用户选项 */
    getUserOptions() {
      listUser().then(response => {
        this.userOptions = response.rows;
      });
    },
    /** 获取通知 */
    getNotifications() {
      // 检查权限
      if (!this.$auth || !this.$auth.hasPermi || !this.$auth.hasPermi('approval:notification:list')) {
        this.notifications = [];
        this.unreadCount = 0;
        return;
      }

      getApprovalNotifications().then(response => {
        this.notifications = response.data || [];
        this.unreadCount = this.notifications.filter(item => item.status === '0').length;
      }).catch(error => {
        console.log('获取审批通知失败:', error);
        // 静默处理错误，避免影响用户体验
        this.notifications = [];
        this.unreadCount = 0;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        title: undefined,
        businessType: undefined,
        priority: undefined
      };
      this.handleQuery();
    },
    /** 审批按钮操作 */
    handleApprove(row) {
      // 尝试获取实例ID，支持多种字段名
      const instanceId = row.instanceId || row.instance_id || row.id;

      if (!row || !instanceId) {
        this.$message.error("无效的审批实例ID");
        return;
      }

      // 跳转到审批详情页，传递审批实例ID
      this.$router.push({
        path: "/approval/detail/" + instanceId
      });
    },
    /** 提交审批 */
    submitApprove() {
      this.$refs["approveForm"].validate(valid => {
        if (valid) {
          // 确保instanceId是有效值且为字符串类型
          if (!this.approveForm.instanceId) {
            this.$message.error("无效的审批实例ID");
            return;
          }
          
          const data = {
            instanceId: String(this.approveForm.instanceId),
            action: this.approveForm.action,
            opinion: this.approveForm.opinion
          };
          
          approve(data).then(response => {
            if (response.code === 200) {
              this.$message.success("审批提交成功");
              this.approveOpen = false;
              this.getList();
            } else {
              this.$message.error(response.msg || "审批提交失败");
            }
          }).catch(error => {
            console.error("审批提交失败:", error);
            this.$message.error("审批提交失败: " + (error.message || error));
          });
        }
      });
    },
    /** 重置转交表单 */
    resetTransfer() {
      this.transferForm = {
        instanceId: undefined,
        targetUserId: undefined,
        opinion: ''
      };
      this.transferOpen = false;
      if (this.$refs.transferForm) {
        this.$refs.transferForm.resetFields();
      }
    },
    /** 重置转交表单 */
    resetTransfer() {
      this.transferForm = {
        instanceId: undefined,
        targetUserId: undefined,
        opinion: ''
      };
      this.transferOpen = false;
      if (this.$refs.transferForm) {
        this.$refs.transferForm.resetFields();
      }
    },
    /** 转交按钮操作 */
    handleTransfer(row) {
      this.resetTransfer();
      if (!row || !row.instanceId) {
        this.$message.error("无效的审批实例ID");
        return;
      }

      // 确保instanceId是字符串类型
      const instanceId = String(row.instanceId);
      console.log("转交审批，实例ID类型:", typeof instanceId, "值:", instanceId);

      this.transferForm = {
        instanceId: instanceId,
        targetUserId: undefined,
        opinion: ''
      };
      this.transferOpen = true;
    },
    /** 提交转交 */
    submitTransfer() {
      this.$refs["transferForm"].validate(valid => {
        if (valid) {
          const data = {
            instanceId: String(this.transferForm.instanceId),
            targetUserId: this.transferForm.targetUserId,
            opinion: this.transferForm.opinion
          };
          
          console.log("提交转交参数:", data);
          
          transferApproval(data).then(response => {
            if (response.code === 200) {
              this.$modal.msgSuccess("转交成功");
              this.transferOpen = false;
              this.getList();
            } else {
              this.$modal.msgError(response.msg || "转交失败");
            }
          }).catch(error => {
            console.error("转交审批失败:", error);
            this.$modal.msgError("转交失败: " + (error.message || error));
          });
        }
      });
    },
    /** 查看按钮操作 */
    handleView(row) {
      if (!row || !row.instanceId) {
        this.$message.error("无效的审批实例ID");
        return;
      }
      this.$router.push({ path: '/approval/detail', query: { instanceId: row.instanceId }});
    },
    /** 显示通知 */
    showNotifications() {
      this.notificationOpen = true;
      this.notifyActiveTab = 'unread';
    },
    /** 标记通知为已读 */
    markRead(notification) {
      markNotificationRead(notification.notificationId).then(() => {
        notification.status = '1';
        notification.readTime = new Date();
        this.unreadCount = Math.max(0, this.unreadCount - 1);
      });
    },
    /** 渲染流程实例图 */
    renderInstanceDiagram() {
      if (!this.diagramData || !this.diagramData.nodes || this.diagramData.nodes.length === 0) {
        return;
      }

      // 清空容器
      const container = document.getElementById('instance-diagram');
      container.innerHTML = '';

      // 使用D3.js渲染流程图 - 圆点样式
      const width = container.clientWidth;
      const height = container.clientHeight;
      const nodeWidth = 120;
      const nodeHeight = 80;  // 增加高度以容纳文字和圆点
      const circleRadius = 18; // 圆点半径
      const textHeight = 30;   // 文字区域高度
      const circleY = textHeight + 25; // 圆点Y位置（文字下方）
      
      const svg = d3.select('#instance-diagram')
        .append('svg')
        .attr('width', width)
        .attr('height', height);
        
      const nodes = this.diagramData.nodes;
      const links = this.diagramData.links || [];
      
      // 计算节点位置
      const nodePositions = [];
      const xGap = Math.min(120, width / (nodes.length + 1));
      
      nodes.forEach((node, index) => {
        nodePositions.push({
          x: xGap * (index + 1),
          y: height / 2
        });
      });
      
      // 绘制连线
      const lineGenerator = d3.line()
        .curve(d3.curveBasis);

      links.forEach((link, index) => {
        const sourceIndex = nodes.findIndex(n => n.nodeId === link.source);
        const targetIndex = nodes.findIndex(n => n.nodeId === link.target);

        if (sourceIndex > -1 && targetIndex > -1) {
          const sourcePos = nodePositions[sourceIndex];
          const targetPos = nodePositions[targetIndex];
          const sourceNode = nodes[sourceIndex];
          const targetNode = nodes[targetIndex];

          // 连接线从圆点中心到圆点中心
          const sourceCircleY = sourcePos.y + circleY; // 使用统一的圆点位置
          const targetCircleY = targetPos.y + circleY; // 使用统一的圆点位置

          const points = [
            [sourcePos.x + circleRadius, sourceCircleY],
            [sourcePos.x + circleRadius + (targetPos.x - sourcePos.x) / 2, sourceCircleY],
            [sourcePos.x + circleRadius + (targetPos.x - sourcePos.x) / 2, targetCircleY],
            [targetPos.x - circleRadius, targetCircleY]
          ];

          // 获取连接线样式
          const lineStyle = this.getConnectionLineStyle(sourceNode, targetNode);

          svg.append('path')
            .attr('d', lineGenerator(points))
            .attr('stroke', lineStyle.color)
            .attr('stroke-width', 2)
            .attr('stroke-dasharray', lineStyle.dashArray)
            .attr('fill', 'none')
            .attr('marker-end', 'url(#arrowhead)');
        }
      });
      
      // 添加箭头标记
      svg.append('defs').append('marker')
        .attr('id', 'arrowhead')
        .attr('viewBox', '0 -5 10 10')
        .attr('refX', 8)
        .attr('refY', 0)
        .attr('orient', 'auto')
        .attr('markerWidth', 6)
        .attr('markerHeight', 6)
        .append('path')
        .attr('d', 'M0,-5L10,0L0,5')
        .attr('fill', '#999');
      
      // 绘制节点（文字在上，圆点在下）
      nodes.forEach((node, index) => {
        const g = svg.append('g')
          .attr('transform', `translate(${nodePositions[index].x - nodeWidth / 2}, ${nodePositions[index].y - nodeHeight / 2})`);

        // 获取节点状态
        let nodeStatus = 'pending';
        if (node.status === 'completed') {
          nodeStatus = 'completed';
        } else if (node.nodeId === this.currentInstance.currentNodeId) {
          nodeStatus = 'active';
        }

        // 节点文本（上方）
        g.append('text')
          .attr('x', nodeWidth / 2)
          .attr('y', 15)
          .attr('text-anchor', 'middle')
          .attr('dominant-baseline', 'middle')
          .attr('fill', '#333')
          .attr('font-size', '12px')
          .attr('font-weight', 'bold')
          .text(node.nodeName);

        // 圆点（下方）
        const circle = g.append('circle')
          .attr('cx', nodeWidth / 2)
          .attr('cy', circleY)
          .attr('r', circleRadius)
          .attr('fill', this.getNodeStatusColor(nodeStatus))
          .attr('stroke', '#fff')
          .attr('stroke-width', 2);

        // 为开始节点添加脉冲动画效果
        if (node.nodeType === '0') {
          // 添加脉冲外圈
          const pulseRing = g.append('circle')
            .attr('cx', nodeWidth / 2)
            .attr('cy', circleY)
            .attr('r', circleRadius)
            .attr('fill', 'none')
            .attr('stroke', this.getNodeStatusColor(nodeStatus))
            .attr('stroke-width', 2)
            .attr('opacity', 0.8);

          // 创建脉冲动画函数
          const createPulseAnimation = (element) => {
            element
              .transition()
              .duration(1500)
              .ease(d3.easeLinear)
              .attr('r', circleRadius + 8)
              .attr('opacity', 0)
              .on('end', () => {
                // 动画结束后重新开始
                element
                  .attr('r', circleRadius)
                  .attr('opacity', 0.8);
                // 递归调用创建循环动画
                createPulseAnimation(element);
              });
          };

          // 启动脉冲动画
          createPulseAnimation(pulseRing);
        }

        // 为审批节点添加旋转动画效果
        if (node.nodeType === '1') {
          // 添加旋转外圈
          const rotateRing = g.append('circle')
            .attr('cx', nodeWidth / 2)
            .attr('cy', circleY)
            .attr('r', circleRadius + 3)
            .attr('fill', 'none')
            .attr('stroke', this.getNodeStatusColor(nodeStatus))
            .attr('stroke-width', 2)
            .attr('stroke-dasharray', '5,3')
            .attr('opacity', 0.6);

          // 创建旋转动画函数
          const createRotateAnimation = (element) => {
            element
              .transition()
              .duration(3000)
              .ease(d3.easeLinear)
              .attrTween('transform', () => {
                return d3.interpolateString(
                  `rotate(0 ${nodeWidth / 2} ${circleY})`,
                  `rotate(360 ${nodeWidth / 2} ${circleY})`
                );
              })
              .on('end', () => {
                // 动画结束后重新开始
                createRotateAnimation(element);
              });
          };

          // 启动旋转动画
          createRotateAnimation(rotateRing);
        }

        // 状态图标（圆点内）
        let statusIcon = '';
        if (nodeStatus === 'completed') {
          statusIcon = '✓';
        } else if (nodeStatus === 'active') {
          statusIcon = '●';
        } else {
          statusIcon = '○';
        }

        g.append('text')
          .attr('x', nodeWidth / 2)
          .attr('y', circleY + 1)
          .attr('text-anchor', 'middle')
          .attr('dominant-baseline', 'middle')
          .attr('fill', '#fff')
          .attr('font-size', '12px')
          .attr('font-weight', 'bold')
          .text(statusIcon);

        // 如果是当前节点，添加额外的脉冲动画
        if (nodeStatus === 'active') {
          const activeRing = g.append('circle')
            .attr('cx', nodeWidth / 2)
            .attr('cy', circleY)
            .attr('r', circleRadius + 3)
            .attr('fill', 'none')
            .attr('stroke', '#409EFF')
            .attr('stroke-width', 2)
            .attr('stroke-dasharray', '5,3')
            .attr('opacity', 0.8);

          // 当前节点脉冲动画
          activeRing
            .transition()
            .duration(2000)
            .ease(d3.easeLinear)
            .attr('r', circleRadius + 8)
            .attr('opacity', 0)
            .on('end', function() {
              d3.select(this)
                .attr('r', circleRadius + 3)
                .attr('opacity', 0.8)
                .transition()
                .duration(2000)
                .ease(d3.easeLinear)
                .attr('r', circleRadius + 8)
                .attr('opacity', 0)
                .on('end', arguments.callee);
            });
        }
      });
    },
    /** 获取节点状态颜色 */
    getNodeStatusColor(status) {
      switch (status) {
        case 'completed': return '#67C23A';
        case 'active': return '#409EFF';
        case 'pending': return '#909399';
        default: return '#909399';
      }
    },

    /**
     * 获取连接线样式
     */
    getConnectionLineStyle(sourceNode, targetNode) {
      // 获取节点状态
      const sourceCompleted = sourceNode.status === 'completed';
      const targetCompleted = targetNode.status === 'completed';
      const targetIsCurrent = targetNode.nodeId === this.currentInstance?.currentNodeId;
      const targetIsEnd = targetNode.nodeType === '2'; // 结束节点

      // 如果源节点已完成
      if (sourceCompleted) {
        // 如果目标节点也已完成，或者目标节点是结束节点且前面的审批节点都已完成
        if (targetCompleted || (targetIsEnd && this.isAllApprovalNodesCompleted())) {
          return {
            color: '#67C23A', // 绿色实线
            dashArray: ''
          };
        } else if (targetIsCurrent || (!targetCompleted && !targetIsEnd)) {
          // 如果目标节点是当前节点或未完成的审批节点，显示虚线
          return {
            color: '#409EFF', // 蓝色虚线
            dashArray: '5,5'
          };
        }
      }

      // 默认样式（源节点未完成）
      return {
        color: '#999', // 灰色实线
        dashArray: ''
      };
    },

    /**
     * 检查所有审批节点是否都已完成
     */
    isAllApprovalNodesCompleted() {
      if (!this.diagramData || !this.diagramData.nodes) {
        return false;
      }

      // 获取所有审批节点（nodeType='1'）
      const approvalNodes = this.diagramData.nodes.filter(node => node.nodeType === '1');

      // 检查是否所有审批节点都已完成
      return approvalNodes.every(node => node.status === 'completed');
    },
    /** 获取时间线项目类型 */
    getTimelineItemType(operationType) {
      switch (operationType) {
        case '0': return 'primary'; // 提交
        case '1': return 'success'; // 通过
        case '2': return 'danger';  // 驳回
        case '3': return 'info';    // 撤销
        case '4': return 'warning'; // 转交
        case '5': return '';        // 催办
        default: return '';
      }
    },
    /** 获取操作类型文本 */
    getOperationTypeText(operationType) {
      switch (operationType) {
        case '0': return '提交申请';
        case '1': return '审批通过';
        case '2': return '审批驳回';
        case '3': return '撤销申请';
        case '4': return '转交审批';
        case '5': return '催办';
        default: return '未知操作';
      }
    },
    /** 获取业务类型选项 */
    getBusinessTypeOptions() {
      // 防止重复调用
      if (this.businessTypeLoading || this.businessTypeOptions.length > 0) {
        return;
      }

      this.businessTypeLoading = true;
      getBusinessTypes().then(response => {
        if (response.code === 200) {
          // 使用 Map 去重，避免重复键问题
          const uniqueTypes = new Map();
          response.data.forEach(item => {
            if (item.dictValue && !uniqueTypes.has(item.dictValue)) {
              uniqueTypes.set(item.dictValue, {
                value: item.dictValue,
                label: item.dictLabel
              });
            }
          });

          // 转换为数组
          this.businessTypeOptions = Array.from(uniqueTypes.values());

          console.log('TodoApproval - 业务类型选项已加载:', this.businessTypeOptions.length, '个');
        }
      }).catch(error => {
        console.error('TodoApproval - 获取业务类型选项失败:', error);
        this.businessTypeOptions = [];
      }).finally(() => {
        this.businessTypeLoading = false;
      });
    },
    /** 获取节点详情 */
    // getNodeDetail(row) {
    //   if (!row || !row.currentNodeName) return '未知节点';
      
    //   let detail = `节点名称: ${row.currentNodeName}`;
      
    //   // 添加节点类型信息
    //   if (row.currentNodeType) {
    //     let nodeTypeText = '';
    //     switch (row.currentNodeType) {
    //       case '0': nodeTypeText = '开始节点'; break;
    //       case '1': nodeTypeText = '审批节点'; break;
    //       case '2': nodeTypeText = '结束节点'; break;
    //       default: nodeTypeText = '未知类型';
    //     }
    //     detail += `\n节点类型: ${nodeTypeText}`;
    //   }
      
    //   // 添加审批人信息
    //   if (row.currentApprovers) {
    //     detail += `\n审批人: ${row.currentApprovers}`;
    //   }
      
    //   // 添加业务信息
    //   if (row.businessType) {
    //     let businessTypeText = '';
    //     switch (row.businessType) {
    //       case 'MATERIAL_APPROVAL': businessTypeText = '物料审批'; break;
    //       default: businessTypeText = row.businessType;
    //     }
    //     detail += `\n业务类型: ${businessTypeText}`;
    //   }
      
    //   // 添加创建时间
    //   if (row.createTime) {
    //     detail += `\n创建时间: ${this.parseTime(row.createTime)}`;
    //   }
      
    //   return detail;
    // }
  }
};
</script>

<style scoped>
.unread-badge {
  float: right;
  margin-top: 8px;
}
.business-data {
  margin-top: 20px;
  border-top: 1px dashed #ddd;
  padding-top: 15px;
}
</style> 