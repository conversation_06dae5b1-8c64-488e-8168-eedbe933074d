import request from '@/utils/request'

// 查询组态组件库列表
export function listComponent(query) {
  return request({
    url: '/dwms/component/list',
    method: 'get',
    params: query
  })
}

// 查询组态组件库详细
export function getComponent(id) {
  return request({
    url: '/dwms/component/' + id,
    method: 'get'
  })
}

// 新增组态组件库
export function addComponent(data) {
  return request({
    url: '/dwms/component',
    method: 'post',
    data: data
  })
}

// 修改组态组件库
export function updateComponent(data) {
  return request({
    url: '/dwms/component',
    method: 'put',
    data: data
  })
}

// 删除组态组件库
export function delComponent(id) {
  return request({
    url: '/dwms/component/' + id,
    method: 'delete'
  })
}

// 根据组件类型查询组件
export function getComponentByType(componentType) {
  return request({
    url: '/dwms/component/type/' + componentType,
    method: 'get'
  })
}

// 根据分类查询组件
export function getComponentByCategory(category) {
  return request({
    url: '/dwms/component/category/' + category,
    method: 'get'
  })
}

// 查询启用的组件列表
export function getActiveComponents(query) {
  return request({
    url: '/dwms/component/active',
    method: 'get',
    params: query
  })
}

// 查询系统组件列表
export function getSystemComponents() {
  return request({
    url: '/dwms/component/system',
    method: 'get'
  })
}

// 查询用户自定义组件列表
export function getUserComponents() {
  return request({
    url: '/dwms/component/user',
    method: 'get'
  })
}
