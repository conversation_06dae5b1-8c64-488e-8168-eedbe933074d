<template>
  <div class="approval-flow-container">
    <div class="approval-flow-header">
      <div class="header-left">
        <i class="el-icon-share"></i>
        <span class="header-title">审批流程</span>
      </div>
      <div class="header-right">
        <el-switch
          v-model="showOriginalFlow"
          active-text="原始样式"
          inactive-text="时间轴"
          size="mini">
        </el-switch>
      </div>
    </div>

    <!-- 优化的时间轴组件 -->
    <div v-if="!showOriginalFlow" class="responsive-timeline">
      <div class="timeline-container">
        <div
          v-for="(step, index) in steps"
          :key="index"
          class="timeline-item"
          :class="getTimelineNodeClass(step)"
        >
          <!-- 节点信息（在上方） -->
          <div class="node-info-top">
            <div class="node-title">{{ step.title }}</div>
            <div class="node-approver" v-if="step.operators">审批人：{{ step.operators }}</div>
            <div class="node-time" v-if="step.time">{{ formatTime(step.time) }}</div>
            <div class="node-remark" v-if="step.remark">备注：{{ step.remark }}</div>
          </div>

          <!-- 圆点 -->
          <div class="timeline-dot" :class="getTimelineDotClass(step)">
            <i :class="getTimelineIcon(step)"></i>
          </div>

          <!-- 连接线 -->
          <div v-if="index < steps.length - 1" class="timeline-line"></div>
        </div>
      </div>
    </div>

    <!-- 原始 el-steps 组件 -->
    <el-steps v-if="showOriginalFlow" :active="currentStep" finish-status="success" simple>
      <el-step v-for="(step, index) in steps" :key="index" :title="step.title" :icon="getStepIcon(step.status)">
        <template #description>
          <div v-if="step.status !== 'waiting'" class="step-description">
            <div>{{ getStatusText(step.status) }}</div>
            <div v-if="step.operators">处理人: {{ step.operators }}</div>
            <div v-if="step.time">时间: {{ step.time }}</div>
            <div v-if="step.remark" class="remark">备注: {{ step.remark }}</div>
          </div>
          <div v-else class="step-description">
            等待处理
          </div>
        </template>
      </el-step>
    </el-steps>

    <div v-if="showActions" class="approval-actions">
      <el-divider />
      <el-form :model="form" label-width="80px">
        <el-form-item v-if="showRejectReason" label="驳回原因">
          <el-input v-model="form.rejectReason" type="textarea" :rows="3" placeholder="请输入驳回原因" />
        </el-form-item>

        <el-form-item>
          <el-button v-if="canApprove" type="primary" @click="handleApprove">通过</el-button>
          <el-button v-if="canReject" type="danger" @click="handleReject">驳回</el-button>
          <el-button v-if="canCancel" @click="handleCancel">撤销</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: "ApprovalFlow",
  props: {
    // 审批状态：0-待验收，1-验收通过，2-驳回，3-审批中
    approvalStatus: {
      type: String,
      default: "0"
    },
    // 审批人
    approvalBy: {
      type: String,
      default: ""
    },
    // 审批时间
    approvalTime: {
      type: String,
      default: ""
    },
    // 审批备注
    approvalRemark: {
      type: String,
      default: ""
    },
    // 是否显示操作按钮
    showActions: {
      type: Boolean,
      default: false
    },
    // 当前用户是否有审批权限
    hasApprovalPermission: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        rejectReason: ""
      },
      showRejectReason: false,
      showOriginalFlow: false, // 默认显示优化的时间轴
      // 定义审批步骤
      steps: [
        { title: "提交", status: "process" },
        { title: "审批", status: "waiting" },
        { title: "完成", status: "waiting" }
      ]
    };
  },
  computed: {
    // 当前步骤
    currentStep() {
      switch (this.approvalStatus) {
        case "0": // 待验收
          return 0;
        case "1": // 验收通过
          return 2;
        case "2": // 驳回
          return 2;
        case "3": // 审批中
          return 1;
        default:
          return 0;
      }
    },
    // 是否可以审批通过
    canApprove() {
      // 只有在审批中状态且有权限时才能审批
      return this.hasApprovalPermission && this.approvalStatus === "3";
    },
    // 是否可以驳回
    canReject() {
      // 只有在审批中状态且有权限时才能驳回
      return this.hasApprovalPermission && this.approvalStatus === "3";
    },
    // 是否可以撤销
    canCancel() {
      // 只有在审批中状态时才能撤销
      return this.approvalStatus === "3";
    },
    // 流程是否已结束
    isProcessFinished() {
      return this.approvalStatus === "1" || this.approvalStatus === "2";
    }
  },
  created() {
    this.initSteps();
  },
  methods: {
    // 初始化步骤状态
    initSteps() {
      // 根据审批状态设置步骤
      switch (this.approvalStatus) {
        case "0": // 待验收
          this.steps = [
            { title: "提交", status: "process" },
            { title: "审批", status: "waiting" },
            { title: "完成", status: "waiting" }
          ];
          break;
        case "1": // 验收通过
          this.steps = [
            { title: "提交", status: "success", time: this.approvalTime },
            { title: "审批", status: "success", operators: this.approvalBy, time: this.approvalTime },
            { title: "完成", status: "success", time: this.approvalTime }
          ];
          break;
        case "2": // 驳回
          this.steps = [
            { title: "提交", status: "success", time: this.approvalTime },
            { title: "审批", status: "error", operators: this.approvalBy, time: this.approvalTime, remark: this.approvalRemark },
            { title: "完成", status: "error", time: this.approvalTime }
          ];
          break;
        case "3": // 审批中
          this.steps = [
            { title: "提交", status: "success", time: this.approvalTime },
            { title: "审批", status: "process" },
            { title: "完成", status: "waiting" }
          ];
          break;
      }
    },
    // 获取步骤图标
    getStepIcon(status) {
      switch (status) {
        case "success":
          return "el-icon-check";
        case "error":
          return "el-icon-close";
        case "process":
          return "el-icon-loading";
        default:
          return "";
      }
    },
    // 获取状态文本
    getStatusText(status) {
      switch (status) {
        case "success":
          return "已完成";
        case "error":
          return "被驳回";
        case "process":
          return "处理中";
        case "waiting":
          return "待处理";
        default:
          return "";
      }
    },
    // 审批通过
    handleApprove() {
      this.$emit("approve");
    },
    // 审批驳回
    handleReject() {
      if (this.showRejectReason) {
        if (!this.form.rejectReason) {
          this.$message.warning("请输入驳回原因");
          return;
        }
        this.$emit("reject", this.form.rejectReason);
        this.showRejectReason = false;
      } else {
        this.showRejectReason = true;
      }
    },
    // 撤销审批
    handleCancel() {
      this.$confirm("确认撤销当前审批流程？", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.$emit("cancel");
      }).catch(() => {});
    },

    // 时间轴相关方法
    getTimelineNodeClass(step) {
      return {
        'completed': step.status === 'success',
        'current': step.status === 'process',
        'rejected': step.status === 'error',
        'pending': step.status === 'waiting'
      };
    },

    getTimelineDotClass(step) {
      return {
        'completed': step.status === 'success',
        'current': step.status === 'process',
        'rejected': step.status === 'error',
        'pending': step.status === 'waiting'
      };
    },

    getTimelineIcon(step) {
      switch (step.status) {
        case 'success':
          return 'el-icon-check';
        case 'error':
          return 'el-icon-close';
        case 'process':
          return 'el-icon-loading';
        default:
          return 'el-icon-time';
      }
    },

    formatTime(time) {
      if (!time) return '';
      // 简单的时间格式化
      return time;
    }
  }
};
</script>

<style scoped>
.approval-flow-container {
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.approval-flow-header {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.header-right {
  display: flex;
  align-items: center;
}

.step-description {
  font-size: 12px;
  margin-top: 5px;
  color: #606266;
}

.remark {
  color: #f56c6c;
}

.approval-actions {
  margin-top: 20px;
}

/* 响应式时间轴样式 */
.responsive-timeline {
  width: 100%;
  overflow-x: auto;
  padding: 10px 0;
}

.timeline-container {
  display: flex;
  align-items: flex-start;
  min-width: fit-content;
  position: relative;
  padding: 20px 0;
}

.timeline-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  min-width: 150px;
  margin: 0 10px;
}

.node-info-top {
  background: white;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e4e7ed;
  min-height: 80px;
  width: 100%;
  text-align: center;
}

.node-title {
  font-weight: 600;
  font-size: 14px;
  color: #303133;
  margin-bottom: 6px;
}

.node-approver, .node-time, .node-remark {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
  word-wrap: break-word;
}

.node-remark {
  color: #f56c6c;
}

.timeline-dot {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
  font-size: 12px;
  color: white;
  transition: all 0.3s ease;
}

.timeline-dot.completed {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  box-shadow: 0 0 0 4px rgba(103, 194, 58, 0.2);
}

.timeline-dot.current {
  background: linear-gradient(135deg, #409eff, #66b1ff);
  box-shadow: 0 0 0 4px rgba(64, 158, 255, 0.2);
  animation: pulse 2s infinite;
}

.timeline-dot.rejected {
  background: linear-gradient(135deg, #f56c6c, #f78989);
  box-shadow: 0 0 0 4px rgba(245, 108, 108, 0.2);
}

.timeline-dot.pending {
  background: #dcdfe6;
  color: #909399;
}

.timeline-line {
  position: absolute;
  top: 50%;
  left: calc(50% + 12px);
  right: calc(-100% + 50% - 12px);
  height: 2px;
  background: #e4e7ed;
  z-index: 1;
  transform: translateY(-50%);
}

.timeline-item.completed .timeline-line {
  background: linear-gradient(90deg, #67c23a, #85ce61);
}

.timeline-item.current .timeline-line {
  background: linear-gradient(90deg, #409eff, #66b1ff);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 4px rgba(64, 158, 255, 0.2);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(64, 158, 255, 0.1);
  }
  100% {
    box-shadow: 0 0 0 4px rgba(64, 158, 255, 0.2);
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .timeline-item {
    min-width: 130px;
    margin: 0 8px;
  }

  .node-info-top {
    padding: 10px;
    min-height: 70px;
  }

  .node-title {
    font-size: 13px;
  }

  .node-approver, .node-time, .node-remark {
    font-size: 11px;
  }
}

@media (max-width: 768px) {
  .timeline-item {
    min-width: 110px;
    margin: 0 6px;
  }

  .node-info-top {
    padding: 8px;
    min-height: 60px;
  }

  .node-title {
    font-size: 12px;
  }

  .node-approver, .node-time, .node-remark {
    font-size: 10px;
  }

  .timeline-dot {
    width: 20px;
    height: 20px;
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .timeline-item {
    min-width: 90px;
    margin: 0 4px;
  }

  .node-info-top {
    padding: 6px;
    min-height: 50px;
  }

  .node-title {
    font-size: 11px;
  }

  .node-approver, .node-time, .node-remark {
    font-size: 9px;
  }

  .timeline-dot {
    width: 18px;
    height: 18px;
    font-size: 9px;
  }
}
</style>