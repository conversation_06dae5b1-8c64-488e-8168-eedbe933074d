import request from '@/utils/request'

// 获取 ZLMediaKit 配置信息
export function getVideoConfig() {
  return request({
    url: '/video/test/config',
    method: 'get'
  })
}

// 测试 ZLMediaKit 连接
export function testZlmConnection() {
  return request({
    url: '/video/test/zlmediakit',
    method: 'get'
  })
}

// 测试 ZLMediaKit API
export function testZlmApi() {
  return request({
    url: '/video/test/api',
    method: 'get'
  })
}

// 获取 ZLMediaKit API 列表
export function getZlmApiList() {
  return request({
    url: '/video/test/api-list',
    method: 'get'
  })
}

// 获取 ZLMediaKit 服务器信息
export function getZlmServerInfo() {
  return request({
    url: '/video/test/server',
    method: 'get'
  })
}

// 获取媒体流列表
export function getMediaStreams() {
  return request({
    url: '/video/zlm/streams',
    method: 'get'
  })
}

// 关闭指定流
export function closeStream(vhost, app, stream) {
  return request({
    url: '/video/zlm/stream/close',
    method: 'post',
    data: {
      vhost,
      app,
      stream
    }
  })
}

// 开始录制
export function startRecord(vhost, app, stream, type = 1) {
  return request({
    url: '/video/zlm/record/start',
    method: 'post',
    data: {
      vhost,
      app,
      stream,
      type
    }
  })
}

// 停止录制
export function stopRecord(vhost, app, stream, type = 1) {
  return request({
    url: '/video/zlm/record/stop',
    method: 'post',
    data: {
      vhost,
      app,
      stream,
      type
    }
  })
}

// 获取录制状态
export function getRecordStatus(vhost, app, stream, type = 1) {
  return request({
    url: '/video/zlm/record/status',
    method: 'get',
    params: {
      vhost,
      app,
      stream,
      type
    }
  })
}

// 重启 ZLMediaKit 服务器
export function restartZlmServer() {
  return request({
    url: '/video/zlm/server/restart',
    method: 'post'
  })
}

// 获取 ZLMediaKit 版本信息
export function getZlmVersion() {
  return request({
    url: '/video/zlm/version',
    method: 'get'
  })
}

// 测试推流
export function testPushStream(url, duration = 10) {
  return request({
    url: '/video/test/push',
    method: 'post',
    data: {
      url,
      duration
    }
  })
}

// 测试拉流
export function testPullStream(url) {
  return request({
    url: '/video/test/pull',
    method: 'post',
    data: {
      url
    }
  })
}
