<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="设备编号" prop="deviceCode">
        <el-input
          v-model="queryParams.deviceCode"
          placeholder="请输入设备编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="命令类型" prop="commandType">
        <el-select v-model="queryParams.commandType" placeholder="请选择命令类型" clearable>
          <el-option label="开门" value="open"></el-option>
          <el-option label="关门" value="close"></el-option>
          <el-option label="解锁" value="unlock"></el-option>
          <el-option label="上锁" value="lock"></el-option>
          <el-option label="重启" value="reboot"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="命令状态" prop="commandStatus">
        <el-select v-model="queryParams.commandStatus" placeholder="请选择命令状态" clearable>
          <el-option label="待执行" value="pending"></el-option>
          <el-option label="成功" value="success"></el-option>
          <el-option label="失败" value="failed"></el-option>
          <el-option label="超时" value="timeout"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="操作人员" prop="operator">
        <el-input
          v-model="queryParams.operator"
          placeholder="请输入操作人员"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="执行时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['access:device:command:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['access:device:command:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-refresh"
          size="mini"
          @click="refreshData"
        >刷新</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="commandList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" width="80" />
      <el-table-column label="设备编号" align="center" prop="deviceCode" width="120" />
      <el-table-column label="设备名称" align="center" prop="deviceName" width="150" :show-overflow-tooltip="true" />
      <el-table-column label="命令类型" align="center" prop="commandType" width="100">
        <template slot-scope="scope">
          <el-tag :type="getCommandTypeColor(scope.row.commandType)">
            {{ getCommandTypeName(scope.row.commandType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="命令状态" align="center" prop="commandStatus" width="100">
        <template slot-scope="scope">
          <el-tag :type="getCommandStatusColor(scope.row.commandStatus)">
            <i :class="getCommandStatusIcon(scope.row.commandStatus)"></i>
            {{ getCommandStatusName(scope.row.commandStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作人员" align="center" prop="operator" width="100" />
      <el-table-column label="执行时间" align="center" prop="executeTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.executeTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="响应时间" align="center" prop="responseTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.responseTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="耗时" align="center" width="80">
        <template slot-scope="scope">
          <span>{{ getExecutionDuration(scope.row.executeTime, scope.row.responseTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['access:device:command:remove']"
          >删除</el-button>
          <el-button
            v-if="scope.row.commandStatus === 'failed'"
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="handleRetry(scope.row)"
          >重试</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 命令详情对话框 -->
    <el-dialog title="命令详情" :visible.sync="detailDialogVisible" width="800px">
      <el-descriptions :column="2" border v-if="selectedCommand">
        <el-descriptions-item label="命令ID">{{ selectedCommand.id }}</el-descriptions-item>
        <el-descriptions-item label="设备编号">{{ selectedCommand.deviceCode }}</el-descriptions-item>
        <el-descriptions-item label="设备名称">{{ selectedCommand.deviceName }}</el-descriptions-item>
        <el-descriptions-item label="命令类型">{{ getCommandTypeName(selectedCommand.commandType) }}</el-descriptions-item>
        <el-descriptions-item label="命令状态">
          <el-tag :type="getCommandStatusColor(selectedCommand.commandStatus)">
            {{ getCommandStatusName(selectedCommand.commandStatus) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="MQTT主题">{{ selectedCommand.mqttTopic }}</el-descriptions-item>
        <el-descriptions-item label="操作人员">{{ selectedCommand.operator }}</el-descriptions-item>
        <el-descriptions-item label="执行时间">{{ parseTime(selectedCommand.executeTime) }}</el-descriptions-item>
        <el-descriptions-item label="响应时间">{{ parseTime(selectedCommand.responseTime) }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(selectedCommand.createTime) }}</el-descriptions-item>
      </el-descriptions>
      
      <div style="margin-top: 20px;">
        <h4>命令数据</h4>
        <el-input
          type="textarea"
          :rows="4"
          placeholder="命令数据"
          :value="formatJsonData(selectedCommand.commandData)"
          readonly>
        </el-input>
      </div>
      
      <div style="margin-top: 20px;" v-if="selectedCommand.responseData">
        <h4>响应数据</h4>
        <el-input
          type="textarea"
          :rows="4"
          placeholder="响应数据"
          :value="formatJsonData(selectedCommand.responseData)"
          readonly>
        </el-input>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "AccessDeviceCommand",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 命令记录表格数据
      commandList: [],
      // 弹出层标题
      title: "",
      // 是否显示详情对话框
      detailDialogVisible: false,
      // 选中的命令
      selectedCommand: null,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceCode: null,
        commandType: null,
        commandStatus: null,
        operator: null,
        beginTime: null,
        endTime: null
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询命令记录列表 */
    getList() {
      this.loading = true;
      // 模拟数据，实际项目中应该调用API
      setTimeout(() => {
        this.commandList = [
          {
            id: 1,
            deviceId: 1,
            deviceCode: 'FACE_001',
            deviceName: '1号库房入口人脸门禁',
            commandType: 'open',
            commandData: '{"action": "open", "timeout": 30}',
            mqttTopic: 'dwms/access/face_001/command',
            commandStatus: 'success',
            responseData: '{"result": "success", "message": "门已打开"}',
            executeTime: new Date(Date.now() - 3600000),
            responseTime: new Date(Date.now() - 3599000),
            operator: 'admin',
            createTime: new Date(Date.now() - 3600000)
          },
          {
            id: 2,
            deviceId: 3,
            deviceCode: 'LOCK_001',
            deviceName: '1号库房IP磁力锁',
            commandType: 'unlock',
            commandData: '{"action": "unlock", "force": 500}',
            mqttTopic: 'dwms/lock/lock_001/command',
            commandStatus: 'failed',
            responseData: '{"result": "failed", "error": "设备无响应"}',
            executeTime: new Date(Date.now() - 1800000),
            responseTime: null,
            operator: 'admin',
            createTime: new Date(Date.now() - 1800000)
          },
          {
            id: 3,
            deviceId: 2,
            deviceCode: 'FACE_002',
            deviceName: '2号库房入口人脸门禁',
            commandType: 'reboot',
            commandData: '{"action": "reboot"}',
            mqttTopic: 'dwms/access/face_002/command',
            commandStatus: 'pending',
            responseData: null,
            executeTime: new Date(Date.now() - 300000),
            responseTime: null,
            operator: 'admin',
            createTime: new Date(Date.now() - 300000)
          }
        ];
        this.total = this.commandList.length;
        this.loading = false;
      }, 500);
    },
    /** 刷新数据 */
    refreshData() {
      this.getList();
      this.$message.success("数据已刷新");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      if (this.dateRange && this.dateRange.length === 2) {
        this.queryParams.beginTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      } else {
        this.queryParams.beginTime = null;
        this.queryParams.endTime = null;
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.multiple = !selection.length
    },
    /** 查看详情 */
    handleDetail(row) {
      this.selectedCommand = row;
      this.detailDialogVisible = true;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除命令记录编号为"' + ids + '"的数据项?').then(function() {
        // 模拟删除操作
        return Promise.resolve();
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 重试命令 */
    handleRetry(row) {
      this.$modal.confirm('是否确认重试命令"' + this.getCommandTypeName(row.commandType) + '"?').then(function() {
        // 模拟重试操作
        return Promise.resolve();
      }).then(() => {
        this.$modal.msgSuccess("重试命令已发送");
        this.getList();
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('access/device/command/export', {
        ...this.queryParams
      }, `命令记录_${new Date().getTime()}.xlsx`)
    },
    // 获取命令类型名称
    getCommandTypeName(type) {
      const typeMap = {
        'open': '开门',
        'close': '关门',
        'unlock': '解锁',
        'lock': '上锁',
        'reboot': '重启'
      };
      return typeMap[type] || type;
    },
    // 获取命令类型颜色
    getCommandTypeColor(type) {
      const colorMap = {
        'open': 'success',
        'close': 'warning',
        'unlock': 'primary',
        'lock': 'info',
        'reboot': 'danger'
      };
      return colorMap[type] || '';
    },
    // 获取命令状态名称
    getCommandStatusName(status) {
      const statusMap = {
        'pending': '待执行',
        'success': '成功',
        'failed': '失败',
        'timeout': '超时'
      };
      return statusMap[status] || status;
    },
    // 获取命令状态颜色
    getCommandStatusColor(status) {
      const colorMap = {
        'pending': 'warning',
        'success': 'success',
        'failed': 'danger',
        'timeout': 'info'
      };
      return colorMap[status] || '';
    },
    // 获取命令状态图标
    getCommandStatusIcon(status) {
      const iconMap = {
        'pending': 'el-icon-time',
        'success': 'el-icon-success',
        'failed': 'el-icon-error',
        'timeout': 'el-icon-warning'
      };
      return iconMap[status] || '';
    },
    // 获取执行耗时
    getExecutionDuration(executeTime, responseTime) {
      if (!executeTime || !responseTime) return '-';
      
      const start = new Date(executeTime);
      const end = new Date(responseTime);
      const diffMs = end - start;
      
      if (diffMs < 1000) return diffMs + 'ms';
      return Math.round(diffMs / 1000) + 's';
    },
    // 格式化JSON数据
    formatJsonData(data) {
      if (!data) return '';
      try {
        return JSON.stringify(JSON.parse(data), null, 2);
      } catch (e) {
        return data;
      }
    }
  }
};
</script>
