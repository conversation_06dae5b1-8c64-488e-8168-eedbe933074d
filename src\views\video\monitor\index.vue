<template>
  <div class="video-monitor-container">
    <el-card>
      <div slot="header">
        <span>视频监控中心</span>
        <el-button-group style="float: right;">
          <el-button size="mini" type="primary">全屏</el-button>
          <el-button size="mini" type="success">布局</el-button>
          <el-button size="mini" type="info">刷新</el-button>
        </el-button-group>
      </div>
      
      <el-row :gutter="20">
        <!-- 左侧设备列表 -->
        <el-col :span="6">
          <el-card class="device-list-card" v-loading="loading">
            <div slot="header">
              <span>设备列表</span>
              <el-button type="text" size="mini" @click="loadDeviceList" style="float: right;">
                <i class="el-icon-refresh"></i> 刷新
              </el-button>
            </div>
            <el-input
              v-model="searchText"
              placeholder="搜索设备名称或编码"
              size="small"
              clearable
              style="margin-bottom: 10px;">
              <i slot="prefix" class="el-input__icon el-icon-search"></i>
            </el-input>
            <div class="device-list">
              <div v-if="filteredDeviceList.length === 0" class="empty-device-list">
                <i class="el-icon-video-camera"></i>
                <p>{{ deviceList.length === 0 ? '暂无设备' : '未找到匹配的设备' }}</p>
              </div>
              <div v-for="device in filteredDeviceList" :key="device.id" class="device-item" @click="selectDevice(device)">
                <i class="el-icon-video-camera" :style="{ color: device.status === 1 ? '#67C23A' : '#F56C6C' }"></i>
                <div class="device-info">
                  <div class="device-name">{{ device.deviceName }}</div>
                  <div class="device-code">{{ device.deviceCode }}</div>
                  <div class="device-location">{{ device.location || '未设置位置' }}</div>
                </div>
                <div class="device-actions">
                  <el-tag :type="device.status === 1 ? 'success' : 'danger'" size="mini">
                    {{ device.status === 1 ? '在线' : '离线' }}
                  </el-tag>
                  <el-button
                    type="text"
                    size="mini"
                    :disabled="device.status !== 1"
                    @click.stop="playDevice(device)">
                    播放
                  </el-button>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧视频监控区域 -->
        <el-col :span="18">
          <el-card class="monitor-card">
            <div slot="header">
              <span>监控画面</span>
              <el-radio-group v-model="currentLayout" size="small" style="float: right;">
                <el-radio-button label="1x1">1画面</el-radio-button>
                <el-radio-button label="2x2">4画面</el-radio-button>
                <el-radio-button label="3x3">9画面</el-radio-button>
              </el-radio-group>
            </div>
            
            <!-- 视频播放网格 -->
            <div class="video-grid" :class="'grid-' + currentLayout">
              <div v-for="(slot, index) in videoSlots" :key="index"
                   class="video-slot"
                   :class="{ 'selected': selectedSlot === index }"
                   @click="selectSlot(index)">
                <div v-if="slot.device" class="video-player">
                  <!-- 视频播放组件 -->
                  <VideoPlayer
                    v-if="slot.isPlaying"
                    :src="slot.playUrl"
                    :type="slot.streamType"
                    :autoplay="true"
                    :muted="true"
                    @play="onVideoPlay(index)"
                    @pause="onVideoPause(index)"
                    @error="onVideoError(index, $event)"
                    @snapshot="onVideoSnapshot(index, $event)"
                    @record="onVideoRecord(index)"
                    class="video-component">
                  </VideoPlayer>

                  <!-- 设备预览状态 -->
                  <div v-else class="video-placeholder">
                    <i class="el-icon-video-camera"></i>
                    <p>{{ slot.device.deviceName }}</p>
                    <p class="device-info">{{ slot.device.deviceCode }}</p>
                    <p class="device-location">{{ slot.device.location }}</p>
                    <p class="status">{{ slot.device.status === 1 ? '在线' : '离线' }}</p>
                    <el-button
                      v-if="slot.device.status === 1"
                      type="primary"
                      size="mini"
                      @click.stop="startPlay(index)">
                      开始播放
                    </el-button>
                    <el-button
                      v-else
                      type="info"
                      size="mini"
                      disabled>
                      设备离线
                    </el-button>
                  </div>

                  <!-- 设备信息覆盖层 -->
                  <div class="device-overlay">
                    <span class="device-name">{{ slot.device.deviceName }}</span>
                    <el-tag :type="slot.device.status === 1 ? 'success' : 'danger'" size="mini">
                      {{ slot.device.status === 1 ? '在线' : '离线' }}
                    </el-tag>
                  </div>

                  <!-- 快捷控制按钮 -->
                  <div class="quick-controls">
                    <el-button-group size="mini">
                      <el-button
                        :icon="slot.isPlaying ? 'el-icon-video-pause' : 'el-icon-video-play'"
                        @click.stop="togglePlay(index)"
                        :disabled="slot.device.status !== 1">
                      </el-button>
                      <el-button icon="el-icon-refresh" @click.stop="refreshStream(index)"></el-button>
                      <el-button icon="el-icon-close" @click.stop="closeSlot(index)"></el-button>
                    </el-button-group>
                  </div>
                </div>
                <div v-else class="empty-slot">
                  <i class="el-icon-plus"></i>
                  <p>点击添加设备</p>
                  <p class="slot-number">窗口 {{ index + 1 }}</p>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { listDevice } from "@/api/video/device";
import VideoPlayer from "@/components/VideoPlayer/index.vue";
import videoStreamMonitor from "@/utils/videoStreamMonitor";

export default {
  name: "VideoMonitor",
  components: {
    VideoPlayer
  },
  data() {
    return {
      currentLayout: "2x2",
      deviceList: [],
      videoSlots: [],
      selectedSlot: -1,
      loading: false,
      searchText: ""
    };
  },
  computed: {
    filteredDeviceList() {
      if (!this.searchText) {
        return this.deviceList;
      }
      return this.deviceList.filter(device =>
        device.deviceName.toLowerCase().includes(this.searchText.toLowerCase()) ||
        device.deviceCode.toLowerCase().includes(this.searchText.toLowerCase())
      );
    }
  },
  created() {
    this.initLayout();
    this.loadDeviceList();
  },
  beforeDestroy() {
    // 清理流监控
    this.videoSlots.forEach((slot, index) => {
      if (slot.device && slot.isPlaying) {
        const streamId = this.getStreamId(slot.device);
        videoStreamMonitor.removeStream(streamId);
      }
    });
  },
  watch: {
    currentLayout() {
      this.initLayout();
    }
  },
  methods: {
    /** 加载设备列表 */
    loadDeviceList() {
      this.loading = true;
      listDevice({}).then(response => {
        this.deviceList = response.rows || [];
        console.log('加载设备列表:', this.deviceList);
      }).catch(error => {
        console.error('加载设备列表失败:', error);
        this.$message.error('加载设备列表失败');
      }).finally(() => {
        this.loading = false;
      });
    },
    initLayout() {
      const layouts = { "1x1": 1, "2x2": 4, "3x3": 9 };
      const slotCount = layouts[this.currentLayout];
      this.videoSlots = Array.from({ length: slotCount }, () => ({ device: null }));
    },
    selectDevice(device) {
      console.log('选择设备:', device);
      // 如果有选中的播放窗口，直接播放到该窗口
      if (this.selectedSlot >= 0) {
        this.playDeviceToSlot(device, this.selectedSlot);
      } else {
        // 否则查找空闲窗口
        this.playDevice(device);
      }
    },
    playDevice(device) {
      if (device.status !== 1) {
        this.$message.warning(`设备 ${device.deviceName} 离线，无法播放`);
        return;
      }

      const emptySlotIndex = this.videoSlots.findIndex(slot => !slot.device);
      if (emptySlotIndex >= 0) {
        this.playDeviceToSlot(device, emptySlotIndex);
      } else {
        this.$message.warning('没有空闲的播放窗口，请先关闭其他播放');
      }
    },
    playDeviceToSlot(device, slotIndex) {
      if (device.status !== 1) {
        this.$message.warning(`设备 ${device.deviceName} 离线，无法播放`);
        return;
      }

      // 设置设备到指定窗口
      this.$set(this.videoSlots, slotIndex, {
        device: device,
        playUrl: null,
        streamType: this.getStreamType(device),
        isPlaying: false,
        error: null
      });

      // 开始播放
      this.startPlay(slotIndex);

      this.selectedSlot = -1; // 清除选中状态
    },
    generatePlayUrl(device) {
      // 根据设备信息生成播放URL
      // 这里需要根据实际的流媒体服务器配置
      const streamId = device.deviceCode || device.id;
      const baseUrl = process.env.VUE_APP_STREAM_SERVER || 'http://localhost:8080';

      // 根据设备类型和配置选择流格式
      if (device.streamType === 'hls') {
        return `${baseUrl}/live/${streamId}.m3u8`;
      } else {
        return `${baseUrl}/live/${streamId}.flv`;
      }
    },

    getStreamType(device) {
      // 根据设备配置确定流类型
      return device.streamType || 'flv';
    },

    getStreamId(device) {
      return `stream_${device.id}_${device.deviceCode}`;
    },

    startPlay(slotIndex) {
      const slot = this.videoSlots[slotIndex];
      if (!slot || !slot.device) return;

      if (slot.device.status !== 1) {
        this.$message.warning(`设备 ${slot.device.deviceName} 离线，无法播放`);
        return;
      }

      // 设置播放参数
      slot.playUrl = this.generatePlayUrl(slot.device);
      slot.streamType = this.getStreamType(slot.device);
      slot.isPlaying = true;
      slot.error = null;
      slot.streamStatus = 'connecting';

      // 添加流状态监控
      const streamId = this.getStreamId(slot.device);
      videoStreamMonitor.addStream(streamId, slot.playUrl, (stream) => {
        this.onStreamStatusChange(slotIndex, stream);
      });

      console.log('开始播放:', {
        device: slot.device.deviceName,
        url: slot.playUrl,
        type: slot.streamType,
        streamId: streamId
      });

      this.$message.success(`开始播放 ${slot.device.deviceName}`);
    },
    selectSlot(index) {
      this.selectedSlot = this.selectedSlot === index ? -1 : index;
    },
    togglePlay(index) {
      const slot = this.videoSlots[index];
      if (!slot || !slot.device) return;

      if (slot.isPlaying) {
        this.stopPlay(index);
      } else {
        this.startPlay(index);
      }
    },
    stopPlay(index) {
      const slot = this.videoSlots[index];
      if (slot && slot.device) {
        // 移除流监控
        const streamId = this.getStreamId(slot.device);
        videoStreamMonitor.removeStream(streamId);

        slot.isPlaying = false;
        slot.playUrl = null;
        slot.streamStatus = null;

        console.log('停止播放:', slot.device.deviceName);
        this.$message.info(`已停止播放 ${slot.device.deviceName}`);
      }
    },

    onStreamStatusChange(slotIndex, stream) {
      const slot = this.videoSlots[slotIndex];
      if (!slot) return;

      slot.streamStatus = stream.status;

      console.log('流状态变化:', {
        device: slot.device.deviceName,
        status: stream.status,
        errorCount: stream.errorCount
      });

      // 根据流状态更新UI或采取相应行动
      switch (stream.status) {
        case 'online':
          // 流正常
          break;
        case 'offline':
          this.$message.warning(`${slot.device.deviceName} 视频流离线`);
          break;
        case 'error':
          if (stream.errorCount >= 3) {
            this.$message.error(`${slot.device.deviceName} 视频流连接失败`);
          }
          break;
      }
    },

    refreshStream(index) {
      const slot = this.videoSlots[index];
      if (!slot || !slot.device) return;

      // 重新生成播放URL并刷新流
      this.stopPlay(index);
      setTimeout(() => {
        this.startPlay(index);
      }, 500);

      this.$message.info(`正在刷新 ${slot.device.deviceName} 的视频流`);
    },

    // 视频播放事件处理
    onVideoPlay(index) {
      const slot = this.videoSlots[index];
      if (slot) {
        console.log('视频开始播放:', slot.device.deviceName);
      }
    },

    onVideoPause(index) {
      const slot = this.videoSlots[index];
      if (slot) {
        console.log('视频暂停播放:', slot.device.deviceName);
      }
    },

    onVideoError(index, error) {
      const slot = this.videoSlots[index];
      if (slot) {
        slot.error = error;
        slot.isPlaying = false;
        console.error('视频播放错误:', slot.device.deviceName, error);
        this.$message.error(`${slot.device.deviceName} 播放失败: ${error}`);
      }
    },

    onVideoSnapshot(index, blob) {
      const slot = this.videoSlots[index];
      if (slot) {
        console.log('截图成功:', slot.device.deviceName);
        // 可以在这里上传截图到服务器或进行其他处理
        this.$emit('snapshot', {
          device: slot.device,
          blob: blob,
          timestamp: new Date()
        });
      }
    },

    onVideoRecord(index) {
      const slot = this.videoSlots[index];
      if (slot) {
        // 跳转到录制管理页面并预填设备信息
        this.$router.push({
          path: '/video/record',
          query: {
            deviceId: slot.device.id,
            deviceName: slot.device.deviceName
          }
        });
      }
    },
    takeSnapshot(index) {
      const slot = this.videoSlots[index];
      if (!slot || !slot.device) return;

      // TODO: 实现截图功能
      console.log('截图:', slot.device.deviceName);
      this.$message.success(`${slot.device.deviceName} 截图成功`);
    },
    startRecord(index) {
      const slot = this.videoSlots[index];
      if (!slot || !slot.device) return;

      // TODO: 实现录制功能，可以跳转到录制管理页面
      console.log('开始录制:', slot.device.deviceName);
      this.$message.success(`开始录制 ${slot.device.deviceName}`);
    },
    fullScreen(index) {
      const slot = this.videoSlots[index];
      if (!slot || !slot.device) return;

      // TODO: 实现全屏播放
      console.log('全屏播放:', slot.device.deviceName);
      this.$message.info('全屏功能开发中...');
    },
    closeSlot(index) {
      const slot = this.videoSlots[index];
      if (slot && slot.device) {
        const deviceName = slot.device.deviceName;
        this.stopPlay(index);
        this.$set(this.videoSlots, index, { device: null });
        this.$message.success(`已关闭 ${deviceName}`);
      }
    }
  }
};
</script>

<style scoped>
.video-monitor-container {
  padding: 20px;
}

.device-list-card {
  height: 600px;
}

.device-list {
  height: 500px;
  overflow-y: auto;
}

.device-item {
  display: flex;
  align-items: center;
  padding: 10px 8px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: background-color 0.3s;
}

.device-item:hover {
  background-color: #f5f7fa;
}

.device-info {
  flex: 1;
  margin-left: 10px;
  min-width: 0;
}

.device-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.device-code {
  font-size: 12px;
  color: #909399;
  margin-bottom: 2px;
}

.device-location {
  font-size: 11px;
  color: #C0C4CC;
}

.device-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.empty-device-list {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-device-list i {
  font-size: 48px;
  margin-bottom: 10px;
  display: block;
}

.device-item i {
  margin-right: 8px;
}

.device-item span {
  flex: 1;
  margin-right: 8px;
}

.monitor-card {
  height: 600px;
}

.video-grid {
  display: grid;
  gap: 10px;
  height: 500px;
}

.grid-1x1 {
  grid-template-columns: 1fr;
}

.grid-2x2 {
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

.grid-3x3 {
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
}

.video-slot {
  position: relative;
  border: 2px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  background: #f5f5f5;
}

.video-slot:hover {
  border-color: #409EFF;
}

.video-slot.selected {
  border-color: #409EFF;
  box-shadow: 0 0 8px rgba(64, 158, 255, 0.3);
}

.video-player {
  position: relative;
  width: 100%;
  height: 100%;
}

.video-component {
  width: 100%;
  height: 100%;
}

.device-overlay {
  position: absolute;
  top: 8px;
  left: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  z-index: 10;
}

.device-name {
  font-weight: 500;
}

.quick-controls {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.3s;
}

.video-slot:hover .quick-controls {
  opacity: 1;
}

.video-placeholder {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  background: #000;
  color: white;
}

.video-placeholder i {
  font-size: 48px;
  color: #409EFF;
  margin-bottom: 10px;
}

.video-controls {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.3s;
}

.video-slot:hover .video-controls {
  opacity: 1;
}

.empty-slot {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #999;
  background: #f9f9f9;
  transition: background-color 0.3s;
}

.empty-slot:hover {
  background: #f0f0f0;
  color: #666;
}

.empty-slot i {
  font-size: 48px;
  margin-bottom: 10px;
}

.slot-number {
  font-size: 12px;
  color: #ccc;
  margin-top: 5px;
}

.status {
  font-size: 12px;
  margin-top: 5px;
}

.device-info {
  font-size: 12px;
  color: #ccc;
  margin: 2px 0;
}
</style>
