import request from '@/utils/request'

// 查询门禁记录列表
export function listLog(query) {
  return request({
    url: '/access/log/list',
    method: 'get',
    params: query
  })
}

// 查询门禁记录详细
export function getLog(id) {
  return request({
    url: '/access/log/' + id,
    method: 'get'
  })
}

// 新增门禁记录
export function addLog(data) {
  return request({
    url: '/access/log',
    method: 'post',
    data: data
  })
}

// 修改门禁记录
export function updateLog(data) {
  return request({
    url: '/access/log',
    method: 'put',
    data: data
  })
}

// 删除门禁记录
export function delLog(id) {
  return request({
    url: '/access/log/' + id,
    method: 'delete'
  })
}

// 获取门禁记录统计数据
export function getLogStatistics() {
  return request({
    url: '/access/log/statistics',
    method: 'get'
  })
}

// 导出门禁记录
export function exportLog(query) {
  return request({
    url: '/access/log/export',
    method: 'get',
    params: query
  })
}

// 清理门禁记录
export function cleanupLog(days) {
  return request({
    url: '/access/log/cleanup/' + days,
    method: 'post'
  })
}
