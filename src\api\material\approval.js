import request from '@/utils/request'

// 提交审批
export function submitApproval(billItemId) {
  return request({
    url: '/material/approval/submit/' + billItemId,
    method: 'post'
  })
}

// 提交审批（增强版）
export function submitApprovalEx(data) {
  return request({
    url: '/material/approval/submitEx',
    method: 'post',
    data: data
  })
}

// 审批通过
export function approveApproval(billItemId) {
  return request({
    url: '/material/approval/approve/' + billItemId,
    method: 'post'
  })
}

// 审批驳回
export function rejectApproval(billItemId, reason) {
  return request({
    url: '/material/approval/reject/' + billItemId,
    method: 'post',
    data: reason
  })
}

// 撤销审批
export function cancelApproval(billItemId) {
  return request({
    url: '/material/approval/cancel/' + billItemId,
    method: 'post'
  })
}

// 获取审批详情
export function getApprovalDetail(billItemId) {
  return request({
    url: '/material/approval/detail/' + billItemId,
    method: 'get'
  })
}

// 获取可用的审批流程
export function getAvailableWorkflows(businessType) {
  return request({
    url: '/approval/workflow/available',
    method: 'get',
    params: {
      businessType: businessType
    }
  })
} 