<template>
  <div
    class="touch-gesture"
    @touchstart="handleTouchStart"
    @touchmove="handleTouchMove"
    @touchend="handleTouchEnd"
    @touchcancel="handleTouchCancel"
  >
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'TouchGesture',
  
  props: {
    // 是否启用滑动手势
    swipeEnabled: {
      type: Boolean,
      default: true
    },
    // 是否启用缩放手势
    pinchEnabled: {
      type: Boolean,
      default: false
    },
    // 是否启用旋转手势
    rotateEnabled: {
      type: Boolean,
      default: false
    },
    // 是否启用长按手势
    longPressEnabled: {
      type: Boolean,
      default: false
    },
    // 滑动阈值(px)
    swipeThreshold: {
      type: Number,
      default: 50
    },
    // 缩放阈值
    pinchThreshold: {
      type: Number,
      default: 0.1
    },
    // 旋转阈值(度)
    rotateThreshold: {
      type: Number,
      default: 15
    },
    // 长按时间(ms)
    longPressTime: {
      type: Number,
      default: 500
    },
    // 是否阻止默认行为
    preventDefault: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      // 触摸状态
      touching: false,
      // 触摸点信息
      touches: [],
      // 开始触摸信息
      startTouches: [],
      // 手势状态
      gestureState: {
        type: null, // swipe, pinch, rotate, longpress
        startTime: 0,
        startDistance: 0,
        startAngle: 0,
        currentDistance: 0,
        currentAngle: 0,
        deltaX: 0,
        deltaY: 0,
        scale: 1,
        rotation: 0
      },
      // 长按定时器
      longPressTimer: null
    }
  },
  
  methods: {
    /** 触摸开始 */
    handleTouchStart(event) {
      if (this.preventDefault) {
        event.preventDefault()
      }
      
      this.touching = true
      this.touches = Array.from(event.touches)
      this.startTouches = Array.from(event.touches)
      
      // 重置手势状态
      this.gestureState = {
        type: null,
        startTime: Date.now(),
        startDistance: 0,
        startAngle: 0,
        currentDistance: 0,
        currentAngle: 0,
        deltaX: 0,
        deltaY: 0,
        scale: 1,
        rotation: 0
      }
      
      // 单点触摸
      if (this.touches.length === 1) {
        this.handleSingleTouchStart(this.touches[0])
      }
      // 双点触摸
      else if (this.touches.length === 2) {
        this.handleMultiTouchStart(this.touches)
      }
      
      this.$emit('touch-start', {
        touches: this.touches,
        gestureState: this.gestureState
      })
    },
    
    /** 触摸移动 */
    handleTouchMove(event) {
      if (!this.touching) return
      
      if (this.preventDefault) {
        event.preventDefault()
      }
      
      this.touches = Array.from(event.touches)
      
      // 单点触摸移动
      if (this.touches.length === 1 && this.startTouches.length === 1) {
        this.handleSingleTouchMove(this.touches[0])
      }
      // 双点触摸移动
      else if (this.touches.length === 2 && this.startTouches.length === 2) {
        this.handleMultiTouchMove(this.touches)
      }
      
      this.$emit('touch-move', {
        touches: this.touches,
        gestureState: this.gestureState
      })
    },
    
    /** 触摸结束 */
    handleTouchEnd(event) {
      if (!this.touching) return
      
      if (this.preventDefault) {
        event.preventDefault()
      }
      
      this.touching = false
      this.clearLongPressTimer()
      
      // 处理手势结束
      this.handleGestureEnd()
      
      this.$emit('touch-end', {
        touches: this.touches,
        gestureState: this.gestureState
      })
      
      // 重置状态
      this.touches = []
      this.startTouches = []
    },
    
    /** 触摸取消 */
    handleTouchCancel(event) {
      this.touching = false
      this.clearLongPressTimer()
      
      this.$emit('touch-cancel', {
        touches: this.touches,
        gestureState: this.gestureState
      })
      
      // 重置状态
      this.touches = []
      this.startTouches = []
    },
    
    /** 处理单点触摸开始 */
    handleSingleTouchStart(touch) {
      // 启动长按检测
      if (this.longPressEnabled) {
        this.startLongPressTimer(touch)
      }
    },
    
    /** 处理单点触摸移动 */
    handleSingleTouchMove(touch) {
      const startTouch = this.startTouches[0]
      
      // 计算移动距离
      this.gestureState.deltaX = touch.clientX - startTouch.clientX
      this.gestureState.deltaY = touch.clientY - startTouch.clientY
      
      const distance = Math.sqrt(
        Math.pow(this.gestureState.deltaX, 2) + 
        Math.pow(this.gestureState.deltaY, 2)
      )
      
      // 如果移动距离超过阈值，取消长按
      if (distance > 10) {
        this.clearLongPressTimer()
      }
      
      // 检测滑动手势
      if (this.swipeEnabled && distance > this.swipeThreshold) {
        if (!this.gestureState.type) {
          this.gestureState.type = 'swipe'
          this.handleSwipeStart()
        }
        this.handleSwipeMove()
      }
    },
    
    /** 处理多点触摸开始 */
    handleMultiTouchStart(touches) {
      if (touches.length === 2) {
        // 计算初始距离和角度
        this.gestureState.startDistance = this.getDistance(touches[0], touches[1])
        this.gestureState.startAngle = this.getAngle(touches[0], touches[1])
        this.gestureState.currentDistance = this.gestureState.startDistance
        this.gestureState.currentAngle = this.gestureState.startAngle
      }
    },
    
    /** 处理多点触摸移动 */
    handleMultiTouchMove(touches) {
      if (touches.length === 2) {
        // 计算当前距离和角度
        this.gestureState.currentDistance = this.getDistance(touches[0], touches[1])
        this.gestureState.currentAngle = this.getAngle(touches[0], touches[1])
        
        // 计算缩放比例
        this.gestureState.scale = this.gestureState.currentDistance / this.gestureState.startDistance
        
        // 计算旋转角度
        this.gestureState.rotation = this.gestureState.currentAngle - this.gestureState.startAngle
        
        // 检测缩放手势
        if (this.pinchEnabled) {
          const scaleChange = Math.abs(this.gestureState.scale - 1)
          if (scaleChange > this.pinchThreshold) {
            if (!this.gestureState.type) {
              this.gestureState.type = 'pinch'
              this.handlePinchStart()
            }
            this.handlePinchMove()
          }
        }
        
        // 检测旋转手势
        if (this.rotateEnabled) {
          const rotationChange = Math.abs(this.gestureState.rotation)
          if (rotationChange > this.rotateThreshold) {
            if (!this.gestureState.type) {
              this.gestureState.type = 'rotate'
              this.handleRotateStart()
            }
            this.handleRotateMove()
          }
        }
      }
    },
    
    /** 处理手势结束 */
    handleGestureEnd() {
      const duration = Date.now() - this.gestureState.startTime
      
      switch (this.gestureState.type) {
        case 'swipe':
          this.handleSwipeEnd()
          break
        case 'pinch':
          this.handlePinchEnd()
          break
        case 'rotate':
          this.handleRotateEnd()
          break
        default:
          // 检测点击手势
          if (duration < 200 && this.startTouches.length === 1) {
            this.handleTap()
          }
      }
    },
    
    /** 处理滑动开始 */
    handleSwipeStart() {
      this.$emit('swipe-start', {
        deltaX: this.gestureState.deltaX,
        deltaY: this.gestureState.deltaY
      })
    },
    
    /** 处理滑动移动 */
    handleSwipeMove() {
      this.$emit('swipe-move', {
        deltaX: this.gestureState.deltaX,
        deltaY: this.gestureState.deltaY
      })
    },
    
    /** 处理滑动结束 */
    handleSwipeEnd() {
      const direction = this.getSwipeDirection()
      
      this.$emit('swipe-end', {
        direction,
        deltaX: this.gestureState.deltaX,
        deltaY: this.gestureState.deltaY
      })
      
      this.$emit('swipe', {
        direction,
        deltaX: this.gestureState.deltaX,
        deltaY: this.gestureState.deltaY
      })
    },
    
    /** 处理缩放开始 */
    handlePinchStart() {
      this.$emit('pinch-start', {
        scale: this.gestureState.scale
      })
    },
    
    /** 处理缩放移动 */
    handlePinchMove() {
      this.$emit('pinch-move', {
        scale: this.gestureState.scale
      })
    },
    
    /** 处理缩放结束 */
    handlePinchEnd() {
      this.$emit('pinch-end', {
        scale: this.gestureState.scale
      })
      
      this.$emit('pinch', {
        scale: this.gestureState.scale
      })
    },
    
    /** 处理旋转开始 */
    handleRotateStart() {
      this.$emit('rotate-start', {
        rotation: this.gestureState.rotation
      })
    },
    
    /** 处理旋转移动 */
    handleRotateMove() {
      this.$emit('rotate-move', {
        rotation: this.gestureState.rotation
      })
    },
    
    /** 处理旋转结束 */
    handleRotateEnd() {
      this.$emit('rotate-end', {
        rotation: this.gestureState.rotation
      })
      
      this.$emit('rotate', {
        rotation: this.gestureState.rotation
      })
    },
    
    /** 处理点击 */
    handleTap() {
      this.$emit('tap', {
        x: this.startTouches[0].clientX,
        y: this.startTouches[0].clientY
      })
    },
    
    /** 处理长按 */
    handleLongPress() {
      this.gestureState.type = 'longpress'
      
      this.$emit('long-press', {
        x: this.startTouches[0].clientX,
        y: this.startTouches[0].clientY
      })
    },
    
    /** 启动长按定时器 */
    startLongPressTimer(touch) {
      this.clearLongPressTimer()
      
      this.longPressTimer = setTimeout(() => {
        if (this.touching) {
          this.handleLongPress()
        }
      }, this.longPressTime)
    },
    
    /** 清除长按定时器 */
    clearLongPressTimer() {
      if (this.longPressTimer) {
        clearTimeout(this.longPressTimer)
        this.longPressTimer = null
      }
    },
    
    /** 获取两点间距离 */
    getDistance(touch1, touch2) {
      const dx = touch2.clientX - touch1.clientX
      const dy = touch2.clientY - touch1.clientY
      return Math.sqrt(dx * dx + dy * dy)
    },
    
    /** 获取两点间角度 */
    getAngle(touch1, touch2) {
      const dx = touch2.clientX - touch1.clientX
      const dy = touch2.clientY - touch1.clientY
      return Math.atan2(dy, dx) * 180 / Math.PI
    },
    
    /** 获取滑动方向 */
    getSwipeDirection() {
      const { deltaX, deltaY } = this.gestureState
      const absDeltaX = Math.abs(deltaX)
      const absDeltaY = Math.abs(deltaY)
      
      if (absDeltaX > absDeltaY) {
        return deltaX > 0 ? 'right' : 'left'
      } else {
        return deltaY > 0 ? 'down' : 'up'
      }
    }
  }
}
</script>

<style scoped>
.touch-gesture {
  width: 100%;
  height: 100%;
  touch-action: none; /* 禁用浏览器默认触摸行为 */
}
</style>
