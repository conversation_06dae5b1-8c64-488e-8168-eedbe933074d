<template>
  <div class="format-comparison-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2><i class="el-icon-s-data"></i> 主题格式对比分析</h2>
      <p>对比分析不同主题格式的兼容性和转换</p>
    </div>

    <!-- 格式说明 -->
    <el-card class="format-info-card">
      <div slot="header">
        <span>支持的主题格式</span>
      </div>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="format-item dwms-format">
            <h4>DWMS格式 <el-tag type="success" size="mini">标准</el-tag></h4>
            <p><strong>格式:</strong> dwms/{module}/{deviceType}/{deviceId}/{function}</p>
            <p><strong>示例:</strong> dwms/video/camera/001/alarm</p>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="format-item warehouse-format">
            <h4>Warehouse格式 <el-tag type="warning" size="mini">兼容</el-tag></h4>
            <p><strong>格式:</strong> warehouse/{module}/{deviceCode}/{function}</p>
            <p><strong>示例:</strong> warehouse/access/deviceCode/event</p>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="format-item legacy-format">
            <h4>Legacy格式 <el-tag type="info" size="mini">历史</el-tag></h4>
            <p><strong>格式:</strong> {module}/{deviceType}/{deviceId}/{function}</p>
            <p><strong>示例:</strong> video/camera/002/status</p>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 实际主题对比 -->
    <el-card class="comparison-card">
      <div slot="header">
        <span>实际主题格式对比</span>
        <el-button style="float: right;" type="primary" size="small" @click="loadRealTopics">
          加载实际主题
        </el-button>
      </div>
      
      <el-table :data="topicComparisons" style="width: 100%">
        <el-table-column prop="originalTopic" label="原始主题" min-width="250">
          <template slot-scope="scope">
            <span class="topic-text">{{ scope.row.originalTopic }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="detectedFormat" label="检测格式" width="120" align="center">
          <template slot-scope="scope">
            <el-tag :type="getFormatTagType(scope.row.detectedFormat)" size="small">
              {{ scope.row.detectedFormat }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="valid" label="有效性" width="80" align="center">
          <template slot-scope="scope">
            <i v-if="scope.row.valid" class="el-icon-check" style="color: #67c23a; font-size: 16px;"></i>
            <i v-else class="el-icon-close" style="color: #f56c6c; font-size: 16px;"></i>
          </template>
        </el-table-column>
        
        <el-table-column prop="standardizedTopic" label="标准化主题" min-width="250">
          <template slot-scope="scope">
            <span class="standardized-topic">{{ scope.row.standardizedTopic || '-' }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="module" label="模块" width="100" />
        <el-table-column prop="function" label="功能" width="100" />
        
        <el-table-column label="操作" width="100" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="primary" @click="viewDetails(scope.row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 转换规则说明 -->
    <el-card class="rules-card">
      <div slot="header">
        <span>格式转换规则</span>
      </div>
      
      <div class="rules-content">
        <div class="rule-section">
          <h4>Warehouse → DWMS 转换规则</h4>
          <ul>
            <li>warehouse/access/deviceCode/event → dwms/access/device/id/event</li>
            <li>warehouse/weight/deviceCode/data → dwms/weight/device/id/data</li>
            <li>warehouse/rfid/deviceCode/heartbeat → dwms/device/rfid/id/heartbeat</li>
          </ul>
        </div>
        
        <div class="rule-section">
          <h4>Legacy → DWMS 转换规则</h4>
          <ul>
            <li>video/camera/001/alarm → dwms/video/camera/001/alarm</li>
            <li>access/door/002/event → dwms/access/door/002/event</li>
            <li>device/sensor/003/status → dwms/device/sensor/003/status</li>
          </ul>
        </div>
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      title="主题详细分析"
      :visible.sync="detailDialogVisible"
      width="600px"
    >
      <div v-if="selectedTopic" class="topic-detail">
        <div class="detail-item">
          <span class="label">原始主题:</span>
          <span class="value">{{ selectedTopic.originalTopic }}</span>
        </div>
        <div class="detail-item">
          <span class="label">检测格式:</span>
          <el-tag :type="getFormatTagType(selectedTopic.detectedFormat)" size="small">
            {{ selectedTopic.detectedFormat }}
          </el-tag>
        </div>
        <div class="detail-item">
          <span class="label">有效性:</span>
          <el-tag :type="selectedTopic.valid ? 'success' : 'danger'" size="small">
            {{ selectedTopic.valid ? '有效' : '无效' }}
          </el-tag>
        </div>
        <div class="detail-item">
          <span class="label">标准化主题:</span>
          <span class="value">{{ selectedTopic.standardizedTopic || '-' }}</span>
        </div>
        <div class="detail-item">
          <span class="label">模块:</span>
          <span class="value">{{ selectedTopic.module || '-' }}</span>
        </div>
        <div class="detail-item">
          <span class="label">功能:</span>
          <span class="value">{{ selectedTopic.function || '-' }}</span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'FormatComparisonSimple',
  data() {
    return {
      topicComparisons: [],
      detailDialogVisible: false,
      selectedTopic: null
    }
  },
  
  created() {
    this.loadRealTopics()
  },
  
  methods: {
    // 加载实际主题进行对比
    loadRealTopics() {
      // 模拟从图片中看到的实际主题
      const realTopics = [
        'warehouse/access/deviceCode/event',
        'warehouse/weight/deviceCode/config',
        'warehouse/weight/deviceCode/data', 
        'warehouse/rfid/deviceCode/heartbeat',
        'warehouse/weight/deviceCode/command',
        'warehouse/access/deviceCode/heartbeat',
        'dwms/video/+/alarm',
        'dwms/video/+/data',
        'dwms/video/+/heartbeat',
        'dwms/video/+/status',
        'dwms/video/+/stream'
      ]
      
      this.topicComparisons = realTopics.map(topic => this.analyzeTopicFormat(topic))
    },
    
    // 分析主题格式（前端模拟）
    analyzeTopicFormat(topic) {
      const analysis = {
        originalTopic: topic,
        valid: false,
        detectedFormat: 'CUSTOM',
        standardizedTopic: null
      }
      
      // 检测格式
      if (topic.startsWith('dwms/')) {
        analysis.detectedFormat = 'DWMS'
        analysis.valid = true
        analysis.standardizedTopic = topic
        this.parseDwmsFormat(topic, analysis)
      } else if (topic.startsWith('warehouse/')) {
        analysis.detectedFormat = 'WAREHOUSE'
        analysis.valid = true
        this.parseWarehouseFormat(topic, analysis)
      } else {
        const parts = topic.split('/')
        if (parts.length >= 3) {
          analysis.detectedFormat = 'LEGACY'
          analysis.valid = true
          analysis.standardizedTopic = 'dwms/' + topic
          this.parseLegacyFormat(topic, analysis)
        }
      }
      
      return analysis
    },
    
    // 解析DWMS格式
    parseDwmsFormat(topic, analysis) {
      const parts = topic.split('/')
      if (parts.length >= 2) analysis.module = parts[1]
      if (parts.length >= 3) analysis.deviceType = parts[2]
      if (parts.length >= 4) analysis.deviceId = parts[3]
      if (parts.length >= 5) analysis.function = parts[4]
    },
    
    // 解析Warehouse格式
    parseWarehouseFormat(topic, analysis) {
      const parts = topic.split('/')
      if (parts.length >= 2) analysis.module = parts[1]
      if (parts.length >= 3) analysis.deviceCode = parts[2]
      if (parts.length >= 4) analysis.function = parts[3]
      
      // 转换为DWMS格式
      analysis.standardizedTopic = this.convertWarehouseToDwms(parts)
    },
    
    // 解析Legacy格式
    parseLegacyFormat(topic, analysis) {
      const parts = topic.split('/')
      if (parts.length >= 1) analysis.module = parts[0]
      if (parts.length >= 2) analysis.deviceType = parts[1]
      if (parts.length >= 3) analysis.deviceId = parts[2]
      if (parts.length >= 4) analysis.function = parts[3]
    },
    
    // 转换Warehouse格式到DWMS
    convertWarehouseToDwms(parts) {
      if (parts.length < 3) return null
      
      let module = parts[1]
      // 模块映射
      if (module === 'rfid') module = 'device'
      
      let result = `dwms/${module}/device/001`
      if (parts.length >= 4) {
        result += `/${parts[3]}`
      }
      
      return result
    },
    
    // 获取格式标签类型
    getFormatTagType(format) {
      const types = {
        'DWMS': 'success',
        'WAREHOUSE': 'warning', 
        'LEGACY': 'info',
        'CUSTOM': 'danger'
      }
      return types[format] || ''
    },
    
    // 查看详情
    viewDetails(topic) {
      this.selectedTopic = topic
      this.detailDialogVisible = true
    }
  }
}
</script>

<style scoped>
.format-comparison-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.page-header p {
  margin: 5px 0 0 0;
  color: #909399;
  font-size: 14px;
}

.format-info-card, .comparison-card, .rules-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.format-item {
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 10px;
}

.dwms-format {
  background: #f0f9ff;
  border-left: 4px solid #67c23a;
}

.warehouse-format {
  background: #fefce8;
  border-left: 4px solid #e6a23c;
}

.legacy-format {
  background: #f8fafc;
  border-left: 4px solid #909399;
}

.format-item h4 {
  margin-bottom: 10px;
  color: #303133;
}

.format-item p {
  margin: 5px 0;
  font-size: 14px;
  color: #606266;
}

.topic-text {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.standardized-topic {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #67c23a;
}

.rules-content {
  padding: 20px;
}

.rule-section {
  margin-bottom: 30px;
}

.rule-section h4 {
  margin-bottom: 15px;
  color: #303133;
}

.rule-section ul {
  list-style: none;
  padding: 0;
}

.rule-section li {
  padding: 8px 0;
  border-bottom: 1px solid #ebeef5;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

.topic-detail {
  padding: 20px 0;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.detail-item .label {
  width: 120px;
  color: #909399;
  font-size: 14px;
}

.detail-item .value {
  color: #303133;
  font-size: 14px;
  font-family: 'Courier New', monospace;
}
</style>
