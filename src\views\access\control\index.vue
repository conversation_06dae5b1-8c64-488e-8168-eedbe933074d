<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="门禁设备ID" prop="deviceId">
        <el-input
          v-model="queryParams.deviceId"
          placeholder="请输入门禁设备ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="刷脸/刷卡时间" prop="accessTime">
        <el-date-picker clearable
          v-model="queryParams.accessTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择刷脸/刷卡时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="用户ID" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入用户ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="验证结果(0-通过 1-不通过)" prop="verifyResult">
        <el-input
          v-model="queryParams.verifyResult"
          placeholder="请输入验证结果(0-通过 1-不通过)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="进出方向(0-进 1-出)" prop="direction">
        <el-input
          v-model="queryParams.direction"
          placeholder="请输入进出方向(0-进 1-出)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="关联单据ID" prop="billId">
        <el-input
          v-model="queryParams.billId"
          placeholder="请输入关联单据ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['access:control:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['access:control:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['access:control:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['access:control:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="controlList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="记录ID" align="center" prop="id" />
      <el-table-column label="门禁设备ID" align="center" prop="deviceId" />
      <el-table-column label="刷脸/刷卡时间" align="center" prop="accessTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.accessTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用户ID" align="center" prop="userId" />
      <el-table-column label="验证方式(人脸/门禁卡/管理员)" align="center" prop="verifyType" />
      <el-table-column label="验证结果(0-通过 1-不通过)" align="center" prop="verifyResult" />
      <el-table-column label="进出方向(0-进 1-出)" align="center" prop="direction" />
      <el-table-column label="照片记录URL" align="center" prop="faceImage" width="100">
        <template slot-scope="scope">
          <image-preview :src="scope.row.faceImage" :width="50" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="关联单据ID" align="center" prop="billId" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['access:control:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['access:control:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改门禁记录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="门禁设备ID" prop="deviceId">
          <el-input v-model="form.deviceId" placeholder="请输入门禁设备ID" />
        </el-form-item>
        <el-form-item label="刷脸/刷卡时间" prop="accessTime">
          <el-date-picker clearable
            v-model="form.accessTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择刷脸/刷卡时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="用户ID" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户ID" />
        </el-form-item>
        <el-form-item label="验证结果(0-通过 1-不通过)" prop="verifyResult">
          <el-input v-model="form.verifyResult" placeholder="请输入验证结果(0-通过 1-不通过)" />
        </el-form-item>
        <el-form-item label="进出方向(0-进 1-出)" prop="direction">
          <el-input v-model="form.direction" placeholder="请输入进出方向(0-进 1-出)" />
        </el-form-item>
        <el-form-item label="照片记录URL" prop="faceImage">
          <image-upload v-model="form.faceImage"/>
        </el-form-item>
        <el-form-item label="关联单据ID" prop="billId">
          <el-input v-model="form.billId" placeholder="请输入关联单据ID" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAccessRecord, getAccessRecord, delAccessRecord, addAccessRecord, updateAccessRecord } from "@/api/dwms/access"

export default {
  name: "Control",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 门禁记录表格数据
      controlList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceId: null,
        accessTime: null,
        userId: null,
        verifyType: null,
        verifyResult: null,
        direction: null,
        faceImage: null,
        billId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        deviceId: [
          { required: true, message: "门禁设备ID不能为空", trigger: "blur" }
        ],
        accessTime: [
          { required: true, message: "刷脸/刷卡时间不能为空", trigger: "blur" }
        ],
        userId: [
          { required: true, message: "用户ID不能为空", trigger: "blur" }
        ],
        verifyType: [
          { required: true, message: "验证方式(人脸/门禁卡/管理员)不能为空", trigger: "change" }
        ],
        verifyResult: [
          { required: true, message: "验证结果(0-通过 1-不通过)不能为空", trigger: "blur" }
        ],
        direction: [
          { required: true, message: "进出方向(0-进 1-出)不能为空", trigger: "blur" }
        ],
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询门禁记录列表 */
    getList() {
      this.loading = true
      listAccessRecord(this.queryParams).then(response => {
        this.controlList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        deviceId: null,
        accessTime: null,
        userId: null,
        verifyType: null,
        verifyResult: null,
        direction: null,
        faceImage: null,
        billId: null,
        remark: null,
        createTime: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加门禁记录"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getAccessRecord(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改门禁记录"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateAccessRecord(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addAccessRecord(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除门禁记录编号为"' + ids + '"的数据项？').then(function() {
        return delAccessRecord(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('dwms/access/export', {
        ...this.queryParams
      }, `access_record_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
