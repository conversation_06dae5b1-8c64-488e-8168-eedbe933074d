<template>
  <div class="topic-converter-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2><i class="el-icon-refresh"></i> 主题格式转换器</h2>
      <p>将warehouse格式主题转换为DWMS标准格式</p>
    </div>

    <!-- 转换规则说明 -->
    <el-card class="rules-card">
      <div slot="header">
        <span>转换规则说明</span>
      </div>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="rule-item">
            <div class="rule-header">
              <i class="el-icon-key"></i>
              <span>门禁模块</span>
            </div>
            <div class="rule-content">
              <div class="rule-from">warehouse/access/*</div>
              <i class="el-icon-right"></i>
              <div class="rule-to">dwms/access/controller/001/*</div>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="rule-item">
            <div class="rule-header">
              <i class="el-icon-scale-to-original"></i>
              <span>称重模块</span>
            </div>
            <div class="rule-content">
              <div class="rule-from">warehouse/weight/*</div>
              <i class="el-icon-right"></i>
              <div class="rule-to">dwms/weight/scale/001/*</div>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="rule-item">
            <div class="rule-header">
              <i class="el-icon-cpu"></i>
              <span>RFID模块</span>
            </div>
            <div class="rule-content">
              <div class="rule-from">warehouse/rfid/*</div>
              <i class="el-icon-right"></i>
              <div class="rule-to">dwms/device/reader/001/*</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 转换结果展示 -->
    <el-card class="conversion-card">
      <div slot="header">
        <span>转换结果 ({{ conversions.length }}个主题)</span>
        <div style="float: right;">
          <el-button type="primary" size="small" @click="loadConversions">
            <i class="el-icon-refresh"></i> 刷新
          </el-button>
          <el-button type="success" size="small" @click="exportConversions">
            <i class="el-icon-download"></i> 导出
          </el-button>
          <el-button type="warning" size="small" @click="generateSql">
            <i class="el-icon-document"></i> 生成SQL
          </el-button>
        </div>
      </div>
      
      <el-table :data="conversions" style="width: 100%">
        <el-table-column type="index" label="#" width="50" />
        
        <el-table-column label="原始主题" min-width="250">
          <template slot-scope="scope">
            <div class="topic-cell">
              <el-tag type="warning" size="mini">Warehouse</el-tag>
              <span class="topic-text warehouse">{{ scope.row.original }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="" width="50" align="center">
          <template>
            <i class="el-icon-right conversion-arrow"></i>
          </template>
        </el-table-column>
        
        <el-table-column label="转换后主题" min-width="250">
          <template slot-scope="scope">
            <div class="topic-cell">
              <el-tag type="success" size="mini">DWMS</el-tag>
              <span class="topic-text dwms">{{ scope.row.converted }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="模块" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getModuleTagType(scope.row.module)" size="small">
              {{ scope.row.module }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="设备类型" width="120" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.deviceType }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="功能" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getFunctionTagType(scope.row.function)" size="mini">
              {{ scope.row.function }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="primary" @click="copyTopic(scope.row.converted)">
              复制
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 统计信息 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ conversions.length }}</div>
            <div class="stat-label">总转换数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ moduleStats.access || 0 }}</div>
            <div class="stat-label">门禁模块</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ moduleStats.weight || 0 }}</div>
            <div class="stat-label">称重模块</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ moduleStats.rfid || 0 }}</div>
            <div class="stat-label">RFID模块</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- SQL生成对话框 -->
    <el-dialog
      title="生成SQL更新语句"
      :visible.sync="sqlDialogVisible"
      width="800px"
    >
      <div class="sql-content">
        <div class="sql-header">
          <span>SQL更新语句 ({{ conversions.length }}条)</span>
          <el-button size="small" type="primary" @click="copySql">
            <i class="el-icon-document-copy"></i> 复制全部
          </el-button>
        </div>
        <el-input
          type="textarea"
          :rows="15"
          v-model="generatedSql"
          readonly
          class="sql-textarea"
        />
      </div>
      
      <div slot="footer">
        <el-button @click="sqlDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="downloadSql">
          <i class="el-icon-download"></i> 下载SQL文件
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'TopicConverter',
  data() {
    return {
      conversions: [],
      moduleStats: {},
      sqlDialogVisible: false,
      generatedSql: ''
    }
  },
  
  created() {
    this.loadConversions()
  },
  
  methods: {
    // 加载转换数据
    loadConversions() {
      // 模拟从后端获取转换数据
      const warehouseTopics = [
        'warehouse/access/deviceCode/event',
        'warehouse/weight/deviceCode/config', 
        'warehouse/access/deviceCode/heartbeat',
        'warehouse/access/deviceCode/command',
        'warehouse/rfid/deviceCode/heartbeat',
        'warehouse/weight/deviceCode/alarm',
        'warehouse/weight/deviceCode/data',
        'warehouse/access/deviceCode/config',
        'warehouse/weight/deviceCode/command'
      ]
      
      this.conversions = warehouseTopics.map(topic => {
        const converted = this.convertTopic(topic)
        const parts = converted.split('/')
        
        return {
          original: topic,
          converted: converted,
          module: parts[1],
          deviceType: parts[2],
          function: parts[4]
        }
      })
      
      this.calculateStats()
    },
    
    // 转换单个主题
    convertTopic(warehouseTopic) {
      const parts = warehouseTopic.split('/')
      if (parts.length < 4) return warehouseTopic
      
      const module = parts[1]
      const function_ = parts[3]
      
      const moduleMap = {
        'access': 'access',
        'weight': 'weight',
        'rfid': 'device'
      }
      
      const deviceTypeMap = {
        'access': 'controller',
        'weight': 'scale',
        'rfid': 'reader'
      }
      
      const dwmsModule = moduleMap[module] || 'device'
      const deviceType = deviceTypeMap[module] || 'device'
      
      return `dwms/${dwmsModule}/${deviceType}/001/${function_}`
    },
    
    // 计算统计信息
    calculateStats() {
      this.moduleStats = {}
      this.conversions.forEach(conv => {
        const module = conv.original.split('/')[1]
        this.moduleStats[module] = (this.moduleStats[module] || 0) + 1
      })
    },
    
    // 获取模块标签类型
    getModuleTagType(module) {
      const types = {
        'access': 'primary',
        'weight': 'success',
        'device': 'warning'
      }
      return types[module] || 'info'
    },
    
    // 获取功能标签类型
    getFunctionTagType(func) {
      const types = {
        'event': 'danger',
        'alarm': 'danger',
        'heartbeat': 'success',
        'config': 'warning',
        'command': 'primary',
        'data': 'info'
      }
      return types[func] || 'info'
    },
    
    // 复制主题
    copyTopic(topic) {
      this.$copyText(topic).then(() => {
        this.$message.success('主题已复制到剪贴板')
      }).catch(() => {
        this.$message.error('复制失败')
      })
    },
    
    // 导出转换结果
    exportConversions() {
      const data = {
        conversions: this.conversions,
        stats: this.moduleStats,
        timestamp: new Date().toISOString(),
        total: this.conversions.length
      }
      
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `topic_conversions_${new Date().getTime()}.json`
      link.click()
      window.URL.revokeObjectURL(url)
      this.$message.success('转换结果已导出')
    },
    
    // 生成SQL
    generateSql() {
      let sql = '-- EMQX主题格式转换SQL语句\n'
      sql += `-- 生成时间: ${new Date().toLocaleString()}\n`
      sql += `-- 转换数量: ${this.conversions.length}个主题\n\n`
      
      sql += '-- 更新主题表\n'
      this.conversions.forEach(conv => {
        sql += `UPDATE emqx_topics SET topic = '${conv.converted}' WHERE topic = '${conv.original}';\n`
      })
      
      sql += '\n-- 更新订阅表\n'
      this.conversions.forEach(conv => {
        sql += `UPDATE emqx_subscriptions SET topic = '${conv.converted}' WHERE topic = '${conv.original}';\n`
      })
      
      sql += '\n-- 验证转换结果\n'
      sql += "SELECT 'DWMS主题数量' as description, COUNT(*) as count FROM emqx_topics WHERE topic LIKE 'dwms/%';\n"
      sql += "SELECT 'Warehouse主题数量' as description, COUNT(*) as count FROM emqx_topics WHERE topic LIKE 'warehouse/%';\n"
      
      this.generatedSql = sql
      this.sqlDialogVisible = true
    },
    
    // 复制SQL
    copySql() {
      this.$copyText(this.generatedSql).then(() => {
        this.$message.success('SQL语句已复制到剪贴板')
      }).catch(() => {
        this.$message.error('复制失败')
      })
    },
    
    // 下载SQL文件
    downloadSql() {
      const blob = new Blob([this.generatedSql], { type: 'text/sql' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `topic_migration_${new Date().getTime()}.sql`
      link.click()
      window.URL.revokeObjectURL(url)
      this.$message.success('SQL文件已下载')
    }
  }
}
</script>

<style scoped>
.topic-converter-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.page-header p {
  margin: 5px 0 0 0;
  color: #909399;
  font-size: 14px;
}

.rules-card, .conversion-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.rule-item {
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background: #fafafa;
}

.rule-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-weight: bold;
  color: #303133;
}

.rule-header i {
  margin-right: 8px;
  font-size: 16px;
}

.rule-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.rule-from {
  color: #e6a23c;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.rule-to {
  color: #67c23a;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.topic-cell {
  display: flex;
  align-items: center;
}

.topic-cell .el-tag {
  margin-right: 8px;
}

.topic-text {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.topic-text.warehouse {
  color: #e6a23c;
}

.topic-text.dwms {
  color: #67c23a;
}

.conversion-arrow {
  color: #409eff;
  font-size: 16px;
}

.stats-row {
  margin-top: 20px;
}

.stat-card {
  text-align: center;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  padding: 20px;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 8px;
}

.sql-content {
  padding: 20px 0;
}

.sql-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.sql-textarea {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}
</style>
