<template>
  <div class="app-container">


    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon">
              <i class="el-icon-document" style="color: #409EFF;"></i>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ statistics.totalCount || 0 }}</div>
              <div class="statistics-label">总申请数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon">
              <i class="el-icon-time" style="color: #E6A23C;"></i>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ statistics.approvalStats ? statistics.approvalStats.pendingApproval : 0 }}</div>
              <div class="statistics-label">待审批</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon">
              <i class="el-icon-check" style="color: #67C23A;"></i>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ statistics.statusStats ? statistics.statusStats.completed : 0 }}</div>
              <div class="statistics-label">已完成</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon">
              <i class="el-icon-document-copy" style="color: #909399;"></i>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ statistics.statusStats ? statistics.statusStats.draft : 0 }}</div>
              <div class="statistics-label">暂存草稿</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索条件 -->
    <el-card class="filter-container" shadow="hover">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
        <el-form-item label="单据编号" prop="billNo">
          <el-input
            v-model="queryParams.billNo"
            placeholder="请输入单据编号"
            clearable
            prefix-icon="el-icon-document"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="业务类型" prop="businessType">
          <el-select v-model="queryParams.businessType" placeholder="请选择业务类型" clearable>
            <el-option
              v-for="item in businessTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="单据类型" prop="billType">
          <el-select v-model="queryParams.billType" placeholder="请选择单据类型" clearable>
            <el-option label="出库单" value="0" />
            <el-option label="入库单" value="1" />
            <el-option label="复合单据" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="申请日期" prop="applyTime">
          <el-date-picker
            clearable
            v-model="queryParams.applyTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择申请时间"
            prefix-icon="el-icon-date">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="审批状态" prop="approvalStatus">
          <el-select v-model="queryParams.approvalStatus" placeholder="请选择审批状态" clearable>
            <el-option label="待审核" value="0" />
            <el-option label="审核中" value="1" />
            <el-option label="已通过" value="2" />
            <el-option label="已驳回" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="执行状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择执行状态" clearable>
            <el-option label="暂存" value="9" />
            <el-option label="未执行" value="0" />
            <el-option label="执行中" value="1" />
            <el-option label="已完成" value="2" />
            <el-option label="已取消" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-container" shadow="hover" style="margin-top: 10px;">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['inout:inout:add']"
          >新增申请</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="info"
            plain
            icon="el-icon-document-copy"
            size="mini"
            @click="handleViewDrafts"
          >查看暂存</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['inout:inout:edit']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            icon="el-icon-s-promotion"
            size="mini"
            :disabled="single"
            @click="handleStartApproval"
            v-hasPermi="['inout:inout:approval']"
          >发起审批</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="info"
            icon="el-icon-close"
            size="mini"
            :disabled="single"
            @click="handleCancel"
            v-hasPermi="['inout:inout:cancel']"
          >取消申请</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['inout:inout:remove']"
          >删除</el-button>
        </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['inout:inout:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

      <el-table v-loading="loading" :data="inoutList" @selection-change="handleSelectionChange" border stripe highlight-current-row>
      <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="单据ID" align="center" prop="id" width="80" />
        <el-table-column label="单据编号" align="center" prop="billNo" min-width="120" show-overflow-tooltip />
        <el-table-column label="单据类型" align="center" prop="billType" width="90">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.billType == '0'" type="danger">出库单</el-tag>
            <el-tag v-else-if="scope.row.billType == '1'" type="success">入库单</el-tag>
            <el-tag v-else-if="scope.row.billType == '2'" type="warning">复合单据</el-tag>
            <el-tag v-else type="info">{{scope.row.billType}}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="业务信息" align="center" min-width="200">
          <template slot-scope="scope">
            <div class="business-info">
              <div class="info-item">
                <i class="el-icon-s-order"></i>
                业务类型: <el-tag size="mini" type="primary">{{getBusinessTypeText(scope.row.businessType)}}</el-tag>
              </div>
              <div class="info-item" v-if="scope.row.materialBillCode">
                <i class="el-icon-document-copy"></i>
                物料清单: <el-link
                  type="primary"
                  :underline="false"
                  @click="handleViewMaterialBill(scope.row.materialBillCode)"
                >{{scope.row.materialBillCode}}</el-link>
              </div>
              <div class="info-item" v-if="scope.row.externalSystemNo">
                <i class="el-icon-connection"></i>
                外部单号: <el-tooltip :content="'来源: ' + (scope.row.externalSystem || '未知')">
                  <span>{{scope.row.externalSystemNo}}</span>
                </el-tooltip>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="申请信息" align="center" min-width="200">
          <template slot-scope="scope">
            <div class="applicant-info">
              <div class="info-item">
                <i class="el-icon-user"></i>
                申请人: <span class="user-name">{{scope.row.applicantName || scope.row.applicantId}}</span>
              </div>
              <div class="info-item">
                <i class="el-icon-office-building"></i>
                部门: <span>{{scope.row.applicantDeptName || scope.row.applicantDeptId}}</span>
              </div>
              <div class="info-item">
                <i class="el-icon-time"></i>
                申请时间: <span>{{parseTime(scope.row.applyTime, '{y}-{m}-{d}')}}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="领料员信息" align="center" min-width="180">
          <template slot-scope="scope">
            <div class="receiver-info">
              <!-- 多领料员显示 -->
              <div v-if="scope.row.receivers && scope.row.receivers.length > 0" class="receivers-info">
                <div v-for="(receiver, index) in scope.row.receivers" :key="index" class="receiver-item-display">
                  <div class="info-item">
                    <i class="el-icon-user-solid"></i>
                    领料员{{index + 1}}: <span class="user-name">{{receiver.userName || receiver.userId}}</span>
                  </div>
                  <div class="info-item" v-if="receiver.faceUrl">
                    <el-avatar
                      :size="30"
                      :src="getImageUrl(receiver.faceUrl)"
                      @click="previewImage(getImageUrl(receiver.faceUrl))"
                      style="cursor: pointer;"
                    >
                      <i class="el-icon-user-solid"></i>
                    </el-avatar>
                    <span style="margin-left: 8px;">人脸照片{{index + 1}}</span>
                  </div>
                </div>
              </div>
              <!-- 兼容单领料员显示 -->
              <div v-else class="single-receiver-info">
                <div class="info-item">
                  <i class="el-icon-user-solid"></i>
                  领料员: <span class="user-name">{{scope.row.receiverName || scope.row.receiverId}}</span>
                </div>
                <div class="info-item" v-if="scope.row.receiverFace">
                  <el-avatar
                    :size="30"
                    :src="getImageUrl(scope.row.receiverFace)"
                    @click="previewImage(getImageUrl(scope.row.receiverFace))"
                    style="cursor: pointer;"
                  >
                    <i class="el-icon-user-solid"></i>
                  </el-avatar>
                  <span style="margin-left: 8px;">人脸照片</span>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="申请原因" align="center" min-width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            <div class="reason-info">
              <div v-if="scope.row.reasonType === 'maintenance' && scope.row.maintenanceOrderId">
                <el-tag size="mini" type="warning">维修工单</el-tag>
                <br>
                <span class="link-text" @click="viewMaintenanceOrder(scope.row.maintenanceOrderId)">
                  工单ID: {{scope.row.maintenanceOrderId}}
                </span>
              </div>
              <div v-else>
                <el-tag size="mini" type="info">手动输入</el-tag>
                <br>
                <span>{{scope.row.reason}}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="审批状态" align="center" width="120">
          <template slot-scope="scope">
            <div class="status-info clickable-status" @click="handleViewApprovalDetail(scope.row)">
              <el-tag v-if="scope.row.approvalStatus == '0'" type="warning">审批中</el-tag>
              <el-tag v-else-if="scope.row.approvalStatus == '1'" type="success">已通过</el-tag>
              <el-tag v-else-if="scope.row.approvalStatus == '2'" type="danger">已驳回</el-tag>
              <el-tag v-else-if="scope.row.approvalStatus == '3'" type="info">已撤销</el-tag>
              <el-tag v-else type="info">{{scope.row.approvalStatus}}</el-tag>
              <div v-if="scope.row.workflowCode" class="workflow-info">
                <small>流程: {{scope.row.workflowCode}}</small>
              </div>
              <div v-if="scope.row.approvalInstanceId" class="click-hint">
                <small style="color: #409EFF;">点击查看详情</small>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="执行状态" align="center" width="120">
          <template slot-scope="scope">
            <div class="status-info">
              <el-tag v-if="scope.row.status == '9'" type="" effect="plain">
                <i class="el-icon-document"></i> 暂存
              </el-tag>
              <el-tag v-else-if="scope.row.status == '0'" type="info">未执行</el-tag>
              <el-tag v-else-if="scope.row.status == '1'" type="warning">执行中</el-tag>
              <el-tag v-else-if="scope.row.status == '2'" type="success">已完成</el-tag>
              <el-tag v-else-if="scope.row.status == '3'" type="danger">已取消</el-tag>
              <el-tag v-else type="info">{{scope.row.status}}</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="240">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
              v-hasPermi="['inout:inout:query']"
            >详情</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit-outline"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['inout:inout:edit']"
              v-if="scope.row.status === '9'"
            >继续编辑</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['inout:inout:edit']"
              v-if="scope.row.approvalStatus !== '2' && scope.row.status !== '2' && scope.row.status !== '9'"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-s-promotion"
              @click="handleStartApproval(scope.row)"
              v-hasPermi="['inout:inout:approval']"
              v-if="scope.row.approvalStatus === '0'"
            >发起审批</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-close"
              @click="handleCancel(scope.row)"
              v-hasPermi="['inout:inout:cancel']"
              v-if="scope.row.approvalStatus !== '2' && scope.row.status !== '2' && scope.row.status !== '3'"
            >取消</el-button>
            <!-- 直接显示打印按钮 -->
            <el-button
              size="mini"
              type="text"
              icon="el-icon-printer"
              @click="handlePrintWithTemplate(scope.row)"
              v-hasPermi="['inout:printTemplate:list']"
              v-if="scope.row.approvalStatus === '2'"
              style="color: #409EFF;"
            >打印</el-button>

            <el-dropdown
              size="mini"
              @command="(command) => handleCommand(command, scope.row)"
              v-if="scope.row.materialBillCode || scope.row.receiverFace || scope.row.approvalStatus === '2'"
            >
              <span class="el-dropdown-link">
                更多<i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  command="viewMaterial"
                  v-if="scope.row.materialBillCode"
                  icon="el-icon-document-copy"
                >查看物料清单</el-dropdown-item>
                <el-dropdown-item
                  command="viewFace"
                  v-if="scope.row.receiverFace"
                  icon="el-icon-picture"
                >查看人脸照片</el-dropdown-item>
                <!-- 打印预览功能已移除，使用独立的单据打印功能 -->
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :pageSizes="[10, 20, 30, 50, 100]"
      @pagination="getList"
    />
    </el-card>

    <!-- 添加或修改出入库申请对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body>
      <!-- 暂存状态提示 -->
      <el-alert
        v-if="form.status === '9'"
        title="当前为暂存状态的单据，您可以继续编辑或正式提交"
        type="info"
        :closable="false"
        show-icon
        style="margin-bottom: 20px;">
      </el-alert>

      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-tabs v-model="activeTab">
          <!-- 基本信息 -->
          <el-tab-pane label="基本信息" name="basic">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="单据编号" prop="billNo">
                  <el-input
                    v-model="form.billNo"
                    placeholder="系统自动生成"
                    :disabled="true"
                  >
                    <el-button
                      slot="append"
                      icon="el-icon-refresh"
                      @click="generateBillNo"
                      :disabled="form.id != null"
                    >生成</el-button>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="单据类型" prop="billType">
                  <el-select v-model="form.billType" placeholder="请选择单据类型" style="width: 100%">
                    <el-option label="出库单" value="0" />
                    <el-option label="入库单" value="1" />
                    <el-option label="复合单据" value="2" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="业务类型" prop="businessType">
                  <el-select
                    v-model="form.businessType"
                    placeholder="请选择业务类型"
                    style="width: 100%"
                    @change="handleBusinessTypeChange"
                  >
                    <el-option
                      v-for="item in businessTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="外部系统标识" prop="externalSystem">
                  <el-input v-model="form.externalSystem" placeholder="请输入外部系统标识" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="外部系统单号" prop="externalSystemNo">
                  <el-input v-model="form.externalSystemNo" placeholder="请输入外部系统单据编号" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>

          <!-- 申请信息 -->
          <el-tab-pane label="申请信息" name="applicant">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="申请部门" prop="applicantDeptId">
                  <el-select
                    v-model="form.applicantDeptId"
                    placeholder="请选择申请部门"
                    style="width: 100%"
                    filterable
                    @change="handleDeptChange"
                  >
                    <el-option
                      v-for="dept in departmentOptions"
                      :key="dept.value"
                      :label="dept.label"
                      :value="dept.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="申请人" prop="applicantId">
                  <el-select
                    v-model="form.applicantId"
                    placeholder="请先选择申请部门"
                    style="width: 100%"
                    filterable
                    :disabled="!form.applicantDeptId"
                    @change="handleApplicantChange"
                  >
                    <el-option
                      v-for="user in userOptions"
                      :key="user.value"
                      :label="user.label"
                      :value="user.value"
                    >
                      <span style="float: left">{{ user.label }}</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">{{ user.userName }}</span>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="申请时间" prop="applyTime">
                  <el-date-picker
                    v-model="form.applyTime"
                    type="datetime"
                    placeholder="系统自动设置"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    style="width: 100%"
                    :disabled="true"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="申请原因类型" prop="reasonType">
                  <el-radio-group v-model="form.reasonType" @change="handleReasonTypeChange">
                    <el-radio label="manual">手动输入</el-radio>
                    <el-radio label="maintenance">维修工单</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20" v-if="form.reasonType === 'maintenance'">
              <el-col :span="24">
                <el-form-item label="维修工单" prop="maintenanceOrderId">
                  <el-select
                    v-model="form.maintenanceOrderId"
                    placeholder="请选择维修工单"
                    style="width: 100%"
                    filterable
                    @change="handleMaintenanceOrderChange"
                  >
                    <el-option
                      v-for="order in maintenanceOrderOptions"
                      :key="order.id"
                      :label="order.orderTitle"
                      :value="order.id"
                    >
                      <span style="float: left">{{ order.orderNo }} - {{ order.orderTitle }}</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">{{ order.equipmentName }}</span>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20" v-if="form.reasonType === 'manual'">
              <el-col :span="24">
                <el-form-item label="申请原因" prop="reason">
                  <el-select
                    v-model="form.reason"
                    placeholder="请选择或输入申请原因"
                    style="width: 100%"
                    filterable
                    allow-create
                  >
                    <el-option
                      v-for="reason in reasonOptions"
                      :key="reason.value"
                      :label="reason.label"
                      :value="reason.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>

          <!-- 领料员信息 -->
          <el-tab-pane label="领料员信息" name="receiver">
            <!-- 领料员部门选择 -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="领料员部门" prop="receiverDeptId">
                  <el-select
                    v-model="form.receiverDeptId"
                    placeholder="请选择领料员部门"
                    style="width: 100%"
                    filterable
                    @change="handleReceiverDeptChange"
                  >
                    <el-option
                      v-for="dept in departmentOptions"
                      :key="dept.value"
                      :label="dept.label"
                      :value="dept.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 领料员列表 -->
            <div class="receiver-list">
              <div class="list-header">
                <span>领料员列表</span>
                <el-button
                  type="primary"
                  size="small"
                  icon="el-icon-plus"
                  @click="addReceiver"
                  :disabled="!form.receiverDeptId"
                >
                  添加领料员
                </el-button>
              </div>

              <div v-if="form.receivers && form.receivers.length > 0" class="receiver-items">
                <div
                  v-for="(receiver, index) in form.receivers"
                  :key="index"
                  class="receiver-item"
                >
                  <el-card shadow="hover">
                    <div class="receiver-content">
                      <el-row :gutter="20">
                        <el-col :span="8">
                          <el-form-item :label="`领料员${index + 1}`" :prop="`receivers.${index}.userId`">
                            <el-select
                              v-model="receiver.userId"
                              placeholder="请选择领料员"
                              style="width: 100%"
                              filterable
                              @change="(value) => handleReceiverChange(value, index)"
                            >
                              <el-option
                                v-for="user in receiverUserOptions"
                                :key="user.value"
                                :label="user.label"
                                :value="user.value"
                              >
                                <span style="float: left">{{ user.label }}</span>
                                <span style="float: right; color: #8492a6; font-size: 13px">{{ user.userName }}</span>
                              </el-option>
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item :label="`人脸照片${index + 1}`" :prop="`receivers.${index}.faceUrl`">
                            <el-upload
                              class="face-uploader"
                              action=""
                              :http-request="(options) => handleFaceUpload(options, index)"
                              :show-file-list="false"
                              :before-upload="beforeFaceUpload"
                              accept="image/*"
                            >
                              <img
                                v-if="receiver.faceUrl"
                                :src="getImageUrl(receiver.faceUrl)"
                                class="face-image"
                                @click="previewImage(getImageUrl(receiver.faceUrl))"
                              >
                              <i v-else class="el-icon-plus face-uploader-icon"></i>
                            </el-upload>
                            <div class="upload-tips">
                              <p>点击上传人脸照片</p>
                              <p>支持 jpg、png 格式，大小不超过 5MB</p>
                            </div>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <div class="receiver-actions">
                            <el-button
                              type="danger"
                              size="small"
                              icon="el-icon-delete"
                              @click="removeReceiver(index)"
                            >
                              删除
                            </el-button>
                          </div>
                        </el-col>
                      </el-row>
                    </div>
                  </el-card>
                </div>
              </div>

              <div v-else class="empty-receivers">
                <el-empty description="暂无领料员，请先选择部门后添加领料员"></el-empty>
              </div>
            </div>
          </el-tab-pane>

          <!-- 物料清单 -->
          <el-tab-pane label="物料清单" name="material">
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="物料清单编号" prop="materialBillCode">
                  <el-input
                    v-model="form.materialBillCode"
                    placeholder="请输入物料清单编号"
                    @blur="handleMaterialBillCodeChange"
                  >
                    <el-button
                      slot="append"
                      icon="el-icon-search"
                      @click="searchMaterialBillWithDetails"
                      :loading="materialItemsLoading"
                    >查询</el-button>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20" v-if="materialBillInfo">
              <el-col :span="24">
                <el-card class="material-bill-info">
                  <div slot="header">
                    <span>物料清单信息</span>
                    <el-tag
                      :type="materialBillInfo.status === '3' ? 'success' : 'warning'"
                      style="float: right;"
                    >
                      {{ materialBillInfo.statusName }}
                    </el-tag>
                  </div>
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="清单编号">{{ materialBillInfo.billCode }}</el-descriptions-item>
                    <el-descriptions-item label="仓库名称">{{ materialBillInfo.warehouseName }}</el-descriptions-item>
                    <el-descriptions-item label="业务日期">{{ materialBillInfo.businessDate }}</el-descriptions-item>
                    <el-descriptions-item label="创建时间">{{ materialBillInfo.createTime }}</el-descriptions-item>
                    <el-descriptions-item label="备注" :span="2">{{ materialBillInfo.remark || '-' }}</el-descriptions-item>
                  </el-descriptions>
                </el-card>
              </el-col>
            </el-row>

            <!-- 物料清单明细查询结果 -->
            <el-row :gutter="20" v-if="materialItemsList.length > 0" style="margin-top: 20px;">
              <el-col :span="24">
                <el-card>
                  <div slot="header">
                    <span>物料清单明细 ({{ materialItemsList.length }}条)</span>
                  </div>
                  <el-table
                    :data="materialItemsList"
                    border
                    stripe
                    size="small"
                    v-loading="materialItemsLoading"
                  >
                    <el-table-column type="index" label="序号" width="60" align="center" />
                    <el-table-column label="清单编号" prop="billCode" width="180" align="center">
                      <template slot-scope="scope">
                        <el-link
                          type="primary"
                          :underline="false"
                          @click="handleViewMaterialBillDetail(scope.row.billCode)"
                        >
                          {{ scope.row.billCode }}
                        </el-link>
                      </template>
                    </el-table-column>
                    <el-table-column label="物料编码" prop="materialCode" width="120" align="center" />
                    <el-table-column label="物料名称" prop="materialName" min-width="150" align="center" />
                    <el-table-column label="规格" width="120" align="center">
                      <template slot-scope="scope">
                        {{ scope.row.specification || scope.row.materialSpecification || (scope.row.material && scope.row.material.specification) || '-' }}
                      </template>
                    </el-table-column>
                    <el-table-column label="单位" width="80" align="center">
                      <template slot-scope="scope">
                        {{ scope.row.unit || scope.row.materialUnit || (scope.row.material && scope.row.material.unit) || '-' }}
                      </template>
                    </el-table-column>
                    <el-table-column label="数量" width="100" align="center">
                      <template slot-scope="scope">
                        <span style="color: #409EFF; font-weight: bold;">{{ scope.row.quantity || 0 }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="单价" width="100" align="center">
                      <template slot-scope="scope">
                        <span style="color: #67C23A;">{{ scope.row.unitPrice ? '¥' + scope.row.unitPrice : '-' }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="存储位置" width="120" align="center">
                      <template slot-scope="scope">
                        {{ scope.row.locationCode || scope.row.locationName || scope.row.storageLocation || '-' }}
                      </template>
                    </el-table-column>
                    <el-table-column label="总价" width="120" align="center">
                      <template slot-scope="scope">
                        <span style="color: #E6A23C; font-weight: bold;">
                          {{ scope.row.totalPrice ? '¥' + scope.row.totalPrice : (scope.row.quantity && scope.row.unitPrice ? '¥' + (scope.row.quantity * scope.row.unitPrice).toFixed(2) : '-') }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column label="备注" prop="remark" min-width="120" align="center">
                      <template slot-scope="scope">
                        {{ scope.row.remark || '-' }}
                      </template>
                    </el-table-column>
                  </el-table>
                </el-card>
              </el-col>
            </el-row>

            <!-- 当有物料清单信息但没有明细时的提示 -->
            <el-row :gutter="20" v-if="materialBillInfo && materialItemsList.length === 0 && !materialItemsLoading" style="margin-top: 20px;">
              <el-col :span="24">
                <el-card>
                  <div slot="header">
                    <span>物料清单明细</span>
                  </div>
                  <el-empty description="该物料清单暂无明细数据">
                    <el-button type="primary" size="small" @click="handleCreateMaterialItem">添加明细</el-button>
                  </el-empty>
                </el-card>
              </el-col>
            </el-row>


          </el-tab-pane>

          <!-- 审批信息 -->
          <el-tab-pane label="审批信息" name="approval">
            <!-- 审批流程组件 -->
            <div v-if="form.id">
              <ApprovalFlow
                ref="approvalFlow"
                mode="display"
                :business-id="form.id.toString()"
                :business-type="getApprovalBusinessType()"
                :title="getApprovalBusinessTitle()"
                :show-preview="true"
                @submit-success="handleApprovalSubmitSuccess"
                @preview="handleApprovalPreview"
              />
            </div>

            <!-- 新建时的审批流程选择 -->
            <div v-else>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="审批流程" prop="workflowCode">
                    <el-select
                      v-model="form.workflowCode"
                      placeholder="请选择审批流程"
                      style="width: 100%"
                      @change="handleWorkflowChange"
                    >
                      <el-option
                        v-for="workflow in workflowOptions"
                        :key="workflow.value"
                        :label="workflow.label"
                        :value="workflow.value"
                      >
                        <span style="float: left">{{ workflow.label }}</span>
                        <span style="float: right; color: #8492a6; font-size: 13px">{{ workflow.description }}</span>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 审批操作按钮 -->
            <div v-if="form.id && form.approvalStatus === '0'" style="margin-bottom: 20px;">
              <el-button
                type="primary"
                icon="el-icon-s-promotion"
                @click="showApprovalDialog"
                :disabled="!form.workflowCode"
              >
                发起审批
              </el-button>
              <el-button
                type="info"
                icon="el-icon-view"
                @click="showApprovalHistory"
                v-if="form.approvalInstanceId"
              >
                查看审批记录
              </el-button>
            </div>

            <!-- 基本审批信息显示 -->
            <el-divider content-position="left">基本信息</el-divider>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="审批状态" prop="approvalStatus">
                  <el-tag
                    :type="getApprovalStatusType(form.approvalStatus)"
                    effect="plain"
                  >
                    {{ getApprovalStatusText(form.approvalStatus) }}
                  </el-tag>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="执行状态" prop="status">
                  <el-tag
                    :type="getExecutionStatusType(form.status)"
                    effect="plain"
                  >
                    {{ getExecutionStatusText(form.status) }}
                  </el-tag>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="审批实例ID" v-if="form.approvalInstanceId">
                  <span>{{ form.approvalInstanceId }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="备注" prop="remark">
                  <el-input
                    v-model="form.remark"
                    placeholder="请输入备注信息"
                    type="textarea"
                    :rows="3"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="info" @click="saveDraft" :loading="draftLoading">
          <i class="el-icon-document"></i> 暂 存
        </el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading">
          <i class="el-icon-check"></i> 确 定
        </el-button>
      </div>
    </el-dialog>



    <!-- 审批流程选择对话框 -->
    <el-dialog title="发起审批" :visible.sync="approvalVisible" width="500px" append-to-body>
      <el-form ref="approvalForm" :model="approvalForm" :rules="approvalRules" label-width="100px">
        <el-form-item label="审批流程" prop="workflowCode">
          <el-select v-model="approvalForm.workflowCode" placeholder="请选择审批流程" style="width: 100%">
            <el-option
              v-for="workflow in workflowOptions"
              :key="workflow.value"
              :label="workflow.label"
              :value="workflow.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="approvalForm.remark"
            placeholder="请输入备注信息"
            type="textarea"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="approvalVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitApproval">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 取消申请对话框 -->
    <el-dialog title="取消申请" :visible.sync="cancelVisible" width="500px" append-to-body>
      <el-form ref="cancelForm" :model="cancelForm" :rules="cancelRules" label-width="100px">
        <el-form-item label="取消原因" prop="cancelReason">
          <el-input
            v-model="cancelForm.cancelReason"
            placeholder="请输入取消原因"
            type="textarea"
            :rows="4"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelVisible = false">取 消</el-button>
        <el-button type="danger" @click="submitCancel">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog title="图片预览" :visible.sync="imagePreviewVisible" width="600px" append-to-body>
      <div class="image-preview-container">
        <img :src="previewImageUrl" style="width: 100%; max-height: 500px; object-fit: contain;" />
      </div>
    </el-dialog>

    <!-- 物料清单明细查看对话框 -->
    <el-dialog title="物料清单明细" :visible.sync="materialDetailVisible" width="1000px" append-to-body>
      <el-table
        :data="materialDetailList"
        border
        stripe
        v-loading="materialDetailLoading"
        max-height="500"
      >
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column label="清单编号" prop="billCode" width="180" align="center" />
        <el-table-column label="物料编码" prop="materialCode" width="120" align="center" />
        <el-table-column label="物料名称" prop="materialName" min-width="150" align="center" />
        <el-table-column label="规格" width="120" align="center">
          <template slot-scope="scope">
            {{ scope.row.specification || scope.row.materialSpecification || (scope.row.material && scope.row.material.specification) || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="单位" width="80" align="center">
          <template slot-scope="scope">
            {{ scope.row.unit || scope.row.materialUnit || (scope.row.material && scope.row.material.unit) || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="数量" prop="quantity" width="100" align="center" />
        <el-table-column label="库位" width="120" align="center">
          <template slot-scope="scope">
            {{ scope.row.locationName || scope.row.locationCode || scope.row.containerName || scope.row.containerCode || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="批次号" width="120" align="center">
          <template slot-scope="scope">
            {{ scope.row.batchNo || '-' }}
          </template>
        </el-table-column>
        <!-- 移除状态列，因为数据库表中没有processStatus字段 -->
        <!-- <el-table-column label="状态" prop="processStatus" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.processStatus === '1' ? 'success' : 'warning'">
              {{ scope.row.processStatus === '1' ? '已完成' : '未完成' }}
            </el-tag>
          </template>
        </el-table-column> -->
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="materialDetailVisible = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 物料清单详情对话框 -->
    <el-dialog title="物料清单详情" :visible.sync="materialBillDetailVisible" width="80%" append-to-body>
      <div v-loading="materialBillDetailLoading">
        <div v-if="materialBillDetailData">
          <!-- 物料清单基本信息 -->
          <el-card class="box-card" style="margin-bottom: 20px;">
            <div slot="header" class="clearfix">
              <span style="font-weight: bold;">基本信息</span>
            </div>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>清单编码：</label>
                  <span>{{ materialBillDetailData.billCode }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>清单名称：</label>
                  <span>{{ materialBillDetailData.billName }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>状态：</label>
                  <el-tag :type="materialBillDetailData.status === '2' ? 'success' : 'warning'">
                    {{ materialBillDetailData.status === '2' ? '已完成' : '草稿' }}
                  </el-tag>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20" style="margin-top: 10px;">
              <el-col :span="12">
                <div class="info-item">
                  <label>创建人：</label>
                  <span>{{ materialBillDetailData.createBy }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <label>创建时间：</label>
                  <span>{{ parseTime(materialBillDetailData.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </div>
              </el-col>
            </el-row>
          </el-card>

          <!-- 重量汇总信息 -->
          <el-card class="box-card" style="margin-bottom: 20px;">
            <div slot="header" class="clearfix">
              <span style="font-weight: bold;">重量汇总</span>
            </div>
            <el-row :gutter="20" style="padding: 10px; background-color: #f5f7fa; border: 2px solid #e74c3c; border-radius: 4px;">
              <el-col :span="8">
                <div class="weight-summary-item">
                  <label>数量：</label>
                  <span style="font-weight: bold; color: #409EFF;">{{ getMaterialBillTotalQuantity() }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="weight-summary-item">
                  <label>单重(kg)：</label>
                  <span style="font-weight: bold; color: #E6A23C;">{{ getMaterialBillAverageWeight() }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="weight-summary-item">
                  <label>总重(kg)：</label>
                  <span style="font-weight: bold; color: #67C23A;">{{ getMaterialBillTotalWeight() }}</span>
                </div>
              </el-col>
            </el-row>
          </el-card>

          <!-- 物料明细列表 -->
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span style="font-weight: bold;">物料明细</span>
            </div>
            <el-table :data="materialBillDetailData.items" border style="width: 100%">
              <el-table-column prop="materialCode" label="物料编码" width="150">
                <template slot-scope="scope">
                  <el-link
                    type="primary"
                    :underline="false"
                    @click="handleViewMaterialInfo(scope.row.materialCode)"
                  >{{ scope.row.materialCode }}</el-link>
                </template>
              </el-table-column>
              <el-table-column prop="materialName" label="物料名称" min-width="200" />
              <el-table-column prop="quantity" label="数量" width="100" align="center" />
              <el-table-column prop="unit" label="单位" width="80" align="center" />
              <el-table-column prop="unitWeight" label="单重(kg)" width="100" align="center">
                <template slot-scope="scope">
                  <span>{{ formatWeight(scope.row.unitWeight) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="totalWeight" label="总重(kg)" width="100" align="center">
                <template slot-scope="scope">
                  <span>{{ formatWeight(scope.row.totalWeight) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="remark" label="备注" min-width="150" />
            </el-table>
          </el-card>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="materialBillDetailVisible = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 物料详情对话框 -->
    <el-dialog title="物料详情" :visible.sync="materialInfoVisible" width="60%" append-to-body>
      <div v-loading="materialInfoLoading">
        <div v-if="materialInfoData">
          <el-card class="box-card">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="info-item">
                  <label>物料编码：</label>
                  <span>{{ materialInfoData.materialCode }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <label>物料名称：</label>
                  <span>{{ materialInfoData.materialName }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20" style="margin-top: 10px;">
              <el-col :span="12">
                <div class="info-item">
                  <label>规格型号：</label>
                  <span>{{ materialInfoData.specification || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <label>单位：</label>
                  <span>{{ materialInfoData.unit || '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20" style="margin-top: 10px;">
              <el-col :span="12">
                <div class="info-item">
                  <label>物料分类：</label>
                  <span>{{ materialInfoData.categoryName || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <label>状态：</label>
                  <el-tag :type="materialInfoData.status === '0' ? 'success' : 'danger'">
                    {{ materialInfoData.status === '0' ? '正常' : '停用' }}
                  </el-tag>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20" style="margin-top: 10px;">
              <el-col :span="12">
                <div class="info-item">
                  <label>单位重量：</label>
                  <span>{{ materialInfoData.unitWeight || '-' }} {{ materialInfoData.weightUnit || 'kg' }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <label>危险等级：</label>
                  <span>{{ materialInfoData.hazardLevel || '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20" style="margin-top: 10px;" v-if="materialInfoData.remark">
              <el-col :span="24">
                <div class="info-item">
                  <label>备注：</label>
                  <span>{{ materialInfoData.remark }}</span>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="materialInfoVisible = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 打印预览对话框已移除 -->

    <!-- 打印模板选择对话框 -->
    <el-dialog title="选择打印模板" :visible.sync="printTemplateDialogVisible" width="600px" append-to-body>
      <el-alert
        v-if="currentPrintRow && currentPrintRow.approvalStatus !== '2'"
        title="提示：当前单据未审批通过，不能进行打印"
        type="warning"
        :closable="false"
        style="margin-bottom: 20px;">
      </el-alert>
      <el-form :model="printForm" ref="printForm" label-width="100px">
        <el-form-item label="选择模板" prop="templateId">
          <el-select v-model="printForm.templateId" placeholder="请选择打印模板" style="width: 100%">
            <el-option
              v-for="template in printTemplateList"
              :key="template.id"
              :label="template.templateName"
              :value="template.id">
              <span style="float: left">{{ template.templateName }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ template.templateCode }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="打印份数" prop="copies" v-if="currentPrintRow && currentPrintRow.approvalStatus === '2'">
          <el-input-number v-model="printForm.copies" :min="1" :max="10" style="width: 150px"></el-input-number>
        </el-form-item>
        <el-form-item label="打印说明" v-if="currentPrintRow && currentPrintRow.approvalStatus === '2'">
          <el-input
            v-model="printForm.remark"
            type="textarea"
            :rows="3"
            placeholder="可选：输入打印说明或备注"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="printTemplateDialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="confirmPrint"
          :loading="printLoading"
          v-if="currentPrintRow && currentPrintRow.approvalStatus === '2'">确认打印</el-button>
      </div>
    </el-dialog>

    <!-- 审批详情弹窗 -->
    <el-dialog
      title="审批流程详情"
      :visible.sync="approvalDetailVisible"
      width="900px"
      :close-on-click-modal="false"
      append-to-body
      class="approval-detail-dialog"
    >
      <div v-if="currentInoutForApproval">
        <div class="approval-basic-info">
          <h4>基本信息</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <label>单据编号：</label>
                <span>{{ currentInoutForApproval.billNo }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>单据类型：</label>
                <span>{{ getBillTypeText(currentInoutForApproval.billType) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>业务类型：</label>
                <span>{{ currentInoutForApproval.businessType }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <label>申请人：</label>
                <span>{{ currentInoutForApproval.applicantName }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>申请时间：</label>
                <span>{{ parseTime(currentInoutForApproval.applyTime, '{y}-{m}-{d}') }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>当前状态：</label>
                <el-tag :type="getApprovalStatusType(currentInoutForApproval.approvalStatus)">
                  {{ getApprovalStatusText(currentInoutForApproval.approvalStatus) }}
                </el-tag>
              </div>
            </el-col>
          </el-row>
        </div>

        <div v-if="currentInoutForApproval.approvalInstanceId" class="approval-flow-info">
          <h4>审批流程</h4>
          <div v-loading="approvalDetailLoading">
            <div v-if="approvalFlowData.length > 0">
              <el-timeline>
                <el-timeline-item
                  v-for="(item, index) in approvalFlowData"
                  :key="index"
                  :type="getTimelineType(item.status)"
                  :icon="getTimelineIcon(item.status)"
                >
                  <div class="timeline-content">
                    <div class="timeline-header">
                      <span class="node-name">{{ item.nodeName }}</span>
                      <span class="status-badge">
                        <el-tag :type="getApprovalStatusType(item.status)" size="mini">
                          {{ getApprovalStatusText(item.status) }}
                        </el-tag>
                      </span>
                    </div>
                    <div v-if="item.approverName" class="timeline-detail">
                      <p><strong>审批人：</strong>{{ item.approverName }}</p>
                      <p v-if="item.approveTime"><strong>审批时间：</strong>{{ parseTime(item.approveTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</p>
                      <p v-if="item.opinion"><strong>审批意见：</strong>{{ item.opinion }}</p>
                    </div>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </div>
            <div v-else class="no-approval-data">
              <el-empty description="暂无审批流程数据"></el-empty>
            </div>
          </div>
        </div>

        <div v-else class="no-approval-instance">
          <el-alert
            title="该出入库申请尚未发起审批流程"
            type="info"
            :closable="false"
            show-icon>
          </el-alert>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="approvalDetailVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listInout, getInout, delInout, addInout, updateInout,
  getStatistics, getBusinessTypes, getDepartments, getUsersByDept,
  getMaintenanceOrders, getReasonOptions,
  getSnowflakeId, uploadFace,
  startApproval, cancelApplication, saveInoutDraft, updateInoutDraft
} from "@/api/inout/inout"
// 导入物料清单管理API
import { getMaterialBillByCode } from "@/api/material/bill"
import { listMaterialBillItemByBillCode } from "@/api/material/item"
// 导入物料管理API
import { getMaterialByCode } from "@/api/material/material"
import { listWorkflow, getApprovalDetail } from "@/api/approval/workflow"
import { printInoutDocument } from "@/api/inout/print"
import { listPrintTemplate } from "@/api/inout/printTemplate"
import ApprovalFlow from "@/components/RuiYun/ApprovalFlow"
export default {
  name: "InoutManagement",
  components: {
    ApprovalFlow
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 活动选项卡
      activeTab: "basic",
      // 出入库表格数据
      inoutList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 提交按钮loading状态
      submitLoading: false,
      // 暂存按钮loading状态
      draftLoading: false,
      // 统计信息
      statistics: {},
      // 业务类型选项
      businessTypeOptions: [],
      // 部门选项
      departmentOptions: [],
      // 用户选项（申请人）
      userOptions: [],
      // 领料员用户选项
      receiverUserOptions: [],
      // 审批流程选项
      workflowOptions: [],
      // 维修工单选项
      maintenanceOrderOptions: [],
      // 申请原因选项
      reasonOptions: [],
      // 物料清单信息
      materialBillInfo: null,
      // 物料清单查询相关
      materialBillCode: '',
      materialItemsLoading: false,
      materialItemsList: [],
      // 物料清单明细查看对话框
      materialDetailVisible: false,
      materialDetailList: [],
      materialDetailLoading: false,
      // 物料清单详情对话框
      materialBillDetailVisible: false,
      materialBillDetailData: null,
      materialBillDetailLoading: false,
      // 物料详情对话框
      materialInfoVisible: false,
      materialInfoData: null,
      materialInfoLoading: false,
      // 打印预览相关变量已移除
      // 审批详情对话框
      approvalDetailVisible: false,
      currentInoutForApproval: null,
      approvalFlowData: [],
      approvalDetailLoading: false,
      // 打印模板选择对话框
      printTemplateDialogVisible: false,
      printTemplateList: [],
      printLoading: false,
      printForm: {
        templateId: null,
        copies: 1,
        remark: ''
      },
      currentPrintRow: null,
      // 审批对话框
      approvalVisible: false,
      approvalForm: {},
      approvalRules: {
        workflowCode: [{ required: true, message: "请选择审批流程", trigger: "change" }]
      },
      // 取消对话框
      cancelVisible: false,
      cancelForm: {},
      cancelRules: {
        cancelReason: [{ required: true, message: "请输入取消原因", trigger: "blur" }]
      },
      // 图片预览
      imagePreviewVisible: false,
      previewImageUrl: "",
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        billNo: null,
        billType: null,
        purchaseOrderNo: null,
        externalSystemNo: null,
        externalSystem: null,
        businessType: null,
        applicantId: null,
        applicantDeptId: null,
        applyTime: null,
        receiverId: null,
        receiverFace: null,
        expectedTime: null,
        approvalStatus: null,
        approverId: null,
        approveTime: null,
        reason: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        billType: [
          { required: true, message: "单据类型不能为空", trigger: "change" }
        ],
        businessType: [
          { required: true, message: "业务类型不能为空", trigger: "change" }
        ],
        applicantId: [
          { required: true, message: "申请人不能为空", trigger: "change" }
        ],
        applicantDeptId: [
          { required: true, message: "申请部门不能为空", trigger: "change" }
        ],
        receiverDeptId: [
          { required: true, message: "领料员部门不能为空", trigger: "change" }
        ],
        receivers: [
          { required: true, message: "至少需要添加一个领料员", trigger: "change" }
        ],
        reason: [
          { required: true, message: "申请原因不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.loadInitialData();
  },
  methods: {
    /** 获取完整的图片URL */
    getImageUrl(imagePath) {
      if (!imagePath) {
        return ''
      }
      // 如果已经是完整URL，直接返回
      if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
        return imagePath
      }
      // 否则加上API前缀
      return process.env.VUE_APP_BASE_API + imagePath
    },

    /** 查询出入库列表 */
    getList() {
      this.loading = true;
      listInout(this.queryParams).then(response => {
        this.inoutList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        billNo: null,
        billType: "0",
        externalSystemNo: null,
        externalSystem: null,
        businessType: null,
        applicantId: null,
        applicantName: null,
        applicantDeptId: null,
        applicantDeptName: null,
        applyTime: this.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}'),
        receiverId: null,
        receiverName: null,
        receiverFace: null,
        receiverDeptId: null,
        receiverDeptName: null,
        receivers: [],
        reason: null,
        reasonType: 'manual',
        maintenanceOrderId: null,
        materialBillCode: null,
        materialBillId: null,
        workflowCode: null,
        approvalStatus: "0",
        approvalInstanceId: null,
        status: "0",
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      };
      this.resetForm("form");
      this.activeTab = "basic";
      // 重置相关数据
      this.materialBillInfo = null;
      this.receiverUserOptions = [];
      // 重置物料清单查询相关数据
      this.materialBillCode = '';
      this.materialItemsList = [];
      this.materialDetailVisible = false;
      this.materialDetailList = [];
      // 重置loading状态
      this.submitLoading = false;
      this.draftLoading = false;
      this.materialItemsLoading = false;
      this.materialDetailLoading = false;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length!==1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加出入库单据";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getInout(id).then(response => {
        this.form = response.data;

        // 处理领料员数据兼容性
        if (this.form.receivers && this.form.receivers.length > 0) {
          // 如果有多领料员数据，加载对应部门的用户选项
          if (this.form.receiverDeptId) {
            this.loadReceiverUsersByDept(this.form.receiverDeptId);
          }
        } else if (this.form.receiverId) {
          // 兼容旧的单领料员数据，转换为多领料员格式
          this.form.receivers = [{
            userId: this.form.receiverId,
            userName: this.form.receiverName,
            faceUrl: this.form.receiverFace
          }];

          // 通过用户ID获取用户信息，包括部门信息
          this.loadUserInfoAndDept(this.form.receiverId);
        }

        // 如果有物料清单编号，自动加载物料清单信息
        if (this.form.materialBillCode) {
          this.handleMaterialBillCodeChange();
        }

        this.open = true;
        this.title = "修改出入库单据";
      });
    },
    /** 详情按钮操作 */
    handleView(row) {
      this.reset();
      const id = row.id;
      getInout(id).then(response => {
        this.form = response.data;

        // 处理领料员数据兼容性
        if (this.form.receivers && this.form.receivers.length > 0) {
          // 如果有多领料员数据，加载对应部门的用户选项
          if (this.form.receiverDeptId) {
            this.loadReceiverUsersByDept(this.form.receiverDeptId);
          }
        } else if (this.form.receiverId) {
          // 兼容旧的单领料员数据，转换为多领料员格式
          this.form.receivers = [{
            userId: this.form.receiverId,
            userName: this.form.receiverName,
            faceUrl: this.form.receiverFace
          }];
        }

        this.open = true;
        this.title = "查看出入库单据详情";
        // 设置表单只读
        this.$nextTick(() => {
          this.$refs.form.disabled = true;
        });
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 验证领料员信息
          if (!this.validateReceivers()) {
            return;
          }

          this.submitLoading = true;

          // 准备提交数据
          const submitData = { ...this.form };
          // 设置状态为正式提交
          submitData.status = "0"; // 未执行状态

          // 处理多领料员数据，将第一个领料员信息设置到单领料员字段（兼容后端）
          if (submitData.receivers && submitData.receivers.length > 0) {
            const firstReceiver = submitData.receivers[0];
            submitData.receiverId = firstReceiver.userId;
            submitData.receiverName = firstReceiver.userName;
            submitData.receiverFace = firstReceiver.faceUrl;
            // 确保领料员部门信息也被保存
            if (!submitData.receiverDeptId && this.form.receiverDeptId) {
              submitData.receiverDeptId = this.form.receiverDeptId;
              submitData.receiverDeptName = this.form.receiverDeptName;
            }
          }

          if (this.form.id != null) {
            updateInout(submitData).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).catch(() => {
              this.submitLoading = false;
            }).finally(() => {
              this.submitLoading = false;
            });
          } else {
            addInout(submitData).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).catch(() => {
              this.submitLoading = false;
            }).finally(() => {
              this.submitLoading = false;
            });
          }
        }
      });
    },

    // 验证领料员信息
    validateReceivers() {
      if (!this.form.receivers || this.form.receivers.length === 0) {
        this.$modal.msgError('请至少添加一个领料员');
        return false;
      }

      for (let i = 0; i < this.form.receivers.length; i++) {
        const receiver = this.form.receivers[i];
        if (!receiver.userId) {
          this.$modal.msgError(`请选择第${i + 1}个领料员`);
          return false;
        }
        if (!receiver.faceUrl) {
          this.$modal.msgError(`请上传第${i + 1}个领料员的人脸照片`);
          return false;
        }
      }

      return true;
    },

    /** 暂存按钮 */
    saveDraft() {
      // 暂存时进行基本验证，但不要求所有字段都必填
      if (!this.validateDraftData()) {
        return;
      }

      this.draftLoading = true;

      // 准备暂存数据
      const draftData = { ...this.form };
      // 设置状态为暂存
      draftData.status = "9"; // 暂存状态

      // 处理多领料员数据，将第一个领料员信息设置到单领料员字段（兼容后端）
      if (draftData.receivers && draftData.receivers.length > 0) {
        const firstReceiver = draftData.receivers[0];
        draftData.receiverId = firstReceiver.userId;
        draftData.receiverName = firstReceiver.userName;
        draftData.receiverFace = firstReceiver.faceUrl;
        // 确保领料员部门信息也被保存
        if (!draftData.receiverDeptId && this.form.receiverDeptId) {
          draftData.receiverDeptId = this.form.receiverDeptId;
          draftData.receiverDeptName = this.form.receiverDeptName;
        }
      }

      if (this.form.id != null) {
        // 更新暂存
        updateInoutDraft(draftData).then(response => {
          this.$modal.msgSuccess("暂存成功");
          this.open = false;
          this.getList();
        }).catch(() => {
          this.draftLoading = false;
        }).finally(() => {
          this.draftLoading = false;
        });
      } else {
        // 新增暂存
        saveInoutDraft(draftData).then(response => {
          this.$modal.msgSuccess("暂存成功");
          this.open = false;
          this.getList();
        }).catch(() => {
          this.draftLoading = false;
        }).finally(() => {
          this.draftLoading = false;
        });
      }
    },

    // 验证暂存数据（相对宽松的验证）
    validateDraftData() {
      if (!this.form.billType) {
        this.$modal.msgError('请选择单据类型');
        return false;
      }

      if (!this.form.businessType) {
        this.$modal.msgError('请选择业务类型');
        return false;
      }

      return true;
    },

    /** 查看暂存单据 */
    handleViewDrafts() {
      // 设置查询条件为暂存状态
      this.queryParams.status = '9';
      this.getList();
      this.$modal.msgSuccess('已筛选暂存单据');
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除出入库单据编号为"' + ids + '"的数据项？').then(function() {
        return delInout(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('inout/inout/export', {
        ...this.queryParams
      }, `inout_${new Date().getTime()}.xlsx`);
    },





    
    // 创建物料清单
    handleCreateMaterialBill() {
      this.materialBillDialogVisible = true;
    },
    
    // 创建物料明细
    handleCreateMaterialItem() {
      if (!this.materialBillCode) {
        this.$modal.msgWarning("请先创建或输入物料清单编号");
        return;
      }
      this.materialItemDialogVisible = true;
      this.materialItemCreateUrl = `/material/item?billCode=${this.materialBillCode}`;
    },
    


    // 快速创建物料明细
    handleQuickCreateItem() {
      // 这里可以实现快速创建物料明细的逻辑
      // 可以根据实际需求实现，例如跳转到物料明细创建页面
      this.materialItemDialogVisible = true;
      this.materialItemCreateUrl = `/material/item?billCode=${this.materialBillCode}`;
    },

    // 加载初始化数据
    loadInitialData() {
      // 并行加载所有初始化数据，即使某些失败也不影响其他数据加载
      Promise.allSettled([
        this.getStatistics(),
        this.loadBusinessTypes(),
        this.loadDepartments(),
        this.loadUsers(),
        this.loadWorkflows(),
        this.loadReasonOptions()
      ]).then(results => {
        console.log('初始化数据加载完成:', results);
      });
    },

    // 获取统计信息
    getStatistics() {
      return getStatistics().then(response => {
        if (response.code === 200) {
          this.statistics = response.data;
        }
      });
    },

    /** 获取物料清单总数量 */
    getMaterialBillTotalQuantity() {
      if (!this.materialBillDetailData || !this.materialBillDetailData.items) {
        return 0;
      }
      return this.materialBillDetailData.items.reduce((total, item) => {
        return total + (parseFloat(item.quantity) || 0);
      }, 0);
    },

    /** 获取物料清单平均单重 */
    getMaterialBillAverageWeight() {
      if (!this.materialBillDetailData || !this.materialBillDetailData.items || this.materialBillDetailData.items.length === 0) {
        return 0;
      }

      const totalWeight = this.materialBillDetailData.items.reduce((total, item) => {
        return total + (parseFloat(item.unitWeight) || 0);
      }, 0);

      const averageWeight = totalWeight / this.materialBillDetailData.items.length;
      return averageWeight.toFixed(2);
    },

    /** 获取物料清单总重量 */
    getMaterialBillTotalWeight() {
      if (!this.materialBillDetailData || !this.materialBillDetailData.items) {
        return 0;
      }
      return this.materialBillDetailData.items.reduce((total, item) => {
        const quantity = parseFloat(item.quantity) || 0;
        const unitWeight = parseFloat(item.unitWeight) || 0;
        return total + (quantity * unitWeight);
      }, 0).toFixed(2);
    },

    /** 格式化重量显示 */
    formatWeight(weight) {
      if (weight === null || weight === undefined || isNaN(weight)) {
        return '-';
      }
      const weightNum = parseFloat(weight);
      return weightNum.toFixed(2);
    },

    // 加载业务类型选项
    loadBusinessTypes() {
      return getBusinessTypes().then(response => {
        if (response.code === 200) {
          this.businessTypeOptions = response.data;
        }
      });
    },

    // 加载部门选项
    loadDepartments() {
      return getDepartments().then(response => {
        if (response.code === 200) {
          this.departmentOptions = response.data;
        }
      });
    },

    // 加载用户选项（初始化时不加载，等待部门选择后再加载）
    loadUsers() {
      // 初始化时不加载所有用户，改为级联选择
      this.userOptions = [];
      return Promise.resolve();
    },

    // 加载审批流程选项
    loadWorkflows() {
      return listWorkflow({ status: '0' }).then(response => {
        if (response.code === 200) {
          // 转换数据格式以匹配原有的选项格式
          this.workflowOptions = response.rows.map(workflow => ({
            value: workflow.workflowCode,
            label: workflow.workflowName,
            description: workflow.description || workflow.workflowName
          }));
        }
      });
    },

    // 加载申请原因选项
    loadReasonOptions() {
      return getReasonOptions().then(response => {
        if (response.code === 200) {
          this.reasonOptions = response.data;
        }
      });
    },

    // 生成单据编号
    generateBillNo() {
      getSnowflakeId().then(response => {
        if (response.code === 200) {
          this.form.billNo = response.data.toString();
        }
      }).catch(() => {
        this.$modal.msgError('生成单据编号失败');
      });
    },

    // 业务类型变化处理
    handleBusinessTypeChange(businessType) {
      // 根据业务类型可以做一些相关处理
      console.log('业务类型变化:', businessType);
    },

    // 申请人变化处理
    handleApplicantChange(userId) {
      const user = this.userOptions.find(u => u.value === userId);
      if (user) {
        this.form.applicantName = user.label;
        this.form.applicantDeptId = user.deptId;
        this.form.applicantDeptName = user.deptName;
      }
    },

    // 部门变化处理
    handleDeptChange(deptId) {
      const dept = this.departmentOptions.find(d => d.value === deptId);
      if (dept) {
        this.form.applicantDeptName = dept.label;
      }

      // 清空申请人选择
      this.form.applicantId = null;
      this.form.applicantName = null;

      // 根据部门ID加载该部门下的用户
      if (deptId) {
        this.loadUsersByDept(deptId);
      } else {
        // 如果没有选择部门，清空用户选项
        this.userOptions = [];
      }
    },

    // 根据部门ID加载用户
    async loadUsersByDept(deptId) {
      try {
        const response = await getUsersByDept(deptId);
        if (response.code === 200) {
          this.userOptions = response.data || [];
        } else {
          this.$modal.msgError(response.msg || '获取部门用户失败');
          this.userOptions = [];
        }
      } catch (error) {
        console.error('获取部门用户失败:', error);
        this.$modal.msgError('获取部门用户失败');
        this.userOptions = [];
      }
    },

    // 领料员部门变化处理
    handleReceiverDeptChange(deptId) {
      const dept = this.departmentOptions.find(d => d.value === deptId);
      if (dept) {
        this.form.receiverDeptName = dept.label;
      }

      // 清空领料员列表
      this.form.receivers = [];

      // 根据部门ID加载该部门下的用户
      if (deptId) {
        this.loadReceiverUsersByDept(deptId);
      } else {
        this.receiverUserOptions = [];
      }
    },

    // 根据部门ID加载领料员用户
    async loadReceiverUsersByDept(deptId) {
      try {
        const response = await getUsersByDept(deptId);
        if (response.code === 200) {
          this.receiverUserOptions = response.data || [];
        } else {
          this.$modal.msgError(response.msg || '获取部门用户失败');
          this.receiverUserOptions = [];
        }
      } catch (error) {
        console.error('获取部门用户失败:', error);
        this.$modal.msgError('获取部门用户失败');
        this.receiverUserOptions = [];
      }
    },

    // 通过用户ID获取用户信息和部门信息
    async loadUserInfoAndDept(userId) {
      try {
        // 这里需要调用获取用户详情的API
        // 暂时使用一个简化的方法：加载所有部门，然后为每个部门加载用户
        await this.loadDepartments();

        // 遍历所有部门，找到包含该用户的部门
        for (const dept of this.departmentOptions) {
          try {
            const response = await getUsersByDept(dept.value);
            if (response.code === 200 && response.data) {
              const user = response.data.find(u => u.value == userId);
              if (user) {
                // 找到了用户所属的部门
                this.form.receiverDeptId = dept.value;
                this.form.receiverDeptName = dept.label;
                this.receiverUserOptions = response.data;
                break;
              }
            }
          } catch (error) {
            console.error('查询部门用户失败:', error);
          }
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
      }
    },

    // 添加领料员
    addReceiver() {
      if (!this.form.receivers) {
        this.form.receivers = [];
      }
      this.form.receivers.push({
        userId: null,
        userName: '',
        faceUrl: ''
      });
    },

    // 删除领料员
    removeReceiver(index) {
      this.form.receivers.splice(index, 1);
    },

    // 领料员变化处理
    handleReceiverChange(userId, index) {
      const user = this.receiverUserOptions.find(u => u.value === userId);
      if (user) {
        this.form.receivers[index].userName = user.label;
      }
    },

    // 申请原因类型变化处理
    handleReasonTypeChange(type) {
      if (type === 'maintenance') {
        this.loadMaintenanceOrders();
      }
      this.form.reason = '';
      this.form.maintenanceOrderId = null;
    },

    // 加载维修工单选项
    loadMaintenanceOrders() {
      return getMaintenanceOrders().then(response => {
        if (response.code === 200) {
          this.maintenanceOrderOptions = response.data;
        }
      });
    },

    // 维修工单变化处理
    handleMaintenanceOrderChange(orderId) {
      const order = this.maintenanceOrderOptions.find(o => o.id === orderId);
      if (order) {
        this.form.reason = `维修工单：${order.orderNo} - ${order.orderTitle}`;
      }
    },

    // 文件上传前验证
    beforeFaceUpload(file) {
      const isImage = file.type.indexOf('image/') === 0;
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isImage) {
        this.$modal.msgError('只能上传图片文件!');
        return false;
      }
      if (!isLt5M) {
        this.$modal.msgError('上传图片大小不能超过 5MB!');
        return false;
      }
      return true;
    },

    // 处理文件上传
    handleFaceUpload(options, receiverIndex = null) {
      const formData = new FormData();
      formData.append('file', options.file);

      uploadFace(formData).then(response => {
        if (response.code === 200) {
          if (receiverIndex !== null) {
            // 多领料员模式，更新指定领料员的人脸照片
            this.form.receivers[receiverIndex].faceUrl = response.data.fileUrl;
          } else {
            // 兼容旧的单领料员模式
            this.form.receiverFace = response.data.fileUrl;
          }
          this.$modal.msgSuccess('上传成功');
        } else {
          this.$modal.msgError(response.msg || '上传失败');
        }
      }).catch(() => {
        this.$modal.msgError('上传失败');
      });
    },

    // 预览人脸图片
    previewFaceImage() {
      this.previewImageUrl = this.getImageUrl(this.form.receiverFace);
      this.imagePreviewVisible = true;
    },

    // 查询物料清单（包含基础信息和明细）
    searchMaterialBillWithDetails() {
      if (!this.form.materialBillCode) {
        this.$modal.msgWarning('请输入物料清单编号');
        return;
      }

      this.materialItemsLoading = true;
      this.materialBillInfo = null;
      this.materialItemsList = [];

      const billCode = this.form.materialBillCode.trim();

      // 使用物料管理的API查询物料清单信息
      getMaterialBillByCode(billCode).then(billResponse => {
        if (billResponse.code === 200) {
          this.materialBillInfo = billResponse.data;
          this.form.materialBillId = this.materialBillInfo.id;

          // 查询物料清单明细
          return listMaterialBillItemByBillCode(billCode);
        } else {
          this.$modal.msgWarning(billResponse.msg || '未找到该物料清单');
          this.materialItemsLoading = false;
          return Promise.reject('未找到物料清单');
        }
      }).then(itemsResponse => {
        if (itemsResponse.code === 200) {
          this.materialItemsList = itemsResponse.rows || itemsResponse.data || [];
          this.$modal.msgSuccess(`成功查询到物料清单信息，包含 ${this.materialItemsList.length} 个明细项`);
        } else {
          this.materialItemsList = [];
          this.$modal.msgWarning('未找到物料清单明细');
        }
        this.materialItemsLoading = false;
      }).catch(error => {
        console.error('查询物料清单失败:', error);
        this.$modal.msgError('查询物料清单失败');
        this.materialBillInfo = null;
        this.materialItemsList = [];
        this.materialItemsLoading = false;
      });
    },

    // 物料清单编号变化处理（保持原有的blur事件处理）
    handleMaterialBillCodeChange() {
      if (this.form.materialBillCode) {
        const billCode = this.form.materialBillCode.trim();
        // 使用物料管理的API加载物料清单基本信息
        getMaterialBillByCode(billCode).then(response => {
          if (response.code === 200) {
            this.materialBillInfo = response.data;
            this.form.materialBillId = this.materialBillInfo.id;

            // 同时加载物料清单明细
            this.loadMaterialBillItems(billCode);
          } else {
            this.materialBillInfo = null;
            this.materialItemsList = [];
          }
        }).catch(() => {
          this.materialBillInfo = null;
          this.materialItemsList = [];
        });
      } else {
        this.materialBillInfo = null;
        this.materialItemsList = [];
      }
    },

    // 加载物料清单明细
    loadMaterialBillItems(billCode) {
      if (!billCode) return;

      this.materialItemsLoading = true;
      listMaterialBillItemByBillCode(billCode).then(response => {
        if (response.code === 200) {
          this.materialItemsList = response.rows || response.data || [];
        } else {
          this.materialItemsList = [];
        }
        this.materialItemsLoading = false;
      }).catch(() => {
        this.materialItemsList = [];
        this.materialItemsLoading = false;
      });
    },



    // 发起审批
    handleStartApproval(row) {
      if (row) {
        this.approvalForm = { id: row.id, workflowCode: '', remark: '' };
      } else if (this.ids.length === 1) {
        this.approvalForm = { id: this.ids[0], workflowCode: '', remark: '' };
      } else {
        this.$modal.msgWarning('请选择一条记录');
        return;
      }
      this.approvalVisible = true;
    },

    // 提交审批
    submitApproval() {
      this.$refs.approvalForm.validate(valid => {
        if (valid) {
          startApproval(this.approvalForm.id, {
            workflowCode: this.approvalForm.workflowCode,
            remark: this.approvalForm.remark
          }).then(response => {
            if (response.code === 200) {
              this.$modal.msgSuccess('审批流程启动成功');
              this.approvalVisible = false;
              this.getList();
            } else {
              this.$modal.msgError(response.msg || '启动审批失败');
            }
          }).catch(() => {
            this.$modal.msgError('启动审批失败');
          });
        }
      });
    },

    // 取消申请
    handleCancel(row) {
      if (row) {
        this.cancelForm = { id: row.id, cancelReason: '' };
      } else if (this.ids.length === 1) {
        this.cancelForm = { id: this.ids[0], cancelReason: '' };
      } else {
        this.$modal.msgWarning('请选择一条记录');
        return;
      }
      this.cancelVisible = true;
    },

    // 提交取消
    submitCancel() {
      this.$refs.cancelForm.validate(valid => {
        if (valid) {
          cancelApplication(this.cancelForm.id, {
            cancelReason: this.cancelForm.cancelReason
          }).then(response => {
            if (response.code === 200) {
              this.$modal.msgSuccess('申请已取消');
              this.cancelVisible = false;
              this.getList();
            } else {
              this.$modal.msgError(response.msg || '取消申请失败');
            }
          }).catch(() => {
            this.$modal.msgError('取消申请失败');
          });
        }
      });
    },

    // 处理下拉菜单命令
    handleCommand(command, row) {
      switch (command) {
        case 'viewMaterial':
          if (!row.materialBillCode) {
            this.$modal.msgWarning('该单据没有关联物料清单');
            return;
          }
          this.handleViewMaterialBillDetail(row.materialBillCode);
          break;
        case 'viewFace':
          this.previewImage(this.getImageUrl(row.receiverFace));
          break;
        case 'print':
          this.handlePrint(row);
          break;
        // 打印预览功能已移除
      }
    },

    // 查看物料清单（点击物料清单编号）
    handleViewMaterialBill(billCode) {
      if (!billCode) {
        this.$modal.msgWarning('物料清单编号不能为空');
        return;
      }

      this.materialBillDetailLoading = true;
      this.materialBillDetailVisible = true;
      this.materialBillDetailData = null;

      // 获取物料清单基本信息
      getMaterialBillByCode(billCode).then(response => {
        if (response.code === 200) {
          this.materialBillDetailData = response.data;

          // 获取物料清单明细
          return listMaterialBillItemByBillCode(billCode);
        } else {
          throw new Error(response.msg || "获取物料清单信息失败");
        }
      }).then(response => {
        if (response.code === 200) {
          this.materialBillDetailData.items = response.rows || response.data || [];
        } else {
          this.$modal.msgWarning(response.msg || "获取物料清单明细失败");
          this.materialBillDetailData.items = [];
        }
        this.materialBillDetailLoading = false;
      }).catch(error => {
        console.error("获取物料清单信息出错:", error);
        this.$modal.msgError(error.message || "获取物料清单信息失败");
        this.materialBillDetailLoading = false;
      });
    },

    // 查看物料详情（点击物料编号）
    handleViewMaterialInfo(materialCode) {
      if (!materialCode) {
        this.$modal.msgWarning('物料编号不能为空');
        return;
      }

      this.materialInfoLoading = true;
      this.materialInfoVisible = true;
      this.materialInfoData = null;

      // 获取物料详细信息
      getMaterialByCode(materialCode).then(response => {
        if (response.code === 200) {
          this.materialInfoData = response.data;
        } else {
          this.$modal.msgError(response.msg || "获取物料信息失败");
        }
        this.materialInfoLoading = false;
      }).catch(error => {
        console.error("获取物料信息出错:", error);
        this.$modal.msgError("获取物料信息失败");
        this.materialInfoLoading = false;
      });
    },

    // 查看物料清单详细信息
    handleViewMaterialBillDetail(billCode) {
      if (!billCode) {
        this.$modal.msgWarning('清单编号不能为空');
        return;
      }

      this.materialDetailLoading = true;
      this.materialDetailVisible = true;
      this.materialDetailList = [];

      // 获取物料清单明细
      listMaterialBillItemByBillCode(billCode).then(response => {
        if (response.code === 200) {
          this.materialDetailList = response.rows || response.data || [];
          if (this.materialDetailList.length === 0) {
            this.$modal.msgWarning("该物料清单没有明细数据");
          }
        } else {
          this.$modal.msgError(response.msg || "获取物料清单明细失败");
          this.materialDetailList = [];
        }
        this.materialDetailLoading = false;
      }).catch(error => {
        console.error("获取物料清单明细出错:", error);
        this.$modal.msgError("获取物料清单明细时发生错误");
        this.materialDetailList = [];
        this.materialDetailLoading = false;
      });
    },



    // 预览图片
    previewImage(imageUrl) {
      this.previewImageUrl = imageUrl;
      this.imagePreviewVisible = true;
    },

    // 查看维修工单
    viewMaintenanceOrder(orderId) {
      // 这里可以实现查看维修工单详情的逻辑
      this.$message.info(`查看维修工单详情：${orderId}`);
    },

    // 审批流程变化处理
    handleWorkflowChange(workflowCode) {
      // 根据审批流程可以做一些相关处理
      console.log('审批流程变化:', workflowCode);
    },

    // 审批相关方法
    getApprovalBusinessType() {
      return 'inout';
    },

    getApprovalBusinessTitle() {
      if (this.form.billNo) {
        return `出入库单据 - ${this.form.billNo}`;
      }
      return '出入库单据申请';
    },

    handleApprovalWorkflowChange(workflowCode) {
      this.form.workflowCode = workflowCode;
      console.log('审批流程变化:', workflowCode);
    },

    handleApprovalSubmitSuccess(data) {
      this.form.approvalInstanceId = data.instanceId;
      this.form.approvalStatus = '0'; // 审批中
      this.$modal.msgSuccess('审批流程启动成功');
      this.getList();
    },

    handleApprovalPreview(formData) {
      console.log('预览审批流程:', formData);
    },

    getApprovalStatusText(status) {
      const statusMap = {
        '0': '审批中',
        '1': '已通过',
        '2': '已驳回',
        '3': '已撤销'
      };
      return statusMap[status] || '未知';
    },

    getApprovalStatusType(status) {
      const typeMap = {
        '0': 'warning',
        '1': 'success',
        '2': 'danger',
        '3': 'info'
      };
      return typeMap[status] || 'info';
    },

    getExecutionStatusText(status) {
      const statusMap = {
        '0': '未执行',
        '1': '执行中',
        '2': '已完成',
        '3': '已取消'
      };
      return statusMap[status] || '未知';
    },

    getExecutionStatusType(status) {
      const typeMap = {
        '0': 'info',
        '1': 'warning',
        '2': 'success',
        '3': 'danger'
      };
      return typeMap[status] || 'info';
    },

    // 显示发起审批对话框
    showApprovalDialog() {
      if (!this.form.id) {
        this.$modal.msgWarning('请先保存单据后再发起审批');
        return;
      }
      if (!this.form.workflowCode) {
        this.$modal.msgWarning('请先选择审批流程');
        return;
      }

      // 使用现有的发起审批逻辑
      this.handleStartApproval({ id: this.form.id });
    },

    // 显示审批历史记录
    showApprovalHistory() {
      if (!this.form.approvalInstanceId) {
        this.$modal.msgWarning('该单据尚未发起审批');
        return;
      }

      // 跳转到审批详情页面
      this.$router.push({
        path: '/approval/process/detail',
        query: {
          businessId: this.form.id.toString(),
          businessType: this.getApprovalBusinessType(),
          instanceId: this.form.approvalInstanceId
        }
      });
    },

    // 打印单据（原有方法，保留兼容性）
    async handlePrint(row) {
      if (row.approvalStatus !== '2') {
        this.$modal.msgWarning('只能打印已审批通过的单据');
        return;
      }

      try {
        this.$modal.loading('正在生成打印内容...');
        const response = await printInoutDocument(row.id);
        this.$modal.closeLoading();

        if (response.code === 200) {
          // 打开新窗口进行打印
          const printWindow = window.open('', '_blank');
          printWindow.document.write(response.data);
          printWindow.document.close();
          printWindow.print();
          printWindow.close();

          this.$modal.msgSuccess('打印成功');
        } else {
          this.$modal.msgError(response.msg || '打印失败');
        }
      } catch (error) {
        this.$modal.closeLoading();
        console.error('打印失败:', error);
        this.$modal.msgError('打印失败：' + error.message);
      }
    },

    // 打印单据（带模板选择）
    async handlePrintWithTemplate(row) {
      // 临时移除审批状态限制，允许所有状态的单据进行打印预览
      // 这样可以方便测试和查看单据内容

      // 原有的审批状态检查逻辑（已注释）：
      // if (row.approvalStatus === '0') {
      //   this.$modal.msgWarning('未发起审批的单据不能打印');
      //   return;
      // }
      // if (row.approvalStatus !== '2') {
      //   this.$modal.msgWarning('审批中的单据只能预览，不能正式打印');
      // }

      this.currentPrintRow = row;
      await this.loadPrintTemplates();
      this.resetPrintForm();
      this.printTemplateDialogVisible = true;
    },

    // 加载打印模板列表
    async loadPrintTemplates() {
      try {
        const response = await listPrintTemplate({
          templateType: 'inout',
          status: '0'  // 修复：状态0表示启用，1表示停用
        });
        this.printTemplateList = response.rows || [];

        // 自动选择默认模板
        const defaultTemplate = this.printTemplateList.find(t => t.isDefault === '1');
        if (defaultTemplate) {
          this.printForm.templateId = defaultTemplate.id;
        } else if (this.printTemplateList.length > 0) {
          this.printForm.templateId = this.printTemplateList[0].id;
        }
      } catch (error) {
        console.error('加载打印模板失败:', error);
        this.$modal.msgError('加载打印模板失败');
      }
    },

    // 重置打印表单
    resetPrintForm() {
      this.printForm = {
        templateId: null,
        copies: 1,
        remark: ''
      };
    },



    // 确认打印
    async confirmPrint() {
      if (!this.printForm.templateId) {
        this.$modal.msgWarning('请先选择打印模板');
        return;
      }

      try {
        this.printLoading = true;

        // 调用打印接口
        const response = await printInoutDocument(this.currentPrintRow.id, this.printForm.templateId);

        if (response.code === 200) {
          // 根据打印份数进行多次打印
          for (let i = 0; i < this.printForm.copies; i++) {
            const printWindow = window.open('', '_blank');
            printWindow.document.write(response.data);
            printWindow.document.close();
            printWindow.print();
            printWindow.close();

            // 如果是多份，稍微延迟一下
            if (i < this.printForm.copies - 1) {
              await new Promise(resolve => setTimeout(resolve, 500));
            }
          }

          this.printTemplateDialogVisible = false;
          this.$modal.msgSuccess(`打印成功，共打印 ${this.printForm.copies} 份`);
        } else {
          this.$modal.msgError(response.msg || '打印失败');
        }
      } catch (error) {
        console.error('打印失败:', error);
        this.$modal.msgError('打印失败：' + error.message);
      } finally {
        this.printLoading = false;
      }
    },

    // 打印预览功能已移除

    // 查看审批详情
    handleViewApprovalDetail(row) {
      if (!row.approvalInstanceId) {
        this.$modal.msgWarning('该出入库申请尚未发起审批流程');
        return;
      }

      this.currentInoutForApproval = row;
      this.approvalDetailVisible = true;
      this.loadApprovalFlowData(row.approvalInstanceId);
    },

    // 加载审批流程数据
    async loadApprovalFlowData(instanceId) {
      this.approvalDetailLoading = true;
      this.approvalFlowData = [];

      try {
        // 调用审批系统API获取流程详情
        const response = await getApprovalDetail({ instanceId: instanceId });

        if (response.code === 200 && response.data) {
          // 从返回的数据中提取审批记录
          const records = response.data.records || [];

          // 转换数据格式以适配时间线显示
          this.approvalFlowData = records.map(record => ({
            nodeName: record.nodeName || '审批节点',
            status: record.status || '0',
            approverName: record.approverName || record.approver,
            approveTime: record.approveTime,
            opinion: record.opinion || record.remark
          }));
        } else {
          this.$modal.msgError('获取审批流程数据失败：' + (response.msg || '未知错误'));
        }
      } catch (error) {
        console.error('获取审批流程数据失败:', error);
        this.$modal.msgError('获取审批流程数据失败：' + (error.message || '网络错误'));
      } finally {
        this.approvalDetailLoading = false;
      }
    },

    // 获取时间线类型
    getTimelineType(status) {
      const typeMap = {
        '0': 'info',    // 待审批
        '1': 'success', // 已通过
        '2': 'danger',  // 已驳回
        '3': 'warning'  // 已撤销
      };
      return typeMap[status] || 'info';
    },

    // 获取时间线图标
    getTimelineIcon(status) {
      const iconMap = {
        '0': 'el-icon-time',
        '1': 'el-icon-check',
        '2': 'el-icon-close',
        '3': 'el-icon-refresh-left'
      };
      return iconMap[status] || 'el-icon-time';
    },

    // 获取单据类型文本
    getBillTypeText(billType) {
      const typeMap = {
        '0': '出库',
        '1': '入库',
        '2': '复合'
      };
      return typeMap[billType] || '未知';
    },

    // 获取业务类型文本
    getBusinessTypeText(businessType) {
      if (!businessType) return '未知';

      // 从业务类型选项中查找对应的标签
      const option = this.businessTypeOptions.find(opt => opt.value === businessType);
      return option ? option.label : businessType;
    }

  }
};
</script>

<style scoped>
/* 统计卡片样式 */
.statistics-card {
  margin-bottom: 20px;
}

.statistics-content {
  display: flex;
  align-items: center;
  padding: 20px;
}

.statistics-icon {
  font-size: 40px;
  margin-right: 20px;
}

.statistics-info {
  flex: 1;
}

.statistics-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.statistics-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

/* 表格信息样式 */
.business-info, .applicant-info, .receiver-info {
  line-height: 1.6;
}

.info-item {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.info-item i {
  margin-right: 5px;
  color: #409EFF;
}

.user-name {
  font-weight: 500;
  color: #303133;
}

.link-text {
  color: #409EFF;
  cursor: pointer;
  text-decoration: underline;
}

.link-text:hover {
  color: #66b1ff;
}

.reason-info {
  text-align: left;
}

.status-info {
  text-align: center;
}

.workflow-info {
  margin-top: 5px;
}

/* 表单样式 */
.face-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.face-uploader .el-upload:hover {
  border-color: #409EFF;
}

.face-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}

.face-image {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 6px;
  cursor: pointer;
}

.upload-tips {
  margin-top: 10px;
  font-size: 12px;
  color: #909399;
}

.upload-tips p {
  margin: 2px 0;
}

/* 物料清单信息卡片 */
.material-bill-info {
  margin-top: 15px;
}

/* 图片预览 */
.image-preview-container {
  text-align: center;
}

/* 过滤容器和表格容器 */
.filter-container {
  margin-bottom: 10px;
}

.table-container {
  margin-bottom: 20px;
}

/* 对话框样式 */
.dialog-footer {
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .statistics-content {
    flex-direction: column;
    text-align: center;
  }

  .statistics-icon {
    margin-right: 0;
    margin-bottom: 10px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
  }
}

/* 领料员列表样式 */
.receiver-list {
  margin-top: 20px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;
}

.list-header span {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.receiver-items {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.receiver-item {
  border-radius: 8px;
  overflow: hidden;
}

.receiver-content {
  padding: 15px;
}

.receiver-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 100%;
}

.empty-receivers {
  text-align: center;
  padding: 40px 0;
}

.face-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100px;
  height: 100px;
  display: block;
}

.face-uploader:hover {
  border-color: #409eff;
}

.face-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.face-image {
  width: 100px;
  height: 100px;
  display: block;
  object-fit: cover;
  cursor: pointer;
}

.upload-tips {
  margin-top: 8px;
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
}

.upload-tips p {
  margin: 0;
}

/* 表格中领料员信息显示样式 */
.receivers-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.receiver-item-display {
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.receiver-item-display:nth-child(even) {
  background-color: #f0f9ff;
  border-left-color: #67c23a;
}

.single-receiver-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 暂存状态样式 */
.el-tag.is-draft {
  background-color: #f4f4f5;
  border-color: #e9e9eb;
  color: #909399;
}

/* 对话框按钮样式 */
.dialog-footer {
  text-align: right;
  padding-top: 20px;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

.dialog-footer .el-button:first-child {
  margin-left: 0;
}

/* 审批状态点击样式 */
.clickable-status {
  cursor: pointer;
  transition: all 0.3s;
}

.clickable-status:hover {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 2px;
}

.click-hint {
  margin-top: 2px;
}

/* 审批详情弹窗样式 */
.approval-detail-dialog .approval-basic-info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.approval-detail-dialog .approval-basic-info h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.approval-detail-dialog .info-item {
  margin-bottom: 10px;
}

.approval-detail-dialog .info-item label {
  font-weight: 600;
  color: #606266;
  margin-right: 8px;
}

.approval-detail-dialog .approval-flow-info {
  margin-top: 20px;
}

.approval-detail-dialog .approval-flow-info h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.approval-detail-dialog .timeline-content {
  padding-left: 10px;
}

.approval-detail-dialog .timeline-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.approval-detail-dialog .node-name {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.approval-detail-dialog .timeline-detail {
  color: #606266;
  font-size: 13px;
  line-height: 1.6;
}

.approval-detail-dialog .timeline-detail p {
  margin: 4px 0;
}

.approval-detail-dialog .no-approval-data,
.approval-detail-dialog .no-approval-instance {
  text-align: center;
  padding: 20px;
}

/* 物料清单和物料详情对话框样式 */
.info-item {
  margin-bottom: 10px;
}

.info-item label {
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
}

.info-item span {
  color: #303133;
}

/* 重量汇总样式 */
.weight-summary-item {
  text-align: center;
  padding: 10px;
}

.weight-summary-item label {
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
  font-size: 14px;
}

.weight-summary-item span {
  font-size: 16px;
}

.box-card {
  border-radius: 4px;
}

.box-card .clearfix:before,
.box-card .clearfix:after {
  display: table;
  content: "";
}

.box-card .clearfix:after {
  clear: both;
}
</style>

