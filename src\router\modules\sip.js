import Layout from '@/layout'

const sipRouter = {
  path: '/sip',
  component: Layout,
  redirect: '/sip/dashboard',
  name: '<PERSON><PERSON>',
  meta: {
    title: 'SIP Server',
    icon: 'phone',
    roles: ['admin', 'sip_admin'],
    menuId: 2700  // 对应数据库菜单ID
  },
  children: [
    {
      path: 'dashboard',
      component: () => import('@/views/sip/dashboard/index'),
      name: 'SipDashboard',
      meta: {
        title: '系统概览',
        icon: 'dashboard',
        affix: true,
        noCache: false,
        menuId: 2701,
        perms: ['sip:dashboard:view']
      }
    },
    {
      path: 'devices',
      component: () => import('@/views/sip/devices/index'),
      name: 'SipDevices',
      meta: {
        title: '设备管理',
        icon: 'component',
        menuId: 2702,
        perms: ['sip:device:list']
      }
    },
    {
      path: 'sessions',
      component: () => import('@/views/sip/sessions/index'),
      name: 'SipSessions',
      meta: {
        title: '会话管理',
        icon: 'link',
        perms: ['sip:session:list']
      }
    },
    {
      path: 'media',
      component: () => import('@/views/sip/media/index'),
      name: 'SipMedia',
      meta: {
        title: '媒体管理',
        icon: 'video-play',
        perms: ['sip:media:list']
      }
    },
    {
      path: 'messages',
      component: () => import('@/views/sip/messages/index'),
      name: 'SipMessages',
      meta: {
        title: '消息管理',
        icon: 'message',
        perms: ['sip:message:list']
      }
    },
    {
      path: 'performance',
      component: () => import('@/views/sip/performance/index'),
      name: 'SipPerformance',
      meta: {
        title: '性能监控',
        icon: 'monitor',
        perms: ['sip:monitor:view']
      }
    },
    {
      path: 'testing',
      component: () => import('@/views/sip/testing/index'),
      name: 'SipTesting',
      meta: {
        title: '系统测试',
        icon: 'bug',
        perms: ['sip:test:manage']
      }
    },
    {
      path: 'settings',
      component: () => import('@/views/sip/settings/index'),
      name: 'SipSettings',
      meta: {
        title: '系统设置',
        icon: 'setting',
        perms: ['sip:settings:manage']
      }
    },
    {
      path: 'test-api',
      component: () => import('@/views/sip/test-api'),
      name: 'SipApiTest',
      meta: {
        title: 'API测试',
        icon: 'bug',
        perms: ['sip:test:manage']
      }
    }
  ]
}

export default sipRouter
