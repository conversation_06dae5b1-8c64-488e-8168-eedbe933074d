<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="page-title">
        <h2><i class="el-icon-monitor"></i> EMQX客户端管理系统</h2>
        <p>统一管理EMQX服务器客户端、设备认证、数据分流和系统清理</p>
      </div>
      <div class="page-actions">
        <el-button type="primary" icon="el-icon-refresh" @click="refreshAll" :loading="loading">
          刷新数据
        </el-button>
        <el-button type="warning" icon="el-icon-warning" @click="showConnectionHelp" v-if="hasConnectionError">
          连接帮助
        </el-button>
        <el-button type="success" icon="el-icon-setting" @click="openSettings">
          系统设置
        </el-button>
      </div>
    </div>

    <!-- 系统概览卡片 -->
    <el-row :gutter="20" class="overview-cards">
      <el-col :span="6">
        <el-card class="overview-card" @click.native="navigateTo('/emqx/serverRegistry')">
          <div class="card-content">
            <div class="card-icon servers">
              <i class="el-icon-connection"></i>
            </div>
            <div class="card-info">
              <div class="card-value">{{ overview.serverCount }}</div>
              <div class="card-label">服务器客户端</div>
              <div class="card-status">{{ overview.onlineServers }} 在线</div>
            </div>
          </div>
          <div class="card-footer">
            <span>查看详情</span>
            <i class="el-icon-arrow-right"></i>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card" @click.native="navigateTo('/emqx/deviceAuth')">
          <div class="card-content">
            <div class="card-icon devices">
              <i class="el-icon-key"></i>
            </div>
            <div class="card-info">
              <div class="card-value">{{ overview.deviceCount }}</div>
              <div class="card-label">设备认证</div>
              <div class="card-status">{{ overview.activeDevices }} 活跃</div>
            </div>
          </div>
          <div class="card-footer">
            <span>管理认证</span>
            <i class="el-icon-arrow-right"></i>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card" @click.native="navigateTo('/emqx/dataStream')">
          <div class="card-content">
            <div class="card-icon streams">
              <i class="el-icon-data-line"></i>
            </div>
            <div class="card-info">
              <div class="card-value">{{ formatNumber(overview.totalMessages) }}</div>
              <div class="card-label">消息总数</div>
              <div class="card-status">{{ overview.messageRate }}/s</div>
            </div>
          </div>
          <div class="card-footer">
            <span>数据分流</span>
            <i class="el-icon-arrow-right"></i>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card" @click.native="navigateTo('/emqx/cleanup')">
          <div class="card-content">
            <div class="card-icon cleanup">
              <i class="el-icon-delete"></i>
            </div>
            <div class="card-info">
              <div class="card-value">{{ overview.duplicateCount }}</div>
              <div class="card-label">重复客户端</div>
              <div class="card-status">{{ overview.lastCleanup }}</div>
            </div>
          </div>
          <div class="card-footer">
            <span>清理管理</span>
            <i class="el-icon-arrow-right"></i>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统状态监控 -->
    <el-row :gutter="20">
      <el-col :span="16">
        <el-card class="chart-card">
          <div slot="header">
            <span><i class="el-icon-data-analysis"></i> 系统监控</span>
            <div style="float: right;">
              <el-radio-group v-model="chartTimeRange" size="mini" @change="updateChart">
                <el-radio-button label="1h">1小时</el-radio-button>
                <el-radio-button label="6h">6小时</el-radio-button>
                <el-radio-button label="24h">24小时</el-radio-button>
              </el-radio-group>
            </div>
          </div>
          <div id="systemChart" style="height: 350px;"></div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card class="status-card">
          <div slot="header">
            <span><i class="el-icon-warning"></i> 系统状态</span>
          </div>
          <div class="status-list">
            <div class="status-item" v-for="status in systemStatus" :key="status.name">
              <div class="status-indicator" :class="status.type"></div>
              <div class="status-content">
                <div class="status-name">{{ status.name }}</div>
                <div class="status-desc">{{ status.description }}</div>
              </div>
              <div class="status-action">
                <el-button
                  v-if="status.action"
                  size="mini"
                  :type="status.actionType"
                  @click="handleStatusAction(status)"
                >
                  {{ status.actionText }}
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作和最近活动 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card class="quick-actions-card">
          <div slot="header">
            <span><i class="el-icon-magic-stick"></i> 快速操作</span>
          </div>
          <div class="quick-actions">
            <div class="action-group">
              <h4>服务器管理</h4>
              <el-button-group>
                <el-button size="small" icon="el-icon-plus" @click="quickCreateServer">
                  注册服务器
                </el-button>
                <el-button size="small" icon="el-icon-connection" @click="quickTestConnections">
                  测试连接
                </el-button>
              </el-button-group>
            </div>
            
            <div class="action-group">
              <h4>设备认证</h4>
              <el-button-group>
                <el-button size="small" icon="el-icon-key" @click="quickCreateAuth">
                  创建认证
                </el-button>
                <el-button size="small" icon="el-icon-upload2" @click="quickBatchImport">
                  批量导入
                </el-button>
              </el-button-group>
            </div>
            
            <div class="action-group">
              <h4>数据监控</h4>
              <el-button-group>
                <el-button size="small" icon="el-icon-data-line" @click="quickViewStreams">
                  查看数据流
                </el-button>
                <el-button size="small" icon="el-icon-upload2" @click="quickSimulateData">
                  模拟数据
                </el-button>
              </el-button-group>
            </div>
            
            <div class="action-group">
              <h4>系统维护</h4>
              <el-button-group>
                <el-button size="small" icon="el-icon-search" @click="quickCheckDuplicates">
                  检查重复
                </el-button>
                <el-button size="small" icon="el-icon-delete" @click="quickCleanup">
                  清理系统
                </el-button>
              </el-button-group>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="recent-activities-card">
          <div slot="header">
            <span><i class="el-icon-time"></i> 最近活动</span>
            <el-button type="text" icon="el-icon-refresh" @click="loadRecentActivities">
              刷新
            </el-button>
          </div>
          <div class="activities-list">
            <div
              class="activity-item"
              v-for="activity in recentActivities"
              :key="activity.id"
            >
              <div class="activity-icon" :class="activity.type">
                <i :class="activity.icon"></i>
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-desc">{{ activity.description }}</div>
                <div class="activity-time">{{ formatTime(activity.time) }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 设置对话框 -->
    <el-dialog
      title="系统设置"
      :visible.sync="settingsVisible"
      width="600px"
      :before-close="closeSettings"
    >
      <el-form :model="settings" label-width="120px">
        <el-form-item label="自动刷新">
          <el-switch v-model="settings.autoRefresh" />
          <span style="margin-left: 10px; color: #909399;">
            {{ settings.autoRefresh ? '已启用' : '已禁用' }}
          </span>
        </el-form-item>
        <el-form-item label="刷新间隔">
          <el-select v-model="settings.refreshInterval" :disabled="!settings.autoRefresh">
            <el-option label="30秒" :value="30" />
            <el-option label="1分钟" :value="60" />
            <el-option label="5分钟" :value="300" />
          </el-select>
        </el-form-item>
        <el-form-item label="消息提醒">
          <el-switch v-model="settings.notifications" />
        </el-form-item>
        <el-form-item label="调试模式">
          <el-switch v-model="settings.debugMode" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeSettings">取消</el-button>
        <el-button type="primary" @click="saveSettings">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import {
  getDashboardOverview,
  getSystemStatus,
  getRecentActivities
} from '@/api/emqx/dashboard'

export default {
  name: 'EmqxDashboard',
  data() {
    return {
      loading: false,
      chartTimeRange: '6h',
      hasConnectionError: false,
      
      // 系统概览数据
      overview: {
        serverCount: 5,
        onlineServers: 4,
        deviceCount: 128,
        activeDevices: 95,
        totalMessages: 1234567,
        messageRate: 45,
        duplicateCount: 3,
        lastCleanup: '2小时前'
      },
      
      // 系统状态
      systemStatus: [
        {
          name: 'EMQX服务',
          description: '运行正常',
          type: 'success',
          action: false
        },
        {
          name: '服务器连接',
          description: '4/5 在线',
          type: 'warning',
          action: true,
          actionType: 'warning',
          actionText: '检查'
        },
        {
          name: '数据分流',
          description: '正常运行',
          type: 'success',
          action: false
        },
        {
          name: '重复客户端',
          description: '发现3个重复',
          type: 'danger',
          action: true,
          actionType: 'danger',
          actionText: '清理'
        }
      ],
      
      // 最近活动
      recentActivities: [
        {
          id: 1,
          type: 'success',
          icon: 'el-icon-check',
          title: '设备认证创建',
          description: '成功创建设备 outmis_client_001 的认证',
          time: Date.now() - 300000
        },
        {
          id: 2,
          type: 'warning',
          icon: 'el-icon-warning',
          title: '服务器离线',
          description: 'guide_server_mqtt 连接断开',
          time: Date.now() - 600000
        },
        {
          id: 3,
          type: 'info',
          icon: 'el-icon-data-line',
          title: '数据分流',
          description: '处理了 1,234 条消息',
          time: Date.now() - 900000
        },
        {
          id: 4,
          type: 'danger',
          icon: 'el-icon-delete',
          title: '清理重复客户端',
          description: '删除了 2 个重复的客户端连接',
          time: Date.now() - 1200000
        }
      ],
      
      // 设置
      settingsVisible: false,
      settings: {
        autoRefresh: true,
        refreshInterval: 60,
        notifications: true,
        debugMode: false
      },
      
      // 图表
      systemChart: null,
      refreshTimer: null
    }
  },
  
  created() {
    this.loadData()
    this.startAutoRefresh()
  },
  
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  
  beforeDestroy() {
    this.stopAutoRefresh()
    if (this.systemChart) {
      this.systemChart.dispose()
    }
  },
  
  methods: {
    async loadData() {
      this.loading = true
      try {
        // 这里应该调用实际的API获取数据
        await this.loadOverviewData()
        await this.loadSystemStatus()
        await this.loadRecentActivities()
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },
    
    async loadOverviewData() {
      try {
        const response = await getDashboardOverview()
        if (response.code === 200) {
          this.overview = response.data
          this.hasConnectionError = false
        }
      } catch (error) {
        console.error('加载概览数据失败:', error)
        this.hasConnectionError = true

        if (error.code === 'ECONNABORTED') {
          this.$message.warning('服务器响应超时，请检查后端服务状态')
        } else {
          this.$message.error('加载概览数据失败，请检查网络连接')
        }

        // 使用默认数据
        this.overview = {
          serverCount: 0,
          onlineServers: 0,
          deviceCount: 0,
          activeDevices: 0,
          totalMessages: 0,
          messageRate: 0,
          duplicateCount: 0,
          lastCleanup: '未知'
        }
      }
    },

    async loadSystemStatus() {
      try {
        const response = await getSystemStatus()
        if (response.code === 200) {
          this.systemStatus = response.data
        }
      } catch (error) {
        console.error('加载系统状态失败:', error)
      }
    },

    async loadRecentActivities() {
      try {
        const response = await getRecentActivities()
        if (response.code === 200) {
          this.recentActivities = response.data
        }
      } catch (error) {
        console.error('加载最近活动失败:', error)
      }
    },
    
    refreshAll() {
      this.loadData()
      this.updateChart()
    },
    
    startAutoRefresh() {
      if (this.settings.autoRefresh) {
        this.refreshTimer = setInterval(() => {
          this.loadData()
          this.updateChart()
        }, this.settings.refreshInterval * 1000)
      }
    },
    
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },
    
    navigateTo(path) {
      this.$router.push(path)
    },
    
    // 快速操作方法
    quickCreateServer() {
      this.$router.push('/emqx/serverRegistry')
    },
    
    quickTestConnections() {
      this.$message.info('正在测试所有服务器连接...')
    },
    
    quickCreateAuth() {
      this.$router.push('/emqx/deviceAuth')
    },
    
    quickBatchImport() {
      this.$router.push('/emqx/deviceAuth')
    },
    
    quickViewStreams() {
      this.$router.push('/emqx/dataStream')
    },
    
    quickSimulateData() {
      this.$router.push('/emqx/dataStream')
    },
    
    quickCheckDuplicates() {
      this.$router.push('/emqx/cleanup')
    },
    
    quickCleanup() {
      this.$router.push('/emqx/cleanup')
    },
    
    handleStatusAction(status) {
      if (status.name === '服务器连接') {
        this.quickTestConnections()
      } else if (status.name === '重复客户端') {
        this.quickCleanup()
      }
    },
    
    openSettings() {
      this.settingsVisible = true
    },
    
    closeSettings() {
      this.settingsVisible = false
    },
    
    saveSettings() {
      this.$message.success('设置已保存')
      this.closeSettings()

      // 重新启动自动刷新
      this.stopAutoRefresh()
      this.startAutoRefresh()
    },

    showConnectionHelp() {
      this.$alert(`
        <div style="text-align: left;">
          <h4>连接问题诊断：</h4>
          <p>1. 检查后端服务是否正常运行</p>
          <p>2. 确认API地址配置正确</p>
          <p>3. 检查网络连接状态</p>
          <p>4. 查看浏览器控制台错误信息</p>
          <br>
          <p><strong>当前API地址：</strong> ${process.env.VUE_APP_BASE_API}</p>
        </div>
      `, '连接帮助', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '我知道了'
      })
    },
    
    // 图表相关
    initChart() {
      const chartDom = document.getElementById('systemChart')
      if (!chartDom) return
      
      this.systemChart = echarts.init(chartDom)
      this.updateChart()
    },
    
    updateChart() {
      if (!this.systemChart) return

      // 获取真实的历史数据
      this.getHistoricalData().then(data => {
        const times = []
        const serverData = []
        const deviceData = []
        const messageData = []

        if (data && data.length > 0) {
          // 使用真实数据
          data.forEach(item => {
            const time = new Date(item.timestamp)
            times.push(time.getHours() + ':' + String(time.getMinutes()).padStart(2, '0'))
            serverData.push(item.serverCount || 0)
            deviceData.push(item.deviceCount || 0)
            messageData.push(item.messageCount || 0)
          })
        } else {
          // 如果没有真实数据，生成基础数据点
          const now = new Date()
          const hours = this.chartTimeRange === '1h' ? 1 : this.chartTimeRange === '6h' ? 6 : 24
          const interval = this.chartTimeRange === '1h' ? 5 : this.chartTimeRange === '6h' ? 30 : 60

          for (let i = hours * 60; i >= 0; i -= interval) {
            const time = new Date(now.getTime() - i * 60 * 1000)
            times.push(time.getHours() + ':' + String(time.getMinutes()).padStart(2, '0'))
            serverData.push(0)
            deviceData.push(0)
            messageData.push(0)
          }
        }
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        legend: {
          data: ['在线服务器', '活跃设备', '消息速率']
        },
        xAxis: {
          type: 'category',
          data: times
        },
        yAxis: [
          {
            type: 'value',
            name: '数量',
            position: 'left'
          },
          {
            type: 'value',
            name: '速率(/s)',
            position: 'right'
          }
        ],
        series: [
          {
            name: '在线服务器',
            type: 'line',
            data: serverData,
            smooth: true,
            itemStyle: { color: '#409eff' }
          },
          {
            name: '活跃设备',
            type: 'line',
            data: deviceData,
            smooth: true,
            itemStyle: { color: '#67c23a' }
          },
          {
            name: '消息速率',
            type: 'line',
            yAxisIndex: 1,
            data: messageData,
            smooth: true,
            itemStyle: { color: '#e6a23c' }
          }
        ]
      }
      
      this.systemChart.setOption(option)
      }).catch(error => {
        console.error('获取历史数据失败:', error)
        // 如果获取失败，使用空数据
        this.updateChartWithEmptyData()
      })
    },

    /** 获取历史数据 */
    async getHistoricalData() {
      try {
        // 这里应该调用真实的历史数据API
        // const response = await getHistoricalStats(this.chartTimeRange)
        // return response.data

        // 暂时返回空数组，表示没有历史数据
        return []
      } catch (error) {
        console.error('获取历史数据失败:', error)
        return []
      }
    },

    /** 使用空数据更新图表 */
    updateChartWithEmptyData() {
      if (!this.systemChart) return

      const times = []
      const serverData = []
      const deviceData = []
      const messageData = []

      const now = new Date()
      const hours = this.chartTimeRange === '1h' ? 1 : this.chartTimeRange === '6h' ? 6 : 24
      const interval = this.chartTimeRange === '1h' ? 5 : this.chartTimeRange === '6h' ? 30 : 60

      for (let i = hours * 60; i >= 0; i -= interval) {
        const time = new Date(now.getTime() - i * 60 * 1000)
        times.push(time.getHours() + ':' + String(time.getMinutes()).padStart(2, '0'))
        serverData.push(0)
        deviceData.push(0)
        messageData.push(0)
      }

      const option = {
        xAxis: { data: times },
        series: [
          { data: serverData },
          { data: deviceData },
          { data: messageData }
        ]
      }

      this.systemChart.setOption(option)
    },

    // 工具方法
    formatNumber(num) {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num?.toString() || '0'
    },
    
    formatTime(time) {
      const now = Date.now()
      const diff = now - time
      
      if (diff < 60000) {
        return '刚刚'
      } else if (diff < 3600000) {
        return Math.floor(diff / 60000) + '分钟前'
      } else if (diff < 86400000) {
        return Math.floor(diff / 3600000) + '小时前'
      } else {
        return Math.floor(diff / 86400000) + '天前'
      }
    }
  }
}
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title h2 {
  margin: 0;
  color: #303133;
}

.page-title p {
  margin: 5px 0 0 0;
  color: #909399;
  font-size: 14px;
}

.overview-cards {
  margin-bottom: 20px;
}

.overview-card {
  cursor: pointer;
  transition: all 0.3s;
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.overview-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
}

.card-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  margin-right: 15px;
}

.card-icon.servers { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.card-icon.devices { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.card-icon.streams { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.card-icon.cleanup { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

.card-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
}

.card-label {
  font-size: 14px;
  color: #909399;
  margin: 5px 0;
}

.card-status {
  font-size: 12px;
  color: #67c23a;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 10px;
  border-top: 1px solid #ebeef5;
  color: #409eff;
  font-size: 14px;
}

.chart-card, .status-card, .quick-actions-card, .recent-activities-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.status-list {
  padding: 10px 0;
}

.status-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.status-item:last-child {
  border-bottom: none;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 15px;
}

.status-indicator.success { background: #67c23a; }
.status-indicator.warning { background: #e6a23c; }
.status-indicator.danger { background: #f56c6c; }

.status-content {
  flex: 1;
}

.status-name {
  font-weight: 500;
  color: #303133;
}

.status-desc {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.quick-actions {
  padding: 10px 0;
}

.action-group {
  margin-bottom: 20px;
}

.action-group h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 14px;
}

.activities-list {
  padding: 10px 0;
  max-height: 350px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: white;
  margin-right: 15px;
  flex-shrink: 0;
}

.activity-icon.success { background: #67c23a; }
.activity-icon.warning { background: #e6a23c; }
.activity-icon.info { background: #409eff; }
.activity-icon.danger { background: #f56c6c; }

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 5px;
}

.activity-desc {
  font-size: 12px;
  color: #606266;
  margin-bottom: 5px;
}

.activity-time {
  font-size: 11px;
  color: #909399;
}
</style>
