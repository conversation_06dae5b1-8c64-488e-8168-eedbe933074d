<template>
  <div class="app-container">
    <!-- 搜索区域 - 卡片式布局 -->
    <el-card class="search-card" shadow="hover">
      <div slot="header" class="clearfix">
        <span><i class="el-icon-search"></i> 物料基础信息查询</span>
        <el-button 
          style="float: right; padding: 3px 0" 
          type="text" 
          icon="el-icon-more" 
          @click="toggleAdvancedSearch">
          {{ showAdvanced ? '收起高级搜索' : '展开高级搜索' }}
        </el-button>
      </div>
      
      <!-- 常用快速搜索区域 -->
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="90px">
        <el-form-item label="物料编码" prop="materialCode">
          <el-input
            v-model="queryParams.materialCode"
            placeholder="请输入物料编码"
            clearable
            prefix-icon="el-icon-document"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="物料名称" prop="materialName">
          <el-input
            v-model="queryParams.materialName"
            placeholder="请输入物料名称"
            clearable
            prefix-icon="el-icon-goods"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="物料分类" prop="categoryId">
          <el-cascader
            v-model="queryParams.categoryId"
            :options="categoryOptions"
            :props="{ checkStrictly: true, value: 'id', label: 'name', emitPath: false }"
            style="width: 100%"
            placeholder="请选择物料分类"
            clearable
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="物料状态"
            clearable
            style="width: 100%"
          >
            <el-option label="启用" value="0" />
            <el-option label="停用" value="1" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 高级搜索区域 -->
      <div v-if="showAdvanced">
        <el-divider content-position="left">高级搜索</el-divider>
        <el-form :model="queryParams" size="small" :inline="true" label-width="90px">
          <el-form-item label="外部编码" prop="externalCode">
            <el-input
              v-model="queryParams.externalCode"
              placeholder="请输入外部编码"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="外部系统" prop="externalSystem">
            <el-select
              v-model="queryParams.externalSystem"
              placeholder="请选择外部系统"
              clearable
              style="width: 100%"
            >
              <el-option label="SAP" value="SAP" />
              <el-option label="ERP" value="ERP" />
              <el-option label="WMS" value="WMS" />
              <el-option label="OTHER" value="OTHER" />
            </el-select>
          </el-form-item>
          <el-form-item label="规格型号" prop="specification">
            <el-input
              v-model="queryParams.specification"
              placeholder="请输入规格型号"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="单位" prop="unit">
            <el-select
              v-model="queryParams.unit"
              placeholder="请选择单位"
              clearable
              style="width: 100%"
            >
              <el-option label="千克" value="kg" />
              <el-option label="克" value="g" />
              <el-option label="吨" value="t" />
              <el-option label="个" value="个" />
              <el-option label="箱" value="箱" />
              <el-option label="瓶" value="瓶" />
              <el-option label="桶" value="桶" />
              <el-option label="袋" value="袋" />
            </el-select>
          </el-form-item>
          <el-form-item label="危险等级" prop="hazardLevel">
            <el-select
              v-model="queryParams.hazardLevel"
              placeholder="请选择危险等级"
              clearable
              style="width: 100%"
            >
              <el-option label="无危险" value="0" />
              <el-option label="低危" value="1" />
              <el-option label="中危" value="2" />
              <el-option label="高危" value="3" />
            </el-select>
          </el-form-item>
          <el-form-item label="批次管理" prop="hasBatch">
            <el-select
              v-model="queryParams.hasBatch"
              placeholder="是否批次管理"
              clearable
              style="width: 100%"
            >
              <el-option label="是" value="1" />
              <el-option label="否" value="0" />
            </el-select>
          </el-form-item>
          <el-form-item label="效期管理" prop="hasExpire">
            <el-select
              v-model="queryParams.hasExpire"
              placeholder="是否效期管理"
              clearable
              style="width: 100%"
            >
              <el-option label="是" value="1" />
              <el-option label="否" value="0" />
            </el-select>
          </el-form-item>
          <el-form-item label="重量管控" prop="isWeightControl">
            <el-select
              v-model="queryParams.isWeightControl"
              placeholder="是否重量管控"
              clearable
              style="width: 100%"
            >
              <el-option label="是" value="1" />
              <el-option label="否" value="0" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 操作按钮区域 -->
    <el-row :gutter="10" class="mb8 mt10">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['material:material:add']"
        >新增物料</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['material:material:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['material:material:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['material:material:import']"
        >导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['material:material:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-tickets"
          size="mini"
          @click="handleTemplate"
        >模板下载</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格区域 -->
    <el-card shadow="hover">
      <el-table 
        v-loading="loading" 
        :data="materialList" 
        @selection-change="handleSelectionChange"
        border
        stripe
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" align="center" fixed="left" />
        <el-table-column type="index" label="序号" width="60" align="center" fixed="left" />
        
        <!-- 基本信息列 -->
        <el-table-column label="基本信息" align="center">
          <el-table-column label="物料编码" prop="materialCode" width="120" align="center" :show-overflow-tooltip="true" fixed="left" />
          <el-table-column label="物料名称" prop="materialName" width="150" align="center" :show-overflow-tooltip="true" />
          <el-table-column label="分类名称" prop="categoryName" width="120" align="center" :show-overflow-tooltip="true" />
          <el-table-column label="规格型号" prop="specification" width="120" align="center" :show-overflow-tooltip="true" />
          <el-table-column label="单位" prop="unit" width="80" align="center" />
        </el-table-column>
        
        <!-- 外部系统信息列 -->
        <el-table-column label="外部系统信息" align="center">
          <el-table-column label="外部编码" prop="externalCode" width="120" align="center" :show-overflow-tooltip="true" />
          <el-table-column label="外部系统" prop="externalSystem" width="100" align="center" />
          <el-table-column label="映射数量" width="100" align="center">
            <template slot-scope="scope">
              <el-button 
                type="text" 
                size="mini" 
                @click="handleViewMappings(scope.row)"
                v-if="scope.row.mappingCount"
              >{{scope.row.mappingCount}} 个</el-button>
              <span v-else>无映射</span>
            </template>
          </el-table-column>
        </el-table-column>
        
        <!-- 状态信息列 -->
        <el-table-column label="管理状态" align="center">
          <el-table-column label="批次管理" align="center" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.hasBatch === '1' ? 'success' : 'info'" size="mini">
                {{ scope.row.hasBatch === '1' ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="效期管理" align="center" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.hasExpire === '1' ? 'success' : 'info'" size="mini">
                {{ scope.row.hasExpire === '1' ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="重量管控" align="center" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.isWeightControl === '1' ? 'success' : 'info'" size="mini">
                {{ scope.row.isWeightControl === '1' ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table-column>
        
        <!-- 重量信息列 -->
        <el-table-column label="重量信息" align="center">
          <el-table-column label="标准重量" align="center" prop="standardWeight" width="100" />
          <el-table-column label="重量单位" align="center" prop="weightUnit" width="90" />
        </el-table-column>
        
        <!-- 库存信息列 -->
        <el-table-column label="库存信息" align="center">
          <el-table-column label="最低库存" align="center" prop="minStock" width="90" />
          <el-table-column label="最高库存" align="center" prop="maxStock" width="90" />
        </el-table-column>
        
        <!-- 状态列 -->
        <el-table-column label="状态" align="center" width="120">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === '0' ? 'success' : 'info'">
              {{ scope.row.status === '0' ? '启用' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <!-- 操作列 -->
        <el-table-column label="操作" align="center" width="180" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
            >查看</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['material:material:edit']"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['material:material:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改物料信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="750px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-tabs v-model="activeName">
          <el-tab-pane label="基本信息" name="basic">
            <el-row>
              <el-col :span="12">
                <el-form-item label="物料编码" prop="materialCode">
                  <el-input v-model="form.materialCode" placeholder="请输入物料编码" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="物料名称" prop="materialName">
                  <el-input v-model="form.materialName" placeholder="请输入物料名称" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row>
              <el-col :span="12">
                <el-form-item label="物料分类" prop="categoryId">
                  <el-cascader
                    v-model="form.categoryId"
                    :options="categoryOptions"
                    :props="{ checkStrictly: true, value: 'id', label: 'name', emitPath: false }"
                    style="width: 100%"
                    placeholder="请选择物料分类"
                    clearable
                    @change="handleCategoryChange"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="规格型号" prop="specification">
                  <el-input v-model="form.specification" placeholder="请输入规格型号" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row>
              <el-col :span="12">
                <el-form-item label="单位" prop="unit">
                  <el-select v-model="form.unit" placeholder="请选择单位" style="width: 100%">
                    <el-option label="千克" value="kg" />
                    <el-option label="克" value="g" />
                    <el-option label="吨" value="t" />
                    <el-option label="个" value="个" />
                    <el-option label="箱" value="箱" />
                    <el-option label="瓶" value="瓶" />
                    <el-option label="桶" value="桶" />
                    <el-option label="袋" value="袋" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="状态" prop="status">
                  <el-radio-group v-model="form.status">
                    <el-radio label="0">启用</el-radio>
                    <el-radio label="1">停用</el-radio>
                  </el-radio-group>
                  <div class="el-form-item-tip">启用状态可被其他模块调用，停用状态禁止调用</div>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row>
              <el-col :span="12">
                <el-form-item label="条形码" prop="barCode">
                  <el-input v-model="form.barCode" placeholder="请输入条形码" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="二维码" prop="qrCode">
                  <el-input v-model="form.qrCode" placeholder="请输入二维码" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
          
          <el-tab-pane label="外部系统信息" name="external">
            <el-row>
              <el-col :span="12">
                <el-form-item label="外部编码" prop="externalCode">
                  <el-input v-model="form.externalCode" placeholder="请输入外部编码" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="外部系统" prop="externalSystem">
                  <el-select v-model="form.externalSystem" placeholder="请选择外部系统" style="width: 100%">
                    <el-option label="SAP" value="SAP" />
                    <el-option label="ERP" value="ERP" />
                    <el-option label="WMS" value="WMS" />
                    <el-option label="OTHER" value="OTHER" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row>
              <el-col :span="24">
                <el-form-item label="是否模板" prop="isTemplate">
                  <el-radio-group v-model="form.isTemplate">
                    <el-radio label="0">否</el-radio>
                    <el-radio label="1">是</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row>
              <el-col :span="12">
                <el-form-item label="版本号" prop="version">
                  <el-input v-model="form.version" placeholder="请输入版本号" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="模板ID" prop="templateId" v-if="form.isTemplate === '0'">
                  <el-input v-model="form.templateId" placeholder="请输入模板ID" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
          
          <el-tab-pane label="重量信息" name="weight">
            <el-row>
              <el-col :span="12">
                <el-form-item label="单位重量" prop="unitWeight">
                  <el-input-number v-model="form.unitWeight" :precision="3" :step="0.001" placeholder="请输入单位重量" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="重量单位" prop="weightUnit">
                  <el-select v-model="form.weightUnit" placeholder="请选择重量单位" style="width: 100%">
                    <el-option label="千克" value="kg" />
                    <el-option label="克" value="g" />
                    <el-option label="吨" value="t" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row>
              <el-col :span="12">
                <el-form-item label="毛重" prop="grossWeight">
                  <el-input-number v-model="form.grossWeight" :precision="3" :step="0.001" placeholder="请输入毛重" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="净重" prop="netWeight">
                  <el-input-number v-model="form.netWeight" :precision="3" :step="0.001" placeholder="请输入净重" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row>
              <el-col :span="12">
                <el-form-item label="标准重量" prop="standardWeight">
                  <el-input-number v-model="form.standardWeight" :precision="3" :step="0.001" placeholder="请输入标准重量" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="误差范围(%)" prop="weightTolerance">
                  <el-input-number v-model="form.weightTolerance" :precision="2" :step="0.1" placeholder="请输入误差范围" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row>
              <el-col :span="24">
                <el-form-item label="重量管控" prop="isWeightControl">
                  <el-radio-group v-model="form.isWeightControl">
                    <el-radio label="0">否</el-radio>
                    <el-radio label="1">是</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
          
          <el-tab-pane label="库存信息" name="inventory">
            <el-row>
              <el-col :span="12">
                <el-form-item label="最低库存" prop="minStock">
                  <el-input-number v-model="form.minStock" :precision="2" :step="1" placeholder="请输入最低库存" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="最高库存" prop="maxStock">
                  <el-input-number v-model="form.maxStock" :precision="2" :step="1" placeholder="请输入最高库存" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row>
              <el-col :span="12">
                <el-form-item label="标准单价" prop="standardPrice">
                  <el-input-number v-model="form.standardPrice" :precision="2" :step="0.1" placeholder="请输入标准单价" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="保质期(天)" prop="shelfLife">
                  <el-input-number v-model="form.shelfLife" :min="0" :step="1" placeholder="请输入保质期" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row>
              <el-col :span="12">
                <el-form-item label="批次管理" prop="hasBatch">
                  <el-radio-group v-model="form.hasBatch">
                    <el-radio label="0">否</el-radio>
                    <el-radio label="1">是</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="效期管理" prop="hasExpire">
                  <el-radio-group v-model="form.hasExpire">
                    <el-radio label="0">否</el-radio>
                    <el-radio label="1">是</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
          
          <el-tab-pane label="安全信息" name="safety">
            <el-row>
              <el-col :span="12">
                <el-form-item label="危险等级" prop="hazardLevel">
                  <el-select v-model="form.hazardLevel" placeholder="请选择危险等级" style="width: 100%">
                    <el-option label="无危险" value="0" />
                    <el-option label="低危" value="1" />
                    <el-option label="中危" value="2" />
                    <el-option label="高危" value="3" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="MSDS链接" prop="msdsUrl">
                  <el-input v-model="form.msdsUrl" placeholder="请输入MSDS链接" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row>
              <el-col :span="24">
                <el-form-item label="存储要求" prop="storageReq">
                  <el-input v-model="form.storageReq" type="textarea" placeholder="请输入存储要求" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row>
              <el-col :span="24">
                <el-form-item label="应急措施" prop="emergencyMeasures">
                  <el-input v-model="form.emergencyMeasures" type="textarea" placeholder="请输入应急措施" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
          
          <el-tab-pane label="其他信息" name="other">
            <el-row>
              <el-col :span="12">
                <el-form-item label="供应商信息" prop="supplierInfo">
                  <el-input v-model="form.supplierInfo" placeholder="请输入供应商信息" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="图片链接" prop="imgUrl">
                  <el-input v-model="form.imgUrl" placeholder="请输入图片链接" style="margin-bottom: 8px;" />
                  <el-upload
                    class="avatar-uploader"
                    :action="upload.imageUrl"
                    :headers="upload.headers"
                    :show-file-list="false"
                    :on-success="handleImageSuccess"
                    :before-upload="beforeImageUpload"
                    :on-error="handleImageError"
                  >
                    <img v-if="imageUrl" :src="imageUrl" class="avatar" />
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    <div class="el-upload__tip" slot="tip">只能上传jpg/png文件，且不超过2MB</div>
                  </el-upload>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row>
              <el-col :span="24">
                <el-form-item label="备注" prop="remark">
                  <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    
    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="handleTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 物料详情对话框 -->
    <el-dialog title="物料详情" :visible.sync="viewOpen" width="800px" append-to-body>
      <el-tabs v-model="activeName">
        <el-tab-pane label="基本信息" name="basic">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="物料ID">{{ form.id }}</el-descriptions-item>
            <el-descriptions-item label="物料编码">{{ form.materialCode }}</el-descriptions-item>
            <el-descriptions-item label="物料名称">{{ form.materialName }}</el-descriptions-item>
            <el-descriptions-item label="物料分类">{{ form.categoryName }}</el-descriptions-item>
            <el-descriptions-item label="规格型号">{{ form.specification }}</el-descriptions-item>
            <el-descriptions-item label="单位">{{ form.unit }}</el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="form.status === '0' ? 'success' : 'danger'">
                {{ form.status === '0' ? '启用' : '停用' }}
              </el-tag>
              <span style="margin-left: 8px; font-size: 12px; color: #909399;">
                {{ form.status === '0' ? '可被其他模块调用' : '禁止被其他模块调用' }}
              </span>
            </el-descriptions-item>
            <el-descriptions-item label="条形码">{{ form.barCode }}</el-descriptions-item>
            <el-descriptions-item label="二维码">{{ form.qrCode }}</el-descriptions-item>
            <el-descriptions-item label="版本号">{{ form.version }}</el-descriptions-item>
            <el-descriptions-item label="是否模板">
              <el-tag :type="form.isTemplate === '1' ? 'primary' : 'info'">
                {{ form.isTemplate === '1' ? '是' : '否' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="模板ID">{{ form.templateId }}</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        
        <el-tab-pane label="外部系统信息" name="external">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="主要外部编码">{{ form.externalCode }}</el-descriptions-item>
            <el-descriptions-item label="主要外部系统">{{ form.externalSystem }}</el-descriptions-item>
          </el-descriptions>
          
          <el-divider content-position="left">全部映射关系</el-divider>
          <el-table 
            v-loading="mappingLoading" 
            :data="materialMappings" 
            border 
            style="width: 100%; margin-top: 10px;">
            <el-table-column type="index" label="序号" width="60" align="center" />
            <el-table-column label="外部编码" prop="externalCode" width="120" align="center" :show-overflow-tooltip="true" />
            <el-table-column label="外部系统" prop="externalSystem" width="100" align="center" />
            <el-table-column label="外部系统名称" prop="externalName" width="150" align="center" :show-overflow-tooltip="true" />
            <el-table-column label="是否主要映射" align="center">
              <template slot-scope="scope">
                <el-tag :type="scope.row.isMain === '1' ? 'success' : 'info'" size="mini">
                  {{ scope.row.isMain === '1' ? '是' : '否' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="状态" align="center">
              <template slot-scope="scope">
                <el-tag :type="scope.row.mappingStatus === '0' ? 'success' : 'danger'" size="mini">
                  {{ scope.row.mappingStatus === '0' ? '正常' : '停用' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
          <div class="table-empty-text" v-if="materialMappings.length === 0">暂无映射信息</div>
        </el-tab-pane>
        
        <el-tab-pane label="重量信息" name="weight">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="单位重量">{{ form.unitWeight }}</el-descriptions-item>
            <el-descriptions-item label="重量单位">{{ form.weightUnit }}</el-descriptions-item>
            <el-descriptions-item label="毛重">{{ form.grossWeight }}</el-descriptions-item>
            <el-descriptions-item label="净重">{{ form.netWeight }}</el-descriptions-item>
            <el-descriptions-item label="标准重量">{{ form.standardWeight }}</el-descriptions-item>
            <el-descriptions-item label="重量误差范围(%)">{{ form.weightTolerance }}</el-descriptions-item>
            <el-descriptions-item label="是否重量管控">
              <el-tag :type="form.isWeightControl === '1' ? 'success' : 'info'">
                {{ form.isWeightControl === '1' ? '是' : '否' }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        
        <el-tab-pane label="库存信息" name="inventory">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="最低库存">{{ form.minStock }}</el-descriptions-item>
            <el-descriptions-item label="最高库存">{{ form.maxStock }}</el-descriptions-item>
            <el-descriptions-item label="标准单价">{{ form.standardPrice }}</el-descriptions-item>
            <el-descriptions-item label="是否批次管理">
              <el-tag :type="form.hasBatch === '1' ? 'success' : 'info'">
                {{ form.hasBatch === '1' ? '是' : '否' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="是否效期管理">
              <el-tag :type="form.hasExpire === '1' ? 'success' : 'info'">
                {{ form.hasExpire === '1' ? '是' : '否' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="保质期(天)">{{ form.shelfLife }}</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        
        <el-tab-pane label="安全信息" name="safety">
          <el-descriptions :column="1" border>
            <el-descriptions-item label="危险等级">{{ form.hazardLevel }}</el-descriptions-item>
            <el-descriptions-item label="存储要求">{{ form.storageReq }}</el-descriptions-item>
            <el-descriptions-item label="MSDS链接">
              <el-link :href="form.msdsUrl" target="_blank" type="primary" v-if="form.msdsUrl">查看MSDS</el-link>
              <span v-else>暂无</span>
            </el-descriptions-item>
            <el-descriptions-item label="应急措施">{{ form.emergencyMeasures }}</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        
        <el-tab-pane label="其他信息" name="other">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="供应商信息">{{ form.supplierInfo }}</el-descriptions-item>
            <el-descriptions-item label="图片">
              <el-image 
                v-if="form.imgUrl" 
                :src="form.imgUrl" 
                style="max-width: 100%; max-height: 300px"
                :preview-src-list="[form.imgUrl]">
              </el-image>
              <span v-else>暂无图片</span>
            </el-descriptions-item>
            <el-descriptions-item label="备注">{{ form.remark }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ parseTime(form.createTime) }}</el-descriptions-item>
            <el-descriptions-item label="更新时间">{{ parseTime(form.updateTime) }}</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMaterial, getMaterial, delMaterial, addMaterial, updateMaterial, importTemplate } from "@/api/material/material"
import { changeMaterialStatus } from "@/api/material/material"
import { getCategoryOptions, getCategory } from "@/api/material/category"
import { listMapping } from "@/api/material/mapping"

export default {
  name: "Material",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 显示高级搜索条件
      showAdvanced: false,
      // 当前激活的详情选项卡
      activeName: 'basic',
      // 总条数
      total: 0,
      // 物料信息表格数据
      materialList: [],
      // 物料分类选项
      categoryOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示详情弹出层
      viewOpen: false,
      // 图片预览URL
      imageUrl: "",
      // 导入参数
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + this.$store.getters.token },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/material/material/importData",
        // 图片上传地址
        imageUrl: process.env.VUE_APP_BASE_API + "/common/upload"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        materialCode: null,
        externalCode: null,
        externalSystem: null,
        materialName: null,
        categoryId: null,
        categoryName: null,
        specification: null,
        unit: null,
        unitWeight: null,
        weightUnit: null,
        grossWeight: null,
        netWeight: null,
        standardWeight: null,
        weightTolerance: null,
        isWeightControl: null,
        hazardLevel: null,
        storageReq: null,
        shelfLife: null,
        supplierInfo: null,
        imgUrl: null,
        msdsUrl: null,
        emergencyMeasures: null,
        isTemplate: null,
        version: null,
        templateId: null,
        status: null,
        barCode: null,
        qrCode: null,
        hasBatch: null,
        hasExpire: null,
        minStock: null,
        maxStock: null,
        standardPrice: null,
        approvalStatus: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        materialCode: [
          { required: true, message: "物料编码不能为空", trigger: "blur" }
        ],
        materialName: [
          { required: true, message: "物料名称不能为空", trigger: "blur" }
        ],
        categoryId: [
          { required: true, message: "分类ID不能为空", trigger: "blur" }
        ],
        unit: [
          { required: true, message: "单位不能为空", trigger: "blur" }
        ],
      },
      // 物料映射数据
      materialMappings: [],
      // 映射数据加载状态
      mappingLoading: false,
    }
  },
  created() {
    this.getList()
    this.getCategoryOptions()
  },
  methods: {
    /** 查询物料信息列表 */
    getList() {
      this.loading = true
      listMaterial(this.queryParams).then(response => {
        this.materialList = response.rows
        // 遍历列表，查询每个物料的映射数量
        this.materialList.forEach(item => {
          this.getMappingCount(item)
        })
        this.total = response.total
        this.loading = false
      })
    },
    /** 查询物料分类选项 */
    getCategoryOptions() {
      // 使用新的options接口获取分类选项
      getCategoryOptions().then(response => {
        if (response.code === 200 && response.data) {
          // 手动构建树形结构
          this.categoryOptions = this.buildCategoryTree(response.data)
        } else {
          console.error("获取物料分类列表失败", response)
        }
      }).catch(error => {
        console.error("获取物料分类出错", error)
      })
    },
    /** 构建分类树形结构 */
    buildCategoryTree(categories) {
      // 创建一个映射表
      const categoryMap = {}
      const result = []
      
      // 先把每个分类放入映射表中
      categories.forEach(category => {
        // 确保每个分类都有一个children数组
        category.children = []
        // 添加name属性
        category.name = category.categoryName
        // 将分类放入映射表
        categoryMap[category.id] = category
      })
      
      // 构建树形结构
      categories.forEach(category => {
        // 找到父节点
        const parent = categoryMap[category.parentId]
        if (parent) {
          // 如果有父节点，则把当前分类添加到父节点的children中
          parent.children.push(category)
        } else {
          // 没有父节点，说明是顶层分类，直接放入结果数组
          result.push(category)
        }
      })
      
      return result
    },
    /** 处理分类树形结构 */
    handleCategoryTree(data) {
      if (!Array.isArray(data)) {
        console.error("分类数据不是数组", data)
        return []
      }
      
      return data.map(item => {
        // 复制对象以避免修改原始数据
        const newItem = { ...item }
        
        // 确保子节点存在
        if (newItem.children && newItem.children.length) {
          newItem.children = this.handleCategoryTree(newItem.children)
        } else {
          newItem.children = []
        }
        
        // 添加name属性用于显示
        newItem.name = newItem.categoryName || newItem.name || `类别${newItem.id}`
        
        return newItem
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        materialCode: null,
        externalCode: null,
        externalSystem: null,
        materialName: null,
        categoryId: null,
        categoryName: null,
        specification: null,
        unit: null,
        unitWeight: null,
        weightUnit: null,
        grossWeight: null,
        netWeight: null,
        standardWeight: null,
        weightTolerance: null,
        isWeightControl: "0",
        hazardLevel: null,
        storageReq: null,
        shelfLife: null,
        supplierInfo: null,
        imgUrl: null,
        msdsUrl: null,
        emergencyMeasures: null,
        isTemplate: "0",
        version: 1,
        templateId: null,
        status: "0",
        barCode: null,
        qrCode: null,
        hasBatch: "0",
        hasExpire: "0",
        minStock: null,
        maxStock: null,
        standardPrice: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
        approvalStatus: null,
      }
      this.imageUrl = ""
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    /** 切换高级搜索 */
    toggleAdvancedSearch() {
      this.showAdvanced = !this.showAdvanced
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      // 确保状态默认为正常/启用
      this.form.status = "0" // 0表示正常/启用，1表示停用
      this.open = true
      this.title = "添加物料信息"
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.reset()
      const id = row.id
      getMaterial(id).then(response => {
        this.form = response.data
        this.imageUrl = this.form.imgUrl
        this.viewOpen = true
        this.activeName = 'basic'
        
        // 获取物料的映射信息
        this.getMaterialMappings(id)
      })
    },
    /** 获取物料的映射信息 */
    getMaterialMappings(materialId) {
      this.mappingLoading = true
      // 调用映射列表API，按物料ID过滤
      listMapping({ materialId: materialId }).then(response => {
        this.materialMappings = response.rows || []
        this.mappingLoading = false
      }).catch(error => {
        console.error("获取物料映射信息失败", error)
        this.mappingLoading = false
        this.materialMappings = []
      })
    },
    /** 查看物料映射 */
    handleViewMappings(row) {
      this.reset()
      const id = row.id
      getMaterial(id).then(response => {
        this.form = response.data
        this.viewOpen = true
        this.activeName = 'external'
        
        // 获取物料的映射信息
        this.getMaterialMappings(id)
      })
    },
    /** 获取物料的映射数量 */
    getMappingCount(row) {
      // 调用映射列表API，按物料ID过滤
      listMapping({ materialId: row.id, pageSize: 999 }).then(response => {
        // 将映射数量添加到物料对象中
        this.$set(row, 'mappingCount', response.total)
      }).catch(() => {
        this.$set(row, 'mappingCount', 0)
      })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getMaterial(id).then(response => {
        this.form = response.data
        this.imageUrl = this.form.imgUrl
        this.open = true
        this.title = "修改物料信息"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 确保version是数字类型
          if (this.form.version !== null && this.form.version !== undefined) {
            this.form.version = Number(this.form.version);
          }
          
          if (this.form.id != null) {
            updateMaterial(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addMaterial(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除物料信息编号为"' + ids + '"的数据项？').then(function() {
        return delMaterial(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('material/material/export', {
        ...this.queryParams
      }, `物料信息_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "物料导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    handleTemplate() {
      this.$modal.loading("正在下载模板，请稍候...");
      importTemplate().then(response => {
        const blob = new Blob([response]);
        const fileName = `物料导入模板_${new Date().getTime()}.xlsx`;
        
        if ('download' in document.createElement('a')) {
          // 浏览器支持download属性
          const link = document.createElement('a');
          link.download = fileName;
          link.style.display = 'none';
          link.href = URL.createObjectURL(blob);
          document.body.appendChild(link);
          link.click();
          URL.revokeObjectURL(link.href);
          document.body.removeChild(link);
        } else {
          // IE10+下载
          navigator.msSaveBlob(blob, fileName);
        }
        this.$modal.closeLoading();
      }).catch(error => {
        console.error("下载模板失败", error);
        this.$modal.closeLoading();
        this.$modal.msgError("下载模板失败，请联系管理员");
      });
    },
    /** 文件上传中处理 */
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    /** 文件上传成功处理 */
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    /** 提交上传文件 */
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /** 状态修改 - 控制物料是否可被其他模块调用 */
    handleStatusChange(row) {
      // 获取当前状态
      const currentStatus = row.status;
      // 计算目标状态 - 这里要反转
      const targetStatus = currentStatus === "0" ? "1" : "0";
      const confirmText = targetStatus === "0" ? "启用" : "停用";
      
      // 确认操作，显示更多关于状态变更影响的信息
      const message = targetStatus === "0" 
        ? '确认要启用该物料吗？启用后该物料可被其他模块正常调用。' 
        : '确认要停用该物料吗？停用后该物料将无法被其他模块调用。';
      
      this.$modal.confirm(message).then(() => {
        // 设置加载状态
        this.$set(row, 'statusLoading', true);
        
        // 准备要更新的数据
        const data = {
          id: row.id,
          status: targetStatus
        };
        
        // 使用专用的状态更改API
        changeMaterialStatus(data).then(response => {
          // 取消加载状态
          this.$set(row, 'statusLoading', false);
          
          if (response.code === 200) {
            this.$modal.msgSuccess(confirmText + "成功");
            // 更新UI状态 - 更改状态
            row.status = targetStatus;
          } else {
            this.$modal.msgError(response.msg || (confirmText + "失败"));
            // 状态修改失败，恢复原状态
            row.status = currentStatus;
          }
        }).catch(error => {
          // 取消加载状态
          this.$set(row, 'statusLoading', false);
          console.error("修改状态失败", error);
          this.$modal.msgError(confirmText + "失败");
          // 状态修改失败，恢复原状态
          row.status = currentStatus;
        });
      }).catch(() => {
        // 用户取消操作 - 不改变状态
        row.status = currentStatus;
      });
    },
    /** 图片上传前的校验 */
    beforeImageUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error('上传图片只能是 JPG/PNG 格式!');
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!');
      }
      return isJPG && isLt2M;
    },
    
    /** 图片上传成功处理 */
    handleImageSuccess(res, file) {
      if (res.code === 200) {
        this.imageUrl = res.url || URL.createObjectURL(file.raw);
        this.form.imgUrl = res.url || res.fileName;
        this.$modal.msgSuccess("图片上传成功");
      } else {
        this.$modal.msgError(res.msg || "图片上传失败");
      }
    },
    
    /** 图片上传失败处理 */
    handleImageError() {
      this.$modal.msgError("图片上传失败，请重试");
    },
    
    /** 处理分类选择变更 */
    handleCategoryChange(value) {
      if (!value) {
        this.form.categoryName = null
        return
      }

      // 将value转换为数字类型
      const categoryId = Number(value) || value;
      
      // 根据分类ID查找分类名称
      const findCategoryName = (options, id) => {
        for (let i = 0; i < options.length; i++) {
          if (options[i].id === id || options[i].id === Number(id)) {
            return options[i].name || options[i].categoryName
          }
          if (options[i].children && options[i].children.length) {
            const name = findCategoryName(options[i].children, id)
            if (name) return name
          }
        }
        return null
      }
      
      // 更新分类名称
      this.form.categoryName = findCategoryName(this.categoryOptions, categoryId)
      
      // 如果没有找到分类名称，直接查询API
      if (!this.form.categoryName && categoryId) {
        try {
          getCategory(categoryId).then(response => {
            if (response.code === 200 && response.data) {
              this.form.categoryName = response.data.categoryName
            }
          }).catch(error => {
            console.error("获取分类详情失败", error)
          })
        } catch (error) {
          console.error("获取分类详情异常", error)
        }
      }
    },
  }
}
</script>

<style scoped>
.search-card {
  margin-bottom: 15px;
}
.mt10 {
  margin-top: 10px;
}
.mb8 {
  margin-bottom: 8px;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
.avatar {
  width: 100px;
  height: 100px;
  display: block;
}
.el-form-item-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 3px;
  line-height: 1.4;
}
</style>
