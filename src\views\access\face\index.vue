<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon success">
              <i class="el-icon-user"></i>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ statistics.totalUsers || 0 }}</div>
              <div class="statistics-label">总用户数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon primary">
              <i class="el-icon-picture"></i>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ statistics.registeredUsers || 0 }}</div>
              <div class="statistics-label">已注册人脸</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon warning">
              <i class="el-icon-upload"></i>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ statistics.pendingSync || 0 }}</div>
              <div class="statistics-label">待下发设备</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon danger">
              <i class="el-icon-warning"></i>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ statistics.syncFailed || 0 }}</div>
              <div class="statistics-label">下发失败</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="姓名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工号" prop="employeeNo">
        <el-input
          v-model="queryParams.employeeNo"
          placeholder="请输入工号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="部门" prop="deptName">
        <el-input
          v-model="queryParams.deptName"
          placeholder="请输入部门"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="人脸状态" prop="faceStatus">
        <el-select v-model="queryParams.faceStatus" placeholder="请选择人脸状态" clearable>
          <el-option label="未注册" value="0" />
          <el-option label="已注册" value="1" />
          <el-option label="已下发" value="2" />
          <el-option label="下发失败" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="同步状态" prop="syncStatus">
        <el-select v-model="queryParams.syncStatus" placeholder="请选择同步状态" clearable>
          <el-option label="待同步" value="0" />
          <el-option label="同步中" value="1" />
          <el-option label="同步成功" value="2" />
          <el-option label="同步失败" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-refresh"
          size="mini"
          @click="handleSyncFromInout"
        >从出入库同步</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['access:face:add']"
        >手动添加</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-upload"
          size="mini"
          :disabled="multiple"
          @click="handleBatchSync"
          v-hasPermi="['access:face:sync']"
        >批量下发</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-refresh"
          size="mini"
          @click="handleCheckSyncStatus"
        >检查状态</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['access:face:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          :type="viewMode === 'card' ? 'primary' : 'info'"
          plain
          icon="el-icon-s-grid"
          size="mini"
          @click="viewMode = viewMode === 'table' ? 'card' : 'table'"
        >{{ viewMode === 'table' ? '卡片视图' : '表格视图' }}</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 卡片视图 -->
    <el-row v-if="viewMode === 'card'" :gutter="16" style="margin-top: 20px">
      <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="(item, index) in faceList" :key="index" style="margin-bottom: 16px">
        <el-card :body-style="{ padding: '0px' }" shadow="hover" :class="{'sync-pending': item.syncStatus === '0', 'sync-failed': item.syncStatus === '3'}">
          <el-image
            style="width: 100%; height: 180px"
            :src="item.faceUrl"
            fit="cover"
            :preview-src-list="[item.faceUrl]">
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline" style="font-size: 30px;"></i>
            </div>
          </el-image>
          <div style="padding: 14px">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px">
              <span style="font-weight: bold">{{ item.userName }}</span>
              <div>
                <el-tag :type="getFaceStatusTag(item.faceStatus)" size="mini" style="margin-right: 4px">
                  {{ getFaceStatusText(item.faceStatus) }}
                </el-tag>
                <el-tag :type="getSyncStatusTag(item.syncStatus)" size="mini">
                  {{ getSyncStatusText(item.syncStatus) }}
                </el-tag>
              </div>
            </div>
            <div class="face-info">
              <p><i class="el-icon-user"></i> 工号: {{ item.employeeNo }}</p>
              <p><i class="el-icon-office-building"></i> 部门: {{ item.deptName }}</p>
              <p><i class="el-icon-time"></i> 注册: {{ parseTime(item.registerTime, '{y}-{m}-{d}') }}</p>
              <p><i class="el-icon-date"></i> 更新: {{ parseTime(item.updateTime, '{y}-{m}-{d}') }}</p>
            </div>
            <div style="margin-top: 10px; text-align: right">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-upload"
                @click="handleSyncToDevice(item)"
                v-if="item.syncStatus !== '2'"
              >下发</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(item)"
                v-hasPermi="['access:face:edit']"
              >修改</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(item)"
                v-hasPermi="['access:face:remove']"
              >删除</el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 表格视图 -->
    <el-table v-else v-loading="loading" :data="faceList" @selection-change="handleSelectionChange" row-key="id">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="姓名" align="center" prop="userName" width="100" />
      <el-table-column label="工号" align="center" prop="employeeNo" width="120" />
      <el-table-column label="部门" align="center" prop="deptName" width="120" />
      <el-table-column label="人脸照片" align="center" prop="faceUrl" width="100">
        <template slot-scope="scope">
          <el-image
            style="width: 60px; height: 60px; border-radius: 4px"
            :src="scope.row.faceUrl"
            :preview-src-list="[scope.row.faceUrl]"
            fit="cover">
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
        </template>
      </el-table-column>
      <el-table-column label="人脸状态" align="center" prop="faceStatus" width="100">
        <template slot-scope="scope">
          <el-tag :type="getFaceStatusTag(scope.row.faceStatus)" size="mini">
            {{ getFaceStatusText(scope.row.faceStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="同步状态" align="center" prop="syncStatus" width="100">
        <template slot-scope="scope">
          <el-tag :type="getSyncStatusTag(scope.row.syncStatus)" size="mini">
            {{ getSyncStatusText(scope.row.syncStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="注册时间" align="center" prop="registerTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.registerTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="最后同步" align="center" prop="lastSyncTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.lastSyncTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="同步设备数" align="center" prop="syncDeviceCount" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.syncDeviceCount || 0 }}/{{ scope.row.totalDeviceCount || 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-upload"
            @click="handleSyncToDevice(scope.row)"
            v-if="scope.row.syncStatus !== '2'"
          >下发</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleViewSyncStatus(scope.row)"
          >状态</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['access:face:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['access:face:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改人脸授权对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="姓名" prop="userName">
              <el-input v-model="form.userName" placeholder="请输入姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工号" prop="employeeNo">
              <el-input v-model="form.employeeNo" placeholder="请输入工号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="部门" prop="deptName">
              <el-input v-model="form.deptName" placeholder="请输入部门" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="phoneNumber">
              <el-input v-model="form.phoneNumber" placeholder="请输入手机号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="人脸照片" prop="faceUrl">
          <el-upload
            class="face-uploader"
            action="/upload"
            :show-file-list="false"
            :on-success="handleFaceUploadSuccess"
            :before-upload="beforeFaceUpload">
            <el-image v-if="form.faceUrl" :src="form.faceUrl" class="face-image"></el-image>
            <i v-else class="el-icon-plus face-uploader-icon"></i>
          </el-upload>
          <div class="upload-tip">只能上传jpg/png文件，且不超过2MB</div>
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="人脸状态" prop="faceStatus">
              <el-select v-model="form.faceStatus" placeholder="请选择人脸状态">
                <el-option label="未注册" value="0" />
                <el-option label="已注册" value="1" />
                <el-option label="已下发" value="2" />
                <el-option label="下发失败" value="3" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="同步状态" prop="syncStatus">
              <el-select v-model="form.syncStatus" placeholder="请选择同步状态">
                <el-option label="待同步" value="0" />
                <el-option label="同步中" value="1" />
                <el-option label="同步成功" value="2" />
                <el-option label="同步失败" value="3" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 同步状态详情对话框 -->
    <el-dialog title="同步状态详情" :visible.sync="syncStatusVisible" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="用户姓名">{{ currentSyncRecord.userName }}</el-descriptions-item>
        <el-descriptions-item label="工号">{{ currentSyncRecord.employeeNo }}</el-descriptions-item>
        <el-descriptions-item label="部门">{{ currentSyncRecord.deptName }}</el-descriptions-item>
        <el-descriptions-item label="人脸状态">
          <el-tag :type="getFaceStatusTag(currentSyncRecord.faceStatus)">
            {{ getFaceStatusText(currentSyncRecord.faceStatus) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="同步状态">
          <el-tag :type="getSyncStatusTag(currentSyncRecord.syncStatus)">
            {{ getSyncStatusText(currentSyncRecord.syncStatus) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="最后同步时间">{{ parseTime(currentSyncRecord.lastSyncTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
      </el-descriptions>

      <h4 style="margin-top: 20px;">设备同步详情</h4>
      <el-table :data="currentSyncRecord.deviceSyncDetails || []" style="width: 100%">
        <el-table-column prop="deviceName" label="设备名称" width="150" />
        <el-table-column prop="deviceCode" label="设备编码" width="120" />
        <el-table-column prop="deviceIp" label="设备IP" width="120" />
        <el-table-column prop="syncStatus" label="同步状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getSyncStatusTag(scope.row.syncStatus)" size="mini">
              {{ getSyncStatusText(scope.row.syncStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="syncTime" label="同步时间" width="160">
          <template slot-scope="scope">
            {{ parseTime(scope.row.syncTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
          </template>
        </el-table-column>
        <el-table-column prop="errorMessage" label="错误信息" :show-overflow-tooltip="true" />
      </el-table>

      <div slot="footer" class="dialog-footer">
        <el-button @click="syncStatusVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listFace, getFace, delFace, addFace, updateFace, syncFromInout, syncToDevice, checkSyncStatus, getFaceStatistics } from "@/api/access/face"
import { listDevice } from "@/api/access/device"

export default {
  name: "Face",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 人脸授权表格数据
      faceList: [],
      // 设备列表
      deviceList: [],
      // 统计数据
      statistics: {
        totalUsers: 0,
        registeredUsers: 0,
        pendingSync: 0,
        syncFailed: 0
      },
      // 视图模式
      viewMode: 'table',
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 同步状态对话框
      syncStatusVisible: false,
      currentSyncRecord: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: null,
        employeeNo: null,
        deptName: null,
        faceStatus: null,
        syncStatus: null,
        expiryTime: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        userId: [
          { required: true, message: "关联用户ID不能为空", trigger: "blur" }
        ],
        faceUrl: [
          { required: true, message: "人脸照片URL不能为空", trigger: "blur" }
        ],
        registerTime: [
          { required: true, message: "注册时间不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ]
      }
    }
  },
  created() {
    this.getList()
    this.getDeviceList()
    this.getStatistics()
  },
  methods: {
    /** 查询人脸授权列表 */
    getList() {
      this.loading = true
      listFace(this.queryParams).then(response => {
        this.faceList = response.rows || []
        this.total = response.total || 0
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    /** 获取设备列表 */
    getDeviceList() {
      listDevice().then(response => {
        this.deviceList = response.rows || []
      })
    },
    /** 获取统计数据 */
    getStatistics() {
      // 调用统计接口获取真实数据
      getFaceStatistics().then(response => {
        this.statistics = response.data || {
          totalUsers: 0,
          registeredUsers: 0,
          pendingSync: 0,
          syncFailed: 0
        };
      }).catch(error => {
        console.error('获取统计数据失败:', error);
        this.statistics = {
          totalUsers: 0,
          registeredUsers: 0,
          pendingSync: 0,
          syncFailed: 0
        };
      });
    },
    /** 人脸状态标签类型 */
    getFaceStatusTag(status) {
      switch (status) {
        case '0': return 'info'     // 未注册
        case '1': return 'success'  // 已注册
        case '2': return 'primary'  // 已下发
        case '3': return 'danger'   // 下发失败
        default: return 'info'
      }
    },
    /** 人脸状态文本 */
    getFaceStatusText(status) {
      switch (status) {
        case '0': return '未注册'
        case '1': return '已注册'
        case '2': return '已下发'
        case '3': return '下发失败'
        default: return '未知'
      }
    },
    /** 同步状态标签类型 */
    getSyncStatusTag(status) {
      switch (status) {
        case '0': return 'warning'  // 待同步
        case '1': return 'primary'  // 同步中
        case '2': return 'success'  // 同步成功
        case '3': return 'danger'   // 同步失败
        default: return 'info'
      }
    },
    /** 同步状态文本 */
    getSyncStatusText(status) {
      switch (status) {
        case '0': return '待同步'
        case '1': return '同步中'
        case '2': return '同步成功'
        case '3': return '同步失败'
        default: return '未知'
      }
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userName: null,
        employeeNo: null,
        deptName: null,
        faceUrl: null,
        registerTime: null,
        faceStatus: "0",
        syncStatus: "0"
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 从出入库同步 */
    handleSyncFromInout() {
      this.$modal.confirm('是否确认从出入库申请中同步领料员信息？').then(() => {
        this.loading = true
        return syncFromInout()
      }).then((response) => {
        this.$modal.msgSuccess(`同步成功，共同步 ${response.data.count} 条记录`)
        this.getList()
        this.getStatistics()
      }).catch(() => {
        this.loading = false
      })
    },
    /** 手动添加 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "手动添加人脸授权"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getFace(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改人脸授权"
      })
    },
    /** 批量下发到设备 */
    handleBatchSync() {
      if (this.ids.length === 0) {
        this.$modal.msgError("请选择要下发的用户")
        return
      }
      this.$modal.confirm('是否确认将选中的人脸授权信息下发到所有门禁设备？').then(() => {
        return syncToDevice({ userIds: this.ids })
      }).then((response) => {
        this.$modal.msgSuccess("下发任务已启动，请稍后查看同步状态")
        this.getList()
        this.getStatistics()
      })
    },
    /** 单个用户下发到设备 */
    handleSyncToDevice(row) {
      this.$modal.confirm(`是否确认将用户 ${row.userName} 的人脸授权信息下发到所有门禁设备？`).then(() => {
        return syncToDevice({ userIds: [row.id] })
      }).then((response) => {
        this.$modal.msgSuccess("下发任务已启动，请稍后查看同步状态")
        this.getList()
        this.getStatistics()
      })
    },
    /** 检查同步状态 */
    handleCheckSyncStatus() {
      checkSyncStatus().then(response => {
        this.$modal.msgSuccess("同步状态检查完成")
        this.getList()
        this.getStatistics()
      })
    },
    /** 查看同步状态详情 */
    handleViewSyncStatus(row) {
      this.currentSyncRecord = row
      this.syncStatusVisible = true
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateFace(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addFace(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除人脸授权编号为"' + ids + '"的数据项？').then(function() {
        return delFace(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 人脸照片上传成功 */
    handleFaceUploadSuccess(res, file) {
      this.form.faceUrl = res.url
    },
    /** 人脸照片上传前检查 */
    beforeFaceUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG) {
        this.$modal.msgError('上传头像图片只能是 JPG/PNG 格式!')
      }
      if (!isLt2M) {
        this.$modal.msgError('上传头像图片大小不能超过 2MB!')
      }
      return isJPG && isLt2M
    }
  }
}
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}

.statistics-card {
  cursor: pointer;
  transition: all 0.3s;
}

.statistics-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.statistics-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.statistics-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.statistics-icon.success {
  background: linear-gradient(135deg, #67C23A, #85CE61);
}

.statistics-icon.primary {
  background: linear-gradient(135deg, #409EFF, #66B1FF);
}

.statistics-icon.warning {
  background: linear-gradient(135deg, #E6A23C, #EEBE77);
}

.statistics-icon.danger {
  background: linear-gradient(135deg, #F56C6C, #F78989);
}

.statistics-info {
  flex: 1;
}

.statistics-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.statistics-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #f5f7fa;
}

.face-info {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
}

.face-info p {
  margin: 4px 0;
}

.face-info i {
  margin-right: 5px;
  color: #409EFF;
}

.sync-pending {
  border-left: 4px solid #E6A23C;
}

.sync-failed {
  border-left: 4px solid #F56C6C;
}

.face-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.face-uploader .el-upload:hover {
  border-color: #409EFF;
}

.face-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  line-height: 120px;
  text-align: center;
}

.face-image {
  width: 120px;
  height: 120px;
  display: block;
}

.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}
</style>
