<template>
  <div class="topic-migration-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2><i class="el-icon-refresh"></i> 主题格式迁移</h2>
      <p>将现有的warehouse格式主题迁移为DWMS标准格式</p>
    </div>

    <!-- 迁移状态卡片 -->
    <el-row :gutter="20" class="status-cards">
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon total">
              <i class="el-icon-files"></i>
            </div>
            <div class="status-content">
              <div class="status-number">{{ migrationStats.totalTopics || 0 }}</div>
              <div class="status-label">总主题数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon need-migration">
              <i class="el-icon-warning"></i>
            </div>
            <div class="status-content">
              <div class="status-number">{{ migrationStats.topicsToMigrate || 0 }}</div>
              <div class="status-label">需要迁移</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon success">
              <i class="el-icon-check"></i>
            </div>
            <div class="status-content">
              <div class="status-number">{{ migrationStats.successCount || 0 }}</div>
              <div class="status-label">迁移成功</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon rate">
              <i class="el-icon-pie-chart"></i>
            </div>
            <div class="status-content">
              <div class="status-number">{{ (migrationStats.successRate || 0).toFixed(1) }}%</div>
              <div class="status-label">成功率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 操作按钮 -->
    <el-card class="action-card">
      <div class="action-buttons">
        <el-button 
          type="primary" 
          icon="el-icon-view" 
          @click="previewMigration"
          :loading="previewLoading"
        >
          预览迁移计划
        </el-button>
        
        <el-button 
          type="success" 
          icon="el-icon-refresh" 
          @click="executeMigration"
          :loading="migrationLoading"
          :disabled="!migrationPlan.length"
        >
          执行迁移
        </el-button>
        
        <el-button 
          type="warning" 
          icon="el-icon-refresh-left" 
          @click="showRollbackDialog"
          :disabled="!migrationResults.length"
        >
          回滚迁移
        </el-button>
        
        <el-button 
          type="info" 
          icon="el-icon-download" 
          @click="exportResults"
          :disabled="!migrationResults.length"
        >
          导出结果
        </el-button>
      </div>
    </el-card>

    <!-- 迁移计划表格 -->
    <el-card v-if="migrationPlan.length" class="plan-card">
      <div slot="header">
        <span>迁移计划 ({{ migrationPlan.length }}个主题)</span>
        <div style="float: right;">
          <el-tag :type="getFormatTagType('WAREHOUSE')" size="small">Warehouse格式</el-tag>
          <i class="el-icon-right" style="margin: 0 10px;"></i>
          <el-tag :type="getFormatTagType('DWMS')" size="small">DWMS格式</el-tag>
        </div>
      </div>
      
      <el-table :data="migrationPlan" style="width: 100%" max-height="400">
        <el-table-column prop="originalTopic" label="原始主题" min-width="200">
          <template slot-scope="scope">
            <span class="topic-text original">{{ scope.row.originalTopic }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="detectedFormat" label="检测格式" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getFormatTagType(scope.row.detectedFormat)" size="mini">
              {{ scope.row.detectedFormat }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="standardizedTopic" label="标准化主题" min-width="200">
          <template slot-scope="scope">
            <span class="topic-text standardized">{{ scope.row.standardizedTopic }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="module" label="模块" width="100" />
        <el-table-column prop="function" label="功能" width="100" />
        
        <el-table-column prop="priority" label="优先级" width="80" align="center">
          <template slot-scope="scope">
            <el-tag :type="getPriorityTagType(scope.row.priority)" size="mini">
              {{ scope.row.priority }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="recommendedQos" label="推荐QoS" width="80" align="center">
          <template slot-scope="scope">
            <el-tag :type="getQosTagType(scope.row.recommendedQos)" size="mini">
              {{ scope.row.recommendedQos }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="source" label="来源" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.source === 'subscription' ? 'warning' : 'info'" size="mini">
              {{ scope.row.source === 'subscription' ? '订阅' : '主题' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 迁移结果表格 -->
    <el-card v-if="migrationResults.length" class="results-card">
      <div slot="header">
        <span>迁移结果 ({{ migrationResults.length }}个主题)</span>
        <div style="float: right;">
          <el-tag type="success" size="small">成功: {{ migrationStats.successCount }}</el-tag>
          <el-tag type="danger" size="small">失败: {{ migrationStats.failureCount }}</el-tag>
        </div>
      </div>
      
      <el-table :data="migrationResults" style="width: 100%" max-height="400">
        <el-table-column prop="success" label="状态" width="80" align="center">
          <template slot-scope="scope">
            <i v-if="scope.row.success" class="el-icon-check" style="color: #67c23a; font-size: 16px;"></i>
            <i v-else class="el-icon-close" style="color: #f56c6c; font-size: 16px;"></i>
          </template>
        </el-table-column>
        
        <el-table-column prop="originalTopic" label="原始主题" min-width="200">
          <template slot-scope="scope">
            <span class="topic-text">{{ scope.row.originalTopic }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="standardizedTopic" label="标准化主题" min-width="200">
          <template slot-scope="scope">
            <span class="topic-text standardized">{{ scope.row.standardizedTopic || '-' }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="detectedFormat" label="格式" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getFormatTagType(scope.row.detectedFormat)" size="mini">
              {{ scope.row.detectedFormat }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="新客户端ID" min-width="150">
          <template slot-scope="scope">
            <span v-if="scope.row.subscribeResult && scope.row.subscribeResult.newClientId">
              {{ scope.row.subscribeResult.newClientId }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="error" label="错误信息" min-width="200">
          <template slot-scope="scope">
            <span v-if="scope.row.error" class="error-text">{{ scope.row.error }}</span>
            <span v-else class="success-text">成功</span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 回滚对话框 -->
    <el-dialog
      title="回滚迁移"
      :visible.sync="rollbackDialogVisible"
      width="600px"
    >
      <div class="rollback-content">
        <el-alert
          title="警告"
          type="warning"
          description="回滚操作将取消迁移过程中创建的订阅，请谨慎操作！"
          show-icon
          :closable="false"
        />
        
        <div class="rollback-selection">
          <h4>选择要回滚的客户端:</h4>
          <el-checkbox-group v-model="selectedClientIds">
            <el-checkbox
              v-for="result in migrationResults.filter(r => r.success && r.subscribeResult)"
              :key="result.subscribeResult.newClientId"
              :label="result.subscribeResult.newClientId"
            >
              {{ result.subscribeResult.newClientId }} ({{ result.originalTopic }})
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
      
      <div slot="footer">
        <el-button @click="rollbackDialogVisible = false">取消</el-button>
        <el-button 
          type="danger" 
          @click="executeRollback"
          :loading="rollbackLoading"
          :disabled="!selectedClientIds.length"
        >
          确认回滚
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { previewMigrationPlan, executeMigration, rollbackMigration } from '@/api/emqx'

export default {
  name: 'TopicMigration',
  data() {
    return {
      migrationPlan: [],
      migrationResults: [],
      migrationStats: {},
      previewLoading: false,
      migrationLoading: false,
      rollbackLoading: false,
      rollbackDialogVisible: false,
      selectedClientIds: []
    }
  },
  
  created() {
    this.previewMigration()
  },
  
  methods: {
    // 预览迁移计划
    async previewMigration() {
      this.previewLoading = true
      try {
        const response = await previewMigrationPlan()
        if (response.success) {
          this.migrationPlan = response.migrationPlan || []
          this.migrationStats = {
            totalTopics: response.totalTopics,
            topicsToMigrate: response.topicsToMigrate
          }
          this.$message.success(`发现${response.topicsToMigrate}个主题需要迁移`)
        } else {
          this.$message.error('预览迁移计划失败: ' + response.error)
        }
      } catch (error) {
        this.$message.error('预览迁移计划失败: ' + error.message)
      } finally {
        this.previewLoading = false
      }
    },
    
    // 执行迁移
    async executeMigration() {
      this.$confirm('确认执行主题格式迁移？此操作将创建新的标准化主题订阅。', '确认迁移', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.migrationLoading = true
        try {
          const response = await executeMigration()
          if (response.success) {
            this.migrationResults = response.migrationResults || []
            this.migrationStats = {
              ...this.migrationStats,
              ...response.statistics
            }
            this.$message.success(`迁移完成！成功迁移${response.statistics.successCount}个主题`)
          } else {
            this.$message.error('执行迁移失败: ' + response.error)
          }
        } catch (error) {
          this.$message.error('执行迁移失败: ' + error.message)
        } finally {
          this.migrationLoading = false
        }
      })
    },
    
    // 显示回滚对话框
    showRollbackDialog() {
      this.selectedClientIds = []
      this.rollbackDialogVisible = true
    },
    
    // 执行回滚
    async executeRollback() {
      this.rollbackLoading = true
      try {
        const response = await rollbackMigration(this.selectedClientIds)
        if (response.success) {
          this.$message.success(`回滚完成！处理了${this.selectedClientIds.length}个客户端`)
          this.rollbackDialogVisible = false
          // 刷新数据
          this.previewMigration()
        } else {
          this.$message.error('回滚失败: ' + response.error)
        }
      } catch (error) {
        this.$message.error('回滚失败: ' + error.message)
      } finally {
        this.rollbackLoading = false
      }
    },
    
    // 导出结果
    exportResults() {
      const data = {
        migrationPlan: this.migrationPlan,
        migrationResults: this.migrationResults,
        statistics: this.migrationStats,
        timestamp: new Date().toISOString()
      }
      
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `topic_migration_results_${new Date().getTime()}.json`
      link.click()
      window.URL.revokeObjectURL(url)
      this.$message.success('迁移结果已导出')
    },
    
    // 获取格式标签类型
    getFormatTagType(format) {
      const types = {
        'DWMS': 'success',
        'WAREHOUSE': 'warning',
        'LEGACY': 'info',
        'CUSTOM': 'danger'
      }
      return types[format] || ''
    },
    
    // 获取优先级标签类型
    getPriorityTagType(priority) {
      if (priority <= 1) return 'danger'
      if (priority <= 2) return 'warning'
      if (priority <= 3) return 'primary'
      return 'info'
    },
    
    // 获取QoS标签类型
    getQosTagType(qos) {
      if (qos === 2) return 'danger'
      if (qos === 1) return 'warning'
      return 'success'
    }
  }
}
</script>

<style scoped>
.topic-migration-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.page-header p {
  margin: 5px 0 0 0;
  color: #909399;
  font-size: 14px;
}

.status-cards {
  margin-bottom: 20px;
}

.status-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.status-item {
  display: flex;
  align-items: center;
  padding: 10px;
}

.status-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 20px;
  color: white;
}

.status-icon.total {
  background: #409eff;
}

.status-icon.need-migration {
  background: #e6a23c;
}

.status-icon.success {
  background: #67c23a;
}

.status-icon.rate {
  background: #909399;
}

.status-content {
  flex: 1;
}

.status-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.status-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.action-card, .plan-card, .results-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.action-buttons {
  text-align: center;
  padding: 20px;
}

.action-buttons .el-button {
  margin: 0 10px;
}

.topic-text {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.topic-text.original {
  color: #e6a23c;
}

.topic-text.standardized {
  color: #67c23a;
}

.error-text {
  color: #f56c6c;
  font-size: 12px;
}

.success-text {
  color: #67c23a;
  font-size: 12px;
}

.rollback-content {
  padding: 20px 0;
}

.rollback-selection {
  margin-top: 20px;
}

.rollback-selection h4 {
  margin-bottom: 15px;
  color: #303133;
}

.rollback-selection .el-checkbox {
  display: block;
  margin-bottom: 10px;
}
</style>
