<template>
  <div class="app-container">
    <!-- EMQX连接状态卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-content">
            <div class="status-icon">
              <i class="el-icon-connection" :style="{ color: connectionStatus.color }"></i>
            </div>
            <div class="status-info">
              <div class="status-text">{{ connectionStatus.text }}</div>
              <div class="status-label">连接状态</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <i class="el-icon-monitor" style="color: #409EFF;"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ mqttStats.connectedClients || 0 }}</div>
              <div class="stats-label">在线设备</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <i class="el-icon-message" style="color: #67C23A;"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ mqttStats.messagesReceived || 0 }}</div>
              <div class="stats-label">消息接收</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <i class="el-icon-upload" style="color: #E6A23C;"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ mqttStats.messagesSent || 0 }}</div>
              <div class="stats-label">消息发送</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- EMQX配置表单 -->
    <el-card>
      <div slot="header">
        <span>EMQX服务器配置</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="testConnection">测试连接</el-button>
      </div>
      
      <el-form ref="configForm" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="服务器地址" prop="serverHost">
              <el-input v-model="form.serverHost" placeholder="请输入EMQX服务器地址" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务器端口" prop="serverPort">
              <el-input-number v-model="form.serverPort" :min="1" :max="65535" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户名" prop="username">
              <el-input v-model="form.username" placeholder="请输入用户名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="密码" prop="password">
              <el-input v-model="form.password" type="password" placeholder="请输入密码" show-password />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户端ID" prop="clientId">
              <el-input v-model="form.clientId" placeholder="请输入客户端ID" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="保持连接" prop="keepAlive">
              <el-input-number v-model="form.keepAlive" :min="10" :max="3600" style="width: 100%" />
              <span style="margin-left: 10px; color: #999;">秒</span>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="清除会话">
              <el-switch v-model="form.cleanSession" active-value="1" inactive-value="0"></el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="自动重连">
              <el-switch v-model="form.autoReconnect" active-value="1" inactive-value="0"></el-switch>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="主题配置">
          <el-input
            v-model="form.topics"
            type="textarea"
            :rows="3"
            placeholder="请输入订阅主题，多个主题用换行分隔"
          />
        </el-form-item>
        
        <el-form-item label="描述">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="2"
            placeholder="请输入配置描述"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="saveConfig" :loading="saving">保存配置</el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button type="success" @click="connectMqtt" :loading="connecting">连接MQTT</el-button>
          <el-button type="warning" @click="disconnectMqtt" v-if="isConnected">断开连接</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 消息监控 -->
    <el-card class="mt20">
      <div slot="header">
        <span>消息监控</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="clearMessages">清空消息</el-button>
      </div>
      
      <el-table :data="messages" border height="300" style="width: 100%">
        <el-table-column prop="timestamp" label="时间" width="180" />
        <el-table-column prop="topic" label="主题" width="200" />
        <el-table-column prop="payload" label="消息内容" />
        <el-table-column prop="qos" label="QoS" width="80" />
        <el-table-column label="类型" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.type === 'received' ? 'success' : 'primary'">
              {{ scope.row.type === 'received' ? '接收' : '发送' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
export default {
  name: "OutmisEmqx",
  data() {
    return {
      // 加载状态
      saving: false,
      connecting: false,
      // 连接状态
      isConnected: false,
      // 表单数据
      form: {
        serverHost: 'localhost',
        serverPort: 1883,
        username: '',
        password: '',
        clientId: 'outmis_client_' + Date.now(),
        keepAlive: 60,
        cleanSession: '1',
        autoReconnect: '1',
        topics: 'outmis/device/+/data\noutmis/device/+/status',
        description: '出库防错EMQX配置'
      },
      // 表单验证规则
      rules: {
        serverHost: [
          { required: true, message: '请输入服务器地址', trigger: 'blur' }
        ],
        serverPort: [
          { required: true, message: '请输入服务器端口', trigger: 'blur' }
        ],
        clientId: [
          { required: true, message: '请输入客户端ID', trigger: 'blur' }
        ]
      },
      // MQTT统计数据
      mqttStats: {
        connectedClients: 0,
        messagesReceived: 0,
        messagesSent: 0
      },
      // 消息列表
      messages: []
    };
  },
  computed: {
    connectionStatus() {
      if (this.isConnected) {
        return {
          text: '已连接',
          color: '#67C23A'
        };
      } else {
        return {
          text: '未连接',
          color: '#F56C6C'
        };
      }
    }
  },
  created() {
    this.loadConfig();
    this.loadMqttStats();
  },
  methods: {
    /** 加载配置 */
    loadConfig() {
      // 模拟加载配置数据
      // 实际应该调用API获取配置
    },
    
    /** 加载MQTT统计数据 */
    loadMqttStats() {
      // 模拟统计数据
      this.mqttStats = {
        connectedClients: 5,
        messagesReceived: 1234,
        messagesSent: 567
      };
    },
    
    /** 保存配置 */
    saveConfig() {
      this.$refs.configForm.validate((valid) => {
        if (valid) {
          this.saving = true;
          // 模拟保存操作
          setTimeout(() => {
            this.saving = false;
            this.$modal.msgSuccess("配置保存成功");
          }, 1000);
        }
      });
    },
    
    /** 重置表单 */
    resetForm() {
      this.$refs.configForm.resetFields();
    },
    
    /** 测试连接 */
    testConnection() {
      this.$modal.msgSuccess("连接测试成功");
    },
    
    /** 连接MQTT */
    connectMqtt() {
      this.connecting = true;
      // 模拟连接操作
      setTimeout(() => {
        this.connecting = false;
        this.isConnected = true;
        this.$modal.msgSuccess("MQTT连接成功");
        this.startMessageSimulation();
      }, 2000);
    },
    
    /** 断开MQTT连接 */
    disconnectMqtt() {
      this.isConnected = false;
      this.$modal.msgSuccess("MQTT连接已断开");
    },
    
    /** 清空消息 */
    clearMessages() {
      this.messages = [];
      this.$modal.msgSuccess("消息已清空");
    },
    
    /** 模拟消息接收 */
    startMessageSimulation() {
      if (this.isConnected) {
        // 模拟接收消息
        const message = {
          timestamp: new Date().toLocaleString(),
          topic: 'outmis/device/WS001/data',
          payload: JSON.stringify({
            deviceId: 'WS001',
            weight: 125.6,
            status: 'normal',
            timestamp: Date.now()
          }),
          qos: 1,
          type: 'received'
        };
        
        this.messages.unshift(message);
        
        // 限制消息数量
        if (this.messages.length > 50) {
          this.messages = this.messages.slice(0, 50);
        }
        
        // 继续模拟
        setTimeout(() => {
          this.startMessageSimulation();
        }, 5000);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.status-card, .stats-card {
  .status-content, .stats-content {
    display: flex;
    align-items: center;
    
    .status-icon, .stats-icon {
      font-size: 40px;
      margin-right: 20px;
    }
    
    .status-info, .stats-info {
      flex: 1;
      
      .status-text, .stats-number {
        font-size: 24px;
        font-weight: bold;
        color: #303133;
        line-height: 1;
      }
      
      .status-label, .stats-label {
        font-size: 14px;
        color: #909399;
        margin-top: 8px;
      }
    }
  }
}

.mb20 {
  margin-bottom: 20px;
}

.mt20 {
  margin-top: 20px;
}
</style>
