import request from '@/utils/request'

// 获取系统性能指标
export function getSystemMetrics() {
  return request({
    url: '/video/performance/system/metrics',
    method: 'get'
  })
}

// 获取JVM性能指标
export function getJvmMetrics() {
  return request({
    url: '/video/performance/jvm/metrics',
    method: 'get'
  })
}

// 获取数据库性能指标
export function getDatabaseMetrics() {
  return request({
    url: '/video/performance/database/metrics',
    method: 'get'
  })
}

// 获取网络性能指标
export function getNetworkMetrics() {
  return request({
    url: '/video/performance/network/metrics',
    method: 'get'
  })
}

// 获取视频流性能指标
export function getVideoStreamMetrics() {
  return request({
    url: '/video/performance/video-stream/metrics',
    method: 'get'
  })
}

// 获取存储性能指标
export function getStorageMetrics() {
  return request({
    url: '/video/performance/storage/metrics',
    method: 'get'
  })
}

// 获取性能趋势数据
export function getPerformanceTrend(params) {
  return request({
    url: '/video/performance/trend',
    method: 'get',
    params: params
  })
}

// 获取性能告警列表
export function getPerformanceAlarms(params) {
  return request({
    url: '/video/performance/alarms',
    method: 'get',
    params: params
  })
}

// 获取优化建议
export function getOptimizationSuggestions() {
  return request({
    url: '/video/performance/optimization/suggestions',
    method: 'get'
  })
}

// 执行性能优化
export function executeOptimization(data) {
  return request({
    url: '/video/performance/optimization/execute',
    method: 'post',
    data: data
  })
}

// 导出性能数据
export function exportPerformanceData(params) {
  return request({
    url: '/video/performance/data/export',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}
