<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="物料名称" prop="materialName">
        <el-input
          v-model="queryParams.materialName"
          placeholder="请输入物料名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="审批状态" prop="approvalStatus">
        <el-select v-model="queryParams.approvalStatus" placeholder="请选择审批状态" clearable size="small">
          <el-option label="待验收" value="0" />
          <el-option label="验收通过" value="1" />
          <el-option label="驳回" value="2" />
          <el-option label="审批中" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-s-promotion"
          size="mini"
          :disabled="single"
          @click="handleSubmit"
          v-hasPermi="['material:approval:submit']"
        >提交审批</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-check"
          size="mini"
          :disabled="single"
          @click="handleApprove"
          v-hasPermi="['material:approval:approve']"
        >审批通过</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-close"
          size="mini"
          :disabled="single"
          @click="handleReject"
          v-hasPermi="['material:approval:reject']"
        >审批驳回</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-back"
          size="mini"
          :disabled="single"
          @click="handleCancel"
          v-hasPermi="['material:approval:cancel']"
        >撤销审批</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="materialList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="编号" align="center" prop="id" width="80" />
      <el-table-column label="清单编号" align="center" prop="billCode" width="150" />
      <el-table-column label="物料名称" align="center" prop="materialName" :show-overflow-tooltip="true" width="200" />
      <el-table-column label="规格型号" align="center" prop="specification" :show-overflow-tooltip="true" width="180" />
      <el-table-column label="数量" align="center" prop="quantity" width="100" />
      <el-table-column label="单位" align="center" prop="unit" width="80" />
      <el-table-column label="审批状态" align="center" prop="approvalStatus" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.material_approval_status" :value="scope.row.approvalStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="审批人" align="center" prop="approvalBy" width="100" />
      <el-table-column label="审批时间" align="center" prop="approvalTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.approvalTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
          >详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 审批详情对话框 -->
    <el-dialog title="物料审批详情" :visible.sync="detailOpen" width="700px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="物料名称">{{ form.materialName }}</el-descriptions-item>
        <el-descriptions-item label="规格型号">{{ form.specification }}</el-descriptions-item>
        <el-descriptions-item label="数量">{{ form.quantity }} {{ form.unit }}</el-descriptions-item>
        <el-descriptions-item label="审批状态">
          <dict-tag :options="dict.type.material_approval_status" :value="form.approvalStatus"/>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 审批流程展示 -->
      <approval-flow
        :approval-status="form.approvalStatus"
        :approval-by="form.approvalBy"
        :approval-time="form.approvalTime"
        :approval-remark="form.approvalRemark"
        :show-actions="true"
        :has-approval-permission="checkApprovePermission()"
        @approve="onApprove"
        @reject="onReject"
        @cancel="onCancel"
      />
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 提交审批对话框 -->
    <el-dialog title="提交审批申请" :visible.sync="submitDialogVisible" width="600px" append-to-body>
      <el-form :model="submitForm" :rules="submitRules" ref="submitForm" label-width="100px">
        <el-form-item label="业务类型" prop="businessType">
          <el-select v-model="submitForm.businessType" placeholder="请选择业务类型" style="width: 100%">
            <el-option v-for="item in businessTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="流程编码" prop="workflowCode">
          <el-select v-model="submitForm.workflowCode" placeholder="请选择审批流程" style="width: 100%">
            <el-option 
              v-for="item in workflowOptions" 
              :key="item.workflowId" 
              :label="item.workflowName" 
              :value="item.workflowCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="审批标题" prop="businessTitle">
          <el-input v-model="submitForm.businessTitle" placeholder="请输入审批标题"></el-input>
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-radio-group v-model="submitForm.priority">
            <el-radio label="1">低</el-radio>
            <el-radio label="2">中</el-radio>
            <el-radio label="3">高</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审批说明" prop="remark">
          <el-input type="textarea" v-model="submitForm.remark" placeholder="请输入审批说明" :rows="3"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="submitDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmSubmit">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 驳回对话框 -->
    <el-dialog title="审批驳回" :visible.sync="rejectOpen" width="500px" append-to-body>
      <el-form :model="rejectForm" :rules="rejectRules" ref="rejectForm" label-width="80px">
        <el-form-item label="驳回原因" prop="reason">
          <el-input v-model="rejectForm.reason" type="textarea" :rows="3" placeholder="请输入驳回原因"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitReject">确 定</el-button>
        <el-button @click="rejectOpen = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// 物料清单API已删除，如需要可以使用物料API替代
import { submitApproval, submitApprovalEx, approveApproval, rejectApproval, cancelApproval } from "@/api/material/approval";
import ApprovalFlow from "@/components/RuiYun/Approval/ApprovalFlow";
import { getWorkflowsByBusinessType, getAvailableWorkflows, getWorkflowOptions } from "@/api/approval/workflow";

export default {
  name: "MaterialApproval",
  components: {
    ApprovalFlow
  },
  dicts: ['material_approval_status'],
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 选中物料对象
      selectedItem: null,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 物料表格数据
      materialList: [],
      // 详情窗口
      detailOpen: false,
      // 驳回窗口
      rejectOpen: false,
      // 表单参数
      form: {},
      // 驳回表单
      rejectForm: {
        reason: ""
      },
      // 驳回表单校验
      rejectRules: {
        reason: [
          { required: true, message: "请输入驳回原因", trigger: "blur" }
        ]
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        materialName: null,
        approvalStatus: null
      },
      // 提交表单
      submitForm: {
        businessId: null,
        businessType: "MATERIAL_APPROVAL",
        workflowCode: "",
        businessTitle: "",
        priority: "1",
        remark: "",
        businessData: {},
        applyTime: new Date(),
        applyDeptName: "",
        applyDeptId: "",
        materialCode: "",
        materialName: "",
        specification: "",
        quantity: "",
        unit: ""
      },
      // 提交表单校验
      submitRules: {
        businessType: [
          { required: true, message: "请选择业务类型", trigger: "change" }
        ],
        workflowCode: [
          { required: true, message: "请选择审批流程", trigger: "change" }
        ],
        businessTitle: [
          { required: true, message: "请输入审批标题", trigger: "blur" }
        ],
        priority: [
          { required: true, message: "请选择优先级", trigger: "change" }
        ]
      },
      // 审批流程选项
      workflowOptions: [],
      // 业务类型选项
      businessTypeOptions: [],
      // 提交对话框可见性
      submitDialogVisible: false
    };
  },
  created() {
    this.getList();
    this.getWorkflowOptions();
    this.getBusinessTypeOptions();
  },
  methods: {
    /** 查询物料列表 */
    getList() {
      this.loading = true;
      listMaterialBillItem(this.queryParams).then(response => {
        this.materialList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.detailOpen = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        billId: null,
        billCode: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        specification: null,
        unit: null,
        quantity: null,
        approvalStatus: "0",
        approvalBy: null,
        approvalTime: null,
        approvalRemark: null
      };
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.selectedItem = selection.length ? selection[0] : null;
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      this.reset();
      this.detailOpen = true;
      this.form = Object.assign({}, row);
    },
    /** 检查是否有审批权限 */
    checkApprovePermission() {
      return this.$store.getters.permissions.includes("material:approval:approve");
    },
    /** 提交审批 */
    handleSubmit() {
      if (this.single) return;
      
      // 获取当前用户信息
      const userInfo = this.$store.getters.userInfo || {};
      
      // 打开提交审批对话框
      this.submitForm.businessId = this.selectedItem.id.toString();
      this.submitForm.businessTitle = `物料审批-${this.selectedItem.materialName}`;
      
      // 填充物料业务数据
      this.submitForm.materialCode = this.selectedItem.materialCode || '';
      this.submitForm.materialName = this.selectedItem.materialName || '';
      this.submitForm.specification = this.selectedItem.specification || '';
      this.submitForm.quantity = this.selectedItem.quantity || '';
      this.submitForm.unit = this.selectedItem.unit || '';
      
      // 用户部门信息
      this.submitForm.applyDeptName = userInfo.dept ? userInfo.dept.deptName : '';
      this.submitForm.applyDeptId = userInfo.dept ? userInfo.dept.deptId : '';
      
      // 构建业务数据
      this.submitForm.businessData = {
        billId: this.selectedItem.billId,
        billCode: this.selectedItem.billCode,
        materialId: this.selectedItem.materialId,
        materialCode: this.selectedItem.materialCode,
        materialName: this.selectedItem.materialName,
        specification: this.selectedItem.specification,
        quantity: this.selectedItem.quantity,
        unit: this.selectedItem.unit
      };
      
      // 构建默认备注
      this.submitForm.remark = `物料编码: ${this.selectedItem.materialCode || '无'}, 规格: ${this.selectedItem.specification || '无'}, 数量: ${this.selectedItem.quantity || '0'} ${this.selectedItem.unit || ''}`;
      
      this.submitDialogVisible = true;
    },
    // 获取审批流程选项
    getWorkflowOptions() {
      // 使用新增的API获取可用的审批流程
      getAvailableWorkflows("MATERIAL_APPROVAL").then(response => {
        if (response.code === 200 && response.data) {
          this.workflowOptions = response.data || [];
          
          // 如果有可用的流程，默认选择第一个
          if (this.workflowOptions.length > 0) {
            this.submitForm.workflowCode = this.workflowOptions[0].workflowCode;
          } else {
            // 如果没有可用流程，显示提示
            this.$message.warning("未找到可用的物料审批流程定义，请联系管理员配置");
          }
        }
      }).catch(error => {
        console.error("获取审批流程失败:", error);
        this.$message.error("获取审批流程失败，请联系管理员");
      });
    },
    // 确认提交审批
    confirmSubmit() {
      this.$refs["submitForm"].validate(valid => {
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '正在提交审批...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          
          // 构建提交参数
          const submitParams = {
            businessId: this.submitForm.businessId,
            businessType: this.submitForm.businessType,
            businessTitle: this.submitForm.businessTitle,
            workflowCode: this.submitForm.workflowCode,
            priority: this.submitForm.priority,
            remark: this.submitForm.remark,
            businessData: JSON.stringify(this.submitForm.businessData),
            applyTime: this.submitForm.applyTime,
            applyDeptName: this.submitForm.applyDeptName,
            applyDeptId: this.submitForm.applyDeptId
          };
          
          // 调用增强版审批接口
          submitApprovalEx(submitParams).then(response => {
            loading.close();
            if (response.code === 200) {
              this.$modal.msgSuccess("审批提交成功");
              this.submitDialogVisible = false;
              this.getList();
            } else {
              this.$modal.msgError(response.msg || "提交失败");
            }
          }).catch(error => {
            loading.close();
            console.error("审批提交失败:", error);
            this.$modal.msgError("提交失败: " + (error.message || error));
          });
        }
      });
    },
    /** 审批通过 */
    handleApprove() {
      if (this.single) return;
      const id = this.selectedItem.id;
      
      if (this.selectedItem.approvalStatus !== "3") {
        this.$modal.msgError("该物料当前状态不允许审批通过");
        return;
      }
      
      this.$confirm('确认通过该物料的审批申请?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        approveApproval(id).then(response => {
          this.$modal.msgSuccess("审批通过成功");
          this.getList();
        });
      });
    },
    /** 审批驳回 */
    handleReject() {
      if (this.single) return;
      const id = this.selectedItem.id;
      
      if (this.selectedItem.approvalStatus !== "3") {
        this.$modal.msgError("该物料当前状态不允许审批驳回");
        return;
      }
      
      this.rejectForm.reason = "";
      this.rejectOpen = true;
    },
    /** 提交驳回 */
    submitReject() {
      this.$refs["rejectForm"].validate(valid => {
        if (valid) {
          const id = this.selectedItem.id;
          rejectApproval(id, this.rejectForm.reason).then(response => {
            this.$modal.msgSuccess("审批驳回成功");
            this.rejectOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 撤销审批 */
    handleCancel() {
      if (this.single) return;
      const id = this.selectedItem.id;
      
      if (this.selectedItem.approvalStatus !== "3") {
        this.$modal.msgError("该物料当前状态不允许撤销审批");
        return;
      }
      
      this.$confirm('确认撤销该物料的审批申请?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        cancelApproval(id).then(response => {
          this.$modal.msgSuccess("撤销成功");
          this.getList();
        });
      });
    },
    // 详情页面的审批操作
    onApprove() {
      approveApproval(this.form.id).then(response => {
        this.$modal.msgSuccess("审批通过成功");
        this.detailOpen = false;
        this.getList();
      });
    },
    onReject(reason) {
      rejectApproval(this.form.id, reason).then(response => {
        this.$modal.msgSuccess("审批驳回成功");
        this.detailOpen = false;
        this.getList();
      });
    },
    onCancel() {
      cancelApproval(this.form.id).then(response => {
        this.$modal.msgSuccess("撤销成功");
        this.detailOpen = false;
        this.getList();
      });
    },

    /** 获取业务类型选项 */
    getBusinessTypeOptions() {
      getWorkflowOptions().then(res => {
        if (res.code === 200 && res.data) {
          // 使用Map对象确保每个业务类型只出现一次
          const businessTypeMap = new Map();
          res.data.forEach(item => {
            if (!businessTypeMap.has(item.businessType)) {
              businessTypeMap.set(item.businessType, {
                value: item.businessType,
                label: item.businessTypeName || item.businessType
              });
            }
          });

          // 将Map转换回数组
          this.businessTypeOptions = Array.from(businessTypeMap.values());

          // 如果当前没有选择业务类型，自动选择物料相关的业务类型
          if (!this.submitForm.businessType && this.businessTypeOptions.length > 0) {
            // 优先查找物料审批相关的业务类型
            let materialType = this.businessTypeOptions.find(item =>
              item.value === 'MATERIAL_APPROVAL' ||
              item.value === 'MATERIAL_BILL' ||
              item.value === 'MATERIAL_APPLY'
            );

            // 如果没找到，则查找包含"物料"或"审批"关键词的
            if (!materialType) {
              materialType = this.businessTypeOptions.find(item =>
                item.label.includes('物料') ||
                item.label.includes('审批')
              );
            }

            // 如果还没找到，使用第一个选项
            if (!materialType) {
              materialType = this.businessTypeOptions[0];
            }

            this.submitForm.businessType = materialType.value;
          }
        }
      });
    }
  }
};
</script> 