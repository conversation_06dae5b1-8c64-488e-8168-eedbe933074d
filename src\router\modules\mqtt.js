import Layout from '@/layout'

const mqttRouter = {
  path: '/mqtt',
  component: Layout,
  redirect: '/mqtt/dashboard',
  name: 'Mqtt',
  meta: {
    title: 'MQTT Broker',
    icon: 'message',
    roles: ['admin', 'mqtt_admin'],
    menuId: 2600  // 对应数据库菜单ID
  },
  children: [
    {
      path: 'dashboard',
      component: () => import('@/views/mqtt/dashboard/index'),
      name: 'MqttDashboard',
      meta: {
        title: '系统概览',
        icon: 'dashboard',
        affix: true,
        noCache: false,
        menuId: 2601,
        perms: ['mqtt:dashboard:view']
      }
    },
    {
      path: 'clients',
      component: () => import('@/views/mqtt/clients/index'),
      name: 'MqttClients',
      meta: {
        title: '客户端管理',
        icon: 'connection',
        menuId: 2602,
        perms: ['mqtt:client:list']
      }
    },
    {
      path: 'messages',
      component: () => import('@/views/mqtt/messages/index'),
      name: 'MqttMessages',
      meta: {
        title: '消息管理',
        icon: 'message',
        perms: ['mqtt:message:list']
      }
    },
    {
      path: 'auth',
      component: () => import('@/views/mqtt/auth/index'),
      name: 'MqttAuth',
      meta: {
        title: '认证管理',
        icon: 'key',
        perms: ['mqtt:auth:list']
      }
    },
    {
      path: 'topics',
      component: () => import('@/views/mqtt/topics/index'),
      name: 'MqttTopics',
      meta: {
        title: '主题管理',
        icon: 'tree-table',
        perms: ['mqtt:topic:list']
      }
    },
    {
      path: 'monitoring',
      component: () => import('@/views/mqtt/monitoring/index'),
      name: 'MqttMonitoring',
      meta: {
        title: '实时监控',
        icon: 'monitor',
        perms: ['mqtt:monitor:view']
      }
    },
    {
      path: 'settings',
      component: () => import('@/views/mqtt/settings/index'),
      name: 'MqttSettings',
      meta: {
        title: '系统设置',
        icon: 'setting',
        perms: ['mqtt:settings:manage']
      }
    }
  ]
}

export default mqttRouter
