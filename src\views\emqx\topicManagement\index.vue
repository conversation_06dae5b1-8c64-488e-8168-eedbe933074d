<template>
  <div class="app-container">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <div class="page-title">
        <h2>EMQX主题管理</h2>
        <p>支持按需在EMQX中执行主题操作，实现场景化主题管理</p>
      </div>
      <div class="page-actions">
        <el-button type="primary" icon="el-icon-refresh" @click="refreshData">刷新数据</el-button>
        <el-button type="success" icon="el-icon-plus" @click="showAddTopicDialog">添加订阅</el-button>
        <el-button type="info" icon="el-icon-operation" @click="showBatchDialog">批量操作</el-button>
        <el-button type="warning" icon="el-icon-setting" @click="showScenarioDialog">场景管理</el-button>
      </div>
    </div>

    <!-- 实时统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="el-icon-collection-tag"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ realTimeStats.totalTopics || 0 }}</div>
              <div class="stat-label">总主题数</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="el-icon-connection"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ realTimeStats.totalSubscriptions || 0 }}</div>
              <div class="stat-label">总订阅数</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="el-icon-message"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ realTimeStats.messagesReceived || 0 }}</div>
              <div class="stat-label">接收消息</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="el-icon-upload"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ realTimeStats.messagesSent || 0 }}</div>
              <div class="stat-label">发送消息</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <!-- 主题列表 -->
        <el-tab-pane label="主题列表" name="topics">
          <div class="topic-list-container">
            <!-- 搜索和过滤 -->
            <div class="search-bar">
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-input
                    v-model="searchTopic"
                    placeholder="搜索主题"
                    prefix-icon="el-icon-search"
                    @input="handleSearch"
                  />
                </el-col>
                <el-col :span="4">
                  <el-select v-model="filterModule" placeholder="业务模块" @change="handleFilter">
                    <el-option label="全部" value=""></el-option>
                    <el-option label="视频监控" value="video"></el-option>
                    <el-option label="门禁管理" value="access"></el-option>
                    <el-option label="重量传感器" value="weight"></el-option>
                    <el-option label="设备管理" value="device"></el-option>
                    <el-option label="系统" value="system"></el-option>
                  </el-select>
                </el-col>
                <el-col :span="4">
                  <el-select v-model="filterFunction" placeholder="主题功能" @change="handleFilter">
                    <el-option label="全部" value=""></el-option>
                    <el-option label="心跳" value="heartbeat"></el-option>
                    <el-option label="状态" value="status"></el-option>
                    <el-option label="数据" value="data"></el-option>
                    <el-option label="告警" value="alarm"></el-option>
                    <el-option label="配置" value="config"></el-option>
                  </el-select>
                </el-col>
                <el-col :span="8">
                  <el-button type="primary" @click="getTopicList">查询</el-button>
                  <el-button @click="resetSearch">重置</el-button>
                  <el-button type="success" @click="showTopicTestDialog">测试主题</el-button>
                </el-col>
              </el-row>
            </div>

            <!-- 主题表格 -->
            <el-table
              v-loading="loading"
              :data="filteredTopicList"
              style="width: 100%"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="主题" prop="topic" min-width="200">
                <template slot-scope="scope">
                  <el-tooltip :content="scope.row.topic" placement="top">
                    <span class="topic-name">{{ scope.row.topic }}</span>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column label="业务模块" prop="module" width="100" align="center">
                <template slot-scope="scope">
                  <el-tag :type="getModuleTagType(scope.row.module)" size="small">
                    {{ scope.row.module || '未知' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="功能" prop="function" width="80" align="center">
                <template slot-scope="scope">
                  <el-tag :type="getFunctionTagType(scope.row.function)" size="small">
                    {{ scope.row.function || '其他' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="订阅者数量" prop="subscriberCount" width="100" align="center">
                <template slot-scope="scope">
                  <el-badge :value="scope.row.subscriberCount || 0" class="item">
                    <i class="el-icon-user"></i>
                  </el-badge>
                </template>
              </el-table-column>
              <el-table-column label="节点" prop="node" width="150" align="center" />
              <el-table-column label="操作" width="250" align="center">
                <template slot-scope="scope">
                  <el-button size="mini" type="primary" @click="viewTopicDetail(scope.row)">详情</el-button>
                  <el-button size="mini" type="success" @click="subscribeToTopic(scope.row)">订阅</el-button>
                  <el-button size="mini" type="warning" @click="editTopicSubscription(scope.row)">编辑</el-button>
                  <el-button size="mini" type="danger" @click="removeTopicSubscription(scope.row)">删除</el-button>
                  <el-button size="mini" type="warning" @click="testTopic(scope.row)">测试</el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <pagination
              v-show="total > 0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="getTopicList"
            />
          </div>
        </el-tab-pane>

        <!-- 场景化管理 -->
        <el-tab-pane label="场景化管理" name="scenarios">
          <div class="scenario-container">
            <!-- 设备类型分组 -->
            <el-card class="scenario-card" shadow="hover">
              <div slot="header" class="card-header">
                <span>设备类型分组</span>
                <el-button style="float: right; padding: 3px 0" type="text" @click="refreshDeviceTypes">刷新</el-button>
              </div>
              <el-row :gutter="20">
                <el-col :span="6" v-for="deviceType in deviceTypes" :key="deviceType">
                  <div class="device-type-item" @click="selectDeviceType(deviceType)">
                    <div class="device-icon">
                      <i :class="getDeviceIcon(deviceType)"></i>
                    </div>
                    <div class="device-name">{{ getDeviceTypeName(deviceType) }}</div>
                    <div class="device-count">{{ getDeviceTopicCount(deviceType) }} 个主题</div>
                  </div>
                </el-col>
              </el-row>
            </el-card>

            <!-- 业务模块分组 -->
            <el-card class="scenario-card" shadow="hover">
              <div slot="header" class="card-header">
                <span>业务模块分组</span>
                <el-button style="float: right; padding: 3px 0" type="text" @click="refreshModules">刷新</el-button>
              </div>
              <el-row :gutter="20">
                <el-col :span="6" v-for="module in modules" :key="module">
                  <div class="module-item" @click="selectModule(module)">
                    <div class="module-icon">
                      <i :class="getModuleIcon(module)"></i>
                    </div>
                    <div class="module-name">{{ getModuleName(module) }}</div>
                    <div class="module-count">{{ getModuleTopicCount(module) }} 个主题</div>
                  </div>
                </el-col>
              </el-row>
            </el-card>

            <!-- 场景配置 -->
            <el-card class="scenario-card" shadow="hover">
              <div slot="header" class="card-header">
                <span>场景配置</span>
                <el-button style="float: right; padding: 3px 0" type="text" @click="showCreateScenarioDialog">新建场景</el-button>
              </div>
              <div class="scenario-list">
                <el-empty v-if="scenarios.length === 0" description="暂无场景配置"></el-empty>
                <div v-else class="scenario-items">
                  <div v-for="scenario in scenarios" :key="scenario.name" class="scenario-item">
                    <div class="scenario-info">
                      <div class="scenario-name">{{ scenario.name }}</div>
                      <div class="scenario-desc">{{ scenario.description }}</div>
                      <div class="scenario-meta">
                        <span>{{ scenario.topics.length }} 个主题</span>
                        <span>创建时间: {{ formatTime(scenario.createTime) }}</span>
                      </div>
                    </div>
                    <div class="scenario-actions">
                      <el-button size="mini" type="primary" @click="applyScenario(scenario)">应用</el-button>
                      <el-button size="mini" type="danger" @click="deleteScenario(scenario)">删除</el-button>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </div>
        </el-tab-pane>

        <!-- 统计分析 -->
        <el-tab-pane label="统计分析" name="analytics">
          <div class="analytics-container">
            <!-- 主题活跃度图表 -->
            <el-card class="analytics-card" shadow="hover">
              <div slot="header">
                <span>主题活跃度分析</span>
              </div>
              <div id="activityChart" style="height: 300px;"></div>
            </el-card>

            <!-- 设备类型分布 -->
            <el-card class="analytics-card" shadow="hover">
              <div slot="header">
                <span>设备类型分布</span>
              </div>
              <div id="deviceTypeChart" style="height: 300px;"></div>
            </el-card>

            <!-- 功能分布 -->
            <el-card class="analytics-card" shadow="hover">
              <div slot="header">
                <span>功能分布</span>
              </div>
              <div id="functionChart" style="height: 300px;"></div>
            </el-card>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 动态主题管理组件 -->
    <DynamicTopicManager
      ref="dynamicTopicManager"
      @refresh="refreshData"
    />
  </div>
</template>

<script>
import {
  getTopics,
  getRealTimeTopicStats,
  getTopicsByDeviceType,
  getTopicsByModule,
  getAllTopicScenarios,
  getTopicAnalytics,
  testTopicConnectivity,
  addDynamicTopic,
  removeDynamicTopic,
  updateDynamicTopic
} from '@/api/emqx/topicManagement'
import DynamicTopicManager from './DynamicTopicManager.vue'

export default {
  name: 'TopicManagement',
  components: {
    DynamicTopicManager
  },
  data() {
    return {
      // 页面状态
      loading: false,
      activeTab: 'topics',
      
      // 实时统计
      realTimeStats: {},
      
      // 主题列表
      topicList: [],
      filteredTopicList: [],
      total: 0,
      selectedTopics: [],
      
      // 搜索和过滤
      searchTopic: '',
      filterModule: '',
      filterFunction: '',
      
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      
      // 场景化管理
      deviceTypes: ['video', 'access', 'weight', 'device'],
      modules: ['video', 'access', 'weight', 'device', 'system'],
      scenarios: [],
      
      // 统计分析
      analytics: {},
      
      // 定时器
      statsTimer: null
    }
  },
  
  created() {
    this.init()
  },
  
  beforeDestroy() {
    if (this.statsTimer) {
      clearInterval(this.statsTimer)
    }
  },
  
  methods: {
    // 初始化
    async init() {
      await this.getTopicList()
      await this.getRealTimeStats()
      await this.getScenarios()
      
      // 启动定时刷新
      this.startStatsTimer()
    },
    
    // 启动统计数据定时刷新
    startStatsTimer() {
      this.statsTimer = setInterval(() => {
        this.getRealTimeStats()
      }, 30000) // 30秒刷新一次
    },

    // 获取主题列表
    async getTopicList() {
      this.loading = true
      try {
        const response = await getTopics(this.queryParams)
        this.topicList = response.rows || []
        this.total = response.total || 0
        this.filterTopicList()
      } catch (error) {
        this.$message.error('获取主题列表失败')
        console.error(error)
      } finally {
        this.loading = false
      }
    },

    // 获取实时统计
    async getRealTimeStats() {
      try {
        const response = await getRealTimeTopicStats()
        this.realTimeStats = response.data || {}
      } catch (error) {
        console.error('获取实时统计失败:', error)
      }
    },

    // 获取场景配置
    async getScenarios() {
      try {
        const response = await getAllTopicScenarios()
        this.scenarios = response.data || []
      } catch (error) {
        console.error('获取场景配置失败:', error)
      }
    },

    // 过滤主题列表
    filterTopicList() {
      let filtered = [...this.topicList]

      // 搜索过滤
      if (this.searchTopic) {
        filtered = filtered.filter(topic =>
          topic.topic.toLowerCase().includes(this.searchTopic.toLowerCase())
        )
      }

      // 模块过滤
      if (this.filterModule) {
        filtered = filtered.filter(topic => topic.module === this.filterModule)
      }

      // 功能过滤
      if (this.filterFunction) {
        filtered = filtered.filter(topic => topic.function === this.filterFunction)
      }

      this.filteredTopicList = filtered
    },

    // 处理搜索
    handleSearch() {
      this.filterTopicList()
    },

    // 处理过滤
    handleFilter() {
      this.filterTopicList()
    },

    // 重置搜索
    resetSearch() {
      this.searchTopic = ''
      this.filterModule = ''
      this.filterFunction = ''
      this.filterTopicList()
    },

    // 处理选择变化
    handleSelectionChange(selection) {
      this.selectedTopics = selection
    },

    // 处理标签页切换
    handleTabClick(tab) {
      if (tab.name === 'analytics') {
        this.$nextTick(() => {
          this.initCharts()
        })
      }
    },

    // 刷新数据
    refreshData() {
      this.init()
      this.$message.success('数据已刷新')
    },

    // 获取模块标签类型
    getModuleTagType(module) {
      const types = {
        'video': 'primary',
        'access': 'success',
        'weight': 'warning',
        'device': 'info',
        'system': 'danger'
      }
      return types[module] || ''
    },

    // 获取功能标签类型
    getFunctionTagType(func) {
      const types = {
        'heartbeat': 'success',
        'status': 'primary',
        'data': 'warning',
        'alarm': 'danger',
        'config': 'info'
      }
      return types[func] || ''
    },

    // 获取设备图标
    getDeviceIcon(deviceType) {
      const icons = {
        'video': 'el-icon-video-camera',
        'access': 'el-icon-key',
        'weight': 'el-icon-scale',
        'device': 'el-icon-cpu'
      }
      return icons[deviceType] || 'el-icon-box'
    },

    // 获取设备类型名称
    getDeviceTypeName(deviceType) {
      const names = {
        'video': '视频监控',
        'access': '门禁管理',
        'weight': '重量传感器',
        'device': '设备管理'
      }
      return names[deviceType] || deviceType
    },

    // 获取设备主题数量
    getDeviceTopicCount(deviceType) {
      return this.topicList.filter(topic => topic.module === deviceType).length
    },

    // 获取模块图标
    getModuleIcon(module) {
      return this.getDeviceIcon(module)
    },

    // 获取模块名称
    getModuleName(module) {
      return this.getDeviceTypeName(module)
    },

    // 获取模块主题数量
    getModuleTopicCount(module) {
      return this.getDeviceTopicCount(module)
    },

    // 格式化时间
    formatTime(timestamp) {
      if (!timestamp) return ''
      return new Date(timestamp).toLocaleString()
    },

    // 查看主题详情
    viewTopicDetail(topic) {
      this.$router.push({
        path: '/emqx/topic-detail',
        query: { topic: topic.topic }
      })
    },

    // 订阅主题
    subscribeToTopic(topic) {
      // 实现订阅逻辑
      this.$message.success(`订阅主题: ${topic.topic}`)
    },

    // 测试主题
    async testTopic(topic) {
      try {
        const response = await testTopicConnectivity({
          topic: topic.topic,
          payload: 'test message'
        })

        if (response.data.status === 'SUCCESS') {
          this.$message.success('主题测试成功')
        } else {
          this.$message.error('主题测试失败: ' + response.data.message)
        }
      } catch (error) {
        this.$message.error('测试失败')
        console.error(error)
      }
    },

    // 选择设备类型
    selectDeviceType(deviceType) {
      this.$message.info(`选择设备类型: ${this.getDeviceTypeName(deviceType)}`)
    },

    // 选择模块
    selectModule(module) {
      this.$message.info(`选择业务模块: ${this.getModuleName(module)}`)
    },

    // 应用场景
    applyScenario(scenario) {
      this.$message.success(`应用场景: ${scenario.name}`)
    },

    // 删除场景
    deleteScenario(scenario) {
      this.$confirm(`确定删除场景 "${scenario.name}" 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
        this.getScenarios()
      })
    },

    // 显示添加主题对话框
    showAddTopicDialog() {
      this.$refs.dynamicTopicManager.showAddDialog()
    },

    // 显示批量操作对话框
    showBatchDialog() {
      this.$refs.dynamicTopicManager.showBatchDialog()
    },

    // 显示场景对话框
    showScenarioDialog() {
      this.$message.info('场景管理功能开发中...')
    },

    // 编辑主题订阅
    editTopicSubscription(topic) {
      // 构造订阅信息对象
      const subscription = {
        topic: topic.topic,
        clientId: topic.clientId || 'system_client',
        qos: topic.qos || 1,
        noLocal: false,
        retainAsPublished: false,
        retainHandling: 0
      }
      this.$refs.dynamicTopicManager.showEditDialog(subscription)
    },

    // 删除主题订阅
    async removeTopicSubscription(topic) {
      const subscription = {
        topic: topic.topic,
        clientId: topic.clientId || 'system_client'
      }
      await this.$refs.dynamicTopicManager.removeTopic(subscription)
    },

    // 显示主题测试对话框
    showTopicTestDialog() {
      this.$message.info('主题测试功能开发中...')
    },

    // 显示创建场景对话框
    showCreateScenarioDialog() {
      this.$message.info('创建场景功能开发中...')
    },

    // 刷新设备类型
    refreshDeviceTypes() {
      this.$message.success('设备类型已刷新')
    },

    // 刷新模块
    refreshModules() {
      this.$message.success('业务模块已刷新')
    },

    // 初始化图表
    initCharts() {
      // 图表初始化逻辑
      this.$message.info('图表功能开发中...')
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .page-title {
    h2 {
      margin: 0;
      color: #303133;
    }

    p {
      margin: 5px 0 0 0;
      color: #909399;
      font-size: 14px;
    }
  }
}

.stats-cards {
  margin-bottom: 20px;

  .stat-card {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;

    .stat-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;

      i {
        font-size: 24px;
        color: #fff;
      }
    }

    .stat-content {
      .stat-value {
        font-size: 28px;
        font-weight: bold;
        color: #303133;
        line-height: 1;
      }

      .stat-label {
        font-size: 14px;
        color: #909399;
        margin-top: 5px;
      }
    }
  }
}

.main-content {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .topic-list-container {
    padding: 20px;

    .search-bar {
      margin-bottom: 20px;
    }

    .topic-name {
      font-family: 'Courier New', monospace;
      color: #409eff;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .scenario-container {
    padding: 20px;

    .scenario-card {
      margin-bottom: 20px;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .device-type-item,
      .module-item {
        text-align: center;
        padding: 20px;
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          border-color: #409eff;
          box-shadow: 0 2px 12px 0 rgba(64, 158, 255, 0.2);
        }

        .device-icon,
        .module-icon {
          font-size: 32px;
          color: #409eff;
          margin-bottom: 10px;
        }

        .device-name,
        .module-name {
          font-size: 16px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 5px;
        }

        .device-count,
        .module-count {
          font-size: 14px;
          color: #909399;
        }
      }

      .scenario-list {
        .scenario-items {
          .scenario-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            margin-bottom: 10px;

            .scenario-info {
              flex: 1;

              .scenario-name {
                font-size: 16px;
                font-weight: bold;
                color: #303133;
                margin-bottom: 5px;
              }

              .scenario-desc {
                font-size: 14px;
                color: #606266;
                margin-bottom: 5px;
              }

              .scenario-meta {
                font-size: 12px;
                color: #909399;

                span {
                  margin-right: 15px;
                }
              }
            }

            .scenario-actions {
              flex-shrink: 0;
            }
          }
        }
      }
    }
  }

  .analytics-container {
    padding: 20px;

    .analytics-card {
      margin-bottom: 20px;
    }
  }
}
</style>
