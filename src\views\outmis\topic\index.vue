<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-people">
            <svg-icon icon-class="message" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">总主题数</div>
            <div class="card-panel-num">{{ statistics.totalTopics }}</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-message">
            <svg-icon icon-class="online" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">活跃主题</div>
            <div class="card-panel-num">{{ statistics.activeTopics }}</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-money">
            <svg-icon icon-class="component" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">设备类型</div>
            <div class="card-panel-num">{{ statistics.deviceTypes }}</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-shopping">
            <svg-icon icon-class="chart" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">订阅数</div>
            <div class="card-panel-num">{{ statistics.subscriptions }}</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 查询表单 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="设备类型" prop="deviceType">
        <el-select v-model="queryParams.deviceType" placeholder="请选择设备类型" clearable>
          <el-option label="称重传感器" value="weight_sensor" />
          <el-option label="门禁设备" value="access_control" />
          <el-option label="地磅" value="weighbridge" />
          <el-option label="摄像头" value="camera" />
          <el-option label="温度传感器" value="temperature" />
          <el-option label="RFID读卡器" value="rfid" />
        </el-select>
      </el-form-item>
      <el-form-item label="主题类型" prop="topicType">
        <el-select v-model="queryParams.topicType" placeholder="请选择主题类型" clearable>
          <el-option label="发布主题" value="publish" />
          <el-option label="订阅主题" value="subscribe" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="启用" value="1" />
          <el-option label="停用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['outmis:topic:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['outmis:topic:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['outmis:topic:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['outmis:topic:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 主题列表 -->
    <el-table v-loading="loading" :data="topicList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主题ID" align="center" prop="topicId" width="80" />
      <el-table-column label="设备类型" align="center" prop="deviceType" width="120">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.device_type" :value="scope.row.deviceType"/>
        </template>
      </el-table-column>
      <el-table-column label="主题模式" align="center" prop="topicPattern" :show-overflow-tooltip="true" />
      <el-table-column label="主题类型" align="center" prop="topicType" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.topicType === 'publish' ? 'success' : 'info'">
            {{ scope.row.topicType === 'publish' ? '发布' : '订阅' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="QoS等级" align="center" prop="qos" width="80" />
      <el-table-column label="描述" align="center" prop="description" :show-overflow-tooltip="true" />
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.status" active-value="1" inactive-value="0" @change="handleStatusChange(scope.row)"></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['outmis:topic:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['outmis:topic:remove']">删除</el-button>
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleTest(scope.row)" v-hasPermi="['outmis:topic:test']">测试</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改主题配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="设备类型" prop="deviceType">
          <el-select v-model="form.deviceType" placeholder="请选择设备类型" @change="onDeviceTypeChange">
            <el-option label="称重传感器" value="weight_sensor" />
            <el-option label="门禁设备" value="access_control" />
            <el-option label="地磅" value="weighbridge" />
            <el-option label="摄像头" value="camera" />
            <el-option label="温度传感器" value="temperature" />
            <el-option label="RFID读卡器" value="rfid" />
          </el-select>
        </el-form-item>
        <el-form-item label="主题模式" prop="topicPattern">
          <el-input v-model="form.topicPattern" placeholder="请输入主题模式，如：warehouse/weight/{deviceCode}/data" />
          <div style="color: #909399; font-size: 12px; margin-top: 4px;">
            支持变量：{deviceCode}、{clientId}、{deviceType}
          </div>
        </el-form-item>
        <el-form-item label="主题类型" prop="topicType">
          <el-radio-group v-model="form.topicType">
            <el-radio label="publish">发布主题</el-radio>
            <el-radio label="subscribe">订阅主题</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="QoS等级" prop="qos">
          <el-select v-model="form.qos" placeholder="请选择QoS等级">
            <el-option label="0 - 最多一次" :value="0" />
            <el-option label="1 - 至少一次" :value="1" />
            <el-option label="2 - 恰好一次" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入主题描述" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="1">启用</el-radio>
            <el-radio label="0">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 主题测试对话框 -->
    <el-dialog title="主题测试" :visible.sync="testOpen" width="800px" append-to-body>
      <el-form :model="testForm" label-width="100px">
        <el-form-item label="设备编码">
          <el-input v-model="testForm.deviceCode" placeholder="请输入设备编码" />
        </el-form-item>
        <el-form-item label="实际主题">
          <el-input v-model="testForm.actualTopic" readonly />
        </el-form-item>
        <el-form-item label="测试消息" v-if="testForm.topicType === 'publish'">
          <el-input v-model="testForm.testMessage" type="textarea" placeholder="请输入测试消息内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="executeTest">执行测试</el-button>
        <el-button @click="testOpen = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTopic, getTopic, delTopic, addTopic, updateTopic, getTopicStatistics } from "@/api/outmis/topic";

export default {
  name: "Topic",
  dicts: ['device_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 主题表格数据
      topicList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 测试弹出层
      testOpen: false,
      // 统计数据
      statistics: {
        totalTopics: 0,
        activeTopics: 0,
        deviceTypes: 0,
        subscriptions: 0
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceType: null,
        topicType: null,
        status: null
      },
      // 表单参数
      form: {},
      // 测试表单
      testForm: {},
      // 表单校验
      rules: {
        deviceType: [
          { required: true, message: "设备类型不能为空", trigger: "change" }
        ],
        topicPattern: [
          { required: true, message: "主题模式不能为空", trigger: "blur" }
        ],
        topicType: [
          { required: true, message: "主题类型不能为空", trigger: "change" }
        ],
        qos: [
          { required: true, message: "QoS等级不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getStatistics();
  },
  methods: {
    /** 查询主题列表 */
    getList() {
      this.loading = true;
      listTopic(this.queryParams).then(response => {
        this.topicList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(() => {
        // 模拟数据
        this.topicList = [
          {
            topicId: 1,
            deviceType: "weight_sensor",
            topicPattern: "warehouse/weight/{deviceCode}/data",
            topicType: "publish",
            qos: 1,
            description: "称重传感器数据发布主题",
            status: "1",
            createTime: "2025-01-20 10:00:00"
          },
          {
            topicId: 2,
            deviceType: "weight_sensor",
            topicPattern: "warehouse/weight/{deviceCode}/command",
            topicType: "subscribe",
            qos: 1,
            description: "称重传感器命令订阅主题",
            status: "1",
            createTime: "2025-01-20 10:01:00"
          }
        ];
        this.total = this.topicList.length;
        this.loading = false;
      });
    },
    /** 获取统计数据 */
    getStatistics() {
      getTopicStatistics().then(response => {
        this.statistics = response.data;
      }).catch(() => {
        // 模拟统计数据
        this.statistics = {
          totalTopics: 24,
          activeTopics: 18,
          deviceTypes: 6,
          subscriptions: 156
        };
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        topicId: null,
        deviceType: null,
        topicPattern: null,
        topicType: null,
        qos: 1,
        description: null,
        status: "1"
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.topicId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加主题配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const topicId = row.topicId || this.ids
      getTopic(topicId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改主题配置";
      }).catch(() => {
        // 模拟数据
        this.form = { ...row };
        this.open = true;
        this.title = "修改主题配置";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.topicId != null) {
            updateTopic(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
              this.getStatistics();
            });
          } else {
            addTopic(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
              this.getStatistics();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const topicIds = row.topicId ? [row.topicId] : this.ids;
      this.$modal.confirm('是否确认删除主题配置编号为"' + topicIds + '"的数据项？').then(function() {
        return delTopic(topicIds);
      }).then(() => {
        this.getList();
        this.getStatistics();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('outmis/topic/export', {
        ...this.queryParams
      }, `topic_${new Date().getTime()}.xlsx`)
    },
    /** 状态修改 */
    handleStatusChange(row) {
      let text = row.status === "1" ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '""' + row.topicPattern + '"主题吗？').then(function() {
        return updateTopic(row);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function() {
        row.status = row.status === "1" ? "0" : "1";
      });
    },
    /** 设备类型变化处理 */
    onDeviceTypeChange(deviceType) {
      // 根据设备类型生成默认主题模式
      const topicTemplates = {
        'weight_sensor': 'warehouse/weight/{deviceCode}/data',
        'access_control': 'warehouse/access/{deviceCode}/event',
        'weighbridge': 'warehouse/weighbridge/{deviceCode}/data',
        'camera': 'warehouse/video/{deviceCode}/stream',
        'temperature': 'warehouse/env/{deviceCode}/data',
        'rfid': 'warehouse/rfid/{deviceCode}/scan'
      };

      if (topicTemplates[deviceType]) {
        this.form.topicPattern = topicTemplates[deviceType];
      }
    },
    /** 主题测试 */
    handleTest(row) {
      this.testForm = {
        topicId: row.topicId,
        topicPattern: row.topicPattern,
        topicType: row.topicType,
        deviceCode: '',
        actualTopic: '',
        testMessage: ''
      };
      this.testOpen = true;
    },
    /** 执行测试 */
    executeTest() {
      if (!this.testForm.deviceCode) {
        this.$modal.msgWarning("请输入设备编码");
        return;
      }

      // 生成实际主题
      this.testForm.actualTopic = this.testForm.topicPattern
        .replace('{deviceCode}', this.testForm.deviceCode)
        .replace('{clientId}', `client_${this.testForm.deviceCode}`)
        .replace('{deviceType}', this.testForm.deviceType || 'device');

      // 这里可以调用后端API进行实际的MQTT测试
      this.$modal.msgSuccess("测试完成，主题：" + this.testForm.actualTopic);
    }
  }
};
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}

.card-panel {
  height: 108px;
  cursor: pointer;
  font-size: 12px;
  position: relative;
  overflow: hidden;
  color: #666;
  background: #fff;
  box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
  border-color: rgba(0, 0, 0, .05);
  border-radius: 4px;
}

.card-panel:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-panel-icon-wrapper {
  float: left;
  margin: 14px 0 0 14px;
  padding: 16px;
  transition: all 0.38s ease-out;
  border-radius: 6px;
}

.card-panel-icon {
  float: left;
  font-size: 48px;
}

.card-panel-description {
  float: right;
  font-weight: bold;
  margin: 26px;
  margin-left: 0px;
}

.card-panel-text {
  line-height: 18px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 16px;
  margin-bottom: 12px;
}

.card-panel-num {
  font-size: 20px;
}

.icon-people {
  background: #40c9c6;
}

.icon-message {
  background: #36a3f7;
}

.icon-money {
  background: #f4516c;
}

.icon-shopping {
  background: #34bfa3;
}

.icon-people .card-panel-icon {
  color: #40c9c6;
}

.icon-message .card-panel-icon {
  color: #36a3f7;
}

.icon-money .card-panel-icon {
  color: #f4516c;
}

.icon-shopping .card-panel-icon {
  color: #34bfa3;
}
</style>
