import request from '@/utils/request'

// 查询出入库申请列表
export function listInout(query) {
  return request({
    url: '/inout/inout/list',
    method: 'get',
    params: query
  })
}

// 查询出入库申请详细
export function getInout(id) {
  return request({
    url: '/inout/inout/detail/' + id,
    method: 'get'
  })
}

// 根据单据编号查询出入库申请
export function getInoutByBillNo(billNo) {
  return request({
    url: '/inout/inout/billNo/' + billNo,
    method: 'get'
  })
}

// 根据物料清单编号查询出入库申请
export function getInoutByMaterialBillCode(materialBillCode) {
  return request({
    url: '/inout/inout/materialBillCode/' + materialBillCode,
    method: 'get'
  })
}

// 新增出入库申请
export function addInout(data) {
  return request({
    url: '/inout/inout',
    method: 'post',
    data: data
  })
}

// 修改出入库申请
export function updateInout(data) {
  return request({
    url: '/inout/inout',
    method: 'put',
    data: data
  })
}

// 暂存出入库申请
export function saveInoutDraft(data) {
  return request({
    url: '/inout/inout/draft',
    method: 'post',
    data: data
  })
}

// 更新暂存的出入库申请
export function updateInoutDraft(data) {
  return request({
    url: '/inout/inout/draft',
    method: 'put',
    data: data
  })
}

// 删除出入库申请
export function delInout(id) {
  return request({
    url: '/inout/inout/' + id,
    method: 'delete'
  })
}

// 获取统计信息
export function getStatistics() {
  return request({
    url: '/inout/inout/statistics',
    method: 'get'
  })
}

// 获取业务类型选项
export function getBusinessTypes() {
  return request({
    url: '/inout/inout/businessTypes',
    method: 'get'
  })
}

// 获取部门列表
export function getDepartments() {
  return request({
    url: '/inout/inout/departments',
    method: 'get'
  })
}

// 获取用户列表
export function getUsers() {
  return request({
    url: '/inout/inout/users',
    method: 'get'
  })
}

// 根据部门ID获取用户列表
export function getUsersByDept(deptId) {
  return request({
    url: '/inout/inout/users/dept/' + deptId,
    method: 'get'
  })
}

// 获取审批流程列表
export function getWorkflows() {
  return request({
    url: '/inout/inout/workflows',
    method: 'get'
  })
}

// 获取维修工单列表
export function getMaintenanceOrders() {
  return request({
    url: '/inout/inout/maintenanceOrders',
    method: 'get'
  })
}

// 获取申请原因选项
export function getReasonOptions() {
  return request({
    url: '/inout/inout/reasonOptions',
    method: 'get'
  })
}

// 获取雪花ID
export function getSnowflakeId() {
  return request({
    url: '/inout/inout/snowflakeId',
    method: 'get'
  })
}

// 上传人脸照片
export function uploadFace(data) {
  return request({
    url: '/inout/inout/uploadFace',
    method: 'post',
    data: data,
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

// 获取物料清单信息
export function getMaterialBill(billCode) {
  return request({
    url: '/inout/inout/materialBill/' + billCode,
    method: 'get'
  })
}



// 发起审批
export function startApproval(id, data) {
  return request({
    url: '/inout/inout/' + id + '/startApproval',
    method: 'post',
    data: data
  })
}

// 取消申请
export function cancelApplication(id, data) {
  return request({
    url: '/inout/inout/' + id + '/cancel',
    method: 'post',
    data: data
  })
}

// 批量更新状态
export function batchUpdateStatus(data) {
  return request({
    url: '/inout/inout/batchUpdateStatus',
    method: 'post',
    data: data
  })
}

// 根据状态获取数量
export function getCountByStatus() {
  return request({
    url: '/inout/inout/count/status',
    method: 'get'
  })
}

// 获取最近申请记录
export function getRecentApplications(limit) {
  return request({
    url: '/inout/inout/recent',
    method: 'get',
    params: { limit: limit }
  })
}
