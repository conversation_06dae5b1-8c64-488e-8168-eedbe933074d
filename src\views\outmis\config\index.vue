<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="设备ID" prop="deviceId">
        <el-input
          v-model="queryParams.deviceId"
          placeholder="请输入设备ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="配置键" prop="configKey">
        <el-input
          v-model="queryParams.configKey"
          placeholder="请输入配置键"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="配置分组" prop="configGroup">
        <el-select v-model="queryParams.configGroup" placeholder="请选择配置分组" clearable>
          <el-option label="通用配置" value="general" />
          <el-option label="MQTT配置" value="mqtt" />
          <el-option label="称重配置" value="weight" />
          <el-option label="告警配置" value="alert" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['outmis:config:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['outmis:config:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['outmis:config:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-refresh"
          size="mini"
          @click="handleInitConfig"
          v-hasPermi="['outmis:config:reset']"
        >初始化配置</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['outmis:config:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 配置列表 -->
    <el-table v-loading="loading" :data="configList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="设备ID" align="center" prop="deviceId" />
      <el-table-column label="配置键" align="center" prop="configKey" />
      <el-table-column label="配置值" align="center" prop="configValue" />
      <el-table-column label="配置类型" align="center" prop="configType">
        <template slot-scope="scope">
          <el-tag :type="getConfigTypeTag(scope.row.configType)">
            {{ getConfigTypeLabel(scope.row.configType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="配置分组" align="center" prop="configGroup">
        <template slot-scope="scope">
          <el-tag :type="getConfigGroupTag(scope.row.configGroup)">
            {{ getConfigGroupLabel(scope.row.configGroup) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="配置描述" align="center" prop="configDesc" />
      <el-table-column label="是否必填" align="center" prop="isRequired">
        <template slot-scope="scope">
          <el-tag :type="scope.row.isRequired === '1' ? 'danger' : 'info'">
            {{ scope.row.isRequired === '1' ? '必填' : '可选' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="sortOrder" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['outmis:config:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['outmis:config:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="设备ID" prop="deviceId">
          <el-input-number v-model="form.deviceId" :min="1" placeholder="请输入设备ID" />
        </el-form-item>
        <el-form-item label="配置键" prop="configKey">
          <el-input v-model="form.configKey" placeholder="请输入配置键" />
        </el-form-item>
        <el-form-item label="配置值" prop="configValue">
          <el-input v-model="form.configValue" placeholder="请输入配置值" />
        </el-form-item>
        <el-form-item label="配置类型" prop="configType">
          <el-select v-model="form.configType" placeholder="请选择配置类型">
            <el-option label="字符串" value="string" />
            <el-option label="数字" value="number" />
            <el-option label="布尔值" value="boolean" />
            <el-option label="JSON对象" value="json" />
          </el-select>
        </el-form-item>
        <el-form-item label="配置分组" prop="configGroup">
          <el-select v-model="form.configGroup" placeholder="请选择配置分组">
            <el-option label="通用配置" value="general" />
            <el-option label="MQTT配置" value="mqtt" />
            <el-option label="称重配置" value="weight" />
            <el-option label="告警配置" value="alert" />
          </el-select>
        </el-form-item>
        <el-form-item label="配置描述" prop="configDesc">
          <el-input v-model="form.configDesc" placeholder="请输入配置描述" />
        </el-form-item>
        <el-form-item label="是否必填" prop="isRequired">
          <el-radio-group v-model="form.isRequired">
            <el-radio label="1">必填</el-radio>
            <el-radio label="0">可选</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="默认值" prop="defaultValue">
          <el-input v-model="form.defaultValue" placeholder="请输入默认值" />
        </el-form-item>
        <el-form-item label="验证规则" prop="validationRule">
          <el-input v-model="form.validationRule" placeholder="请输入验证规则(正则表达式)" />
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="form.sortOrder" :min="0" placeholder="请输入排序" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 初始化配置对话框 -->
    <el-dialog title="初始化设备配置" :visible.sync="initOpen" width="400px" append-to-body>
      <el-form ref="initForm" :model="initForm" :rules="initRules" label-width="80px">
        <el-form-item label="设备ID" prop="deviceId">
          <el-input-number v-model="initForm.deviceId" :min="1" placeholder="请输入设备ID" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitInit">确 定</el-button>
        <el-button @click="cancelInit">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  listConfig, 
  getConfig, 
  delConfig, 
  addConfig, 
  updateConfig,
  initDeviceConfig
} from "@/api/outmis/config";

export default {
  name: "Config",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 配置表格数据
      configList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 初始化对话框
      initOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceId: null,
        configKey: null,
        configGroup: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 初始化表单参数
      initForm: {},
      // 表单校验
      rules: {
        deviceId: [
          { required: true, message: "设备ID不能为空", trigger: "blur" }
        ],
        configKey: [
          { required: true, message: "配置键不能为空", trigger: "blur" }
        ],
        configValue: [
          { required: true, message: "配置值不能为空", trigger: "blur" }
        ],
        configType: [
          { required: true, message: "配置类型不能为空", trigger: "change" }
        ],
        configGroup: [
          { required: true, message: "配置分组不能为空", trigger: "change" }
        ],
      },
      // 初始化表单校验
      initRules: {
        deviceId: [
          { required: true, message: "设备ID不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询配置列表 */
    getList() {
      this.loading = true;
      listConfig(this.queryParams).then(response => {
        this.configList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 配置类型标签
    getConfigTypeTag(type) {
      const tags = {
        'string': '',
        'number': 'success',
        'boolean': 'warning',
        'json': 'danger'
      };
      return tags[type] || '';
    },
    // 配置类型标签文本
    getConfigTypeLabel(type) {
      const labels = {
        'string': '字符串',
        'number': '数字',
        'boolean': '布尔值',
        'json': 'JSON'
      };
      return labels[type] || type;
    },
    // 配置分组标签
    getConfigGroupTag(group) {
      const tags = {
        'general': '',
        'mqtt': 'success',
        'weight': 'warning',
        'alert': 'danger'
      };
      return tags[group] || '';
    },
    // 配置分组标签文本
    getConfigGroupLabel(group) {
      const labels = {
        'general': '通用',
        'mqtt': 'MQTT',
        'weight': '称重',
        'alert': '告警'
      };
      return labels[group] || group;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        configId: null,
        deviceId: null,
        configKey: null,
        configValue: null,
        configType: null,
        configGroup: null,
        configDesc: null,
        isRequired: "0",
        defaultValue: null,
        validationRule: null,
        sortOrder: 0,
        status: "0"
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.configId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const configId = row.configId || this.ids
      getConfig(configId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改配置";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.configId != null) {
            updateConfig(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addConfig(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const configIds = row.configId || this.ids;
      this.$modal.confirm('是否确认删除配置编号为"' + configIds + '"的数据项？').then(function() {
        return delConfig(configIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 初始化配置按钮操作 */
    handleInitConfig() {
      this.initForm = { deviceId: null };
      this.initOpen = true;
    },
    /** 提交初始化 */
    submitInit() {
      this.$refs["initForm"].validate(valid => {
        if (valid) {
          this.$modal.confirm('是否确认初始化设备ID为"' + this.initForm.deviceId + '"的配置？此操作将删除现有配置并重新创建默认配置。').then(() => {
            return initDeviceConfig(this.initForm.deviceId);
          }).then(() => {
            this.$modal.msgSuccess("初始化成功");
            this.initOpen = false;
            this.getList();
          }).catch(() => {});
        }
      });
    },
    /** 取消初始化 */
    cancelInit() {
      this.initOpen = false;
      this.initForm = {};
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('outmis/config/export', {
        ...this.queryParams
      }, `config_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
