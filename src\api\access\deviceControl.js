import request from '@/utils/request'

// 查询设备列表
export function listDevices(query) {
  return request({
    url: '/access/deviceControl/devices',
    method: 'get',
    params: query
  })
}

// 设备登录
export function loginDevice(deviceCode) {
  return request({
    url: '/access/deviceControl/login/' + deviceCode,
    method: 'post'
  })
}

// 设备登出
export function logoutDevice(deviceCode) {
  return request({
    url: '/access/deviceControl/logout/' + deviceCode,
    method: 'post'
  })
}

// 远程开门
export function openDoor(data) {
  return request({
    url: '/access/deviceControl/open',
    method: 'post',
    data: data
  })
}

// 远程关门
export function closeDoor(data) {
  return request({
    url: '/access/deviceControl/close',
    method: 'post',
    data: data
  })
}

// 批量开门
export function batchOpenDoor(deviceList) {
  return request({
    url: '/access/deviceControl/batchOpen',
    method: 'post',
    data: deviceList
  })
}

// 设置人脸识别参数
export function setFaceConfig(deviceCode, configs) {
  return request({
    url: '/access/deviceControl/faceConfig/' + deviceCode,
    method: 'post',
    data: configs
  })
}

// 获取所有设备状态
export function getAllDeviceStatus() {
  return request({
    url: '/access/deviceControl/status',
    method: 'get'
  })
}

// 启动设备事件监听
export function startEventListener(deviceCode) {
  return request({
    url: '/access/deviceControl/startListener/' + deviceCode,
    method: 'post'
  })
}

// 停止设备事件监听
export function stopEventListener(deviceCode) {
  return request({
    url: '/access/deviceControl/stopListener/' + deviceCode,
    method: 'post'
  })
}

// 重启设备
export function rebootDevice(deviceCode) {
  return request({
    url: '/access/deviceControl/reboot/' + deviceCode,
    method: 'post'
  })
}

// 同步设备时间
export function syncDeviceTime(deviceCode) {
  return request({
    url: '/access/deviceControl/syncTime/' + deviceCode,
    method: 'post'
  })
}

// 查询人脸识别结果列表
export function listFaceResults(query) {
  return request({
    url: '/access/faceResult/list',
    method: 'get',
    params: query
  })
}

// 查询人脸识别结果详细
export function getFaceResult(resultId) {
  return request({
    url: '/access/faceResult/' + resultId,
    method: 'get'
  })
}

// 新增人脸识别结果
export function addFaceResult(data) {
  return request({
    url: '/access/faceResult',
    method: 'post',
    data: data
  })
}

// 修改人脸识别结果
export function updateFaceResult(data) {
  return request({
    url: '/access/faceResult',
    method: 'put',
    data: data
  })
}

// 删除人脸识别结果
export function delFaceResult(resultId) {
  return request({
    url: '/access/faceResult/' + resultId,
    method: 'delete'
  })
}

// 导出人脸识别结果
export function exportFaceResult(query) {
  return request({
    url: '/access/faceResult/export',
    method: 'post',
    params: query
  })
}

// 获取实时识别统计
export function getRealTimeStats() {
  return request({
    url: '/access/deviceControl/realTimeStats',
    method: 'get'
  })
}

// 获取设备统计
export function getDeviceStats() {
  return request({
    url: '/access/deviceControl/deviceStats',
    method: 'get'
  })
}

// 获取今日统计
export function getTodayStats() {
  return request({
    url: '/access/deviceControl/todayStats',
    method: 'get'
  })
}

// 获取实时识别记录
export function getRealTimeRecords(query) {
  return request({
    url: '/access/deviceControl/realTimeRecords',
    method: 'get',
    params: query
  })
}

// 处理识别记录
export function processFaceResult(data) {
  return request({
    url: '/access/faceResult/process',
    method: 'put',
    data: data
  })
}

// 批量处理识别记录
export function batchProcessFaceResult(resultIds, processData) {
  return request({
    url: '/access/faceResult/batchProcess',
    method: 'put',
    data: {
      resultIds: resultIds,
      ...processData
    }
  })
}

// 获取识别记录统计图表数据
export function getFaceResultChart(query) {
  return request({
    url: '/access/faceResult/chart',
    method: 'get',
    params: query
  })
}

// 测试设备连接
export function testDeviceConnection(deviceCode) {
  return request({
    url: '/access/deviceControl/testConnection/' + deviceCode,
    method: 'post'
  })
}

// 获取设备能力
export function getDeviceAbility(deviceCode) {
  return request({
    url: '/access/deviceControl/deviceAbility/' + deviceCode,
    method: 'get'
  })
}

// 设置设备参数
export function setDeviceParams(deviceCode, params) {
  return request({
    url: '/access/deviceControl/deviceParams/' + deviceCode,
    method: 'post',
    data: params
  })
}

// 获取设备参数
export function getDeviceParams(deviceCode) {
  return request({
    url: '/access/deviceControl/deviceParams/' + deviceCode,
    method: 'get'
  })
}

// 重置设备参数
export function resetDeviceParams(deviceCode) {
  return request({
    url: '/access/deviceControl/resetParams/' + deviceCode,
    method: 'post'
  })
}

// 备份设备配置
export function backupDeviceConfig(deviceCode) {
  return request({
    url: '/access/deviceControl/backupConfig/' + deviceCode,
    method: 'post'
  })
}

// 恢复设备配置
export function restoreDeviceConfig(deviceCode, configData) {
  return request({
    url: '/access/deviceControl/restoreConfig/' + deviceCode,
    method: 'post',
    data: configData
  })
}

// 获取设备日志
export function getDeviceLogs(deviceCode, query) {
  return request({
    url: '/access/deviceControl/deviceLogs/' + deviceCode,
    method: 'get',
    params: query
  })
}

// 清理设备日志
export function clearDeviceLogs(deviceCode, beforeDate) {
  return request({
    url: '/access/deviceControl/clearLogs/' + deviceCode,
    method: 'delete',
    params: {
      beforeDate: beforeDate
    }
  })
}

// 获取设备固件版本
export function getDeviceFirmwareVersion(deviceCode) {
  return request({
    url: '/access/deviceControl/firmwareVersion/' + deviceCode,
    method: 'get'
  })
}

// 升级设备固件
export function upgradeDeviceFirmware(deviceCode, firmwareFile) {
  return request({
    url: '/access/deviceControl/upgradeFirmware/' + deviceCode,
    method: 'post',
    data: firmwareFile
  })
}

// 获取升级进度
export function getUpgradeProgress(deviceCode) {
  return request({
    url: '/access/deviceControl/upgradeProgress/' + deviceCode,
    method: 'get'
  })
}
