<template>
  <div class="app-container">
    <!-- 设备选择 -->
    <el-card class="device-selector" shadow="never">
      <div slot="header" class="clearfix">
        <span>设备选择</span>
      </div>
      <el-form :inline="true" size="small">
        <el-form-item label="选择设备">
          <el-select v-model="selectedDeviceId" placeholder="请选择智能导寻设备" @change="handleDeviceChange" style="width: 300px;">
            <el-option
              v-for="device in deviceList"
              :key="device.id"
              :label="`${device.deviceName} (${device.deviceCode})`"
              :value="device.id">
              <span style="float: left">{{ device.deviceName }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ device.deviceCode }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-refresh" @click="loadDeviceList">刷新设备</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 配置管理 -->
    <el-card v-if="selectedDeviceId" class="config-management" shadow="never">
      <div slot="header" class="clearfix">
        <span>智能导寻设备参数配置</span>
        <div style="float: right;">
          <el-button size="mini" type="success" icon="el-icon-check" @click="saveAllConfig">保存所有配置</el-button>
          <el-button size="mini" type="warning" icon="el-icon-refresh" @click="resetToDefault">重置为默认值</el-button>
          <el-button size="mini" type="info" icon="el-icon-download" @click="exportConfig">导出配置</el-button>
          <el-button size="mini" type="primary" icon="el-icon-upload2" @click="showImportDialog">导入配置</el-button>
        </div>
      </div>

      <!-- 配置分组标签页 -->
      <el-tabs v-model="activeTab" type="card">
        <el-tab-pane 
          v-for="(configs, groupName) in configGroups" 
          :key="groupName" 
          :label="getGroupLabel(groupName)" 
          :name="groupName">
          
          <el-form :model="configData" label-width="150px" size="small">
            <el-row :gutter="20">
              <el-col :span="12" v-for="config in configs" :key="config.configKey">
                <el-form-item :label="config.configDesc" :required="config.isRequired === '1'">
                  <!-- 根据配置类型渲染不同的输入组件 -->
                  <el-input 
                    v-if="config.configType === 'input'"
                    v-model="configData[config.configKey]"
                    :placeholder="config.defaultValue"
                    @change="handleConfigChange(config.configKey, $event)"
                  />
                  <el-input-number 
                    v-else-if="config.configType === 'number'"
                    v-model="configData[config.configKey]"
                    :min="config.minValue"
                    :max="config.maxValue"
                    :placeholder="config.defaultValue"
                    @change="handleConfigChange(config.configKey, $event)"
                  />
                  <el-select 
                    v-else-if="config.configType === 'select'"
                    v-model="configData[config.configKey]"
                    :placeholder="config.defaultValue"
                    @change="handleConfigChange(config.configKey, $event)"
                  >
                    <el-option 
                      v-for="option in parseSelectOptions(config.configOptions)"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                  <el-switch 
                    v-else-if="config.configType === 'switch'"
                    v-model="configData[config.configKey]"
                    :active-value="1"
                    :inactive-value="0"
                    @change="handleConfigChange(config.configKey, $event)"
                  />
                  <el-input 
                    v-else
                    v-model="configData[config.configKey]"
                    :placeholder="config.defaultValue"
                    @change="handleConfigChange(config.configKey, $event)"
                  />
                  <div class="config-help" v-if="config.configHelp">
                    <i class="el-icon-info"></i>
                    <span>{{ config.configHelp }}</span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 配置导入对话框 -->
    <el-dialog title="导入配置" :visible.sync="importDialogVisible" width="500px">
      <el-upload
        class="upload-demo"
        drag
        action="#"
        :auto-upload="false"
        :on-change="handleFileChange"
        :file-list="fileList"
        accept=".json,.txt"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">只能上传json/txt文件，且不超过500kb</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="importDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="importConfig">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDevice } from "@/api/guide/device";
import { listGuideConfig, getGuideConfig, updateGuideConfig, resetGuideConfig, exportGuideConfig, importGuideConfig } from "@/api/guide/config";

export default {
  name: "GuideConfigParams",
  data() {
    return {
      // 设备列表
      deviceList: [],
      // 选中的设备ID
      selectedDeviceId: null,
      // 当前激活的标签页
      activeTab: 'basic',
      // 配置数据
      configData: {},
      // 配置分组
      configGroups: {
        basic: [],
        guide: [],
        voice: [],
        light: [],
        network: [],
        advanced: []
      },
      // 导入对话框显示状态
      importDialogVisible: false,
      // 文件列表
      fileList: []
    };
  },
  created() {
    this.loadDeviceList();
  },
  methods: {
    /** 加载设备列表 */
    loadDeviceList() {
      // 临时使用模拟数据
      this.deviceList = [
        {
          id: 1,
          deviceName: '智能导寻设备-001',
          deviceCode: 'GUIDE001',
          status: '0'
        },
        {
          id: 2,
          deviceName: '智能导寻设备-002',
          deviceCode: 'GUIDE002',
          status: '0'
        },
        {
          id: 3,
          deviceName: '智能导寻设备-003',
          deviceCode: 'GUIDE003',
          status: '0'
        }
      ];

      // 正式版本使用以下代码：
      // listDevice({ status: '0' }).then(response => {
      //   this.deviceList = response.rows || [];
      // }).catch(error => {
      //   console.error('获取设备列表失败:', error);
      //   this.deviceList = [];
      //   this.$modal.msgError('获取设备列表失败');
      // });
    },
    
    /** 设备变更处理 */
    handleDeviceChange(deviceId) {
      if (deviceId) {
        this.loadDeviceConfig(deviceId);
      } else {
        this.configData = {};
        this.configGroups = {
          basic: [],
          guide: [],
          voice: [],
          light: [],
          network: [],
          advanced: []
        };
      }
    },
    
    /** 加载设备配置 */
    loadDeviceConfig(deviceId) {
      // 临时使用模拟数据
      const mockConfigs = [
        {
          configKey: 'guide_brightness',
          configDesc: '导寻灯亮度',
          configType: 'number',
          minValue: 1,
          maxValue: 100,
          defaultValue: '80',
          configValue: '80',
          configGroup: 'guide',
          isRequired: '1',
          configHelp: '设置导寻灯的亮度，范围1-100'
        },
        {
          configKey: 'guide_speed',
          configDesc: '导寻速度',
          configType: 'select',
          configOptions: '[{"value":"slow","label":"慢速"},{"value":"normal","label":"正常"},{"value":"fast","label":"快速"}]',
          defaultValue: 'normal',
          configValue: 'normal',
          configGroup: 'guide',
          isRequired: '1',
          configHelp: '设置导寻灯的闪烁速度'
        },
        {
          configKey: 'voice_enabled',
          configDesc: '启用语音提示',
          configType: 'switch',
          defaultValue: '1',
          configValue: '1',
          configGroup: 'voice',
          isRequired: '0',
          configHelp: '是否启用语音导寻提示'
        },
        {
          configKey: 'voice_volume',
          configDesc: '语音音量',
          configType: 'number',
          minValue: 0,
          maxValue: 100,
          defaultValue: '70',
          configValue: '70',
          configGroup: 'voice',
          isRequired: '0',
          configHelp: '设置语音提示的音量大小'
        },
        {
          configKey: 'network_timeout',
          configDesc: '网络超时时间',
          configType: 'number',
          minValue: 1,
          maxValue: 60,
          defaultValue: '10',
          configValue: '10',
          configGroup: 'network',
          isRequired: '1',
          configHelp: '网络连接超时时间，单位：秒'
        }
      ];

      this.groupConfigs(mockConfigs);
      this.initConfigData(mockConfigs);

      // 正式版本使用以下代码：
      // listGuideConfig({ deviceId: deviceId }).then(response => {
      //   const configs = response.rows || [];
      //   this.groupConfigs(configs);
      //   this.initConfigData(configs);
      // }).catch(error => {
      //   console.error('获取设备配置失败:', error);
      //   this.$modal.msgError('获取设备配置失败，请检查后端接口是否正常');
      // });
    },
    
    /** 配置分组 */
    groupConfigs(configs) {
      this.configGroups = {
        basic: [],
        guide: [],
        voice: [],
        light: [],
        network: [],
        advanced: []
      };
      
      configs.forEach(config => {
        const group = config.configGroup || 'basic';
        if (this.configGroups[group]) {
          this.configGroups[group].push(config);
        } else {
          this.configGroups.basic.push(config);
        }
      });
    },
    
    /** 初始化配置数据 */
    initConfigData(configs) {
      this.configData = {};
      configs.forEach(config => {
        this.configData[config.configKey] = config.configValue || config.defaultValue;
      });
    },
    
    /** 配置变更处理 */
    handleConfigChange(configKey, value) {
      this.configData[configKey] = value;
    },
    
    /** 获取分组标签 */
    getGroupLabel(groupName) {
      const labels = {
        basic: '基础配置',
        guide: '导寻配置',
        voice: '语音配置',
        light: '灯光配置',
        network: '网络配置',
        advanced: '高级配置'
      };
      return labels[groupName] || groupName;
    },
    
    /** 解析选择框选项 */
    parseSelectOptions(optionsStr) {
      if (!optionsStr) return [];
      try {
        return JSON.parse(optionsStr);
      } catch (e) {
        // 如果不是JSON格式，尝试解析为简单的键值对
        return optionsStr.split(',').map(item => {
          const [value, label] = item.split(':');
          return { value: value.trim(), label: (label || value).trim() };
        });
      }
    },
    
    /** 保存所有配置 */
    saveAllConfig() {
      if (!this.selectedDeviceId) {
        this.$modal.msgWarning('请先选择设备');
        return;
      }

      // 临时模拟保存成功
      this.$modal.msgSuccess('配置保存成功');

      // 正式版本使用以下代码：
      // const configList = [];
      // Object.keys(this.configData).forEach(key => {
      //   configList.push({
      //     deviceId: this.selectedDeviceId,
      //     configKey: key,
      //     configValue: this.configData[key]
      //   });
      // });
      //
      // updateGuideConfig(configList).then(response => {
      //   this.$modal.msgSuccess('配置保存成功');
      // }).catch(error => {
      //   this.$modal.msgError('配置保存失败: ' + (error.msg || error.message || '未知错误'));
      // });
    },
    
    /** 重置为默认值 */
    resetToDefault() {
      if (!this.selectedDeviceId) {
        this.$modal.msgWarning('请先选择设备');
        return;
      }

      this.$modal.confirm('确认要重置所有配置为默认值吗？').then(() => {
        // 临时模拟重置成功
        this.$modal.msgSuccess('重置成功');
        this.loadDeviceConfig(this.selectedDeviceId);

        // 正式版本使用以下代码：
        // resetGuideConfig(this.selectedDeviceId).then(response => {
        //   this.$modal.msgSuccess('重置成功');
        //   this.loadDeviceConfig(this.selectedDeviceId);
        // });
      }).catch(() => {});
    },
    
    /** 导出配置 */
    exportConfig() {
      if (!this.selectedDeviceId) {
        this.$modal.msgWarning('请先选择设备');
        return;
      }

      // 临时模拟导出成功
      this.$modal.msgSuccess('配置导出成功');

      // 正式版本使用以下代码：
      // exportGuideConfig(this.selectedDeviceId).then(response => {
      //   this.download('guide/config/export', {
      //     deviceId: this.selectedDeviceId
      //   }, `guide_config_${this.selectedDeviceId}_${new Date().getTime()}.json`);
      // });
    },
    
    /** 显示导入对话框 */
    showImportDialog() {
      if (!this.selectedDeviceId) {
        this.$modal.msgWarning('请先选择设备');
        return;
      }
      this.importDialogVisible = true;
      this.fileList = [];
    },
    
    /** 文件变更处理 */
    handleFileChange(file, fileList) {
      this.fileList = fileList;
    },
    
    /** 导入配置 */
    importConfig() {
      if (this.fileList.length === 0) {
        this.$modal.msgWarning('请选择要导入的文件');
        return;
      }

      // 临时模拟导入成功
      this.$modal.msgSuccess('导入成功');
      this.importDialogVisible = false;
      this.loadDeviceConfig(this.selectedDeviceId);

      // 正式版本使用以下代码：
      // const file = this.fileList[0].raw;
      // const reader = new FileReader();
      // reader.onload = (e) => {
      //   try {
      //     const configData = JSON.parse(e.target.result);
      //     importGuideConfig(this.selectedDeviceId, configData).then(response => {
      //       this.$modal.msgSuccess('导入成功');
      //       this.importDialogVisible = false;
      //       this.loadDeviceConfig(this.selectedDeviceId);
      //     });
      //   } catch (error) {
      //     this.$modal.msgError('文件格式错误，请检查文件内容');
      //   }
      // };
      // reader.readAsText(file);
    }
  }
};
</script>

<style scoped>
.device-selector {
  margin-bottom: 20px;
}

.config-management {
  margin-bottom: 20px;
}

.config-help {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.config-help i {
  margin-right: 5px;
}

.upload-demo {
  text-align: center;
}
</style>
