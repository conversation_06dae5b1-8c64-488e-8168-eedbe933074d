<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 流程定义列表 -->
      <el-col :span="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>流程定义列表</span>
          </div>
          <div class="filter-container">
            <el-input v-model="queryParams.workflowName" placeholder="流程名称" clearable style="width: 200px" class="filter-item" @keyup.enter.native="handleQuery" />
            <el-select v-model="queryParams.businessType" placeholder="业务类型" clearable class="filter-item" style="width: 200px">
              <el-option v-for="item in businessTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-select v-model="queryParams.status" placeholder="状态" clearable class="filter-item" style="width: 120px">
              <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-button type="primary" icon="el-icon-search" class="filter-item" @click="handleQuery">搜索</el-button>
            <el-button type="success" icon="el-icon-plus" class="filter-item" @click="handleAdd" v-hasPermi="['approval:workflow:add']">新增</el-button>
          </div>

          <el-table v-loading="loading" :data="workflowList" border>
            <el-table-column label="序号" type="index" width="50" align="center" />
            <el-table-column label="流程名称" prop="workflowName" :show-overflow-tooltip="true" />
            <el-table-column label="流程编码" prop="workflowCode" :show-overflow-tooltip="true" />
            <el-table-column label="业务类型" prop="businessType" :show-overflow-tooltip="true" />
            <el-table-column label="描述" prop="description" :show-overflow-tooltip="true" />
            <el-table-column label="使用情况" prop="usageCount" align="center" width="100">
              <template slot-scope="scope">
                <el-popover placement="top" width="200" trigger="hover" v-if="scope.row.usageCount > 0">
                  <div>
                    <p>已被使用 {{ scope.row.usageCount }} 次</p>
                    <p>最近使用: {{ parseTime(scope.row.lastUsedTime) || '未知' }}</p>
                  </div>
                  <div slot="reference" class="usage-count">
                    <el-tag type="info">{{ scope.row.usageCount }}次</el-tag>
                  </div>
                </el-popover>
                <span v-else>未使用</span>
              </template>
            </el-table-column>
            <el-table-column label="状态" align="center" width="100">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.status === '0'" type="success">启用</el-tag>
                <el-tag v-else type="info">禁用</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="创建时间" align="center" width="180">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="280" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['approval:workflow:edit']">修改</el-button>
                <el-button type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
                <el-button type="text" icon="el-icon-view" @click="handleView(scope.row)" v-hasPermi="['approval:workflow:query']">查看</el-button>
                <el-button type="text" icon="el-icon-picture" @click="handleDiagram(scope.row)" v-hasPermi="['approval:workflow:query']">流程图</el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
        </el-card>
      </el-col>
    </el-row>

    <!-- 流程图对话框 -->
    <el-dialog :title="'流程图 - ' + diagramTitle" :visible.sync="diagramVisible" width="800px" append-to-body>
      <div class="workflow-diagram">
        <div id="diagram-container" style="width: 100%; height: 500px; border: 1px solid #ddd; background-color: #f5f7fa;"></div>
      </div>
      <div v-if="workflowNodes.length > 0" class="workflow-node-list">
        <h4>流程节点列表</h4>
        <el-table :data="workflowNodes" border size="mini">
          <el-table-column label="序号" type="index" width="50" align="center" />
          <el-table-column label="节点名称" prop="nodeName" min-width="120" />
          <el-table-column label="节点类型" align="center" width="100">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.nodeType === '0'" type="primary">开始</el-tag>
              <el-tag v-else-if="scope.row.nodeType === '1'" type="info">审批</el-tag>
              <el-tag v-else-if="scope.row.nodeType === '2'" type="success">结束</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="审批人" prop="approverName" min-width="150" :show-overflow-tooltip="true" />
          <el-table-column label="审批条件" align="center" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.approvalCondition === 'L' || scope.row.approvalCondition === 'ALL' ? '所有人' : '任意一人' }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listWorkflow, getWorkflow, delWorkflow, getWorkflowDiagram, getWorkflowOptions } from "@/api/approval/workflow";
import * as d3 from 'd3';
import { parseTime } from '@/utils/ruiyun'

export default {
  name: "Workflow",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 流程定义列表
      workflowList: [],
      // 业务类型选项
      businessTypeOptions: [],
      // 状态选项
      statusOptions: [
        { value: "0", label: "启用" },
        { value: "1", label: "禁用" }
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        workflowName: undefined,
        businessType: undefined,
        status: undefined
      },
      // 流程图显示
      diagramVisible: false,
      diagramTitle: "",
      workflowNodes: [],
      diagramData: null
    };
  },
  created() {
    this.getList();
    this.getBusinessTypeOptions();
  },
  methods: {
    parseTime,
    /** 查询流程定义列表 */
    getList() {
      this.loading = true;
      listWorkflow(this.queryParams).then(response => {
        console.log('=== 流程定义API响应 ===');
        console.log('完整响应:', response);
        console.log('响应代码:', response.code);
        console.log('响应消息:', response.msg);
        console.log('数据行:', response.rows);
        console.log('总数:', response.total);

        if (response.code === 200) {
          this.workflowList = response.rows || [];
          this.total = response.total || 0;
        } else {
          console.error('API返回错误:', response.msg);
          this.$modal.msgError('获取流程定义列表失败: ' + (response.msg || '未知错误'));
          this.workflowList = [];
          this.total = 0;
        }
        this.loading = false;
      }).catch(error => {
        console.error('API调用失败:', error);
        this.$modal.msgError('获取流程定义列表失败，请稍后重试');
        this.workflowList = [];
        this.total = 0;
        this.loading = false;
      });
    },
    /** 获取业务类型选项 */
    getBusinessTypeOptions() {
      // 从字典API获取业务类型数据
      getWorkflowOptions().then(res => {
        if (res.code === 200 && res.data) {
          // 使用Map对象确保每个业务类型只出现一次
          const businessTypeMap = new Map();
          res.data.forEach(item => {
            if (!businessTypeMap.has(item.businessType)) {
              businessTypeMap.set(item.businessType, {
                value: item.businessType,
                label: item.businessTypeName || item.businessType
              });
            }
          });
          
          // 将Map转换回数组
          this.businessTypeOptions = Array.from(businessTypeMap.values());
        }
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        workflowName: undefined,
        businessType: undefined,
        status: undefined
      };
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push('/approval/workflow/add');
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.$router.push({ 
        path: '/approval/workflow/edit', 
        query: { 
          workflowId: String(row.workflowId), 
          workflowCode: row.workflowCode,
          workflowName: row.workflowName,
          businessType: row.businessType,
          businessTypeName: row.businessTypeName,
          description: row.description,
          status: row.status
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除流程定义编号为"' + row.workflowId + '"的数据项?').then(() => {
        return delWorkflow(String(row.workflowId));
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.$router.push({ 
        path: '/approval/workflow/detail', 
        query: { 
          workflowId: String(row.workflowId) 
        }
      });
    },
    /** 流程图按钮操作 */
    handleDiagram(row) {
      this.diagramVisible = true;
      this.diagramTitle = row.workflowName;
      
      // 获取流程图数据，确保ID是字符串
      getWorkflowDiagram(String(row.workflowId)).then(res => {
        if (res.code === 200) {
          this.workflowNodes = res.data.nodes || [];
          this.diagramData = res.data;
          this.$nextTick(() => {
            this.renderDiagram();
          });
        }
      });
    },
    /** 渲染流程图 */
    renderDiagram() {
      if (!this.diagramData || !this.diagramData.nodes || this.diagramData.nodes.length === 0) {
        return;
      }

      // 清空容器
      const container = document.getElementById('diagram-container');
      container.innerHTML = '';

      // 使用D3.js渲染流程图 - 圆点样式
      const width = container.clientWidth;
      const height = container.clientHeight;
      const nodeWidth = 120;
      const nodeHeight = 80;  // 增加高度以容纳文字和圆点
      const circleRadius = 18; // 圆点半径
      const textHeight = 30;   // 文字区域高度
      const circleY = textHeight + 25; // 圆点Y位置（文字下方）

      const svg = d3.select('#diagram-container')
        .append('svg')
        .attr('width', width)
        .attr('height', height);

      const nodes = this.diagramData.nodes;
      const links = this.diagramData.links || [];

      // 计算节点位置
      const nodePositions = [];
      const xGap = Math.min(140, width / (nodes.length + 1)); // 增加间距

      nodes.forEach((node, index) => {
        nodePositions.push({
          x: xGap * (index + 1),
          y: height / 2
        });
      });

      // 绘制连线（圆点之间的连接）
      for (let i = 0; i < nodes.length - 1; i++) {
        const sourcePos = nodePositions[i];
        const targetPos = nodePositions[i + 1];

        // 计算圆点中心位置
        const sourceX = sourcePos.x;
        const sourceY = sourcePos.y - nodeHeight / 2 + circleY;
        const targetX = targetPos.x;
        const targetY = targetPos.y - nodeHeight / 2 + circleY;

        // 绘制直线连接
        svg.append('line')
          .attr('x1', sourceX + circleRadius)
          .attr('y1', sourceY)
          .attr('x2', targetX - circleRadius)
          .attr('y2', targetY)
          .attr('stroke', '#C0C4CC')
          .attr('stroke-width', 2)
          .attr('fill', 'none');
      }

      // 绘制节点（文字在上，圆点在下）
      nodes.forEach((node, index) => {
        const g = svg.append('g')
          .attr('transform', `translate(${nodePositions[index].x - nodeWidth / 2}, ${nodePositions[index].y - nodeHeight / 2})`);

        // 节点文本（上方）
        g.append('text')
          .attr('x', nodeWidth / 2)
          .attr('y', 15)
          .attr('text-anchor', 'middle')
          .attr('dominant-baseline', 'middle')
          .attr('fill', '#333')
          .attr('font-size', '12px')
          .attr('font-weight', 'bold')
          .text(node.nodeName);

        // 圆点（下方）
        const circle = g.append('circle')
          .attr('cx', nodeWidth / 2)
          .attr('cy', circleY)
          .attr('r', circleRadius)
          .attr('fill', this.getNodeColor(node.nodeType))
          .attr('stroke', '#fff')
          .attr('stroke-width', 2);

        // 为开始节点添加脉冲动画效果
        if (node.nodeType === '0') {
          // 添加脉冲外圈
          const pulseRing = g.append('circle')
            .attr('cx', nodeWidth / 2)
            .attr('cy', circleY)
            .attr('r', circleRadius)
            .attr('fill', 'none')
            .attr('stroke', this.getNodeColor(node.nodeType))
            .attr('stroke-width', 2)
            .attr('opacity', 0.8);

          // 创建脉冲动画函数
          const createPulseAnimation = (element) => {
            element
              .transition()
              .duration(1500)
              .ease(d3.easeLinear)
              .attr('r', circleRadius + 8)
              .attr('opacity', 0)
              .on('end', () => {
                // 动画结束后重新开始
                element
                  .attr('r', circleRadius)
                  .attr('opacity', 0.8);
                // 递归调用创建循环动画
                createPulseAnimation(element);
              });
          };

          // 启动脉冲动画
          createPulseAnimation(pulseRing);
        }

        // 为审批节点添加旋转动画效果
        if (node.nodeType === '1') {
          // 添加旋转外圈
          const rotateRing = g.append('circle')
            .attr('cx', nodeWidth / 2)
            .attr('cy', circleY)
            .attr('r', circleRadius + 3)
            .attr('fill', 'none')
            .attr('stroke', this.getNodeColor(node.nodeType))
            .attr('stroke-width', 2)
            .attr('stroke-dasharray', '5,3')
            .attr('opacity', 0.6);

          // 创建旋转动画函数
          const createRotateAnimation = (element) => {
            element
              .transition()
              .duration(3000)
              .ease(d3.easeLinear)
              .attrTween('transform', () => {
                return d3.interpolateString(
                  `rotate(0 ${nodeWidth / 2} ${circleY})`,
                  `rotate(360 ${nodeWidth / 2} ${circleY})`
                );
              })
              .on('end', () => {
                // 动画结束后重新开始
                createRotateAnimation(element);
              });
          };

          // 启动旋转动画
          createRotateAnimation(rotateRing);
        }

        // 状态图标（圆点内）
        g.append('text')
          .attr('x', nodeWidth / 2)
          .attr('y', circleY + 1)
          .attr('text-anchor', 'middle')
          .attr('dominant-baseline', 'middle')
          .attr('fill', '#fff')
          .attr('font-size', '12px')
          .attr('font-weight', 'bold')
          .text(this.getNodeIcon(node.nodeType));
      });
    },
    /** 获取节点颜色 */
    getNodeColor(nodeType) {
      switch (nodeType) {
        case '0': return '#409EFF'; // 开始节点
        case '1': return '#909399'; // 审批节点
        case '2': return '#67C23A'; // 结束节点
        default: return '#909399';
      }
    },
    /** 获取节点图标 */
    getNodeIcon(nodeType) {
      switch (nodeType) {
        case '0': return '●'; // 开始节点
        case '1': return '◐'; // 审批节点
        case '2': return '✓'; // 结束节点
        default: return '●';
      }
    }
  }
};
</script>

<style scoped>
.workflow-diagram {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}
.workflow-node-list {
  margin-top: 20px;
}
</style> 