<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="page-title">
        <h2><i class="el-icon-delete"></i> 客户端清理管理</h2>
        <p>检测和清理重复的EMQX客户端注册,维护系统的唯一性约束</p>
      </div>
      <div class="page-actions">
        <el-button type="primary" icon="el-icon-search" @click="checkDuplicates" :loading="checkLoading">
          检查重复
        </el-button>
        <el-button type="warning" icon="el-icon-view" @click="previewCleanup" :loading="previewLoading">
          预览清理
        </el-button>
        <el-button type="success" icon="el-icon-setting" @click="establishConstraints">
          建立约束
        </el-button>
      </div>
    </div>

    <!-- 安全检查卡片 -->
    <el-card class="safety-card" v-if="safetyCheck">
      <div slot="header">
        <span><i class="el-icon-shield"></i> 安全检查</span>
        <el-tag :type="safetyCheck.safeToClean ? 'success' : 'warning'" size="small">
          {{ safetyCheck.riskLevel }}
        </el-tag>
      </div>
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="safety-item">
            <div class="safety-icon" :class="safetyCheck.hasActiveConnections ? 'danger' : 'success'">
              <i class="el-icon-connection"></i>
            </div>
            <div class="safety-content">
              <div class="safety-label">活跃连接</div>
              <div class="safety-value">{{ safetyCheck.hasActiveConnections ? '有' : '无' }}</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="safety-item">
            <div class="safety-icon" :class="safetyCheck.isPeakHours ? 'warning' : 'success'">
              <i class="el-icon-time"></i>
            </div>
            <div class="safety-content">
              <div class="safety-label">业务高峰期</div>
              <div class="safety-value">{{ safetyCheck.isPeakHours ? '是' : '否' }}</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="safety-item">
            <div class="safety-icon" :class="safetyCheck.hasRecentBackup ? 'success' : 'danger'">
              <i class="el-icon-folder"></i>
            </div>
            <div class="safety-content">
              <div class="safety-label">最近备份</div>
              <div class="safety-value">{{ safetyCheck.hasRecentBackup ? '有' : '无' }}</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="safety-item">
            <div class="safety-icon" :class="safetyCheck.safeToClean ? 'success' : 'warning'">
              <i class="el-icon-check"></i>
            </div>
            <div class="safety-content">
              <div class="safety-label">安全状态</div>
              <div class="safety-value">{{ safetyCheck.safeToClean ? '安全' : '注意' }}</div>
            </div>
          </div>
        </el-col>
      </el-row>
      <div class="safety-recommendation">
        <strong>建议:</strong>{{ safetyCheck.recommendation }}
      </div>
      <div v-if="safetyCheck.suggestions && safetyCheck.suggestions.length > 0" class="safety-suggestions">
        <strong>注意事项:</strong>
        <ul>
          <li v-for="suggestion in safetyCheck.suggestions" :key="suggestion">{{ suggestion }}</li>
        </ul>
      </div>
    </el-card>

    <!-- 重复检查结果 -->
    <el-card class="duplicate-card" v-if="duplicateCheck">
      <div slot="header">
        <span><i class="el-icon-warning"></i> 重复客户端检查结果</span>
        <el-tag :type="duplicateCheck.hasDuplicates ? 'danger' : 'success'" size="small">
          {{ duplicateCheck.hasDuplicates ? '发现重复' : '无重复' }}
        </el-tag>
      </div>
      <el-row :gutter="20" class="duplicate-stats">
        <el-col :span="6">
          <div class="stat-box">
            <div class="stat-value">{{ duplicateCheck.totalClients }}</div>
            <div class="stat-label">总客户端数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-box">
            <div class="stat-value">{{ duplicateCheck.duplicateCount }}</div>
            <div class="stat-label">重复客户端</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-box">
            <div class="stat-value">{{ duplicateCheck.uniqueCount }}</div>
            <div class="stat-label">唯一客户端</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-box">
            <div class="stat-value">{{ formatTime(duplicateCheck.checkTime) }}</div>
            <div class="stat-label">检查时间</div>
          </div>
        </el-col>
      </el-row>
      
      <div v-if="duplicateCheck.hasDuplicates" class="duplicate-list">
        <h4>重复客户端列表:</h4>
        <el-table :data="duplicateCheck.duplicateClients" size="small" max-height="200">
          <el-table-column prop="clientId" label="客户端ID" />
          <el-table-column prop="status" label="状态" width="100">
            <template slot-scope="scope">
              <el-tag type="warning" size="mini">待清理</el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <div class="duplicate-recommendation">
        <strong>建议:</strong>{{ duplicateCheck.recommendation }}
      </div>
    </el-card>

    <!-- 清理预览结果 -->
    <el-card class="preview-card" v-if="previewResult">
      <div slot="header">
        <span><i class="el-icon-view"></i> 清理预览结果</span>
        <div style="float: right;">
          <el-button
            type="danger"
            size="small"
            icon="el-icon-delete"
            @click="executeCleanup"
            :disabled="!previewResult.duplicatesFound || !(safetyCheck && safetyCheck.safeToClean)"
            :loading="executeLoading"
          >
            执行清理
          </el-button>
        </div>
      </div>
      
      <el-row :gutter="20" class="preview-stats">
        <el-col :span="6">
          <div class="preview-stat">
            <div class="preview-icon total">
              <i class="el-icon-files"></i>
            </div>
            <div class="preview-content">
              <div class="preview-value">{{ previewResult.totalFound }}</div>
              <div class="preview-label">发现总数</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="preview-stat">
            <div class="preview-icon duplicates">
              <i class="el-icon-warning"></i>
            </div>
            <div class="preview-content">
              <div class="preview-value">{{ previewResult.duplicatesFound }}</div>
              <div class="preview-label">重复数量</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="preview-stat">
            <div class="preview-icon retained">
              <i class="el-icon-check"></i>
            </div>
            <div class="preview-content">
              <div class="preview-value">{{ previewResult.uniqueRetained }}</div>
              <div class="preview-label">保留数量</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="preview-stat">
            <div class="preview-icon status">
              <i class="el-icon-info"></i>
            </div>
            <div class="preview-content">
              <div class="preview-value">{{ previewResult.status }}</div>
              <div class="preview-label">预览状态</div>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 详细列表 -->
      <el-tabs v-model="activeTab" class="preview-tabs">
        <el-tab-pane label="待删除客户端" name="removed">
          <el-table :data="previewResult.removedClients" size="small" max-height="300">
            <el-table-column prop="clientId" label="客户端ID" />
            <el-table-column label="操作" width="100">
              <template slot-scope="scope">
                <el-tag type="danger" size="mini">删除</el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="保留客户端" name="retained">
          <el-table :data="previewResult.retainedClients" size="small" max-height="300">
            <el-table-column prop="clientId" label="客户端ID" />
            <el-table-column label="操作" width="100">
              <template slot-scope="scope">
                <el-tag type="success" size="mini">保留</el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 清理历史 -->
    <el-card class="history-card">
      <div slot="header">
        <span><i class="el-icon-time"></i> 清理历史记录</span>
        <el-button type="text" icon="el-icon-refresh" @click="loadHistory">刷新</el-button>
      </div>
      
      <el-table
        :data="cleanupHistory"
        v-loading="historyLoading"
        element-loading-text="加载中..."
        style="width: 100%"
      >
        <el-table-column prop="cleanupId" label="清理ID" width="200">
          <template slot-scope="scope">
            <span class="cleanup-id">{{ scope.row.cleanupId.substring(0, 8) }}...</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="cleanupTime" label="清理时间" width="160">
          <template slot-scope="scope">
            {{ formatTime(scope.row.cleanupTime) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="totalFound" label="发现总数" width="100" />
        
        <el-table-column prop="duplicatesRemoved" label="删除数量" width="100">
          <template slot-scope="scope">
            <span class="removed-count">{{ scope.row.duplicatesRemoved }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="uniqueRetained" label="保留数量" width="100">
          <template slot-scope="scope">
            <span class="retained-count">{{ scope.row.uniqueRetained }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.status)" size="mini">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="message" label="说明" min-width="150" />
        
        <el-table-column label="操作" width="120" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-view"
              @click="viewHistoryDetail(scope.row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 历史详情对话框 -->
    <el-dialog
      title="清理记录详情"
      :visible.sync="historyDetailVisible"
      width="700px"
      :before-close="closeHistoryDetail"
    >
      <div v-if="selectedHistory" class="history-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="清理ID" :span="2">
            {{ selectedHistory.cleanupId }}
          </el-descriptions-item>
          <el-descriptions-item label="清理时间">
            {{ formatTime(selectedHistory.cleanupTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedHistory.status)">
              {{ getStatusText(selectedHistory.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发现总数">
            {{ selectedHistory.totalFound }}
          </el-descriptions-item>
          <el-descriptions-item label="删除数量">
            {{ selectedHistory.duplicatesRemoved }}
          </el-descriptions-item>
          <el-descriptions-item label="保留数量">
            {{ selectedHistory.uniqueRetained }}
          </el-descriptions-item>
          <el-descriptions-item label="说明" :span="2">
            {{ selectedHistory.message }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 详细列表 -->
        <div class="detail-lists">
          <div class="detail-section">
            <h4>删除的客户端:</h4>
            <el-tag
              v-for="client in selectedHistory.removedClients"
              :key="client"
              type="danger"
              size="small"
              style="margin: 2px;"
            >
              {{ client }}
            </el-tag>
          </div>
          
          <div class="detail-section">
            <h4>保留的客户端:</h4>
            <el-tag
              v-for="client in selectedHistory.retainedClients"
              :key="client"
              type="success"
              size="small"
              style="margin: 2px;"
            >
              {{ client }}
            </el-tag>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 约束建立对话框 -->
    <el-dialog
      title="建立唯一性约束"
      :visible.sync="constraintDialogVisible"
      width="600px"
      :before-close="closeConstraintDialog"
    >
      <div class="constraint-content">
        <el-alert
          title="约束建立说明"
          type="info"
          :closable="false"
          style="margin-bottom: 20px;"
        >
          <div slot="description">
            <p>建立唯一性约束将在以下层面防止重复注册:</p>
            <ul>
              <li>数据库层面: 创建唯一索引约束</li>
              <li>应用层面: 添加重复检查逻辑</li>
              <li>EMQX层面: 配置客户端唯一性验证</li>
            </ul>
          </div>
        </el-alert>
        
        <div v-if="constraintResult" class="constraint-result">
          <h4>约束建立结果:</h4>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="数据库约束">
              <el-tag :type="constraintResult.databaseConstraints ? 'success' : 'danger'">
                {{ constraintResult.databaseConstraints ? '成功' : '失败' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="应用层验证">
              <el-tag :type="constraintResult.applicationValidation ? 'success' : 'danger'">
                {{ constraintResult.applicationValidation ? '成功' : '失败' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="EMQX验证">
              <el-tag :type="constraintResult.emqxValidation ? 'success' : 'danger'">
                {{ constraintResult.emqxValidation ? '成功' : '失败' }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeConstraintDialog">关闭</el-button>
        <el-button
          type="primary"
          @click="submitEstablishConstraints"
          :loading="constraintLoading"
          v-if="!constraintResult"
        >
          建立约束
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  previewCleanup,
  executeCleanup,
  checkDuplicates,
  performSafetyCheck,
  getCleanupHistory,
  getCleanupRecord,
  establishUniqueConstraints
} from '@/api/emqx/cleanup'

export default {
  name: 'Cleanup',
  data() {
    return {
      checkLoading: false,
      previewLoading: false,
      executeLoading: false,
      historyLoading: false,
      constraintLoading: false,
      
      // 安全检查结果
      safetyCheck: null,
      
      // 重复检查结果
      duplicateCheck: null,
      
      // 预览结果
      previewResult: null,
      activeTab: 'removed',
      
      // 清理历史
      cleanupHistory: [],
      
      // 历史详情对话框
      historyDetailVisible: false,
      selectedHistory: null,
      
      // 约束对话框
      constraintDialogVisible: false,
      constraintResult: null
    }
  },
  
  created() {
    this.loadInitialData()
  },
  
  methods: {
    async loadInitialData() {
      await Promise.all([
        this.loadSafetyCheck(),
        this.loadHistory()
      ])
    },
    
    async loadSafetyCheck() {
      try {
        const response = await performSafetyCheck()
        if (response.code === 200) {
          this.safetyCheck = response.data
        }
      } catch (error) {
        console.error('加载安全检查失败:', error)
      }
    },
    
    async checkDuplicates() {
      this.checkLoading = true
      try {
        const response = await checkDuplicates()
        if (response.code === 200) {
          this.duplicateCheck = response.data
          this.$message.success('重复检查完成')
        } else {
          this.$message.error('检查失败: ' + response.msg)
        }
      } catch (error) {
        this.$message.error('检查失败')
      } finally {
        this.checkLoading = false
      }
    },
    
    async previewCleanup() {
      this.previewLoading = true
      try {
        const response = await previewCleanup()
        if (response.code === 200) {
          this.previewResult = response.data
          this.activeTab = 'removed'
          this.$message.success('预览完成')
        } else {
          this.$message.error('预览失败: ' + response.msg)
        }
      } catch (error) {
        this.$message.error('预览失败')
      } finally {
        this.previewLoading = false
      }
    },
    
    async executeCleanup() {
      if (!(this.safetyCheck && this.safetyCheck.safeToClean)) {
        this.$message.warning('当前环境不安全,建议稍后执行清理操作')
        return
      }
      
      this.$confirm(
        `确定要执行清理操作吗？将删除 ${(this.previewResult && this.previewResult.duplicatesFound) || 0} 个重复客户端。`,
        '确认清理',
        {
          type: 'warning',
          confirmButtonText: '确认执行',
          cancelButtonText: '取消'
        }
      ).then(async () => {
        this.executeLoading = true
        try {
          const response = await executeCleanup(true)
          if (response.code === 200) {
            const result = response.data
            this.$message.success(`清理完成: 删除 ${result.duplicatesRemoved} 个,保留 ${result.uniqueRetained} 个`)
            this.previewResult = null
            this.duplicateCheck = null
            this.loadHistory()
            this.loadSafetyCheck()
          } else {
            this.$message.error('清理失败: ' + response.msg)
          }
        } catch (error) {
          this.$message.error('清理失败')
        } finally {
          this.executeLoading = false
        }
      })
    },
    
    async loadHistory() {
      this.historyLoading = true
      try {
        const response = await getCleanupHistory()
        if (response.code === 200) {
          this.cleanupHistory = response.data.history || []
        }
      } catch (error) {
        console.error('加载清理历史失败:', error)
      } finally {
        this.historyLoading = false
      }
    },
    
    viewHistoryDetail(row) {
      this.selectedHistory = row
      this.historyDetailVisible = true
    },
    
    closeHistoryDetail() {
      this.historyDetailVisible = false
      this.selectedHistory = null
    },
    
    establishConstraints() {
      this.constraintDialogVisible = true
      this.constraintResult = null
    },
    
    closeConstraintDialog() {
      this.constraintDialogVisible = false
      this.constraintResult = null
    },
    
    async submitEstablishConstraints() {
      this.constraintLoading = true
      try {
        const response = await establishUniqueConstraints()
        if (response.code === 200) {
          this.constraintResult = response.data
          if (response.data.success) {
            this.$message.success('唯一性约束建立成功')
          } else {
            this.$message.warning('部分约束建立失败,请查看详情')
          }
        } else {
          this.$message.error('建立约束失败: ' + response.msg)
        }
      } catch (error) {
        this.$message.error('建立约束失败')
      } finally {
        this.constraintLoading = false
      }
    },
    
    // 工具方法
    getStatusType(status) {
      const statusMap = {
        'COMPLETED': 'success',
        'FAILED': 'danger',
        'RUNNING': 'warning'
      }
      return statusMap[status] || 'info'
    },
    
    getStatusText(status) {
      const statusMap = {
        'COMPLETED': '完成',
        'FAILED': '失败',
        'RUNNING': '运行中'
      }
      return statusMap[status] || status
    },
    
    formatTime(time) {
      if (!time) return '-'
      return new Date(time).toLocaleString()
    }
  }
}
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title h2 {
  margin: 0;
  color: #303133;
}

.page-title p {
  margin: 5px 0 0 0;
  color: #909399;
  font-size: 14px;
}

.safety-card, .duplicate-card, .preview-card, .history-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.safety-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.safety-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
  margin-right: 12px;
}

.safety-icon.success { background: #67c23a; }
.safety-icon.warning { background: #e6a23c; }
.safety-icon.danger { background: #f56c6c; }

.safety-label {
  font-size: 12px;
  color: #909399;
}

.safety-value {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.safety-recommendation, .safety-suggestions {
  margin-top: 15px;
  padding: 10px;
  background: #f0f9ff;
  border-left: 4px solid #409eff;
  border-radius: 4px;
}

.duplicate-stats, .preview-stats {
  margin-bottom: 20px;
}

.stat-box {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.duplicate-list, .duplicate-recommendation {
  margin-top: 15px;
}

.preview-stat {
  display: flex;
  align-items: center;
  padding: 15px;
  background: white;
  border: 1px solid #ebeef5;
  border-radius: 8px;
}

.preview-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  margin-right: 15px;
}

.preview-icon.total { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.preview-icon.duplicates { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.preview-icon.retained { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.preview-icon.status { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

.preview-value {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
}

.preview-label {
  font-size: 14px;
  color: #909399;
}

.preview-tabs {
  margin-top: 20px;
}

.cleanup-id {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.removed-count {
  color: #f56c6c;
  font-weight: bold;
}

.retained-count {
  color: #67c23a;
  font-weight: bold;
}

.history-detail {
  padding: 10px 0;
}

.detail-lists {
  margin-top: 20px;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section h4 {
  margin-bottom: 10px;
  color: #303133;
}

.constraint-content {
  padding: 10px 0;
}

.constraint-result {
  margin-top: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
}
</style>
