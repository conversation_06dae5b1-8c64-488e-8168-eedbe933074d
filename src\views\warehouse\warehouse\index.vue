<template>
  <div class="app-container">
    <el-card class="filter-container" shadow="hover">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
        <el-form-item label="仓库编号" prop="warehouseCode">
          <el-input
            v-model="queryParams.warehouseCode"
            placeholder="请输入仓库编号"
            clearable
            prefix-icon="el-icon-document"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="仓库名称" prop="warehouseName">
          <el-input
            v-model="queryParams.warehouseName"
            placeholder="请输入仓库名称"
            clearable
            prefix-icon="el-icon-office-building"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="仓库类型" prop="warehouseType">
          <el-select v-model="queryParams.warehouseType" placeholder="请选择仓库类型" clearable>
            <el-option label="原材料仓库" value="0" />
            <el-option label="成品仓库" value="1" />
            <el-option label="半成品仓库" value="2" />
            <el-option label="备品仓库" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="危险等级" prop="hazardLevel">
          <el-select v-model="queryParams.hazardLevel" placeholder="请选择危险等级" clearable>
            <el-option label="安全" value="0" />
            <el-option label="低危" value="1" />
            <el-option label="中危" value="2" />
            <el-option label="高危" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option label="正常" value="0" />
            <el-option label="停用" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="table-container" shadow="hover" style="margin-top: 10px;">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['warehouse:warehouse:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['warehouse:warehouse:edit']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['warehouse:warehouse:remove']"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['warehouse:warehouse:export']"
          >导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="warehouseList" @selection-change="handleSelectionChange" border stripe highlight-current-row>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="仓库ID" align="center" prop="id" width="80" />
        <el-table-column label="仓库编号" align="center" prop="warehouseCode" min-width="120" />
        <el-table-column label="仓库名称" align="center" prop="warehouseName" min-width="120" show-overflow-tooltip />
        <el-table-column label="仓库类型" align="center" prop="warehouseType" width="100">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.warehouseType == '0'" type="success">原材料仓库</el-tag>
            <el-tag v-else-if="scope.row.warehouseType == '1'" type="primary">成品仓库</el-tag>
            <el-tag v-else-if="scope.row.warehouseType == '2'" type="warning">半成品仓库</el-tag>
            <el-tag v-else-if="scope.row.warehouseType == '3'" type="info">备品仓库</el-tag>
            <el-tag v-else>{{ scope.row.warehouseType }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="面积(㎡)" align="center" prop="area" width="100" />
        <el-table-column label="位置" align="center" prop="location" min-width="120" show-overflow-tooltip />
        <el-table-column label="负责人" align="center" prop="managerId" width="100" />
        <el-table-column label="联系方式" align="center" prop="contact" min-width="120" />
        <el-table-column label="危险等级" align="center" prop="hazardLevel" width="90">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.hazardLevel == '0'" type="success">安全</el-tag>
            <el-tag v-else-if="scope.row.hazardLevel == '1'" type="info">低危</el-tag>
            <el-tag v-else-if="scope.row.hazardLevel == '2'" type="warning">中危</el-tag>
            <el-tag v-else-if="scope.row.hazardLevel == '3'" type="danger">高危</el-tag>
            <el-tag v-else>{{ scope.row.hazardLevel }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status" width="80">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="'0'"
              :inactive-value="'1'"
              @change="handleStatusChange(scope.row)"
              active-color="#13ce66"
              inactive-color="#ff4949">
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
              v-hasPermi="['warehouse:warehouse:query']"
            >详情</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['warehouse:warehouse:edit']"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['warehouse:warehouse:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改仓库信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="仓库编号" prop="warehouseCode">
              <el-input v-model="form.warehouseCode" placeholder="请输入仓库编号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="仓库名称" prop="warehouseName">
              <el-input v-model="form.warehouseName" placeholder="请输入仓库名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="仓库类型" prop="warehouseType">
              <el-select v-model="form.warehouseType" placeholder="请选择仓库类型" style="width: 100%">
                <el-option label="原材料仓库" value="0" />
                <el-option label="成品仓库" value="1" />
                <el-option label="半成品仓库" value="2" />
                <el-option label="备品仓库" value="3" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="面积(平方米)" prop="area">
              <el-input-number v-model="form.area" :min="0" :precision="2" :step="10" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="位置" prop="location">
              <el-input v-model="form.location" placeholder="请输入位置" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="负责人" prop="managerId">
              <el-select v-model="form.managerId" placeholder="请选择负责人" style="width: 100%">
                <el-option v-for="item in managerOptions" :key="item.userId" :label="item.userName" :value="item.userId" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系方式" prop="contact">
              <el-input v-model="form.contact" placeholder="请输入联系方式" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="危险等级" prop="hazardLevel">
              <el-radio-group v-model="form.hazardLevel">
                <el-radio label="0">安全</el-radio>
                <el-radio label="1">低危</el-radio>
                <el-radio label="2">中危</el-radio>
                <el-radio label="3">高危</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio label="0">正常</el-radio>
                <el-radio label="1">停用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 仓库详情对话框 -->
    <el-dialog title="仓库详情" :visible.sync="viewDialog" width="700px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="仓库编号">{{ viewForm.warehouseCode }}</el-descriptions-item>
        <el-descriptions-item label="仓库名称">{{ viewForm.warehouseName }}</el-descriptions-item>
        <el-descriptions-item label="仓库类型">
          <el-tag v-if="viewForm.warehouseType == '0'" type="success">原材料仓库</el-tag>
          <el-tag v-else-if="viewForm.warehouseType == '1'" type="primary">成品仓库</el-tag>
          <el-tag v-else-if="viewForm.warehouseType == '2'" type="warning">半成品仓库</el-tag>
          <el-tag v-else-if="viewForm.warehouseType == '3'" type="info">备品仓库</el-tag>
          <el-tag v-else>{{ viewForm.warehouseType }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="面积(平方米)">{{ viewForm.area }}</el-descriptions-item>
        <el-descriptions-item label="位置" :span="2">{{ viewForm.location }}</el-descriptions-item>
        <el-descriptions-item label="负责人">{{ viewForm.managerName }}</el-descriptions-item>
        <el-descriptions-item label="联系方式">{{ viewForm.contact }}</el-descriptions-item>
        <el-descriptions-item label="危险等级">
          <el-tag v-if="viewForm.hazardLevel == '0'" type="success">安全</el-tag>
          <el-tag v-else-if="viewForm.hazardLevel == '1'" type="info">低危</el-tag>
          <el-tag v-else-if="viewForm.hazardLevel == '2'" type="warning">中危</el-tag>
          <el-tag v-else-if="viewForm.hazardLevel == '3'" type="danger">高危</el-tag>
          <el-tag v-else>{{ viewForm.hazardLevel }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag v-if="viewForm.status == '0'" type="success">正常</el-tag>
          <el-tag v-else-if="viewForm.status == '1'" type="danger">停用</el-tag>
          <el-tag v-else>{{ viewForm.status }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ viewForm.remark }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewDialog = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listWarehouse, getWarehouse, delWarehouse, addWarehouse, updateWarehouse, changeStatus } from "@/api/warehouse/warehouse"
import { listUser } from "@/api/system/user"

export default {
  name: "WarehouseManagement",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 仓库信息表格数据
      warehouseList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示详情弹出层
      viewDialog: false,
      // 详情表单
      viewForm: {},
      // 负责人选项
      managerOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        warehouseCode: null,
        warehouseName: null,
        warehouseType: null,
        area: null,
        location: null,
        managerId: null,
        contact: null,
        hazardLevel: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        warehouseCode: [
          { required: true, message: "仓库编号不能为空", trigger: "blur" }
        ],
        warehouseName: [
          { required: true, message: "仓库名称不能为空", trigger: "blur" }
        ],
        warehouseType: [
          { required: true, message: "仓库类型不能为空", trigger: "change" }
        ],
        area: [
          { required: true, message: "面积不能为空", trigger: "blur" }
        ],
        location: [
          { required: true, message: "位置不能为空", trigger: "blur" }
        ],
        managerId: [
          { required: true, message: "负责人不能为空", trigger: "change" }
        ],
        contact: [
          { required: true, message: "联系方式不能为空", trigger: "blur" },
          { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
        ],
        hazardLevel: [
          { required: true, message: "危险等级不能为空", trigger: "change" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ],
      }
    }
  },
  created() {
    this.getList()
    this.getManagerList()
  },
  methods: {
    /** 查询仓库信息列表 */
    getList() {
      this.loading = true
      listWarehouse(this.queryParams).then(response => {
        this.warehouseList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 查询负责人列表 */
    getManagerList() {
      listUser().then(response => {
        this.managerOptions = response.rows
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        warehouseCode: null,
        warehouseName: null,
        warehouseType: "0",
        area: 0,
        location: null,
        managerId: null,
        contact: null,
        hazardLevel: "0",
        status: "0",
        remark: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加仓库信息"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getWarehouse(id).then(response => {
        this.form = JSON.parse(JSON.stringify(response.data))
        this.open = true
        this.title = "修改仓库信息"
      })
    },
    /** 查看详情按钮操作 */
    handleView(row) {
      this.viewForm = {}
      getWarehouse(row.id).then(response => {
        this.viewForm = response.data
        // 查找负责人名称
        const manager = this.managerOptions.find(item => item.userId === this.viewForm.managerId)
        if (manager) {
          this.viewForm.managerName = manager.userName
        }
        this.viewDialog = true
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          const formData = JSON.parse(JSON.stringify(this.form))
          
          if (formData.id != null) {
            updateWarehouse(formData).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            }).catch(error => {
              console.error("更新失败:", error)
              this.$modal.msgError("修改失败：" + (error.message || "未知错误"))
            })
          } else {
            addWarehouse(formData).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            }).catch(error => {
              console.error("新增失败:", error)
              this.$modal.msgError("新增失败：" + (error.message || "未知错误"))
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除仓库信息编号为"' + ids + '"的数据项？').then(() => {
        return delWarehouse(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('warehouse/warehouse/export', {
        ...this.queryParams
      }, `warehouse_${new Date().getTime()}.xlsx`)
    },
    /** 状态修改 */
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用"
      this.$modal.confirm('确认要"' + text + '""' + row.warehouseName + '"仓库吗?').then(() => {
        return changeStatus(row.id, row.status)
      }).then(() => {
        this.$modal.msgSuccess(text + "成功")
      }).catch(() => {
        row.status = row.status === "0" ? "1" : "0"
      })
    }
  }
}
</script>

<style scoped>
.filter-container {
  margin-bottom: 10px;
}
.table-container {
  margin-bottom: 20px;
}
.el-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>
