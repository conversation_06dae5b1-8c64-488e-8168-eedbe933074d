<template>
  <div class="app-container">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <div class="page-title">
        <h2><i class="el-icon-connection"></i> 服务器客户端管理</h2>
        <p>管理EMQX服务器客户端的注册、状态和连接</p>
      </div>
      <div class="page-actions">
        <el-button type="primary" icon="el-icon-refresh" @click="refreshData" :loading="loading">
          刷新数据
        </el-button>
        <el-button type="success" icon="el-icon-plus" @click="showRegisterDialog">
          手动注册
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon registered">
              <i class="el-icon-connection"></i>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.totalServers }}</div>
              <div class="stat-label">总服务器数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon online">
              <i class="el-icon-success"></i>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.onlineServers }}</div>
              <div class="stat-label">在线服务器</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon offline">
              <i class="el-icon-error"></i>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.offlineServers }}</div>
              <div class="stat-label">离线服务器</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon messages">
              <i class="el-icon-message"></i>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.totalMessages }}</div>
              <div class="stat-label">总消息数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 服务器列表 -->
    <el-card class="server-list-card">
      <div slot="header" class="card-header">
        <span><i class="el-icon-server"></i> 服务器列表</span>
        <div class="header-actions">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索服务器ID或名称"
            prefix-icon="el-icon-search"
            style="width: 250px; margin-right: 10px;"
            clearable
            @input="filterServers"
          />
          <el-select
            v-model="statusFilter"
            placeholder="状态筛选"
            style="width: 120px;"
            clearable
            @change="filterServers"
          >
            <el-option label="在线" value="ONLINE" />
            <el-option label="离线" value="OFFLINE" />
            <el-option label="已注册" value="REGISTERED" />
          </el-select>
        </div>
      </div>

      <el-table
        :data="filteredServers"
        v-loading="loading"
        element-loading-text="加载中..."
        style="width: 100%"
        @row-click="handleRowClick"
      >
        <el-table-column prop="serverId" label="服务器ID" min-width="180">
          <template slot-scope="scope">
            <div class="server-id-cell">
              <el-tag :type="getServerTypeTag(scope.row.serverId)" size="mini">
                {{ getServerTypeName(scope.row.serverId) }}
              </el-tag>
              <span class="server-id">{{ scope.row.serverId }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="serverName" label="服务器名称" min-width="150" />
        
        <el-table-column prop="clientId" label="客户端ID" min-width="200">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.clientId" placement="top">
              <span class="client-id">{{ scope.row.clientId }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="connectionCount" label="连接数" width="80" />
        
        <el-table-column prop="messageCount" label="消息数" width="100">
          <template slot-scope="scope">
            {{ formatNumber(scope.row.messageCount) }}
          </template>
        </el-table-column>

        <el-table-column prop="lastHeartbeat" label="最后心跳" width="160">
          <template slot-scope="scope">
            {{ formatTime(scope.row.lastHeartbeat) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-view"
              @click.stop="viewServerDetail(scope.row)"
            >
              详情
            </el-button>
            <el-button
              size="mini"
              type="success"
              icon="el-icon-connection"
              @click.stop="testConnection(scope.row)"
              :loading="scope.row.testing"
            >
              测试
            </el-button>
            <el-dropdown @command="handleCommand" trigger="click">
              <el-button size="mini" type="text">
                更多<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :command="{action: 'restart', row: scope.row}">
                  <i class="el-icon-refresh"></i> 重启
                </el-dropdown-item>
                <el-dropdown-item :command="{action: 'unregister', row: scope.row}" divided>
                  <i class="el-icon-delete"></i> 注销
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 服务器详情对话框 -->
    <el-dialog
      title="服务器详情"
      :visible.sync="detailDialogVisible"
      width="800px"
      :before-close="closeDetailDialog"
    >
      <div v-if="selectedServer" class="server-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="服务器ID">
            {{ selectedServer.serverId }}
          </el-descriptions-item>
          <el-descriptions-item label="服务器名称">
            {{ selectedServer.serverName }}
          </el-descriptions-item>
          <el-descriptions-item label="客户端ID" :span="2">
            {{ selectedServer.clientId }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedServer.status)">
              {{ getStatusText(selectedServer.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="连接数">
            {{ selectedServer.connectionCount }}
          </el-descriptions-item>
          <el-descriptions-item label="消息数">
            {{ formatNumber(selectedServer.messageCount) }}
          </el-descriptions-item>
          <el-descriptions-item label="注册时间">
            {{ formatTime(selectedServer.registerTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">
            {{ selectedServer.description || '无描述' }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 订阅主题 -->
        <div class="topic-section">
          <h4><i class="el-icon-download"></i> 订阅主题</h4>
          <el-tag
            v-for="topic in selectedServer.subscribeTopics"
            :key="topic"
            type="info"
            size="small"
            style="margin: 2px;"
          >
            {{ topic }}
          </el-tag>
        </div>

        <!-- 发布主题 -->
        <div class="topic-section">
          <h4><i class="el-icon-upload2"></i> 发布主题</h4>
          <el-tag
            v-for="topic in selectedServer.publishTopics"
            :key="topic"
            type="warning"
            size="small"
            style="margin: 2px;"
          >
            {{ topic }}
          </el-tag>
        </div>
      </div>
    </el-dialog>

    <!-- 手动注册对话框 -->
    <el-dialog
      title="手动注册服务器"
      :visible.sync="registerDialogVisible"
      width="600px"
      :before-close="closeRegisterDialog"
    >
      <el-form
        ref="registerForm"
        :model="registerForm"
        :rules="registerRules"
        label-width="120px"
      >
        <el-form-item label="服务器ID" prop="serverId">
          <el-input v-model="registerForm.serverId" placeholder="请输入服务器ID" />
        </el-form-item>
        <el-form-item label="服务器名称" prop="serverName">
          <el-input v-model="registerForm.serverName" placeholder="请输入服务器名称" />
        </el-form-item>
        <el-form-item label="客户端ID" prop="clientId">
          <el-input v-model="registerForm.clientId" placeholder="请输入客户端ID" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="registerForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入描述信息"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeRegisterDialog">取消</el-button>
        <el-button type="primary" @click="submitRegister" :loading="registerLoading">
          注册
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getMqttServers,
  getServerStats,
  registerMqttServer,
  unregisterMqttServer,
  testServerConnection,
  getServerDetail
} from '@/api/emqx/serverRegistry'

export default {
  name: 'ServerRegistry',
  data() {
    return {
      loading: false,
      servers: [],
      filteredServers: [],
      searchKeyword: '',
      statusFilter: '',
      
      // 统计数据
      stats: {
        totalServers: 0,
        onlineServers: 0,
        offlineServers: 0,
        totalMessages: 0
      },
      
      // 详情对话框
      detailDialogVisible: false,
      selectedServer: null,
      
      // 注册对话框
      registerDialogVisible: false,
      registerLoading: false,
      registerForm: {
        serverId: '',
        serverName: '',
        clientId: '',
        description: ''
      },
      registerRules: {
        serverId: [
          { required: true, message: '请输入服务器ID', trigger: 'blur' }
        ],
        serverName: [
          { required: true, message: '请输入服务器名称', trigger: 'blur' }
        ],
        clientId: [
          { required: true, message: '请输入客户端ID', trigger: 'blur' }
        ]
      }
    }
  },
  
  created() {
    this.loadData()
  },
  
  methods: {
    async loadData() {
      this.loading = true
      try {
        await Promise.all([
          this.loadServers(),
          this.loadStats()
        ])
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },
    
    async loadServers() {
      try {
        const response = await getMqttServers()
        if (response.code === 200) {
          this.servers = response.data || []
          this.filterServers()
        }
      } catch (error) {
        console.error('加载服务器列表失败:', error)
      }
    },
    
    async loadStats() {
      try {
        const response = await getServerStats()
        if (response.code === 200) {
          this.stats = response.data || this.stats
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },
    
    filterServers() {
      let filtered = this.servers
      
      // 关键词搜索
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        filtered = filtered.filter(server =>
          server.serverId.toLowerCase().includes(keyword) ||
          server.serverName.toLowerCase().includes(keyword) ||
          server.clientId.toLowerCase().includes(keyword)
        )
      }
      
      // 状态筛选
      if (this.statusFilter) {
        filtered = filtered.filter(server => server.status === this.statusFilter)
      }
      
      this.filteredServers = filtered
    },
    
    refreshData() {
      this.loadData()
    },
    
    handleRowClick(row) {
      this.viewServerDetail(row)
    },
    
    async viewServerDetail(row) {
      this.selectedServer = row
      this.detailDialogVisible = true
    },
    
    closeDetailDialog() {
      this.detailDialogVisible = false
      this.selectedServer = null
    },
    
    async testConnection(row) {
      this.$set(row, 'testing', true)
      try {
        const response = await testServerConnection(row.serverId)
        if (response.code === 200) {
          this.$message.success('连接测试成功')
        } else {
          this.$message.error('连接测试失败: ' + response.msg)
        }
      } catch (error) {
        this.$message.error('连接测试失败')
      } finally {
        this.$set(row, 'testing', false)
      }
    },
    
    handleCommand(command) {
      const { action, row } = command
      switch (action) {
        case 'restart':
          this.restartServer(row)
          break
        case 'unregister':
          this.unregisterServer(row)
          break
      }
    },
    
    restartServer(row) {
      this.$confirm('确定要重启此服务器吗？', '确认操作', {
        type: 'warning'
      }).then(() => {
        this.$message.info('重启功能开发中...')
      })
    },
    
    unregisterServer(row) {
      this.$confirm('确定要注销此服务器吗？注销后将无法接收消息。', '确认操作', {
        type: 'warning'
      }).then(async () => {
        try {
          const response = await unregisterMqttServer(row.serverId)
          if (response.code === 200) {
            this.$message.success('注销成功')
            this.loadData()
          } else {
            this.$message.error('注销失败: ' + response.msg)
          }
        } catch (error) {
          this.$message.error('注销失败')
        }
      })
    },
    
    showRegisterDialog() {
      this.registerDialogVisible = true
    },
    
    closeRegisterDialog() {
      this.registerDialogVisible = false
      this.resetRegisterForm()
    },
    
    resetRegisterForm() {
      this.$refs.registerForm?.resetFields()
      this.registerForm = {
        serverId: '',
        serverName: '',
        clientId: '',
        description: ''
      }
    },
    
    submitRegister() {
      this.$refs.registerForm.validate(async (valid) => {
        if (valid) {
          this.registerLoading = true
          try {
            const response = await registerMqttServer(this.registerForm)
            if (response.code === 200) {
              this.$message.success('注册成功')
              this.closeRegisterDialog()
              this.loadData()
            } else {
              this.$message.error('注册失败: ' + response.msg)
            }
          } catch (error) {
            this.$message.error('注册失败')
          } finally {
            this.registerLoading = false
          }
        }
      })
    },
    
    // 工具方法
    getServerTypeTag(serverId) {
      if (!serverId) return ''
      if (serverId.includes('video')) return 'primary'
      if (serverId.includes('access')) return 'success'
      if (serverId.includes('guide')) return 'warning'
      if (serverId.includes('dwms')) return 'info'
      if (serverId.includes('outmis')) return 'danger'
      return ''
    },
    
    getServerTypeName(serverId) {
      if (!serverId) return '未知'
      if (serverId.includes('video')) return '视频'
      if (serverId.includes('access')) return '门禁'
      if (serverId.includes('guide')) return '导寻'
      if (serverId.includes('dwms')) return '库房'
      if (serverId.includes('outmis')) return '出库'
      return '未知'
    },
    
    getStatusType(status) {
      const statusMap = {
        'ONLINE': 'success',
        'OFFLINE': 'danger',
        'REGISTERED': 'info'
      }
      return statusMap[status] || 'info'
    },
    
    getStatusText(status) {
      const statusMap = {
        'ONLINE': '在线',
        'OFFLINE': '离线',
        'REGISTERED': '已注册'
      }
      return statusMap[status] || status
    },
    
    formatNumber(num) {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num?.toString() || '0'
    },
    
    formatTime(time) {
      if (!time) return '-'
      return new Date(time).toLocaleString()
    }
  }
}
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title h2 {
  margin: 0;
  color: #303133;
}

.page-title p {
  margin: 5px 0 0 0;
  color: #909399;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  margin-right: 15px;
}

.stat-icon.registered { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.stat-icon.online { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.stat-icon.offline { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.stat-icon.messages { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.server-list-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
}

.server-id-cell {
  display: flex;
  align-items: center;
}

.server-id {
  margin-left: 8px;
  font-family: 'Courier New', monospace;
}

.client-id {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.server-detail {
  padding: 10px 0;
}

.topic-section {
  margin-top: 20px;
}

.topic-section h4 {
  margin-bottom: 10px;
  color: #303133;
}
</style>
