<template>
  <div class="storage-location-test">
    <el-card header="存放位置选择器测试">
      <el-alert
        title="测试说明"
        type="info"
        description="这是存放位置选择器的独立测试页面，用于验证组件功能是否正常。"
        style="margin-bottom: 20px;"
        show-icon
      />

      <el-form :model="testForm" label-width="120px">
        <storage-location-selector
          v-model="testForm.storageLocation"
          @change="handleStorageLocationChange"
        />

        <el-form-item label="当前选择">
          <pre>{{ JSON.stringify(testForm.storageLocation, null, 2) }}</pre>
        </el-form-item>

        <el-form-item label="组件状态">
          <el-tag v-if="componentLoaded" type="success">组件已加载</el-tag>
          <el-tag v-else type="warning">组件加载中...</el-tag>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="testSubmit">测试提交</el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button type="info" @click="openConsole">打开控制台</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import StorageLocationSelector from "@/components/StorageLocationSelector.vue"

export default {
  name: "StorageLocationTest",
  components: {
    StorageLocationSelector
  },
  data() {
    return {
      testForm: {
        storageLocation: {
          storageType: null,
          locationCode: null,
          containerCode: null
        }
      },
      componentLoaded: false
    }
  },

  mounted() {
    this.componentLoaded = true
    console.log('StorageLocationTest: 测试页面已加载')
  },
  methods: {
    handleStorageLocationChange(storageLocation) {
      console.log('存放位置变化:', storageLocation)
    },
    
    testSubmit() {
      console.log('提交数据:', this.testForm.storageLocation)
      this.$message.success('测试提交成功，请查看控制台')
    },
    
    resetForm() {
      this.testForm.storageLocation = {
        storageType: null,
        locationCode: null,
        containerCode: null
      }
      console.log('StorageLocationTest: 表单已重置')
    },

    openConsole() {
      this.$message.info('请按F12打开浏览器开发者工具查看控制台日志')
    }
  }
}
</script>

<style scoped>
.storage-location-test {
  padding: 20px;
}

pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
}
</style>
