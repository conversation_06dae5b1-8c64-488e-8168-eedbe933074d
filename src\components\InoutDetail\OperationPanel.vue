<template>
  <div class="operation-panel">
    <!-- 操作状态显示 -->
    <el-card class="status-card" shadow="hover">
      <div slot="header">
        <span class="card-title">
          <i class="el-icon-info"></i>
          操作状态
        </span>
      </div>
      
      <div class="status-content">
        <el-steps :active="currentStep" finish-status="success" align-center>
          <el-step title="审批检查" :status="stepStatus.approval"></el-step>
          <el-step title="开始操作" :status="stepStatus.start"></el-step>
          <el-step title="进行中" :status="stepStatus.progress"></el-step>
          <el-step title="完成" :status="stepStatus.complete"></el-step>
        </el-steps>
        
        <div class="status-info" v-if="operationInfo">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="当前状态">
              <el-tag :type="getStatusTagType(operationInfo.status)">
                {{ getStatusText(operationInfo.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="操作类型">
              {{ operationInfo.operationType === '1' ? '入库' : '出库' }}
            </el-descriptions-item>
            <el-descriptions-item label="操作人员">
              {{ operationInfo.operatorName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="开始时间">
              {{ operationInfo.startTime || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-card>

    <!-- 操作控制 -->
    <el-card class="control-card" shadow="hover">
      <div slot="header">
        <span class="card-title">
          <i class="el-icon-setting"></i>
          操作控制
        </span>
      </div>
      
      <div class="control-content">
        <!-- 审批状态检查 -->
        <div class="control-section" v-if="!approvalChecked">
          <h4>1. 审批状态检查</h4>
          <p class="section-desc">检查关联单据的审批状态，确保已审批通过</p>
          <el-button 
            type="primary" 
            @click="checkApprovalStatus" 
            :loading="checking"
            icon="el-icon-search"
          >
            检查审批状态
          </el-button>
        </div>

        <!-- 人员认证 -->
        <div class="control-section" v-if="approvalChecked && !authenticated">
          <h4>2. 人员认证</h4>
          <p class="section-desc">进行刷卡或刷脸认证，确认操作人员身份</p>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card class="auth-card">
                <div slot="header">刷卡认证</div>
                <el-input 
                  v-model="authData.cardId" 
                  placeholder="请刷卡或输入卡号"
                  @keyup.enter.native="authenticateByCard"
                />
                <el-button 
                  type="success" 
                  @click="authenticateByCard" 
                  :loading="cardAuthLoading"
                  style="width: 100%; margin-top: 10px;"
                >
                  刷卡认证
                </el-button>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card class="auth-card">
                <div slot="header">刷脸认证</div>
                <div class="face-auth-area">
                  <i class="el-icon-camera" style="font-size: 48px; color: #ccc;"></i>
                  <p>点击进行人脸识别</p>
                </div>
                <el-button 
                  type="success" 
                  @click="authenticateByFace" 
                  :loading="faceAuthLoading"
                  style="width: 100%;"
                >
                  刷脸认证
                </el-button>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 开始操作 -->
        <div class="control-section" v-if="authenticated && !operationStarted">
          <h4>3. 开始操作</h4>
          <p class="section-desc">开始执行出入库操作</p>
          
          <el-form :model="operationForm" label-width="100px">
            <el-form-item label="操作类型">
              <el-radio-group v-model="operationForm.operationType">
                <el-radio label="1">入库</el-radio>
                <el-radio label="2">出库</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="存储位置" v-if="operationForm.operationType === '1'">
              <el-input v-model="operationForm.storageLocation" placeholder="请输入存储位置" />
            </el-form-item>
          </el-form>
          
          <el-button 
            type="primary" 
            @click="startOperation" 
            :loading="startLoading"
            icon="el-icon-video-play"
            size="medium"
          >
            开始{{ operationForm.operationType === '1' ? '入库' : '出库' }}操作
          </el-button>
        </div>

        <!-- 操作进度 -->
        <div class="control-section" v-if="operationStarted && !operationCompleted">
          <h4>4. 操作进度</h4>
          <p class="section-desc">实时更新操作进度和数据</p>
          
          <el-form :model="progressForm" label-width="100px">
            <el-form-item label="实际数量">
              <el-input-number 
                v-model="progressForm.actualQuantity" 
                :min="0" 
                :precision="3"
                style="width: 100%"
                @change="updateProgress"
              />
            </el-form-item>
            <el-form-item label="实际重量">
              <el-input-number 
                v-model="progressForm.actualWeight" 
                :min="0" 
                :precision="3"
                style="width: 100%"
                @change="updateProgress"
              />
            </el-form-item>
            <el-form-item label="称重数据">
              <el-input 
                v-model="progressForm.sensorData" 
                placeholder="传感器数据"
                readonly
              />
              <el-button @click="readSensorData" size="mini" style="margin-left: 10px;">
                读取传感器
              </el-button>
            </el-form-item>
          </el-form>
          
          <div class="progress-actions">
            <el-button 
              type="success" 
              @click="completeOperation" 
              :loading="completeLoading"
              icon="el-icon-check"
            >
              完成操作
            </el-button>
            <el-button 
              type="danger" 
              @click="handleError" 
              icon="el-icon-close"
            >
              异常处理
            </el-button>
          </div>
        </div>

        <!-- 操作完成 -->
        <div class="control-section" v-if="operationCompleted">
          <h4>5. 操作完成</h4>
          <el-result
            icon="success"
            title="操作完成"
            sub-title="出入库操作已成功完成"
          >
            <template slot="extra">
              <el-button type="primary" @click="viewOperationLog">查看操作日志</el-button>
              <el-button @click="resetOperation">重新操作</el-button>
            </template>
          </el-result>
        </div>
      </div>
    </el-card>

    <!-- 称重对比结果 -->
    <el-card class="weight-card" shadow="hover" v-if="weightResult">
      <div slot="header">
        <span class="card-title">
          <i class="el-icon-scale-to-original"></i>
          称重对比结果
        </span>
      </div>
      
      <div class="weight-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="计划重量">
            {{ weightResult.plannedWeight }} kg
          </el-descriptions-item>
          <el-descriptions-item label="实际重量">
            {{ weightResult.actualWeight }} kg
          </el-descriptions-item>
          <el-descriptions-item label="偏差率">
            <span :class="{'text-danger': weightResult.deviationRate > 5}">
              {{ weightResult.deviationRate }}%
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="对比结果">
            <el-tag :type="getWeightResultTagType(weightResult.compareResult)">
              {{ weightResult.compareResultText }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>
  </div>
</template>

<script>
import { 
  checkApprovalStatus, 
  authenticateByCard, 
  authenticateByFace,
  startOperation,
  updateOperationProgress,
  completeOperation,
  handleOperationError,
  processWeightSensor
} from "@/api/inout/operation";

export default {
  name: "OperationPanel",
  props: {
    detailId: {
      type: Number,
      required: true
    },
    billNo: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      // 操作状态
      currentStep: 0,
      stepStatus: {
        approval: 'wait',
        start: 'wait',
        progress: 'wait',
        complete: 'wait'
      },
      
      // 状态标志
      approvalChecked: false,
      authenticated: false,
      operationStarted: false,
      operationCompleted: false,
      
      // 加载状态
      checking: false,
      cardAuthLoading: false,
      faceAuthLoading: false,
      startLoading: false,
      completeLoading: false,
      
      // 认证数据
      authData: {
        cardId: '',
        faceData: ''
      },
      
      // 操作表单
      operationForm: {
        operationType: '1',
        storageLocation: ''
      },
      
      // 进度表单
      progressForm: {
        actualQuantity: 0,
        actualWeight: 0,
        sensorData: ''
      },
      
      // 操作信息
      operationInfo: null,
      
      // 称重结果
      weightResult: null
    };
  },
  methods: {
    /** 检查审批状态 */
    async checkApprovalStatus() {
      this.checking = true;
      try {
        const response = await checkApprovalStatus(this.billNo);
        if (response.code === 200) {
          this.approvalChecked = true;
          this.currentStep = 1;
          this.stepStatus.approval = 'finish';
          this.$message.success('审批状态检查通过');
        } else {
          this.$message.error(response.msg);
        }
      } catch (error) {
        this.$message.error('检查审批状态失败');
      } finally {
        this.checking = false;
      }
    },
    
    /** 刷卡认证 */
    async authenticateByCard() {
      if (!this.authData.cardId) {
        this.$message.warning('请输入卡号');
        return;
      }
      
      this.cardAuthLoading = true;
      try {
        const response = await authenticateByCard({
          cardId: this.authData.cardId,
          deviceId: 'DEVICE_001'
        });
        
        if (response.code === 200) {
          this.authenticated = true;
          this.currentStep = 2;
          this.stepStatus.start = 'process';
          this.$message.success('刷卡认证成功');
        } else {
          this.$message.error(response.msg);
        }
      } catch (error) {
        this.$message.error('刷卡认证失败');
      } finally {
        this.cardAuthLoading = false;
      }
    },
    
    /** 刷脸认证 */
    async authenticateByFace() {
      this.faceAuthLoading = true;
      try {
        // 模拟人脸识别
        const response = await authenticateByFace({
          faceData: 'FACE_DATA_MOCK',
          deviceId: 'DEVICE_001'
        });
        
        if (response.code === 200) {
          this.authenticated = true;
          this.currentStep = 2;
          this.stepStatus.start = 'process';
          this.$message.success('刷脸认证成功');
        } else {
          this.$message.error(response.msg);
        }
      } catch (error) {
        this.$message.error('刷脸认证失败');
      } finally {
        this.faceAuthLoading = false;
      }
    },
    
    /** 开始操作 */
    async startOperation() {
      this.startLoading = true;
      try {
        const response = await startOperation({
          detailId: this.detailId,
          operationType: this.operationForm.operationType,
          storageLocation: this.operationForm.storageLocation
        });
        
        if (response.code === 200) {
          this.operationStarted = true;
          this.currentStep = 3;
          this.stepStatus.start = 'finish';
          this.stepStatus.progress = 'process';
          this.operationInfo = response.data;
          this.$message.success('操作开始成功');
        } else {
          this.$message.error(response.msg);
        }
      } catch (error) {
        this.$message.error('开始操作失败');
      } finally {
        this.startLoading = false;
      }
    },
    
    /** 更新进度 */
    async updateProgress() {
      try {
        await updateOperationProgress(this.detailId, '2', this.progressForm);
      } catch (error) {
        console.error('更新进度失败', error);
      }
    },
    
    /** 完成操作 */
    async completeOperation() {
      this.completeLoading = true;
      try {
        const response = await completeOperation({
          detailId: this.detailId,
          actualQuantity: this.progressForm.actualQuantity,
          actualWeight: this.progressForm.actualWeight,
          sensorData: this.progressForm.sensorData
        });
        
        if (response.code === 200) {
          this.operationCompleted = true;
          this.currentStep = 4;
          this.stepStatus.progress = 'finish';
          this.stepStatus.complete = 'finish';
          this.weightResult = response.data.weightResult;
          this.$message.success('操作完成成功');
        } else {
          this.$message.error(response.msg);
        }
      } catch (error) {
        this.$message.error('完成操作失败');
      } finally {
        this.completeLoading = false;
      }
    },
    
    /** 异常处理 */
    async handleError() {
      this.$prompt('请输入异常信息', '异常处理', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(({ value }) => {
        return handleOperationError(this.detailId, value);
      }).then(() => {
        this.$message.success('异常处理完成');
        this.resetOperation();
      });
    },
    
    /** 读取传感器数据 */
    readSensorData() {
      // 模拟读取传感器数据
      this.progressForm.sensorData = `{"weight": ${Math.random() * 100 + 50}, "timestamp": "${new Date().toISOString()}"}`;
      this.progressForm.actualWeight = JSON.parse(this.progressForm.sensorData).weight;
    },
    
    /** 查看操作日志 */
    viewOperationLog() {
      this.$router.push({
        path: '/inout/log',
        query: { detailId: this.detailId }
      });
    },
    
    /** 重置操作 */
    resetOperation() {
      this.currentStep = 0;
      this.stepStatus = {
        approval: 'wait',
        start: 'wait',
        progress: 'wait',
        complete: 'wait'
      };
      this.approvalChecked = false;
      this.authenticated = false;
      this.operationStarted = false;
      this.operationCompleted = false;
      this.operationInfo = null;
      this.weightResult = null;
    },
    
    /** 获取状态文本 */
    getStatusText(status) {
      const map = {
        '0': '未处理',
        '1': '处理中',
        '2': '已完成',
        '3': '已取消',
        '4': '异常'
      };
      return map[status] || '未知';
    },
    
    /** 获取状态标签类型 */
    getStatusTagType(status) {
      const map = {
        '0': 'info',
        '1': 'warning',
        '2': 'success',
        '3': 'info',
        '4': 'danger'
      };
      return map[status] || 'info';
    },
    
    /** 获取称重结果标签类型 */
    getWeightResultTagType(result) {
      const map = {
        '1': 'success',
        '2': 'warning',
        '3': 'danger'
      };
      return map[result] || 'info';
    }
  }
};
</script>

<style scoped>
.operation-panel {
  padding: 20px;
}

.status-card,
.control-card,
.weight-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.status-content,
.control-content,
.weight-content {
  padding: 10px 0;
}

.status-info {
  margin-top: 20px;
}

.control-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.control-section:last-child {
  border-bottom: none;
}

.control-section h4 {
  color: #409EFF;
  margin-bottom: 10px;
}

.section-desc {
  color: #909399;
  margin-bottom: 15px;
  font-size: 14px;
}

.auth-card {
  text-align: center;
}

.face-auth-area {
  height: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed #ddd;
  border-radius: 6px;
  margin-bottom: 10px;
  cursor: pointer;
}

.face-auth-area:hover {
  border-color: #409EFF;
}

.progress-actions {
  margin-top: 20px;
}

.text-danger {
  color: #F56C6C;
}

.el-steps {
  margin-bottom: 20px;
}
</style>
