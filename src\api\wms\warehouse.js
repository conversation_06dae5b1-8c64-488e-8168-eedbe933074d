import request from '@/utils/request'

// 查询仓库列表
export function listWarehouse(query) {
  return request({
    url: '/wms/warehouse/list',
    method: 'get',
    params: query
  })
}

// 查询仓库详细
export function getWarehouse(warehouseId) {
  return request({
    url: '/wms/warehouse/' + warehouseId,
    method: 'get'
  })
}

// 新增仓库
export function addWarehouse(data) {
  return request({
    url: '/wms/warehouse',
    method: 'post',
    data: data
  })
}

// 修改仓库
export function updateWarehouse(data) {
  return request({
    url: '/wms/warehouse',
    method: 'put',
    data: data
  })
}

// 删除仓库
export function delWarehouse(warehouseId) {
  return request({
    url: '/wms/warehouse/' + warehouseId,
    method: 'delete'
  })
}

// 查询仓库区域列表
export function listWarehouseArea(query) {
  return request({
    url: '/wms/warehouse/area/list',
    method: 'get',
    params: query
  })
}

// 查询仓库货位列表
export function listWarehouseLocation(query) {
  return request({
    url: '/wms/warehouse/location/list',
    method: 'get',
    params: query
  })
}

// 查询所有仓库（不分页）
export function getAllWarehouse() {
  return request({
    url: '/wms/warehouse/all',
    method: 'get'
  })
}

// 查询仓库下所有区域（不分页）
export function getAreasByWarehouse(warehouseId) {
  return request({
    url: '/wms/warehouse/area/byWarehouse/' + warehouseId,
    method: 'get'
  })
}

// 查询区域下所有货位（不分页）
export function getLocationsByArea(areaId) {
  return request({
    url: '/wms/warehouse/location/byArea/' + areaId,
    method: 'get'
  })
} 