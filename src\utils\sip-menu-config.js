// SIP Server 管理系统菜单配置

export const sipMenuConfig = {
  // 主菜单配置
  mainMenu: {
    path: '/sip',
    name: '<PERSON><PERSON>',
    title: 'SIP Server',
    icon: 'phone',
    roles: ['admin', 'sip_admin'],
    redirect: '/sip/dashboard'
  },
  
  // 子菜单配置
  subMenus: [
    {
      path: 'dashboard',
      name: 'SipDashboard',
      title: '系统概览',
      icon: 'dashboard',
      component: '@/views/sip/dashboard/index',
      meta: {
        affix: true,
        noCache: false,
        description: '查看 SIP 服务器系统整体状态和关键指标'
      }
    },
    {
      path: 'devices',
      name: 'SipDevices',
      title: '设备管理',
      icon: 'component',
      component: '@/views/sip/devices/index',
      meta: {
        perms: ['sip:device:list'],
        description: '管理 GB28181 设备的注册、状态和控制'
      }
    },
    {
      path: 'sessions',
      name: 'SipSessions',
      title: '会话管理',
      icon: 'link',
      component: '@/views/sip/sessions/index',
      meta: {
        perms: ['sip:session:list'],
        description: '管理 SIP 会话和媒体流连接'
      }
    },
    {
      path: 'media',
      name: 'SipMedia',
      title: '媒体管理',
      icon: 'video-play',
      component: '@/views/sip/media/index',
      meta: {
        perms: ['sip:media:list'],
        description: '管理 RTP 媒体流和端口分配'
      }
    },
    {
      path: 'messages',
      name: 'SipMessages',
      title: '消息管理',
      icon: 'message',
      component: '@/views/sip/messages/index',
      meta: {
        perms: ['sip:message:list'],
        description: '管理 SIP 协议消息和 MANSCDP+XML 交互'
      }
    },
    {
      path: 'monitoring',
      name: 'SipMonitoring',
      title: '实时监控',
      icon: 'monitor',
      component: '@/views/sip/monitoring/index',
      meta: {
        perms: ['sip:monitor:view'],
        description: '实时监控 SIP 服务器性能和设备状态'
      }
    },
    {
      path: 'settings',
      name: 'SipSettings',
      title: '系统设置',
      icon: 'setting',
      component: '@/views/sip/settings/index',
      meta: {
        perms: ['sip:settings:manage'],
        description: '配置 SIP 服务器系统参数和 GB28181 协议设置'
      }
    }
  ]
}

// 权限配置
export const sipPermissions = {
  // 设备管理权限
  device: {
    list: 'sip:device:list',
    view: 'sip:device:view',
    control: 'sip:device:control',
    query: 'sip:device:query',
    offline: 'sip:device:offline',
    export: 'sip:device:export'
  },
  
  // 会话管理权限
  session: {
    list: 'sip:session:list',
    view: 'sip:session:view',
    terminate: 'sip:session:terminate',
    export: 'sip:session:export'
  },
  
  // 媒体管理权限
  media: {
    list: 'sip:media:list',
    view: 'sip:media:view',
    control: 'sip:media:control',
    export: 'sip:media:export'
  },
  
  // 消息管理权限
  message: {
    list: 'sip:message:list',
    view: 'sip:message:view',
    send: 'sip:message:send',
    export: 'sip:message:export'
  },
  
  // 监控权限
  monitor: {
    view: 'sip:monitor:view',
    export: 'sip:monitor:export'
  },
  
  // 系统设置权限
  settings: {
    view: 'sip:settings:view',
    manage: 'sip:settings:manage'
  }
}

// 设备类型配置
export const sipDeviceTypes = {
  CAMERA: {
    name: '摄像头',
    icon: 'el-icon-video-camera',
    color: '#409EFF'
  },
  NVR: {
    name: '网络录像机',
    icon: 'el-icon-monitor',
    color: '#67C23A'
  },
  DVR: {
    name: '数字录像机',
    icon: 'el-icon-monitor',
    color: '#E6A23C'
  },
  PLATFORM: {
    name: '平台',
    icon: 'el-icon-platform-eleme',
    color: '#F56C6C'
  },
  ALARM: {
    name: '报警设备',
    icon: 'el-icon-warning',
    color: '#909399'
  },
  OTHER: {
    name: '其他',
    icon: 'el-icon-more',
    color: '#606266'
  }
}

// 会话类型配置
export const sipSessionTypes = {
  INVITE: {
    name: '邀请',
    color: 'primary'
  },
  PLAY: {
    name: '播放',
    color: 'success'
  },
  PLAYBACK: {
    name: '回放',
    color: 'info'
  },
  DOWNLOAD: {
    name: '下载',
    color: 'warning'
  },
  TALK: {
    name: '对讲',
    color: 'danger'
  }
}

// 菜单图标配置
export const sipMenuIcons = {
  dashboard: 'el-icon-dashboard',
  component: 'el-icon-monitor',
  link: 'el-icon-link',
  'video-play': 'el-icon-video-play',
  message: 'el-icon-message',
  monitor: 'el-icon-monitor',
  setting: 'el-icon-setting',
  phone: 'el-icon-phone'
}

// 快速操作配置
export const sipQuickActions = {
  dashboard: [
    {
      name: '设备管理',
      icon: 'el-icon-monitor',
      path: '/sip/devices',
      permission: 'sip:device:list'
    },
    {
      name: '会话管理',
      icon: 'el-icon-link',
      path: '/sip/sessions',
      permission: 'sip:session:list'
    },
    {
      name: '媒体管理',
      icon: 'el-icon-video-play',
      path: '/sip/media',
      permission: 'sip:media:list'
    },
    {
      name: '系统设置',
      icon: 'el-icon-setting',
      path: '/sip/settings',
      permission: 'sip:settings:manage'
    }
  ]
}

export default sipMenuConfig
