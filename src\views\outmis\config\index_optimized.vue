<template>
  <div class="app-container">
    <!-- 配置分类标签 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="设备参数" name="device">
        <device-config ref="deviceConfig" />
      </el-tab-pane>
      <el-tab-pane label="EMQX配置" name="emqx">
        <emqx-config ref="emqxConfig" />
      </el-tab-pane>
      <el-tab-pane label="系统配置" name="system">
        <system-config ref="systemConfig" />
      </el-tab-pane>
      <el-tab-pane label="报警配置" name="alarm">
        <alarm-config ref="alarmConfig" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
// 设备参数配置组件
const DeviceConfig = {
  template: `
    <div>
      <el-form :model="form" :rules="rules" ref="form" label-width="120px">
        <el-card class="config-card">
          <div slot="header" class="clearfix">
            <span>称重设备配置</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="resetWeightConfig">重置</el-button>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="采样频率" prop="sampleRate">
                <el-input-number v-model="form.sampleRate" :min="1" :max="1000" />
                <span style="margin-left: 10px;">Hz</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="稳定阈值" prop="stableThreshold">
                <el-input-number v-model="form.stableThreshold" :min="0.1" :max="10" :precision="2" />
                <span style="margin-left: 10px;">kg</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="零点校准" prop="zeroCalibration">
                <el-input-number v-model="form.zeroCalibration" :precision="3" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="满量程校准" prop="fullScaleCalibration">
                <el-input-number v-model="form.fullScaleCalibration" :precision="3" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <el-card class="config-card">
          <div slot="header" class="clearfix">
            <span>RFID设备配置</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="resetRfidConfig">重置</el-button>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="读取功率" prop="readPower">
                <el-slider v-model="form.readPower" :min="10" :max="30" show-stops />
                <span>{{ form.readPower }}dBm</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="读取间隔" prop="readInterval">
                <el-input-number v-model="form.readInterval" :min="100" :max="5000" />
                <span style="margin-left: 10px;">ms</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="天线配置" prop="antennaConfig">
                <el-checkbox-group v-model="form.antennaConfig">
                  <el-checkbox label="1">天线1</el-checkbox>
                  <el-checkbox label="2">天线2</el-checkbox>
                  <el-checkbox label="3">天线3</el-checkbox>
                  <el-checkbox label="4">天线4</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="过滤重复" prop="filterDuplicate">
                <el-switch v-model="form.filterDuplicate" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <el-card class="config-card">
          <div slot="header" class="clearfix">
            <span>通信配置</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="心跳间隔" prop="heartbeatInterval">
                <el-input-number v-model="form.heartbeatInterval" :min="10" :max="300" />
                <span style="margin-left: 10px;">秒</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="连接超时" prop="connectionTimeout">
                <el-input-number v-model="form.connectionTimeout" :min="5" :max="60" />
                <span style="margin-left: 10px;">秒</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="重连次数" prop="reconnectAttempts">
                <el-input-number v-model="form.reconnectAttempts" :min="1" :max="10" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="重连间隔" prop="reconnectInterval">
                <el-input-number v-model="form.reconnectInterval" :min="1" :max="60" />
                <span style="margin-left: 10px;">秒</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <div class="config-actions">
          <el-button type="primary" @click="saveConfig">保存配置</el-button>
          <el-button @click="resetConfig">重置所有</el-button>
          <el-button type="info" @click="testConfig">测试连接</el-button>
        </div>
      </el-form>
    </div>
  `,
  data() {
    return {
      form: {
        // 称重设备
        sampleRate: 10,
        stableThreshold: 0.5,
        zeroCalibration: 0.000,
        fullScaleCalibration: 1.000,
        // RFID设备
        readPower: 20,
        readInterval: 1000,
        antennaConfig: ['1', '2'],
        filterDuplicate: true,
        // 通信配置
        heartbeatInterval: 30,
        connectionTimeout: 10,
        reconnectAttempts: 3,
        reconnectInterval: 5
      },
      rules: {
        sampleRate: [{ required: true, message: '采样频率不能为空', trigger: 'blur' }],
        stableThreshold: [{ required: true, message: '稳定阈值不能为空', trigger: 'blur' }]
      }
    };
  },
  methods: {
    saveConfig() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          console.log('保存设备配置:', this.form);
          this.$message.success('设备配置保存成功');
        }
      });
    },
    resetConfig() {
      this.$refs.form.resetFields();
    },
    resetWeightConfig() {
      this.form.sampleRate = 10;
      this.form.stableThreshold = 0.5;
      this.form.zeroCalibration = 0.000;
      this.form.fullScaleCalibration = 1.000;
    },
    resetRfidConfig() {
      this.form.readPower = 20;
      this.form.readInterval = 1000;
      this.form.antennaConfig = ['1', '2'];
      this.form.filterDuplicate = true;
    },
    testConfig() {
      this.$message.info('正在测试设备连接...');
      setTimeout(() => {
        this.$message.success('设备连接测试成功');
      }, 2000);
    }
  }
};

// EMQX配置组件
const EmqxConfig = {
  template: `
    <div>
      <el-form :model="form" :rules="rules" ref="form" label-width="120px">
        <el-card class="config-card">
          <div slot="header" class="clearfix">
            <span>EMQX服务器配置</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="testConnection">测试连接</el-button>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="服务器地址" prop="host">
                <el-input v-model="form.host" placeholder="请输入EMQX服务器地址" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="端口" prop="port">
                <el-input-number v-model="form.port" :min="1" :max="65535" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="用户名" prop="username">
                <el-input v-model="form.username" placeholder="请输入用户名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="密码" prop="password">
                <el-input v-model="form.password" type="password" placeholder="请输入密码" show-password />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <el-card class="config-card">
          <div slot="header" class="clearfix">
            <span>主题配置</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="设备状态主题" prop="deviceStatusTopic">
                <el-input v-model="form.deviceStatusTopic" placeholder="设备状态上报主题" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="称重数据主题" prop="weightDataTopic">
                <el-input v-model="form.weightDataTopic" placeholder="称重数据上报主题" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="RFID数据主题" prop="rfidDataTopic">
                <el-input v-model="form.rfidDataTopic" placeholder="RFID数据上报主题" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="报警主题" prop="alarmTopic">
                <el-input v-model="form.alarmTopic" placeholder="报警信息主题" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <el-card class="config-card">
          <div slot="header" class="clearfix">
            <span>连接参数</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="保持连接" prop="keepAlive">
                <el-input-number v-model="form.keepAlive" :min="10" :max="300" />
                <span style="margin-left: 10px;">秒</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="清理会话" prop="cleanSession">
                <el-switch v-model="form.cleanSession" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="QoS等级" prop="qos">
                <el-select v-model="form.qos" placeholder="请选择QoS等级">
                  <el-option label="0 - 最多一次" value="0" />
                  <el-option label="1 - 至少一次" value="1" />
                  <el-option label="2 - 只有一次" value="2" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="自动重连" prop="autoReconnect">
                <el-switch v-model="form.autoReconnect" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <div class="config-actions">
          <el-button type="primary" @click="saveConfig">保存配置</el-button>
          <el-button @click="resetConfig">重置</el-button>
          <el-button type="success" @click="testConnection">测试连接</el-button>
        </div>
      </el-form>
    </div>
  `,
  data() {
    return {
      form: {
        host: '*************',
        port: 1883,
        username: 'admin',
        password: '',
        deviceStatusTopic: 'outmis/device/status',
        weightDataTopic: 'outmis/weight/data',
        rfidDataTopic: 'outmis/rfid/data',
        alarmTopic: 'outmis/alarm',
        keepAlive: 60,
        cleanSession: true,
        qos: '1',
        autoReconnect: true
      },
      rules: {
        host: [{ required: true, message: '服务器地址不能为空', trigger: 'blur' }],
        port: [{ required: true, message: '端口不能为空', trigger: 'blur' }]
      }
    };
  },
  methods: {
    saveConfig() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          console.log('保存EMQX配置:', this.form);
          this.$message.success('EMQX配置保存成功');
        }
      });
    },
    resetConfig() {
      this.$refs.form.resetFields();
    },
    testConnection() {
      this.$message.info('正在测试EMQX连接...');
      setTimeout(() => {
        this.$message.success('EMQX连接测试成功');
      }, 2000);
    }
  }
};

// 系统配置组件
const SystemConfig = {
  template: `
    <div>
      <el-form :model="form" ref="form" label-width="120px">
        <el-card class="config-card">
          <div slot="header" class="clearfix">
            <span>系统参数</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="数据保留天数">
                <el-input-number v-model="form.dataRetentionDays" :min="1" :max="365" />
                <span style="margin-left: 10px;">天</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="日志级别">
                <el-select v-model="form.logLevel" placeholder="请选择日志级别">
                  <el-option label="DEBUG" value="debug" />
                  <el-option label="INFO" value="info" />
                  <el-option label="WARN" value="warn" />
                  <el-option label="ERROR" value="error" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="自动备份">
                <el-switch v-model="form.autoBackup" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="备份间隔">
                <el-input-number v-model="form.backupInterval" :min="1" :max="24" />
                <span style="margin-left: 10px;">小时</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <div class="config-actions">
          <el-button type="primary" @click="saveConfig">保存配置</el-button>
          <el-button @click="resetConfig">重置</el-button>
        </div>
      </el-form>
    </div>
  `,
  data() {
    return {
      form: {
        dataRetentionDays: 30,
        logLevel: 'info',
        autoBackup: true,
        backupInterval: 6
      }
    };
  },
  methods: {
    saveConfig() {
      console.log('保存系统配置:', this.form);
      this.$message.success('系统配置保存成功');
    },
    resetConfig() {
      this.$refs.form.resetFields();
    }
  }
};

// 报警配置组件
const AlarmConfig = {
  template: `
    <div>
      <el-form :model="form" ref="form" label-width="120px">
        <el-card class="config-card">
          <div slot="header" class="clearfix">
            <span>报警阈值</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="重量偏差阈值">
                <el-input-number v-model="form.weightDeviationThreshold" :min="0.1" :max="10" :precision="2" />
                <span style="margin-left: 10px;">kg</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="设备离线阈值">
                <el-input-number v-model="form.deviceOfflineThreshold" :min="1" :max="60" />
                <span style="margin-left: 10px;">分钟</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <el-card class="config-card">
          <div slot="header" class="clearfix">
            <span>通知方式</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="邮件通知">
                <el-switch v-model="form.emailNotification" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="短信通知">
                <el-switch v-model="form.smsNotification" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <div class="config-actions">
          <el-button type="primary" @click="saveConfig">保存配置</el-button>
          <el-button @click="resetConfig">重置</el-button>
        </div>
      </el-form>
    </div>
  `,
  data() {
    return {
      form: {
        weightDeviationThreshold: 2.0,
        deviceOfflineThreshold: 5,
        emailNotification: true,
        smsNotification: false
      }
    };
  },
  methods: {
    saveConfig() {
      console.log('保存报警配置:', this.form);
      this.$message.success('报警配置保存成功');
    },
    resetConfig() {
      this.$refs.form.resetFields();
    }
  }
};

export default {
  name: "OutmisConfig",
  components: {
    DeviceConfig,
    EmqxConfig,
    SystemConfig,
    AlarmConfig
  },
  data() {
    return {
      activeTab: 'device'
    };
  },
  methods: {
    handleTabClick(tab) {
      console.log('切换到标签:', tab.name);
    }
  }
};
</script>

<style scoped>
.config-card {
  margin-bottom: 20px;
}
.config-actions {
  text-align: center;
  margin-top: 20px;
}
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both;
}
</style>
