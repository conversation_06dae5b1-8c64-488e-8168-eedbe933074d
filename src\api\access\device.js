import request from '@/utils/request'

// 查询门禁设备列表
export function listDevice(query) {
  return request({
    url: '/access/device/list',
    method: 'get',
    params: query
  })
}

// 查询门禁设备详细
export function getDevice(id) {
  return request({
    url: '/access/device/' + id,
    method: 'get'
  })
}

// 根据设备编号查询门禁设备详细
export function getDeviceByCode(deviceCode) {
  return request({
    url: '/access/device/code/' + deviceCode,
    method: 'get'
  })
}

// 新增门禁设备
export function addDevice(data) {
  return request({
    url: '/access/device',
    method: 'post',
    data: data
  })
}

// 修改门禁设备
export function updateDevice(data) {
  return request({
    url: '/access/device',
    method: 'put',
    data: data
  })
}

// 删除门禁设备
export function delDevice(id) {
  return request({
    url: '/access/device/' + id,
    method: 'delete'
  })
}

// 获取设备统计信息
export function getDeviceStatistics() {
  return request({
    url: '/access/device/statistics',
    method: 'get'
  })
}

// 根据库房ID获取设备统计信息
export function getDeviceStatisticsByWarehouse(warehouseId) {
  return request({
    url: '/access/device/statistics/' + warehouseId,
    method: 'get'
  })
}

// 查询在线设备列表
export function getOnlineDevices() {
  return request({
    url: '/access/device/online',
    method: 'get'
  })
}

// 查询离线设备列表
export function getOfflineDevices(timeoutMinutes) {
  return request({
    url: '/access/device/offline',
    method: 'get',
    params: { timeoutMinutes }
  })
}

// 根据库房ID查询设备列表
export function getDevicesByWarehouse(warehouseId) {
  return request({
    url: '/access/device/warehouse/' + warehouseId,
    method: 'get'
  })
}

// 根据设备类型查询设备列表
export function getDevicesByType(deviceType) {
  return request({
    url: '/access/device/type/' + deviceType,
    method: 'get'
  })
}

// 控制门禁设备
export function controlDevice(deviceCode, data) {
  return request({
    url: '/access/device/control/' + deviceCode,
    method: 'post',
    data: data
  })
}

// 测试设备连接
export function testDevice(deviceCode) {
  return request({
    url: '/access/device/test/' + deviceCode,
    method: 'post'
  })
}

// 重启设备
export function restartDevice(deviceCode) {
  return request({
    url: '/access/device/restart/' + deviceCode,
    method: 'post'
  })
}

// 处理设备MQTT消息
export function processMqttMessage(deviceCode, topic, payload) {
  return request({
    url: '/access/device/mqtt/' + deviceCode,
    method: 'post',
    params: { topic },
    data: payload
  })
}
