<template>
  <div class="material-bill-detail">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>物料清单详情</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="handleClose">关闭</el-button>
      </div>
      
      <!-- 基本信息 -->
      <el-descriptions title="基本信息" :column="3" border>
        <el-descriptions-item label="清单编号">{{ billInfo.billCode }}</el-descriptions-item>
        <el-descriptions-item label="清单名称">{{ billInfo.billName }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(billInfo.status)">{{ getStatusText(billInfo.status) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="录入方式">
          <el-tag type="info">{{ getInputMethodText(billInfo.inputMethod) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="明细条数">{{ billInfo.itemCount || 0 }}</el-descriptions-item>
        <el-descriptions-item label="总数量">{{ billInfo.totalQuantity || 0 }}</el-descriptions-item>
        <el-descriptions-item label="总重量">{{ billInfo.totalWeight || 0 }} kg</el-descriptions-item>
        <el-descriptions-item label="重量容差">{{ billInfo.weightTolerance || 0 }} kg</el-descriptions-item>
        <el-descriptions-item label="创建人">{{ billInfo.createBy }}</el-descriptions-item>
        <el-descriptions-item label="创建时间" :span="2">{{ billInfo.createTime }}</el-descriptions-item>
        <el-descriptions-item label="锁定人" v-if="billInfo.lockedBy">{{ billInfo.lockedBy }}</el-descriptions-item>
        <el-descriptions-item label="锁定时间" v-if="billInfo.lockedTime">{{ billInfo.lockedTime }}</el-descriptions-item>
        <el-descriptions-item label="描述" :span="3">{{ billInfo.description || '无' }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="3">{{ billInfo.remark || '无' }}</el-descriptions-item>
      </el-descriptions>

      <!-- 明细信息 -->
      <el-divider content-position="left">明细信息</el-divider>
      
      <div class="detail-operations">
        <el-button type="primary" size="small" icon="el-icon-refresh" @click="refreshItems" v-hasPermi="['material:item:query']">刷新明细</el-button>
        <el-button type="success" size="small" icon="el-icon-download" @click="exportItems" v-hasPermi="['material:item:export']">导出明细</el-button>
        <!-- 已删除：添加明细、编辑清单、锁定清单按钮 -->
      </div>

      <el-table :data="itemList" border v-loading="itemLoading" style="margin-top: 10px;">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column label="物料编码" prop="materialCode" width="120" />
        <el-table-column label="物料名称" prop="materialName" min-width="150" show-overflow-tooltip />
        <el-table-column label="规格" prop="materialSpec" width="120" show-overflow-tooltip />
        <el-table-column label="型号" prop="materialModel" width="120" show-overflow-tooltip />
        <el-table-column label="单位" prop="unit" width="80" align="center" />
        <el-table-column label="数量" prop="quantity" width="100" align="right" />
        <el-table-column label="单位重量(kg)" prop="unitWeight" width="120" align="right" />
        <el-table-column label="总重量(kg)" prop="totalWeight" width="120" align="right" />
        <el-table-column label="存储位置" prop="locationCode" width="120" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.locationCode || '未设置' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="排序" prop="sortOrder" width="80" align="center" />
        <el-table-column label="创建时间" prop="createTime" width="160" />
        <el-table-column label="备注" prop="remark" min-width="120" show-overflow-tooltip />
      </el-table>

      <!-- 统计信息 -->
      <el-card class="statistics-card" style="margin-top: 20px;">
        <div slot="header">
          <span>统计信息</span>
        </div>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ itemList.length }}</div>
              <div class="stat-label">明细条数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ getTotalQuantity() }}</div>
              <div class="stat-label">总数量</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ getTotalWeight() }} kg</div>
              <div class="stat-label">总重量</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ getAverageWeight() }} kg</div>
              <div class="stat-label">平均重量</div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </el-card>
  </div>
</template>

<script>
import { getMaterialBill } from "@/api/material/bill";
import { listMaterialBillItemByBillId, exportMaterialBillItem } from "@/api/material/item";

export default {
  name: "MaterialBillDetail",
  data() {
    return {
      billInfo: {},
      itemList: [],
      itemLoading: false
    };
  },
  methods: {
    // 初始化
    init(billId) {
      this.loadBillInfo(billId);
      this.loadItemList(billId);
    },

    // 加载清单信息
    loadBillInfo(billId) {
      getMaterialBill(billId).then(response => {
        this.billInfo = response.data;
      });
    },

    // 加载明细列表
    loadItemList(billId) {
      this.itemLoading = true;
      listMaterialBillItemByBillId(billId).then(response => {
        this.itemList = response.rows || response.data || [];
        this.itemLoading = false;
      }).catch(() => {
        this.itemLoading = false;
      });
    },

    // 刷新明细
    refreshItems() {
      if (this.billInfo.id) {
        this.loadItemList(this.billInfo.id);
      }
    },

    // 导出明细
    exportItems() {
      if (this.itemList.length === 0) {
        this.$message.warning("没有明细数据可导出");
        return;
      }

      const query = { billId: this.billInfo.id };
      exportMaterialBillItem(query).then(response => {
        this.$message.success("导出成功");
      });
    },

    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        '0': 'info',    // 草稿
        '1': 'success', // 正常
        '2': 'warning'  // 锁定
      };
      return statusMap[status] || 'info';
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        '0': '草稿',
        '1': '正常',
        '2': '锁定'
      };
      return statusMap[status] || '未知';
    },

    // 获取录入方式文本
    getInputMethodText(inputMethod) {
      const methodMap = {
        'web': '网页',
        'kiosk': '自助机',
        'miniapp': '小程序'
      };
      return methodMap[inputMethod] || '未知';
    },

    // 计算总数量
    getTotalQuantity() {
      return this.itemList.reduce((total, item) => {
        return total + (parseFloat(item.quantity) || 0);
      }, 0).toFixed(3);
    },

    // 计算总重量
    getTotalWeight() {
      return this.itemList.reduce((total, item) => {
        return total + (parseFloat(item.totalWeight) || 0);
      }, 0).toFixed(3);
    },

    // 计算平均重量
    getAverageWeight() {
      if (this.itemList.length === 0) return '0.000';
      const totalWeight = parseFloat(this.getTotalWeight());
      return (totalWeight / this.itemList.length).toFixed(3);
    },

    // 关闭
    handleClose() {
      this.$emit("close");
    },

    // 添加明细
    addItems() {
      // 关闭详情对话框，打开编辑模式
      this.$emit("close");
      this.$emit("editBill", this.billInfo);
    },

    // 编辑清单
    editBill() {
      // 关闭详情对话框，打开编辑模式
      this.$emit("close");
      this.$emit("editBill", this.billInfo);
    },

    // 锁定清单
    lockBill() {
      this.$confirm('确认锁定该物料清单吗？锁定后将无法修改。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用锁定API
        this.performLockAction();
      }).catch(() => {});
    },

    // 解锁清单
    unlockBill() {
      this.$confirm('确认解锁该物料清单吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用解锁API
        this.performUnlockAction();
      }).catch(() => {});
    },

    // 执行锁定操作
    performLockAction() {
      // 这里需要导入锁定API
      // import { lockMaterialBill } from "@/api/material/bill";

      // 临时使用模拟实现
      this.$message.success('锁定成功');
      this.billInfo.status = '2'; // 更新状态为锁定
      this.$emit("refresh"); // 通知父组件刷新
    },

    // 执行解锁操作
    performUnlockAction() {
      // 这里需要导入解锁API
      // import { unlockMaterialBill } from "@/api/material/bill";

      // 临时使用模拟实现
      this.$message.success('解锁成功');
      this.billInfo.status = '1'; // 更新状态为正常
      this.$emit("refresh"); // 通知父组件刷新
    }
  }
};
</script>

<style scoped>
.material-bill-detail {
  padding: 20px;
}

.detail-operations {
  margin-bottom: 10px;
}

.statistics-card {
  background: #f8f9fa;
}

.stat-item {
  text-align: center;
  padding: 10px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style>
