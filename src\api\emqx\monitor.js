import request from '@/utils/request'

// 查询EMQX监控概览
export function getOverview() {
  return request({
    url: '/emqx/monitor/overview',
    method: 'get'
  })
}

// 查询客户端连接列表
export function getClients(query) {
  return request({
    url: '/emqx/monitor/clients',
    method: 'get',
    params: query
  })
}



// 获取消息统计信息
export function getMessageStats() {
  return request({
    url: '/emqx/monitor/stats/messages',
    method: 'get'
  })
}

// 获取系统性能指标
export function getSystemMetrics() {
  return request({
    url: '/emqx/monitor/metrics/system',
    method: 'get'
  })
}

// 获取客户端详情
export function getClientInfo(clientId) {
  return request({
    url: '/emqx/monitor/clients/' + clientId,
    method: 'get'
  })
}

// 获取客户端订阅信息
export function getClientSubscriptions(clientId) {
  return request({
    url: '/emqx/monitor/clients/' + clientId + '/subscriptions',
    method: 'get'
  })
}

// 获取主题统计信息
export function getTopicStats() {
  return request({
    url: '/emqx/monitor/stats/topics',
    method: 'get'
  })
}

// 获取订阅统计信息
export function getSubscriptionStats() {
  return request({
    url: '/emqx/monitor/stats/subscriptions',
    method: 'get'
  })
}



// 断开客户端连接
export function disconnectClient(clientId) {
  return request({
    url: `/emqx/monitor/clients/${clientId}/disconnect`,
    method: 'post'
  })
}

// 获取订阅列表
export function getSubscriptions(query) {
  return request({
    url: '/emqx/monitor/subscriptions',
    method: 'get',
    params: query
  })
}

// 获取实时数据
export function getRealtimeData() {
  return request({
    url: '/emqx/monitor/realtime',
    method: 'get'
  })
}

// 发布消息到主题
export function publishMessage(data) {
  return request({
    url: '/emqx/manage/publish',
    method: 'post',
    data: data
  })
}

// 测试EMQX连接
export function testConnection() {
  return request({
    url: '/emqx/manage/test-connection',
    method: 'get'
  })
}
