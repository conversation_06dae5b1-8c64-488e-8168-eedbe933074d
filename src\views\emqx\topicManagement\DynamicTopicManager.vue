<template>
  <div class="dynamic-topic-manager">
    <!-- 添加主题对话框 -->
    <el-dialog
      title="添加主题订阅"
      :visible.sync="addDialogVisible"
      width="600px"
      :before-close="handleAddDialogClose"
    >
      <el-form
        ref="addForm"
        :model="addForm"
        :rules="addRules"
        label-width="120px"
      >
        <el-form-item label="主题" prop="topic">
          <el-input
            v-model="addForm.topic"
            placeholder="请输入主题，如：dwms/device/+/status"
            clearable
          >
            <template slot="prepend">Topic</template>
          </el-input>
          <div class="form-tip">
            支持通配符：+ (单级) 和 # (多级)
          </div>
        </el-form-item>
        
        <el-form-item label="客户端ID" prop="clientId">
          <el-select
            v-model="addForm.clientId"
            placeholder="选择客户端"
            filterable
            allow-create
            style="width: 100%"
          >
            <el-option
              v-for="client in availableClients"
              :key="client.clientId"
              :label="client.clientId"
              :value="client.clientId"
            >
              <span style="float: left">{{ client.clientId }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">
                {{ client.status }}
              </span>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="QoS等级" prop="qos">
          <el-radio-group v-model="addForm.qos">
            <el-radio :label="0">QoS 0 - 最多一次</el-radio>
            <el-radio :label="1">QoS 1 - 至少一次</el-radio>
            <el-radio :label="2">QoS 2 - 恰好一次</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="高级选项">
          <el-checkbox v-model="addForm.noLocal">No Local</el-checkbox>
          <el-checkbox v-model="addForm.retainAsPublished">Retain As Published</el-checkbox>
          <div class="form-tip">
            No Local: 不接收自己发布的消息<br>
            Retain As Published: 保持发布时的Retain标志
          </div>
        </el-form-item>
        
        <el-form-item label="Retain Handling">
          <el-select v-model="addForm.retainHandling" style="width: 200px">
            <el-option label="发送保留消息" :value="0"></el-option>
            <el-option label="仅新订阅发送" :value="1"></el-option>
            <el-option label="不发送保留消息" :value="2"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleAddDialogClose">取消</el-button>
        <el-button type="primary" @click="submitAddTopic" :loading="addLoading">
          添加订阅
        </el-button>
      </div>
    </el-dialog>

    <!-- 编辑主题对话框 -->
    <el-dialog
      title="修改主题订阅"
      :visible.sync="editDialogVisible"
      width="600px"
      :before-close="handleEditDialogClose"
    >
      <el-form
        ref="editForm"
        :model="editForm"
        :rules="editRules"
        label-width="120px"
      >
        <el-form-item label="主题">
          <el-input v-model="editForm.topic" disabled>
            <template slot="prepend">Topic</template>
          </el-input>
        </el-form-item>
        
        <el-form-item label="客户端ID">
          <el-input v-model="editForm.clientId" disabled>
            <template slot="prepend">Client</template>
          </el-input>
        </el-form-item>
        
        <el-form-item label="QoS等级" prop="qos">
          <el-radio-group v-model="editForm.qos">
            <el-radio :label="0">QoS 0 - 最多一次</el-radio>
            <el-radio :label="1">QoS 1 - 至少一次</el-radio>
            <el-radio :label="2">QoS 2 - 恰好一次</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="高级选项">
          <el-checkbox v-model="editForm.noLocal">No Local</el-checkbox>
          <el-checkbox v-model="editForm.retainAsPublished">Retain As Published</el-checkbox>
        </el-form-item>
        
        <el-form-item label="Retain Handling">
          <el-select v-model="editForm.retainHandling" style="width: 200px">
            <el-option label="发送保留消息" :value="0"></el-option>
            <el-option label="仅新订阅发送" :value="1"></el-option>
            <el-option label="不发送保留消息" :value="2"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleEditDialogClose">取消</el-button>
        <el-button type="primary" @click="submitEditTopic" :loading="editLoading">
          保存修改
        </el-button>
      </div>
    </el-dialog>

    <!-- 批量操作对话框 -->
    <el-dialog
      title="批量主题操作"
      :visible.sync="batchDialogVisible"
      width="800px"
    >
      <div class="batch-operation">
        <el-tabs v-model="batchActiveTab">
          <el-tab-pane label="批量订阅" name="subscribe">
            <el-form :model="batchForm" label-width="120px">
              <el-form-item label="客户端ID">
                <el-select
                  v-model="batchForm.clientId"
                  placeholder="选择客户端"
                  filterable
                  style="width: 100%"
                >
                  <el-option
                    v-for="client in availableClients"
                    :key="client.clientId"
                    :label="client.clientId"
                    :value="client.clientId"
                  />
                </el-select>
              </el-form-item>
              
              <el-form-item label="主题列表">
                <el-input
                  type="textarea"
                  v-model="batchForm.topicList"
                  :rows="6"
                  placeholder="每行一个主题，例如：&#10;dwms/device/+/status&#10;dwms/video/+/alarm&#10;dwms/access/+/event"
                />
              </el-form-item>
              
              <el-form-item label="QoS等级">
                <el-radio-group v-model="batchForm.qos">
                  <el-radio :label="0">QoS 0</el-radio>
                  <el-radio :label="1">QoS 1</el-radio>
                  <el-radio :label="2">QoS 2</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
          </el-tab-pane>
          
          <el-tab-pane label="批量取消订阅" name="unsubscribe">
            <el-form :model="batchForm" label-width="120px">
              <el-form-item label="客户端ID">
                <el-select
                  v-model="batchForm.clientId"
                  placeholder="选择客户端"
                  filterable
                  style="width: 100%"
                >
                  <el-option
                    v-for="client in availableClients"
                    :key="client.clientId"
                    :label="client.clientId"
                    :value="client.clientId"
                  />
                </el-select>
              </el-form-item>
              
              <el-form-item label="主题列表">
                <el-input
                  type="textarea"
                  v-model="batchForm.topicList"
                  :rows="6"
                  placeholder="每行一个主题"
                />
              </el-form-item>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="batchDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitBatchOperation" :loading="batchLoading">
          执行操作
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  addDynamicTopic,
  removeDynamicTopic,
  updateDynamicTopic,
  batchSubscribeTopics,
  batchUnsubscribeTopics
} from '@/api/emqx/topicManagement'
import { getActiveClients } from '@/api/emqx/monitoring'

export default {
  name: 'DynamicTopicManager',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 添加对话框
      addDialogVisible: false,
      addLoading: false,
      addForm: {
        topic: '',
        clientId: '',
        qos: 1,
        noLocal: false,
        retainAsPublished: false,
        retainHandling: 0
      },
      addRules: {
        topic: [
          { required: true, message: '请输入主题', trigger: 'blur' },
          { min: 1, max: 255, message: '主题长度在 1 到 255 个字符', trigger: 'blur' }
        ],
        clientId: [
          { required: true, message: '请选择客户端', trigger: 'change' }
        ]
      },
      
      // 编辑对话框
      editDialogVisible: false,
      editLoading: false,
      editForm: {
        topic: '',
        clientId: '',
        qos: 1,
        noLocal: false,
        retainAsPublished: false,
        retainHandling: 0
      },
      editRules: {
        qos: [
          { required: true, message: '请选择QoS等级', trigger: 'change' }
        ]
      },
      
      // 批量操作对话框
      batchDialogVisible: false,
      batchLoading: false,
      batchActiveTab: 'subscribe',
      batchForm: {
        clientId: '',
        topicList: '',
        qos: 1
      },
      
      // 可用客户端列表
      availableClients: []
    }
  },
  
  watch: {
    visible(val) {
      if (val) {
        this.loadAvailableClients()
      }
    }
  },
  
  methods: {
    // 加载可用客户端
    async loadAvailableClients() {
      try {
        const response = await getActiveClients()
        this.availableClients = response.data || []
      } catch (error) {
        console.error('加载客户端列表失败:', error)
        this.availableClients = []
      }
    },
    
    // 显示添加对话框
    showAddDialog() {
      this.addDialogVisible = true
      this.loadAvailableClients()
    },
    
    // 显示编辑对话框
    showEditDialog(subscription) {
      this.editForm = {
        topic: subscription.topic,
        clientId: subscription.clientId,
        qos: subscription.qos || 1,
        noLocal: subscription.noLocal || false,
        retainAsPublished: subscription.retainAsPublished || false,
        retainHandling: subscription.retainHandling || 0
      }
      this.editDialogVisible = true
    },
    
    // 显示批量操作对话框
    showBatchDialog() {
      this.batchDialogVisible = true
      this.loadAvailableClients()
    },

    // 处理添加对话框关闭
    handleAddDialogClose() {
      this.addDialogVisible = false
      this.resetAddForm()
    },

    // 处理编辑对话框关闭
    handleEditDialogClose() {
      this.editDialogVisible = false
      this.resetEditForm()
    },

    // 重置添加表单
    resetAddForm() {
      this.addForm = {
        topic: '',
        clientId: '',
        qos: 1,
        noLocal: false,
        retainAsPublished: false,
        retainHandling: 0
      }
      if (this.$refs.addForm) {
        this.$refs.addForm.resetFields()
      }
    },

    // 重置编辑表单
    resetEditForm() {
      this.editForm = {
        topic: '',
        clientId: '',
        qos: 1,
        noLocal: false,
        retainAsPublished: false,
        retainHandling: 0
      }
      if (this.$refs.editForm) {
        this.$refs.editForm.resetFields()
      }
    },

    // 提交添加主题
    async submitAddTopic() {
      try {
        await this.$refs.addForm.validate()
        this.addLoading = true

        const response = await addDynamicTopic(this.addForm)

        if (response.code === 200) {
          this.$message.success('主题订阅添加成功')
          this.handleAddDialogClose()
          this.$emit('refresh')
        } else {
          this.$message.error('添加失败: ' + response.msg)
        }
      } catch (error) {
        if (error !== false) { // 不是表单验证错误
          this.$message.error('添加失败')
          console.error(error)
        }
      } finally {
        this.addLoading = false
      }
    },

    // 提交编辑主题
    async submitEditTopic() {
      try {
        await this.$refs.editForm.validate()
        this.editLoading = true

        const response = await updateDynamicTopic(this.editForm)

        if (response.code === 200) {
          this.$message.success('主题订阅修改成功')
          this.handleEditDialogClose()
          this.$emit('refresh')
        } else {
          this.$message.error('修改失败: ' + response.msg)
        }
      } catch (error) {
        if (error !== false) {
          this.$message.error('修改失败')
          console.error(error)
        }
      } finally {
        this.editLoading = false
      }
    },

    // 删除主题订阅
    async removeTopic(subscription) {
      try {
        await this.$confirm('确定要删除这个主题订阅吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const response = await removeDynamicTopic({
          topic: subscription.topic,
          clientId: subscription.clientId
        })

        if (response.code === 200) {
          this.$message.success('主题订阅删除成功')
          this.$emit('refresh')
        } else {
          this.$message.error('删除失败: ' + response.msg)
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败')
          console.error(error)
        }
      }
    },

    // 提交批量操作
    async submitBatchOperation() {
      if (!this.batchForm.clientId) {
        this.$message.warning('请选择客户端')
        return
      }

      if (!this.batchForm.topicList.trim()) {
        this.$message.warning('请输入主题列表')
        return
      }

      const topics = this.batchForm.topicList
        .split('\n')
        .map(topic => topic.trim())
        .filter(topic => topic.length > 0)

      if (topics.length === 0) {
        this.$message.warning('请输入有效的主题')
        return
      }

      this.batchLoading = true

      try {
        let response
        if (this.batchActiveTab === 'subscribe') {
          const topicData = topics.map(topic => ({
            topic: topic,
            qos: this.batchForm.qos
          }))

          response = await batchSubscribeTopics({
            clientId: this.batchForm.clientId,
            topics: topicData
          })
        } else {
          response = await batchUnsubscribeTopics({
            clientId: this.batchForm.clientId,
            topics: topics
          })
        }

        if (response.code === 200) {
          const operation = this.batchActiveTab === 'subscribe' ? '订阅' : '取消订阅'
          this.$message.success(`批量${operation}成功`)
          this.batchDialogVisible = false
          this.$emit('refresh')
        } else {
          this.$message.error('操作失败: ' + response.msg)
        }
      } catch (error) {
        this.$message.error('批量操作失败')
        console.error(error)
      } finally {
        this.batchLoading = false
      }
    }
  }
}
</script>

<style scoped>
.dynamic-topic-manager {
  /* 组件样式 */
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.batch-operation {
  min-height: 300px;
}

.dialog-footer {
  text-align: right;
}
</style>
