import request from '@/utils/request'

// 查询仓库物品列表
export function listItem(query) {
  return request({
    url: '/warehouse/item/list',
    method: 'get',
    params: query
  })
}

// 查询仓库下的物品列表
export function listItemByWarehouse(warehouseId) {
  return request({
    url: '/warehouse/item/listByWarehouse/' + warehouseId,
    method: 'get'
  })
}

// 查询仓库物品详细
export function getItem(id) {
  return request({
    url: '/warehouse/item/' + id,
    method: 'get'
  })
}

// 新增仓库物品
export function addItem(data) {
  return request({
    url: '/warehouse/item',
    method: 'post',
    data: data
  })
}

// 修改仓库物品
export function updateItem(data) {
  return request({
    url: '/warehouse/item',
    method: 'put',
    data: data
  })
}

// 删除仓库物品
export function delItem(id) {
  return request({
    url: '/warehouse/item/' + id,
    method: 'delete'
  })
}

// 导出仓库物品
export function exportItem(query) {
  return request({
    url: '/warehouse/item/export',
    method: 'get',
    params: query
  })
} 