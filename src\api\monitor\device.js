import request from '@/utils/request'

// 设备监控API接口

/**
 * 获取设备统计信息
 */
export function getDeviceStatistics() {
  return request({
    url: '/api/system/health/device-stats',
    method: 'get'
  })
}

/**
 * 获取设备列表
 */
export function getDeviceList(query) {
  return request({
    url: '/monitor/device/list',
    method: 'get',
    params: query
  })
}

/**
 * 获取设备详情
 */
export function getDeviceDetail(deviceId) {
  return request({
    url: '/monitor/device/' + deviceId,
    method: 'get'
  })
}

/**
 * 获取设备健康评分
 */
export function getDeviceHealthScore(deviceId) {
  return request({
    url: '/monitor/device/' + deviceId + '/health',
    method: 'get'
  })
}

/**
 * 获取设备健康趋势
 */
export function getDeviceHealthTrend(query) {
  return request({
    url: '/monitor/device/health-trend',
    method: 'get',
    params: query
  })
}

/**
 * 获取设备监控指标
 */
export function getDeviceMetrics(deviceId, query) {
  return request({
    url: '/monitor/device/' + deviceId + '/metrics',
    method: 'get',
    params: query
  })
}

/**
 * 获取最新告警
 */
export function getRecentAlerts(query) {
  return request({
    url: '/monitor/alert/recent',
    method: 'get',
    params: query
  })
}

/**
 * 获取告警列表
 */
export function getAlertList(query) {
  return request({
    url: '/monitor/alert/list',
    method: 'get',
    params: query
  })
}

/**
 * 获取告警详情
 */
export function getAlertDetail(alertId) {
  return request({
    url: '/monitor/alert/' + alertId,
    method: 'get'
  })
}

/**
 * 确认告警
 */
export function acknowledgeAlert(alertId, data) {
  return request({
    url: '/monitor/alert/' + alertId + '/acknowledge',
    method: 'post',
    data: data
  })
}

/**
 * 解决告警
 */
export function resolveAlert(alertId, data) {
  return request({
    url: '/monitor/alert/' + alertId + '/resolve',
    method: 'post',
    data: data
  })
}

/**
 * 批量处理告警
 */
export function batchProcessAlerts(data) {
  return request({
    url: '/monitor/alert/batch-process',
    method: 'post',
    data: data
  })
}

/**
 * 获取告警规则列表
 */
export function getAlertRules(query) {
  return request({
    url: '/monitor/alert-rule/list',
    method: 'get',
    params: query
  })
}

/**
 * 创建告警规则
 */
export function createAlertRule(data) {
  return request({
    url: '/monitor/alert-rule',
    method: 'post',
    data: data
  })
}

/**
 * 更新告警规则
 */
export function updateAlertRule(ruleId, data) {
  return request({
    url: '/monitor/alert-rule/' + ruleId,
    method: 'put',
    data: data
  })
}

/**
 * 删除告警规则
 */
export function deleteAlertRule(ruleId) {
  return request({
    url: '/monitor/alert-rule/' + ruleId,
    method: 'delete'
  })
}

/**
 * 启用/禁用告警规则
 */
export function toggleAlertRule(ruleId, enabled) {
  return request({
    url: '/monitor/alert-rule/' + ruleId + '/toggle',
    method: 'post',
    data: { enabled }
  })
}

/**
 * 测试告警规则
 */
export function testAlertRule(data) {
  return request({
    url: '/monitor/alert-rule/test',
    method: 'post',
    data: data
  })
}

/**
 * 获取设备类型列表
 */
export function getDeviceTypes() {
  return request({
    url: '/monitor/device/types',
    method: 'get'
  })
}

/**
 * 获取设备状态历史
 */
export function getDeviceStatusHistory(deviceId, query) {
  return request({
    url: '/monitor/device/' + deviceId + '/status-history',
    method: 'get',
    params: query
  })
}

/**
 * 执行设备健康检查
 */
export function performHealthCheck(deviceId) {
  return request({
    url: '/monitor/device/' + deviceId + '/health-check',
    method: 'post'
  })
}

/**
 * 重启设备
 */
export function restartDevice(deviceId) {
  return request({
    url: '/monitor/device/' + deviceId + '/restart',
    method: 'post'
  })
}

/**
 * 更新设备配置
 */
export function updateDeviceConfig(deviceId, data) {
  return request({
    url: '/monitor/device/' + deviceId + '/config',
    method: 'put',
    data: data
  })
}

/**
 * 获取设备配置
 */
export function getDeviceConfig(deviceId) {
  return request({
    url: '/monitor/device/' + deviceId + '/config',
    method: 'get'
  })
}

/**
 * 获取设备日志
 */
export function getDeviceLogs(deviceId, query) {
  return request({
    url: '/monitor/device/' + deviceId + '/logs',
    method: 'get',
    params: query
  })
}

/**
 * 导出设备监控报告
 */
export function exportDeviceReport(query) {
  return request({
    url: '/monitor/device/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

/**
 * 获取监控概览数据
 */
export function getMonitorOverview() {
  return request({
    url: '/monitor/overview',
    method: 'get'
  })
}

/**
 * 获取系统性能指标
 */
export function getSystemPerformance() {
  return request({
    url: '/monitor/system/performance',
    method: 'get'
  })
}

/**
 * 获取网络连接状态
 */
export function getNetworkStatus() {
  return request({
    url: '/monitor/network/status',
    method: 'get'
  })
}

/**
 * 获取存储使用情况
 */
export function getStorageUsage() {
  return request({
    url: '/monitor/storage/usage',
    method: 'get'
  })
}

/**
 * 获取告警统计
 */
export function getAlertStatistics(query) {
  return request({
    url: '/monitor/alert/statistics',
    method: 'get',
    params: query
  })
}

/**
 * 获取设备分布统计
 */
export function getDeviceDistribution() {
  return request({
    url: '/monitor/device/distribution',
    method: 'get'
  })
}

/**
 * 获取实时监控数据
 */
export function getRealtimeMonitorData() {
  return request({
    url: '/monitor/realtime',
    method: 'get'
  })
}

/**
 * 订阅实时监控数据
 */
export function subscribeRealtimeData(callback) {
  // WebSocket连接实现
  const wsUrl = process.env.VUE_APP_WS_API || 'ws://localhost:8080/ws/monitor'
  const ws = new WebSocket(wsUrl)
  
  ws.onopen = () => {
    console.log('监控WebSocket连接已建立')
  }
  
  ws.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data)
      callback(data)
    } catch (error) {
      console.error('解析监控数据失败:', error)
    }
  }
  
  ws.onerror = (error) => {
    console.error('监控WebSocket连接错误:', error)
  }
  
  ws.onclose = () => {
    console.log('监控WebSocket连接已关闭')
  }
  
  return ws
}

/**
 * 发送设备控制命令
 */
export function sendDeviceCommand(deviceId, command, params) {
  return request({
    url: '/monitor/device/' + deviceId + '/command',
    method: 'post',
    data: {
      command,
      params
    }
  })
}

/**
 * 获取设备命令历史
 */
export function getDeviceCommandHistory(deviceId, query) {
  return request({
    url: '/monitor/device/' + deviceId + '/command-history',
    method: 'get',
    params: query
  })
}
