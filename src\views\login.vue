<template>
  <div class="login">
    <!-- 动态背景 -->
    <div class="login-bg">
      <!-- 化工分子动画 -->
      <div class="molecule-container">
        <div class="molecule molecule-1">
          <div class="atom atom-center"></div>
          <div class="atom atom-1"></div>
          <div class="atom atom-2"></div>
          <div class="atom atom-3"></div>
          <div class="bond bond-1"></div>
          <div class="bond bond-2"></div>
          <div class="bond bond-3"></div>
        </div>
        <div class="molecule molecule-2">
          <div class="atom atom-center"></div>
          <div class="atom atom-1"></div>
          <div class="atom atom-2"></div>
          <div class="bond bond-1"></div>
          <div class="bond bond-2"></div>
        </div>
        <div class="molecule molecule-3">
          <div class="atom atom-center"></div>
          <div class="atom atom-1"></div>
          <div class="atom atom-2"></div>
          <div class="atom atom-3"></div>
          <div class="atom atom-4"></div>
          <div class="bond bond-1"></div>
          <div class="bond bond-2"></div>
          <div class="bond bond-3"></div>
          <div class="bond bond-4"></div>
        </div>
      </div>

      <!-- 数据流动画 -->
      <div class="data-flow">
        <div class="flow-line flow-1"></div>
        <div class="flow-line flow-2"></div>
        <div class="flow-line flow-3"></div>
        <div class="flow-dot dot-1"></div>
        <div class="flow-dot dot-2"></div>
        <div class="flow-dot dot-3"></div>
        <div class="flow-dot dot-4"></div>
        <div class="flow-dot dot-5"></div>
      </div>

      <!-- 网格背景 -->
      <div class="grid-bg"></div>
    </div>

    <!-- 左侧品牌区域 -->
    <div class="login-brand">
      <div class="brand-content">
        <div class="brand-logo">
          <div class="logo-container">
            <svg-icon icon-class="ruiyun-logo-large" class="brand-logo-svg" />
          </div>
          <h1 class="brand-title">睿云管理系统</h1>
          <p class="brand-subtitle">Intelligent Chemical Warehouse Management</p>
          <div class="brand-tagline">
            <span class="tagline-text">智能 · 安全 · 高效</span>
          </div>
        </div>

        <div class="brand-features">
          <div class="feature-item" data-aos="fade-up" data-aos-delay="100">
            <div class="feature-icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </div>
            <div class="feature-content">
              <h3>智能化管理</h3>
              <p>AI驱动的智能决策系统</p>
            </div>
          </div>

          <div class="feature-item" data-aos="fade-up" data-aos-delay="200">
            <div class="feature-icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
              </svg>
            </div>
            <div class="feature-content">
              <h3>数据分析</h3>
              <p>实时数据监控与分析</p>
            </div>
          </div>

          <div class="feature-item" data-aos="fade-up" data-aos-delay="300">
            <div class="feature-icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </div>
            <div class="feature-content">
              <h3>安全保障</h3>
              <p>多层级安全防护体系</p>
            </div>
          </div>

          <div class="feature-item" data-aos="fade-up" data-aos-delay="400">
            <div class="feature-icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
              </svg>
            </div>
            <div class="feature-content">
              <h3>物联网集成</h3>
              <p>全面的IoT设备连接</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧登录表单区域 -->
    <div class="login-container">
      <div class="login-form-wrapper">
        <div class="login-header">
          <h2 class="login-title">用户登录</h2>
          <p class="login-desc">欢迎使用智能仓储管理系统</p>
        </div>

        <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
          <el-form-item prop="username">
            <div class="input-wrapper">
              <el-input
                v-model="loginForm.username"
                type="text"
                auto-complete="off"
                placeholder="请输入用户名"
                size="large"
                class="login-input"
              >
                <i slot="prefix" class="el-icon-user input-icon"></i>
              </el-input>
            </div>
          </el-form-item>

          <el-form-item prop="password">
            <div class="input-wrapper">
              <el-input
                v-model="loginForm.password"
                type="password"
                auto-complete="off"
                placeholder="请输入密码"
                size="large"
                class="login-input"
                @keyup.enter.native="handleLogin"
              >
                <i slot="prefix" class="el-icon-lock input-icon"></i>
              </el-input>
            </div>
          </el-form-item>

          <el-form-item prop="code" v-if="captchaEnabled">
            <div class="captcha-wrapper">
              <div class="input-wrapper captcha-input">
                <el-input
                  v-model="loginForm.code"
                  auto-complete="off"
                  placeholder="验证码"
                  size="large"
                  class="login-input"
                  @keyup.enter.native="handleLogin"
                >
                  <i slot="prefix" class="el-icon-picture input-icon"></i>
                </el-input>
              </div>
              <div class="captcha-code">
                <img :src="codeUrl" @click="getCode" class="captcha-img" title="点击刷新验证码"/>
              </div>
            </div>
          </el-form-item>

          <div class="login-options">
            <el-checkbox v-model="loginForm.rememberMe" class="remember-me">
              记住密码
            </el-checkbox>
            <a href="#" class="forgot-password">忘记密码？</a>
          </div>

          <el-form-item class="login-submit">
            <el-button
              :loading="loading"
              type="primary"
              size="large"
              class="login-btn"
              @click.native.prevent="handleLogin"
            >
              <span v-if="!loading">
                <i class="el-icon-right"></i>
                立即登录
              </span>
              <span v-else>
                <i class="el-icon-loading"></i>
                登录中...
              </span>
            </el-button>
          </el-form-item>

          <div class="register-link" v-if="register">
            <span>还没有账号？</span>
            <router-link class="link-register" :to="'/register'">立即注册</router-link>
          </div>
        </el-form>
      </div>
    </div>

    <!--  底部版权信息  -->
    <div class="login-footer">
      <span>Copyright © 2018-2025 ruiyun.vip All Rights Reserved.</span>
    </div>
  </div>
</template>

<script>
import { getCodeImg } from "@/api/login"
import Cookies from "js-cookie"
import { encrypt, decrypt } from '@/utils/jsencrypt'

export default {
  name: "Login",
  data() {
    return {
      title: process.env.VUE_APP_TITLE,
      codeUrl: "",
      loginForm: {
        username: "admin",
        password: "admin123",
        rememberMe: false,
        code: "",
        uuid: ""
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "请输入您的账号" }
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码" }
        ],
        code: [{ required: true, trigger: "change", message: "请输入验证码" }]
      },
      loading: false,
      // 验证码开关
      captchaEnabled: true,
      // 注册开关
      register: false,
      redirect: undefined
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },
  created() {
    this.getCode()
    this.getCookie()
  },
  methods: {
    getCode() {
      getCodeImg().then(res => {
        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled
        if (this.captchaEnabled) {
          this.codeUrl = "data:image/gif;base64," + res.img
          this.loginForm.uuid = res.uuid
        }
      })
    },
    getCookie() {
      const username = Cookies.get("username")
      const password = Cookies.get("password")
      const rememberMe = Cookies.get('rememberMe')
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password: password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
      }
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true
          if (this.loginForm.rememberMe) {
            Cookies.set("username", this.loginForm.username, { expires: 30 })
            Cookies.set("password", encrypt(this.loginForm.password), { expires: 30 })
            Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 })
          } else {
            Cookies.remove("username")
            Cookies.remove("password")
            Cookies.remove('rememberMe')
          }
          this.$store.dispatch("Login", this.loginForm).then(() => {
            this.$router.push({ path: this.redirect || "/" }).catch(()=>{})
          }).catch(() => {
            this.loading = false
            if (this.captchaEnabled) {
              this.getCode()
            }
          })
        }
      })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
// 企业化色彩定义
$primary-blue: #0f172a;
$secondary-blue: #1e293b;
$accent-blue: #3b82f6;
$light-blue: #60a5fa;
$tech-blue: #0ea5e9;
$dark-blue: #1e40af;
$chemical-green: #10b981;
$warning-orange: #f59e0b;
$enterprise-gradient: linear-gradient(135deg, #0f172a 0%, #1e293b 30%, #334155 60%, #475569 100%);
$chemical-gradient: linear-gradient(45deg, #10b981 0%, #3b82f6 50%, #8b5cf6 100%);
$glass-bg: rgba(255, 255, 255, 0.08);
$glass-border: rgba(255, 255, 255, 0.12);

.login {
  position: relative;
  display: flex;
  min-height: 100vh;
  background: $enterprise-gradient;
  overflow: hidden;
  font-family: 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;

  // 动态背景系统
  .login-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;

    // 网格背景
    .grid-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image:
        linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
      background-size: 50px 50px;
      animation: gridMove 20s linear infinite;
    }

    // 化工分子动画
    .molecule-container {
      position: absolute;
      width: 100%;
      height: 100%;

      .molecule {
        position: absolute;

        &.molecule-1 {
          top: 15%;
          left: 10%;
          animation: moleculeFloat 8s ease-in-out infinite;
        }

        &.molecule-2 {
          top: 60%;
          left: 20%;
          animation: moleculeFloat 10s ease-in-out infinite reverse;
        }

        &.molecule-3 {
          top: 30%;
          right: 15%;
          animation: moleculeFloat 12s ease-in-out infinite;
        }

        .atom {
          position: absolute;
          border-radius: 50%;
          background: $chemical-gradient;
          box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);

          &.atom-center {
            width: 12px;
            height: 12px;
            background: $tech-blue;
            animation: atomPulse 2s ease-in-out infinite;
          }

          &.atom-1 {
            width: 8px;
            height: 8px;
            top: -20px;
            left: 15px;
            background: $chemical-green;
            animation: atomOrbit 4s linear infinite;
          }

          &.atom-2 {
            width: 8px;
            height: 8px;
            top: 15px;
            left: -20px;
            background: $warning-orange;
            animation: atomOrbit 4s linear infinite reverse;
          }

          &.atom-3 {
            width: 8px;
            height: 8px;
            top: 15px;
            right: -20px;
            background: $light-blue;
            animation: atomOrbit 6s linear infinite;
          }

          &.atom-4 {
            width: 8px;
            height: 8px;
            bottom: -20px;
            left: 15px;
            background: #8b5cf6;
            animation: atomOrbit 5s linear infinite reverse;
          }
        }

        .bond {
          position: absolute;
          background: rgba(255, 255, 255, 0.2);
          transform-origin: left center;

          &.bond-1 {
            width: 25px;
            height: 2px;
            top: 6px;
            left: 6px;
            transform: rotate(-45deg);
            animation: bondPulse 3s ease-in-out infinite;
          }

          &.bond-2 {
            width: 25px;
            height: 2px;
            top: 6px;
            left: 6px;
            transform: rotate(45deg);
            animation: bondPulse 3s ease-in-out infinite 0.5s;
          }

          &.bond-3 {
            width: 25px;
            height: 2px;
            top: 6px;
            left: 6px;
            transform: rotate(135deg);
            animation: bondPulse 3s ease-in-out infinite 1s;
          }

          &.bond-4 {
            width: 25px;
            height: 2px;
            top: 6px;
            left: 6px;
            transform: rotate(-135deg);
            animation: bondPulse 3s ease-in-out infinite 1.5s;
          }
        }
      }
    }

    // 数据流动画
    .data-flow {
      position: absolute;
      width: 100%;
      height: 100%;

      .flow-line {
        position: absolute;
        background: linear-gradient(90deg, transparent, $tech-blue, transparent);
        height: 2px;
        animation: dataFlow 4s linear infinite;

        &.flow-1 {
          top: 25%;
          width: 200px;
          left: -200px;
          animation-delay: 0s;
        }

        &.flow-2 {
          top: 50%;
          width: 150px;
          left: -150px;
          animation-delay: 1s;
        }

        &.flow-3 {
          top: 75%;
          width: 180px;
          left: -180px;
          animation-delay: 2s;
        }
      }

      .flow-dot {
        position: absolute;
        width: 4px;
        height: 4px;
        background: $tech-blue;
        border-radius: 50%;
        box-shadow: 0 0 10px $tech-blue;
        animation: dotFloat 6s ease-in-out infinite;

        &.dot-1 {
          top: 20%;
          left: 15%;
          animation-delay: 0s;
        }

        &.dot-2 {
          top: 40%;
          left: 25%;
          animation-delay: 1s;
        }

        &.dot-3 {
          top: 60%;
          left: 35%;
          animation-delay: 2s;
        }

        &.dot-4 {
          top: 80%;
          left: 20%;
          animation-delay: 3s;
        }

        &.dot-5 {
          top: 30%;
          right: 20%;
          animation-delay: 4s;
        }
      }
    }
  }

  // 左侧品牌区域
  .login-brand {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 80px 60px;
    position: relative;
    z-index: 2;

    .brand-content {
      max-width: 500px;
      text-align: center;
      color: white;

      .brand-logo {
        margin-bottom: 80px;

        .logo-container {
          margin-bottom: 30px;

          .brand-logo-svg {
            width: 200px;
            height: 80px;
            color: $tech-blue;
            filter: drop-shadow(0 0 30px rgba(14, 165, 233, 0.4));
            animation: logoGlow 3s ease-in-out infinite;
          }
        }

        .brand-title {
          font-size: 42px;
          font-weight: 800;
          margin: 0 0 15px 0;
          background: linear-gradient(135deg, #ffffff 0%, #60a5fa 50%, #10b981 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          letter-spacing: 3px;
          text-shadow: 0 0 30px rgba(255, 255, 255, 0.1);
          animation: titleShine 4s ease-in-out infinite;
        }

        .brand-subtitle {
          font-size: 18px;
          color: rgba(255, 255, 255, 0.8);
          margin: 0 0 20px 0;
          letter-spacing: 2px;
          text-transform: uppercase;
          font-weight: 300;
        }

        .brand-tagline {
          .tagline-text {
            display: inline-block;
            padding: 8px 20px;
            background: $glass-bg;
            border: 1px solid $glass-border;
            border-radius: 20px;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            letter-spacing: 1px;
            backdrop-filter: blur(10px);
          }
        }
      }

      .brand-features {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 24px;
        max-width: 480px;
        margin: 0 auto;

        .feature-item {
          display: flex;
          align-items: flex-start;
          padding: 24px;
          background: $glass-bg;
          border: 1px solid $glass-border;
          border-radius: 16px;
          backdrop-filter: blur(15px);
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.6s ease;
          }

          &:hover {
            transform: translateY(-8px) scale(1.02);
            background: rgba(255, 255, 255, 0.12);
            border-color: rgba(255, 255, 255, 0.3);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);

            &::before {
              left: 100%;
            }

            .feature-icon {
              transform: scale(1.1) rotate(5deg);
            }
          }

          .feature-icon {
            width: 48px;
            height: 48px;
            margin-right: 16px;
            background: $chemical-gradient;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            flex-shrink: 0;

            svg {
              width: 24px;
              height: 24px;
              color: white;
            }
          }

          .feature-content {
            flex: 1;
            text-align: left;

            h3 {
              margin: 0 0 8px 0;
              font-size: 16px;
              font-weight: 600;
              color: rgba(255, 255, 255, 0.95);
              line-height: 1.2;
            }

            p {
              margin: 0;
              font-size: 13px;
              color: rgba(255, 255, 255, 0.7);
              line-height: 1.4;
            }
          }
        }
      }
    }
  }

  // 右侧登录容器
  .login-container {
    width: 480px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    position: relative;
    z-index: 2;

    .login-form-wrapper {
      width: 100%;
      max-width: 380px;
      padding: 60px 40px;

      .login-header {
        text-align: center;
        margin-bottom: 40px;

        .login-title {
          font-size: 28px;
          font-weight: 700;
          color: $primary-blue;
          margin: 0 0 10px 0;
          letter-spacing: 1px;
        }

        .login-desc {
          font-size: 14px;
          color: #666;
          margin: 0;
        }
      }

      .login-form {
        .el-form-item {
          margin-bottom: 24px;

          .input-wrapper {
            position: relative;

            .login-input {
              .el-input__inner {
                height: 50px;
                border: 2px solid #e5e7eb;
                border-radius: 12px;
                padding-left: 50px;
                font-size: 16px;
                transition: all 0.3s ease;
                background: #fafafa;

                &:focus {
                  border-color: $secondary-blue;
                  background: white;
                  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                }

                &::placeholder {
                  color: #9ca3af;
                }
              }

              .input-icon {
                position: absolute;
                left: 16px;
                top: 50%;
                transform: translateY(-50%);
                font-size: 18px;
                color: #6b7280;
                z-index: 10;
              }
            }
          }

          // 验证码样式
          .captcha-wrapper {
            display: flex;
            gap: 12px;

            .captcha-input {
              flex: 1;
            }

            .captcha-code {
              width: 120px;
              height: 50px;

              .captcha-img {
                width: 100%;
                height: 100%;
                border-radius: 8px;
                cursor: pointer;
                border: 2px solid #e5e7eb;
                transition: all 0.3s ease;

                &:hover {
                  border-color: $secondary-blue;
                }
              }
            }
          }
        }

        // 登录选项
        .login-options {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 30px;

          .remember-me {
            color: #6b7280;
            font-size: 14px;

            .el-checkbox__label {
              color: #6b7280;
            }
          }

          .forgot-password {
            color: $accent-blue;
            text-decoration: none;
            font-size: 14px;
            transition: color 0.3s ease;

            &:hover {
              color: $secondary-blue;
            }
          }
        }

        // 登录按钮
        .login-submit {
          margin-bottom: 20px;

          .login-btn {
            width: 100%;
            height: 50px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 12px;
            background: linear-gradient(135deg, $secondary-blue 0%, $accent-blue 100%);
            border: none;
            transition: all 0.3s ease;
            letter-spacing: 1px;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
            }

            &:active {
              transform: translateY(0);
            }

            i {
              margin-right: 8px;
            }
          }
        }

        // 注册链接
        .register-link {
          text-align: center;
          font-size: 14px;
          color: #6b7280;

          .link-register {
            color: $secondary-blue;
            text-decoration: none;
            font-weight: 600;
            margin-left: 8px;
            transition: color 0.3s ease;

            &:hover {
              color: $dark-blue;
            }
          }
        }
      }
    }
  }

  // 底部版权
  .login-footer {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
    z-index: 10;
    letter-spacing: 1px;
  }
}

// 企业化动画定义
@keyframes gridMove {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(50px, 50px);
  }
}

@keyframes moleculeFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-15px) rotate(120deg);
  }
  66% {
    transform: translateY(-8px) rotate(240deg);
  }
}

@keyframes atomPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    transform: scale(1.2);
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.6);
  }
}

@keyframes atomOrbit {
  0% {
    transform: rotate(0deg) translateX(25px) rotate(0deg);
  }
  100% {
    transform: rotate(360deg) translateX(25px) rotate(-360deg);
  }
}

@keyframes bondPulse {
  0%, 100% {
    opacity: 0.2;
    transform: scaleX(1);
  }
  50% {
    opacity: 0.6;
    transform: scaleX(1.1);
  }
}

@keyframes dataFlow {
  0% {
    left: -200px;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}

@keyframes dotFloat {
  0%, 100% {
    transform: translateY(0px);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

@keyframes logoGlow {
  0%, 100% {
    filter: drop-shadow(0 0 20px rgba(14, 165, 233, 0.3));
  }
  50% {
    filter: drop-shadow(0 0 30px rgba(14, 165, 233, 0.6));
  }
}

@keyframes titleShine {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .login {
    .login-brand {
      display: none;
    }

    .login-container {
      width: 100%;
    }
  }
}

@media (max-width: 768px) {
  .login {
    .login-container {
      .login-form-wrapper {
        padding: 40px 20px;
        max-width: 100%;
      }
    }
  }
}
</style>
