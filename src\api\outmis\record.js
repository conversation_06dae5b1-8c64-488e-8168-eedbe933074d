import request from '@/utils/request'

// 查询出库称重记录列表
export function listRecord(query) {
  return request({
    url: '/outmis/record/list',
    method: 'get',
    params: query
  })
}

// 查询出库称重记录详细
export function getRecord(recordId) {
  return request({
    url: '/outmis/record/' + recordId,
    method: 'get'
  })
}

// 新增出库称重记录
export function addRecord(data) {
  return request({
    url: '/outmis/record',
    method: 'post',
    data: data
  })
}

// 修改出库称重记录
export function updateRecord(data) {
  return request({
    url: '/outmis/record',
    method: 'put',
    data: data
  })
}

// 删除出库称重记录
export function delRecord(recordId) {
  return request({
    url: '/outmis/record/' + recordId,
    method: 'delete'
  })
}
