import request from '@/utils/request'

// 查询数字库房组态配置列表
export function listConfig(query) {
  return request({
    url: '/dwms/config/list',
    method: 'get',
    params: query
  })
}

// 查询数字库房组态配置详细
export function getConfig(id) {
  return request({
    url: '/dwms/config/' + id,
    method: 'get'
  })
}

// 新增数字库房组态配置
export function addConfig(data) {
  return request({
    url: '/dwms/config',
    method: 'post',
    data: data
  })
}

// 修改数字库房组态配置
export function updateConfig(data) {
  return request({
    url: '/dwms/config',
    method: 'put',
    data: data
  })
}

// 删除数字库房组态配置
export function delConfig(id) {
  return request({
    url: '/dwms/config/' + id,
    method: 'delete'
  })
}

// 保存组态配置
export function saveConfig(data) {
  return request({
    url: '/dwms/config/save',
    method: 'post',
    data: data
  })
}

// 复制组态配置
export function copyConfig(id, newName) {
  return request({
    url: '/dwms/config/copy',
    method: 'post',
    data: {
      id: id,
      newName: newName
    }
  })
}

// 启用/禁用组态配置
export function toggleConfigStatus(id, isActive) {
  return request({
    url: '/dwms/config/toggle',
    method: 'put',
    data: {
      id: id,
      isActive: isActive
    }
  })
}

// 导出组态配置
export function exportConfig(id) {
  return request({
    url: '/dwms/config/export/' + id,
    method: 'get'
  })
}

// 导入组态配置
export function importConfig(data) {
  return request({
    url: '/dwms/config/import',
    method: 'post',
    data: data
  })
}

// 根据库房ID查询组态配置
export function getConfigByWarehouse(warehouseId) {
  return request({
    url: '/dwms/config/warehouse/' + warehouseId,
    method: 'get'
  })
}

// 根据组态类型查询配置
export function getConfigByType(configType) {
  return request({
    url: '/dwms/config/type/' + configType,
    method: 'get'
  })
}

// 查询启用的组态配置
export function getActiveConfigs(query) {
  return request({
    url: '/dwms/config/active',
    method: 'get',
    params: query
  })
}

// 预览组态配置
export function previewConfig(id) {
  return request({
    url: '/dwms/config/preview/' + id,
    method: 'get'
  })
}
