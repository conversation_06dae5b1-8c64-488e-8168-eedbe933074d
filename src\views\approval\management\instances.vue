<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="业务类型" prop="businessType">
        <el-select v-model="queryParams.businessType" placeholder="请选择业务类型" clearable>
          <el-option label="物料清单" value="MATERIAL_BILL" />
          <el-option label="采购申请" value="PURCHASE_REQUEST" />
          <el-option label="出库申请" value="OUTBOUND_REQUEST" />
          <el-option label="入库申请" value="INBOUND_REQUEST" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="审批中" value="0" />
          <el-option label="已通过" value="1" />
          <el-option label="已驳回" value="2" />
          <el-option label="已撤销" value="3" />
          <el-option label="已终止" value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="申请人" prop="applyUserName">
        <el-input
          v-model="queryParams.applyUserName"
          placeholder="请输入申请人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['approval:instance:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="instanceList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="实例ID" align="center" prop="instanceId" width="120" />
      <el-table-column label="业务标题" align="center" prop="businessTitle" :show-overflow-tooltip="true" />
      <el-table-column label="业务类型" align="center" prop="businessType" width="120">
        <template slot-scope="scope">
          <dict-tag :options="businessTypeOptions" :value="scope.row.businessType"/>
        </template>
      </el-table-column>
      <el-table-column label="申请人" align="center" prop="applyUserName" width="100" />
      <el-table-column label="当前节点" align="center" prop="currentNodeName" width="120" />
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.status === '0' ? 'warning' : scope.row.status === '1' ? 'success' : 'danger'"
          >
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['approval:instance:detail']"
          >详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 审批详情对话框 -->
    <el-dialog title="审批详情" :visible.sync="detailOpen" width="80%" append-to-body>
      <div v-if="detailOpen && currentInstance" style="padding: 20px;">
        <el-descriptions title="基本信息" :column="2" border>
          <el-descriptions-item label="实例ID">{{ currentInstance.instanceId }}</el-descriptions-item>
          <el-descriptions-item label="业务标题">{{ currentInstance.businessTitle }}</el-descriptions-item>
          <el-descriptions-item label="业务类型">{{ currentInstance.businessType }}</el-descriptions-item>
          <el-descriptions-item label="申请人">{{ currentInstance.applyUserName }}</el-descriptions-item>
          <el-descriptions-item label="当前节点">{{ currentInstance.currentNodeName }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="currentInstance.status === '0' ? 'warning' : currentInstance.status === '1' ? 'success' : 'danger'">
              {{ getStatusText(currentInstance.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ parseTime(currentInstance.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        </el-descriptions>

        <el-divider content-position="left">操作</el-divider>
        <el-button type="primary" @click="viewFullDetail">查看完整详情</el-button>
        <el-button @click="detailOpen = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listApprovalInstances } from "@/api/approval/management";

export default {
  name: "ApprovalInstances",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 审批实例表格数据
      instanceList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示详情弹出层
      detailOpen: false,
      // 当前实例ID
      currentInstanceId: null,
      // 当前实例数据
      currentInstance: null,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        businessType: null,
        status: null,
        applyUserName: null
      },
      // 业务类型选项
      businessTypeOptions: [
        { label: "物料清单", value: "MATERIAL_BILL" },
        { label: "采购申请", value: "PURCHASE_REQUEST" },
        { label: "出库申请", value: "OUTBOUND_REQUEST" },
        { label: "入库申请", value: "INBOUND_REQUEST" }
      ]
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询审批实例列表 */
    getList() {
      this.loading = true;
      listApprovalInstances(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.instanceList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.instanceId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      this.currentInstanceId = row.instanceId;
      this.currentInstance = row;
      this.detailOpen = true;
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('approval/management/instances/export', {
        ...this.queryParams
      }, `approval_instances_${new Date().getTime()}.xlsx`)
    },
    /** 获取状态文本 */
    getStatusText(status) {
      const statusMap = {
        '0': '审批中',
        '1': '已通过',
        '2': '已驳回',
        '3': '已撤销',
        '4': '已终止'
      };
      return statusMap[status] || '未知';
    },
    /** 查看完整详情 */
    viewFullDetail() {
      // 跳转到详细的审批详情页面
      this.$router.push({
        path: '/approval/detail',
        query: { instanceId: this.currentInstanceId }
      });
    }
  }
};
</script>
