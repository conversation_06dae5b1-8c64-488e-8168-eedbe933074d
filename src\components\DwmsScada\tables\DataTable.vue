<template>
  <div class="data-table" :style="containerStyle">
    <div v-if="config.title" class="table-header">
      <h4>{{ config.title }}</h4>
      <div class="header-actions">
        <el-button v-if="config.showRefresh" size="mini" icon="el-icon-refresh" @click="refreshData"></el-button>
        <el-button v-if="config.showExport" size="mini" icon="el-icon-download" @click="exportData"></el-button>
      </div>
    </div>
    
    <el-table
      ref="dataTable"
      :data="tableData"
      :height="tableHeight"
      :stripe="config.stripe !== false"
      :border="config.border !== false"
      :size="config.size || 'small'"
      :row-class-name="getRowClassName"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
      @row-click="handleRowClick"
      v-loading="loading"
    >
      <!-- 选择列 -->
      <el-table-column
        v-if="config.showSelection"
        type="selection"
        width="55"
        align="center"
      ></el-table-column>
      
      <!-- 序号列 -->
      <el-table-column
        v-if="config.showIndex"
        type="index"
        label="序号"
        width="60"
        align="center"
      ></el-table-column>
      
      <!-- 数据列 -->
      <el-table-column
        v-for="column in columns"
        :key="column.prop"
        :prop="column.prop"
        :label="column.label"
        :width="column.width"
        :min-width="column.minWidth"
        :align="column.align || 'left'"
        :sortable="column.sortable"
        :formatter="column.formatter"
        :show-overflow-tooltip="column.showOverflowTooltip !== false"
      >
        <template slot-scope="scope">
          <!-- 自定义渲染 -->
          <div v-if="column.type === 'custom'" v-html="renderCustomColumn(column, scope.row, scope.$index)"></div>
          
          <!-- 状态标签 -->
          <el-tag
            v-else-if="column.type === 'status'"
            :type="getStatusType(scope.row[column.prop])"
            size="mini"
          >
            {{ getStatusText(scope.row[column.prop], column) }}
          </el-tag>
          
          <!-- 进度条 -->
          <el-progress
            v-else-if="column.type === 'progress'"
            :percentage="scope.row[column.prop]"
            :color="getProgressColor(scope.row[column.prop], column)"
            :show-text="column.showText !== false"
            :stroke-width="column.strokeWidth || 6"
          ></el-progress>
          
          <!-- 图标 -->
          <i
            v-else-if="column.type === 'icon'"
            :class="getIconClass(scope.row[column.prop], column)"
            :style="getIconStyle(scope.row[column.prop], column)"
          ></i>
          
          <!-- 链接 -->
          <el-link
            v-else-if="column.type === 'link'"
            :href="scope.row[column.prop]"
            :type="column.linkType || 'primary'"
            :underline="column.underline !== false"
            target="_blank"
          >
            {{ scope.row[column.prop] }}
          </el-link>
          
          <!-- 按钮组 -->
          <div v-else-if="column.type === 'actions'" class="action-buttons">
            <el-button
              v-for="action in column.actions"
              :key="action.name"
              :type="action.type || 'text'"
              :size="action.size || 'mini'"
              :icon="action.icon"
              :disabled="isActionDisabled(action, scope.row)"
              @click="handleAction(action, scope.row, scope.$index)"
            >
              {{ action.label }}
            </el-button>
          </div>
          
          <!-- 默认文本 -->
          <span v-else>{{ formatValue(scope.row[column.prop], column) }}</span>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <el-pagination
      v-if="config.showPagination"
      :current-page="currentPage"
      :page-sizes="pageSizes"
      :page-size="pageSize"
      :total="total"
      :layout="paginationLayout"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      style="margin-top: 16px; text-align: right;"
    ></el-pagination>
  </div>
</template>

<script>
export default {
  name: 'DataTable',
  props: {
    config: {
      type: Object,
      default: () => ({})
    },
    data: {
      type: Array,
      default: () => []
    },
    columns: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    width: {
      type: Number,
      default: 600
    },
    height: {
      type: Number,
      default: 400
    }
  },
  data() {
    return {
      tableData: [],
      currentPage: 1,
      pageSize: 20,
      total: 0,
      selectedRows: []
    }
  },
  computed: {
    containerStyle() {
      return {
        width: this.width + 'px',
        height: this.height + 'px'
      }
    },
    tableHeight() {
      let height = this.height
      if (this.config.title) height -= 40
      if (this.config.showPagination) height -= 50
      return height
    },
    pageSizes() {
      return this.config.pageSizes || [10, 20, 50, 100]
    },
    paginationLayout() {
      return this.config.paginationLayout || 'total, sizes, prev, pager, next, jumper'
    }
  },
  watch: {
    data: {
      handler(newData) {
        this.updateTableData(newData)
      },
      immediate: true
    }
  },
  methods: {
    updateTableData(data) {
      if (this.config.showPagination) {
        // 客户端分页
        this.total = data.length
        const start = (this.currentPage - 1) * this.pageSize
        const end = start + this.pageSize
        this.tableData = data.slice(start, end)
      } else {
        this.tableData = data
        this.total = data.length
      }
    },
    
    formatValue(value, column) {
      if (value === null || value === undefined) {
        return column.emptyText || '-'
      }
      
      if (column.formatter && typeof column.formatter === 'function') {
        return column.formatter(value)
      }
      
      switch (column.type) {
        case 'date':
          return this.formatDate(value, column.dateFormat)
        case 'number':
          return this.formatNumber(value, column.precision)
        case 'currency':
          return this.formatCurrency(value, column.currency)
        case 'percentage':
          return this.formatPercentage(value)
        default:
          return value
      }
    },
    
    formatDate(value, format = 'YYYY-MM-DD HH:mm:ss') {
      if (!value) return '-'
      const date = new Date(value)
      return this.$moment ? this.$moment(date).format(format) : date.toLocaleString()
    },
    
    formatNumber(value, precision = 2) {
      if (isNaN(value)) return '-'
      return Number(value).toFixed(precision)
    },
    
    formatCurrency(value, currency = '¥') {
      if (isNaN(value)) return '-'
      return currency + Number(value).toFixed(2)
    },
    
    formatPercentage(value) {
      if (isNaN(value)) return '-'
      return (Number(value) * 100).toFixed(1) + '%'
    },
    
    getRowClassName({ row, rowIndex }) {
      if (this.config.rowClassName && typeof this.config.rowClassName === 'function') {
        return this.config.rowClassName({ row, rowIndex })
      }
      
      // 根据行数据状态设置样式
      if (row.status === 'error') {
        return 'row-error'
      } else if (row.status === 'warning') {
        return 'row-warning'
      }
      
      return ''
    },
    
    getStatusType(status) {
      const typeMap = {
        'success': 'success',
        'online': 'success',
        'active': 'success',
        'warning': 'warning',
        'pending': 'warning',
        'error': 'danger',
        'offline': 'danger',
        'inactive': 'info',
        'disabled': 'info'
      }
      return typeMap[status] || 'info'
    },
    
    getStatusText(status, column) {
      if (column.statusMap && column.statusMap[status]) {
        return column.statusMap[status]
      }
      
      const textMap = {
        'online': '在线',
        'offline': '离线',
        'active': '激活',
        'inactive': '未激活',
        'success': '成功',
        'error': '错误',
        'warning': '警告',
        'pending': '待处理'
      }
      return textMap[status] || status
    },
    
    getProgressColor(percentage, column) {
      if (column.progressColors) {
        for (const colorConfig of column.progressColors) {
          if (percentage <= colorConfig.threshold) {
            return colorConfig.color
          }
        }
      }
      
      // 默认颜色
      if (percentage < 30) return '#f56c6c'
      if (percentage < 70) return '#e6a23c'
      return '#67c23a'
    },
    
    getIconClass(value, column) {
      if (column.iconMap && column.iconMap[value]) {
        return column.iconMap[value]
      }
      return value
    },
    
    getIconStyle(value, column) {
      const style = {}
      if (column.iconColors && column.iconColors[value]) {
        style.color = column.iconColors[value]
      }
      if (column.iconSize) {
        style.fontSize = column.iconSize + 'px'
      }
      return style
    },
    
    renderCustomColumn(column, row, index) {
      if (column.render && typeof column.render === 'function') {
        return column.render(row[column.prop], row, index)
      }
      return row[column.prop]
    },
    
    isActionDisabled(action, row) {
      if (action.disabled && typeof action.disabled === 'function') {
        return action.disabled(row)
      }
      return action.disabled || false
    },
    
    handleAction(action, row, index) {
      this.$emit('action', {
        action: action.name,
        row: row,
        index: index
      })
    },
    
    handleSelectionChange(selection) {
      this.selectedRows = selection
      this.$emit('selection-change', selection)
    },
    
    handleSortChange({ column, prop, order }) {
      this.$emit('sort-change', { column, prop, order })
    },
    
    handleRowClick(row, column, event) {
      this.$emit('row-click', { row, column, event })
    },
    
    handleSizeChange(size) {
      this.pageSize = size
      this.updateTableData(this.data)
      this.$emit('size-change', size)
    },
    
    handleCurrentChange(page) {
      this.currentPage = page
      this.updateTableData(this.data)
      this.$emit('current-change', page)
    },
    
    refreshData() {
      this.$emit('refresh')
    },
    
    exportData() {
      // 导出当前表格数据
      const exportData = this.tableData.map(row => {
        const exportRow = {}
        this.columns.forEach(column => {
          if (column.prop && column.type !== 'actions') {
            exportRow[column.label] = this.formatValue(row[column.prop], column)
          }
        })
        return exportRow
      })
      
      this.$emit('export', exportData)
    },
    
    // 公共方法
    clearSelection() {
      this.$refs.dataTable.clearSelection()
    },
    
    toggleRowSelection(row, selected) {
      this.$refs.dataTable.toggleRowSelection(row, selected)
    },
    
    setCurrentRow(row) {
      this.$refs.dataTable.setCurrentRow(row)
    }
  }
}
</script>

<style lang="scss" scoped>
.data-table {
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 0 16px 0;
    
    h4 {
      margin: 0;
      font-size: 16px;
      color: #333;
    }
    
    .header-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .action-buttons {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
  }
}

// 全局样式
::v-deep .el-table {
  .row-error {
    background-color: #fef0f0;
  }
  
  .row-warning {
    background-color: #fdf6ec;
  }
  
  .el-table__row:hover {
    .row-error {
      background-color: #fde2e2;
    }
    
    .row-warning {
      background-color: #faecd8;
    }
  }
}
</style>
