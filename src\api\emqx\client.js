import request from '@/utils/request'

// 查询MQTT客户端会话列表
export function listMqttClientSession(query) {
  return request({
    url: '/emqx/client/session/list',
    method: 'get',
    params: query
  })
}

// 查询MQTT客户端会话详细
export function getMqttClientSession(sessionId) {
  return request({
    url: '/emqx/client/session/' + sessionId,
    method: 'get'
  })
}

// 根据客户端ID查询会话
export function getMqttClientSessionByClientId(clientId) {
  return request({
    url: '/emqx/client/session/client/' + clientId,
    method: 'get'
  })
}

// 断开MQTT客户端连接
export function disconnectMqttClient(clientId) {
  return request({
    url: '/emqx/client/disconnect/' + clientId,
    method: 'post'
  })
}

// 强制踢出MQTT客户端
export function kickoutMqttClient(clientId, data) {
  return request({
    url: '/emqx/client/kickout/' + clientId,
    method: 'post',
    data: data
  })
}

// MQTT客户端认证并建立连接
export function authenticateAndConnect(data) {
  return request({
    url: '/emqx/client/authenticate',
    method: 'post',
    data: data
  })
}

// 获取在线客户端列表
export function getOnlineMqttClients() {
  return request({
    url: '/emqx/client/online',
    method: 'get'
  })
}

// 获取客户端连接状态
export function getMqttClientStatus(clientId) {
  return request({
    url: '/emqx/client/status/' + clientId,
    method: 'get'
  })
}

// 获取设备类型主题配置
export function getDeviceTopicConfig(deviceType) {
  return request({
    url: '/emqx/client/topic-config/' + deviceType,
    method: 'get'
  })
}

// 订阅设备主题
export function subscribeDeviceTopics(clientId, deviceType) {
  return request({
    url: '/emqx/client/subscribe/' + clientId + '/' + deviceType,
    method: 'post'
  })
}

// 发布设备数据
export function publishDeviceData(data) {
  return request({
    url: '/emqx/client/publish',
    method: 'post',
    data: data
  })
}

// 处理设备心跳
export function handleDeviceHeartbeat(clientId, data) {
  return request({
    url: '/emqx/client/heartbeat/' + clientId,
    method: 'post',
    data: data
  })
}

// 获取设备数据统计
export function getDeviceDataStatistics(clientId, startTime, endTime) {
  return request({
    url: '/emqx/client/statistics/' + clientId,
    method: 'get',
    params: {
      startTime: startTime,
      endTime: endTime
    }
  })
}

// 获取设备类型支持的操作
export function getSupportedOperations(deviceType) {
  return request({
    url: '/emqx/client/operations/' + deviceType,
    method: 'get'
  })
}

// 执行设备控制命令
export function executeDeviceCommand(clientId, data) {
  return request({
    url: '/emqx/client/control/' + clientId,
    method: 'post',
    data: data
  })
}

// 获取设备实时数据
export function getDeviceRealtimeData(clientId) {
  return request({
    url: '/emqx/client/realtime/' + clientId,
    method: 'get'
  })
}

// 设置设备配置
export function setDeviceConfig(clientId, data) {
  return request({
    url: '/emqx/client/config/' + clientId,
    method: 'post',
    data: data
  })
}

// 获取设备配置
export function getDeviceConfig(clientId) {
  return request({
    url: '/emqx/client/config/' + clientId,
    method: 'get'
  })
}

// 获取MQTT客户端会话统计
export function getMqttClientSessionStats() {
  return request({
    url: '/emqx/client/session/statistics',
    method: 'get'
  })
}

// 获取支持的设备类型
export function getSupportedDeviceTypes() {
  return request({
    url: '/emqx/client/device-types',
    method: 'get'
  })
}

// 清理过期会话
export function cleanExpiredSessions(expireHours) {
  return request({
    url: '/emqx/client/session/clean/' + expireHours,
    method: 'post'
  })
}

// 批量断开客户端连接
export function batchDisconnectClients(clientIds) {
  return request({
    url: '/emqx/client/batch/disconnect',
    method: 'post',
    data: { clientIds: clientIds }
  })
}

// 获取客户端连接历史
export function getMqttClientHistory(clientId, query) {
  return request({
    url: '/emqx/client/history/' + clientId,
    method: 'get',
    params: query
  })
}

// 测试MQTT连接
export function testMqttConnection(data) {
  return request({
    url: '/emqx/client/test',
    method: 'post',
    data: data
  })
}

// 获取MQTT服务器信息
export function getMqttServerInfo() {
  return request({
    url: '/emqx/client/server/info',
    method: 'get'
  })
}

// 获取MQTT主题列表
export function getMqttTopics() {
  return request({
    url: '/emqx/client/topics',
    method: 'get'
  })
}

// 订阅MQTT主题
export function subscribeMqttTopic(data) {
  return request({
    url: '/emqx/client/topic/subscribe',
    method: 'post',
    data: data
  })
}

// 取消订阅MQTT主题
export function unsubscribeMqttTopic(data) {
  return request({
    url: '/emqx/client/topic/unsubscribe',
    method: 'post',
    data: data
  })
}

// 发布MQTT消息
export function publishMqttMessage(data) {
  return request({
    url: '/emqx/client/message/publish',
    method: 'post',
    data: data
  })
}

// 获取MQTT消息历史
export function getMqttMessageHistory(query) {
  return request({
    url: '/emqx/client/message/history',
    method: 'get',
    params: query
  })
}
