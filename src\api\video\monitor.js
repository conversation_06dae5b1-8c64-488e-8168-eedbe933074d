import request from '@/utils/request'

// 获取监控布局列表
export function getLayoutList(query) {
  return request({
    url: '/video/monitor/layouts',
    method: 'get',
    params: query
  })
}

// 保存监控布局
export function saveLayout(data) {
  return request({
    url: '/video/monitor/layout',
    method: 'post',
    data: data
  })
}

// 删除监控布局
export function deleteLayout(id) {
  return request({
    url: '/video/monitor/layout/' + id,
    method: 'delete'
  })
}

// 设置默认布局
export function setDefaultLayout(id) {
  return request({
    url: '/video/monitor/layout/default/' + id,
    method: 'put'
  })
}

// 获取设备实时流地址
export function getStreamUrl(deviceId) {
  return request({
    url: '/video/monitor/stream/' + deviceId,
    method: 'get'
  })
}

// 获取监控中心设备列表
export function getMonitorDeviceList(query) {
  return request({
    url: '/video/device/list',
    method: 'get',
    params: query
  })
}

// 获取设备流信息
export function getDeviceStreams(deviceId) {
  return request({
    url: '/video/device/stream/' + deviceId,
    method: 'get'
  })
}

// 获取设备播放地址
export function getPlayUrl(deviceId, streamType = 'hls') {
  return request({
    url: '/video/device/play/' + deviceId,
    method: 'get',
    params: { streamType }
  })
}

// 开始播放
export function startPlay(deviceId, streamType) {
  return request({
    url: '/video/monitor/play/start',
    method: 'post',
    data: {
      deviceId: deviceId,
      streamType: streamType || 'main'
    }
  })
}

// 停止播放
export function stopPlay(deviceId) {
  return request({
    url: '/video/monitor/play/stop/' + deviceId,
    method: 'post'
  })
}

// 获取播放状态
export function getPlayStatus(deviceId) {
  return request({
    url: '/video/monitor/play/status/' + deviceId,
    method: 'get'
  })
}
