import request from '@/utils/request'

// 设备认证管理API

/**
 * 获取支持的设备类型
 */
export function getSupportedDeviceTypes() {
  return request({
    url: '/emqx/device/auth/device-types',
    method: 'get'
  })
}

/**
 * 预览客户端ID生成
 */
export function previewClientId(deviceType, deviceId) {
  return request({
    url: '/emqx/device/auth/preview-client-id',
    method: 'get',
    params: {
      deviceType,
      deviceId
    }
  })
}

/**
 * 创建设备认证
 */
export function createDeviceAuth(data) {
  return request({
    url: '/emqx/device/auth/create',
    method: 'post',
    data: data
  })
}

/**
 * 验证设备认证
 */
export function validateDeviceAuth(data) {
  return request({
    url: '/emqx/device/auth/validate',
    method: 'post',
    data: data
  })
}

/**
 * 批量创建设备认证
 */
export function batchCreateDeviceAuth(data) {
  return request({
    url: '/emqx/device/auth/batch-create',
    method: 'post',
    data: data
  })
}

/**
 * 测试设备连接
 */
export function testDeviceConnection(data) {
  return request({
    url: '/emqx/device/auth/test-connection',
    method: 'post',
    data: data
  })
}

/**
 * 获取设备认证统计信息
 */
export function getAuthStatistics() {
  return request({
    url: '/emqx/device/auth/statistics',
    method: 'get'
  })
}

/**
 * 获取设备认证列表
 */
export function getDeviceAuthList(params) {
  return request({
    url: '/emqx/device/auth/list',
    method: 'get',
    params
  })
}

/**
 * 删除设备认证
 */
export function deleteDeviceAuth(deviceId) {
  return request({
    url: `/emqx/device/auth/${deviceId}`,
    method: 'delete'
  })
}

/**
 * 更新设备认证状态
 */
export function updateDeviceAuthStatus(deviceId, active) {
  return request({
    url: `/emqx/device/auth/${deviceId}/status`,
    method: 'put',
    data: { active }
  })
}

/**
 * 重置设备密码
 */
export function resetDevicePassword(deviceId) {
  return request({
    url: `/emqx/device/auth/${deviceId}/reset-password`,
    method: 'post'
  })
}
