<template>
  <div class="device-component" :style="{ width: width + 'px', height: height + 'px' }">
    <!-- 设备图标显示 -->
    <div class="device-container" :class="{ 'device-online': isOnline, 'device-offline': !isOnline }">
      <!-- 设备图标 -->
      <div class="device-icon">
        <img 
          v-if="config.icon && config.icon.endsWith('.png')"
          :src="getIconUrl(config.icon)"
          :alt="deviceName"
          @error="handleImageError"
        />
        <i 
          v-else
          :class="getDeviceIcon()"
          :style="{ fontSize: iconSize + 'px', color: getIconColor() }"
        ></i>
      </div>
      
      <!-- 设备名称 -->
      <div class="device-name" v-if="config.showName !== false">
        {{ deviceName }}
      </div>
      
      <!-- 设备状态 -->
      <div class="device-status" v-if="config.showStatus !== false">
        <span class="status-dot" :style="{ backgroundColor: getStatusColor() }"></span>
        <span class="status-text">{{ statusText }}</span>
      </div>
      
      <!-- 设备信息 -->
      <div class="device-info" v-if="config.showInfo && deviceInfo">
        <div class="info-item" v-for="(value, key) in deviceInfo" :key="key">
          <span class="info-label">{{ getInfoLabel(key) }}:</span>
          <span class="info-value">{{ value }}</span>
        </div>
      </div>
      
      <!-- 告警指示器 -->
      <div 
        v-if="hasAlert" 
        class="alert-indicator"
        :class="'alert-level-' + alertLevel"
      >
        <i class="el-icon-warning"></i>
      </div>
      
      <!-- 信号强度指示器 -->
      <div v-if="config.showSignal && signalStrength !== null" class="signal-indicator">
        <div class="signal-bars">
          <div 
            v-for="i in 4" 
            :key="i"
            class="signal-bar"
            :class="{ 'active': i <= getSignalBars() }"
          ></div>
        </div>
      </div>
    </div>
    
    <!-- 设备详情弹窗 -->
    <el-dialog 
      :title="deviceName + ' 详情'"
      :visible.sync="detailDialogVisible"
      width="500px"
      @close="closeDetailDialog"
    >
      <div class="device-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="设备ID">{{ deviceId }}</el-descriptions-item>
          <el-descriptions-item label="设备类型">{{ getDeviceTypeName() }}</el-descriptions-item>
          <el-descriptions-item label="设备状态">
            <el-tag :type="isOnline ? 'success' : 'danger'">{{ statusText }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="位置">{{ deviceLocation || '未知' }}</el-descriptions-item>
          <el-descriptions-item label="最后心跳" span="2">
            {{ lastHeartbeat || '无数据' }}
          </el-descriptions-item>
        </el-descriptions>
        
        <!-- 实时数据 -->
        <div v-if="realtimeData" class="realtime-data">
          <h4>实时数据</h4>
          <el-row :gutter="16">
            <el-col v-for="(value, key) in realtimeData" :key="key" :span="12">
              <div class="data-item">
                <span class="data-label">{{ getDataLabel(key) }}</span>
                <span class="data-value">{{ value }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'DeviceComponent',
  props: {
    config: {
      type: Object,
      default: () => ({})
    },
    data: {
      type: Object,
      default: () => ({})
    },
    width: {
      type: Number,
      default: 80
    },
    height: {
      type: Number,
      default: 80
    }
  },
  data() {
    return {
      detailDialogVisible: false,
      imageError: false
    }
  },
  computed: {
    deviceId() {
      return this.data?.deviceId || this.config.deviceId || 'Unknown'
    },
    deviceName() {
      return this.data?.name || this.config.name || this.deviceId
    },
    deviceType() {
      return this.data?.type || this.config.type || 'unknown'
    },
    deviceLocation() {
      return this.data?.location || this.config.location
    },
    isOnline() {
      if (this.data?.status !== undefined) {
        return this.data.status === 1 || this.data.status === 'online'
      }
      return this.config.defaultStatus !== 'offline'
    },
    statusText() {
      return this.isOnline ? '在线' : '离线'
    },
    hasAlert() {
      return this.data?.alertLevel > 0 || this.config.hasAlert
    },
    alertLevel() {
      return this.data?.alertLevel || 1
    },
    signalStrength() {
      return this.data?.signalStrength !== undefined ? this.data.signalStrength : null
    },
    lastHeartbeat() {
      return this.data?.lastHeartbeat ? this.formatTime(this.data.lastHeartbeat) : null
    },
    deviceInfo() {
      const info = {}
      if (this.data?.ip) info.ip = this.data.ip
      if (this.data?.version) info.version = this.data.version
      if (this.data?.manufacturer) info.manufacturer = this.data.manufacturer
      return Object.keys(info).length > 0 ? info : null
    },
    realtimeData() {
      const data = {}
      if (this.data?.temperature !== undefined) data.temperature = this.data.temperature + '°C'
      if (this.data?.humidity !== undefined) data.humidity = this.data.humidity + '%'
      if (this.data?.battery !== undefined) data.battery = this.data.battery + '%'
      if (this.data?.weight !== undefined) data.weight = this.data.weight + 'kg'
      return Object.keys(data).length > 0 ? data : null
    },
    iconSize() {
      return Math.min(this.width, this.height) * 0.4
    }
  },
  methods: {
    // 获取设备图标
    getDeviceIcon() {
      if (this.config.icon && !this.config.icon.endsWith('.png')) {
        return this.config.icon
      }
      
      const iconMap = {
        weight: 'el-icon-scale-to-original',
        access: 'el-icon-key',
        camera: 'el-icon-camera',
        beacon: 'el-icon-position',
        sensor: 'el-icon-cpu',
        gateway: 'el-icon-connection'
      }
      
      return iconMap[this.deviceType] || 'el-icon-cpu'
    },
    
    // 获取图标颜色
    getIconColor() {
      if (this.config.iconColor) {
        return this.config.iconColor
      }
      
      return this.isOnline ? '#67C23A' : '#F56C6C'
    },
    
    // 获取状态颜色
    getStatusColor() {
      if (this.hasAlert) {
        const alertColors = {
          1: '#E6A23C', // 警告
          2: '#F56C6C', // 严重
          3: '#FF0000'  // 紧急
        }
        return alertColors[this.alertLevel] || '#E6A23C'
      }
      
      return this.isOnline ? '#67C23A' : '#F56C6C'
    },
    
    // 获取图标URL
    getIconUrl(icon) {
      return `/static/images/devices/${icon}`
    },
    
    // 处理图片加载错误
    handleImageError() {
      this.imageError = true
    },
    
    // 获取信号强度条数
    getSignalBars() {
      if (this.signalStrength === null) return 0
      
      if (this.signalStrength >= 80) return 4
      if (this.signalStrength >= 60) return 3
      if (this.signalStrength >= 40) return 2
      if (this.signalStrength >= 20) return 1
      return 0
    },
    
    // 获取设备类型名称
    getDeviceTypeName() {
      const typeMap = {
        weight: '重量传感器',
        access: '门禁设备',
        camera: '监控摄像头',
        beacon: '蓝牙信标',
        sensor: '传感器',
        gateway: '网关设备'
      }
      
      return typeMap[this.deviceType] || '未知设备'
    },
    
    // 获取信息标签
    getInfoLabel(key) {
      const labelMap = {
        ip: 'IP地址',
        version: '版本',
        manufacturer: '厂商',
        model: '型号'
      }
      
      return labelMap[key] || key
    },
    
    // 获取数据标签
    getDataLabel(key) {
      const labelMap = {
        temperature: '温度',
        humidity: '湿度',
        battery: '电量',
        weight: '重量',
        pressure: '压力',
        voltage: '电压'
      }
      
      return labelMap[key] || key
    },
    
    // 格式化时间
    formatTime(time) {
      if (!time) return ''
      
      const date = new Date(time)
      return date.toLocaleString()
    },
    
    // 显示设备详情
    showDeviceDetail() {
      this.detailDialogVisible = true
    },
    
    // 关闭详情对话框
    closeDetailDialog() {
      this.detailDialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.device-component {
  display: flex;
  align-items: center;
  justify-content: center;
  
  .device-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    padding: 8px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &.device-online {
      background: rgba(103, 194, 58, 0.1);
      border: 2px solid rgba(103, 194, 58, 0.3);
      
      &:hover {
        background: rgba(103, 194, 58, 0.2);
        transform: scale(1.05);
      }
    }
    
    &.device-offline {
      background: rgba(245, 108, 108, 0.1);
      border: 2px solid rgba(245, 108, 108, 0.3);
      
      &:hover {
        background: rgba(245, 108, 108, 0.2);
        transform: scale(1.05);
      }
    }
    
    .device-icon {
      margin-bottom: 4px;
      
      img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
      }
    }
    
    .device-name {
      font-size: 10px;
      color: #333;
      text-align: center;
      margin-bottom: 2px;
      word-break: break-word;
    }
    
    .device-status {
      display: flex;
      align-items: center;
      font-size: 8px;
      
      .status-dot {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        margin-right: 4px;
      }
      
      .status-text {
        color: #666;
      }
    }
    
    .device-info {
      margin-top: 4px;
      font-size: 8px;
      
      .info-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 2px;
        
        .info-label {
          color: #999;
        }
        
        .info-value {
          color: #333;
          font-weight: 500;
        }
      }
    }
    
    .alert-indicator {
      position: absolute;
      top: 2px;
      right: 2px;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      animation: pulse 2s infinite;
      
      i {
        font-size: 10px;
        color: #fff;
      }
      
      &.alert-level-1 {
        background: #E6A23C;
      }
      
      &.alert-level-2 {
        background: #F56C6C;
      }
      
      &.alert-level-3 {
        background: #FF0000;
      }
    }
    
    .signal-indicator {
      position: absolute;
      top: 2px;
      left: 2px;
      
      .signal-bars {
        display: flex;
        align-items: flex-end;
        gap: 1px;
        
        .signal-bar {
          width: 2px;
          background: #ddd;
          
          &:nth-child(1) { height: 4px; }
          &:nth-child(2) { height: 6px; }
          &:nth-child(3) { height: 8px; }
          &:nth-child(4) { height: 10px; }
          
          &.active {
            background: #67C23A;
          }
        }
      }
    }
  }
}

.device-detail {
  .realtime-data {
    margin-top: 20px;
    
    h4 {
      margin-bottom: 16px;
      color: #333;
    }
    
    .data-item {
      display: flex;
      justify-content: space-between;
      padding: 8px 12px;
      background: #f5f7fa;
      border-radius: 4px;
      margin-bottom: 8px;
      
      .data-label {
        color: #666;
        font-size: 14px;
      }
      
      .data-value {
        color: #333;
        font-weight: 500;
      }
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
