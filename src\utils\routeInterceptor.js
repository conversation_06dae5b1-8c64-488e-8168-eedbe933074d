/**
 * Vue Router 路由拦截器
 * 在Vue Router层面直接拦截重复路由定义
 */

import router from '@/router'

let isInterceptorApplied = false
const registeredRoutes = new Set()

/**
 * 应用路由拦截器
 */
export function applyRouteInterceptor() {
  if (isInterceptorApplied) {
    console.log('[路由拦截器] 拦截器已应用，跳过')
    return
  }

  console.log('[路由拦截器] 开始应用路由拦截器...')

  try {
    // 拦截 addRoutes 方法
    if (router && typeof router.addRoutes === 'function') {
      const originalAddRoutes = router.addRoutes

      router.addRoutes = function(routes) {
        console.log(`[路由拦截器] 拦截到 addRoutes 调用，路由数量: ${routes.length}`)
        
        // 过滤重复路由
        const filteredRoutes = filterDuplicateRoutes(routes)
        console.log(`[路由拦截器] 过滤后路由数量: ${filteredRoutes.length}`)
        
        if (filteredRoutes.length > 0) {
          return originalAddRoutes.call(this, filteredRoutes)
        } else {
          console.log('[路由拦截器] 没有新路由需要添加')
        }
      }
    }

    // 拦截 addRoute 方法（如果存在）
    if (router && typeof router.addRoute === 'function') {
      const originalAddRoute = router.addRoute

      router.addRoute = function(route) {
        const routeKey = createRouteKey(route)
        if (registeredRoutes.has(routeKey)) {
          console.log(`[路由拦截器] 跳过重复路由: ${route.name || route.path}`)
          return
        }
        
        registeredRoutes.add(routeKey)
        return originalAddRoute.call(this, route)
      }
    }

    isInterceptorApplied = true
    console.log('[路由拦截器] 路由拦截器应用完成')

  } catch (error) {
    console.error('[路由拦截器] 应用拦截器失败:', error)
  }
}

/**
 * 过滤重复路由
 */
function filterDuplicateRoutes(routes) {
  const filtered = []
  
  routes.forEach(route => {
    const routeKey = createRouteKey(route)
    
    if (!registeredRoutes.has(routeKey)) {
      registeredRoutes.add(routeKey)
      
      // 递归处理子路由
      if (route.children && Array.isArray(route.children)) {
        route.children = filterDuplicateRoutes(route.children)
      }
      
      filtered.push(route)
      console.log(`[路由拦截器] 添加路由: ${route.name || route.path}`)
    } else {
      console.log(`[路由拦截器] 跳过重复路由: ${route.name || route.path}`)
    }
  })
  
  return filtered
}

/**
 * 创建路由唯一标识
 */
function createRouteKey(route) {
  if (route.name) {
    return `name:${route.name}`
  } else if (route.path) {
    return `path:${route.path}`
  } else {
    return `unknown:${Math.random()}`
  }
}

/**
 * 重置拦截器状态
 */
export function resetRouteInterceptor() {
  isInterceptorApplied = false
  registeredRoutes.clear()
  console.log('[路由拦截器] 拦截器状态已重置')
}

/**
 * 获取已注册路由数量
 */
export function getRegisteredRoutesCount() {
  return registeredRoutes.size
}
