import request from '@/utils/request'

// ==================== 订阅管理API ====================

// 获取客户端订阅列表
export function getClientSubscriptions(clientId) {
  return request({
    url: `/emqx/subscription/client/${clientId}`,
    method: 'get'
  })
}

// 获取主题订阅者列表
export function getTopicSubscribers(topic) {
  return request({
    url: `/emqx/subscription/topic/${encodeURIComponent(topic)}/subscribers`,
    method: 'get'
  })
}

// 添加订阅
export function addSubscription(data) {
  return request({
    url: '/emqx/subscription/add',
    method: 'post',
    data: data
  })
}

// 删除订阅
export function removeSubscription(data) {
  return request({
    url: '/emqx/subscription/remove',
    method: 'delete',
    data: data
  })
}

// 更新订阅
export function updateSubscription(data) {
  return request({
    url: '/emqx/subscription/update',
    method: 'post',
    data: data
  })
}

// 批量添加订阅
export function batchAddSubscriptions(data) {
  return request({
    url: '/emqx/subscription/batch/add',
    method: 'post',
    data: data
  })
}

// 批量删除订阅
export function batchRemoveSubscriptions(data) {
  return request({
    url: '/emqx/subscription/batch/remove',
    method: 'post',
    data: data
  })
}

// 获取活跃订阅列表
export function getActiveSubscriptions(query) {
  return request({
    url: '/emqx/subscription/active',
    method: 'get',
    params: query
  })
}

// 搜索订阅
export function searchSubscriptions(query) {
  return request({
    url: '/emqx/subscription/search',
    method: 'get',
    params: query
  })
}

// 获取订阅统计
export function getSubscriptionStats() {
  return request({
    url: '/emqx/subscription/stats',
    method: 'get'
  })
}

// 验证订阅
export function verifySubscription(clientId, topic) {
  return request({
    url: '/emqx/subscription/verify',
    method: 'get',
    params: {
      clientId: clientId,
      topic: topic
    }
  })
}

// 导出订阅配置
export function exportSubscriptions(clientId) {
  return request({
    url: '/emqx/subscription/export',
    method: 'get',
    params: {
      clientId: clientId
    }
  })
}

// 导入订阅配置
export function importSubscriptions(data) {
  return request({
    url: '/emqx/subscription/import',
    method: 'post',
    data: data
  })
}

// 获取订阅历史
export function getSubscriptionHistory(query) {
  return request({
    url: '/emqx/subscription/history',
    method: 'get',
    params: query
  })
}

// 获取订阅性能指标
export function getSubscriptionMetrics() {
  return request({
    url: '/emqx/subscription/metrics',
    method: 'get'
  })
}

// ==================== 高级功能API ====================

// 清理无效订阅
export function cleanupInvalidSubscriptions() {
  return request({
    url: '/emqx/subscription/cleanup',
    method: 'post'
  })
}

// 监控订阅变化
export function monitorSubscriptionChanges() {
  return request({
    url: '/emqx/subscription/monitor',
    method: 'get'
  })
}

// 获取订阅分析报告
export function getSubscriptionAnalysis(query) {
  return request({
    url: '/emqx/subscription/analysis',
    method: 'get',
    params: query
  })
}

// 订阅健康检查
export function healthCheckSubscriptions() {
  return request({
    url: '/emqx/subscription/health-check',
    method: 'post'
  })
}

// 订阅备份
export function backupSubscriptions(data) {
  return request({
    url: '/emqx/subscription/backup',
    method: 'post',
    data: data
  })
}

// 订阅恢复
export function restoreSubscriptions(data) {
  return request({
    url: '/emqx/subscription/restore',
    method: 'post',
    data: data
  })
}

// 获取订阅模板
export function getSubscriptionTemplates() {
  return request({
    url: '/emqx/subscription/templates',
    method: 'get'
  })
}

// 应用订阅模板
export function applySubscriptionTemplate(data) {
  return request({
    url: '/emqx/subscription/templates/apply',
    method: 'post',
    data: data
  })
}

// 创建订阅模板
export function createSubscriptionTemplate(data) {
  return request({
    url: '/emqx/subscription/templates/create',
    method: 'post',
    data: data
  })
}

// 删除订阅模板
export function deleteSubscriptionTemplate(templateId) {
  return request({
    url: `/emqx/subscription/templates/${templateId}`,
    method: 'delete'
  })
}

// ==================== 实时监控API ====================

// 获取实时订阅状态
export function getRealTimeSubscriptionStatus() {
  return request({
    url: '/emqx/subscription/realtime/status',
    method: 'get'
  })
}

// 获取订阅流量统计
export function getSubscriptionTrafficStats(query) {
  return request({
    url: '/emqx/subscription/traffic/stats',
    method: 'get',
    params: query
  })
}

// 获取订阅错误日志
export function getSubscriptionErrorLogs(query) {
  return request({
    url: '/emqx/subscription/logs/errors',
    method: 'get',
    params: query
  })
}

// 订阅性能测试
export function performanceTestSubscription(data) {
  return request({
    url: '/emqx/subscription/performance/test',
    method: 'post',
    data: data
  })
}

// 订阅压力测试
export function stressTestSubscription(data) {
  return request({
    url: '/emqx/subscription/stress/test',
    method: 'post',
    data: data
  })
}

// ==================== 订阅规则管理API ====================

// 获取订阅规则列表
export function getSubscriptionRules() {
  return request({
    url: '/emqx/subscription/rules',
    method: 'get'
  })
}

// 创建订阅规则
export function createSubscriptionRule(data) {
  return request({
    url: '/emqx/subscription/rules',
    method: 'post',
    data: data
  })
}

// 更新订阅规则
export function updateSubscriptionRule(ruleId, data) {
  return request({
    url: `/emqx/subscription/rules/${ruleId}`,
    method: 'put',
    data: data
  })
}

// 删除订阅规则
export function deleteSubscriptionRule(ruleId) {
  return request({
    url: `/emqx/subscription/rules/${ruleId}`,
    method: 'delete'
  })
}

// 应用订阅规则
export function applySubscriptionRule(ruleId, data) {
  return request({
    url: `/emqx/subscription/rules/${ruleId}/apply`,
    method: 'post',
    data: data
  })
}

// 验证订阅规则
export function validateSubscriptionRule(data) {
  return request({
    url: '/emqx/subscription/rules/validate',
    method: 'post',
    data: data
  })
}
