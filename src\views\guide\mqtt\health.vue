<template>
  <div class="app-container">
    <!-- 健康状态概览 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <div class="health-card" :class="getStatusClass(healthStatus.connected)">
          <div class="card-header">
            <i class="el-icon-connection"></i>
            <span>连接状态</span>
          </div>
          <div class="card-content">
            <div class="status">{{ healthStatus.connected ? '已连接' : '已断开' }}</div>
            <div class="desc">{{ healthStatus.clientId }}</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="health-card">
          <div class="card-header">
            <i class="el-icon-time"></i>
            <span>连接时长</span>
          </div>
          <div class="card-content">
            <div class="number">{{ formatDuration(healthStatus.connectionDuration) }}</div>
            <div class="desc">当前连接持续时间</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="health-card">
          <div class="card-header">
            <i class="el-icon-refresh"></i>
            <span>重连次数</span>
          </div>
          <div class="card-content">
            <div class="number" :class="getReconnectClass(healthStatus.totalReconnections)">
              {{ healthStatus.totalReconnections || 0 }}
            </div>
            <div class="desc">总重连次数</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="health-card">
          <div class="card-header">
            <i class="el-icon-star-on"></i>
            <span>稳定性评分</span>
          </div>
          <div class="card-content">
            <div class="number" :class="getScoreClass(healthStatus.stabilityScore)">
              {{ Math.round(healthStatus.stabilityScore || 0) }}
            </div>
            <div class="desc">{{ healthStatus.stabilityLevel }}</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 操作按钮 -->
    <el-row class="mb20">
      <el-col :span="24">
        <el-button type="primary" icon="el-icon-refresh" @click="refreshData" :loading="loading">
          刷新数据
        </el-button>
        <el-button type="warning" icon="el-icon-connection" @click="forceReconnect" :loading="reconnecting">
          强制重连
        </el-button>
        <el-button type="info" icon="el-icon-delete" @click="resetStats">
          重置统计
        </el-button>
        <el-switch
          v-model="autoRefresh"
          @change="toggleAutoRefresh"
          active-text="自动刷新"
          inactive-text=""
          style="margin-left: 20px;">
        </el-switch>
      </el-col>
    </el-row>

    <!-- 详细统计信息 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>连接统计</span>
          </div>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="连接状态">
              <el-tag :type="healthStatus.connected ? 'success' : 'danger'">
                {{ healthStatus.connected ? '已连接' : '已断开' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="客户端ID">{{ healthStatus.clientId }}</el-descriptions-item>
            <el-descriptions-item label="设备类型">{{ healthStatus.deviceType }}</el-descriptions-item>
            <el-descriptions-item label="连接时长">{{ formatDuration(healthStatus.connectionDuration) }}</el-descriptions-item>
            <el-descriptions-item label="总断开次数">{{ healthStatus.totalDisconnections || 0 }}</el-descriptions-item>
            <el-descriptions-item label="总重连次数">{{ healthStatus.totalReconnections || 0 }}</el-descriptions-item>
            <el-descriptions-item label="稳定性评分">
              <span :class="getScoreClass(healthStatus.stabilityScore)">
                {{ Math.round(healthStatus.stabilityScore || 0) }} 分 ({{ healthStatus.stabilityLevel }})
              </span>
            </el-descriptions-item>
            <el-descriptions-item label="最后连接时间">
              {{ parseTime(healthStatus.lastConnectedTime) }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>诊断信息</span>
          </div>
          <div v-if="diagnosticInfo.healthLevel">
            <el-alert
              :title="`健康等级: ${diagnosticInfo.healthLevel}`"
              :type="getAlertType(diagnosticInfo.severity)"
              :description="`严重程度: ${diagnosticInfo.severity}`"
              show-icon
              :closable="false"
              class="mb10">
            </el-alert>
            
            <div class="diagnostic-section">
              <h4>当前状态</h4>
              <p>{{ diagnosticInfo.currentStatus }}</p>
            </div>
            
            <div class="diagnostic-section" v-if="diagnosticInfo.suggestions && diagnosticInfo.suggestions.length > 0">
              <h4>优化建议</h4>
              <ul>
                <li v-for="(suggestion, index) in diagnosticInfo.suggestions" :key="index">
                  {{ suggestion }}
                </li>
              </ul>
            </div>
          </div>
          <div v-else class="text-center">
            <el-button type="primary" @click="getDiagnosticInfo">获取诊断信息</el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getHealthStatus, getDetailedStats, getDiagnosticInfo, resetStats, forceReconnect } from "@/api/guide/mqtt"

export default {
  name: "GuideMqttHealth",
  data() {
    return {
      loading: false,
      reconnecting: false,
      autoRefresh: true,
      timer: null,
      healthStatus: {
        connected: false,
        connectionDuration: 0,
        totalDisconnections: 0,
        totalReconnections: 0,
        stabilityScore: 0,
        stabilityLevel: '未知',
        clientId: 'dwms_guide_server',
        deviceType: '智能导寻设备',
        lastConnectedTime: null
      },
      diagnosticInfo: {}
    };
  },
  created() {
    this.refreshData();
    this.getDiagnosticInfo();
    if (this.autoRefresh) {
      this.startAutoRefresh();
    }
  },
  beforeDestroy() {
    this.stopAutoRefresh();
  },
  methods: {
    /** 刷新数据 */
    refreshData() {
      this.loading = true;
      this.getHealthStatus();
    },
    
    /** 获取健康状态 */
    getHealthStatus() {
      getHealthStatus().then(response => {
        this.healthStatus = { ...this.healthStatus, ...response.data };
        this.loading = false;
      }).catch(error => {
        console.error('获取健康状态失败:', error);
        this.loading = false;
        // 使用模拟数据
        this.healthStatus = {
          connected: true,
          connectionDuration: 3600000,
          totalDisconnections: 2,
          totalReconnections: 3,
          stabilityScore: 85,
          stabilityLevel: '良好',
          clientId: 'dwms_guide_server',
          deviceType: '智能导寻设备',
          lastConnectedTime: new Date().getTime()
        };
      });
    },
    
    /** 获取诊断信息 */
    getDiagnosticInfo() {
      getDiagnosticInfo().then(response => {
        this.diagnosticInfo = response.data;
      }).catch(error => {
        console.error('获取诊断信息失败:', error);
        // 使用模拟数据
        this.diagnosticInfo = {
          currentStatus: '连接正常',
          healthLevel: '良好',
          severity: '正常',
          suggestions: ['连接基本稳定，可继续观察']
        };
      });
    },
    
    /** 强制重连 */
    forceReconnect() {
      this.$modal.confirm('确认要强制重连MQTT吗？这可能会短暂中断服务。').then(() => {
        this.reconnecting = true;
        forceReconnect().then(response => {
          this.$modal.msgSuccess("重连成功");
          this.reconnecting = false;
          // 延迟刷新数据
          setTimeout(() => {
            this.refreshData();
            this.getDiagnosticInfo();
          }, 2000);
        }).catch(error => {
          this.$modal.msgError("重连失败: " + error.message);
          this.reconnecting = false;
        });
      }).catch(() => {
        // 用户取消
      });
    },
    
    /** 重置统计 */
    resetStats() {
      this.$modal.confirm('确认要重置所有统计信息吗？').then(() => {
        resetStats().then(response => {
          this.$modal.msgSuccess("统计信息重置成功");
          this.refreshData();
          this.getDiagnosticInfo();
        }).catch(error => {
          this.$modal.msgError("重置失败: " + error.message);
        });
      }).catch(() => {
        // 用户取消
      });
    },
    
    /** 切换自动刷新 */
    toggleAutoRefresh(value) {
      if (value) {
        this.startAutoRefresh();
      } else {
        this.stopAutoRefresh();
      }
    },
    
    /** 开始自动刷新 */
    startAutoRefresh() {
      this.timer = setInterval(() => {
        this.getHealthStatus();
      }, 30000); // 30秒刷新一次
    },
    
    /** 停止自动刷新 */
    stopAutoRefresh() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },
    
    /** 格式化持续时间 */
    formatDuration(durationMs) {
      if (!durationMs || durationMs < 1000) return '< 1秒';
      if (durationMs < 60000) return Math.floor(durationMs / 1000) + '秒';
      if (durationMs < 3600000) return Math.floor(durationMs / 60000) + '分钟';
      if (durationMs < 86400000) return Math.floor(durationMs / 3600000) + '小时';
      return Math.floor(durationMs / 86400000) + '天';
    },
    
    /** 获取状态样式类 */
    getStatusClass(connected) {
      return connected ? 'status-connected' : 'status-disconnected';
    },
    
    /** 获取重连次数样式类 */
    getReconnectClass(count) {
      if (count === 0) return 'text-success';
      if (count < 5) return 'text-warning';
      return 'text-danger';
    },
    
    /** 获取评分样式类 */
    getScoreClass(score) {
      if (score >= 90) return 'text-success';
      if (score >= 70) return 'text-warning';
      return 'text-danger';
    },
    
    /** 获取告警类型 */
    getAlertType(severity) {
      switch (severity) {
        case '严重': return 'error';
        case '中等': return 'warning';
        case '轻微': return 'info';
        default: return 'success';
      }
    }
  }
};
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}

.mb10 {
  margin-bottom: 10px;
}

.health-card {
  background: #fff;
  border-radius: 4px;
  border: 1px solid #e6ebf5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  padding: 20px;
  text-align: center;
  transition: all 0.3s;
}

.health-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15), 0 0 10px rgba(0, 0, 0, 0.06);
}

.status-connected {
  border-left: 4px solid #67c23a;
}

.status-disconnected {
  border-left: 4px solid #f56c6c;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  color: #606266;
  font-size: 14px;
}

.card-header i {
  margin-right: 8px;
  font-size: 18px;
}

.card-content .number {
  font-size: 28px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.card-content .status {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.card-content .desc {
  color: #909399;
  font-size: 12px;
}

.text-success {
  color: #67c23a;
}

.text-warning {
  color: #e6a23c;
}

.text-danger {
  color: #f56c6c;
}

.diagnostic-section {
  margin-bottom: 15px;
}

.diagnostic-section h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 14px;
}

.diagnostic-section p {
  margin: 0;
  color: #606266;
  font-size: 13px;
}

.diagnostic-section ul {
  margin: 0;
  padding-left: 20px;
  color: #606266;
  font-size: 13px;
}

.diagnostic-section li {
  margin-bottom: 5px;
}

.text-center {
  text-align: center;
  padding: 20px;
}
</style>
