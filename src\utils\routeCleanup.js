/**
 * 强力路由清理工具
 * 彻底解决路由重复问题
 */

import router from '@/router'

// 存储已清理的路由信息
let isCleanupApplied = false
const cleanupLog = []

/**
 * 安全的路由清理 - 不破坏Vue Router内部结构
 */
function safeRouterCleanup() {
  try {
    console.log('[路由清理] 执行安全的路由清理')
    // 只记录当前状态，不修改内部结构
    if (router && router.matcher) {
      console.log('[路由清理] Vue Router状态正常')
    }
    return true
  } catch (error) {
    console.error('[路由清理] 路由状态检查失败:', error)
    return false
  }
}

/**
 * 彻底的路由去重处理
 */
export function thoroughRouteDeduplication(routes) {
  if (!Array.isArray(routes)) {
    console.warn('[路由清理] 输入不是数组，返回空数组')
    return []
  }
  
  console.log(`[路由清理] 开始彻底去重处理，输入路由数量: ${routes.length}`)
  
  // 使用Map来确保唯一性
  const routeMap = new Map()
  const pathMap = new Map()
  const result = []
  
  function processRoute(route, level = 0) {
    if (!route || typeof route !== 'object') {
      return null
    }
    
    const indent = '  '.repeat(level)
    
    // 跳过明显的重复或无效路由
    if (!route.name && !route.path) {
      console.log(`${indent}[路由清理] 跳过无名称无路径的路由`)
      return null
    }
    
    // 创建唯一标识符
    const routeKey = route.name || `path_${route.path}`
    const pathKey = route.path
    
    // 检查路由名称重复
    if (route.name && routeMap.has(route.name)) {
      console.log(`${indent}[路由清理] 跳过重复路由名称: ${route.name}`)
      return null
    }
    
    // 检查路径重复（排除特殊路径）
    if (route.path && 
        route.path !== '/' && 
        route.path !== '*' && 
        !route.path.startsWith('http') &&
        pathMap.has(route.path)) {
      console.log(`${indent}[路由清理] 跳过重复路径: ${route.path}`)
      return null
    }
    
    // 创建清理后的路由对象
    const cleanRoute = {
      ...route,
      children: undefined // 先清除子路由，稍后处理
    }
    
    // 处理子路由
    if (route.children && Array.isArray(route.children) && route.children.length > 0) {
      const cleanChildren = []
      route.children.forEach(child => {
        const cleanChild = processRoute(child, level + 1)
        if (cleanChild) {
          cleanChildren.push(cleanChild)
        }
      })
      
      if (cleanChildren.length > 0) {
        cleanRoute.children = cleanChildren
      }
    }
    
    // 注册路由
    if (route.name) {
      routeMap.set(route.name, cleanRoute)
    }
    if (route.path && route.path !== '/' && route.path !== '*') {
      pathMap.set(route.path, cleanRoute)
    }
    
    console.log(`${indent}[路由清理] 保留路由: ${route.name || route.path}`)
    return cleanRoute
  }
  
  // 处理所有路由
  routes.forEach(route => {
    const cleanRoute = processRoute(route)
    if (cleanRoute) {
      result.push(cleanRoute)
    }
  })
  
  console.log(`[路由清理] 去重完成，输出路由数量: ${result.length}`)
  console.log(`[路由清理] 注册的路由名称数量: ${routeMap.size}`)
  console.log(`[路由清理] 注册的路径数量: ${pathMap.size}`)
  
  return result
}

/**
 * 应用安全的路由清理
 */
export function applyThoroughRouteCleanup() {
  if (isCleanupApplied) {
    console.log('[路由清理] 清理已应用，跳过重复处理')
    return
  }

  console.log('[路由清理] 开始应用安全的路由清理...')

  try {
    // 1. 安全的路由状态检查
    const routerChecked = safeRouterCleanup()
    cleanupLog.push(`Vue Router状态检查: ${routerChecked ? '成功' : '失败'}`)

    // 2. 安全地重写addRoutes方法
    if (router && typeof router.addRoutes === 'function') {
      const originalAddRoutes = router.addRoutes
      router.addRoutes = function(routes) {
        console.log(`[路由清理] 拦截addRoutes调用，原始路由数量: ${routes.length}`)

        // 应用彻底去重
        const cleanedRoutes = thoroughRouteDeduplication(routes)
        console.log(`[路由清理] 清理后路由数量: ${cleanedRoutes.length}`)

        if (cleanedRoutes.length > 0) {
          return originalAddRoutes.call(this, cleanedRoutes)
        } else {
          console.log('[路由清理] 没有有效路由需要添加')
        }
      }
      cleanupLog.push('addRoutes方法重写: 成功')
    } else {
      cleanupLog.push('addRoutes方法重写: 跳过（方法不存在）')
    }

    isCleanupApplied = true
    console.log('[路由清理] 安全路由清理应用完成')

  } catch (error) {
    console.error('[路由清理] 应用清理失败:', error)
    cleanupLog.push(`清理失败: ${error.message}`)
  }
}

/**
 * 获取清理日志
 */
export function getCleanupLog() {
  return {
    isApplied: isCleanupApplied,
    log: [...cleanupLog],
    timestamp: new Date().toISOString()
  }
}

/**
 * 重置清理状态（用于测试）
 */
export function resetCleanupState() {
  isCleanupApplied = false
  cleanupLog.length = 0
  console.log('[路由清理] 清理状态已重置')
}
