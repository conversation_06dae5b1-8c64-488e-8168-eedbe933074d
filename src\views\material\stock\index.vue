<template>
  <div class="stock-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="page-title">
            <i class="el-icon-box"></i>
            <h1>物料库存管理</h1>
          </div>
          <p class="page-desc">智能化工仓储物料库存实时监控与管理</p>
        </div>
        <div class="header-right">
          <div class="header-stats">
            <div class="stat-item">
              <div class="stat-value">{{ stockList.length }}</div>
              <div class="stat-label">库存记录</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ warehouseCount }}</div>
              <div class="stat-label">仓库数量</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ totalQuantity }}</div>
              <div class="stat-label">总库存量</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section" v-show="showSearch">
      <el-card class="search-card" shadow="hover">
        <div slot="header" class="search-header">
          <span class="search-title">
            <i class="el-icon-search"></i>
            高级搜索
          </span>
          <el-button type="text" @click="toggleSearchExpand" class="expand-btn">
            {{ searchExpanded ? '收起' : '展开' }}
            <i :class="searchExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
          </el-button>
        </div>

        <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="100px" class="search-form">
          <!-- 基础搜索条件 -->
          <div class="search-row basic-search">
            <el-form-item label="物料编码" prop="materialCode">
              <el-input
                v-model="queryParams.materialCode"
                placeholder="请输入物料编码"
                clearable
                prefix-icon="el-icon-goods"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="物料名称" prop="materialName">
              <el-input
                v-model="queryParams.materialName"
                placeholder="请输入物料名称"
                clearable
                prefix-icon="el-icon-collection-tag"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="仓库名称" prop="warehouseName">
              <el-input
                v-model="queryParams.warehouseName"
                placeholder="请输入仓库名称"
                clearable
                prefix-icon="el-icon-office-building"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="批次号" prop="batchNo">
              <el-input
                v-model="queryParams.batchNo"
                placeholder="请输入批次号"
                clearable
                prefix-icon="el-icon-collection"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
          </div>

          <!-- 高级搜索条件 -->
          <div class="search-row advanced-search" v-show="searchExpanded">
            <el-form-item label="物料ID" prop="materialId">
              <el-input
                v-model="queryParams.materialId"
                placeholder="请输入物料ID"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="外部编码" prop="externalCode">
              <el-input
                v-model="queryParams.externalCode"
                placeholder="请输入外部系统物料编码"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="外部系统" prop="externalSystem">
              <el-input
                v-model="queryParams.externalSystem"
                placeholder="请输入外部系统标识"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="库位编码" prop="locationCode">
              <el-input
                v-model="queryParams.locationCode"
                placeholder="请输入库位编码"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="数量范围" prop="quantity">
              <el-input
                v-model="queryParams.quantity"
                placeholder="请输入数量"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="标准重量" prop="standardWeight">
              <el-input
                v-model="queryParams.standardWeight"
                placeholder="请输入标准重量"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="实际重量" prop="actualWeight">
              <el-input
                v-model="queryParams.actualWeight"
                placeholder="请输入实际重量"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="生产日期" prop="productionDate">
              <el-date-picker
                v-model="queryParams.productionDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择生产日期"
                clearable
              />
            </el-form-item>
            <el-form-item label="过期日期" prop="expiryDate">
              <el-date-picker
                v-model="queryParams.expiryDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择过期日期"
                clearable
              />
            </el-form-item>
          </div>

          <!-- 搜索按钮 -->
          <div class="search-actions">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">
              搜索
            </el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">
              重置
            </el-button>
            <el-button type="info" icon="el-icon-download" @click="handleExport" v-hasPermi="['material:stock:export']">
              导出
            </el-button>
          </div>
        </el-form>
      </el-card>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar-section">
      <el-card class="toolbar-card" shadow="hover">
        <div class="toolbar-content">
          <div class="toolbar-left">
            <div class="action-buttons">
              <el-button
                type="primary"
                icon="el-icon-plus"
                @click="handleAdd"
                v-hasPermi="['material:stock:add']"
                class="action-btn primary-btn"
              >
                <span>新增库存</span>
              </el-button>
              <el-button
                type="success"
                icon="el-icon-edit"
                :disabled="single"
                @click="handleUpdate"
                v-hasPermi="['material:stock:edit']"
                class="action-btn success-btn"
              >
                <span>批量修改</span>
              </el-button>
              <el-button
                type="danger"
                icon="el-icon-delete"
                :disabled="multiple"
                @click="handleDelete"
                v-hasPermi="['material:stock:remove']"
                class="action-btn danger-btn"
              >
                <span>批量删除</span>
              </el-button>
              <el-button
                type="info"
                icon="el-icon-refresh"
                @click="getList"
                class="action-btn info-btn"
              >
                <span>刷新数据</span>
              </el-button>
            </div>
          </div>
          <div class="toolbar-right">
            <div class="toolbar-tools">
              <el-tooltip content="显示/隐藏搜索" placement="top">
                <el-button
                  type="text"
                  icon="el-icon-search"
                  @click="showSearch = !showSearch"
                  :class="{ active: showSearch }"
                  class="tool-btn"
                />
              </el-tooltip>
              <el-tooltip content="刷新表格" placement="top">
                <el-button
                  type="text"
                  icon="el-icon-refresh"
                  @click="getList"
                  class="tool-btn"
                />
              </el-tooltip>
              <el-tooltip content="列设置" placement="top">
                <el-button
                  type="text"
                  icon="el-icon-setting"
                  class="tool-btn"
                />
              </el-tooltip>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-card class="table-card" shadow="hover">
        <el-table
          v-loading="loading"
          :data="stockList"
          @selection-change="handleSelectionChange"
          class="stock-table"
          stripe
          border
          :header-cell-style="{ background: '#f8fafc', color: '#374151', fontWeight: '600' }"
        >
          <el-table-column type="selection" width="55" align="center" />

          <!-- 基础信息列 -->
          <el-table-column label="物料信息" min-width="200" fixed="left">
            <template slot-scope="scope">
              <div class="material-info">
                <div class="material-code">
                  <el-tag size="mini" type="primary">{{ scope.row.materialCode }}</el-tag>
                </div>
                <div class="material-name">{{ scope.row.materialName }}</div>
                <div class="material-id">ID: {{ scope.row.materialId }}</div>
              </div>
            </template>
          </el-table-column>

          <!-- 仓储信息列 -->
          <el-table-column label="仓储位置" min-width="180">
            <template slot-scope="scope">
              <div class="warehouse-info">
                <div class="warehouse-name">
                  <i class="el-icon-office-building"></i>
                  {{ scope.row.warehouseName }}
                </div>
                <div class="location-info">
                  <span class="location-code">{{ scope.row.locationCode }}</span>
                  <span class="location-name">{{ scope.row.locationName }}</span>
                </div>
              </div>
            </template>
          </el-table-column>

          <!-- 批次信息列 -->
          <el-table-column label="批次信息" min-width="120">
            <template slot-scope="scope">
              <div class="batch-info">
                <el-tag size="small" type="info">{{ scope.row.batchNo || '无批次' }}</el-tag>
              </div>
            </template>
          </el-table-column>

          <!-- 库存数量列 -->
          <el-table-column label="库存数量" min-width="100" align="center">
            <template slot-scope="scope">
              <div class="quantity-info">
                <div class="quantity-value">{{ scope.row.quantity }}</div>
                <div class="quantity-unit">件</div>
              </div>
            </template>
          </el-table-column>

          <!-- 重量信息列 -->
          <el-table-column label="重量信息" min-width="160">
            <template slot-scope="scope">
              <div class="weight-info">
                <div class="weight-row">
                  <span class="weight-label">标准:</span>
                  <span class="weight-value">{{ scope.row.standardWeight || '-' }}kg</span>
                </div>
                <div class="weight-row">
                  <span class="weight-label">实际:</span>
                  <span class="weight-value">{{ scope.row.actualWeight || '-' }}kg</span>
                </div>
                <div class="weight-deviation" v-if="scope.row.weightDeviationPercent">
                  <el-tag
                    size="mini"
                    :type="getDeviationType(scope.row.weightDeviationPercent)"
                  >
                    {{ scope.row.weightDeviationPercent }}%
                  </el-tag>
                </div>
              </div>
            </template>
          </el-table-column>

          <!-- 日期信息列 -->
          <el-table-column label="日期信息" min-width="160">
            <template slot-scope="scope">
              <div class="date-info">
                <div class="date-row" v-if="scope.row.productionDate">
                  <span class="date-label">生产:</span>
                  <span class="date-value">{{ parseTime(scope.row.productionDate, '{y}-{m}-{d}') }}</span>
                </div>
                <div class="date-row" v-if="scope.row.expiryDate">
                  <span class="date-label">过期:</span>
                  <span class="date-value" :class="getExpiryClass(scope.row.expiryDate)">
                    {{ parseTime(scope.row.expiryDate, '{y}-{m}-{d}') }}
                  </span>
                </div>
              </div>
            </template>
          </el-table-column>

          <!-- 状态列 -->
          <el-table-column label="状态" width="100" align="center">
            <template slot-scope="scope">
              <el-tag
                :type="getStatusType(scope.row.status)"
                size="small"
              >
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <!-- 外部系统信息列 -->
          <el-table-column label="外部系统" min-width="140" v-if="showExternalColumns">
            <template slot-scope="scope">
              <div class="external-info" v-if="scope.row.externalCode || scope.row.externalSystem">
                <div class="external-code">{{ scope.row.externalCode }}</div>
                <div class="external-system">{{ scope.row.externalSystem }}</div>
              </div>
              <span v-else class="no-data">-</span>
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column label="操作" width="160" align="center" fixed="right">
            <template slot-scope="scope">
              <div class="action-buttons-cell">
                <el-tooltip content="查看详情" placement="top">
                  <el-button
                    size="mini"
                    type="primary"
                    icon="el-icon-view"
                    circle
                    @click="handleView(scope.row)"
                  />
                </el-tooltip>
                <el-tooltip content="编辑" placement="top">
                  <el-button
                    size="mini"
                    type="success"
                    icon="el-icon-edit"
                    circle
                    @click="handleUpdate(scope.row)"
                    v-hasPermi="['material:stock:edit']"
                  />
                </el-tooltip>
                <el-tooltip content="删除" placement="top">
                  <el-button
                    size="mini"
                    type="danger"
                    icon="el-icon-delete"
                    circle
                    @click="handleDelete(scope.row)"
                    v-hasPermi="['material:stock:remove']"
                  />
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改物料库存对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="物料ID" prop="materialId">
          <el-input v-model="form.materialId" placeholder="请输入物料ID" />
        </el-form-item>
        <el-form-item label="物料编码" prop="materialCode">
          <el-input v-model="form.materialCode" placeholder="请输入物料编码" />
        </el-form-item>
        <el-form-item label="物料名称" prop="materialName">
          <el-input v-model="form.materialName" placeholder="请输入物料名称" />
        </el-form-item>
        <el-form-item label="外部系统物料编码" prop="externalCode">
          <el-input v-model="form.externalCode" placeholder="请输入外部系统物料编码" />
        </el-form-item>
        <el-form-item label="外部系统标识" prop="externalSystem">
          <el-input v-model="form.externalSystem" placeholder="请输入外部系统标识" />
        </el-form-item>
        <el-form-item label="仓库ID" prop="warehouseId">
          <el-input v-model="form.warehouseId" placeholder="请输入仓库ID" />
        </el-form-item>
        <el-form-item label="仓库名称" prop="warehouseName">
          <el-input v-model="form.warehouseName" placeholder="请输入仓库名称" />
        </el-form-item>
        <el-form-item label="库位ID" prop="locationId">
          <el-input v-model="form.locationId" placeholder="请输入库位ID" />
        </el-form-item>
        <el-form-item label="库位名称" prop="locationName">
          <el-input v-model="form.locationName" placeholder="请输入库位名称" />
        </el-form-item>
        <el-form-item label="库位编码" prop="locationCode">
          <el-input v-model="form.locationCode" placeholder="请输入库位编码" />
        </el-form-item>
        <el-form-item label="批次号" prop="batchNo">
          <el-input v-model="form.batchNo" placeholder="请输入批次号" />
        </el-form-item>
        <el-form-item label="数量" prop="quantity">
          <el-input v-model="form.quantity" placeholder="请输入数量" />
        </el-form-item>
        <el-form-item label="标准重量" prop="standardWeight">
          <el-input v-model="form.standardWeight" placeholder="请输入标准重量" />
        </el-form-item>
        <el-form-item label="实际重量" prop="actualWeight">
          <el-input v-model="form.actualWeight" placeholder="请输入实际重量" />
        </el-form-item>
        <el-form-item label="重量偏差值" prop="weightDeviation">
          <el-input v-model="form.weightDeviation" placeholder="请输入重量偏差值" />
        </el-form-item>
        <el-form-item label="重量偏差百分比" prop="weightDeviationPercent">
          <el-input v-model="form.weightDeviationPercent" placeholder="请输入重量偏差百分比" />
        </el-form-item>
        <el-form-item label="生产日期" prop="productionDate">
          <el-date-picker clearable
            v-model="form.productionDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择生产日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="过期日期" prop="expiryDate">
          <el-date-picker clearable
            v-model="form.expiryDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择过期日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="版本号" prop="version">
          <el-input v-model="form.version" placeholder="请输入版本号" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listStock, getStock, delStock, addStock, updateStock } from "@/api/material/stock"

export default {
  name: "Stock",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 搜索展开状态
      searchExpanded: false,
      // 显示外部系统列
      showExternalColumns: false,
      // 总条数
      total: 0,
      // 物料库存表格数据
      stockList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        materialId: null,
        materialCode: null,
        materialName: null,
        externalCode: null,
        externalSystem: null,
        warehouseId: null,
        warehouseName: null,
        locationId: null,
        locationName: null,
        locationCode: null,
        batchNo: null,
        quantity: null,
        standardWeight: null,
        actualWeight: null,
        weightDeviation: null,
        weightDeviationPercent: null,
        productionDate: null,
        expiryDate: null,
        status: null,
        version: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        materialId: [
          { required: true, message: "物料ID不能为空", trigger: "blur" }
        ],
        materialCode: [
          { required: true, message: "物料编码不能为空", trigger: "blur" }
        ],
        materialName: [
          { required: true, message: "物料名称不能为空", trigger: "blur" }
        ],
        warehouseId: [
          { required: true, message: "仓库ID不能为空", trigger: "blur" }
        ],
        warehouseName: [
          { required: true, message: "仓库名称不能为空", trigger: "blur" }
        ],
        quantity: [
          { required: true, message: "数量不能为空", trigger: "blur" }
        ],
      }
    }
  },
  computed: {
    // 计算仓库数量
    warehouseCount() {
      const warehouses = new Set(this.stockList.map(item => item.warehouseId))
      return warehouses.size
    },
    // 计算总库存量
    totalQuantity() {
      return this.stockList.reduce((total, item) => {
        return total + (parseInt(item.quantity) || 0)
      }, 0)
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询物料库存列表 */
    getList() {
      this.loading = true
      listStock(this.queryParams).then(response => {
        this.stockList = response.rows
        this.total = response.total
        this.loading = false
        // 检查是否有外部系统数据
        this.checkExternalData()
      })
    },
    // 检查是否显示外部系统列
    checkExternalData() {
      this.showExternalColumns = this.stockList.some(item =>
        item.externalCode || item.externalSystem
      )
    },
    // 切换搜索展开状态
    toggleSearchExpand() {
      this.searchExpanded = !this.searchExpanded
    },
    // 获取重量偏差类型
    getDeviationType(percent) {
      const value = Math.abs(parseFloat(percent))
      if (value <= 2) return 'success'
      if (value <= 5) return 'warning'
      return 'danger'
    },
    // 获取过期日期样式类
    getExpiryClass(expiryDate) {
      if (!expiryDate) return ''
      const today = new Date()
      const expiry = new Date(expiryDate)
      const diffDays = Math.ceil((expiry - today) / (1000 * 60 * 60 * 24))

      if (diffDays < 0) return 'expired'
      if (diffDays <= 7) return 'expiring-soon'
      if (diffDays <= 30) return 'expiring-warning'
      return ''
    },
    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        '0': 'info',     // 待入库
        '1': 'success',  // 正常
        '2': 'warning',  // 预警
        '3': 'danger',   // 异常
        '4': 'info'      // 已出库
      }
      return statusMap[status] || 'info'
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        '0': '待入库',
        '1': '正常',
        '2': '预警',
        '3': '异常',
        '4': '已出库'
      }
      return statusMap[status] || '未知'
    },
    // 查看详情
    handleView(row) {
      this.$router.push({
        path: '/material/stock/detail',
        query: { id: row.id }
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        externalCode: null,
        externalSystem: null,
        warehouseId: null,
        warehouseName: null,
        locationId: null,
        locationName: null,
        locationCode: null,
        batchNo: null,
        quantity: null,
        standardWeight: null,
        actualWeight: null,
        weightDeviation: null,
        weightDeviationPercent: null,
        productionDate: null,
        expiryDate: null,
        status: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        version: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加物料库存"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getStock(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改物料库存"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateStock(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addStock(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除物料库存编号为"' + ids + '"的数据项？').then(function() {
        return delStock(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('material/stock/export', {
        ...this.queryParams
      }, `stock_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>

<style lang="scss" scoped>
// 主色调定义
$primary-blue: #1e3a8a;
$secondary-blue: #3b82f6;
$accent-blue: #60a5fa;
$light-blue: #dbeafe;
$success-green: #10b981;
$warning-orange: #f59e0b;
$danger-red: #ef4444;
$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-700: #374151;
$gray-900: #111827;

.stock-container {
  padding: 20px;
  background: #f8fafc;
  min-height: calc(100vh - 84px);

  // 页面头部样式
  .page-header {
    margin-bottom: 24px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: white;
      padding: 24px 32px;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      .header-left {
        .page-title {
          display: flex;
          align-items: center;
          margin-bottom: 8px;

          i {
            font-size: 32px;
            color: $primary-blue;
            margin-right: 12px;
          }

          h1 {
            font-size: 28px;
            font-weight: 700;
            color: $gray-900;
            margin: 0;
          }
        }

        .page-desc {
          color: $gray-500;
          font-size: 14px;
          margin: 0;
        }
      }

      .header-right {
        .header-stats {
          display: flex;
          gap: 32px;

          .stat-item {
            text-align: center;

            .stat-value {
              font-size: 24px;
              font-weight: 700;
              color: $primary-blue;
              line-height: 1;
            }

            .stat-label {
              font-size: 12px;
              color: $gray-500;
              margin-top: 4px;
            }
          }
        }
      }
    }
  }

  // 搜索区域样式
  .search-section {
    margin-bottom: 24px;

    .search-card {
      border-radius: 12px;
      border: none;

      .search-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .search-title {
          font-weight: 600;
          color: $gray-700;

          i {
            margin-right: 8px;
            color: $primary-blue;
          }
        }

        .expand-btn {
          color: $primary-blue;
          font-size: 14px;

          i {
            margin-left: 4px;
          }
        }
      }

      .search-form {
        .search-row {
          display: flex;
          flex-wrap: wrap;
          gap: 16px;
          margin-bottom: 16px;

          .el-form-item {
            margin-bottom: 0;
            flex: 1;
            min-width: 200px;
          }

          &.advanced-search {
            border-top: 1px solid $gray-200;
            padding-top: 16px;
            margin-top: 16px;
          }
        }

        .search-actions {
          display: flex;
          gap: 12px;
          justify-content: flex-end;
          margin-top: 20px;
        }
      }
    }
  }

  // 工具栏样式
  .toolbar-section {
    margin-bottom: 24px;

    .toolbar-card {
      border-radius: 12px;
      border: none;

      .toolbar-content {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .toolbar-left {
          .action-buttons {
            display: flex;
            gap: 12px;

            .action-btn {
              border-radius: 8px;
              font-weight: 500;
              transition: all 0.3s ease;

              &.primary-btn {
                background: linear-gradient(135deg, $primary-blue, $secondary-blue);
                border: none;

                &:hover {
                  transform: translateY(-2px);
                  box-shadow: 0 8px 25px rgba(30, 58, 138, 0.3);
                }
              }

              &.success-btn {
                background: linear-gradient(135deg, $success-green, #059669);
                border: none;

                &:hover {
                  transform: translateY(-2px);
                  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
                }
              }

              &.danger-btn {
                background: linear-gradient(135deg, $danger-red, #dc2626);
                border: none;

                &:hover {
                  transform: translateY(-2px);
                  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
                }
              }

              &.info-btn {
                background: linear-gradient(135deg, $gray-500, #4b5563);
                border: none;

                &:hover {
                  transform: translateY(-2px);
                  box-shadow: 0 8px 25px rgba(107, 114, 128, 0.3);
                }
              }
            }
          }
        }

        .toolbar-right {
          .toolbar-tools {
            display: flex;
            gap: 8px;

            .tool-btn {
              width: 36px;
              height: 36px;
              border-radius: 8px;
              border: 1px solid $gray-200;
              color: $gray-500;
              transition: all 0.3s ease;

              &:hover {
                color: $primary-blue;
                border-color: $primary-blue;
                background: rgba(30, 58, 138, 0.05);
              }

              &.active {
                color: $primary-blue;
                border-color: $primary-blue;
                background: rgba(30, 58, 138, 0.1);
              }
            }
          }
        }
      }
    }
  }

  // 表格区域样式
  .table-section {
    .table-card {
      border-radius: 12px;
      border: none;
      overflow: hidden;

      .stock-table {
        // 物料信息列样式
        .material-info {
          .material-code {
            margin-bottom: 6px;
          }

          .material-name {
            font-weight: 600;
            color: $gray-900;
            margin-bottom: 4px;
            font-size: 14px;
          }

          .material-id {
            font-size: 12px;
            color: $gray-500;
          }
        }

        // 仓储信息列样式
        .warehouse-info {
          .warehouse-name {
            display: flex;
            align-items: center;
            font-weight: 600;
            color: $gray-900;
            margin-bottom: 6px;

            i {
              margin-right: 6px;
              color: $primary-blue;
            }
          }

          .location-info {
            display: flex;
            flex-direction: column;
            gap: 2px;

            .location-code {
              font-size: 12px;
              color: $primary-blue;
              font-weight: 500;
            }

            .location-name {
              font-size: 12px;
              color: $gray-500;
            }
          }
        }

        // 批次信息样式
        .batch-info {
          text-align: center;
        }

        // 数量信息样式
        .quantity-info {
          text-align: center;

          .quantity-value {
            font-size: 18px;
            font-weight: 700;
            color: $primary-blue;
          }

          .quantity-unit {
            font-size: 12px;
            color: $gray-500;
          }
        }

        // 重量信息样式
        .weight-info {
          .weight-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;
            font-size: 12px;

            .weight-label {
              color: $gray-500;
            }

            .weight-value {
              color: $gray-900;
              font-weight: 500;
            }
          }

          .weight-deviation {
            text-align: center;
            margin-top: 6px;
          }
        }

        // 日期信息样式
        .date-info {
          .date-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;
            font-size: 12px;

            .date-label {
              color: $gray-500;
            }

            .date-value {
              color: $gray-900;
              font-weight: 500;

              &.expired {
                color: $danger-red;
                font-weight: 600;
              }

              &.expiring-soon {
                color: $danger-red;
              }

              &.expiring-warning {
                color: $warning-orange;
              }
            }
          }
        }

        // 外部系统信息样式
        .external-info {
          .external-code {
            font-size: 12px;
            color: $primary-blue;
            font-weight: 500;
            margin-bottom: 2px;
          }

          .external-system {
            font-size: 11px;
            color: $gray-500;
          }
        }

        .no-data {
          color: $gray-400;
          font-style: italic;
        }

        // 操作按钮样式
        .action-buttons-cell {
          display: flex;
          gap: 6px;
          justify-content: center;
        }
      }
    }
  }
}

// 全局样式覆盖
:deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid $gray-200;
  background: $gray-50;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background: $gray-50 !important;
  color: $gray-700 !important;
  font-weight: 600 !important;
  border-bottom: 2px solid $gray-200 !important;
}

:deep(.el-table td) {
  border-bottom: 1px solid $gray-100 !important;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background: rgba(248, 250, 252, 0.5) !important;
}

:deep(.el-table__body tr:hover > td) {
  background: rgba(30, 58, 138, 0.05) !important;
}

:deep(.el-button--mini.is-circle) {
  width: 28px;
  height: 28px;
}

:deep(.el-tag--mini) {
  height: 20px;
  line-height: 18px;
  font-size: 11px;
}

:deep(.el-input__inner) {
  border-radius: 8px;
  transition: all 0.3s ease;

  &:focus {
    border-color: $primary-blue;
    box-shadow: 0 0 0 2px rgba(30, 58, 138, 0.1);
  }
}

:deep(.el-date-editor.el-input) {
  width: 100%;
}

// 响应式设计
@media (max-width: 1200px) {
  .stock-container {
    .page-header .header-content {
      flex-direction: column;
      gap: 20px;
      text-align: center;
    }

    .search-form .search-row {
      .el-form-item {
        min-width: 180px;
      }
    }

    .toolbar-content {
      flex-direction: column;
      gap: 16px;
    }
  }
}

@media (max-width: 768px) {
  .stock-container {
    padding: 12px;

    .search-form .search-row {
      .el-form-item {
        min-width: 100%;
      }
    }

    .action-buttons {
      flex-wrap: wrap;
    }
  }
}
</style>
