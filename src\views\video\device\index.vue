<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="设备名称" prop="deviceName">
        <el-input v-model="queryParams.deviceName" placeholder="请输入设备名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="设备状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择设备状态" clearable>
          <el-option label="在线" value="1"/>
          <el-option label="离线" value="0"/>
          <el-option label="故障" value="2"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-video-play" size="mini" :disabled="multiple" @click="handleBatchTest">批量测试</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-setting" size="mini" @click="showSearch = !showSearch">
          {{ showSearch ? '隐藏' : '显示' }}搜索
        </el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="deviceList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="设备名称" align="center" prop="deviceName" min-width="120" show-overflow-tooltip />
      <el-table-column label="设备编码" align="center" prop="deviceCode" width="140" />
      <el-table-column label="设备类型" align="center" prop="deviceType" width="100">
        <template slot-scope="scope">
          <el-tag size="mini" :type="getDeviceTypeTag(scope.row.deviceType)">
            {{ getDeviceTypeText(scope.row.deviceType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="协议类型" align="center" prop="protocolType" width="100">
        <template slot-scope="scope">
          <el-tag size="mini" type="info">{{ scope.row.protocolType ? scope.row.protocolType.toUpperCase() : '-' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="IP地址" align="center" prop="ipAddress" width="130" />
      <el-table-column label="厂商" align="center" prop="manufacturer" width="100" show-overflow-tooltip />
      <el-table-column label="设备状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status == 1 ? 'success' : scope.row.status == 0 ? 'danger' : 'warning'">
            {{ scope.row.status == 1 ? '在线' : scope.row.status == 0 ? '离线' : '故障' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="连接状态" align="center" prop="connectionStatus" width="100">
        <template slot-scope="scope">
          <el-tag size="mini" :type="scope.row.connectionStatus == 1 ? 'success' : 'info'">
            {{ scope.row.connectionStatus == 1 ? '已连接' : '未连接' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="安装位置" align="center" prop="location" min-width="120" show-overflow-tooltip />
      <el-table-column label="最后心跳" align="center" prop="lastHeartbeat" width="150">
        <template slot-scope="scope">
          <span>{{ scope.row.lastHeartbeat ? parseTime(scope.row.lastHeartbeat, '{y}-{m}-{d} {h}:{i}') : '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="240">
        <template slot-scope="scope">
          <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)" style="margin-right: 5px;">
            <el-button size="mini" type="primary">
              操作<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="edit" icon="el-icon-edit">修改</el-dropdown-item>
              <el-dropdown-item command="test" icon="el-icon-video-play">测试连接</el-dropdown-item>
              <el-dropdown-item command="preview" icon="el-icon-view">预览</el-dropdown-item>
              <el-dropdown-item divided command="addStream" icon="el-icon-plus">添加流代理</el-dropdown-item>
              <el-dropdown-item command="delStream" icon="el-icon-minus">删除流代理</el-dropdown-item>
              <el-dropdown-item command="restartStream" icon="el-icon-refresh">重启流</el-dropdown-item>
              <el-dropdown-item command="streamStatus" icon="el-icon-info">流状态</el-dropdown-item>
              <el-dropdown-item divided command="delete" icon="el-icon-delete">删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    
    <el-pagination
      v-show="total>0"
      :current-page="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="queryParams.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange">
    </el-pagination>

    <!-- 添加或修改设备对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备名称" prop="deviceName">
              <el-input v-model="form.deviceName" placeholder="请输入设备名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备编码" prop="deviceCode">
              <el-input v-model="form.deviceCode" placeholder="请输入设备编码" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备类型" prop="deviceType">
              <el-select v-model="form.deviceType" placeholder="请选择设备类型">
                <el-option label="摄像头" value="camera" />
                <el-option label="网络录像机" value="nvr" />
                <el-option label="数字录像机" value="dvr" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="协议类型" prop="protocolType">
              <el-select v-model="form.protocolType" placeholder="请选择协议类型">
                <el-option label="RTSP" value="rtsp" />
                <el-option label="RTMP" value="rtmp" />
                <el-option label="GB28181" value="gb28181" />
                <el-option label="ONVIF" value="onvif" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备厂商" prop="manufacturer">
              <el-input v-model="form.manufacturer" placeholder="请输入设备厂商" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备型号" prop="model">
              <el-input v-model="form.model" placeholder="请输入设备型号" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="IP地址" prop="ipAddress">
              <el-input v-model="form.ipAddress" placeholder="请输入IP地址" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="端口号" prop="port">
              <el-input-number v-model="form.port" :min="1" :max="65535" placeholder="请输入端口号" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户名" prop="username">
              <el-input v-model="form.username" placeholder="请输入用户名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="密码" prop="password">
              <el-input v-model="form.password" type="password" placeholder="请输入密码" show-password />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="RTSP地址" prop="rtspUrl">
              <el-input v-model="form.rtspUrl" placeholder="rtsp://ip:port/path" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分辨率" prop="resolution">
              <el-select v-model="form.resolution" placeholder="请选择分辨率">
                <el-option label="1920x1080" value="1920x1080" />
                <el-option label="1280x720" value="1280x720" />
                <el-option label="640x480" value="640x480" />
                <el-option label="自定义" value="custom" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="安装位置" prop="location">
              <el-input v-model="form.location" placeholder="请输入安装位置" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属库房" prop="warehouseId">
              <el-select v-model="form.warehouseId" placeholder="请选择所属库房" clearable>
                <el-option label="仓库1" value="1" />
                <el-option label="仓库2" value="2" />
                <el-option label="仓库3" value="3" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="支持PTZ">
              <el-switch v-model="form.supportPtz" :active-value="1" :inactive-value="0" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="支持音频">
              <el-switch v-model="form.supportAudio" :active-value="1" :inactive-value="0" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="支持录制">
              <el-switch v-model="form.supportRecord" :active-value="1" :inactive-value="0" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDevice, getDevice, addDevice, updateDevice, delDevice, testDevice, batchTestDevice, addStreamProxy, delStreamProxy, restartStream, getStreamStatus } from "@/api/video/device";

export default {
  name: "VideoDevice",
  data() {
    return {
      loading: true,
      ids: [],
      single: true,
      multiple: true,
      showSearch: true,
      total: 0,
      deviceList: [],
      title: "",
      open: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceName: null,
        status: null
      },
      form: {},
      rules: {
        deviceName: [
          { required: true, message: "设备名称不能为空", trigger: "blur" },
          { min: 2, max: 100, message: "设备名称长度在 2 到 100 个字符", trigger: "blur" }
        ],
        deviceCode: [
          { required: true, message: "设备编码不能为空", trigger: "blur" },
          { min: 3, max: 50, message: "设备编码长度在 3 到 50 个字符", trigger: "blur" },
          { pattern: /^[A-Z0-9_]+$/, message: "设备编码只能包含大写字母、数字和下划线", trigger: "blur" }
        ],
        deviceType: [
          { required: true, message: "请选择设备类型", trigger: "change" }
        ],
        protocolType: [
          { required: true, message: "请选择协议类型", trigger: "change" }
        ],
        ipAddress: [
          { required: true, message: "IP地址不能为空", trigger: "blur" },
          { pattern: /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/, message: "请输入正确的IP地址格式", trigger: "blur" }
        ],
        port: [
          { required: true, message: "端口号不能为空", trigger: "blur" },
          { type: "number", min: 1, max: 65535, message: "端口号必须在 1-65535 之间", trigger: "blur" }
        ],
        username: [
          { required: true, message: "用户名不能为空", trigger: "blur" }
        ],
        password: [
          { required: true, message: "密码不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询设备列表 */
    getList() {
      this.loading = true;
      listDevice(this.queryParams).then(response => {
        this.deviceList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: null,
        deviceName: null,
        deviceCode: null,
        deviceType: "camera",
        protocolType: "rtsp",
        manufacturer: null,
        model: null,
        ipAddress: null,
        port: 554,
        username: null,
        password: null,
        rtspUrl: null,
        warehouseId: null,
        location: null,
        resolution: "1920x1080",
        supportPtz: 0,
        supportAudio: 0,
        supportRecord: 1,
        autoRecord: 0,
        status: 1,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加视频设备";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getDevice(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改视频设备";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateDevice(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDevice(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除设备编号为"' + ids + '"的数据项？').then(function() {
        return delDevice(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('video/device/export', {
        ...this.queryParams
      }, `device_${new Date().getTime()}.xlsx`)
    },
    /** 测试设备连接 */
    handleTest(row) {
      this.$modal.loading("正在测试设备连接...");
      testDevice(row.id).then(response => {
        this.$modal.closeLoading();
        if (response.code === 200) {
          this.$modal.msgSuccess("设备连接测试成功");
          this.getList(); // 刷新列表以更新设备状态
        } else {
          this.$modal.msgError("设备连接测试失败: " + response.msg);
        }
      }).catch(error => {
        this.$modal.closeLoading();
        this.$modal.msgError("设备连接测试异常: " + error.message);
      });
    },
    handleSizeChange(val) {
      this.queryParams.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.queryParams.pageNum = val;
      this.getList();
    },
    /** 获取设备类型标签样式 */
    getDeviceTypeTag(deviceType) {
      const tagMap = {
        'camera': 'primary',
        'nvr': 'success',
        'dvr': 'warning'
      };
      return tagMap[deviceType] || 'info';
    },
    /** 获取设备类型文本 */
    getDeviceTypeText(deviceType) {
      const textMap = {
        'camera': '摄像头',
        'nvr': '网络录像机',
        'dvr': '数字录像机'
      };
      return textMap[deviceType] || deviceType;
    },
    /** 预览设备视频 */
    handlePreview(row) {
      if (row.status !== 1) {
        this.$modal.msgWarning("设备离线，无法预览");
        return;
      }
      if (row.connectionStatus !== 1) {
        this.$modal.msgWarning("设备未连接，无法预览");
        return;
      }
      // TODO: 打开视频预览对话框
      this.$modal.msgInfo("视频预览功能开发中...");
    },
    /** 批量测试设备连接 */
    handleBatchTest() {
      if (this.ids.length === 0) {
        this.$modal.msgWarning("请选择要测试的设备");
        return;
      }

      this.$modal.confirm('是否确认测试选中的 ' + this.ids.length + ' 个设备的连接？').then(() => {
        this.$modal.loading("正在批量测试设备连接...");
        batchTestDevice(this.ids).then(response => {
          this.$modal.closeLoading();
          if (response.code === 200) {
            this.$modal.msgSuccess(response.msg);
            this.getList(); // 刷新列表以更新设备状态
          } else {
            this.$modal.msgError("批量测试失败: " + response.msg);
          }
        }).catch(error => {
          this.$modal.closeLoading();
          this.$modal.msgError("批量测试异常: " + error.message);
        });
      }).catch(() => {});
    },
    /** 处理操作命令 */
    handleCommand(command, row) {
      switch (command) {
        case 'edit':
          this.handleUpdate(row);
          break;
        case 'test':
          this.handleTest(row);
          break;
        case 'preview':
          this.handlePreview(row);
          break;
        case 'addStream':
          this.handleAddStream(row);
          break;
        case 'delStream':
          this.handleDelStream(row);
          break;
        case 'restartStream':
          this.handleRestartStream(row);
          break;
        case 'streamStatus':
          this.handleStreamStatus(row);
          break;
        case 'delete':
          this.handleDelete(row);
          break;
      }
    },
    /** 添加流代理 */
    handleAddStream(row) {
      this.$modal.confirm('是否为设备"' + row.deviceName + '"添加流代理？').then(() => {
        this.$modal.loading("正在添加流代理...");
        addStreamProxy(row.id).then(response => {
          this.$modal.closeLoading();
          if (response.code === 200) {
            this.$modal.msgSuccess("流代理添加成功");
          } else {
            this.$modal.msgError("流代理添加失败: " + response.msg);
          }
        }).catch(error => {
          this.$modal.closeLoading();
          this.$modal.msgError("流代理添加异常: " + error.message);
        });
      }).catch(() => {});
    },
    /** 删除流代理 */
    handleDelStream(row) {
      this.$modal.confirm('是否删除设备"' + row.deviceName + '"的流代理？').then(() => {
        this.$modal.loading("正在删除流代理...");
        delStreamProxy(row.id).then(response => {
          this.$modal.closeLoading();
          if (response.code === 200) {
            this.$modal.msgSuccess("流代理删除成功");
          } else {
            this.$modal.msgError("流代理删除失败: " + response.msg);
          }
        }).catch(error => {
          this.$modal.closeLoading();
          this.$modal.msgError("流代理删除异常: " + error.message);
        });
      }).catch(() => {});
    },
    /** 重启流 */
    handleRestartStream(row) {
      this.$modal.confirm('是否重启设备"' + row.deviceName + '"的流？').then(() => {
        this.$modal.loading("正在重启流...");
        restartStream(row.id).then(response => {
          this.$modal.closeLoading();
          if (response.code === 200) {
            this.$modal.msgSuccess("流重启成功");
          } else {
            this.$modal.msgError("流重启失败: " + response.msg);
          }
        }).catch(error => {
          this.$modal.closeLoading();
          this.$modal.msgError("流重启异常: " + error.message);
        });
      }).catch(() => {});
    },
    /** 查看流状态 */
    handleStreamStatus(row) {
      this.$modal.loading("正在获取流状态...");
      getStreamStatus(row.id).then(response => {
        this.$modal.closeLoading();
        if (response.code === 200) {
          const status = response.data;
          this.$modal.msgInfo(`设备: ${row.deviceName}\n流ID: ${status.streamId}\n状态: ${status.status}`);
        } else {
          this.$modal.msgError("获取流状态失败: " + response.msg);
        }
      }).catch(error => {
        this.$modal.closeLoading();
        this.$modal.msgError("获取流状态异常: " + error.message);
      });
    }
  }
};
</script>
