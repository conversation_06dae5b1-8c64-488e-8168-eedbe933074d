import request from '@/utils/request'

// 查询设备列表
export function listDevice(query) {
  return request({
    url: '/video/device/list',
    method: 'get',
    params: query
  })
}

// 查询设备详细
export function getDevice(id) {
  return request({
    url: '/video/device/' + id,
    method: 'get'
  })
}

// 新增设备
export function addDevice(data) {
  return request({
    url: '/video/device',
    method: 'post',
    data: data
  })
}

// 修改设备
export function updateDevice(data) {
  return request({
    url: '/video/device',
    method: 'put',
    data: data
  })
}

// 删除设备
export function delDevice(id) {
  return request({
    url: '/video/device/' + id,
    method: 'delete'
  })
}

// 测试设备连接
export function testDevice(id) {
  return request({
    url: '/video/device/test/' + id,
    method: 'post'
  })
}

// 批量测试设备连接
export function batchTestDevice(ids) {
  return request({
    url: '/video/device/test/batch',
    method: 'post',
    data: ids
  })
}

// PTZ控制
export function ptzControl(id, command, data) {
  return request({
    url: '/video/device/ptz/' + id + '/' + command,
    method: 'post',
    data: data
  })
}

// 获取预置位列表
export function getPresets(deviceId) {
  return request({
    url: '/video/device/presets/' + deviceId,
    method: 'get'
  })
}

// 转到预置位
export function gotoPreset(deviceId, presetNo) {
  return request({
    url: '/video/device/preset/' + deviceId + '/' + presetNo,
    method: 'post'
  })
}

// 设置预置位
export function setPreset(deviceId, data) {
  return request({
    url: '/video/device/preset/' + deviceId,
    method: 'post',
    data: data
  })
}

// 删除预置位
export function delPreset(deviceId, presetNo) {
  return request({
    url: '/video/device/preset/' + deviceId + '/' + presetNo,
    method: 'delete'
  })
}

// 获取设备播放地址
export function getPlayUrl(deviceId, protocol = 'hls') {
  return request({
    url: '/video/device/play/' + deviceId,
    method: 'get',
    params: { protocol }
  })
}

// 添加流代理
export function addStreamProxy(deviceId) {
  return request({
    url: '/video/device/stream/add/' + deviceId,
    method: 'post'
  })
}

// 删除流代理
export function delStreamProxy(deviceId) {
  return request({
    url: '/video/device/stream/del/' + deviceId,
    method: 'delete'
  })
}

// 重启流代理
export function restartStream(deviceId) {
  return request({
    url: '/video/device/stream/restart/' + deviceId,
    method: 'post'
  })
}

// 获取流状态
export function getStreamStatus(deviceId) {
  return request({
    url: '/video/device/stream/status/' + deviceId,
    method: 'get'
  })
}
