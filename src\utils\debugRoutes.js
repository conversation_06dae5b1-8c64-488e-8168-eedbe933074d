/**
 * 路由调试工具
 * 用于深度诊断路由问题
 */

// 调试后端返回的路由数据
export function debugBackendRoutes(routes) {
  console.log('=== 开始调试后端路由数据 ===')
  console.log('总路由数量:', routes.length)
  
  // 查找门禁管理相关的路由
  const accessRoutes = findAccessRelatedRoutes(routes)
  console.log('门禁相关路由:', accessRoutes)
  
  // 详细分析每个路由
  routes.forEach((route, index) => {
    if (route.meta && route.meta.title && route.meta.title.includes('门禁')) {
      console.log(`=== 门禁路由 ${index} ===`)
      console.log('路由对象:', JSON.stringify(route, null, 2))
    }
  })
  
  return routes
}

// 查找门禁相关的路由
function findAccessRelatedRoutes(routes) {
  const accessRoutes = []
  
  function searchRoutes(routeList, level = 0) {
    routeList.forEach(route => {
      const indent = '  '.repeat(level)
      
      if (route.meta && route.meta.title) {
        if (route.meta.title.includes('门禁') || 
            route.path === 'access' || 
            route.name === 'Access' ||
            route.name === 'AccessManagement') {
          
          console.log(`${indent}找到门禁路由: ${route.meta.title} (${route.name}) - ${route.path}`)
          accessRoutes.push({
            level,
            title: route.meta.title,
            name: route.name,
            path: route.path,
            component: route.component,
            children: route.children ? route.children.length : 0
          })
          
          // 如果有子路由，也记录下来
          if (route.children && route.children.length > 0) {
            console.log(`${indent}  子路由数量: ${route.children.length}`)
            route.children.forEach((child, idx) => {
              console.log(`${indent}    ${idx + 1}. ${child.meta?.title || child.name} - ${child.path}`)
            })
          }
        }
      }
      
      if (route.children && route.children.length > 0) {
        searchRoutes(route.children, level + 1)
      }
    })
  }
  
  searchRoutes(routes)
  return accessRoutes
}

// 强制添加缺失的门禁子菜单
export function forceAddMissingAccessMenus(routes) {
  console.log('=== 开始强制添加缺失的门禁菜单 ===')
  
  // 查找门禁管理主菜单
  let accessMainMenu = null
  
  function findAccessMainMenu(routeList) {
    for (let route of routeList) {
      if ((route.meta && route.meta.title === '门禁管理') || 
          route.name === 'AccessManagement' ||
          route.path === 'access') {
        return route
      }
      if (route.children && route.children.length > 0) {
        const found = findAccessMainMenu(route.children)
        if (found) return found
      }
    }
    return null
  }
  
  accessMainMenu = findAccessMainMenu(routes)
  
  if (!accessMainMenu) {
    console.log('未找到门禁管理主菜单，创建一个新的')
    accessMainMenu = {
      name: 'AccessManagement',
      path: 'access',
      component: 'Layout',
      meta: {
        title: '门禁管理',
        icon: 'system'
      },
      children: []
    }
    routes.push(accessMainMenu)
  }
  
  console.log('找到门禁管理主菜单:', accessMainMenu.meta?.title)
  console.log('当前子菜单数量:', accessMainMenu.children ? accessMainMenu.children.length : 0)
  
  // 确保子菜单数组存在
  if (!accessMainMenu.children) {
    accessMainMenu.children = []
  }
  
  // 定义应该存在的所有子菜单
  const expectedSubMenus = [
    {
      name: 'AccessDevice',
      path: 'access-device',
      component: 'access/device/index',
      meta: { title: '门禁设备', icon: 'system' }
    },
    {
      name: 'FaceRecord',
      path: 'recognition',
      component: 'access/recognition/index',
      meta: { title: '人脸识别记录', icon: 'system' }
    },
    {
      name: 'AccessRecord',
      path: 'access-log',
      component: 'access/log/index',
      meta: { title: '门禁记录', icon: 'system' }
    },
    {
      name: 'PersonApproval',
      path: 'audit',
      component: 'access/audit/index',
      meta: { title: '人员审核', icon: 'system' }
    },
    {
      name: 'AccessPermission',
      path: 'access-permission',
      component: 'access/access/index',
      meta: { title: '门禁权限', icon: 'system' }
    },
    {
      name: 'UserFaceInfo',
      path: 'face',
      component: 'access/face/index',
      meta: { title: '用户人脸信息', icon: 'system' }
    },
    {
      name: 'DeviceParams',
      path: 'config',
      component: 'access/config/index',
      meta: { title: '设备参数', icon: 'system' }
    },
    {
      name: 'EmqxConfig',
      path: 'emqx',
      component: 'access/emqx/index',
      meta: { title: 'EMQX配置', icon: 'system' }
    }
  ]
  
  // 检查并添加缺失的子菜单
  const existingPaths = new Set(accessMainMenu.children.map(child => child.path))
  const existingNames = new Set(accessMainMenu.children.map(child => child.name))
  
  expectedSubMenus.forEach(subMenu => {
    if (!existingPaths.has(subMenu.path) && !existingNames.has(subMenu.name)) {
      console.log(`强制添加缺失的子菜单: ${subMenu.meta.title}`)
      accessMainMenu.children.push(subMenu)
    } else {
      console.log(`子菜单已存在: ${subMenu.meta.title}`)
    }
  })
  
  console.log('修复后的门禁子菜单数量:', accessMainMenu.children.length)
  accessMainMenu.children.forEach((child, idx) => {
    console.log(`  ${idx + 1}. ${child.meta?.title} - ${child.path}`)
  })
  
  return routes
}
