<template>
  <div class="template-selector">
    <el-alert
      title="选择合适的审批模板快速创建多级审批流程"
      type="info"
      :closable="false"
      style="margin-bottom: 20px;">
    </el-alert>

    <el-row :gutter="20">
      <!-- 标准模板 -->
      <el-col :span="12">
        <el-card class="template-card">
          <div slot="header" class="clearfix">
            <span>标准审批模板</span>
          </div>
          <div class="template-list">
            <div 
              v-for="template in standardTemplates" 
              :key="template.levels"
              class="template-item"
              @click="selectStandardTemplate(template)"
            >
              <div class="template-header">
                <h4>{{ template.name }}</h4>
                <el-tag type="primary" size="small">{{ template.levels }}级</el-tag>
              </div>
              <div class="template-description">
                {{ template.description }}
              </div>
              <div class="template-levels">
                <el-tag 
                  v-for="(level, index) in template.levelNames" 
                  :key="index"
                  size="mini"
                  style="margin-right: 4px; margin-bottom: 4px;"
                >
                  {{ level }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 业务模板 -->
      <el-col :span="12">
        <el-card class="template-card">
          <div slot="header" class="clearfix">
            <span>业务审批模板</span>
          </div>
          <div class="template-list">
            <div 
              v-for="template in businessTemplates" 
              :key="template.businessType"
              class="template-item"
              @click="selectBusinessTemplate(template)"
            >
              <div class="template-header">
                <h4>{{ template.name }}</h4>
                <el-tag :type="template.tagType" size="small">{{ template.businessType }}</el-tag>
              </div>
              <div class="template-description">
                {{ template.description }}
              </div>
              <div class="template-levels">
                <el-tag 
                  v-for="(level, index) in template.levelNames" 
                  :key="index"
                  size="mini"
                  style="margin-right: 4px; margin-bottom: 4px;"
                >
                  {{ level }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 自定义模板 -->
    <el-card class="template-card" style="margin-top: 20px;">
      <div slot="header" class="clearfix">
        <span>自定义模板</span>
      </div>
      <el-form :model="customForm" :rules="customRules" ref="customForm" :inline="true">
        <el-form-item label="审批级别" prop="levels">
          <el-input-number 
            v-model="customForm.levels" 
            :min="1" 
            :max="5" 
            placeholder="请输入级别数"
          />
        </el-form-item>
        <el-form-item label="模板名称" prop="name">
          <el-input v-model="customForm.name" placeholder="请输入模板名称" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="createCustomTemplate">创建自定义模板</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <div style="text-align: center; margin-top: 30px;">
      <el-button @click="handleCancel">取消</el-button>
    </div>
  </div>
</template>

<script>
import { getTemplate } from "@/api/approval/multilevel";

export default {
  name: "TemplateSelector",
  data() {
    return {
      standardTemplates: [
        {
          levels: 1,
          name: "一级审批",
          description: "适用于简单的审批流程，只需要一级审批",
          levelNames: ["部门审批"]
        },
        {
          levels: 2,
          name: "二级审批",
          description: "适用于中等重要性的审批流程",
          levelNames: ["部门审批", "经理审批"]
        },
        {
          levels: 3,
          name: "三级审批",
          description: "适用于重要的审批流程",
          levelNames: ["部门审批", "经理审批", "总监审批"]
        },
        {
          levels: 4,
          name: "四级审批",
          description: "适用于非常重要的审批流程",
          levelNames: ["部门审批", "经理审批", "总监审批", "副总审批"]
        },
        {
          levels: 5,
          name: "五级审批",
          description: "适用于最高级别的审批流程",
          levelNames: ["部门审批", "经理审批", "总监审批", "副总审批", "总经理审批"]
        }
      ],
      businessTemplates: [
        {
          businessType: "MATERIAL_APPROVAL",
          name: "物料审批模板",
          description: "专门用于物料申请的审批流程",
          tagType: "primary",
          levelNames: ["部门审批", "采购经理", "总监审批"]
        },
        {
          businessType: "PURCHASE_APPROVAL",
          name: "采购审批模板",
          description: "专门用于采购申请的审批流程",
          tagType: "success",
          levelNames: ["部门审批", "采购经理", "财务审批", "总经理审批"]
        },
        {
          businessType: "EXPENSE_APPROVAL",
          name: "费用审批模板",
          description: "专门用于费用报销的审批流程",
          tagType: "warning",
          levelNames: ["部门审批", "财务审批", "总监审批"]
        }
      ],
      customForm: {
        levels: 1,
        name: ''
      },
      customRules: {
        levels: [
          { required: true, message: '请输入审批级别', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '请输入模板名称', trigger: 'blur' }
        ]
      }
    };
  },
  methods: {
    /** 选择标准模板 */
    selectStandardTemplate(template) {
      getTemplate(template.levels).then(response => {
        const templateData = {
          name: template.name,
          description: template.description,
          levels: response.data
        };
        this.$emit('select', templateData);
      }).catch(error => {
        this.$message.error('加载模板失败: ' + error.message);
      });
    },

    /** 选择业务模板 */
    selectBusinessTemplate(template) {
      // 根据业务类型创建特定的审批级别配置
      const levels = this.createBusinessLevels(template);
      const templateData = {
        name: template.name,
        description: template.description,
        businessType: template.businessType,
        levels: levels
      };
      this.$emit('select', templateData);
    },

    /** 创建业务级别配置 */
    createBusinessLevels(template) {
      const levels = [];
      template.levelNames.forEach((levelName, index) => {
        levels.push({
          level: index + 1,
          levelName: levelName,
          approvalType: '0', // 指定人员
          approvers: '',
          approvalCondition: 'ANY',
          timeoutAction: '0',
          timeoutHours: 24
        });
      });
      return levels;
    },

    /** 创建自定义模板 */
    createCustomTemplate() {
      this.$refs.customForm.validate(valid => {
        if (!valid) {
          return;
        }

        getTemplate(this.customForm.levels).then(response => {
          const templateData = {
            name: this.customForm.name,
            description: `自定义${this.customForm.levels}级审批模板`,
            levels: response.data
          };
          this.$emit('select', templateData);
        }).catch(error => {
          this.$message.error('创建模板失败: ' + error.message);
        });
      });
    },

    /** 取消操作 */
    handleCancel() {
      this.$emit('cancel');
    }
  }
};
</script>

<style scoped>
.template-selector {
  padding: 20px;
}

.template-card {
  height: 100%;
}

.template-list {
  max-height: 400px;
  overflow-y: auto;
}

.template-item {
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.template-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
  transform: translateY(-2px);
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.template-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
}

.template-description {
  color: #606266;
  font-size: 14px;
  margin-bottom: 10px;
  line-height: 1.5;
}

.template-levels {
  min-height: 24px;
}

.el-card {
  border-radius: 8px;
}

.el-card__header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}
</style>
