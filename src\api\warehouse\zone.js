import request from '@/utils/request'

// 查询区域信息列表
export function listZone(query) {
  console.log("查询区域列表参数:", query)
  return request({
    url: '/warehouse/zone/list',
    method: 'get',
    params: query
  })
}

// 查询区域信息详细
export function getZone(id) {
  console.log("查询区域详情ID:", id)
  return request({
    url: '/warehouse/zone/' + id,
    method: 'get'
  })
}

// 新增区域信息
export function addZone(data) {
  console.log("新增区域数据:", data)
  return request({
    url: '/warehouse/zone',
    method: 'post',
    data: data
  })
}

// 修改区域信息
export function updateZone(data) {
  console.log("修改区域数据:", data)
  return request({
    url: '/warehouse/zone',
    method: 'put',
    data: data
  })
}

// 删除区域信息
export function delZone(id) {
  return request({
    url: '/warehouse/zone/' + id,
    method: 'delete'
  })
}

// 修改区域状态
export function changeStatus(id, status) {
  const data = {
    id,
    status
  }
  return request({
    url: '/warehouse/zone/changeStatus',
    method: 'put',
    data: data
  })
}
