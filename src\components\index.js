/**
 * 全局组件注册
 * 统一注册项目中的公共组件
 */

import Vue from 'vue'

// 导入菜单图标组件
import MenuIcon from './MenuIcon'

// 全局注册组件
const components = {
  MenuIcon
}

// 批量注册组件
Object.keys(components).forEach(key => {
  Vue.component(key, components[key])
})

// 也可以通过插件方式注册
const ComponentsPlugin = {
  install(Vue) {
    Object.keys(components).forEach(key => {
      Vue.component(key, components[key])
    })
  }
}

export default ComponentsPlugin
export { MenuIcon }
