import request from '@/utils/request'

// 数据分流管理API

/**
 * 处理MQTT消息
 */
export function processMessage(data) {
  return request({
    url: '/emqx/data-stream/process',
    method: 'post',
    data: data
  })
}

/**
 * 批量处理MQTT消息
 */
export function batchProcessMessages(data) {
  return request({
    url: '/emqx/data-stream/batch-process',
    method: 'post',
    data: data
  })
}

/**
 * 获取数据流统计信息
 */
export function getDataStreamStatistics() {
  return request({
    url: '/emqx/data-stream/statistics',
    method: 'get'
  })
}

/**
 * 清理过期统计信息
 */
export function cleanExpiredStatistics(expireHours = 24) {
  return request({
    url: '/emqx/data-stream/clean-expired',
    method: 'post',
    params: {
      expireHours
    }
  })
}

/**
 * 模拟设备数据上报
 */
export function simulateDeviceData(data) {
  return request({
    url: '/emqx/data-stream/simulate-device-data',
    method: 'post',
    data: data
  })
}
