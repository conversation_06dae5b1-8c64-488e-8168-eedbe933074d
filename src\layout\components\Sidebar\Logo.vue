<template>
  <div class="sidebar-logo-container" :class="{'collapse':collapse}" :style="{ backgroundColor: sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground }">
    <transition name="sidebarLogoFade">
      <router-link v-if="collapse" key="collapse" class="sidebar-logo-link" to="/">
        <div class="logo-content">
          <svg-icon icon-class="ruiyun-logo" class="sidebar-logo-svg" />
        </div>
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" to="/">
        <div class="logo-content">
          <svg-icon icon-class="ruiyun-logo" class="sidebar-logo-svg" />
          <div class="logo-text">
            <h1 class="sidebar-title" :style="{ color: sideTheme === 'theme-dark' ? variables.logoTitleColor : variables.logoLightTitleColor }">{{ title }}</h1>
            <span class="sidebar-subtitle" :style="{ color: sideTheme === 'theme-dark' ? variables.logoTitleColor : variables.logoLightTitleColor }">管理系统</span>
          </div>
        </div>
      </router-link>
    </transition>
  </div>
</template>

<script>
import variables from '@/assets/styles/variables.scss'

export default {
  name: 'SidebarLogo',
  props: {
    collapse: {
      type: Boolean,
      required: true
    }
  },
  computed: {
    variables() {
      return variables
    },
    sideTheme() {
      return this.$store.state.settings.sideTheme
    }
  },
  data() {
    return {
      title: '睿云'
    }
  }
}
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 60px;
  background: #2b2f3a;
  overflow: hidden;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .sidebar-logo-link {
    display: flex;
    align-items: center;
    height: 100%;
    width: 100%;
    padding: 0 16px;
    text-decoration: none;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.05);
    }

    .logo-content {
      display: flex;
      align-items: center;
      width: 100%;

      .sidebar-logo-svg {
        width: 32px;
        height: 32px;
        color: #60a5fa;
        flex-shrink: 0;
        transition: all 0.3s ease;
      }

      .logo-text {
        margin-left: 12px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        min-width: 0;

        .sidebar-title {
          margin: 0;
          color: #fff;
          font-weight: 700;
          font-size: 16px;
          line-height: 1.2;
          font-family: 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .sidebar-subtitle {
          margin: 0;
          color: rgba(255, 255, 255, 0.7);
          font-size: 11px;
          line-height: 1;
          font-weight: 400;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }

  &.collapse {
    .logo-content {
      justify-content: center;

      .sidebar-logo-svg {
        margin: 0;
      }

      .logo-text {
        display: none;
      }
    }
  }
}
</style>
