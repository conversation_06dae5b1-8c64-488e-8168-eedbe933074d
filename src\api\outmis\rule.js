import request from '@/utils/request'

// 查询出库防错规则列表
export function listRule(query) {
  return request({
    url: '/outmis/rule/list',
    method: 'get',
    params: query
  })
}

// 查询出库防错规则详细
export function getRule(ruleId) {
  return request({
    url: '/outmis/rule/' + ruleId,
    method: 'get'
  })
}

// 根据物料编码查询规则信息
export function getRuleByMaterialCode(materialCode) {
  return request({
    url: '/outmis/rule/material/' + materialCode,
    method: 'get'
  })
}

// 新增出库防错规则
export function addRule(data) {
  return request({
    url: '/outmis/rule',
    method: 'post',
    data: data
  })
}

// 修改出库防错规则
export function updateRule(data) {
  return request({
    url: '/outmis/rule',
    method: 'put',
    data: data
  })
}

// 删除出库防错规则
export function delRule(ruleId) {
  return request({
    url: '/outmis/rule/' + ruleId,
    method: 'delete'
  })
}
