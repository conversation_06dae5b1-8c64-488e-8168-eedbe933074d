<template>
  <div class="mqtt-messages">
    <!-- 搜索和操作栏 -->
    <div class="search-bar">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="主题">
          <el-input
            v-model="searchForm.topic"
            placeholder="请输入主题"
            clearable
            @keyup.enter.native="handleSearch">
          </el-input>
        </el-form-item>
        <el-form-item label="消息类型">
          <el-select v-model="searchForm.messageType" placeholder="请选择类型" clearable>
            <el-option label="普通消息" value="normal"></el-option>
            <el-option label="保留消息" value="retained"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchForm.timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
      
      <div class="action-buttons">
        <el-button type="success" icon="el-icon-refresh" @click="refreshData">刷新</el-button>
        <el-button type="primary" icon="el-icon-plus" @click="showPublishDialog">发布消息</el-button>
        <el-button type="info" icon="el-icon-view" @click="showRetainedMessages">保留消息</el-button>
        <el-button type="warning" icon="el-icon-download" @click="exportData">导出</el-button>
        <el-button type="danger" icon="el-icon-delete" @click="cleanupMessages">清理过期</el-button>
      </div>
    </div>

    <!-- 消息列表 -->
    <el-table
      v-loading="loading"
      :data="messageList"
      style="width: 100%">
      
      <el-table-column prop="messageId" label="消息ID" width="200">
        <template slot-scope="scope">
          <el-link type="primary" @click="showMessageDetails(scope.row)">
            {{ scope.row.messageId }}
          </el-link>
        </template>
      </el-table-column>
      
      <el-table-column prop="topic" label="主题" min-width="200">
        <template slot-scope="scope">
          <el-tag size="mini" type="info">{{ scope.row.topic }}</el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="qos" label="QoS" width="60" align="center">
        <template slot-scope="scope">
          <el-tag size="mini" :type="getQosType(scope.row.qos)">
            {{ scope.row.qos }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="retained" label="保留" width="80" align="center">
        <template slot-scope="scope">
          <el-tag size="mini" :type="scope.row.retained ? 'success' : 'info'">
            {{ scope.row.retained ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="clientId" label="客户端ID" width="150">
        <template slot-scope="scope">
          <el-link type="primary" @click="viewClient(scope.row.clientId)">
            {{ scope.row.clientId }}
          </el-link>
        </template>
      </el-table-column>
      
      <el-table-column prop="payloadSize" label="消息大小" width="100" align="center">
        <template slot-scope="scope">
          {{ formatBytes(scope.row.payloadSize) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="timestamp" label="时间" width="160">
        <template slot-scope="scope">
          {{ formatTime(scope.row.timestamp) }}
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="150" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="primary" @click="showMessageDetails(scope.row)">详情</el-button>
          <el-button size="mini" type="info" @click="resendMessage(scope.row)">重发</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total">
      </el-pagination>
    </div>

    <!-- 消息详情对话框 -->
    <el-dialog title="消息详情" :visible.sync="detailDialogVisible" width="800px">
      <div v-if="selectedMessage" class="message-details">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="消息ID">{{ selectedMessage.messageId }}</el-descriptions-item>
          <el-descriptions-item label="主题">{{ selectedMessage.topic }}</el-descriptions-item>
          <el-descriptions-item label="QoS">{{ selectedMessage.qos }}</el-descriptions-item>
          <el-descriptions-item label="保留消息">{{ selectedMessage.retained ? '是' : '否' }}</el-descriptions-item>
          <el-descriptions-item label="客户端ID">{{ selectedMessage.clientId }}</el-descriptions-item>
          <el-descriptions-item label="消息大小">{{ formatBytes(selectedMessage.payloadSize) }}</el-descriptions-item>
          <el-descriptions-item label="发布时间">{{ formatTime(selectedMessage.timestamp) }}</el-descriptions-item>
          <el-descriptions-item label="过期时间">{{ formatTime(selectedMessage.expireTime) }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="payload-section" style="margin-top: 20px;">
          <h4>消息内容</h4>
          <el-input
            type="textarea"
            :rows="8"
            :value="selectedMessage.payload"
            readonly
            placeholder="消息内容">
          </el-input>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="resendMessage(selectedMessage)">重发消息</el-button>
      </div>
    </el-dialog>

    <!-- 发布消息对话框 -->
    <el-dialog title="发布消息" :visible.sync="publishDialogVisible" width="600px">
      <el-form :model="publishForm" :rules="publishRules" ref="publishForm" label-width="100px">
        <el-form-item label="主题" prop="topic">
          <el-input v-model="publishForm.topic" placeholder="请输入主题"></el-input>
        </el-form-item>
        <el-form-item label="QoS" prop="qos">
          <el-select v-model="publishForm.qos" placeholder="请选择QoS">
            <el-option label="0 - 最多一次" :value="0"></el-option>
            <el-option label="1 - 至少一次" :value="1"></el-option>
            <el-option label="2 - 恰好一次" :value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="保留消息">
          <el-switch v-model="publishForm.retained"></el-switch>
        </el-form-item>
        <el-form-item label="消息内容" prop="payload">
          <el-input
            type="textarea"
            :rows="6"
            v-model="publishForm.payload"
            placeholder="请输入消息内容">
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="publishDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="publishMessage">发布</el-button>
      </div>
    </el-dialog>

    <!-- 保留消息对话框 -->
    <el-dialog title="保留消息" :visible.sync="retainedDialogVisible" width="800px">
      <el-table :data="retainedMessages" style="width: 100%">
        <el-table-column prop="topic" label="主题" min-width="200"></el-table-column>
        <el-table-column prop="qos" label="QoS" width="60" align="center"></el-table-column>
        <el-table-column prop="payloadSize" label="大小" width="100" align="center">
          <template slot-scope="scope">
            {{ formatBytes(scope.row.payloadSize) }}
          </template>
        </el-table-column>
        <el-table-column prop="timestamp" label="时间" width="160">
          <template slot-scope="scope">
            {{ formatTime(scope.row.timestamp) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template slot-scope="scope">
            <el-button size="mini" type="danger" @click="deleteRetainedMessage(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="retainedDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  getMessageHistory, 
  getRetainedMessages,
  deleteRetainedMessage,
  publishMessage,
  resendMessage,
  cleanupExpiredMessages,
  exportMessageHistory 
} from '@/api/mqtt/message'

export default {
  name: 'MqttMessages',
  data() {
    return {
      loading: false,
      messageList: [],
      retainedMessages: [],
      selectedMessage: null,
      searchForm: {
        topic: '',
        messageType: '',
        timeRange: []
      },
      publishForm: {
        topic: '',
        qos: 0,
        retained: false,
        payload: ''
      },
      publishRules: {
        topic: [
          { required: true, message: '请输入主题', trigger: 'blur' }
        ],
        payload: [
          { required: true, message: '请输入消息内容', trigger: 'blur' }
        ]
      },
      pagination: {
        page: 1,
        size: 20,
        total: 0
      },
      detailDialogVisible: false,
      publishDialogVisible: false,
      retainedDialogVisible: false
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    async loadData() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.page,
          size: this.pagination.size,
          ...this.searchForm
        }
        if (this.searchForm.timeRange && this.searchForm.timeRange.length === 2) {
          params.startTime = this.searchForm.timeRange[0]
          params.endTime = this.searchForm.timeRange[1]
        }
        const response = await getMessageHistory(params)
        this.messageList = response.data.data
        this.pagination.total = response.data.total
      } catch (error) {
        this.$message.error('获取消息列表失败: ' + error.message)
      } finally {
        this.loading = false
      }
    },
    
    handleSearch() {
      this.pagination.page = 1
      this.loadData()
    },
    
    resetSearch() {
      this.searchForm = {
        topic: '',
        messageType: '',
        timeRange: []
      }
      this.handleSearch()
    },
    
    refreshData() {
      this.loadData()
    },
    
    handleSizeChange(val) {
      this.pagination.size = val
      this.loadData()
    },
    
    handleCurrentChange(val) {
      this.pagination.page = val
      this.loadData()
    },
    
    showMessageDetails(message) {
      this.selectedMessage = message
      this.detailDialogVisible = true
    },
    
    showPublishDialog() {
      this.publishForm = {
        topic: '',
        qos: 0,
        retained: false,
        payload: ''
      }
      this.publishDialogVisible = true
    },
    
    async showRetainedMessages() {
      try {
        const response = await getRetainedMessages()
        this.retainedMessages = response.data
        this.retainedDialogVisible = true
      } catch (error) {
        this.$message.error('获取保留消息失败: ' + error.message)
      }
    },
    
    async publishMessage() {
      try {
        await this.$refs.publishForm.validate()
        await publishMessage(this.publishForm)
        this.$message.success('消息发布成功')
        this.publishDialogVisible = false
        this.loadData()
      } catch (error) {
        if (error.message) {
          this.$message.error('发布消息失败: ' + error.message)
        }
      }
    },
    
    async resendMessage(message) {
      try {
        await resendMessage(message.messageId)
        this.$message.success('消息重发成功')
      } catch (error) {
        this.$message.error('重发消息失败: ' + error.message)
      }
    },
    
    async deleteRetainedMessage(message) {
      try {
        await this.$confirm(`确定要删除主题 ${message.topic} 的保留消息吗？`, '确认操作', {
          type: 'warning'
        })
        
        await deleteRetainedMessage(message.topic)
        this.$message.success('保留消息已删除')
        this.showRetainedMessages()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除保留消息失败: ' + error.message)
        }
      }
    },
    
    async cleanupMessages() {
      try {
        const { value } = await this.$prompt('请输入过期时间（小时）', '清理过期消息', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /^\d+$/,
          inputErrorMessage: '请输入有效的数字'
        })
        
        await cleanupExpiredMessages(parseInt(value))
        this.$message.success('过期消息清理完成')
        this.loadData()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('清理过期消息失败: ' + error.message)
        }
      }
    },
    
    async exportData() {
      try {
        const params = { ...this.searchForm }
        await exportMessageHistory(params)
        this.$message.success('导出成功')
      } catch (error) {
        this.$message.error('导出失败: ' + error.message)
      }
    },
    
    viewClient(clientId) {
      this.$router.push(`/mqtt/clients?clientId=${clientId}`)
    },
    
    getQosType(qos) {
      const typeMap = { 0: 'info', 1: 'success', 2: 'warning' }
      return typeMap[qos] || 'info'
    },
    
    formatTime(timestamp) {
      if (!timestamp) return 'N/A'
      return new Date(timestamp).toLocaleString()
    },
    
    formatBytes(bytes) {
      if (!bytes) return '0 B'
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(1024))
      return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
    }
  }
}
</script>

<style lang="scss" scoped>
.mqtt-messages {
  padding: 20px;
  
  .search-bar {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    
    .search-form {
      flex: 1;
    }
    
    .action-buttons {
      margin-left: 20px;
    }
  }
  
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
  
  .message-details {
    margin-bottom: 20px;
  }
  
  .payload-section {
    h4 {
      margin-bottom: 10px;
      color: #333;
    }
  }
}
</style>
