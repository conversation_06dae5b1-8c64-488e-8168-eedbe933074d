<template>
  <div class="multilevel-detail">
    <el-card v-loading="loading" class="box-card">
      <!-- 基本信息 -->
      <div slot="header" class="clearfix">
        <span>多级审批详情</span>
        <el-button style="float: right;" size="mini" @click="refreshData">刷新</el-button>
      </div>

      <div v-if="workflowInfo">
        <!-- 工作流基本信息 -->
        <el-descriptions title="基本信息" :column="2" border>
          <el-descriptions-item label="流程名称">{{ workflowInfo.workflowName }}</el-descriptions-item>
          <el-descriptions-item label="业务类型">
            <el-tag :type="getBusinessTypeTagType(workflowInfo.businessType)">
              {{ getBusinessTypeName(workflowInfo.businessType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="审批级别">
            <el-tag type="success">{{ levels.length }}级审批</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <dict-tag :options="dict.type.sys_normal_disable" :value="workflowInfo.status"/>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">
            {{ parseTime(workflowInfo.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
          </el-descriptions-item>
          <el-descriptions-item label="流程描述" :span="2">
            {{ workflowInfo.description || '暂无描述' }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 审批级别详情 -->
        <div style="margin-top: 30px;">
          <h3>审批级别配置</h3>
          <el-timeline>
            <el-timeline-item
              v-for="(level, index) in levels"
              :key="index"
              :icon="getTimelineIcon(level)"
              :type="getTimelineType(level)"
              :size="'large'"
            >
              <el-card class="level-detail-card">
                <div slot="header" class="level-header">
                  <span class="level-title">{{ level.levelName }}</span>
                  <el-tag size="small" :type="getApprovalTypeTag(level.approvalType)">
                    {{ getApprovalTypeText(level.approvalType) }}
                  </el-tag>
                </div>

                <el-row :gutter="20">
                  <el-col :span="12">
                    <div class="detail-item">
                      <label>审批条件：</label>
                      <el-tag size="mini" :type="getConditionType(level.approvalCondition)">
                        {{ getConditionText(level.approvalCondition) }}
                      </el-tag>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="detail-item">
                      <label>超时时间：</label>
                      <span>{{ level.timeoutHours || 24 }}小时</span>
                    </div>
                  </el-col>
                </el-row>

                <el-row :gutter="20" style="margin-top: 10px;">
                  <el-col :span="12">
                    <div class="detail-item">
                      <label>超时处理：</label>
                      <span>{{ getTimeoutActionText(level.timeoutAction) }}</span>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="detail-item">
                      <label>级别顺序：</label>
                      <span>第{{ level.level }}级</span>
                    </div>
                  </el-col>
                </el-row>

                <!-- 审批人员信息 -->
                <div class="approver-info" style="margin-top: 15px;">
                  <label>审批人员：</label>
                  <div class="approver-list">
                    <template v-if="level.approvalType === '0'">
                      <!-- 指定人员 -->
                      <el-tag
                        v-for="approver in getApprovers(level.approvers)"
                        :key="approver.id"
                        type="info"
                        size="small"
                        style="margin-right: 8px; margin-bottom: 4px;"
                      >
                        {{ approver.name }}
                      </el-tag>
                    </template>
                    <template v-else-if="level.approvalType === '1'">
                      <!-- 指定角色 -->
                      <el-tag type="warning" size="small">
                        角色：{{ getRoleName(level.roleId) }}
                      </el-tag>
                    </template>
                    <template v-else-if="level.approvalType === '2'">
                      <!-- 指定部门 -->
                      <el-tag type="success" size="small">
                        部门：{{ getDeptName(level.deptId) }}
                      </el-tag>
                    </template>
                    <template v-else-if="level.approvalType === '3'">
                      <!-- 部门主管 -->
                      <el-tag type="primary" size="small">部门主管</el-tag>
                    </template>
                    <template v-else-if="level.approvalType === '4'">
                      <!-- 上级主管 -->
                      <el-tag type="danger" size="small">上级主管</el-tag>
                    </template>
                  </div>
                </div>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>

        <!-- 流程统计 -->
        <div style="margin-top: 30px;">
          <h3>流程统计</h3>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-statistic title="审批级别" :value="levels.length" suffix="级" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="预计审批人数" :value="totalApprovers" suffix="人" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="预计总时长" :value="estimatedTime" suffix="小时" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="复杂度" :value="complexityLevel" />
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 空状态 -->
      <el-empty v-else description="暂无数据" :image-size="100" />
    </el-card>
  </div>
</template>

<script>
import { getWorkflow, getWorkflowOptions } from "@/api/approval/workflow";
import { getWorkflowLevels, getUsers, getRoles, getDepts } from "@/api/approval/multilevel";

export default {
  name: "MultilevelDetail",
  dicts: ['sys_normal_disable'],
  props: {
    workflowId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      loading: false,
      workflowInfo: null,
      levels: [],
      userList: [],
      roleList: [],
      deptList: [],
      // 业务类型选项
      businessTypeOptions: []
    };
  },
  computed: {
    /** 总审批人数 */
    totalApprovers() {
      return this.levels.reduce((total, level) => {
        if (level.approvalType === '0' && level.approvers) {
          return total + level.approvers.split(',').length;
        }
        return total + 1; // 角色、部门等按1人计算
      }, 0);
    },

    /** 预计总时长 */
    estimatedTime() {
      return this.levels.reduce((total, level) => {
        return total + (level.timeoutHours || 24);
      }, 0);
    },

    /** 复杂度等级 */
    complexityLevel() {
      const levelCount = this.levels.length;
      if (levelCount <= 1) return '简单';
      if (levelCount <= 3) return '中等';
      if (levelCount <= 5) return '复杂';
      return '极复杂';
    }
  },
  created() {
    this.loadData();
    this.loadBasicData();
    this.getBusinessTypeOptions();
  },
  methods: {
    /** 加载数据 */
    async loadData() {
      this.loading = true;
      try {
        // 加载工作流信息
        const workflowResponse = await getWorkflow(this.workflowId);
        this.workflowInfo = workflowResponse.data;

        // 加载审批级别
        const levelsResponse = await getWorkflowLevels(this.workflowId);
        this.levels = levelsResponse.data || [];

      } catch (error) {
        this.$message.error('加载数据失败: ' + error.message);
      } finally {
        this.loading = false;
      }
    },

    /** 加载基础数据 */
    async loadBasicData() {
      try {
        const [usersResponse, rolesResponse, deptsResponse] = await Promise.all([
          getUsers(),
          getRoles(),
          getDepts()
        ]);
        
        this.userList = usersResponse.rows || [];
        this.roleList = rolesResponse.rows || [];
        this.deptList = deptsResponse.data || [];
      } catch (error) {
        console.warn('加载基础数据失败:', error);
      }
    },

    /** 刷新数据 */
    refreshData() {
      this.loadData();
    },

    /** 获取时间线图标 */
    getTimelineIcon(level) {
      switch (level.approvalType) {
        case '0': return 'el-icon-user';
        case '1': return 'el-icon-s-custom';
        case '2': return 'el-icon-office-building';
        case '3': return 'el-icon-user-solid';
        case '4': return 'el-icon-s-promotion';
        default: return 'el-icon-s-check';
      }
    },

    /** 获取时间线类型 */
    getTimelineType(level) {
      return level.level <= 2 ? 'primary' : 'success';
    },

    /** 获取审批类型标签 */
    getApprovalTypeTag(type) {
      const tags = {
        '0': 'primary',
        '1': 'warning',
        '2': 'success',
        '3': 'info',
        '4': 'danger'
      };
      return tags[type] || 'info';
    },

    /** 获取审批类型文本 */
    getApprovalTypeText(type) {
      const texts = {
        '0': '指定人员',
        '1': '指定角色',
        '2': '指定部门',
        '3': '部门主管',
        '4': '上级主管'
      };
      return texts[type] || '未知';
    },

    /** 获取条件类型 */
    getConditionType(condition) {
      const types = {
        'ANY': 'success',
        'ALL': 'warning',
        'RATIO': 'info'
      };
      return types[condition] || '';
    },

    /** 获取条件文本 */
    getConditionText(condition) {
      const texts = {
        'ANY': '任意一人同意',
        'ALL': '全部人员同意',
        'RATIO': '按比例同意'
      };
      return texts[condition] || '未知';
    },

    /** 获取超时处理文本 */
    getTimeoutActionText(action) {
      const texts = {
        '0': '无操作',
        '1': '自动通过',
        '2': '自动拒绝',
        '3': '转交上级'
      };
      return texts[action] || '未知';
    },

    /** 获取审批人员 */
    getApprovers(approverIds) {
      if (!approverIds) return [];
      
      const ids = approverIds.split(',').map(id => parseInt(id)).filter(id => !isNaN(id));
      return ids.map(id => {
        const user = this.userList.find(u => u.userId === id);
        return user ? { id: user.userId, name: user.nickName } : { id, name: '未知用户' };
      });
    },

    /** 获取角色名称 */
    getRoleName(roleId) {
      const role = this.roleList.find(r => r.roleId === roleId);
      return role ? role.roleName : '未知角色';
    },

    /** 获取部门名称 */
    getDeptName(deptId) {
      const dept = this.deptList.find(d => d.deptId === deptId);
      return dept ? dept.deptName : '未知部门';
    },

    /** 获取业务类型选项 */
    getBusinessTypeOptions() {
      getWorkflowOptions().then(res => {
        if (res.code === 200 && res.data) {
          // 使用Map对象确保每个业务类型只出现一次
          const businessTypeMap = new Map();
          res.data.forEach(item => {
            if (!businessTypeMap.has(item.businessType)) {
              businessTypeMap.set(item.businessType, {
                value: item.businessType,
                label: item.businessTypeName || item.businessType
              });
            }
          });

          // 将Map转换回数组
          this.businessTypeOptions = Array.from(businessTypeMap.values());
        }
      });
    },

    /** 获取业务类型名称 */
    getBusinessTypeName(businessType) {
      const option = this.businessTypeOptions.find(item => item.value === businessType);
      return option ? option.label : businessType;
    },

    /** 获取业务类型标签类型 */
    getBusinessTypeTagType(businessType) {
      const typeMap = {
        'MATERIAL_APPROVAL': 'primary',
        'MATERIAL_BILL': 'success',
        'PURCHASE_APPROVAL': 'warning',
        'EXPENSE_APPROVAL': 'danger'
      };
      return typeMap[businessType] || 'info';
    }
  }
};
</script>

<style scoped>
.multilevel-detail {
  padding: 20px;
}

.level-detail-card {
  margin-bottom: 10px;
}

.level-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.level-title {
  font-weight: bold;
  font-size: 16px;
}

.detail-item {
  margin-bottom: 8px;
}

.detail-item label {
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
}

.approver-info label {
  font-weight: bold;
  color: #606266;
  display: block;
  margin-bottom: 8px;
}

.approver-list {
  min-height: 24px;
}

.el-timeline {
  padding-left: 0;
}

.el-statistic {
  text-align: center;
}
</style>
