<template>
  <div class="chart-3d">
    <div class="chart-controls" v-if="showControls">
      <el-button-group size="mini">
        <el-button @click="resetView">
          <i class="el-icon-refresh"></i>
          重置视角
        </el-button>
        <el-button @click="toggleAutoRotate">
          <i :class="autoRotate ? 'el-icon-video-pause' : 'el-icon-video-play'"></i>
          {{ autoRotate ? '停止旋转' : '自动旋转' }}
        </el-button>
        <el-button @click="toggleWireframe">
          <i class="el-icon-view"></i>
          {{ wireframe ? '实体模式' : '线框模式' }}
        </el-button>
        <el-button @click="downloadImage">
          <i class="el-icon-download"></i>
          下载图片
        </el-button>
      </el-button-group>
    </div>
    
    <base-chart
      ref="chart"
      :option="chartOption"
      :width="width"
      :height="height"
      :loading="loading"
      @chart-ready="onChartReady"
    />
    
    <div class="chart-info" v-if="showInfo">
      <div class="info-item">
        <span class="label">数据点数:</span>
        <span class="value">{{ dataCount }}</span>
      </div>
      <div class="info-item">
        <span class="label">视角:</span>
        <span class="value">{{ viewAngle.alpha }}°, {{ viewAngle.beta }}°</span>
      </div>
      <div class="info-item">
        <span class="label">缩放:</span>
        <span class="value">{{ zoomLevel }}%</span>
      </div>
    </div>
  </div>
</template>

<script>
import BaseChart from './BaseChart.vue'
import 'echarts-gl'

export default {
  name: 'Chart3D',
  components: {
    BaseChart
  },
  
  props: {
    // 图表类型
    chartType: {
      type: String,
      default: 'scatter3D', // scatter3D, bar3D, surface, line3D
      validator: value => ['scatter3D', 'bar3D', 'surface', 'line3D'].includes(value)
    },
    // 图表数据
    data: {
      type: Array,
      required: true
    },
    // 图表宽度
    width: {
      type: String,
      default: '100%'
    },
    // 图表高度
    height: {
      type: String,
      default: '500px'
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    },
    // 是否显示控制按钮
    showControls: {
      type: Boolean,
      default: true
    },
    // 是否显示信息
    showInfo: {
      type: Boolean,
      default: true
    },
    // 坐标轴配置
    axisConfig: {
      type: Object,
      default: () => ({
        x: { name: 'X轴', min: 0, max: 100 },
        y: { name: 'Y轴', min: 0, max: 100 },
        z: { name: 'Z轴', min: 0, max: 100 }
      })
    },
    // 颜色配置
    colorConfig: {
      type: Object,
      default: () => ({
        type: 'gradient',
        colors: ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffcc', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026']
      })
    },
    // 3D配置
    config3D: {
      type: Object,
      default: () => ({
        alpha: 40,
        beta: 40,
        distance: 200,
        autoRotate: false,
        autoRotateSpeed: 10
      })
    }
  },
  
  data() {
    return {
      chart: null,
      autoRotate: false,
      wireframe: false,
      viewAngle: {
        alpha: 40,
        beta: 40
      },
      zoomLevel: 100
    }
  },
  
  computed: {
    dataCount() {
      return Array.isArray(this.data) ? this.data.length : 0
    },
    
    chartOption() {
      const baseOption = {
        tooltip: {
          trigger: 'item',
          formatter: this.getTooltipFormatter()
        },
        visualMap: this.getVisualMapConfig(),
        grid3D: this.getGrid3DConfig(),
        xAxis3D: this.getAxisConfig('x'),
        yAxis3D: this.getAxisConfig('y'),
        zAxis3D: this.getAxisConfig('z'),
        series: [this.getSeriesConfig()]
      }
      
      return baseOption
    }
  },
  
  watch: {
    data: {
      handler() {
        this.updateChart()
      },
      deep: true
    },
    
    autoRotate(newVal) {
      this.updateAutoRotate(newVal)
    }
  },
  
  mounted() {
    this.autoRotate = this.config3D.autoRotate
  },
  
  methods: {
    /** 获取网格3D配置 */
    getGrid3DConfig() {
      return {
        boxWidth: 100,
        boxHeight: 100,
        boxDepth: 100,
        viewControl: {
          alpha: this.config3D.alpha,
          beta: this.config3D.beta,
          distance: this.config3D.distance,
          autoRotate: this.autoRotate,
          autoRotateSpeed: this.config3D.autoRotateSpeed,
          rotateSensitivity: 1,
          zoomSensitivity: 1,
          panSensitivity: 1
        },
        light: {
          main: {
            intensity: 1.2,
            shadow: true
          },
          ambient: {
            intensity: 0.3
          }
        },
        environment: 'auto'
      }
    },
    
    /** 获取坐标轴配置 */
    getAxisConfig(axis) {
      const config = this.axisConfig[axis]
      return {
        name: config.name,
        min: config.min,
        max: config.max,
        type: 'value',
        axisLabel: {
          textStyle: {
            color: '#666'
          }
        },
        axisLine: {
          lineStyle: {
            color: '#999'
          }
        },
        splitLine: {
          lineStyle: {
            color: '#ddd'
          }
        }
      }
    },
    
    /** 获取视觉映射配置 */
    getVisualMapConfig() {
      if (this.chartType === 'surface') {
        return {
          type: 'continuous',
          min: 0,
          max: 100,
          inRange: {
            color: this.colorConfig.colors
          },
          calculable: true,
          realtime: false,
          top: 'top',
          right: 'right'
        }
      }
      
      return {
        type: 'continuous',
        min: 0,
        max: 100,
        inRange: {
          color: this.colorConfig.colors
        },
        show: false
      }
    },
    
    /** 获取系列配置 */
    getSeriesConfig() {
      const baseConfig = {
        type: this.chartType,
        data: this.processData(),
        shading: 'lambert',
        emphasis: {
          label: {
            show: true,
            textStyle: {
              color: '#000',
              fontSize: 12
            }
          }
        }
      }
      
      switch (this.chartType) {
        case 'scatter3D':
          return {
            ...baseConfig,
            symbolSize: 8,
            itemStyle: {
              opacity: 0.8
            }
          }
          
        case 'bar3D':
          return {
            ...baseConfig,
            bevelSize: 0.3,
            bevelSmoothness: 2
          }
          
        case 'surface':
          return {
            ...baseConfig,
            wireframe: {
              show: this.wireframe
            },
            equation: {
              x: { step: 0.1 },
              y: { step: 0.1 }
            }
          }
          
        case 'line3D':
          return {
            ...baseConfig,
            lineStyle: {
              width: 4,
              opacity: 0.8
            }
          }
          
        default:
          return baseConfig
      }
    },
    
    /** 处理数据 */
    processData() {
      if (!Array.isArray(this.data)) {
        return []
      }
      
      switch (this.chartType) {
        case 'surface':
          return this.processSurfaceData()
        default:
          return this.data
      }
    },
    
    /** 处理曲面数据 */
    processSurfaceData() {
      // 如果数据是函数形式，生成网格数据
      if (typeof this.data[0] === 'function') {
        const func = this.data[0]
        const data = []
        const step = 0.1
        
        for (let x = this.axisConfig.x.min; x <= this.axisConfig.x.max; x += step) {
          for (let y = this.axisConfig.y.min; y <= this.axisConfig.y.max; y += step) {
            const z = func(x, y)
            data.push([x, y, z])
          }
        }
        
        return data
      }
      
      return this.data
    },
    
    /** 获取提示框格式化器 */
    getTooltipFormatter() {
      switch (this.chartType) {
        case 'scatter3D':
          return (params) => {
            const data = params.data
            return `X: ${data[0]}<br/>Y: ${data[1]}<br/>Z: ${data[2]}`
          }
          
        case 'bar3D':
          return (params) => {
            const data = params.data
            return `位置: (${data[0]}, ${data[1]})<br/>值: ${data[2]}`
          }
          
        case 'surface':
          return (params) => {
            const data = params.data
            return `X: ${data[0].toFixed(2)}<br/>Y: ${data[1].toFixed(2)}<br/>Z: ${data[2].toFixed(2)}`
          }
          
        default:
          return (params) => {
            return `数据: ${JSON.stringify(params.data)}`
          }
      }
    },
    
    /** 图表准备就绪 */
    onChartReady(chart) {
      this.chart = chart
      
      // 监听视角变化
      chart.on('grid3drotate', (params) => {
        this.viewAngle.alpha = Math.round(params.alpha)
        this.viewAngle.beta = Math.round(params.beta)
      })
      
      // 监听缩放变化
      chart.on('grid3dzoom', (params) => {
        this.zoomLevel = Math.round(params.zoom * 100)
      })
      
      this.$emit('chart-ready', chart)
    },
    
    /** 更新图表 */
    updateChart() {
      if (this.chart) {
        this.chart.setOption(this.chartOption, true)
      }
    },
    
    /** 重置视角 */
    resetView() {
      if (this.chart) {
        this.chart.setOption({
          grid3D: {
            viewControl: {
              alpha: this.config3D.alpha,
              beta: this.config3D.beta,
              distance: this.config3D.distance
            }
          }
        })
        
        this.viewAngle.alpha = this.config3D.alpha
        this.viewAngle.beta = this.config3D.beta
        this.zoomLevel = 100
      }
    },
    
    /** 切换自动旋转 */
    toggleAutoRotate() {
      this.autoRotate = !this.autoRotate
    },
    
    /** 更新自动旋转 */
    updateAutoRotate(enabled) {
      if (this.chart) {
        this.chart.setOption({
          grid3D: {
            viewControl: {
              autoRotate: enabled
            }
          }
        })
      }
    },
    
    /** 切换线框模式 */
    toggleWireframe() {
      this.wireframe = !this.wireframe
      this.updateChart()
    },
    
    /** 下载图片 */
    downloadImage() {
      if (this.$refs.chart) {
        const filename = `3d-chart-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}`
        this.$refs.chart.downloadChart(filename)
      }
    },
    
    /** 设置视角 */
    setViewAngle(alpha, beta) {
      if (this.chart) {
        this.chart.setOption({
          grid3D: {
            viewControl: {
              alpha: alpha,
              beta: beta
            }
          }
        })
      }
    },
    
    /** 设置缩放 */
    setZoom(distance) {
      if (this.chart) {
        this.chart.setOption({
          grid3D: {
            viewControl: {
              distance: distance
            }
          }
        })
      }
    },
    
    /** 添加数据点 */
    addDataPoint(point) {
      const newData = [...this.data, point]
      this.$emit('update:data', newData)
    },
    
    /** 清空数据 */
    clearData() {
      this.$emit('update:data', [])
    },
    
    /** 获取当前视角 */
    getCurrentView() {
      return {
        alpha: this.viewAngle.alpha,
        beta: this.viewAngle.beta,
        zoom: this.zoomLevel
      }
    }
  }
}
</script>

<style scoped>
.chart-3d {
  position: relative;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

.chart-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
  background: rgba(255, 255, 255, 0.9);
  padding: 5px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-info {
  position: absolute;
  bottom: 10px;
  left: 10px;
  z-index: 10;
  background: rgba(255, 255, 255, 0.9);
  padding: 8px 12px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-size: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  color: #606266;
  margin-right: 8px;
}

.value {
  color: #303133;
  font-weight: bold;
}
</style>
