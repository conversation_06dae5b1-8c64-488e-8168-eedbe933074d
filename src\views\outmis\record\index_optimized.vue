<template>
  <div class="app-container">
    <!-- 称重统计卡片 -->
    <el-row :gutter="20" class="stats-cards" style="margin-bottom: 20px;">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total">
              <i class="el-icon-scale-to-original"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.totalRecords || 0 }}</div>
              <div class="stats-label">称重记录</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon today">
              <i class="el-icon-date"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.todayRecords || 0 }}</div>
              <div class="stats-label">今日称重</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon weight">
              <i class="el-icon-heavy-rain"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.totalWeight || '0kg' }}</div>
              <div class="stats-label">总重量</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon accuracy">
              <i class="el-icon-success"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.accuracy || '0%' }}</div>
              <div class="stats-label">准确率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="设备编码" prop="deviceCode">
        <el-input
          v-model="queryParams.deviceCode"
          placeholder="请输入设备编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="物料编码" prop="materialCode">
        <el-input
          v-model="queryParams.materialCode"
          placeholder="请输入物料编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="重量范围">
        <el-input-number
          v-model="queryParams.minWeight"
          placeholder="最小重量"
          :min="0"
          :precision="2"
          style="width: 120px;"
        />
        <span style="margin: 0 10px;">-</span>
        <el-input-number
          v-model="queryParams.maxWeight"
          placeholder="最大重量"
          :min="0"
          :precision="2"
          style="width: 120px;"
        />
      </el-form-item>
      <el-form-item label="称重时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['outmis:record:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-pie-chart"
          size="mini"
          @click="handleStats"
          v-hasPermi="['outmis:record:stats']"
        >统计分析</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          @click="handleClean"
          v-hasPermi="['outmis:record:clean']"
        >清理数据</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 称重记录列表 -->
    <el-table v-loading="loading" :data="recordList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="设备编码" align="center" prop="deviceCode" />
      <el-table-column label="设备名称" align="center" prop="deviceName" />
      <el-table-column label="物料编码" align="center" prop="materialCode" />
      <el-table-column label="物料名称" align="center" prop="materialName" :show-overflow-tooltip="true" />
      <el-table-column label="称重值" align="center" prop="weight">
        <template slot-scope="scope">
          <span :class="getWeightClass(scope.row.weight, scope.row.standardWeight)">
            {{ scope.row.weight }}{{ scope.row.unit }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="标准重量" align="center" prop="standardWeight">
        <template slot-scope="scope">
          {{ scope.row.standardWeight }}{{ scope.row.unit }}
        </template>
      </el-table-column>
      <el-table-column label="偏差" align="center" prop="deviation">
        <template slot-scope="scope">
          <span :class="getDeviationClass(scope.row.deviation)">
            {{ scope.row.deviation > 0 ? '+' : '' }}{{ scope.row.deviation }}{{ scope.row.unit }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="稳定性" align="center" prop="stable">
        <template slot-scope="scope">
          <el-tag :type="scope.row.stable ? 'success' : 'warning'">
            {{ scope.row.stable ? '稳定' : '不稳定' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作员" align="center" prop="operator" />
      <el-table-column label="称重时间" align="center" prop="weighTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.weighTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['outmis:record:query']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="handleReweigh(scope.row)"
            v-if="scope.row.canReweigh"
          >重新称重</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 称重详情对话框 -->
    <el-dialog title="称重记录详情" :visible.sync="detailOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="记录ID">{{ detailData.recordId }}</el-descriptions-item>
        <el-descriptions-item label="设备编码">{{ detailData.deviceCode }}</el-descriptions-item>
        <el-descriptions-item label="设备名称">{{ detailData.deviceName }}</el-descriptions-item>
        <el-descriptions-item label="物料编码">{{ detailData.materialCode }}</el-descriptions-item>
        <el-descriptions-item label="物料名称">{{ detailData.materialName }}</el-descriptions-item>
        <el-descriptions-item label="称重值">{{ detailData.weight }}{{ detailData.unit }}</el-descriptions-item>
        <el-descriptions-item label="标准重量">{{ detailData.standardWeight }}{{ detailData.unit }}</el-descriptions-item>
        <el-descriptions-item label="偏差">{{ detailData.deviation }}{{ detailData.unit }}</el-descriptions-item>
        <el-descriptions-item label="稳定性">{{ detailData.stable ? '稳定' : '不稳定' }}</el-descriptions-item>
        <el-descriptions-item label="操作员">{{ detailData.operator }}</el-descriptions-item>
        <el-descriptions-item label="称重时间">{{ parseTime(detailData.weighTime) }}</el-descriptions-item>
        <el-descriptions-item label="备注">{{ detailData.remark || '无' }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 统计分析对话框 -->
    <el-dialog title="称重统计分析" :visible.sync="statsOpen" width="800px" append-to-body>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>重量分布</span>
            </div>
            <div class="text item">
              <p>平均重量：{{ statsData.avgWeight }}kg</p>
              <p>最大重量：{{ statsData.maxWeight }}kg</p>
              <p>最小重量：{{ statsData.minWeight }}kg</p>
              <p>标准差：{{ statsData.stdDev }}kg</p>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>准确性分析</span>
            </div>
            <div class="text item">
              <p>准确记录：{{ statsData.accurateCount }}条</p>
              <p>偏差记录：{{ statsData.deviationCount }}条</p>
              <p>准确率：{{ statsData.accuracyRate }}%</p>
              <p>平均偏差：{{ statsData.avgDeviation }}kg</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button @click="statsOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "OutmisRecord",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 记录表格数据
      recordList: [],
      // 日期范围
      dateRange: [],
      // 详情对话框
      detailOpen: false,
      // 详情数据
      detailData: {},
      // 统计对话框
      statsOpen: false,
      // 统计数据
      statsData: {},
      // 统计信息
      statistics: {
        totalRecords: 0,
        todayRecords: 0,
        totalWeight: '0kg',
        accuracy: '0%'
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceCode: null,
        materialCode: null,
        minWeight: null,
        maxWeight: null
      }
    };
  },
  created() {
    this.getList();
    this.getStatistics();
  },
  methods: {
    /** 查询记录列表 */
    getList() {
      this.loading = true;
      // 模拟数据
      this.recordList = [
        {
          recordId: 1,
          deviceCode: "WEIGHT_001",
          deviceName: "称重传感器01",
          materialCode: "MAT001",
          materialName: "钢材A型",
          weight: 125.6,
          standardWeight: 125.0,
          deviation: 0.6,
          unit: "kg",
          stable: true,
          operator: "张三",
          weighTime: new Date(),
          canReweigh: true,
          remark: "正常称重"
        },
        {
          recordId: 2,
          deviceCode: "WEIGHT_002",
          deviceName: "称重传感器02",
          materialCode: "MAT002",
          materialName: "铝材B型",
          weight: 98.2,
          standardWeight: 100.0,
          deviation: -1.8,
          unit: "kg",
          stable: false,
          operator: "李四",
          weighTime: new Date(Date.now() - 60000),
          canReweigh: false,
          remark: "重量偏差较大"
        }
      ];
      this.total = this.recordList.length;
      this.loading = false;
    },
    /** 获取统计数据 */
    getStatistics() {
      this.statistics = {
        totalRecords: 1250,
        todayRecords: 85,
        totalWeight: '12.5t',
        accuracy: '96.8%'
      };
    },
    /** 获取重量样式 */
    getWeightClass(weight, standardWeight) {
      const deviation = Math.abs(weight - standardWeight);
      if (deviation <= 0.5) return 'weight-normal';
      if (deviation <= 2.0) return 'weight-warning';
      return 'weight-danger';
    },
    /** 获取偏差样式 */
    getDeviationClass(deviation) {
      const abs = Math.abs(deviation);
      if (abs <= 0.5) return 'deviation-normal';
      if (abs <= 2.0) return 'deviation-warning';
      return 'deviation-danger';
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.recordId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$modal.confirm('是否确认导出所有称重记录数据项?').then(() => {
        console.log('导出称重记录');
        this.$modal.msgSuccess("导出成功");
      }).catch(() => {});
    },
    /** 统计分析按钮操作 */
    handleStats() {
      this.statsData = {
        avgWeight: 112.5,
        maxWeight: 250.0,
        minWeight: 15.2,
        stdDev: 25.8,
        accurateCount: 1210,
        deviationCount: 40,
        accuracyRate: 96.8,
        avgDeviation: 0.8
      };
      this.statsOpen = true;
    },
    /** 清理数据按钮操作 */
    handleClean() {
      this.$modal.confirm('是否确认清理过期的称重记录数据?').then(() => {
        console.log('清理称重记录');
        this.getList();
        this.$modal.msgSuccess("清理成功");
      }).catch(() => {});
    },
    /** 查看详情按钮操作 */
    handleView(row) {
      this.detailData = row;
      this.detailOpen = true;
    },
    /** 重新称重按钮操作 */
    handleReweigh(row) {
      this.$modal.confirm('是否确认对"' + row.materialName + '"进行重新称重?').then(function() {
        console.log('重新称重:', row.recordId);
      }).then(() => {
        this.$modal.msgSuccess("重新称重指令已发送");
      }).catch(() => {});
    }
  }
};
</script>

<style scoped>
.stats-cards {
  margin-bottom: 20px;
}
.stats-card {
  border-radius: 8px;
}
.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}
.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}
.stats-icon.total { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.stats-icon.today { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.stats-icon.weight { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.stats-icon.accuracy { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
.stats-info {
  flex: 1;
}
.stats-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}
.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.weight-normal { color: #67C23A; }
.weight-warning { color: #E6A23C; }
.weight-danger { color: #F56C6C; }

.deviation-normal { color: #67C23A; }
.deviation-warning { color: #E6A23C; }
.deviation-danger { color: #F56C6C; }

.text {
  font-size: 14px;
}
.item {
  margin-bottom: 18px;
}
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both
}
.box-card {
  width: 100%;
}
</style>
