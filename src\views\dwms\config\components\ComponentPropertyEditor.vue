<template>
  <div class="property-editor">
    <div class="editor-header">
      <h4>属性编辑器</h4>
      <el-button-group v-if="selectedComponent">
        <el-button size="mini" @click="resetProperties">
          <i class="el-icon-refresh-left"></i>
          重置
        </el-button>
        <el-button size="mini" @click="copyProperties">
          <i class="el-icon-document-copy"></i>
          复制
        </el-button>
      </el-button-group>
    </div>
    
    <div class="editor-content">
      <div v-if="!selectedComponent" class="empty-editor">
        <i class="el-icon-edit"></i>
        <p>请选择一个组件进行编辑</p>
      </div>
      
      <el-form 
        v-else 
        :model="componentProperties" 
        label-width="80px" 
        size="mini"
        @submit.native.prevent
      >
        <!-- 基础属性 -->
        <div class="property-group">
          <div class="group-title">基础属性</div>
          
          <el-form-item label="组件ID">
            <el-input 
              v-model="componentProperties.id" 
              placeholder="组件唯一标识"
              @change="updateProperty('id', $event)"
            />
          </el-form-item>
          
          <el-form-item label="组件名称">
            <el-input 
              v-model="componentProperties.name" 
              placeholder="组件显示名称"
              @change="updateProperty('name', $event)"
            />
          </el-form-item>
          
          <el-form-item label="组件类型">
            <el-select 
              v-model="componentProperties.type" 
              placeholder="选择组件类型"
              @change="updateProperty('type', $event)"
            >
              <el-option 
                v-for="type in componentTypes" 
                :key="type.value" 
                :label="type.label" 
                :value="type.value"
              />
            </el-select>
          </el-form-item>
        </div>
        
        <!-- 位置和尺寸 -->
        <div class="property-group">
          <div class="group-title">位置和尺寸</div>
          
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="X坐标">
                <el-input-number 
                  v-model="componentProperties.x" 
                  :min="0"
                  controls-position="right"
                  @change="updateProperty('x', $event)"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="Y坐标">
                <el-input-number 
                  v-model="componentProperties.y" 
                  :min="0"
                  controls-position="right"
                  @change="updateProperty('y', $event)"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="宽度">
                <el-input-number 
                  v-model="componentProperties.width" 
                  :min="1"
                  controls-position="right"
                  @change="updateProperty('width', $event)"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="高度">
                <el-input-number 
                  v-model="componentProperties.height" 
                  :min="1"
                  controls-position="right"
                  @change="updateProperty('height', $event)"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        
        <!-- 样式属性 -->
        <div class="property-group">
          <div class="group-title">样式属性</div>
          
          <el-form-item label="背景色">
            <el-color-picker 
              v-model="componentProperties.backgroundColor"
              show-alpha
              @change="updateProperty('backgroundColor', $event)"
            />
          </el-form-item>
          
          <el-form-item label="边框色">
            <el-color-picker 
              v-model="componentProperties.borderColor"
              @change="updateProperty('borderColor', $event)"
            />
          </el-form-item>
          
          <el-form-item label="边框宽度">
            <el-input-number 
              v-model="componentProperties.borderWidth" 
              :min="0"
              :max="10"
              controls-position="right"
              @change="updateProperty('borderWidth', $event)"
            />
          </el-form-item>
          
          <el-form-item label="圆角">
            <el-input-number 
              v-model="componentProperties.borderRadius" 
              :min="0"
              :max="50"
              controls-position="right"
              @change="updateProperty('borderRadius', $event)"
            />
          </el-form-item>
          
          <el-form-item label="透明度">
            <el-slider 
              v-model="componentProperties.opacity"
              :min="0"
              :max="1"
              :step="0.1"
              @change="updateProperty('opacity', $event)"
            />
          </el-form-item>
        </div>
        
        <!-- 动态属性 -->
        <div class="property-group" v-if="dynamicProperties.length > 0">
          <div class="group-title">组件属性</div>
          
          <el-form-item 
            v-for="prop in dynamicProperties" 
            :key="prop.key"
            :label="prop.label"
          >
            <!-- 文本输入 -->
            <el-input 
              v-if="prop.type === 'text'"
              v-model="componentProperties[prop.key]"
              :placeholder="prop.placeholder"
              @change="updateProperty(prop.key, $event)"
            />
            
            <!-- 数字输入 -->
            <el-input-number 
              v-else-if="prop.type === 'number'"
              v-model="componentProperties[prop.key]"
              :min="prop.min"
              :max="prop.max"
              :step="prop.step"
              controls-position="right"
              @change="updateProperty(prop.key, $event)"
            />
            
            <!-- 选择器 -->
            <el-select 
              v-else-if="prop.type === 'select'"
              v-model="componentProperties[prop.key]"
              :placeholder="prop.placeholder"
              @change="updateProperty(prop.key, $event)"
            >
              <el-option 
                v-for="option in prop.options" 
                :key="option.value" 
                :label="option.label" 
                :value="option.value"
              />
            </el-select>
            
            <!-- 开关 -->
            <el-switch 
              v-else-if="prop.type === 'boolean'"
              v-model="componentProperties[prop.key]"
              @change="updateProperty(prop.key, $event)"
            />
            
            <!-- 颜色选择器 -->
            <el-color-picker 
              v-else-if="prop.type === 'color'"
              v-model="componentProperties[prop.key]"
              @change="updateProperty(prop.key, $event)"
            />
          </el-form-item>
        </div>
        
        <!-- 数据绑定 -->
        <div class="property-group">
          <div class="group-title">数据绑定</div>
          
          <el-form-item label="数据源">
            <el-select 
              v-model="componentProperties.dataSource"
              placeholder="选择数据源"
              @change="updateProperty('dataSource', $event)"
            >
              <el-option 
                v-for="source in dataSources" 
                :key="source.value" 
                :label="source.label" 
                :value="source.value"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="更新频率">
            <el-input-number 
              v-model="componentProperties.updateInterval" 
              :min="100"
              :step="100"
              controls-position="right"
              @change="updateProperty('updateInterval', $event)"
            />
            <span class="unit-text">毫秒</span>
          </el-form-item>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ComponentPropertyEditor',
  props: {
    selectedComponent: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      componentProperties: {},
      componentTypes: [
        { label: '文本', value: 'text' },
        { label: '按钮', value: 'button' },
        { label: '图表', value: 'chart' },
        { label: '仪表盘', value: 'gauge' },
        { label: '图片', value: 'image' },
        { label: '视频', value: 'video' },
        { label: '容器', value: 'container' }
      ],
      dataSources: [
        { label: '重量传感器', value: 'weight_sensor' },
        { label: '温度传感器', value: 'temperature_sensor' },
        { label: '湿度传感器', value: 'humidity_sensor' },
        { label: '门禁状态', value: 'access_status' },
        { label: '设备状态', value: 'device_status' }
      ]
    }
  },
  computed: {
    dynamicProperties() {
      if (!this.selectedComponent || !this.selectedComponent.type) {
        return []
      }
      
      // 根据组件类型返回不同的属性配置
      const typeProperties = {
        text: [
          { key: 'text', label: '文本内容', type: 'text', placeholder: '请输入文本' },
          { key: 'fontSize', label: '字体大小', type: 'number', min: 12, max: 72, step: 1 },
          { key: 'fontColor', label: '字体颜色', type: 'color' },
          { key: 'textAlign', label: '对齐方式', type: 'select', options: [
            { label: '左对齐', value: 'left' },
            { label: '居中', value: 'center' },
            { label: '右对齐', value: 'right' }
          ]}
        ],
        button: [
          { key: 'text', label: '按钮文本', type: 'text', placeholder: '请输入按钮文本' },
          { key: 'buttonType', label: '按钮类型', type: 'select', options: [
            { label: '默认', value: 'default' },
            { label: '主要', value: 'primary' },
            { label: '成功', value: 'success' },
            { label: '警告', value: 'warning' },
            { label: '危险', value: 'danger' }
          ]},
          { key: 'disabled', label: '禁用状态', type: 'boolean' }
        ],
        chart: [
          { key: 'chartType', label: '图表类型', type: 'select', options: [
            { label: '折线图', value: 'line' },
            { label: '柱状图', value: 'bar' },
            { label: '饼图', value: 'pie' },
            { label: '散点图', value: 'scatter' }
          ]},
          { key: 'showLegend', label: '显示图例', type: 'boolean' },
          { key: 'showGrid', label: '显示网格', type: 'boolean' }
        ]
      }
      
      return typeProperties[this.selectedComponent.type] || []
    }
  },
  watch: {
    selectedComponent: {
      handler(newComponent) {
        if (newComponent) {
          this.componentProperties = { ...newComponent }
        } else {
          this.componentProperties = {}
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    updateProperty(key, value) {
      this.$set(this.componentProperties, key, value)
      this.$emit('property-change', {
        component: this.selectedComponent,
        property: key,
        value: value,
        properties: this.componentProperties
      })
    },
    
    resetProperties() {
      if (this.selectedComponent) {
        this.componentProperties = { ...this.selectedComponent }
        this.$emit('properties-reset', this.selectedComponent)
      }
    },
    
    copyProperties() {
      if (this.componentProperties) {
        const propertiesText = JSON.stringify(this.componentProperties, null, 2)
        navigator.clipboard.writeText(propertiesText).then(() => {
          this.$message.success('属性已复制到剪贴板')
        }).catch(() => {
          this.$message.error('复制失败')
        })
      }
    }
  }
}
</script>

<style scoped>
.property-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fff;
}

.editor-header {
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f5f7fa;
}

.editor-header h4 {
  margin: 0;
  font-size: 14px;
  color: #303133;
}

.editor-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.empty-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;
}

.empty-editor i {
  font-size: 48px;
  margin-bottom: 16px;
}

.property-group {
  margin-bottom: 24px;
}

.group-title {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.unit-text {
  margin-left: 8px;
  color: #909399;
  font-size: 12px;
}

/* 表单项样式调整 */
.el-form-item {
  margin-bottom: 16px;
}

.el-form-item__label {
  font-size: 12px;
  color: #606266;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .editor-header {
    flex-direction: column;
    gap: 8px;
  }
  
  .editor-content {
    padding: 12px;
  }
}
</style>
