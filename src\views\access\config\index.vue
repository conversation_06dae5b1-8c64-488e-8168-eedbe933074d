<template>
  <div class="app-container">
    <!-- 设备选择 -->
    <el-card class="device-selector" shadow="never">
      <div slot="header" class="clearfix">
        <span>设备选择</span>
      </div>
      <el-form :inline="true" size="small">
        <el-form-item label="选择设备">
          <el-select v-model="selectedDeviceId" placeholder="请选择门禁设备" @change="handleDeviceChange" style="width: 300px;">
            <el-option
              v-for="device in deviceList"
              :key="device.id"
              :label="`${device.deviceName} (${device.deviceCode})`"
              :value="device.id">
              <span style="float: left">{{ device.deviceName }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ device.deviceCode }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-refresh" @click="loadDeviceList">刷新设备</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 设备信息显示 -->
    <el-card v-if="selectedDeviceId && selectedDeviceInfo" class="device-info" shadow="never">
      <div slot="header" class="clearfix">
        <span>设备基础信息</span>
      </div>
      <el-descriptions :column="4" border size="small">
        <el-descriptions-item label="设备编号">{{ selectedDeviceInfo.deviceCode }}</el-descriptions-item>
        <el-descriptions-item label="设备名称">{{ selectedDeviceInfo.deviceName }}</el-descriptions-item>
        <el-descriptions-item label="设备类型">{{ getDeviceTypeLabel(selectedDeviceInfo.deviceType) }}</el-descriptions-item>
        <el-descriptions-item label="安装位置">{{ selectedDeviceInfo.installLocation || '未设置' }}</el-descriptions-item>
        <el-descriptions-item label="IP地址">{{ selectedDeviceInfo.ipAddress || '未设置' }}</el-descriptions-item>
        <el-descriptions-item label="端口">{{ selectedDeviceInfo.port || '未设置' }}</el-descriptions-item>
        <el-descriptions-item label="厂商">{{ selectedDeviceInfo.manufacturer || '未设置' }}</el-descriptions-item>
        <el-descriptions-item label="设备型号">{{ selectedDeviceInfo.deviceModel || '未设置' }}</el-descriptions-item>
        <el-descriptions-item label="在线状态">
          <el-tag :type="selectedDeviceInfo.onlineStatus === 1 ? 'success' : 'danger'">
            {{ selectedDeviceInfo.onlineStatus === 1 ? '在线' : '离线' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="最后心跳">{{ parseTime(selectedDeviceInfo.lastHeartbeat) || '无' }}</el-descriptions-item>
        <el-descriptions-item label="MQTT客户端ID">{{ selectedDeviceInfo.mqttClientId || '未设置' }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="selectedDeviceInfo.status === 1 ? 'success' : 'info'">
            {{ selectedDeviceInfo.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 配置管理 -->
    <el-card v-if="selectedDeviceId" class="config-management" shadow="never">
      <div slot="header" class="clearfix">
        <span>门禁设备参数配置</span>
        <div style="float: right;">
          <el-button size="mini" type="success" icon="el-icon-check" @click="saveAllConfig">保存所有配置</el-button>
          <el-button size="mini" type="warning" icon="el-icon-refresh" @click="resetToDefault">重置为默认值</el-button>
          <el-button size="mini" type="info" icon="el-icon-download" @click="exportConfig">导出配置</el-button>
          <el-button size="mini" type="primary" icon="el-icon-upload2" @click="showImportDialog">导入配置</el-button>
          <el-divider direction="vertical"></el-divider>
          <el-button size="mini" type="primary" icon="el-icon-connection" @click="testEmqxConnection">测试EMQX连接</el-button>
          <el-button size="mini" type="success" icon="el-icon-link" @click="registerToEmqx">注册到EMQX</el-button>
          <el-button size="mini" type="danger" icon="el-icon-close" @click="unregisterFromEmqx">注销EMQX</el-button>
        </div>
      </div>

      <!-- 配置分组标签页 -->
      <el-tabs v-model="activeTab" type="card">
        <el-tab-pane 
          v-for="(configs, groupName) in configGroups" 
          :key="groupName" 
          :label="getGroupLabel(groupName)" 
          :name="groupName">
          
          <el-form :model="configData" label-width="150px" size="small">
            <el-row :gutter="20">
              <el-col :span="12" v-for="config in configs" :key="config.configKey">
                <el-form-item :label="config.configDesc" :required="config.isRequired === '1'">
                  <!-- 字符串类型 -->
                  <el-input 
                    v-if="config.configType === 'string'"
                    v-model="configData[config.configKey]"
                    :placeholder="config.defaultValue"
                    @change="validateConfig(config)">
                  </el-input>
                  
                  <!-- 数字类型 -->
                  <el-input-number 
                    v-else-if="config.configType === 'number'"
                    v-model="configData[config.configKey]"
                    :placeholder="config.defaultValue"
                    :precision="getNumberPrecision(config.configKey)"
                    :min="getNumberMin(config.validationRule)"
                    :max="getNumberMax(config.validationRule)"
                    @change="validateConfig(config)"
                    style="width: 100%;">
                  </el-input-number>
                  
                  <!-- 布尔类型 -->
                  <el-switch 
                    v-else-if="config.configType === 'boolean'"
                    v-model="configData[config.configKey]"
                    active-text="是"
                    inactive-text="否"
                    active-value="true"
                    inactive-value="false"
                    @change="validateConfig(config)">
                  </el-switch>
                  
                  <!-- JSON类型 -->
                  <el-input 
                    v-else-if="config.configType === 'json'"
                    v-model="configData[config.configKey]"
                    type="textarea"
                    :rows="3"
                    :placeholder="config.defaultValue"
                    @change="validateConfig(config)">
                  </el-input>
                  
                  <!-- 默认字符串 -->
                  <el-input 
                    v-else
                    v-model="configData[config.configKey]"
                    :placeholder="config.defaultValue"
                    @change="validateConfig(config)">
                  </el-input>
                  
                  <div class="config-help" v-if="config.validationRule">
                    <small class="text-muted">{{ formatValidationRule(config.validationRule) }}</small>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 导入配置对话框 -->
    <el-dialog title="导入配置" :visible.sync="importDialogVisible" width="600px">
      <el-form label-width="100px">
        <el-form-item label="配置数据">
          <el-input
            v-model="importConfigData"
            type="textarea"
            :rows="10"
            placeholder="请输入JSON格式的配置数据">
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="importDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleImportConfig">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 空状态 -->
    <el-empty v-if="!selectedDeviceId" description="请先选择一个门禁设备来管理其参数配置">
      <el-button type="primary" @click="loadDeviceList">加载设备列表</el-button>
    </el-empty>
  </div>
</template>

<script>
import { listDevice, getDevice } from "@/api/access/device";
import { 
  getDeviceConfigByGroup,
  batchSaveConfig,
  resetConfigToDefault,
  importConfig,
  exportConfig,
  validateConfig
} from "@/api/access/config";

export default {
  name: "AccessDeviceConfig",
  data() {
    return {
      // 设备列表
      deviceList: [],
      // 选中的设备ID
      selectedDeviceId: null,
      // 选中的设备信息
      selectedDeviceInfo: null,
      // 配置分组数据
      configGroups: {},
      // 配置数据
      configData: {},
      // 当前活动标签页
      activeTab: 'general',
      // 导入对话框
      importDialogVisible: false,
      // 导入配置数据
      importConfigData: '',
      // 分组标签映射
      groupLabels: {
        'general': '通用配置',
        'mqtt': 'MQTT配置',
        'access': '门禁配置',
        'security': '安全配置',
        'face': '人脸识别'
      }
    };
  },
  created() {
    this.loadDeviceList();
  },
  methods: {
    /** 加载设备列表 */
    loadDeviceList() {
      listDevice({ status: '1' }).then(response => {
        this.deviceList = response.rows || [];
        console.log('加载设备列表成功:', this.deviceList);
      }).catch(error => {
        console.error('加载设备列表失败:', error);
        this.deviceList = [];
        this.$modal.msgError("加载设备列表失败");
      });
    },
    
    /** 设备变化处理 */
    handleDeviceChange(deviceId) {
      if (deviceId) {
        this.loadDeviceInfo(deviceId);
        this.loadDeviceConfig(deviceId);
      } else {
        this.selectedDeviceInfo = null;
        this.configGroups = {};
        this.configData = {};
      }
    },

    /** 加载设备详细信息 */
    loadDeviceInfo(deviceId) {
      // 从设备列表中找到选中的设备
      this.selectedDeviceInfo = this.deviceList.find(device => device.id === deviceId);
      if (!this.selectedDeviceInfo) {
        // 如果在列表中没找到，调用API获取
        getDevice(deviceId).then(response => {
          this.selectedDeviceInfo = response.data;
        }).catch(error => {
          console.error('获取设备信息失败:', error);
          this.$modal.msgError("获取设备信息失败");
        });
      }
    },
    
    /** 加载设备配置 */
    loadDeviceConfig(deviceId) {
      getDeviceConfigByGroup(deviceId).then(response => {
        this.configGroups = response.data;
        this.buildConfigData();
        
        // 设置默认活动标签页
        const groups = Object.keys(this.configGroups);
        if (groups.length > 0) {
          this.activeTab = groups[0];
        }
      });
    },
    
    /** 构建配置数据对象 */
    buildConfigData() {
      this.configData = {};
      Object.values(this.configGroups).forEach(configs => {
        configs.forEach(config => {
          this.configData[config.configKey] = config.configValue || config.defaultValue;
        });
      });
    },
    
    /** 获取分组标签 */
    getGroupLabel(groupName) {
      return this.groupLabels[groupName] || groupName;
    },

    /** 获取设备类型标签 */
    getDeviceTypeLabel(deviceType) {
      const typeLabels = {
        'face_access': '人脸门禁',
        'ip_lock': 'IP磁力锁',
        'card_reader': '刷卡器',
        'access_controller': '门禁控制器'
      };
      return typeLabels[deviceType] || deviceType;
    },
    
    /** 获取数字精度 */
    getNumberPrecision(configKey) {
      if (configKey.includes('threshold') || configKey.includes('recognition')) {
        return 2;
      }
      return 0;
    },
    
    /** 从验证规则获取最小值 */
    getNumberMin(validationRule) {
      if (!validationRule) return undefined;
      const match = validationRule.match(/min:(\d+(?:\.\d+)?)/);
      return match ? parseFloat(match[1]) : undefined;
    },
    
    /** 从验证规则获取最大值 */
    getNumberMax(validationRule) {
      if (!validationRule) return undefined;
      const match = validationRule.match(/max:(\d+(?:\.\d+)?)/);
      return match ? parseFloat(match[1]) : undefined;
    },
    
    /** 格式化验证规则显示 */
    formatValidationRule(validationRule) {
      if (!validationRule) return '';
      
      let rules = [];
      
      const minMatch = validationRule.match(/min:(\d+(?:\.\d+)?)/);
      if (minMatch) {
        rules.push(`最小值: ${minMatch[1]}`);
      }
      
      const maxMatch = validationRule.match(/max:(\d+(?:\.\d+)?)/);
      if (maxMatch) {
        rules.push(`最大值: ${maxMatch[1]}`);
      }
      
      const inMatch = validationRule.match(/in:([^,]+(?:,[^,]+)*)/);
      if (inMatch) {
        rules.push(`可选值: ${inMatch[1]}`);
      }
      
      return rules.join(', ');
    },
    
    /** 验证配置 */
    validateConfig(config) {
      const configCopy = { ...config };
      configCopy.configValue = this.configData[config.configKey];
      
      validateConfig(configCopy).then(response => {
        // 验证通过
      }).catch(error => {
        this.$message.error(`配置项 ${config.configDesc} 验证失败: ${error.msg}`);
      });
    },
    
    /** 保存所有配置 */
    saveAllConfig() {
      if (!this.selectedDeviceId) {
        this.$message.warning('请先选择设备');
        return;
      }
      
      // 构建配置列表
      const configList = [];
      Object.values(this.configGroups).forEach(configs => {
        configs.forEach(config => {
          configList.push({
            ...config,
            configValue: this.configData[config.configKey]
          });
        });
      });
      
      batchSaveConfig(this.selectedDeviceId, configList).then(response => {
        this.$message.success(response.msg);
        this.loadDeviceConfig(this.selectedDeviceId);
      });
    },
    
    /** 重置为默认值 */
    resetToDefault() {
      if (!this.selectedDeviceId) {
        this.$message.warning('请先选择设备');
        return;
      }
      
      this.$confirm('确认将所有配置重置为默认值？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        resetConfigToDefault(this.selectedDeviceId).then(response => {
          this.$message.success(response.msg);
          this.loadDeviceConfig(this.selectedDeviceId);
        });
      });
    },
    
    /** 导出配置 */
    exportConfig() {
      if (!this.selectedDeviceId) {
        this.$message.warning('请先选择设备');
        return;
      }
      
      exportConfig(this.selectedDeviceId).then(response => {
        const configData = JSON.stringify(response.data, null, 2);
        
        // 创建下载链接
        const blob = new Blob([configData], { type: 'application/json' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `access_device_config_${this.selectedDeviceId}.json`;
        link.click();
        window.URL.revokeObjectURL(url);
        
        this.$message.success('配置导出成功');
      });
    },
    
    /** 显示导入对话框 */
    showImportDialog() {
      if (!this.selectedDeviceId) {
        this.$message.warning('请先选择设备');
        return;
      }
      this.importConfigData = '';
      this.importDialogVisible = true;
    },
    
    /** 处理导入配置 */
    handleImportConfig() {
      if (!this.importConfigData.trim()) {
        this.$message.warning('请输入配置数据');
        return;
      }

      try {
        const configData = JSON.parse(this.importConfigData);

        importConfig(this.selectedDeviceId, configData).then(response => {
          this.$message.success(response.msg);
          this.importDialogVisible = false;
          this.loadDeviceConfig(this.selectedDeviceId);
        });
      } catch (error) {
        this.$message.error('配置数据格式错误，请检查JSON格式');
      }
    },

    /** 测试EMQX连接 */
    testEmqxConnection() {
      if (!this.selectedDeviceId || !this.selectedDeviceInfo) {
        this.$modal.msgWarning("请先选择设备");
        return;
      }

      this.$modal.loading("正在测试EMQX连接...");

      this.$http.post(`/access/emqx/integration/test/${this.selectedDeviceInfo.deviceCode}`)
        .then(response => {
          this.$modal.closeLoading();
          if (response.data && response.data.success) {
            this.$modal.msgSuccess("EMQX连接测试成功");
            this.$modal.alert(`
              <p><strong>连接信息：</strong></p>
              <p>设备编号：${response.data.deviceCode}</p>
              <p>客户端ID：${response.data.clientId}</p>
              <p>服务器地址：${response.data.brokerHost}:${response.data.brokerPort}</p>
              <p>连接状态：${response.data.success ? '成功' : '失败'}</p>
            `, "连接测试结果", {
              dangerouslyUseHTMLString: true
            });
          } else {
            this.$modal.msgError("EMQX连接测试失败：" + (response.data ? response.data.message : "未知错误"));
          }
        })
        .catch(error => {
          this.$modal.closeLoading();
          console.error('EMQX连接测试失败:', error);
          this.$modal.msgError("EMQX连接测试失败");
        });
    },

    /** 注册设备到EMQX */
    registerToEmqx() {
      if (!this.selectedDeviceId || !this.selectedDeviceInfo) {
        this.$modal.msgWarning("请先选择设备");
        return;
      }

      this.$modal.confirm(`确认将设备 ${this.selectedDeviceInfo.deviceName} 注册到EMQX服务器？`)
        .then(() => {
          this.$modal.loading("正在注册设备到EMQX...");

          return this.$http.post(`/access/emqx/integration/register/${this.selectedDeviceInfo.deviceCode}`);
        })
        .then(response => {
          this.$modal.closeLoading();
          this.$modal.msgSuccess("设备注册到EMQX成功");

          // 刷新设备信息
          this.loadDeviceInfo(this.selectedDeviceId);
        })
        .catch(error => {
          this.$modal.closeLoading();
          if (error !== 'cancel') {
            console.error('注册设备到EMQX失败:', error);
            this.$modal.msgError("注册设备到EMQX失败");
          }
        });
    },

    /** 从EMQX注销设备 */
    unregisterFromEmqx() {
      if (!this.selectedDeviceId || !this.selectedDeviceInfo) {
        this.$modal.msgWarning("请先选择设备");
        return;
      }

      this.$modal.confirm(`确认将设备 ${this.selectedDeviceInfo.deviceName} 从EMQX服务器注销？`, "警告", {
        type: 'warning'
      })
        .then(() => {
          this.$modal.loading("正在从EMQX注销设备...");

          return this.$http.post(`/access/emqx/integration/unregister/${this.selectedDeviceInfo.deviceCode}`);
        })
        .then(response => {
          this.$modal.closeLoading();
          this.$modal.msgSuccess("设备从EMQX注销成功");

          // 刷新设备信息
          this.loadDeviceInfo(this.selectedDeviceId);
        })
        .catch(error => {
          this.$modal.closeLoading();
          if (error !== 'cancel') {
            console.error('从EMQX注销设备失败:', error);
            this.$modal.msgError("从EMQX注销设备失败");
          }
        });
    }
  }
};
</script>

<style scoped>
.device-selector {
  margin-bottom: 20px;
}

.config-management {
  min-height: 500px;
}

.config-help {
  margin-top: 5px;
}

.text-muted {
  color: #909399;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-tabs__content {
  padding: 20px 0;
}

.el-empty {
  padding: 60px 0;
}
</style>
