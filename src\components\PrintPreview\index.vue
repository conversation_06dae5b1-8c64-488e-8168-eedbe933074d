<template>
  <div class="print-preview-container">
    <!-- 工具栏 -->
    <div class="print-toolbar">
      <el-row :gutter="10">
        <el-col :span="6">
          <el-select v-model="selectedTemplateId" placeholder="选择打印模板" @change="handleTemplateChange">
            <el-option
              v-for="template in templateList"
              :key="template.id"
              :label="template.templateName"
              :value="template.id">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button-group>
            <el-button size="small" icon="el-icon-zoom-in" @click="zoomIn">放大</el-button>
            <el-button size="small" icon="el-icon-zoom-out" @click="zoomOut">缩小</el-button>
            <el-button size="small" icon="el-icon-refresh" @click="resetZoom">重置</el-button>
          </el-button-group>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" size="small" icon="el-icon-printer" @click="handlePrint">打印</el-button>
          <el-button size="small" icon="el-icon-view" @click="handlePreview">预览</el-button>
        </el-col>
        <el-col :span="6">
          <el-button size="small" icon="el-icon-close" @click="handleClose">关闭</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 预览区域 -->
    <div class="print-preview-area" v-loading="loading">
      <div class="print-page" :style="pageStyle">
        <div v-html="printContent" class="print-content"></div>
      </div>
    </div>

    <!-- 打印设置对话框 -->
    <el-dialog title="打印设置" :visible.sync="printSettingsVisible" width="500px">
      <el-form :model="printSettings" label-width="100px">
        <el-form-item label="打印机">
          <el-select v-model="printSettings.printer" placeholder="选择打印机">
            <el-option label="默认打印机" value="default"></el-option>
            <!-- 这里可以动态加载可用打印机列表 -->
          </el-select>
        </el-form-item>
        <el-form-item label="打印份数">
          <el-input-number v-model="printSettings.copies" :min="1" :max="99"></el-input-number>
        </el-form-item>
        <el-form-item label="纸张大小">
          <el-select v-model="printSettings.paperSize">
            <el-option label="A4" value="A4"></el-option>
            <el-option label="A5" value="A5"></el-option>
            <el-option label="Letter" value="Letter"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="打印方向">
          <el-radio-group v-model="printSettings.orientation">
            <el-radio label="portrait">纵向</el-radio>
            <el-radio label="landscape">横向</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="printSettingsVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmPrint">确认打印</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getInoutPrintPreview, printInoutDocument, generateInoutPrintContent, getInoutPrintData } from "@/api/inout/print";
import { listPrintTemplate } from "@/api/inout/printTemplate";

export default {
  name: "PrintPreview",
  props: {
    // 业务单据ID
    businessId: {
      type: Number,
      required: true
    },
    // 业务类型
    businessType: {
      type: String,
      default: 'inout'
    },
    // 是否显示
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      // 模板列表
      templateList: [],
      // 选中的模板ID
      selectedTemplateId: null,
      // 打印内容
      printContent: '',
      // 缩放比例
      zoomLevel: 1,
      // 打印设置对话框
      printSettingsVisible: false,
      // 打印设置
      printSettings: {
        printer: 'default',
        copies: 1,
        paperSize: 'A4',
        orientation: 'portrait'
      }
    };
  },
  computed: {
    pageStyle() {
      return {
        transform: `scale(${this.zoomLevel})`,
        transformOrigin: 'top left',
        width: '210mm', // A4纸张宽度
        minHeight: '297mm', // A4纸张高度
        margin: '0 auto',
        backgroundColor: 'white',
        boxShadow: '0 0 10px rgba(0,0,0,0.1)',
        padding: '20mm'
      };
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.loadTemplates().then(() => {
          this.loadPreview();
        });
      }
    },
    businessId() {
      if (this.visible) {
        this.loadPreview();
      }
    }
  },
  methods: {
    // 加载模板列表
    async loadTemplates() {
      try {
        console.log('开始加载模板列表:', { businessType: this.businessType });
        const response = await listPrintTemplate({
          templateType: this.businessType,
          status: '0'  // 修复：状态0表示启用，1表示停用
        });
        console.log('模板列表响应:', response);

        this.templateList = response.rows || response.data || [];
        console.log('解析后的模板列表:', this.templateList);

        if (this.templateList.length === 0) {
          console.warn('没有找到可用的打印模板');
          this.$message.warning('没有找到可用的打印模板，将使用系统默认模板');
          // 不设置默认模板ID，让后端使用默认模板
          this.selectedTemplateId = null;
          return;
        }

        // 选择默认模板
        const defaultTemplate = this.templateList.find(t => t.isDefault === '1' || t.isDefault === true);
        if (defaultTemplate) {
          this.selectedTemplateId = defaultTemplate.id;
          console.log('选择默认模板:', defaultTemplate);
        } else if (this.templateList.length > 0) {
          this.selectedTemplateId = this.templateList[0].id;
          console.log('选择第一个模板:', this.templateList[0]);
        }

        console.log('最终选择的模板ID:', this.selectedTemplateId);
      } catch (error) {
        console.error('加载模板列表失败:', error);
        this.$message.warning('加载模板列表失败，将使用默认模板');
        // 创建一个默认模板选项
        this.templateList = [{
          id: 'default',
          templateName: '默认模板',
          isDefault: true
        }];
        this.selectedTemplateId = 'default';
      }
    },

    // 加载预览内容
    async loadPreview() {
      if (!this.businessId) {
        console.warn('businessId 为空，无法加载预览');
        return;
      }

      this.loading = true;
      try {
        console.log('加载预览内容:', { businessId: this.businessId, templateId: this.selectedTemplateId });

        // 直接调用后端API，不管是否有模板ID（后端会使用默认模板）
        let response;
        try {
          // 优先使用预览API
          response = await getInoutPrintPreview(this.businessId, this.selectedTemplateId);
          console.log('预览API响应:', response);
        } catch (previewError) {
          console.warn('预览API调用失败，尝试使用生成内容API:', previewError);
          try {
            // 如果预览API不可用，尝试使用生成内容API
            response = await generateInoutPrintContent(this.businessId, this.selectedTemplateId);
            console.log('生成内容API响应:', response);
          } catch (contentError) {
            console.error('所有打印API都不可用:', contentError);
            this.$message.error('打印服务不可用：' + (contentError.message || '网络错误'));
            this.printContent = this.generateErrorContent('打印服务不可用，请检查网络连接或联系管理员');
            return;
          }
        }

        if (response && response.code === 200) {
          // 处理不同的响应格式
          let htmlContent = '';
          if (typeof response.data === 'string') {
            htmlContent = response.data;
          } else if (response.data && response.data.htmlContent) {
            htmlContent = response.data.htmlContent;
          } else if (response.data) {
            htmlContent = JSON.stringify(response.data);
          }

          if (htmlContent && htmlContent.trim()) {
            this.printContent = htmlContent;
          } else {
            console.warn('API返回的内容为空，使用模拟内容');
            this.printContent = this.generateMockPrintContent();
          }
        } else {
          console.error('预览内容响应错误:', response);
          const errorMsg = response && response.msg ? response.msg : '未知错误';
          this.printContent = `<div style="text-align: center; padding: 50px; color: #f56c6c;">
            <h3>加载失败</h3>
            <p>${errorMsg}</p>
          </div>`;
        }
      } catch (error) {
        console.error('加载预览内容失败:', error);
        this.$message.warning('加载预览内容失败，显示模拟内容: ' + (error.message || '网络错误'));
        this.printContent = this.generateMockPrintContent();
      } finally {
        this.loading = false;
      }
    },

    // 模板变化处理
    handleTemplateChange() {
      this.loadPreview();
    },

    // 放大
    zoomIn() {
      this.zoomLevel = Math.min(this.zoomLevel + 0.1, 2);
    },

    // 缩小
    zoomOut() {
      this.zoomLevel = Math.max(this.zoomLevel - 0.1, 0.5);
    },

    // 重置缩放
    resetZoom() {
      this.zoomLevel = 1;
    },

    // 预览
    handlePreview() {
      // 打开新窗口预览
      const previewWindow = window.open('', '_blank');
      previewWindow.document.write(this.printContent);
      previewWindow.document.close();
    },

    // 打印
    handlePrint() {
      this.printSettingsVisible = true;
    },

    // 确认打印
    async confirmPrint() {
      try {
        this.loading = true;
        await printInoutDocument(this.businessId, this.selectedTemplateId);
        
        // 调用浏览器打印
        const printWindow = window.open('', '_blank');
        printWindow.document.write(this.printContent);
        printWindow.document.close();
        printWindow.print();
        printWindow.close();
        
        this.printSettingsVisible = false;
        this.$message.success('打印成功');
      } catch (error) {
        console.error('打印失败:', error);
        this.$message.error('打印失败：' + error.message);
      } finally {
        this.loading = false;
      }
    },

    // 关闭
    handleClose() {
      this.$emit('close');
    },

    // 生成模拟打印内容（当后端API不可用时使用）
    generateMockPrintContent() {
      const currentDate = new Date().toLocaleDateString('zh-CN');
      const currentTime = new Date().toLocaleString('zh-CN');

      return `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>出入库单据</title>
          <style>
            body { font-family: 'SimSun', serif; margin: 20px; line-height: 1.6; }
            table { width: 100%; border-collapse: collapse; margin: 10px 0; }
            th, td { border: 1px solid #000; padding: 8px; text-align: left; }
            th { background-color: #f5f5f5; font-weight: bold; text-align: center; }
            .header { text-align: center; font-size: 24px; font-weight: bold; margin-bottom: 20px; }
            .info-table { margin-bottom: 20px; }
            .info-table td { border: none; padding: 5px 10px; }
            .signature { margin-top: 30px; }
            .signature table { border: none; }
            .signature td { border: none; text-align: center; padding: 20px; }
            .notice { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
            .material-table { margin: 20px 0; }
          </style>
        </head>
        <body>
          <div class="header">出入库申请单</div>

          <table class="info-table">
            <tr>
              <td><strong>单据编号：</strong>${this.businessId || '648638116061184'}</td>
              <td><strong>业务类型：</strong>物料出库</td>
              <td><strong>申请日期：</strong>${currentDate}</td>
            </tr>
            <tr>
              <td><strong>申请人：</strong>小强</td>
              <td><strong>申请部门：</strong>生产部</td>
              <td><strong>审批状态：</strong>已通过</td>
            </tr>
            <tr>
              <td><strong>预计执行时间：</strong>${currentDate}</td>
              <td><strong>创建时间：</strong>${currentTime}</td>
              <td><strong>备注：</strong>生产用料申请</td>
            </tr>
          </table>

          <table class="material-table">
            <tr>
              <th colspan="6">物料清单明细</th>
            </tr>
            <tr>
              <th>序号</th>
              <th>物料编码</th>
              <th>物料名称</th>
              <th>规格</th>
              <th>数量</th>
              <th>单位</th>
            </tr>
            <tr>
              <td>1</td>
              <td>MAT001</td>
              <td>钢材</td>
              <td>Q235</td>
              <td>100</td>
              <td>kg</td>
            </tr>
            <tr>
              <td>2</td>
              <td>MAT002</td>
              <td>螺栓</td>
              <td>M8×20</td>
              <td>50</td>
              <td>个</td>
            </tr>
          </table>

          <div class="signature">
            <table>
              <tr>
                <td>申请人签名：<br><br>_________________</td>
                <td>审批人签名：<br><br>_________________</td>
                <td>执行人签名：<br><br>_________________</td>
              </tr>
              <tr>
                <td>日期：___________</td>
                <td>日期：___________</td>
                <td>日期：___________</td>
              </tr>
            </table>
          </div>

          <div class="notice">
            <p><strong>预览说明：</strong></p>
            <p>• 这是模拟预览内容，用于展示打印格式</p>
            <p>• 实际打印时将使用真实的出入库单据数据</p>
            <p>• 如需查看实际数据，请确保后端服务正常运行</p>
          </div>
        </body>
        </html>
      `;
    },

    // 生成错误内容
    generateErrorContent(errorMessage) {
      return `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>打印错误</title>
          <style>
            body {
              font-family: 'SimSun', serif;
              margin: 20px;
              text-align: center;
              padding: 50px;
            }
            .error {
              color: #f56c6c;
              font-size: 16px;
              margin: 50px 0;
              border: 2px solid #f56c6c;
              border-radius: 8px;
              padding: 30px;
              background-color: #fef0f0;
            }
            .error h3 {
              margin-top: 0;
              color: #e6a23c;
            }
          </style>
        </head>
        <body>
          <div class="error">
            <h3>⚠️ 打印预览加载失败</h3>
            <p>${errorMessage}</p>
            <p style="font-size: 14px; color: #909399; margin-top: 20px;">
              请检查网络连接或联系系统管理员
            </p>
          </div>
        </body>
        </html>
      `;
    }
  }
};
</script>

<style scoped>
.print-preview-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.print-toolbar {
  padding: 10px;
  border-bottom: 1px solid #e6e6e6;
  background: #f5f5f5;
}

.print-preview-area {
  flex: 1;
  overflow: auto;
  padding: 20px;
  background: #f0f0f0;
}

.print-page {
  transition: transform 0.3s ease;
}

.print-content {
  font-family: 'SimSun', serif;
  line-height: 1.5;
}

/* 打印样式 */
@media print {
  .print-toolbar {
    display: none !important;
  }
  
  .print-preview-area {
    padding: 0 !important;
    background: white !important;
  }
  
  .print-page {
    transform: none !important;
    box-shadow: none !important;
    margin: 0 !important;
  }
}
</style>
