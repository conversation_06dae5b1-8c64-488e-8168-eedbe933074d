<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="设备ID" prop="deviceId">
        <el-input
          v-model="queryParams.deviceId"
          placeholder="请输入设备ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备类型" prop="deviceType">
        <el-select v-model="queryParams.deviceType" placeholder="请选择设备类型" clearable>
          <el-option label="重量传感器" value="weight_sensor" />
          <el-option label="门禁设备" value="access_control" />
          <el-option label="摄像头" value="camera" />
          <el-option label="环境传感器" value="env_sensor" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="设备状态" clearable>
          <el-option label="在线" value="online" />
          <el-option label="离线" value="offline" />
          <el-option label="故障" value="error" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['dwms:device:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['dwms:device:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['dwms:device:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-view"
          size="mini"
          @click="handleMonitor"
        >实时监控</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 设备状态概览 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <div class="stat-card online">
          <div class="stat-number">{{ deviceStats.online }}</div>
          <div class="stat-label">在线设备</div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card offline">
          <div class="stat-number">{{ deviceStats.offline }}</div>
          <div class="stat-label">离线设备</div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card warning">
          <div class="stat-number">{{ deviceStats.warning }}</div>
          <div class="stat-label">告警设备</div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card total">
          <div class="stat-number">{{ deviceStats.total }}</div>
          <div class="stat-label">设备总数</div>
        </div>
      </el-col>
    </el-row>

    <!-- 实时监控面板 -->
    <el-card v-if="showMonitor" class="mb8">
      <div slot="header">
        <span>设备实时监控</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="showMonitor = false">关闭</el-button>
      </div>
      <el-row :gutter="20">
        <el-col :span="6" v-for="device in monitorDevices" :key="device.deviceId">
          <el-card class="device-card">
            <div class="device-info">
              <h4>{{ device.deviceName }}</h4>
              <div class="device-id">{{ device.deviceId }}</div>
              <div class="device-status" :class="device.status">
                {{ device.status === 'online' ? '在线' : '离线' }}
              </div>
              <div class="device-data">
                <div v-if="device.deviceType === 'weight_sensor'">
                  重量: {{ device.currentValue }} kg
                </div>
                <div v-else-if="device.deviceType === 'env_sensor'">
                  温度: {{ device.temperature }}°C<br>
                  湿度: {{ device.humidity }}%
                </div>
                <div v-else>
                  状态: {{ device.currentValue }}
                </div>
              </div>
              <div class="update-time">{{ device.updateTime }}</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>

    <el-table v-loading="loading" :data="deviceList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="设备ID" align="center" prop="deviceId" />
      <el-table-column label="设备名称" align="center" prop="deviceName" />
      <el-table-column label="设备类型" align="center" prop="deviceType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.dwms_device_type" :value="scope.row.deviceType"/>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 'online' ? 'success' : scope.row.status === 'offline' ? 'info' : 'danger'">
            {{ scope.row.status === 'online' ? '在线' : scope.row.status === 'offline' ? '离线' : '故障' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="库房ID" align="center" prop="warehouseId" />
      <el-table-column label="位置" align="center" prop="location" />
      <el-table-column label="最后心跳" align="center" prop="lastHeartbeat" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.lastHeartbeat, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-setting"
            @click="handleControl(scope.row)"
          >控制</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['dwms:device:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加设备状态对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="设备ID" prop="deviceId">
          <el-input v-model="form.deviceId" placeholder="请输入设备ID" />
        </el-form-item>
        <el-form-item label="设备名称" prop="deviceName">
          <el-input v-model="form.deviceName" placeholder="请输入设备名称" />
        </el-form-item>
        <el-form-item label="设备类型" prop="deviceType">
          <el-select v-model="form.deviceType" placeholder="请选择设备类型">
            <el-option label="重量传感器" value="weight_sensor" />
            <el-option label="门禁设备" value="access_control" />
            <el-option label="摄像头" value="camera" />
            <el-option label="环境传感器" value="env_sensor" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择状态">
            <el-option label="在线" value="online" />
            <el-option label="离线" value="offline" />
            <el-option label="故障" value="error" />
          </el-select>
        </el-form-item>
        <el-form-item label="库房ID" prop="warehouseId">
          <el-input-number v-model="form.warehouseId" placeholder="请输入库房ID" />
        </el-form-item>
        <el-form-item label="位置" prop="location">
          <el-input v-model="form.location" placeholder="请输入位置信息" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog title="设备状态详情" :visible.sync="viewOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="设备ID">{{ viewData.deviceId }}</el-descriptions-item>
        <el-descriptions-item label="设备名称">{{ viewData.deviceName }}</el-descriptions-item>
        <el-descriptions-item label="设备类型">{{ viewData.deviceType }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="viewData.status === 'online' ? 'success' : viewData.status === 'offline' ? 'info' : 'danger'">
            {{ viewData.status === 'online' ? '在线' : viewData.status === 'offline' ? '离线' : '故障' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="库房ID">{{ viewData.warehouseId }}</el-descriptions-item>
        <el-descriptions-item label="位置">{{ viewData.location }}</el-descriptions-item>
        <el-descriptions-item label="最后心跳">{{ parseTime(viewData.lastHeartbeat, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(viewData.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 设备控制对话框 -->
    <el-dialog title="设备控制" :visible.sync="controlOpen" width="400px" append-to-body>
      <el-form :model="controlForm" label-width="80px">
        <el-form-item label="控制命令">
          <el-select v-model="controlForm.command" placeholder="请选择控制命令">
            <el-option label="重启设备" value="restart" />
            <el-option label="关闭设备" value="shutdown" />
            <el-option label="更新固件" value="update" />
            <el-option label="重置配置" value="reset" />
          </el-select>
        </el-form-item>
        <el-form-item label="参数">
          <el-input v-model="controlForm.params" placeholder="请输入参数（可选）" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="sendControlCommand">确 定</el-button>
        <el-button @click="controlOpen = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import { listDevice, getDevice, delDevice, addDevice } from "@/api/dwms/device";

export default {
  name: "Device",
  dicts: ['dwms_device_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设备状态表格数据
      deviceList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查看详情弹出层
      viewOpen: false,
      // 控制弹出层
      controlOpen: false,
      // 查看数据
      viewData: {},
      // 控制表单
      controlForm: {},
      // 显示监控
      showMonitor: false,
      // 监控设备数据
      monitorDevices: [],
      // 设备统计
      deviceStats: {
        online: 0,
        offline: 0,
        warning: 0,
        total: 0
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceId: null,
        deviceType: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        deviceId: [
          { required: true, message: "设备ID不能为空", trigger: "blur" }
        ],
        deviceName: [
          { required: true, message: "设备名称不能为空", trigger: "blur" }
        ],
        deviceType: [
          { required: true, message: "设备类型不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.loadDeviceStats();
  },
  methods: {
    /** 查询设备状态列表 */
    getList() {
      this.loading = true;

      // 使用模拟数据
      setTimeout(() => {
        this.deviceList = [
          {
            id: 1,
            deviceId: 'DEV001',
            deviceName: '重量传感器1',
            deviceType: 'weight_sensor',
            status: 'online',
            warehouseId: 1,
            location: 'A区-01货架',
            lastHeartbeat: new Date().toISOString(),
            createTime: new Date().toISOString()
          },
          {
            id: 2,
            deviceId: 'DEV002',
            deviceName: '门禁设备1',
            deviceType: 'access_control',
            status: 'online',
            warehouseId: 1,
            location: '主入口',
            lastHeartbeat: new Date().toISOString(),
            createTime: new Date().toISOString()
          },
          {
            id: 3,
            deviceId: 'DEV003',
            deviceName: '摄像头1',
            deviceType: 'camera',
            status: 'offline',
            warehouseId: 1,
            location: 'B区监控点',
            lastHeartbeat: new Date(Date.now() - 300000).toISOString(),
            createTime: new Date().toISOString()
          }
        ];
        this.total = this.deviceList.length;
        this.loading = false;
      }, 500);

      // 实际API调用（当后端准备好时启用）
      /*
      listDevice(this.queryParams).then(response => {
        this.deviceList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        console.error('获取设备状态失败:', error);
        this.loading = false;
        this.$message.error('获取设备状态失败');
      });
      */
    },
    /** 加载设备统计 */
    loadDeviceStats() {
      // 模拟统计数据，实际应该调用API
      this.deviceStats = {
        online: 15,
        offline: 3,
        warning: 2,
        total: 20
      };
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        deviceId: null,
        deviceName: null,
        deviceType: null,
        status: "online",
        warehouseId: null,
        location: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加设备状态";
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 模拟新增成功
          this.$message.success("新增成功");
          this.open = false;
          this.getList();

          // 实际API调用（当后端准备好时启用）
          /*
          addDevice(this.form).then(response => {
            this.$message.success("新增成功");
            this.open = false;
            this.getList();
          }).catch(error => {
            this.$message.error("新增失败");
          });
          */
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('是否确认删除设备状态编号为"' + ids + '"的数据项？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 模拟删除成功
        this.getList();
        this.$message.success("删除成功");

        // 实际API调用（当后端准备好时启用）
        /*
        return delDevice(ids);
        }).then(() => {
          this.getList();
          this.$message.success("删除成功");
        }).catch(error => {
          this.$message.error("删除失败");
        */
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('dwms/device/export', {
        ...this.queryParams
      }, `device_${new Date().getTime()}.xlsx`)
    },
    /** 查看详情 */
    handleView(row) {
      this.viewData = row;
      this.viewOpen = true;
    },
    /** 设备控制 */
    handleControl(row) {
      this.controlForm = {
        deviceId: row.deviceId,
        command: null,
        params: null
      };
      this.controlOpen = true;
    },
    /** 发送控制命令 */
    sendControlCommand() {
      if (!this.controlForm.command) {
        this.$message.warning('请选择控制命令');
        return;
      }
      // 这里应该调用API发送控制命令
      this.$message.success('控制命令已发送');
      this.controlOpen = false;
    },
    /** 实时监控 */
    handleMonitor() {
      this.showMonitor = !this.showMonitor;
      if (this.showMonitor) {
        this.loadMonitorData();
        // 每5秒刷新一次监控数据
        this.monitorTimer = setInterval(() => {
          this.loadMonitorData();
        }, 5000);
      } else {
        if (this.monitorTimer) {
          clearInterval(this.monitorTimer);
        }
      }
    },
    /** 加载监控数据 */
    loadMonitorData() {
      // 模拟监控数据，实际应该调用API
      this.monitorDevices = [
        {
          deviceId: 'DEV001',
          deviceName: '重量传感器1',
          deviceType: 'weight_sensor',
          status: 'online',
          currentValue: (Math.random() * 1000).toFixed(2),
          updateTime: new Date().toLocaleTimeString()
        },
        {
          deviceId: 'DEV002',
          deviceName: '环境传感器1',
          deviceType: 'env_sensor',
          status: 'online',
          temperature: (Math.random() * 30 + 10).toFixed(1),
          humidity: (Math.random() * 50 + 30).toFixed(1),
          updateTime: new Date().toLocaleTimeString()
        },
        {
          deviceId: 'DEV003',
          deviceName: '门禁设备1',
          deviceType: 'access_control',
          status: Math.random() > 0.8 ? 'offline' : 'online',
          currentValue: '正常',
          updateTime: new Date().toLocaleTimeString()
        }
      ];
    }
  },
  beforeDestroy() {
    if (this.monitorTimer) {
      clearInterval(this.monitorTimer);
    }
  }
};
</script>

<style scoped>
.stat-card {
  padding: 20px;
  text-align: center;
  border-radius: 4px;
  color: white;
  margin-bottom: 20px;
}

.stat-card.online {
  background: linear-gradient(135deg, #67C23A, #85CE61);
}

.stat-card.offline {
  background: linear-gradient(135deg, #909399, #B1B3B8);
}

.stat-card.warning {
  background: linear-gradient(135deg, #E6A23C, #EEBE77);
}

.stat-card.total {
  background: linear-gradient(135deg, #409EFF, #66B1FF);
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

.device-card {
  margin-bottom: 10px;
}

.device-info {
  text-align: center;
}

.device-id {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
}

.device-status.online {
  color: #67C23A;
  font-weight: bold;
}

.device-status.offline {
  color: #F56C6C;
  font-weight: bold;
}

.device-data {
  margin: 10px 0;
  font-size: 14px;
  color: #606266;
}

.update-time {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style>
