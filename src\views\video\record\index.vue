<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="设备名称" prop="deviceName">
        <el-input v-model="queryParams.deviceName" placeholder="请输入设备名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="录制类型" prop="recordType">
        <el-select v-model="queryParams.recordType" placeholder="请选择录制类型" clearable>
          <el-option label="手动录制" value="manual"/>
          <el-option label="自动录制" value="auto"/>
          <el-option label="告警录制" value="alarm"/>
        </el-select>
      </el-form-item>
      <el-form-item label="录制时间">
        <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['video:record:add']">新增录制</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-video-play" size="mini" :disabled="single" @click="handlePlayback" v-hasPermi="['video:record:playback']">回放</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-download" size="mini" :disabled="multiple" @click="handleDownload" v-hasPermi="['video:record:download']">下载</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['video:record:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['video:record:export']">导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-setting" size="mini" @click="showSearch = !showSearch">
          {{ showSearch ? '隐藏' : '显示' }}搜索
        </el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="recordList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="录制ID" align="center" prop="id" />
      <el-table-column label="录制名称" align="center" prop="recordName" />
      <el-table-column label="设备名称" align="center" prop="deviceName" />
      <el-table-column label="录制类型" align="center" prop="recordType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.video_record_type" :value="scope.row.recordType"/>
        </template>
      </el-table-column>
      <el-table-column label="文件大小" align="center" prop="fileSize">
        <template slot-scope="scope">
          <span>{{ formatFileSize(scope.row.fileSize) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="录制时长" align="center" prop="duration">
        <template slot-scope="scope">
          <span>{{ formatDuration(scope.row.duration) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="录制状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status == 1 ? 'success' : scope.row.status == 0 ? 'warning' : 'danger'">
            {{ scope.row.status == 1 ? '完成' : scope.row.status == 0 ? '录制中' : '失败' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="开始时间" align="center" prop="startTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-video-play" @click="handlePlayback(scope.row)" v-hasPermi="['video:record:playback']">回放</el-button>
          <el-button size="mini" type="text" icon="el-icon-download" @click="handleDownload(scope.row)" v-hasPermi="['video:record:download']">下载</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['video:record:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加录制对话框 -->
    <el-dialog title="新增录制" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="设备选择" prop="deviceId">
              <el-select v-model="form.deviceId" placeholder="请选择设备" style="width: 100%" @change="handleDeviceChange">
                <el-option
                  v-for="device in deviceOptions"
                  :key="device.id"
                  :label="`${device.deviceName} (${device.deviceCode})`"
                  :value="device.id"
                  :disabled="device.status !== 1">
                  <span style="float: left">{{ device.deviceName }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">
                    {{ device.deviceCode }}
                    <el-tag :type="device.status == 1 ? 'success' : 'danger'" size="mini" style="margin-left: 5px">
                      {{ device.status == 1 ? '在线' : '离线' }}
                    </el-tag>
                  </span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="selectedDevice">
          <el-col :span="12">
            <el-form-item label="设备状态">
              <el-tag :type="selectedDevice.status == 1 ? 'success' : 'danger'">
                {{ selectedDevice.status == 1 ? '在线' : '离线' }}
              </el-tag>
              <span v-if="selectedDevice.status !== 1" style="color: #f56c6c; font-size: 12px; margin-left: 10px">
                (设备离线，无法录制)
              </span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备位置">
              <span>{{ selectedDevice.location || '未设置' }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="录制名称" prop="recordName">
              <el-input v-model="form.recordName" placeholder="请输入录制名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="录制类型" prop="recordType">
              <el-select v-model="form.recordType" placeholder="请选择录制类型">
                <el-option label="手动录制" value="manual"/>
                <el-option label="自动录制" value="auto"/>
                <el-option label="告警录制" value="alarm"/>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="录制时长" prop="duration">
              <el-input-number v-model="form.duration" :min="60" :max="86400" placeholder="录制时长(秒)" style="width: 100%" />
              <div style="font-size: 12px; color: #999; margin-top: 4px;">
                {{ formatDuration(form.duration) }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="文件格式" prop="fileFormat">
              <el-select v-model="form.fileFormat" placeholder="请选择文件格式">
                <el-option label="MP4" value="mp4"/>
                <el-option label="AVI" value="avi"/>
                <el-option label="FLV" value="flv"/>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="录制质量" prop="quality">
              <el-select v-model="form.quality" placeholder="请选择录制质量">
                <el-option label="高清 (1080P)" value="1080p"/>
                <el-option label="标清 (720P)" value="720p"/>
                <el-option label="流畅 (480P)" value="480p"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="自动停止">
              <el-switch v-model="form.autoStop" active-text="是" inactive-text="否" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>

        <!-- 设备状态提示 -->
        <el-alert
          v-if="deviceOptions.length === 0"
          title="暂无设备"
          description="请先在设备管理中添加监控设备"
          type="warning"
          :closable="false"
          style="margin-bottom: 15px;">
        </el-alert>

        <el-alert
          v-else-if="deviceOptions.filter(d => d.status === 1).length === 0"
          title="所有设备均离线"
          description="设备需要注册到ZLMediaKit服务器才能进行录制。请检查设备网络连接和ZLMediaKit服务状态。"
          type="warning"
          :closable="false"
          style="margin-bottom: 15px;">
        </el-alert>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="!selectedDevice || selectedDevice.status !== 1">开始录制</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 视频回放对话框 -->
    <el-dialog title="视频回放" :visible.sync="playbackOpen" width="800px" append-to-body>
      <div class="video-player">
        <video controls width="100%" height="400">
          <source :src="playbackUrl" type="video/mp4">
          您的浏览器不支持视频播放
        </video>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listRecord, getRecord, addRecord, delRecord, startRecord, stopRecord, getRecordStatus, downloadRecord, getPlaybackUrl } from "@/api/video/record";
import { listDevice } from "@/api/video/device";

export default {
  name: "VideoRecord",
  dicts: ['video_record_type'],
  data() {
    return {
      loading: true,
      ids: [],
      single: true,
      multiple: true,
      showSearch: true,
      total: 0,
      recordList: [],
      open: false,
      playbackOpen: false,
      playbackUrl: '',
      dateRange: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceName: null,
        recordType: null
      },
      form: {},
      deviceOptions: [],
      selectedDevice: null,
      rules: {
        deviceId: [{ required: true, message: "请选择设备", trigger: "change" }],
        recordName: [{ required: true, message: "录制名称不能为空", trigger: "blur" }],
        recordType: [{ required: true, message: "请选择录制类型", trigger: "change" }],
        duration: [{ required: true, message: "请输入录制时长", trigger: "blur" }],
        fileFormat: [{ required: true, message: "请选择文件格式", trigger: "change" }],
        quality: [{ required: true, message: "请选择录制质量", trigger: "change" }]
      }
    };
  },
  created() {
    this.getList();
    this.loadDeviceOptions();
  },
  methods: {
    /** 查询录制记录列表 */
    getList() {
      this.loading = true;
      const params = Object.assign({}, this.queryParams);
      if (this.dateRange != null && this.dateRange !== '') {
        params.startTime = this.dateRange[0];
        params.endTime = this.dateRange[1];
      }
      listRecord(params).then(response => {
        this.recordList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    /** 加载设备选项 */
    loadDeviceOptions() {
      // 加载所有设备，不限制状态
      listDevice({}).then(response => {
        this.deviceOptions = response.rows || [];
        console.log('加载设备列表:', this.deviceOptions);
      }).catch(error => {
        console.error('加载设备列表失败:', error);
        this.$modal.msgError('加载设备列表失败');
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        deviceId: null,
        recordName: null,
        recordType: 'manual',
        duration: 3600,
        fileFormat: 'mp4',
        quality: '1080p',
        autoStop: true,
        remark: null
      };
      this.selectedDevice = null;
      this.resetForm("form");
    },
    handleQuery() { this.queryParams.pageNum = 1; this.getList(); },
    resetQuery() { this.resetForm("queryForm"); this.dateRange = []; this.handleQuery(); },
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
    },
    /** 设备选择变化处理 */
    handleDeviceChange(deviceId) {
      this.selectedDevice = this.deviceOptions.find(device => device.id === deviceId);
      if (this.selectedDevice) {
        // 自动生成录制名称
        const now = new Date();
        const dateStr = now.getFullYear() +
                       String(now.getMonth() + 1).padStart(2, '0') +
                       String(now.getDate()).padStart(2, '0') + '_' +
                       String(now.getHours()).padStart(2, '0') +
                       String(now.getMinutes()).padStart(2, '0');
        this.form.recordName = `${this.selectedDevice.deviceName}_${dateStr}`;
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          const params = {
            deviceId: this.form.deviceId,
            recordName: this.form.recordName,
            recordType: this.form.recordType,
            duration: this.form.duration
          };

          startRecord(params).then(response => {
            this.$modal.msgSuccess("录制开始成功");
            this.open = false;
            this.getList();
          }).catch(error => {
            this.$modal.msgError("录制开始失败: " + (error.message || error));
          });
        }
      });
    },
    /** 回放按钮操作 */
    handlePlayback(row) {
      if (row.status !== 1) {
        this.$modal.msgWarning("只能回放已完成的录制文件");
        return;
      }

      getPlaybackUrl(row.id).then(response => {
        this.playbackUrl = response.data;
        this.playbackOpen = true;
      }).catch(error => {
        this.$modal.msgError("获取回放地址失败: " + (error.message || error));
      });
    },
    /** 下载按钮操作 */
    handleDownload(row) {
      const ids = row.id || this.ids;

      if (Array.isArray(ids)) {
        // 批量下载
        this.$modal.msgInfo("批量下载功能开发中...");
      } else {
        // 单个下载
        if (row.status !== 1) {
          this.$modal.msgWarning("只能下载已完成的录制文件");
          return;
        }

        this.$modal.loading("正在准备下载...");
        downloadRecord(ids).then(response => {
          this.$modal.closeLoading();

          // 创建下载链接
          const blob = new Blob([response], { type: 'application/octet-stream' });
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = row.fileName || `record_${ids}.mp4`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);

          this.$modal.msgSuccess("下载成功");
        }).catch(error => {
          this.$modal.closeLoading();
          this.$modal.msgError("下载失败: " + (error.message || error));
        });
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      const names = row.recordName || this.recordList.filter(item => this.ids.includes(item.id)).map(item => item.recordName).join(', ');

      this.$modal.confirm('是否确认删除录制记录"' + names + '"？').then(() => {
        if (Array.isArray(ids)) {
          // 批量删除
          Promise.all(ids.map(id => delRecord(id))).then(() => {
            this.$modal.msgSuccess("删除成功");
            this.getList();
          }).catch(error => {
            this.$modal.msgError("删除失败: " + (error.message || error));
          });
        } else {
          // 单个删除
          delRecord(ids).then(() => {
            this.$modal.msgSuccess("删除成功");
            this.getList();
          }).catch(error => {
            this.$modal.msgError("删除失败: " + (error.message || error));
          });
        }
      }).catch(() => {});
    },
    handleExport() { this.$modal.msgSuccess("导出成功"); },
    formatFileSize(size) {
      if (!size) return '0 B';
      const units = ['B', 'KB', 'MB', 'GB'];
      let index = 0;
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024;
        index++;
      }
      return size.toFixed(2) + ' ' + units[index];
    },
    formatDuration(seconds) {
      if (!seconds) return '0秒';
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = seconds % 60;
      if (hours > 0) {
        return `${hours}时${minutes}分${secs}秒`;
      } else if (minutes > 0) {
        return `${minutes}分${secs}秒`;
      } else {
        return `${secs}秒`;
      }
    }
  }
};
</script>

<style scoped>
.video-player {
  text-align: center;
}
</style>
