<template>
  <div class="base-chart">
    <div
      ref="chartContainer"
      :style="{ width: width, height: height }"
      v-loading="loading"
    ></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { debounce } from 'lodash'

export default {
  name: 'BaseChart',
  props: {
    // 图表配置
    option: {
      type: Object,
      required: true
    },
    // 图表宽度
    width: {
      type: String,
      default: '100%'
    },
    // 图表高度
    height: {
      type: String,
      default: '400px'
    },
    // 是否自动调整大小
    autoResize: {
      type: Boolean,
      default: true
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    },
    // 主题
    theme: {
      type: String,
      default: 'default'
    },
    // 图表类型
    chartType: {
      type: String,
      default: 'line'
    },
    // 是否启用动画
    animation: {
      type: Boolean,
      default: true
    },
    // 点击事件
    clickable: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      chart: null,
      resizeHandler: null
    }
  },
  
  watch: {
    option: {
      handler(newOption) {
        if (this.chart) {
          this.updateChart(newOption)
        }
      },
      deep: true
    },
    
    loading(newVal) {
      if (this.chart) {
        if (newVal) {
          this.chart.showLoading('default', {
            text: '加载中...',
            color: '#409EFF',
            textColor: '#000',
            maskColor: 'rgba(255, 255, 255, 0.8)',
            zlevel: 0
          })
        } else {
          this.chart.hideLoading()
        }
      }
    }
  },
  
  mounted() {
    this.initChart()
    if (this.autoResize) {
      this.initResizeHandler()
    }
  },
  
  beforeDestroy() {
    this.destroyChart()
  },
  
  methods: {
    /** 初始化图表 */
    initChart() {
      if (!this.$refs.chartContainer) {
        return
      }
      
      // 初始化ECharts实例
      this.chart = echarts.init(this.$refs.chartContainer, this.theme)
      
      // 设置图表配置
      this.updateChart(this.option)
      
      // 绑定事件
      if (this.clickable) {
        this.chart.on('click', this.handleChartClick)
      }
      
      // 绑定其他事件
      this.chart.on('legendselectchanged', this.handleLegendChange)
      this.chart.on('datazoom', this.handleDataZoom)
      
      this.$emit('chart-ready', this.chart)
    },
    
    /** 更新图表 */
    updateChart(option) {
      if (!this.chart || !option) {
        return
      }
      
      // 合并默认配置
      const mergedOption = this.mergeDefaultOption(option)
      
      // 设置图表配置
      this.chart.setOption(mergedOption, true)
    },
    
    /** 合并默认配置 */
    mergeDefaultOption(option) {
      const defaultOption = {
        animation: this.animation,
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(50, 50, 50, 0.9)',
          borderColor: '#333',
          borderWidth: 1,
          textStyle: {
            color: '#fff',
            fontSize: 12
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        toolbox: {
          feature: {
            saveAsImage: {
              title: '保存为图片'
            },
            dataZoom: {
              title: {
                zoom: '区域缩放',
                back: '区域缩放还原'
              }
            },
            restore: {
              title: '还原'
            }
          }
        }
      }
      
      return this.deepMerge(defaultOption, option)
    },
    
    /** 深度合并对象 */
    deepMerge(target, source) {
      const result = { ...target }
      
      for (const key in source) {
        if (source.hasOwnProperty(key)) {
          if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
            result[key] = this.deepMerge(result[key] || {}, source[key])
          } else {
            result[key] = source[key]
          }
        }
      }
      
      return result
    },
    
    /** 初始化窗口大小调整处理器 */
    initResizeHandler() {
      this.resizeHandler = debounce(() => {
        if (this.chart) {
          this.chart.resize()
        }
      }, 100)
      
      window.addEventListener('resize', this.resizeHandler)
    },
    
    /** 手动调整图表大小 */
    resize() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    
    /** 销毁图表 */
    destroyChart() {
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
      
      if (this.resizeHandler) {
        window.removeEventListener('resize', this.resizeHandler)
        this.resizeHandler = null
      }
    },
    
    /** 图表点击事件 */
    handleChartClick(params) {
      this.$emit('chart-click', params)
    },
    
    /** 图例选择变化事件 */
    handleLegendChange(params) {
      this.$emit('legend-change', params)
    },
    
    /** 数据缩放事件 */
    handleDataZoom(params) {
      this.$emit('data-zoom', params)
    },
    
    /** 获取图表实例 */
    getChart() {
      return this.chart
    },
    
    /** 获取图表数据URL */
    getDataURL(options = {}) {
      if (!this.chart) {
        return null
      }
      
      const defaultOptions = {
        type: 'png',
        pixelRatio: 1,
        backgroundColor: '#fff'
      }
      
      return this.chart.getDataURL({ ...defaultOptions, ...options })
    },
    
    /** 下载图表 */
    downloadChart(filename = 'chart') {
      const dataURL = this.getDataURL()
      if (dataURL) {
        const link = document.createElement('a')
        link.download = `${filename}.png`
        link.href = dataURL
        link.click()
      }
    },
    
    /** 设置图表主题 */
    setTheme(theme) {
      if (this.chart) {
        this.destroyChart()
        this.$nextTick(() => {
          this.theme = theme
          this.initChart()
        })
      }
    },
    
    /** 显示加载动画 */
    showLoading(text = '加载中...') {
      if (this.chart) {
        this.chart.showLoading('default', {
          text: text,
          color: '#409EFF',
          textColor: '#000',
          maskColor: 'rgba(255, 255, 255, 0.8)',
          zlevel: 0
        })
      }
    },
    
    /** 隐藏加载动画 */
    hideLoading() {
      if (this.chart) {
        this.chart.hideLoading()
      }
    },
    
    /** 清空图表 */
    clear() {
      if (this.chart) {
        this.chart.clear()
      }
    },
    
    /** 刷新图表 */
    refresh() {
      if (this.chart) {
        this.chart.resize()
        this.updateChart(this.option)
      }
    }
  }
}
</script>

<style scoped>
.base-chart {
  position: relative;
}

.base-chart .el-loading-mask {
  border-radius: 4px;
}
</style>
