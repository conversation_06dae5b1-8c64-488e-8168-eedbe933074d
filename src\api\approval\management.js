import request from '@/utils/request'

// 查询审批实例列表（管理员）
export function listApprovalInstances(query) {
  return request({
    url: '/approval/management/instances',
    method: 'get',
    params: query
  })
}

// 查询所有待审批列表（管理员）
export function listAllTodoApprovals(query) {
  return request({
    url: '/approval/management/todo/all',
    method: 'get',
    params: query
  })
}

// 查询所有已完成审批列表（管理员）
export function listAllDoneApprovals(query) {
  return request({
    url: '/approval/management/done/all',
    method: 'get',
    params: query
  })
}

// 获取审批统计信息（管理员）
export function getApprovalStatistics() {
  return request({
    url: '/approval/management/statistics',
    method: 'get'
  })
}

// 导出审批实例
export function exportApprovalInstances(query) {
  return request({
    url: '/approval/management/instances/export',
    method: 'post',
    data: query
  })
}
