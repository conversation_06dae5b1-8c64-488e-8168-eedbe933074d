<template>
  <div class="video-player-container" ref="container">
    <video 
      ref="videoElement"
      class="video-element"
      :poster="poster"
      @loadstart="onLoadStart"
      @loadeddata="onLoadedData"
      @error="onError"
      @play="onPlay"
      @pause="onPause"
      @ended="onEnded">
    </video>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="video-loading">
      <i class="el-icon-loading"></i>
      <p>正在加载视频流...</p>
    </div>
    
    <!-- 错误状态 -->
    <div v-if="error" class="video-error">
      <i class="el-icon-warning"></i>
      <p>播放失败</p>
      <p class="error-msg">{{ error }}</p>
      <el-button size="mini" @click="retry">重试</el-button>
    </div>
    
    <!-- 控制栏 -->
    <div v-if="showControls" class="video-controls" @click.stop>
      <div class="controls-left">
        <el-button 
          :icon="playing ? 'el-icon-video-pause' : 'el-icon-video-play'"
          size="mini"
          circle
          @click="togglePlay">
        </el-button>
        <span class="time-display">{{ currentTimeText }} / {{ durationText }}</span>
      </div>
      
      <div class="controls-center">
        <el-slider
          v-model="progress"
          :max="100"
          :show-tooltip="false"
          @change="onSeek"
          class="progress-slider">
        </el-slider>
      </div>
      
      <div class="controls-right">
        <el-button icon="el-icon-camera" size="mini" circle @click="takeSnapshot"></el-button>
        <el-button icon="el-icon-video-camera" size="mini" circle @click="startRecord"></el-button>
        <el-button icon="el-icon-full-screen" size="mini" circle @click="toggleFullscreen"></el-button>
      </div>
    </div>
  </div>
</template>

<script>
// 简化版本，先使用原生video标签，后续可以扩展
export default {
  name: "VideoPlayer",
  props: {
    src: {
      type: String,
      required: true
    },
    type: {
      type: String,
      default: 'flv' // flv, hls, mp4
    },
    autoplay: {
      type: Boolean,
      default: true
    },
    muted: {
      type: Boolean,
      default: true
    },
    poster: {
      type: String,
      default: ''
    },
    showControls: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      player: null,
      loading: false,
      error: null,
      playing: false,
      currentTime: 0,
      duration: 0,
      progress: 0
    };
  },
  computed: {
    currentTimeText() {
      return this.formatTime(this.currentTime);
    },
    durationText() {
      return this.formatTime(this.duration);
    }
  },
  mounted() {
    this.initPlayer();
  },
  beforeDestroy() {
    this.destroyPlayer();
  },
  watch: {
    src() {
      this.destroyPlayer();
      this.initPlayer();
    }
  },
  methods: {
    async initPlayer() {
      if (!this.src) return;

      this.loading = true;
      this.error = null;

      try {
        // 暂时使用原生播放器，后续可以扩展flv.js和hls.js
        await this.initNativePlayer();
      } catch (err) {
        this.error = err.message || '播放器初始化失败';
        this.loading = false;
      }
    },
    

    
    async initNativePlayer() {
      const video = this.$refs.videoElement;
      video.src = this.src;
      video.muted = this.muted;

      this.setupVideoEvents();

      if (this.autoplay) {
        try {
          await video.play();
        } catch (err) {
          console.warn('Autoplay failed:', err);
          this.loading = false;
        }
      } else {
        this.loading = false;
      }
    },
    

    
    setupVideoEvents() {
      const video = this.$refs.videoElement;
      
      video.addEventListener('timeupdate', () => {
        this.currentTime = video.currentTime;
        if (video.duration) {
          this.progress = (video.currentTime / video.duration) * 100;
        }
      });
      
      video.addEventListener('durationchange', () => {
        this.duration = video.duration;
      });
    },
    
    destroyPlayer() {
      const video = this.$refs.videoElement;
      if (video) {
        video.pause();
        video.src = '';
        video.load();
      }
      this.player = null;
    },
    
    togglePlay() {
      const video = this.$refs.videoElement;
      if (video.paused) {
        video.play();
      } else {
        video.pause();
      }
    },
    
    onSeek(value) {
      const video = this.$refs.videoElement;
      if (video.duration) {
        video.currentTime = (value / 100) * video.duration;
      }
    },
    
    takeSnapshot() {
      const video = this.$refs.videoElement;
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
      
      // 转换为blob并下载
      canvas.toBlob((blob) => {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `snapshot_${new Date().getTime()}.png`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.$emit('snapshot', blob);
        this.$message.success('截图成功');
      }, 'image/png');
    },
    
    startRecord() {
      this.$emit('record');
      this.$message.info('录制功能开发中...');
    },
    
    toggleFullscreen() {
      const container = this.$refs.container;
      
      if (!document.fullscreenElement) {
        container.requestFullscreen().catch(err => {
          console.error('进入全屏失败:', err);
        });
      } else {
        document.exitFullscreen();
      }
    },
    
    retry() {
      this.error = null;
      this.initPlayer();
    },
    
    formatTime(seconds) {
      if (!seconds || !isFinite(seconds)) return '00:00';
      
      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    },
    
    // 事件处理
    onLoadStart() {
      this.loading = true;
    },
    
    onLoadedData() {
      this.loading = false;
    },
    
    onError(e) {
      this.error = '视频加载失败';
      this.loading = false;
    },
    
    onPlay() {
      this.playing = true;
      this.$emit('play');
    },
    
    onPause() {
      this.playing = false;
      this.$emit('pause');
    },
    
    onEnded() {
      this.playing = false;
      this.$emit('ended');
    }
  }
};
</script>

<style scoped>
.video-player-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #000;
  overflow: hidden;
}

.video-element {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.video-loading,
.video-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.8);
  color: white;
}

.video-loading i {
  font-size: 32px;
  margin-bottom: 10px;
}

.video-error i {
  font-size: 48px;
  color: #f56c6c;
  margin-bottom: 10px;
}

.error-msg {
  font-size: 12px;
  opacity: 0.8;
  margin-bottom: 10px;
}

.video-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 20px 15px 15px;
  display: flex;
  align-items: center;
  gap: 15px;
  opacity: 0;
  transition: opacity 0.3s;
}

.video-player-container:hover .video-controls {
  opacity: 1;
}

.controls-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.time-display {
  color: white;
  font-size: 12px;
  white-space: nowrap;
}

.controls-center {
  flex: 1;
}

.progress-slider {
  margin: 0;
}

.controls-right {
  display: flex;
  gap: 5px;
}

/* 全屏样式 */
.video-player-container:fullscreen {
  background: #000;
}

.video-player-container:fullscreen .video-element {
  object-fit: contain;
}
</style>
