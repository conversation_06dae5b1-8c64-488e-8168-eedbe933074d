<template>
  <div class="app-container">
    <el-card class="filter-container" shadow="hover">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
        <el-form-item label="仓库" prop="warehouseId">
          <el-select v-model="queryParams.warehouseId" placeholder="请选择仓库" clearable style="width: 240px" @change="handleWarehouseChange">
            <el-option
              v-for="item in warehouseOptions"
              :key="item.id"
              :label="item.warehouseName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="库区" prop="zoneId">
          <el-select v-model="queryParams.zoneId" placeholder="请选择库区" clearable style="width: 240px">
            <el-option
              v-for="item in zoneOptions"
              :key="item.id"
              :label="item.zoneName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="库位编号" prop="locationCode">
          <el-input
            v-model="queryParams.locationCode"
            placeholder="请输入库位编号"
            clearable
            prefix-icon="el-icon-document"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="库位类型" prop="locationType">
          <el-select v-model="queryParams.locationType" placeholder="请选择库位类型" clearable>
            <el-option label="标准库位" value="0" />
            <el-option label="托盘库位" value="1" />
            <el-option label="散装库位" value="2" />
            <el-option label="特殊库位" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option label="空闲" value="0" />
            <el-option label="占用" value="1" />
            <el-option label="锁定" value="2" />
            <el-option label="禁用" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="table-container" shadow="hover" style="margin-top: 10px;">
      <!-- 添加数据统计卡片 -->
      <el-row :gutter="20" style="margin-bottom: 20px;">
        <el-col :span="6">
          <el-card shadow="hover" class="data-card">
            <div slot="header" class="clearfix">
              <span>库位总数</span>
            </div>
            <div class="data-card-body">
              <div class="data-card-value">{{ total }}</div>
              <div class="data-card-icon">
                <i class="el-icon-s-grid"></i>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="data-card status-free">
            <div slot="header" class="clearfix">
              <span>空闲库位</span>
            </div>
            <div class="data-card-body">
              <div class="data-card-value">{{ statusCounts['0'] || 0 }}</div>
              <div class="data-card-icon">
                <i class="el-icon-circle-check"></i>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="data-card status-occupied">
            <div slot="header" class="clearfix">
              <span>占用库位</span>
            </div>
            <div class="data-card-body">
              <div class="data-card-value">{{ statusCounts['1'] || 0 }}</div>
              <div class="data-card-icon">
                <i class="el-icon-goods"></i>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="data-card status-locked">
            <div slot="header" class="clearfix">
              <span>锁定/禁用库位</span>
            </div>
            <div class="data-card-body">
              <div class="data-card-value">{{ (statusCounts['2'] || 0) + (statusCounts['3'] || 0) }}</div>
              <div class="data-card-icon">
                <i class="el-icon-lock"></i>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['warehouse:location:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['warehouse:location:edit']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['warehouse:location:remove']"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['warehouse:location:export']"
          >导出</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="info"
            icon="el-icon-sort"
            size="mini"
            @click="handleBatchGenerate"
            v-hasPermi="['warehouse:location:add']"
          >批量生成</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="locationList" @selection-change="handleSelectionChange" border stripe highlight-current-row>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="库位ID" align="center" prop="id" width="80" />
        <el-table-column label="仓库名称" align="center" prop="warehouseName" min-width="120" show-overflow-tooltip />
        <el-table-column label="库区名称" align="center" prop="zoneName" min-width="120" show-overflow-tooltip />
        <el-table-column label="库位编号" align="center" prop="locationCode" min-width="120" />
        <el-table-column label="库位类型" align="center" prop="locationType" width="100">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.locationType == '0'" type="success">标准库位</el-tag>
            <el-tag v-else-if="scope.row.locationType == '1'" type="primary">托盘库位</el-tag>
            <el-tag v-else-if="scope.row.locationType == '2'" type="info">散装库位</el-tag>
            <el-tag v-else-if="scope.row.locationType == '3'" type="warning">特殊库位</el-tag>
            <el-tag v-else>{{ scope.row.locationType }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="库位状态" align="center" prop="status" width="90">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status == '0'" type="success">空闲</el-tag>
            <el-tag v-else-if="scope.row.status == '1'" type="primary">占用</el-tag>
            <el-tag v-else-if="scope.row.status == '2'" type="warning">锁定</el-tag>
            <el-tag v-else-if="scope.row.status == '3'" type="danger">禁用</el-tag>
            <el-tag v-else>{{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="长(CM)" align="center" prop="length" width="80" />
        <el-table-column label="宽(CM)" align="center" prop="width" width="80" />
        <el-table-column label="高(CM)" align="center" prop="height" width="80" />
        <el-table-column label="载重(KG)" align="center" prop="loadCapacity" width="90" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
              v-hasPermi="['warehouse:location:query']"
            >详情</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['warehouse:location:edit']"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['warehouse:location:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改库位信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="所属仓库" prop="warehouseId">
          <el-select v-model="form.warehouseId" placeholder="请选择仓库" style="width: 100%" @change="handleFormWarehouseChange">
            <el-option
              v-for="item in warehouseOptions"
              :key="item.id"
              :label="item.warehouseName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="所属库区" prop="zoneId">
          <el-select v-model="form.zoneId" placeholder="请选择库区" style="width: 100%" @change="handleFormZoneChange">
            <el-option
              v-for="item in formZoneOptions"
              :key="item.id"
              :label="item.zoneName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="库位编号" prop="locationCode">
              <el-input v-model="form.locationCode" placeholder="请输入库位编号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="库位类型" prop="locationType">
              <el-select v-model="form.locationType" placeholder="请选择库位类型" style="width: 100%">
                <el-option label="标准库位" value="0" />
                <el-option label="托盘库位" value="1" />
                <el-option label="散装库位" value="2" />
                <el-option label="特殊库位" value="3" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="长度(CM)" prop="length">
              <el-input-number v-model="form.length" :min="0" :precision="1" :step="10" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="宽度(CM)" prop="width">
              <el-input-number v-model="form.width" :min="0" :precision="1" :step="10" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="高度(CM)" prop="height">
              <el-input-number v-model="form.height" :min="0" :precision="1" :step="10" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="载重(KG)" prop="loadCapacity">
              <el-input-number v-model="form.loadCapacity" :min="0" :precision="1" :step="100" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="库位状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择库位状态" style="width: 100%">
                <el-option label="空闲" value="0" disabled />
                <el-option label="占用" value="1" disabled />
                <el-option label="锁定" value="2" />
                <el-option label="禁用" value="3" />
              </el-select>
              <div class="status-tip" v-if="form.id">
                <i class="el-icon-info"></i> 
                <span v-if="form.status === '0' || form.status === '1'">
                  空闲和占用状态会根据关联容器自动判断
                </span>
                <span v-else-if="form.status === '2'">
                  锁定状态下无法添加新容器，现有关联保持不变
                </span>
                <span v-else-if="form.status === '3'" class="warning-tip">
                  <i class="el-icon-warning"></i> 禁用状态会清除所有关联容器，请谨慎操作！
                </span>
              </div>
              <div class="status-tip" v-else>
                <i class="el-icon-info"></i> 新增货位默认为空闲状态
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 库位详情对话框 -->
    <el-dialog title="库位详情" :visible.sync="viewDialog" width="700px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="所属仓库">{{ viewForm.warehouseName }}</el-descriptions-item>
        <el-descriptions-item label="所属库区">{{ viewForm.zoneName }}</el-descriptions-item>
        <el-descriptions-item label="库位编号">{{ viewForm.locationCode }}</el-descriptions-item>
        <el-descriptions-item label="库位类型">
          <el-tag v-if="viewForm.locationType == '0'" type="success">标准库位</el-tag>
          <el-tag v-else-if="viewForm.locationType == '1'" type="primary">托盘库位</el-tag>
          <el-tag v-else-if="viewForm.locationType == '2'" type="info">散装库位</el-tag>
          <el-tag v-else-if="viewForm.locationType == '3'" type="warning">特殊库位</el-tag>
          <el-tag v-else>{{ viewForm.locationType }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="库位状态">
          <el-tag v-if="viewForm.status == '0'" type="success">空闲</el-tag>
          <el-tag v-else-if="viewForm.status == '1'" type="primary">占用</el-tag>
          <el-tag v-else-if="viewForm.status == '2'" type="warning">锁定</el-tag>
          <el-tag v-else-if="viewForm.status == '3'" type="danger">禁用</el-tag>
          <el-tag v-else>{{ viewForm.status }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="尺寸(长×宽×高)">{{ viewForm.length || 0 }}×{{ viewForm.width || 0 }}×{{ viewForm.height || 0 }} CM</el-descriptions-item>
        <el-descriptions-item label="载重">{{ viewForm.loadCapacity || 0 }} KG</el-descriptions-item>
        <el-descriptions-item label="当前货物" v-if="viewForm.status == '1'">{{ viewForm.currentItem || '-' }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ viewForm.remark }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewDialog = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 批量生成货位对话框 -->
    <el-dialog title="批量生成货位" :visible.sync="batchGenerateOpen" width="700px" append-to-body>
      <el-form ref="batchForm" :model="batchForm" :rules="batchRules" label-width="100px">
        <el-divider content-position="left">货架选择</el-divider>
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属仓库" prop="warehouseId">
              <el-select v-model="batchForm.warehouseId" placeholder="请选择仓库" style="width: 100%" @change="handleBatchWarehouseChange">
                <el-option
                  v-for="item in warehouseOptions"
                  :key="item.id"
                  :label="item.warehouseName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属区域" prop="zoneId">
              <el-select v-model="batchForm.zoneId" placeholder="请选择区域" style="width: 100%" @change="handleBatchZoneChange">
                <el-option
                  v-for="item in batchZoneOptions"
                  :key="item.id"
                  :label="item.zoneName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="选择货架" prop="rackId">
              <el-select v-model="batchForm.rackId" placeholder="请选择货架" style="width: 100%" @change="handleBatchRackChange">
                <el-option
                  v-for="item in batchRackOptions"
                  :key="item.id"
                  :label="`${item.rackCode} - ${item.rackName}`"
                  :value="item.id"
                  :disabled="item.disabled"
                >
                  <span>{{ item.rackCode }} - {{ item.rackName }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">
                    {{ item.rowNum }}行 × {{ item.columnNum }}列 × {{ item.layerCount }}层
                  </span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="left">货架信息</el-divider>
        <el-row v-if="selectedRack">
          <el-col :span="8">
            <el-descriptions :column="1" border size="small">
              <el-descriptions-item label="货架编号">{{ selectedRack.rackCode }}</el-descriptions-item>
            </el-descriptions>
          </el-col>
          <el-col :span="8">
            <el-descriptions :column="1" border size="small">
              <el-descriptions-item label="货架名称">{{ selectedRack.rackName }}</el-descriptions-item>
            </el-descriptions>
          </el-col>
          <el-col :span="8">
            <el-descriptions :column="1" border size="small">
              <el-descriptions-item label="所属区域">{{ selectedRack.zoneName }}</el-descriptions-item>
            </el-descriptions>
          </el-col>
        </el-row>
        <el-row v-if="selectedRack" style="margin-top: 10px;">
          <el-col :span="8">
            <el-descriptions :column="1" border size="small">
              <el-descriptions-item label="行数">{{ selectedRack.rowNum }}</el-descriptions-item>
            </el-descriptions>
          </el-col>
          <el-col :span="8">
            <el-descriptions :column="1" border size="small">
              <el-descriptions-item label="列数">{{ selectedRack.columnNum }}</el-descriptions-item>
            </el-descriptions>
          </el-col>
          <el-col :span="8">
            <el-descriptions :column="1" border size="small">
              <el-descriptions-item label="层数">{{ selectedRack.layerCount }}</el-descriptions-item>
            </el-descriptions>
          </el-col>
        </el-row>
        <el-divider content-position="left">货位参数</el-divider>
        <el-row>
          <el-col :span="12">
            <el-form-item label="长度(CM)" prop="length">
              <el-input-number v-model="batchForm.length" :min="0" :precision="1" :step="10" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="宽度(CM)" prop="width">
              <el-input-number v-model="batchForm.width" :min="0" :precision="1" :step="10" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="高度(CM)" prop="height">
              <el-input-number v-model="batchForm.height" :min="0" :precision="1" :step="10" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最大承重(KG)" prop="maxWeight">
              <el-input-number v-model="batchForm.maxWeight" :min="0" :precision="1" :step="100" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="库位类型" prop="locationType">
              <el-select v-model="batchForm.locationType" placeholder="请选择库位类型" style="width: 100%">
                <el-option label="标准库位" value="0" />
                <el-option label="托盘库位" value="1" />
                <el-option label="散装库位" value="2" />
                <el-option label="特殊库位" value="3" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item v-if="selectedRack">
          <el-alert
            :title="`将生成 ${locationCount} 个货位，编号格式示例：${selectedRack.rackCode}-010101`"
            type="success"
            :closable="false">
          </el-alert>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitBatchForm" :disabled="!selectedRack">确 定</el-button>
        <el-button @click="cancelBatch">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listLocation, getLocation, delLocation, addLocation, updateLocation, batchGenerateLocation } from "@/api/warehouse/location"
import { listWarehouse } from "@/api/warehouse/warehouse"
import { listRyWarehouseZone } from "@/api/warehouse/zone"
import { listRyWarehouseRack } from "@/api/warehouse/rack"

export default {
  name: "WarehouseLocation",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 统计数据loading
      statsLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 库位信息表格数据
      locationList: [],
      // 仓库选项
      warehouseOptions: [],
      // 库区选项
      zoneOptions: [],
      // 表单库区选项
      formZoneOptions: [],
      // 批量库区选项
      batchZoneOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示批量生成弹出层
      batchGenerateOpen: false,
      // 详情表单
      viewForm: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        warehouseId: null,
        zoneId: null,
        locationCode: null,
        locationType: null,
        status: null
      },
      // 表单参数
      form: {},
      // 状态统计
      statusCounts: {
        '0': 0, // 空闲
        '1': 0, // 占用
        '2': 0, // 锁定
        '3': 0  // 禁用
      },
      // 批量表单参数
      batchForm: {
        warehouseId: null,
        zoneId: null,
        rackId: null,
        startNumber: 1,
        count: 10,
        prefix: "",
        locationType: "0",
        length: 100,
        width: 100,
        height: 150,
        loadCapacity: 500,
        volume: 1500000,
        status: "0"
      },
      // 表单校验
      rules: {
        warehouseId: [
          { required: true, message: "所属仓库不能为空", trigger: "change" }
        ],
        zoneId: [
          { required: true, message: "所属库区不能为空", trigger: "change" }
        ],
        rackId: [
          { required: true, message: "请选择货架", trigger: "change" }
        ]
      },
      // 批量表单校验
      batchRules: {
        warehouseId: [
          { required: true, message: "所属仓库不能为空", trigger: "change" }
        ],
        zoneId: [
          { required: true, message: "所属库区不能为空", trigger: "change" }
        ],
        rackId: [
          { required: true, message: "请选择货架", trigger: "change" }
        ]
      },
      // 批量生成相关
      selectedRack: null,
      locationCount: 0,
      batchRackOptions: [],
      // 状态统计
      locationStats: {
        total: 0,
        status_0: 0, // 空闲
        status_1: 0, // 占用
        status_2: 0, // 锁定
        status_3: 0  // 禁用
      }
    }
  },
  created() {
    this.getList()
    this.getWarehouseOptions()
    this.getLocationStatistics()
  },
  methods: {
    /** 查询库位信息列表 */
    getList() {
      this.loading = true
      listLocation(this.queryParams).then(response => {
        this.locationList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    
    /** 获取库位统计信息 */
    getLocationStatistics() {
      this.statsLoading = true
      
      // 克隆查询参数，移除分页相关参数，以获取全部统计数据
      const statsParams = { ...this.queryParams }
      delete statsParams.pageNum
      delete statsParams.pageSize
      
      // 获取总数
      listLocation(statsParams).then(response => {
        // 初始化统计数据
        this.statusCounts = {
          '0': 0,
          '1': 0,
          '2': 0,
          '3': 0
        }
        
        // 分别查询各状态的库位数量
        const statusPromises = []
        
        // 空闲库位
        const freeParams = { ...statsParams, status: "0" }
        statusPromises.push(
          listLocation(freeParams).then(res => {
            this.statusCounts['0'] = res.total || 0
          })
        )
        
        // 占用库位
        const occupiedParams = { ...statsParams, status: "1" }
        statusPromises.push(
          listLocation(occupiedParams).then(res => {
            this.statusCounts['1'] = res.total || 0
          })
        )
        
        // 锁定库位
        const lockedParams = { ...statsParams, status: "2" }
        statusPromises.push(
          listLocation(lockedParams).then(res => {
            this.statusCounts['2'] = res.total || 0
          })
        )
        
        // 禁用库位
        const disabledParams = { ...statsParams, status: "3" }
        statusPromises.push(
          listLocation(disabledParams).then(res => {
            this.statusCounts['3'] = res.total || 0
          })
        )
        
        Promise.all(statusPromises).finally(() => {
          this.statsLoading = false
        })
      })
    },
    
    /** 查询仓库选项 */
    getWarehouseOptions() {
      listWarehouse().then(response => {
        this.warehouseOptions = response.rows
      })
    },
    /** 查询库区选项 */
    getZoneOptions(warehouseId) {
      if (!warehouseId) {
        this.zoneOptions = []
        return
      }
      listRyWarehouseZone({ warehouseId: warehouseId }).then(response => {
        this.zoneOptions = response.rows
      })
    },
    /** 查询表单库区选项 */
    getFormZoneOptions(warehouseId) {
      if (!warehouseId) {
        this.formZoneOptions = []
        return
      }
      listRyWarehouseZone({ warehouseId: warehouseId }).then(response => {
        this.formZoneOptions = response.rows
      })
    },
    /** 查询批量库区选项 */
    getBatchZoneOptions(warehouseId) {
      if (!warehouseId) {
        this.batchZoneOptions = []
        return
      }
      listRyWarehouseZone({ warehouseId: warehouseId }).then(response => {
        this.batchZoneOptions = response.rows
      })
    },
    // 仓库选择框变化
    handleWarehouseChange(value) {
      this.getZoneOptions(value)
      this.queryParams.zoneId = null
    },
    // 表单仓库选择框变化
    handleFormWarehouseChange(value) {
      this.getFormZoneOptions(value)
      this.form.zoneId = null
      this.form.zoneCode = null
      this.form.zoneName = null
      
      // 更新仓库信息
      const selectedWarehouse = this.warehouseOptions.find(w => w.id === value)
      if (selectedWarehouse) {
        this.form.warehouseCode = selectedWarehouse.warehouseCode
        this.form.warehouseName = selectedWarehouse.warehouseName
      }
    },
    // 表单区域选择框变化
    handleFormZoneChange(value) {
      // 更新区域信息
      const selectedZone = this.formZoneOptions.find(z => z.id === value)
      if (selectedZone) {
        this.form.zoneCode = selectedZone.zoneCode
        this.form.zoneName = selectedZone.zoneName
      }
    },
    // 批量表单仓库选择框变化
    handleBatchWarehouseChange(value) {
      this.getBatchZoneOptions(value)
      this.batchForm.zoneId = null
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 批量取消按钮
    cancelBatch() {
      this.batchGenerateOpen = false
      this.resetBatch()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        warehouseId: null,
        zoneId: null,
        locationCode: null,
        locationType: "0",
        length: 100,
        width: 100,
        height: 100,
        loadCapacity: 1000,
        status: "0",
        remark: null
      }
      this.resetForm("form")
      this.formZoneOptions = []
    },
    // 批量表单重置
    resetBatch() {
      this.batchForm = {
        warehouseId: null,
        zoneId: null,
        rackId: null,
        startNumber: 1,
        count: 10,
        prefix: "",
        locationType: "0",
        length: 100,
        width: 100,
        height: 150,
        loadCapacity: 500,
        volume: 1500000,
        status: "0"
      }
      this.resetForm("batchForm")
      this.batchZoneOptions = []
      this.batchRackOptions = []
      this.selectedRack = null
      this.locationCount = 0
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
      this.getLocationStatistics()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加库位信息"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getLocation(id).then(response => {
        this.form = response.data
        // 加载库区选项
        this.getFormZoneOptions(this.form.warehouseId)
        // 如果有货位编号，解析它并预填充相关信息
        if(this.form.locationCode) {
          this.parseLocationCode(this.form.locationCode)
        }
        this.open = true
        this.title = "修改货位信息"
      })
    },
    /** 解析货位编号 */
    parseLocationCode(locationCode) {
      try {
        // 示例格式：WH01A001-R001-010205
        // 其中 WH01 是仓库编号，A001 是区域编号，R001 是货架编号，010205 是行列层位置
        if(locationCode && locationCode.includes('-')) {
          const parts = locationCode.split('-')
          
          // 获取仓库和区域信息
          if(parts[0] && parts[0].length > 2) {
            // 假设仓库编号是前4个字符，如WH01
            const warehouseCode = parts[0].substring(0, 4)
            
            // 使用仓库编号查询仓库信息
            const warehouse = this.warehouseOptions.find(w => w.warehouseCode === warehouseCode)
            if(warehouse) {
              console.log(`已找到对应的仓库: ${warehouse.warehouseName}`)
              // 设置表单中的仓库信息
              this.form.warehouseId = warehouse.id
              this.form.warehouseName = warehouse.warehouseName
              this.form.warehouseCode = warehouse.warehouseCode
              
              // 加载该仓库的所有区域
              this.getFormZoneOptions(warehouse.id)
              
              // 假设区域标识是从第4个字符到-之前，如A001
              const zonePrefix = parts[0].substring(4)
              
              // 延迟一点时间等待区域列表加载完成
              setTimeout(() => {
                // 在区域列表中查找匹配的区域
                const zone = this.formZoneOptions.find(z => z.zoneCode && z.zoneCode.includes(zonePrefix))
                if(zone) {
                  console.log(`已找到对应的区域: ${zone.zoneName}`)
                  // 设置表单中的区域信息
                  this.form.zoneId = zone.id
                  this.form.zoneName = zone.zoneName
                  this.form.zoneCode = zone.zoneCode
                }
              }, 1000)  // 增加延时，确保区域加载完成
            }
          }
        }
      } catch(error) {
        console.error('解析货位编号失败:', error)
      }
    },
    /** 查看详情按钮操作 */
    handleView(row) {
      this.viewForm = {}
      getLocation(row.id).then(response => {
        this.viewForm = response.data
        this.viewDialog = true
      })
    },
    /** 批量生成按钮操作 */
    handleBatchGenerate() {
      this.resetBatch()
      this.batchGenerateOpen = true
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 计算体积 (长 x 宽 x 高)
          if (this.form.length && this.form.width && this.form.height) {
            this.form.volume = this.form.length * this.form.width * this.form.height
          }
          
          // 确保库位类型字段存在且有效
          if (!this.form.locationType) {
            this.form.locationType = "0" // 默认为标准库位
          }
          
          if (this.form.id != null) {
            updateLocation(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
              this.getLocationStatistics()
            })
          } else {
            addLocation(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
              this.getLocationStatistics()
            })
          }
        }
      })
    },
    /** 批量提交按钮 */
    submitBatchForm() {
      this.$refs["batchForm"].validate(valid => {
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '正在生成货位...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          
          // 构建请求参数
          const params = {
            rackId: this.batchForm.rackId,
            rackCode: this.selectedRack ? this.selectedRack.rackCode : '',
            zoneId: this.batchForm.zoneId,
            warehouseId: this.batchForm.warehouseId,
            length: this.batchForm.length,
            width: this.batchForm.width,
            height: this.batchForm.height,
            maxWeight: this.batchForm.maxWeight,
            locationType: this.batchForm.locationType || "0"  // 确保库位类型有值
          }
          
          batchGenerateLocation(params).then(response => {
            loading.close()
            this.$modal.msgSuccess(response.msg || "批量生成成功")
            this.batchGenerateOpen = false
            this.getList()
            this.getLocationStatistics()
          }).catch(() => {
            loading.close()
          })
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除库位信息编号为"' + ids + '"的数据项？').then(() => {
        return delLocation(ids)
      }).then(() => {
        this.getList()
        this.getLocationStatistics()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('warehouse/location/export', {
        ...this.queryParams
      }, `location_${new Date().getTime()}.xlsx`)
    },
    // 格式化数字
    formatNumber(num, digitCount) {
      return String(num).padStart(Number(digitCount), '0')
    },
    // 批量生成相关
    handleBatchZoneChange(value) {
      this.getBatchRackOptions(value)
    },
    handleBatchRackChange(value) {
      this.selectedRack = this.batchRackOptions.find(item => item.id === value)
      if (this.selectedRack) {
        this.locationCount = this.selectedRack.rowNum * this.selectedRack.columnNum * this.selectedRack.layerCount
      }
    },
    getBatchRackOptions(zoneId) {
      if (!zoneId) {
        this.batchRackOptions = []
        return
      }
      // 根据区域ID查询货架列表
      listRyWarehouseRack(zoneId).then(response => {
        this.batchRackOptions = response.data
        // 重置选中的货架
        this.selectedRack = null
        this.batchForm.rackId = null
        this.locationCount = 0
      })
    }
  }
}
</script>

<style scoped>
.filter-container {
  margin-bottom: 10px;
}
.table-container {
  margin-bottom: 20px;
}
.el-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
.data-card {
  border-radius: 8px;
  overflow: hidden;
}
.data-card-header {
  background-color: #f5f7fa;
  padding: 10px 15px;
  font-weight: bold;
}
.data-card-body {
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.data-card-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}
.data-card-icon {
  font-size: 32px;
  color: #909399;
  opacity: 0.7;
}
.status-free .data-card-value,
.status-free .data-card-icon {
  color: #67c23a;
}
.status-occupied .data-card-value,
.status-occupied .data-card-icon {
  color: #409eff;
}
.status-locked .data-card-value,
.status-locked .data-card-icon {
  color: #e6a23c;
}
.status-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.4;
}
.warning-tip {
  color: #f56c6c;
}
</style>
