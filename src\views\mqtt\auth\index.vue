<template>
  <div class="mqtt-auth">
    <div class="page-header">
      <h2>MQTT 认证管理</h2>
      <p>管理 MQTT 客户端的认证凭据和权限</p>
    </div>
    
    <el-card>
      <div class="coming-soon">
        <i class="el-icon-key" style="font-size: 64px; color: #409EFF;"></i>
        <h3>功能开发中...</h3>
        <p>MQTT 认证管理功能正在开发中，敬请期待！</p>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'MqttAuth'
}
</script>

<style lang="scss" scoped>
.mqtt-auth {
  padding: 20px;
  
  .page-header {
    margin-bottom: 20px;
    
    h2 {
      margin: 0 0 8px 0;
      color: #303133;
    }
    
    p {
      margin: 0;
      color: #606266;
    }
  }
  
  .coming-soon {
    text-align: center;
    padding: 60px 20px;
    
    h3 {
      margin: 20px 0 10px 0;
      color: #303133;
    }
    
    p {
      color: #606266;
    }
  }
}
</style>
