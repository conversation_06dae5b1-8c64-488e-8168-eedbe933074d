import request from '@/utils/request'

// 获取SIP服务器状态
export function getSipServerStatus() {
  return request({
    url: '/sip/monitoring/server-status',
    method: 'get'
  })
}

// 获取SIP监控指标
export function getSipMetrics() {
  return request({
    url: '/sip/monitoring/metrics',
    method: 'get'
  })
}

// 获取SIP设备统计
export function getSipDeviceStats() {
  return request({
    url: '/sip/monitoring/device-stats',
    method: 'get'
  })
}

// 获取SIP会话统计
export function getSipSessionStats() {
  return request({
    url: '/sip/monitoring/session-stats',
    method: 'get'
  })
}

// 获取ZLMediaKit性能指标
export function getZlmMetrics() {
  return request({
    url: '/video/performance/metrics',
    method: 'get'
  })
}

// 获取ZLMediaKit服务器状态
export function getZlmServerStatus() {
  return request({
    url: '/video/performance/server-status',
    method: 'get'
  })
}

// 获取系统资源使用情况
export function getSystemResources() {
  return request({
    url: '/sip/monitoring/system-resources',
    method: 'get'
  })
}

// 获取性能趋势数据
export function getPerformanceTrend(params) {
  return request({
    url: '/sip/monitoring/performance-trend',
    method: 'get',
    params: params
  })
}

// 获取告警信息
export function getAlerts() {
  return request({
    url: '/sip/monitoring/alerts',
    method: 'get'
  })
}

// 清除告警
export function clearAlert(alertId) {
  return request({
    url: `/sip/monitoring/alerts/${alertId}/clear`,
    method: 'post'
  })
}

// 获取性能报告
export function getPerformanceReport(params) {
  return request({
    url: '/sip/monitoring/performance-report',
    method: 'get',
    params: params
  })
}

// 导出性能数据
export function exportPerformanceData(params) {
  return request({
    url: '/sip/monitoring/export',
    method: 'get',
    params: params,
    responseType: 'blob'
  })
}
