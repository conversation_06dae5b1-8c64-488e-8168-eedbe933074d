<template>
  <div class="app-container">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <div class="page-title">
        <h2>EMQX消息管理</h2>
        <p>实时查看、发送和管理EMQX消息队列中的消息</p>
      </div>
      <div class="page-actions">
        <el-button type="primary" icon="el-icon-refresh" @click="refreshData">刷新数据</el-button>
        <el-button type="success" icon="el-icon-plus" @click="showSendDialog">发送消息</el-button>
        <el-button type="info" icon="el-icon-download" @click="exportMessages">导出消息</el-button>
        <el-button type="warning" icon="el-icon-delete" @click="clearMessages">清理消息</el-button>
      </div>
    </div>

    <!-- 搜索条件 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
        <el-form-item label="主题" prop="topic">
          <el-input
            v-model="queryParams.topic"
            placeholder="请输入主题名称"
            clearable
            style="width: 200px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="客户端ID" prop="clientId">
          <el-input
            v-model="queryParams.clientId"
            placeholder="请输入客户端ID"
            clearable
            style="width: 200px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="消息类型" prop="messageType">
          <el-select v-model="queryParams.messageType" placeholder="请选择消息类型" clearable style="width: 150px">
            <el-option label="发布" value="publish" />
            <el-option label="订阅" value="subscribe" />
            <el-option label="取消订阅" value="unsubscribe" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围" prop="dateRange">
          <el-date-picker
            v-model="queryParams.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 300px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 消息列表 -->
    <el-card class="table-card" shadow="never">
      <el-table
        v-loading="loading"
        :data="messageList"
        row-key="id"
        :default-expand-all="false"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column label="消息ID" prop="messageId" width="120" show-overflow-tooltip />
        <el-table-column label="主题" prop="topic" width="200" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tag size="mini" type="info">{{ scope.row.topic }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="客户端ID" prop="clientId" width="150" show-overflow-tooltip />
        <el-table-column label="消息类型" prop="messageType" width="100" align="center">
          <template slot-scope="scope">
            <el-tag
              size="mini"
              :type="scope.row.messageType === 'publish' ? 'success' : 
                     scope.row.messageType === 'subscribe' ? 'primary' : 'warning'"
            >
              {{ getMessageTypeText(scope.row.messageType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="QoS" prop="qos" width="80" align="center">
          <template slot-scope="scope">
            <el-tag size="mini" :type="getQosType(scope.row.qos)">{{ scope.row.qos }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="消息大小" prop="payloadSize" width="100" align="center">
          <template slot-scope="scope">
            {{ formatBytes(scope.row.payloadSize) }}
          </template>
        </el-table-column>
        <el-table-column label="时间戳" prop="timestamp" width="160" align="center">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.timestamp) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" width="100" align="center">
          <template slot-scope="scope">
            <el-tag
              size="mini"
              :type="scope.row.status === 'delivered' ? 'success' : 
                     scope.row.status === 'pending' ? 'warning' : 'danger'"
            >
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="viewMessage(scope.row)"
              v-hasPermi="['emqx:message:query']"
            >查看</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-copy-document"
              @click="resendMessage(scope.row)"
              v-hasPermi="['emqx:message:send']"
            >重发</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="deleteMessage(scope.row)"
              v-hasPermi="['emqx:message:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 发送消息对话框 -->
    <el-dialog title="发送消息" :visible.sync="sendDialog.visible" width="600px" append-to-body>
      <el-form ref="sendForm" :model="sendForm" :rules="sendRules" label-width="100px">
        <el-form-item label="主题" prop="topic">
          <el-input v-model="sendForm.topic" placeholder="请输入主题名称" />
        </el-form-item>
        <el-form-item label="QoS等级" prop="qos">
          <el-select v-model="sendForm.qos" placeholder="请选择QoS等级">
            <el-option label="0 - 最多一次" :value="0" />
            <el-option label="1 - 至少一次" :value="1" />
            <el-option label="2 - 恰好一次" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="保留消息" prop="retain">
          <el-switch v-model="sendForm.retain" />
        </el-form-item>
        <el-form-item label="消息内容" prop="payload">
          <el-input
            v-model="sendForm.payload"
            type="textarea"
            :rows="6"
            placeholder="请输入消息内容（支持JSON格式）"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="sendDialog.visible = false">取 消</el-button>
        <el-button type="primary" @click="submitSendMessage">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 查看消息对话框 -->
    <el-dialog title="消息详情" :visible.sync="viewDialog.visible" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="消息ID">{{ viewDialog.data.messageId }}</el-descriptions-item>
        <el-descriptions-item label="主题">{{ viewDialog.data.topic }}</el-descriptions-item>
        <el-descriptions-item label="客户端ID">{{ viewDialog.data.clientId }}</el-descriptions-item>
        <el-descriptions-item label="消息类型">{{ getMessageTypeText(viewDialog.data.messageType) }}</el-descriptions-item>
        <el-descriptions-item label="QoS等级">{{ viewDialog.data.qos }}</el-descriptions-item>
        <el-descriptions-item label="保留消息">{{ viewDialog.data.retain ? '是' : '否' }}</el-descriptions-item>
        <el-descriptions-item label="消息大小">{{ formatBytes(viewDialog.data.payloadSize) }}</el-descriptions-item>
        <el-descriptions-item label="时间戳">{{ parseTime(viewDialog.data.timestamp) }}</el-descriptions-item>
        <el-descriptions-item label="状态" :span="2">{{ getStatusText(viewDialog.data.status) }}</el-descriptions-item>
      </el-descriptions>
      
      <div style="margin-top: 20px;">
        <h4>消息内容：</h4>
        <el-input
          v-model="viewDialog.data.payload"
          type="textarea"
          :rows="8"
          readonly
          style="margin-top: 10px;"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getMessageList, sendMessage, deleteMessage, exportMessageData } from "@/api/emqx/message"

export default {
  name: "EmqxMessages",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 消息表格数据
      messageList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        topic: null,
        clientId: null,
        messageType: null,
        dateRange: null
      },
      // 发送消息对话框
      sendDialog: {
        visible: false
      },
      // 发送消息表单
      sendForm: {
        topic: '',
        qos: 0,
        retain: false,
        payload: ''
      },
      // 发送消息表单校验
      sendRules: {
        topic: [
          { required: true, message: "主题不能为空", trigger: "blur" }
        ],
        payload: [
          { required: true, message: "消息内容不能为空", trigger: "blur" }
        ]
      },
      // 查看消息对话框
      viewDialog: {
        visible: false,
        data: {}
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询消息列表 */
    getList() {
      this.loading = true;
      getMessageList(this.queryParams).then(response => {
        this.messageList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 刷新数据 */
    refreshData() {
      this.getList();
      this.$modal.msgSuccess("数据刷新成功");
    },
    /** 显示发送消息对话框 */
    showSendDialog() {
      this.reset();
      this.sendDialog.visible = true;
    },
    /** 表单重置 */
    reset() {
      this.sendForm = {
        topic: '',
        qos: 0,
        retain: false,
        payload: ''
      };
      this.resetForm("sendForm");
    },
    /** 提交发送消息 */
    submitSendMessage() {
      this.$refs["sendForm"].validate(valid => {
        if (valid) {
          sendMessage(this.sendForm).then(response => {
            this.$modal.msgSuccess("消息发送成功");
            this.sendDialog.visible = false;
            this.getList();
          });
        }
      });
    },
    /** 查看消息详情 */
    viewMessage(row) {
      this.viewDialog.data = { ...row };
      this.viewDialog.visible = true;
    },
    /** 重发消息 */
    resendMessage(row) {
      this.sendForm = {
        topic: row.topic,
        qos: row.qos,
        retain: row.retain,
        payload: row.payload
      };
      this.sendDialog.visible = true;
    },
    /** 删除消息 */
    deleteMessage(row) {
      const messageIds = row.id || this.ids;
      this.$modal.confirm('是否确认删除消息编号为"' + messageIds + '"的数据项？').then(function() {
        return deleteMessage(messageIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出消息 */
    exportMessages() {
      this.download('emqx/message/export', {
        ...this.queryParams
      }, `messages_${new Date().getTime()}.xlsx`)
    },
    /** 清理消息 */
    clearMessages() {
      this.$modal.confirm('是否确认清理所有历史消息？此操作不可恢复！').then(() => {
        // 调用清理API
        this.$modal.msgSuccess("消息清理成功");
        this.getList();
      }).catch(() => {});
    },
    /** 获取消息类型文本 */
    getMessageTypeText(type) {
      const typeMap = {
        'publish': '发布',
        'subscribe': '订阅',
        'unsubscribe': '取消订阅'
      };
      return typeMap[type] || type;
    },
    /** 获取QoS类型 */
    getQosType(qos) {
      return qos === 0 ? 'info' : qos === 1 ? 'warning' : 'danger';
    },
    /** 获取状态文本 */
    getStatusText(status) {
      const statusMap = {
        'delivered': '已送达',
        'pending': '待处理',
        'failed': '失败'
      };
      return statusMap[status] || status;
    },
    /** 格式化字节大小 */
    formatBytes(bytes) {
      if (bytes === 0) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
  }
};
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title h2 {
  margin: 0;
  color: #303133;
}

.page-title p {
  margin: 5px 0 0 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}
</style>
