<template>
  <div class="error-boundary">
    <!-- 正常内容 -->
    <slot v-if="!hasError"></slot>
    
    <!-- 错误状态 -->
    <div class="error-container" v-else>
      <!-- 简单错误显示 -->
      <div class="error-simple" v-if="errorType === 'simple'">
        <div class="error-icon">
          <i class="el-icon-warning-outline"></i>
        </div>
        <div class="error-message">{{ errorMessage }}</div>
        <div class="error-actions">
          <el-button type="primary" @click="handleRetry">重试</el-button>
          <el-button @click="handleReload">刷新页面</el-button>
        </div>
      </div>
      
      <!-- 详细错误显示 -->
      <div class="error-detailed" v-else-if="errorType === 'detailed'">
        <div class="error-header">
          <div class="error-icon-large">
            <i class="el-icon-circle-close"></i>
          </div>
          <div class="error-title">页面出现错误</div>
          <div class="error-subtitle">{{ errorMessage }}</div>
        </div>
        
        <div class="error-content">
          <el-collapse v-model="activeCollapse">
            <el-collapse-item title="错误详情" name="details">
              <div class="error-details">
                <div class="detail-item">
                  <span class="detail-label">错误类型:</span>
                  <span class="detail-value">{{ errorInfo.type }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">发生时间:</span>
                  <span class="detail-value">{{ errorInfo.timestamp }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">页面路径:</span>
                  <span class="detail-value">{{ errorInfo.path }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">用户代理:</span>
                  <span class="detail-value">{{ errorInfo.userAgent }}</span>
                </div>
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="错误堆栈" name="stack" v-if="errorInfo.stack">
              <pre class="error-stack">{{ errorInfo.stack }}</pre>
            </el-collapse-item>
            
            <el-collapse-item title="组件信息" name="component" v-if="errorInfo.componentStack">
              <pre class="component-stack">{{ errorInfo.componentStack }}</pre>
            </el-collapse-item>
          </el-collapse>
        </div>
        
        <div class="error-actions">
          <el-button type="primary" @click="handleRetry">重试</el-button>
          <el-button @click="handleReload">刷新页面</el-button>
          <el-button type="info" @click="handleReport">报告错误</el-button>
          <el-button @click="handleGoHome">返回首页</el-button>
        </div>
      </div>
      
      <!-- 网络错误显示 -->
      <div class="error-network" v-else-if="errorType === 'network'">
        <div class="network-icon">
          <i class="el-icon-connection"></i>
        </div>
        <div class="network-title">网络连接异常</div>
        <div class="network-message">{{ errorMessage }}</div>
        <div class="network-tips">
          <p>请检查您的网络连接，然后重试</p>
          <ul>
            <li>确认网络连接正常</li>
            <li>检查防火墙设置</li>
            <li>尝试刷新页面</li>
          </ul>
        </div>
        <div class="error-actions">
          <el-button type="primary" @click="handleRetry">重试</el-button>
          <el-button @click="handleReload">刷新页面</el-button>
        </div>
      </div>
      
      <!-- 权限错误显示 -->
      <div class="error-permission" v-else-if="errorType === 'permission'">
        <div class="permission-icon">
          <i class="el-icon-lock"></i>
        </div>
        <div class="permission-title">访问权限不足</div>
        <div class="permission-message">{{ errorMessage }}</div>
        <div class="permission-tips">
          <p>您没有访问此页面的权限，请联系管理员</p>
        </div>
        <div class="error-actions">
          <el-button type="primary" @click="handleGoHome">返回首页</el-button>
          <el-button @click="handleLogin">重新登录</el-button>
        </div>
      </div>
      
      <!-- 404错误显示 -->
      <div class="error-notfound" v-else-if="errorType === 'notfound'">
        <div class="notfound-icon">
          <i class="el-icon-document-delete"></i>
        </div>
        <div class="notfound-title">页面不存在</div>
        <div class="notfound-message">{{ errorMessage }}</div>
        <div class="notfound-tips">
          <p>您访问的页面不存在，可能已被删除或移动</p>
        </div>
        <div class="error-actions">
          <el-button type="primary" @click="handleGoHome">返回首页</el-button>
          <el-button @click="handleGoBack">返回上页</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ErrorBoundary',
  
  props: {
    // 错误类型
    errorType: {
      type: String,
      default: 'simple',
      validator: value => ['simple', 'detailed', 'network', 'permission', 'notfound'].includes(value)
    },
    // 是否自动重试
    autoRetry: {
      type: Boolean,
      default: false
    },
    // 自动重试次数
    maxRetries: {
      type: Number,
      default: 3
    },
    // 重试间隔(毫秒)
    retryInterval: {
      type: Number,
      default: 1000
    },
    // 是否显示错误报告按钮
    showReport: {
      type: Boolean,
      default: true
    }
  },
  
  data() {
    return {
      hasError: false,
      errorMessage: '',
      errorInfo: {},
      retryCount: 0,
      activeCollapse: []
    }
  },
  
  errorCaptured(err, vm, info) {
    this.captureError(err, vm, info)
    return false // 阻止错误继续传播
  },
  
  methods: {
    /** 捕获错误 */
    captureError(error, vm, info) {
      this.hasError = true
      this.errorMessage = error.message || '发生未知错误'
      
      // 收集错误信息
      this.errorInfo = {
        type: error.name || 'Error',
        message: error.message,
        stack: error.stack,
        componentStack: info,
        timestamp: new Date().toLocaleString(),
        path: this.$route ? this.$route.fullPath : window.location.pathname,
        userAgent: navigator.userAgent,
        url: window.location.href
      }
      
      // 上报错误
      this.reportError(this.errorInfo)
      
      // 自动重试
      if (this.autoRetry && this.retryCount < this.maxRetries) {
        setTimeout(() => {
          this.handleRetry()
        }, this.retryInterval)
      }
      
      this.$emit('error', this.errorInfo)
    },
    
    /** 设置错误状态 */
    setError(message, type = 'simple', info = {}) {
      this.hasError = true
      this.errorMessage = message
      this.errorInfo = {
        type: type,
        message: message,
        timestamp: new Date().toLocaleString(),
        path: this.$route ? this.$route.fullPath : window.location.pathname,
        userAgent: navigator.userAgent,
        url: window.location.href,
        ...info
      }
      
      this.reportError(this.errorInfo)
      this.$emit('error', this.errorInfo)
    },
    
    /** 清除错误状态 */
    clearError() {
      this.hasError = false
      this.errorMessage = ''
      this.errorInfo = {}
      this.retryCount = 0
      this.$emit('clear')
    },
    
    /** 重试 */
    handleRetry() {
      this.retryCount++
      this.clearError()
      this.$emit('retry', this.retryCount)
      
      // 重新渲染组件
      this.$nextTick(() => {
        this.$forceUpdate()
      })
    },
    
    /** 刷新页面 */
    handleReload() {
      window.location.reload()
    },
    
    /** 返回首页 */
    handleGoHome() {
      this.$router.push('/')
    },
    
    /** 返回上页 */
    handleGoBack() {
      this.$router.go(-1)
    },
    
    /** 重新登录 */
    handleLogin() {
      this.$router.push('/login')
    },
    
    /** 报告错误 */
    handleReport() {
      this.reportError(this.errorInfo, true)
      this.$message.success('错误报告已发送')
    },
    
    /** 上报错误信息 */
    reportError(errorInfo, manual = false) {
      try {
        // 发送错误信息到服务器
        const reportData = {
          ...errorInfo,
          manual: manual,
          userId: this.$store.getters.userId,
          sessionId: this.getSessionId()
        }
        
        // 这里可以调用错误上报API
        console.error('Error Report:', reportData)
        
        // 也可以发送到第三方错误监控服务
        if (window.Sentry) {
          window.Sentry.captureException(new Error(errorInfo.message), {
            extra: reportData
          })
        }
        
      } catch (err) {
        console.error('Failed to report error:', err)
      }
    },
    
    /** 获取会话ID */
    getSessionId() {
      let sessionId = sessionStorage.getItem('sessionId')
      if (!sessionId) {
        sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
        sessionStorage.setItem('sessionId', sessionId)
      }
      return sessionId
    }
  }
}
</script>

<style scoped>
.error-boundary {
  width: 100%;
  height: 100%;
}

.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 40px 20px;
}

/* 简单错误样式 */
.error-simple {
  text-align: center;
  max-width: 400px;
}

.error-icon {
  font-size: 64px;
  color: #F56C6C;
  margin-bottom: 20px;
}

.error-message {
  font-size: 16px;
  color: #303133;
  margin-bottom: 30px;
  line-height: 1.5;
}

/* 详细错误样式 */
.error-detailed {
  max-width: 800px;
  width: 100%;
}

.error-header {
  text-align: center;
  margin-bottom: 30px;
}

.error-icon-large {
  font-size: 80px;
  color: #F56C6C;
  margin-bottom: 20px;
}

.error-title {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
}

.error-subtitle {
  font-size: 16px;
  color: #606266;
}

.error-content {
  margin-bottom: 30px;
}

.error-details {
  padding: 20px;
  background: #f5f7fa;
  border-radius: 4px;
}

.detail-item {
  display: flex;
  margin-bottom: 10px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  width: 100px;
  color: #909399;
  font-weight: bold;
}

.detail-value {
  flex: 1;
  color: #303133;
  word-break: break-all;
}

.error-stack,
.component-stack {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.5;
  color: #606266;
  overflow-x: auto;
  white-space: pre-wrap;
}

/* 网络错误样式 */
.error-network {
  text-align: center;
  max-width: 500px;
}

.network-icon {
  font-size: 64px;
  color: #E6A23C;
  margin-bottom: 20px;
}

.network-title {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
}

.network-message {
  font-size: 16px;
  color: #606266;
  margin-bottom: 20px;
}

.network-tips {
  text-align: left;
  background: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 30px;
}

.network-tips p {
  margin-bottom: 10px;
  color: #303133;
  font-weight: bold;
}

.network-tips ul {
  margin: 0;
  padding-left: 20px;
  color: #606266;
}

.network-tips li {
  margin-bottom: 5px;
}

/* 权限错误样式 */
.error-permission {
  text-align: center;
  max-width: 400px;
}

.permission-icon {
  font-size: 64px;
  color: #909399;
  margin-bottom: 20px;
}

.permission-title {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
}

.permission-message {
  font-size: 16px;
  color: #606266;
  margin-bottom: 20px;
}

.permission-tips {
  background: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 30px;
}

.permission-tips p {
  margin: 0;
  color: #606266;
  line-height: 1.5;
}

/* 404错误样式 */
.error-notfound {
  text-align: center;
  max-width: 400px;
}

.notfound-icon {
  font-size: 64px;
  color: #C0C4CC;
  margin-bottom: 20px;
}

.notfound-title {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
}

.notfound-message {
  font-size: 16px;
  color: #606266;
  margin-bottom: 20px;
}

.notfound-tips {
  background: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 30px;
}

.notfound-tips p {
  margin: 0;
  color: #606266;
  line-height: 1.5;
}

/* 操作按钮 */
.error-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-container {
    padding: 20px 15px;
    min-height: 300px;
  }
  
  .error-detailed {
    max-width: 100%;
  }
  
  .error-icon,
  .error-icon-large,
  .network-icon,
  .permission-icon,
  .notfound-icon {
    font-size: 48px;
  }
  
  .error-title,
  .network-title,
  .permission-title,
  .notfound-title {
    font-size: 18px;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .error-actions .el-button {
    width: 200px;
  }
}
</style>
