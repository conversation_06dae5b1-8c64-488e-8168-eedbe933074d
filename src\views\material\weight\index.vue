<template>
  <div class="app-container">
    <!-- 搜索区域 - 优化布局 -->
    <el-card class="search-card" shadow="hover" v-show="showSearch">
      <el-form :model="queryParams" ref="queryForm" size="small" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="物料编码" prop="materialCode">
              <el-input
                v-model="queryParams.materialCode"
                placeholder="请输入物料编码"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="物料名称" prop="materialName">
              <el-input
                v-model="queryParams.materialName"
                placeholder="请输入物料名称"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="重量单位" prop="weightUnit">
              <el-select
                v-model="queryParams.weightUnit"
                placeholder="请选择重量单位"
                clearable
                style="width: 100%"
              >
                <el-option label="千克(kg)" value="kg" />
                <el-option label="克(g)" value="g" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="是否重量控制" prop="isWeightControl">
              <el-select
                v-model="queryParams.isWeightControl"
                placeholder="请选择是否重量控制"
                clearable
                style="width: 100%"
              >
                <el-option label="是" value="1" />
                <el-option label="否" value="0" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" style="text-align: right">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['material:weight:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['material:weight:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['material:weight:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-s-tools"
          size="mini"
          @click="handleCalibration"
          v-hasPermi="['material:weight:edit']"
        >校准</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['material:weight:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 - 优化显示字段 -->
    <el-table v-loading="loading" :data="weightList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="物料编码" align="center" prop="materialCode" />
      <el-table-column label="物料名称" align="center" prop="materialName" />
      <el-table-column label="单位重量" align="center" prop="unitWeight" />
      <el-table-column label="重量单位" align="center" prop="weightUnit">
        <template slot-scope="scope">
          {{ scope.row.weightUnit === 'kg' ? '千克(kg)' : scope.row.weightUnit === 'g' ? '克(g)' : scope.row.weightUnit }}
        </template>
      </el-table-column>
      <el-table-column label="毛重" align="center" prop="grossWeight" />
      <el-table-column label="净重" align="center" prop="netWeight" />
      <el-table-column label="标准重量" align="center" prop="standardWeight" />
      <el-table-column label="重量公差" align="center" prop="weightTolerance">
        <template slot-scope="scope">
          {{ scope.row.weightTolerance }}%
        </template>
      </el-table-column>
      <el-table-column label="重量控制" align="center" prop="isWeightControl">
        <template slot-scope="scope">
          <el-tag :type="scope.row.isWeightControl == 1 ? 'success' : 'info'">
            {{ scope.row.isWeightControl == 1 ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="动态监测重量" align="center" prop="dynamicWeight" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.dynamicWeight || '-' }}</span>
          <el-tooltip v-if="scope.row.lastSyncTime" class="item" effect="dark" :content="'最后同步: ' + parseTime(scope.row.lastSyncTime)" placement="top">
            <i class="el-icon-time" style="margin-left: 5px; color: #909399;"></i>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="校准时间" align="center" width="180">
        <template slot-scope="scope">
          <div>上次: {{ parseTime(scope.row.lastCalibrationTime, '{y}-{m}-{d}') || '-' }}</div>
          <div>下次: {{ parseTime(scope.row.nextCalibrationTime, '{y}-{m}-{d}') || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleViewDetail(scope.row)"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['material:weight:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['material:weight:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改物料重量信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="750px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="物料编码" prop="materialCode">
              <el-input v-model="form.materialCode" placeholder="请输入物料编码" @blur="handleMaterialCodeBlur" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物料名称" prop="materialName">
              <el-input v-model="form.materialName" placeholder="请输入物料名称" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="单位重量" prop="unitWeight">
              <el-input v-model="form.unitWeight" placeholder="请输入单位重量" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="重量单位" prop="weightUnit">
              <el-select v-model="form.weightUnit" placeholder="请选择重量单位" style="width: 100%">
                <el-option label="千克(kg)" value="kg" />
                <el-option label="克(g)" value="g" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="毛重" prop="grossWeight">
              <el-input v-model="form.grossWeight" placeholder="请输入毛重" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="净重" prop="netWeight">
              <el-input v-model="form.netWeight" placeholder="请输入净重" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="容器重量" prop="containerWeight">
              <el-input v-model="form.containerWeight" placeholder="请输入容器重量" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="包装重量" prop="packageWeight">
              <el-input v-model="form.packageWeight" placeholder="请输入包装重量" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="标准重量" prop="standardWeight">
              <el-input v-model="form.standardWeight" placeholder="请输入标准重量" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="重量公差(%)" prop="weightTolerance">
              <el-input v-model="form.weightTolerance" placeholder="请输入重量公差(%)" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否重量控制" prop="isWeightControl">
              <el-radio-group v-model="form.isWeightControl">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="测量方法" prop="measureMethod">
              <el-input v-model="form.measureMethod" placeholder="请输入测量方法" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="上次校准时间" prop="lastCalibrationTime">
              <el-date-picker clearable
                v-model="form.lastCalibrationTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择上次校准时间"
                style="width: 100%;">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="下次校准时间" prop="nextCalibrationTime">
              <el-date-picker clearable
                v-model="form.nextCalibrationTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择下次校准时间"
                style="width: 100%;">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 物料重量详情弹窗 -->
    <el-dialog title="物料重量详情" :visible.sync="detailOpen" width="800px" append-to-body>
      <el-descriptions class="margin-top" title="基本信息" :column="3" border>
        <el-descriptions-item label="物料编码">{{ detailInfo.materialCode }}</el-descriptions-item>
        <el-descriptions-item label="物料名称">{{ detailInfo.materialName }}</el-descriptions-item>
        <el-descriptions-item label="物料分类">{{ detailInfo.categoryName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="规格型号">{{ detailInfo.specification || '-' }}</el-descriptions-item>
        <el-descriptions-item label="物料单位">{{ detailInfo.unit || '-' }}</el-descriptions-item>
        <el-descriptions-item label="物料状态">
          <el-tag :type="detailInfo.status === '0' ? 'success' : 'danger'">
            {{ detailInfo.status === '0' ? '正常' : '停用' }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>

      <el-descriptions class="margin-top" title="重量信息" :column="3" border style="margin-top: 20px">
        <el-descriptions-item label="单位重量">{{ detailInfo.unitWeight }}</el-descriptions-item>
        <el-descriptions-item label="重量单位">
          {{ detailInfo.weightUnit === 'kg' ? '千克(kg)' : detailInfo.weightUnit === 'g' ? '克(g)' : detailInfo.weightUnit }}
        </el-descriptions-item>
        <el-descriptions-item label="毛重">{{ detailInfo.grossWeight }}</el-descriptions-item>
        <el-descriptions-item label="净重">{{ detailInfo.netWeight }}</el-descriptions-item>
        <el-descriptions-item label="容器重量">{{ detailInfo.containerWeight }}</el-descriptions-item>
        <el-descriptions-item label="包装重量">{{ detailInfo.packageWeight }}</el-descriptions-item>
        <el-descriptions-item label="标准重量">{{ detailInfo.standardWeight }}</el-descriptions-item>
        <el-descriptions-item label="重量公差">{{ detailInfo.weightTolerance }}%</el-descriptions-item>
        <el-descriptions-item label="重量控制">{{ detailInfo.isWeightControl === 1 ? '是' : '否' }}</el-descriptions-item>
        <el-descriptions-item label="测量方法">{{ detailInfo.measureMethod || '-' }}</el-descriptions-item>
        <el-descriptions-item label="上次校准时间">{{ parseTime(detailInfo.lastCalibrationTime, '{y}-{m}-{d}') || '-' }}</el-descriptions-item>
        <el-descriptions-item label="下次校准时间">{{ parseTime(detailInfo.nextCalibrationTime, '{y}-{m}-{d}') || '-' }}</el-descriptions-item>
      </el-descriptions>

      <el-descriptions class="margin-top" title="动态监测" :column="2" border style="margin-top: 20px">
        <el-descriptions-item label="当前动态重量">
          <span style="font-size: 16px; font-weight: bold; color: #409EFF">{{ detailInfo.dynamicWeight || '-' }}</span>
          <el-tag v-if="detailInfo.weightStatus" :type="detailInfo.weightStatus === 'normal' ? 'success' : 'danger'" style="margin-left: 10px">
            {{ detailInfo.weightStatus === 'normal' ? '正常' : '异常' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="最后同步时间">{{ parseTime(detailInfo.lastSyncTime) || '-' }}</el-descriptions-item>
        <el-descriptions-item label="数据来源">{{ detailInfo.deviceName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="备注">{{ detailInfo.remark || '-' }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 校准弹窗 -->
    <el-dialog title="物料重量校准" :visible.sync="calibrationOpen" width="600px" append-to-body>
      <el-form ref="calibrationForm" :model="calibrationForm" label-width="120px">
        <el-form-item label="物料编码">
          <el-input v-model="calibrationForm.materialCode" disabled />
        </el-form-item>
        <el-form-item label="物料名称">
          <el-input v-model="calibrationForm.materialName" disabled />
        </el-form-item>
        <el-form-item label="当前标准重量">
          <el-input v-model="calibrationForm.standardWeight" disabled>
            <template slot="append">{{ calibrationForm.weightUnit }}</template>
          </el-input>
        </el-form-item>
        <el-form-item label="新标准重量" prop="newStandardWeight">
          <el-input v-model="calibrationForm.newStandardWeight" placeholder="请输入新的标准重量">
            <template slot="append">{{ calibrationForm.weightUnit }}</template>
          </el-input>
        </el-form-item>
        <el-form-item label="校准日期" prop="calibrationDate">
          <el-date-picker
            v-model="calibrationForm.calibrationDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择校准日期"
            style="width: 100%;">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="下次校准日期" prop="nextCalibrationDate">
          <el-date-picker
            v-model="calibrationForm.nextCalibrationDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择下次校准日期"
            style="width: 100%;">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="校准说明" prop="calibrationNote">
          <el-input v-model="calibrationForm.calibrationNote" type="textarea" placeholder="请输入校准说明"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitCalibration">确 定</el-button>
        <el-button @click="cancelCalibration">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listWeight, getWeight, delWeight, addWeight, updateWeight } from "@/api/material/weight"
import { getMaterialByCode, getMaterialDetail } from "@/api/material/material"

export default {
  name: "Weight",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 物料重量信息表格数据
      weightList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 详情弹窗
      detailOpen: false,
      // 详情信息
      detailInfo: {},
      // 校准弹窗
      calibrationOpen: false,
      // 校准表单
      calibrationForm: {
        materialCode: '',
        materialName: '',
        standardWeight: '',
        weightUnit: '',
        newStandardWeight: '',
        calibrationDate: '',
        nextCalibrationDate: '',
        calibrationNote: ''
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        materialId: null,
        materialCode: null,
        materialName: null,
        unitWeight: null,
        weightUnit: null,
        grossWeight: null,
        netWeight: null,
        containerWeight: null,
        packageWeight: null,
        standardWeight: null,
        weightTolerance: null,
        isWeightControl: null,
        measureMethod: null,
        lastCalibrationTime: null,
        nextCalibrationTime: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        materialCode: [
          { required: true, message: "物料编码不能为空", trigger: "blur" }
        ],
        materialName: [
          { required: true, message: "物料名称不能为空", trigger: "blur" }
        ],
        unitWeight: [
          { required: true, message: "单位重量不能为空", trigger: "blur" }
        ],
        weightUnit: [
          { required: true, message: "重量单位不能为空", trigger: "change" }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询物料重量信息列表 */
    getList() {
      this.loading = true
      listWeight(this.queryParams).then(response => {
        this.weightList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        unitWeight: null,
        weightUnit: null,
        grossWeight: null,
        netWeight: null,
        containerWeight: null,
        packageWeight: null,
        standardWeight: null,
        weightTolerance: null,
        isWeightControl: null,
        measureMethod: null,
        lastCalibrationTime: null,
        nextCalibrationTime: null,
        status: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加物料重量信息"
      // 设置默认值
      this.form.isWeightControl = 0
      this.form.weightUnit = 'kg'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getWeight(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改物料重量信息"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateWeight(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addWeight(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除物料重量信息编号为"' + ids + '"的数据项？').then(function() {
        return delWeight(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('material/weight/export', {
        ...this.queryParams
      }, `weight_${new Date().getTime()}.xlsx`)
    },
    /** 查看详情 */
    handleViewDetail(row) {
      // 加载详情信息
      this.detailInfo = { ...row }
      getMaterialDetail(row.materialId).then(response => {
        // 合并详情信息
        if (response.data) {
          this.detailInfo = { ...this.detailInfo, ...response.data }
        }
        this.detailOpen = true
      }).catch(() => {
        // 如果获取详情失败，直接展示基础信息
        this.detailOpen = true
      })
    },
    /** 物料编码失去焦点时，获取物料信息 */
    handleMaterialCodeBlur() {
      if (this.form.materialCode) {
        getMaterialByCode(this.form.materialCode).then(response => {
          if (response.data) {
            this.form.materialId = response.data.id
            this.form.materialName = response.data.materialName
            // 可以进一步获取更多信息如果需要
          }
        }).catch(() => {
          this.$message.warning('未找到该物料编码对应的物料信息')
        })
      }
    },
    /** 校准操作 */
    handleCalibration() {
      if (this.single) {
        this.$modal.msgWarning("请选择一条记录进行校准")
        return
      }

      const id = this.ids[0]
      getWeight(id).then(response => {
        const data = response.data
        this.calibrationForm = {
          id: data.id,
          materialId: data.materialId,
          materialCode: data.materialCode,
          materialName: data.materialName,
          standardWeight: data.standardWeight,
          weightUnit: data.weightUnit,
          newStandardWeight: data.standardWeight,
          calibrationDate: this.parseTime(new Date(), '{y}-{m}-{d}'),
          nextCalibrationDate: data.nextCalibrationTime,
          calibrationNote: ''
        }
        this.calibrationOpen = true
      })
    },
    /** 提交校准 */
    submitCalibration() {
      const data = {
        id: this.calibrationForm.id,
        standardWeight: this.calibrationForm.newStandardWeight,
        lastCalibrationTime: this.calibrationForm.calibrationDate,
        nextCalibrationTime: this.calibrationForm.nextCalibrationDate,
        remark: this.calibrationForm.calibrationNote
      }

      updateWeight(data).then(response => {
        this.$modal.msgSuccess("校准成功")
        this.calibrationOpen = false
        this.getList()
      })
    },
    /** 取消校准 */
    cancelCalibration() {
      this.calibrationOpen = false
    }
  }
}
</script>

<style scoped>
.search-card {
  margin-bottom: 18px;
}

.mb8 {
  margin-bottom: 8px;
}

.el-description-item__label {
  font-weight: bold;
}

.el-descriptions {
  margin-bottom: 20px;
}

.el-tag {
  margin-right: 4px;
}

.margin-top {
  margin-top: 20px;
}

.el-table .warning-row {
  background: #fdf5e6;
}

.el-table .success-row {
  background: #f0f9eb;
}

/* 添加日期选择器样式调整 */
.el-date-editor.el-input {
  width: 100%;
}

/* 改进表单布局 */
.el-form-item {
  margin-bottom: 18px;
}

.el-dialog .el-form {
  padding: 0 15px;
}

/* 确保表单项不溢出 */
.el-form-item__content {
  overflow: hidden;
}
</style>
