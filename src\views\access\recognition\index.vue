<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon success">
              <i class="el-icon-success"></i>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ statistics.successCount || 0 }}</div>
              <div class="statistics-label">识别成功</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon danger">
              <i class="el-icon-error"></i>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ statistics.failCount || 0 }}</div>
              <div class="statistics-label">识别失败</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon warning">
              <i class="el-icon-warning"></i>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ statistics.strangerCount || 0 }}</div>
              <div class="statistics-label">陌生人</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon info">
              <i class="el-icon-time"></i>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ statistics.todayCount || 0 }}</div>
              <div class="statistics-label">今日识别</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="设备名称" prop="deviceCode">
        <el-select v-model="queryParams.deviceCode" placeholder="请选择设备" clearable>
          <el-option
            v-for="device in deviceOptions"
            :key="device.deviceCode"
            :label="device.deviceName"
            :value="device.deviceCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="人员姓名" prop="personName">
        <el-input
          v-model="queryParams.personName"
          placeholder="请输入人员姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="识别结果" prop="recognitionResult">
        <el-select v-model="queryParams.recognitionResult" placeholder="请选择识别结果" clearable>
          <el-option label="成功" value="0" />
          <el-option label="失败" value="1" />
          <el-option label="陌生人" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="识别时间" prop="recognitionTime">
        <el-date-picker
          v-model="queryParams.recognitionTime"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-refresh"
          size="mini"
          @click="handleRefresh"
        >刷新数据</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['access:recognition:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['access:recognition:remove']"
        >批量删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-setting"
          size="mini"
          @click="handleCleanup"
          v-hasPermi="['access:recognition:cleanup']"
        >清理记录</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="recognitionList" @selection-change="handleSelectionChange" row-key="resultId">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="识别时间" align="center" prop="recognitionTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.recognitionTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="设备名称" align="center" prop="deviceName" width="150" />
      <el-table-column label="设备编码" align="center" prop="deviceCode" width="120" />
      <el-table-column label="人员姓名" align="center" prop="personName" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.personName">{{ scope.row.personName }}</span>
          <el-tag v-else type="warning" size="mini">陌生人</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="工号" align="center" prop="employeeNo" width="100" />
      <el-table-column label="识别结果" align="center" prop="recognitionResult" width="100">
        <template slot-scope="scope">
          <el-tag :type="getResultTagType(scope.row.recognitionResult)" size="mini">
            {{ getResultText(scope.row.recognitionResult) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="置信度" align="center" prop="confidence" width="80">
        <template slot-scope="scope">
          <span>{{ Math.round((scope.row.confidence || 0) * 100) }}%</span>
        </template>
      </el-table-column>
      <el-table-column label="人脸图片" align="center" prop="faceImagePath" width="100">
        <template slot-scope="scope">
          <image-preview v-if="scope.row.faceImagePath" :src="scope.row.faceImagePath" :width="50" :height="50"/>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="通行结果" align="center" prop="accessResult" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.accessResult === '0' ? 'success' : 'danger'" size="mini">
            {{ scope.row.accessResult === '0' ? '允许' : '拒绝' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['access:recognition:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 识别详情对话框 -->
    <el-dialog title="识别详情" :visible.sync="detailVisible" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="识别时间">{{ parseTime(currentRecord.recognitionTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="设备名称">{{ currentRecord.deviceName }}</el-descriptions-item>
        <el-descriptions-item label="设备编码">{{ currentRecord.deviceCode }}</el-descriptions-item>
        <el-descriptions-item label="设备IP">{{ currentRecord.deviceIp }}</el-descriptions-item>
        <el-descriptions-item label="人员姓名">{{ currentRecord.personName || '陌生人' }}</el-descriptions-item>
        <el-descriptions-item label="人员工号">{{ currentRecord.employeeNo || '-' }}</el-descriptions-item>
        <el-descriptions-item label="识别置信度">{{ Math.round((currentRecord.confidence || 0) * 100) }}%</el-descriptions-item>
        <el-descriptions-item label="识别阈值">{{ Math.round((currentRecord.threshold || 0) * 100) }}%</el-descriptions-item>
        <el-descriptions-item label="识别结果">
          <el-tag :type="getResultTagType(currentRecord.recognitionResult)">
            {{ getResultText(currentRecord.recognitionResult) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="活体检测">
          <el-tag :type="currentRecord.livenessResult === '0' ? 'success' : 'danger'">
            {{ currentRecord.livenessResult === '0' ? '真人' : currentRecord.livenessResult === '1' ? '假人' : '未检测' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="口罩检测">
          <el-tag :type="currentRecord.maskResult === '1' ? 'success' : 'warning'">
            {{ currentRecord.maskResult === '1' ? '戴口罩' : currentRecord.maskResult === '0' ? '未戴口罩' : '未检测' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="体温检测">
          <span v-if="currentRecord.temperature">
            {{ currentRecord.temperature }}°C
            <el-tag :type="currentRecord.temperatureStatus === '0' ? 'success' : 'danger'" size="mini">
              {{ currentRecord.temperatureStatus === '0' ? '正常' : '异常' }}
            </el-tag>
          </span>
          <span v-else>未检测</span>
        </el-descriptions-item>
        <el-descriptions-item label="通行结果">
          <el-tag :type="currentRecord.accessResult === '0' ? 'success' : 'danger'">
            {{ currentRecord.accessResult === '0' ? '允许通行' : '拒绝通行' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="拒绝原因" v-if="currentRecord.rejectReason">{{ currentRecord.rejectReason }}</el-descriptions-item>
      </el-descriptions>

      <div class="image-preview-section" style="margin-top: 20px;">
        <el-row :gutter="20">
          <el-col :span="12" v-if="currentRecord.faceImagePath">
            <div class="image-preview-item">
              <h4>人脸图片</h4>
              <el-image
                :src="currentRecord.faceImagePath"
                style="width: 200px; height: 200px;"
                fit="cover"
                :preview-src-list="[currentRecord.faceImagePath]"
              />
            </div>
          </el-col>
          <el-col :span="12" v-if="currentRecord.sceneImagePath">
            <div class="image-preview-item">
              <h4>现场图片</h4>
              <el-image
                :src="currentRecord.sceneImagePath"
                style="width: 200px; height: 200px;"
                fit="cover"
                :preview-src-list="[currentRecord.sceneImagePath]"
              />
            </div>
          </el-col>
        </el-row>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="detailVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listFaceResult, getFaceResult, delFaceResult, exportFaceResult, cleanupFaceResult, getFaceResultStatistics } from "@/api/access/faceResult"
import { listDevice } from "@/api/access/device"

export default {
  name: "Recognition",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 人脸识别记录表格数据
      recognitionList: [],
      // 设备选项
      deviceOptions: [],
      // 统计数据
      statistics: {
        successCount: 0,
        failCount: 0,
        strangerCount: 0,
        todayCount: 0
      },
      // 详情对话框
      detailVisible: false,
      currentRecord: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceCode: null,
        personName: null,
        recognitionResult: null,
        recognitionTime: null,
      },
    }
  },
  created() {
    this.getList()
    this.getDeviceOptions()
    this.getStatistics()
  },
  methods: {
    /** 查询人脸识别记录列表 */
    getList() {
      this.loading = true
      // 处理时间范围参数
      const params = { ...this.queryParams }
      if (params.recognitionTime && params.recognitionTime.length === 2) {
        params.startTime = params.recognitionTime[0]
        params.endTime = params.recognitionTime[1]
        delete params.recognitionTime
      }

      listFaceResult(params).then(response => {
        this.recognitionList = response.rows || []
        this.total = response.total || 0
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    /** 获取设备选项 */
    getDeviceOptions() {
      listDevice().then(response => {
        this.deviceOptions = response.rows || []
      })
    },
    /** 获取统计数据 */
    getStatistics() {
      // 调用统计接口获取真实数据
      getFaceResultStatistics().then(response => {
        this.statistics = response.data || {
          successCount: 0,
          failCount: 0,
          strangerCount: 0,
          todayCount: 0
        };
      }).catch(error => {
        console.error('获取统计数据失败:', error);
        this.statistics = {
          successCount: 0,
          failCount: 0,
          strangerCount: 0,
          todayCount: 0
        };
      });
    },
    /** 识别结果标签类型 */
    getResultTagType(result) {
      switch (result) {
        case '0': return 'success'
        case '1': return 'danger'
        case '2': return 'warning'
        default: return 'info'
      }
    },
    /** 识别结果文本 */
    getResultText(result) {
      switch (result) {
        case '0': return '成功'
        case '1': return '失败'
        case '2': return '陌生人'
        default: return '未知'
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.resultId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 刷新数据 */
    handleRefresh() {
      this.getList()
      this.getStatistics()
      this.$modal.msgSuccess("数据已刷新")
    },
    /** 查看详情 */
    handleView(row) {
      this.currentRecord = row
      this.detailVisible = true
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('access/faceResult/export', {
        ...this.queryParams
      }, `人脸识别记录_${new Date().getTime()}.xlsx`)
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.resultId || this.ids
      this.$modal.confirm('是否确认删除选中的人脸识别记录？').then(() => {
        return delFaceResult(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 清理记录 */
    handleCleanup() {
      this.$modal.confirm('是否确认清理30天前的识别记录？此操作不可恢复！').then(() => {
        return cleanupFaceResult({ days: 30 })
      }).then(() => {
        this.getList()
        this.getStatistics()
        this.$modal.msgSuccess("清理成功")
      }).catch(() => {})
    }
  }
}
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}

.statistics-card {
  cursor: pointer;
  transition: all 0.3s;
}

.statistics-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.statistics-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.statistics-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.statistics-icon.success {
  background: linear-gradient(135deg, #67C23A, #85CE61);
}

.statistics-icon.danger {
  background: linear-gradient(135deg, #F56C6C, #F78989);
}

.statistics-icon.warning {
  background: linear-gradient(135deg, #E6A23C, #EEBE77);
}

.statistics-icon.info {
  background: linear-gradient(135deg, #409EFF, #66B1FF);
}

.statistics-info {
  flex: 1;
}

.statistics-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.statistics-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.image-preview-section {
  border-top: 1px solid #EBEEF5;
  padding-top: 20px;
}

.image-preview-item {
  text-align: center;
}

.image-preview-item h4 {
  margin-bottom: 10px;
  color: #303133;
  font-size: 14px;
}
</style>
