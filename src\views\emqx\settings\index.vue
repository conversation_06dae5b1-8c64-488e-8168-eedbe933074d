<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="page-title">
        <h2><i class="el-icon-setting"></i> EMQX系统设置</h2>
        <p>配置EMQX客户端管理系统的各项参数和选项</p>
      </div>
      <div class="page-actions">
        <el-button type="primary" icon="el-icon-check" @click="saveAllSettings" :loading="saveLoading">
          保存所有设置
        </el-button>
        <el-button type="info" icon="el-icon-refresh" @click="resetToDefaults">
          恢复默认
        </el-button>
      </div>
    </div>

    <!-- 设置选项卡 -->
    <el-tabs v-model="activeTab" class="settings-tabs">
      <!-- 基础配置 -->
      <el-tab-pane label="基础配置" name="basic">
        <el-card class="settings-card">
          <div slot="header">
            <span><i class="el-icon-cpu"></i> EMQX连接配置</span>
          </div>
          <el-form :model="basicSettings" label-width="150px">
            <el-form-item label="EMQX服务器地址">
              <el-input v-model="basicSettings.emqxHost" placeholder="请输入EMQX服务器地址" />
            </el-form-item>
            <el-form-item label="EMQX端口">
              <el-input-number v-model="basicSettings.emqxPort" :min="1" :max="65535" />
            </el-form-item>
            <el-form-item label="管理API端口">
              <el-input-number v-model="basicSettings.apiPort" :min="1" :max="65535" />
            </el-form-item>
            <el-form-item label="API用户名">
              <el-input v-model="basicSettings.apiUsername" placeholder="请输入API用户名" />
            </el-form-item>
            <el-form-item label="API密码">
              <el-input v-model="basicSettings.apiPassword" type="password" placeholder="请输入API密码" show-password />
            </el-form-item>
            <el-form-item label="连接超时(秒)">
              <el-input-number v-model="basicSettings.connectionTimeout" :min="1" :max="300" />
            </el-form-item>
          </el-form>
        </el-card>

        <el-card class="settings-card">
          <div slot="header">
            <span><i class="el-icon-data-line"></i> 数据库配置</span>
          </div>
          <el-form :model="basicSettings" label-width="150px">
            <el-form-item label="数据库类型">
              <el-select v-model="basicSettings.dbType" placeholder="请选择数据库类型">
                <el-option label="MySQL" value="mysql" />
                <el-option label="PostgreSQL" value="postgresql" />
                <el-option label="Oracle" value="oracle" />
              </el-select>
            </el-form-item>
            <el-form-item label="数据库地址">
              <el-input v-model="basicSettings.dbHost" placeholder="请输入数据库地址" />
            </el-form-item>
            <el-form-item label="数据库端口">
              <el-input-number v-model="basicSettings.dbPort" :min="1" :max="65535" />
            </el-form-item>
            <el-form-item label="数据库名称">
              <el-input v-model="basicSettings.dbName" placeholder="请输入数据库名称" />
            </el-form-item>
            <el-form-item label="连接池大小">
              <el-input-number v-model="basicSettings.dbPoolSize" :min="1" :max="100" />
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>

      <!-- 客户端配置 -->
      <el-tab-pane label="客户端配置" name="client">
        <el-card class="settings-card">
          <div slot="header">
            <span><i class="el-icon-connection"></i> 服务器客户端设置</span>
          </div>
          <el-form :model="clientSettings" label-width="150px">
            <el-form-item label="自动注册">
              <el-switch v-model="clientSettings.autoRegister" />
              <span class="setting-desc">启用后系统启动时自动注册服务器客户端</span>
            </el-form-item>
            <el-form-item label="心跳间隔(秒)">
              <el-input-number v-model="clientSettings.keepAlive" :min="10" :max="3600" />
            </el-form-item>
            <el-form-item label="重连间隔(秒)">
              <el-input-number v-model="clientSettings.reconnectInterval" :min="1" :max="300" />
            </el-form-item>
            <el-form-item label="最大重连次数">
              <el-input-number v-model="clientSettings.maxReconnectAttempts" :min="1" :max="100" />
            </el-form-item>
            <el-form-item label="Clean Session">
              <el-switch v-model="clientSettings.cleanSession" />
              <span class="setting-desc">是否在连接时清除会话状态</span>
            </el-form-item>
          </el-form>
        </el-card>

        <el-card class="settings-card">
          <div slot="header">
            <span><i class="el-icon-key"></i> 设备认证设置</span>
          </div>
          <el-form :model="clientSettings" label-width="150px">
            <el-form-item label="密码加密">
              <el-switch v-model="clientSettings.passwordEncryption" />
              <span class="setting-desc">启用密码哈希加密存储</span>
            </el-form-item>
            <el-form-item label="加密算法">
              <el-select v-model="clientSettings.encryptionAlgorithm" :disabled="!clientSettings.passwordEncryption">
                <el-option label="SHA-256" value="sha256" />
                <el-option label="BCrypt" value="bcrypt" />
                <el-option label="PBKDF2" value="pbkdf2" />
              </el-select>
            </el-form-item>
            <el-form-item label="密码最小长度">
              <el-input-number v-model="clientSettings.minPasswordLength" :min="4" :max="32" />
            </el-form-item>
            <el-form-item label="认证缓存时间(分钟)">
              <el-input-number v-model="clientSettings.authCacheTime" :min="1" :max="1440" />
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>

      <!-- 监控配置 -->
      <el-tab-pane label="监控配置" name="monitoring">
        <el-card class="settings-card">
          <div slot="header">
            <span><i class="el-icon-monitor"></i> 实时监控设置</span>
          </div>
          <el-form :model="monitoringSettings" label-width="150px">
            <el-form-item label="启用监控">
              <el-switch v-model="monitoringSettings.enabled" />
            </el-form-item>
            <el-form-item label="数据刷新间隔(秒)">
              <el-input-number v-model="monitoringSettings.refreshInterval" :min="1" :max="300" />
            </el-form-item>
            <el-form-item label="数据保留天数">
              <el-input-number v-model="monitoringSettings.dataRetentionDays" :min="1" :max="365" />
            </el-form-item>
            <el-form-item label="告警阈值">
              <div class="threshold-settings">
                <div class="threshold-item">
                  <span>连接数告警阈值(%):</span>
                  <el-input-number v-model="monitoringSettings.connectionThreshold" :min="1" :max="100" />
                </div>
                <div class="threshold-item">
                  <span>消息速率告警阈值(/s):</span>
                  <el-input-number v-model="monitoringSettings.messageRateThreshold" :min="1" :max="10000" />
                </div>
                <div class="threshold-item">
                  <span>CPU使用率告警阈值(%):</span>
                  <el-input-number v-model="monitoringSettings.cpuThreshold" :min="1" :max="100" />
                </div>
                <div class="threshold-item">
                  <span>内存使用率告警阈值(%):</span>
                  <el-input-number v-model="monitoringSettings.memoryThreshold" :min="1" :max="100" />
                </div>
              </div>
            </el-form-item>
          </el-form>
        </el-card>

        <el-card class="settings-card">
          <div slot="header">
            <span><i class="el-icon-message"></i> 告警通知设置</span>
          </div>
          <el-form :model="monitoringSettings" label-width="150px">
            <el-form-item label="邮件通知">
              <el-switch v-model="monitoringSettings.emailNotification" />
            </el-form-item>
            <el-form-item label="SMTP服务器" v-if="monitoringSettings.emailNotification">
              <el-input v-model="monitoringSettings.smtpServer" placeholder="请输入SMTP服务器地址" />
            </el-form-item>
            <el-form-item label="SMTP端口" v-if="monitoringSettings.emailNotification">
              <el-input-number v-model="monitoringSettings.smtpPort" :min="1" :max="65535" />
            </el-form-item>
            <el-form-item label="发送邮箱" v-if="monitoringSettings.emailNotification">
              <el-input v-model="monitoringSettings.senderEmail" placeholder="请输入发送邮箱" />
            </el-form-item>
            <el-form-item label="接收邮箱" v-if="monitoringSettings.emailNotification">
              <el-input v-model="monitoringSettings.receiverEmails" placeholder="多个邮箱用逗号分隔" />
            </el-form-item>
            <el-form-item label="微信通知">
              <el-switch v-model="monitoringSettings.wechatNotification" />
            </el-form-item>
            <el-form-item label="企业微信Webhook" v-if="monitoringSettings.wechatNotification">
              <el-input v-model="monitoringSettings.wechatWebhook" placeholder="请输入企业微信Webhook地址" />
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>

      <!-- 数据分流配置 -->
      <el-tab-pane label="数据分流" name="datastream">
        <el-card class="settings-card">
          <div slot="header">
            <span><i class="el-icon-data-line"></i> 分流规则配置</span>
          </div>
          <el-form :model="datastreamSettings" label-width="150px">
            <el-form-item label="启用数据分流">
              <el-switch v-model="datastreamSettings.enabled" />
            </el-form-item>
            <el-form-item label="批量处理大小">
              <el-input-number v-model="datastreamSettings.batchSize" :min="1" :max="1000" />
            </el-form-item>
            <el-form-item label="处理超时(秒)">
              <el-input-number v-model="datastreamSettings.processTimeout" :min="1" :max="300" />
            </el-form-item>
            <el-form-item label="统计数据保留(小时)">
              <el-input-number v-model="datastreamSettings.statsRetentionHours" :min="1" :max="720" />
            </el-form-item>
          </el-form>
        </el-card>

        <el-card class="settings-card">
          <div slot="header">
            <span><i class="el-icon-menu"></i> 设备类型映射</span>
            <el-button type="primary" size="small" style="float: right;" @click="addDeviceTypeMapping">
              添加映射
            </el-button>
          </div>
          <el-table :data="datastreamSettings.deviceTypeMappings" style="width: 100%">
            <el-table-column prop="deviceType" label="设备类型" />
            <el-table-column prop="clientPrefix" label="客户端前缀" />
            <el-table-column prop="serverClientId" label="归属服务器" />
            <el-table-column prop="topicPrefix" label="主题前缀" />
            <el-table-column label="操作" width="120">
              <template slot-scope="scope">
                <el-button size="mini" @click="editDeviceTypeMapping(scope.row)">编辑</el-button>
                <el-button size="mini" type="danger" @click="deleteDeviceTypeMapping(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <!-- 清理配置 -->
      <el-tab-pane label="清理配置" name="cleanup">
        <el-card class="settings-card">
          <div slot="header">
            <span><i class="el-icon-delete"></i> 自动清理设置</span>
          </div>
          <el-form :model="cleanupSettings" label-width="150px">
            <el-form-item label="启用自动清理">
              <el-switch v-model="cleanupSettings.autoCleanup" />
            </el-form-item>
            <el-form-item label="清理间隔(小时)">
              <el-input-number v-model="cleanupSettings.cleanupInterval" :min="1" :max="168" />
            </el-form-item>
            <el-form-item label="安全检查">
              <el-switch v-model="cleanupSettings.safetyCheck" />
              <span class="setting-desc">执行清理前进行安全检查</span>
            </el-form-item>
            <el-form-item label="备份检查">
              <el-switch v-model="cleanupSettings.backupCheck" />
              <span class="setting-desc">检查是否有最近的数据备份</span>
            </el-form-item>
            <el-form-item label="清理历史保留(天)">
              <el-input-number v-model="cleanupSettings.historyRetentionDays" :min="1" :max="365" />
            </el-form-item>
          </el-form>
        </el-card>

        <el-card class="settings-card">
          <div slot="header">
            <span><i class="el-icon-warning"></i> 清理规则</span>
          </div>
          <el-form :model="cleanupSettings" label-width="150px">
            <el-form-item label="重复客户端检测">
              <el-checkbox-group v-model="cleanupSettings.duplicateDetectionRules">
                <el-checkbox label="clientId">相同客户端ID</el-checkbox>
                <el-checkbox label="ipPort">相同IP和端口</el-checkbox>
                <el-checkbox label="username">相同用户名</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="清理策略">
              <el-radio-group v-model="cleanupSettings.cleanupStrategy">
                <el-radio label="keepLatest">保留最新连接</el-radio>
                <el-radio label="keepOldest">保留最早连接</el-radio>
                <el-radio label="manual">手动选择</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>

      <!-- 系统配置 -->
      <el-tab-pane label="系统配置" name="system">
        <el-card class="settings-card">
          <div slot="header">
            <span><i class="el-icon-setting"></i> 系统参数</span>
          </div>
          <el-form :model="systemSettings" label-width="150px">
            <el-form-item label="系统名称">
              <el-input v-model="systemSettings.systemName" placeholder="请输入系统名称" />
            </el-form-item>
            <el-form-item label="系统版本">
              <el-input v-model="systemSettings.systemVersion" readonly />
            </el-form-item>
            <el-form-item label="日志级别">
              <el-select v-model="systemSettings.logLevel">
                <el-option label="DEBUG" value="debug" />
                <el-option label="INFO" value="info" />
                <el-option label="WARN" value="warn" />
                <el-option label="ERROR" value="error" />
              </el-select>
            </el-form-item>
            <el-form-item label="日志保留天数">
              <el-input-number v-model="systemSettings.logRetentionDays" :min="1" :max="365" />
            </el-form-item>
            <el-form-item label="API限流">
              <el-switch v-model="systemSettings.apiRateLimit" />
            </el-form-item>
            <el-form-item label="每分钟请求限制" v-if="systemSettings.apiRateLimit">
              <el-input-number v-model="systemSettings.apiRatePerMinute" :min="1" :max="10000" />
            </el-form-item>
          </el-form>
        </el-card>

        <el-card class="settings-card">
          <div slot="header">
            <span><i class="el-icon-lock"></i> 安全设置</span>
          </div>
          <el-form :model="systemSettings" label-width="150px">
            <el-form-item label="启用HTTPS">
              <el-switch v-model="systemSettings.httpsEnabled" />
            </el-form-item>
            <el-form-item label="SSL证书路径" v-if="systemSettings.httpsEnabled">
              <el-input v-model="systemSettings.sslCertPath" placeholder="请输入SSL证书路径" />
            </el-form-item>
            <el-form-item label="SSL私钥路径" v-if="systemSettings.httpsEnabled">
              <el-input v-model="systemSettings.sslKeyPath" placeholder="请输入SSL私钥路径" />
            </el-form-item>
            <el-form-item label="会话超时(分钟)">
              <el-input-number v-model="systemSettings.sessionTimeout" :min="1" :max="1440" />
            </el-form-item>
            <el-form-item label="IP白名单">
              <el-input
                v-model="systemSettings.ipWhitelist"
                type="textarea"
                :rows="3"
                placeholder="每行一个IP地址或IP段，例如：***********/24"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <!-- 设备类型映射对话框 -->
    <el-dialog
      :title="mappingDialogTitle"
      :visible.sync="mappingDialogVisible"
      width="500px"
    >
      <el-form :model="currentMapping" label-width="120px">
        <el-form-item label="设备类型">
          <el-input v-model="currentMapping.deviceType" placeholder="请输入设备类型" />
        </el-form-item>
        <el-form-item label="客户端前缀">
          <el-input v-model="currentMapping.clientPrefix" placeholder="请输入客户端前缀" />
        </el-form-item>
        <el-form-item label="归属服务器">
          <el-input v-model="currentMapping.serverClientId" placeholder="请输入归属服务器" />
        </el-form-item>
        <el-form-item label="主题前缀">
          <el-input v-model="currentMapping.topicPrefix" placeholder="请输入主题前缀" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="mappingDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveMappingDialog">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'EmqxSettings',
  data() {
    return {
      saveLoading: false,
      activeTab: 'basic',
      
      // 基础设置
      basicSettings: {
        emqxHost: 'localhost',
        emqxPort: 1883,
        apiPort: 18083,
        apiUsername: 'admin',
        apiPassword: 'public',
        connectionTimeout: 30,
        dbType: 'mysql',
        dbHost: 'localhost',
        dbPort: 3306,
        dbName: 'ruiyun_emqx',
        dbPoolSize: 10
      },
      
      // 客户端设置
      clientSettings: {
        autoRegister: true,
        keepAlive: 60,
        reconnectInterval: 5,
        maxReconnectAttempts: 10,
        cleanSession: true,
        passwordEncryption: true,
        encryptionAlgorithm: 'sha256',
        minPasswordLength: 6,
        authCacheTime: 30
      },
      
      // 监控设置
      monitoringSettings: {
        enabled: true,
        refreshInterval: 5,
        dataRetentionDays: 30,
        connectionThreshold: 80,
        messageRateThreshold: 1000,
        cpuThreshold: 80,
        memoryThreshold: 85,
        emailNotification: false,
        smtpServer: '',
        smtpPort: 587,
        senderEmail: '',
        receiverEmails: '',
        wechatNotification: false,
        wechatWebhook: ''
      },
      
      // 数据分流设置
      datastreamSettings: {
        enabled: true,
        batchSize: 100,
        processTimeout: 30,
        statsRetentionHours: 24,
        deviceTypeMappings: [
          {
            deviceType: '出库防错客户端',
            clientPrefix: 'outmis_client_',
            serverClientId: 'outmis_server_mqtt',
            topicPrefix: 'dwms/outmistake/'
          },
          {
            deviceType: '门禁设备客户端',
            clientPrefix: 'access_client_',
            serverClientId: 'access_server_mqtt',
            topicPrefix: 'dwms/access/'
          }
        ]
      },
      
      // 清理设置
      cleanupSettings: {
        autoCleanup: false,
        cleanupInterval: 24,
        safetyCheck: true,
        backupCheck: true,
        historyRetentionDays: 30,
        duplicateDetectionRules: ['clientId'],
        cleanupStrategy: 'keepLatest'
      },
      
      // 系统设置
      systemSettings: {
        systemName: 'EMQX客户端管理系统',
        systemVersion: '1.0.0',
        logLevel: 'info',
        logRetentionDays: 30,
        apiRateLimit: true,
        apiRatePerMinute: 1000,
        httpsEnabled: false,
        sslCertPath: '',
        sslKeyPath: '',
        sessionTimeout: 30,
        ipWhitelist: ''
      },
      
      // 映射对话框
      mappingDialogVisible: false,
      mappingDialogTitle: '',
      currentMapping: {},
      editingMappingIndex: -1
    }
  },
  
  created() {
    this.loadSettings()
  },
  
  methods: {
    async loadSettings() {
      try {
        // 这里应该从后端API加载设置
        // const response = await getSystemSettings()
        // if (response.code === 200) {
        //   Object.assign(this.basicSettings, response.data.basic)
        //   Object.assign(this.clientSettings, response.data.client)
        //   // ... 其他设置
        // }
        console.log('加载系统设置')
      } catch (error) {
        console.error('加载设置失败:', error)
        this.$message.error('加载设置失败')
      }
    },
    
    async saveAllSettings() {
      this.saveLoading = true
      try {
        const allSettings = {
          basic: this.basicSettings,
          client: this.clientSettings,
          monitoring: this.monitoringSettings,
          datastream: this.datastreamSettings,
          cleanup: this.cleanupSettings,
          system: this.systemSettings
        }
        
        // 这里应该调用后端API保存设置
        // const response = await saveSystemSettings(allSettings)
        // if (response.code === 200) {
        //   this.$message.success('设置保存成功')
        // }
        
        console.log('保存设置:', allSettings)
        this.$message.success('设置保存成功')
      } catch (error) {
        console.error('保存设置失败:', error)
        this.$message.error('保存设置失败')
      } finally {
        this.saveLoading = false
      }
    },
    
    resetToDefaults() {
      this.$confirm('确定要恢复所有设置到默认值吗？', '确认操作', {
        type: 'warning'
      }).then(() => {
        // 重置所有设置到默认值
        this.loadDefaultSettings()
        this.$message.success('已恢复默认设置')
      })
    },
    
    loadDefaultSettings() {
      // 重置到默认值的逻辑
      this.basicSettings = {
        emqxHost: 'localhost',
        emqxPort: 1883,
        apiPort: 18083,
        apiUsername: 'admin',
        apiPassword: 'public',
        connectionTimeout: 30,
        dbType: 'mysql',
        dbHost: 'localhost',
        dbPort: 3306,
        dbName: 'ruiyun_emqx',
        dbPoolSize: 10
      }
      // ... 其他默认设置
    },
    
    addDeviceTypeMapping() {
      this.mappingDialogTitle = '添加设备类型映射'
      this.currentMapping = {
        deviceType: '',
        clientPrefix: '',
        serverClientId: '',
        topicPrefix: ''
      }
      this.editingMappingIndex = -1
      this.mappingDialogVisible = true
    },
    
    editDeviceTypeMapping(mapping) {
      this.mappingDialogTitle = '编辑设备类型映射'
      this.currentMapping = { ...mapping }
      this.editingMappingIndex = this.datastreamSettings.deviceTypeMappings.indexOf(mapping)
      this.mappingDialogVisible = true
    },
    
    deleteDeviceTypeMapping(index) {
      this.$confirm('确定要删除此映射吗？', '确认操作', {
        type: 'warning'
      }).then(() => {
        this.datastreamSettings.deviceTypeMappings.splice(index, 1)
        this.$message.success('映射已删除')
      })
    },
    
    saveMappingDialog() {
      if (!this.currentMapping.deviceType || !this.currentMapping.clientPrefix) {
        this.$message.error('请填写必要的字段')
        return
      }
      
      if (this.editingMappingIndex >= 0) {
        // 编辑模式
        this.$set(this.datastreamSettings.deviceTypeMappings, this.editingMappingIndex, { ...this.currentMapping })
      } else {
        // 添加模式
        this.datastreamSettings.deviceTypeMappings.push({ ...this.currentMapping })
      }
      
      this.mappingDialogVisible = false
      this.$message.success('映射保存成功')
    }
  }
}
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title h2 {
  margin: 0;
  color: #303133;
}

.page-title p {
  margin: 5px 0 0 0;
  color: #909399;
  font-size: 14px;
}

.settings-tabs {
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.settings-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);
}

.setting-desc {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

.threshold-settings {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  background: #fafafa;
}

.threshold-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}

.threshold-item:last-child {
  margin-bottom: 0;
}

.threshold-item span {
  color: #606266;
  font-size: 14px;
}
</style>
