import request from '@/utils/request'

// 获取SIP服务器测试状态
export function getSipTestStatus() {
  return request({
    url: '/sip/testing/status',
    method: 'get'
  })
}

// 获取ZLMediaKit测试状态
export function getZlmTestStatus() {
  return request({
    url: '/video/test/status',
    method: 'get'
  })
}

// SIP连接测试
export function testSipConnection() {
  return request({
    url: '/sip/testing/connection',
    method: 'post'
  })
}

// SIP注册测试
export function testSipRegister(data) {
  return request({
    url: '/sip/testing/register',
    method: 'post',
    data: data
  })
}

// SIP呼叫测试
export function testSipInvite(data) {
  return request({
    url: '/sip/testing/invite',
    method: 'post',
    data: data
  })
}

// SIP消息测试
export function testSipMessage(data) {
  return request({
    url: '/sip/testing/message',
    method: 'post',
    data: data
  })
}

// ZLMediaKit连接测试
export function testZlmConnection() {
  return request({
    url: '/video/test/connection',
    method: 'post'
  })
}

// ZLMediaKit API测试
export function testZlmApi() {
  return request({
    url: '/video/test/api-test',
    method: 'post'
  })
}

// ZLMediaKit推流测试
export function testZlmStream(data) {
  return request({
    url: '/video/test/stream',
    method: 'post',
    data: data
  })
}

// ZLMediaKit录制测试
export function testZlmRecord(data) {
  return request({
    url: '/video/test/record',
    method: 'post',
    data: data
  })
}

// 集成测试
export function startIntegrationTest(data) {
  return request({
    url: '/sip/testing/integration/start',
    method: 'post',
    data: data
  })
}

// 停止集成测试
export function stopIntegrationTest() {
  return request({
    url: '/sip/testing/integration/stop',
    method: 'post'
  })
}

// 获取测试结果
export function getTestResults(testId) {
  return request({
    url: `/sip/testing/results/${testId}`,
    method: 'get'
  })
}

// 获取测试历史
export function getTestHistory(params) {
  return request({
    url: '/sip/testing/history',
    method: 'get',
    params: params
  })
}

// 获取测试配置
export function getTestConfig() {
  return request({
    url: '/sip/testing/config',
    method: 'get'
  })
}

// 更新测试配置
export function updateTestConfig(data) {
  return request({
    url: '/sip/testing/config',
    method: 'put',
    data: data
  })
}

// 生成测试报告
export function generateTestReport(params) {
  return request({
    url: '/sip/testing/report',
    method: 'get',
    params: params
  })
}

// 导出测试数据
export function exportTestData(params) {
  return request({
    url: '/sip/testing/export',
    method: 'get',
    params: params,
    responseType: 'blob'
  })
}
