import request from '@/utils/request'

// 获取MQTT健康状态
export function getHealthStatus() {
  return request({
    url: '/guide/mqtt/health/status',
    method: 'get'
  })
}

// 获取详细统计信息
export function getDetailedStats() {
  return request({
    url: '/guide/mqtt/health/stats',
    method: 'get'
  })
}

// 获取诊断信息
export function getDiagnosticInfo() {
  return request({
    url: '/guide/mqtt/health/diagnose',
    method: 'get'
  })
}

// 重置统计信息
export function resetStats() {
  return request({
    url: '/guide/mqtt/health/reset',
    method: 'post'
  })
}

// 强制重连
export function forceReconnect() {
  return request({
    url: '/guide/mqtt/health/reconnect',
    method: 'post'
  })
}
