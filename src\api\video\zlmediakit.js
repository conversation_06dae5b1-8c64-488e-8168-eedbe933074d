import request from '@/utils/request'

/**
 * 获取ZLMediaKit服务器信息
 */
export function getServerInfo() {
  return request({
    url: '/video/zlm/server/config',
    method: 'get'
  })
}

/**
 * 获取服务器统计信息
 */
export function getServerStats() {
  return request({
    url: '/video/zlm/server/stats',
    method: 'get'
  })
}

/**
 * 获取活跃流列表
 */
export function getMediaList() {
  return request({
    url: '/video/zlm/media/list',
    method: 'get'
  })
}

/**
 * 获取线程负载信息
 */
export function getThreadsLoad() {
  return request({
    url: '/video/zlm/server/threads',
    method: 'get'
  })
}

/**
 * 获取工作线程负载
 */
export function getWorkThreadsLoad() {
  return request({
    url: '/video/zlm/server/work-threads',
    method: 'get'
  })
}

/**
 * 获取会话列表
 */
export function getSessionList() {
  return request({
    url: '/video/zlm/session/list',
    method: 'get'
  })
}

/**
 * 重启服务器
 */
export function restartServer() {
  return request({
    url: `${ZLM_API_BASE}/index/api/restartServer`,
    method: 'get'
  })
}

/**
 * 获取版本信息
 */
export function getVersion() {
  return request({
    url: '/video/zlm/server/version',
    method: 'get'
  })
}

/**
 * 获取服务器状态摘要
 */
export function getServerSummary() {
  return request({
    url: '/video/zlm/server/summary',
    method: 'get'
  })
}

/**
 * 测试连接
 */
export function testConnection() {
  return request({
    url: '/video/zlm/test/connection',
    method: 'get'
  })
}

/**
 * 获取API列表
 */
export function getApiList() {
  return request({
    url: '/video/zlm/api/list',
    method: 'get'
  })
}

/**
 * 获取流播放者列表
 */
export function getMediaPlayers(params) {
  return request({
    url: `${ZLM_API_BASE}/index/api/getMediaPlayers`,
    method: 'get',
    params
  })
}

/**
 * 关闭流
 */
export function closeStream(params) {
  return request({
    url: '/video/zlm/stream/close',
    method: 'post',
    data: params
  })
}

/**
 * 关闭会话
 */
export function closeSession(params) {
  return request({
    url: `${ZLM_API_BASE}/index/api/kick_session`,
    method: 'get',
    params
  })
}

/**
 * 获取流信息
 */
export function getMediaInfo(params) {
  return request({
    url: `${ZLM_API_BASE}/index/api/getMediaInfo`,
    method: 'get',
    params
  })
}

/**
 * 获取录制状态
 */
export function isRecording(params) {
  return request({
    url: `${ZLM_API_BASE}/index/api/isRecording`,
    method: 'get',
    params
  })
}

/**
 * 开始录制
 */
export function startRecord(params) {
  return request({
    url: `${ZLM_API_BASE}/index/api/startRecord`,
    method: 'get',
    params
  })
}

/**
 * 停止录制
 */
export function stopRecord(params) {
  return request({
    url: `${ZLM_API_BASE}/index/api/stopRecord`,
    method: 'get',
    params
  })
}

/**
 * 获取录制文件列表
 */
export function getRecordList(params) {
  return request({
    url: `${ZLM_API_BASE}/index/api/getMp4RecordFile`,
    method: 'get',
    params
  })
}

/**
 * 删除录制文件
 */
export function deleteRecordFile(params) {
  return request({
    url: `${ZLM_API_BASE}/index/api/deleteRecordDirectory`,
    method: 'get',
    params
  })
}

/**
 * 获取快照
 */
export function getSnap(params) {
  return request({
    url: `${ZLM_API_BASE}/index/api/getSnap`,
    method: 'get',
    params
  })
}

/**
 * 添加流代理
 */
export function addStreamProxy(params) {
  return request({
    url: `${ZLM_API_BASE}/index/api/addStreamProxy`,
    method: 'post',
    data: params
  })
}

/**
 * 删除流代理
 */
export function delStreamProxy(params) {
  return request({
    url: `${ZLM_API_BASE}/index/api/delStreamProxy`,
    method: 'get',
    params
  })
}

/**
 * 添加FFmpeg源
 */
export function addFFmpegSource(params) {
  return request({
    url: `${ZLM_API_BASE}/index/api/addFFmpegSource`,
    method: 'post',
    data: params
  })
}

/**
 * 删除FFmpeg源
 */
export function delFFmpegSource(params) {
  return request({
    url: `${ZLM_API_BASE}/index/api/delFFmpegSource`,
    method: 'get',
    params
  })
}

/**
 * 获取RTP信息
 */
export function getRtpInfo(params) {
  return request({
    url: `${ZLM_API_BASE}/index/api/getRtpInfo`,
    method: 'get',
    params
  })
}

/**
 * 开启RTP服务器
 */
export function openRtpServer(params) {
  return request({
    url: `${ZLM_API_BASE}/index/api/openRtpServer`,
    method: 'post',
    data: params
  })
}

/**
 * 关闭RTP服务器
 */
export function closeRtpServer(params) {
  return request({
    url: `${ZLM_API_BASE}/index/api/closeRtpServer`,
    method: 'get',
    params
  })
}

/**
 * 获取服务器配置
 */
export function getServerConfig() {
  return request({
    url: `${ZLM_API_BASE}/index/api/getServerConfig`,
    method: 'get'
  })
}

/**
 * 设置服务器配置
 */
export function setServerConfig(params) {
  return request({
    url: `${ZLM_API_BASE}/index/api/setServerConfig`,
    method: 'post',
    data: params
  })
}

/**
 * 重新加载配置
 */
export function reloadConfig() {
  return request({
    url: `${ZLM_API_BASE}/index/api/reloadConfig`,
    method: 'get'
  })
}
