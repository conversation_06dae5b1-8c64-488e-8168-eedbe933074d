<template>
  <div class="dynamic-menu-test">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>动态菜单图标测试</span>
      </div>
      
      <div class="menu-test-grid">
        <div 
          v-for="(icon, menuName) in testMenus" 
          :key="menuName"
          class="menu-item-test"
        >
          <div class="icon-display">
            <svg-icon :icon-class="icon" class="menu-icon" />
          </div>
          <div class="menu-name">{{ menuName }}</div>
          <div class="icon-name">{{ icon }}</div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import DynamicMenuIconMapper from '@/components/DynamicMenu'

export default {
  name: 'DynamicMenuTest',
  mixins: [DynamicMenuIconMapper],
  data() {
    return {
      testMenus: {}
    }
  },
  created() {
    this.generateTestMenus()
  },
  methods: {
    generateTestMenus() {
      const menuNames = [
        // 主菜单
        '库房组态', '仓库管理', '物料管理', '审批管理', '出入库管理',
        '出库防错', '智能导寻', '门禁管理', '视频监控', 'EMQX管理',
        'SIP视频服务', '系统工具', '系统监控', '系统管理', '睿云慧通',

        // 系统管理子菜单
        '用户管理', '角色管理', '菜单管理', '部门管理', '岗位管理',
        '字典管理', '参数设置', '通知公告', '日志管理',

        // 系统监控子菜单
        '在线用户', '定时任务', '数据监控', '服务监控', '缓存监控',

        // 系统工具子菜单
        '表单构建', '代码生成', '系统接口',

        // 仓库管理子菜单
        '仓库信息', '区域信息', '货架信息', '货位信息', '容器信息',

        // 物料管理子菜单
        '物料库存快查', '物料编码映射', '物料信息', '物料库存',
        '物料分类', '物料清单管理', '物料重量信息',

        // 门禁管理子菜单
        '设备管理', '门禁记录', '人脸授权', '识别记录',

        // 智能导寻子菜单
        '物品导寻', '导寻配置', '管控日志', '物品管理',
        '协议管理', '命令管理', '连接管理',

        // 出入库管理子菜单
        '出入库申请', '出入库执行', '库房盘点', '出入库日志',
        '单据打印', '日志清理管理', '打印模板管理', '单据打印日志',

        // 库房组态子菜单
        'Web组态编辑器', '重量监控管理', '门禁记录管理', '设备状态监控', '权限管理'
      ]
      
      menuNames.forEach(menuName => {
        this.testMenus[menuName] = this.getMenuIcon(menuName)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dynamic-menu-test {
  padding: 20px;
  
  .menu-test-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
    margin-top: 20px;
  }
  
  .menu-item-test {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 16px;
    text-align: center;
    transition: all 0.3s;
    
    &:hover {
      border-color: #409eff;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
    
    .icon-display {
      margin-bottom: 8px;
      
      .menu-icon {
        font-size: 24px;
        color: #409eff;
      }
    }
    
    .menu-name {
      font-weight: 500;
      color: #303133;
      margin-bottom: 4px;
    }
    
    .icon-name {
      font-size: 12px;
      color: #909399;
      font-family: monospace;
    }
  }
}
</style>
