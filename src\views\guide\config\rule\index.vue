<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="规则名称" prop="ruleName">
        <el-input
          v-model="queryParams.ruleName"
          placeholder="请输入规则名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="规则编码" prop="ruleCode">
        <el-input
          v-model="queryParams.ruleCode"
          placeholder="请输入规则编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="关联模板" prop="templateId">
        <el-select v-model="queryParams.templateId" placeholder="请选择关联模板" clearable>
          <el-option
            v-for="template in templateOptions"
            :key="template.templateId"
            :label="template.templateName"
            :value="template.templateId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="条件类型" prop="conditionType">
        <el-select v-model="queryParams.conditionType" placeholder="请选择条件类型" clearable>
          <el-option label="物料" value="material" />
          <el-option label="位置" value="location" />
          <el-option label="时间" value="time" />
          <el-option label="用户" value="user" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否激活" prop="isActive">
        <el-select v-model="queryParams.isActive" placeholder="请选择状态" clearable>
          <el-option label="激活" value="1" />
          <el-option label="停用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['guide:config:rule:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['guide:config:rule:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['guide:config:rule:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-switch-button"
          size="mini"
          :disabled="multiple"
          @click="handleBatchStatus"
          v-hasPermi="['guide:config:rule:edit']"
        >批量启停</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="ruleList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="规则ID" align="center" prop="ruleId" />
      <el-table-column label="规则名称" align="center" prop="ruleName" />
      <el-table-column label="规则编码" align="center" prop="ruleCode" />
      <el-table-column label="关联模板" align="center" prop="templateName" />
      <el-table-column label="条件类型" align="center" prop="conditionType">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.conditionType === 'material'" type="primary">物料</el-tag>
          <el-tag v-else-if="scope.row.conditionType === 'location'" type="success">位置</el-tag>
          <el-tag v-else-if="scope.row.conditionType === 'time'" type="warning">时间</el-tag>
          <el-tag v-else-if="scope.row.conditionType === 'user'" type="info">用户</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作符" align="center" prop="conditionOperator">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.guide_condition_operator" :value="scope.row.conditionOperator"/>
        </template>
      </el-table-column>
      <el-table-column label="条件值" align="center" prop="conditionValue" :show-overflow-tooltip="true" />
      <el-table-column label="优先级" align="center" prop="priority" />
      <el-table-column label="是否激活" align="center" prop="isActive">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.isActive"
            active-value="1"
            inactive-value="0"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['guide:config:rule:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-copy-document"
            @click="handleCopy(scope.row)"
            v-hasPermi="['guide:config:rule:add']"
          >复制</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['guide:config:rule:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改导寻配置规则对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="规则名称" prop="ruleName">
              <el-input v-model="form.ruleName" placeholder="请输入规则名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="规则编码" prop="ruleCode">
              <el-input v-model="form.ruleCode" placeholder="请输入规则编码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="关联模板" prop="templateId">
              <el-select v-model="form.templateId" placeholder="请选择关联模板">
                <el-option
                  v-for="template in templateOptions"
                  :key="template.templateId"
                  :label="template.templateName"
                  :value="template.templateId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-input-number v-model="form.priority" :min="1" :max="10" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="条件类型" prop="conditionType">
              <el-select v-model="form.conditionType" placeholder="请选择条件类型" @change="handleConditionTypeChange">
                <el-option label="物料" value="material" />
                <el-option label="位置" value="location" />
                <el-option label="时间" value="time" />
                <el-option label="用户" value="user" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="操作符" prop="conditionOperator">
              <el-select v-model="form.conditionOperator" placeholder="请选择操作符">
                <el-option
                  v-for="dict in dict.type.guide_condition_operator"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="条件值" prop="conditionValue">
          <el-input v-model="form.conditionValue" type="textarea" :rows="3" placeholder="请输入条件值，JSON格式" />
          <div class="condition-help">
            <p><strong>条件值示例：</strong></p>
            <p v-if="form.conditionType === 'material'">
              等于：{"value": "MAT001"}<br>
              模糊匹配：{"value": "URGENT_%"}<br>
              包含：{"value": ["MAT001", "MAT002"]}
            </p>
            <p v-if="form.conditionType === 'location'">
              等于：{"value": "A-01-01"}<br>
              模糊匹配：{"value": "A-%"}<br>
              包含：{"value": ["A-01", "B-02"]}
            </p>
            <p v-if="form.conditionType === 'time'">
              时间范围：{"start": "22:00:00", "end": "06:00:00"}
            </p>
            <p v-if="form.conditionType === 'user'">
              等于：{"value": "admin"}<br>
              包含：{"value": ["admin", "user1"]}
            </p>
          </div>
        </el-form-item>
        <el-form-item label="是否激活" prop="isActive">
          <el-switch v-model="form.isActive" active-value="1" inactive-value="0" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 复制规则对话框 -->
    <el-dialog title="复制规则" :visible.sync="copyOpen" width="500px" append-to-body>
      <el-form ref="copyForm" :model="copyForm" :rules="copyRules" label-width="120px">
        <el-form-item label="新规则名称" prop="ruleName">
          <el-input v-model="copyForm.ruleName" placeholder="请输入新规则名称" />
        </el-form-item>
        <el-form-item label="新规则编码" prop="ruleCode">
          <el-input v-model="copyForm.ruleCode" placeholder="请输入新规则编码" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitCopy">确 定</el-button>
        <el-button @click="cancelCopy">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 批量状态修改对话框 -->
    <el-dialog title="批量状态修改" :visible.sync="batchStatusOpen" width="400px" append-to-body>
      <el-form ref="batchStatusForm" :model="batchStatusForm" label-width="120px">
        <el-form-item label="操作类型">
          <el-radio-group v-model="batchStatusForm.isActive">
            <el-radio label="1">激活</el-radio>
            <el-radio label="0">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <p>将对选中的 {{ ids.length }} 条规则执行{{ batchStatusForm.isActive === '1' ? '激活' : '停用' }}操作</p>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitBatchStatus">确 定</el-button>
        <el-button @click="cancelBatchStatus">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listRule, getRule, delRule, addRule, updateRule, copyRule, checkRuleCodeUnique, batchUpdateRuleStatus } from "@/api/guide/config";
import { listEnabledTemplate } from "@/api/guide/config";

export default {
  name: "GuideConfigRule",
  dicts: ['guide_condition_operator'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 导寻配置规则表格数据
      ruleList: [],
      // 模板选项
      templateOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示复制弹出层
      copyOpen: false,
      // 是否显示批量状态弹出层
      batchStatusOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        ruleName: null,
        ruleCode: null,
        templateId: null,
        conditionType: null,
        isActive: null
      },
      // 表单参数
      form: {},
      // 复制表单参数
      copyForm: {},
      // 批量状态表单参数
      batchStatusForm: {
        isActive: "1"
      },
      // 当前复制的规则ID
      copyRuleId: null,
      // 表单校验
      rules: {
        ruleName: [
          { required: true, message: "规则名称不能为空", trigger: "blur" }
        ],
        ruleCode: [
          { required: true, message: "规则编码不能为空", trigger: "blur" },
          { validator: this.validateRuleCode, trigger: "blur" }
        ],
        templateId: [
          { required: true, message: "关联模板不能为空", trigger: "change" }
        ],
        conditionType: [
          { required: true, message: "条件类型不能为空", trigger: "change" }
        ],
        conditionOperator: [
          { required: true, message: "操作符不能为空", trigger: "change" }
        ],
        conditionValue: [
          { required: true, message: "条件值不能为空", trigger: "blur" },
          { validator: this.validateConditionValue, trigger: "blur" }
        ]
      },
      // 复制表单校验
      copyRules: {
        ruleName: [
          { required: true, message: "新规则名称不能为空", trigger: "blur" }
        ],
        ruleCode: [
          { required: true, message: "新规则编码不能为空", trigger: "blur" },
          { validator: this.validateCopyRuleCode, trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getTemplateOptions();
  },
  methods: {
    /** 查询导寻配置规则列表 */
    getList() {
      this.loading = true;
      listRule(this.queryParams).then(response => {
        this.ruleList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取模板选项 */
    getTemplateOptions() {
      listEnabledTemplate().then(response => {
        this.templateOptions = response.data;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        ruleId: null,
        ruleName: null,
        ruleCode: null,
        templateId: null,
        conditionType: null,
        conditionOperator: null,
        conditionValue: null,
        priority: 5,
        isActive: "1",
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.ruleId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加导寻配置规则";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const ruleId = row.ruleId || this.ids
      getRule(ruleId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改导寻配置规则";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.ruleId != null) {
            updateRule(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRule(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ruleIds = row.ruleId || this.ids;
      this.$modal.confirm('是否确认删除导寻配置规则编号为"' + ruleIds + '"的数据项？').then(function() {
        return delRule(ruleIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 状态修改 */
    handleStatusChange(row) {
      let text = row.isActive === "1" ? "激活" : "停用";
      this.$modal.confirm('确认要"' + text + '""' + row.ruleName + '"规则吗？').then(function() {
        return updateRule(row);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function() {
        row.isActive = row.isActive === "0" ? "1" : "0";
      });
    },
    /** 批量状态修改 */
    handleBatchStatus() {
      this.batchStatusOpen = true;
    },
    /** 提交批量状态修改 */
    submitBatchStatus() {
      batchUpdateRuleStatus(this.ids, this.batchStatusForm.isActive).then(response => {
        this.$modal.msgSuccess("操作成功");
        this.batchStatusOpen = false;
        this.getList();
      });
    },
    /** 取消批量状态修改 */
    cancelBatchStatus() {
      this.batchStatusOpen = false;
    },
    /** 复制规则 */
    handleCopy(row) {
      this.copyRuleId = row.ruleId;
      this.copyForm = {
        ruleName: row.ruleName + "_副本",
        ruleCode: row.ruleCode + "_COPY"
      };
      this.copyOpen = true;
    },
    /** 提交复制 */
    submitCopy() {
      this.$refs["copyForm"].validate(valid => {
        if (valid) {
          copyRule(this.copyRuleId, this.copyForm).then(response => {
            this.$modal.msgSuccess("复制成功");
            this.copyOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 取消复制 */
    cancelCopy() {
      this.copyOpen = false;
      this.copyForm = {};
      this.copyRuleId = null;
    },
    /** 条件类型变化 */
    handleConditionTypeChange(value) {
      // 重置条件值
      this.form.conditionValue = null;
    },
    /** 校验规则编码 */
    validateRuleCode(rule, value, callback) {
      if (value) {
        checkRuleCodeUnique(value, this.form.ruleId).then(response => {
          if (response.data) {
            callback();
          } else {
            callback(new Error("规则编码已存在"));
          }
        });
      } else {
        callback();
      }
    },
    /** 校验复制规则编码 */
    validateCopyRuleCode(rule, value, callback) {
      if (value) {
        checkRuleCodeUnique(value, null).then(response => {
          if (response.data) {
            callback();
          } else {
            callback(new Error("规则编码已存在"));
          }
        });
      } else {
        callback();
      }
    },
    /** 校验条件值 */
    validateConditionValue(rule, value, callback) {
      if (value) {
        try {
          JSON.parse(value);
          callback();
        } catch (e) {
          callback(new Error("条件值必须是有效的JSON格式"));
        }
      } else {
        callback();
      }
    }
  }
};
</script>

<style scoped>
.condition-help {
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-size: 12px;
  color: #606266;
}
.condition-help p {
  margin: 5px 0;
}
</style>
