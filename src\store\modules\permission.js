import auth from '@/plugins/auth'
import router, { constantRoutes, dynamicRoutes } from '@/router'
import { getRouters } from '@/api/menu'
import Layout from '@/layout/index'
import ParentView from '@/components/ParentView'
import InnerLink from '@/layout/components/InnerLink'
import { deduplicateRoutes, resetRouteRegistry } from '@/utils/routeDeduplication'
import { safeRouteDeduplication } from '@/utils/simpleRouteDedup'

// 辅助函数：查找指定名称的菜单
function findMenuByName(routes, menuName) {
  for (let route of routes) {
    if (route.meta && route.meta.title === menuName) {
      return route
    }
    if (route.children && route.children.length > 0) {
      const found = findMenuByName(route.children, menuName)
      if (found) return found
    }
  }
  return null
}

const permission = {
  state: {
    routes: [],
    addRoutes: [],
    defaultRoutes: [],
    topbarRouters: [],
    sidebarRouters: []
  },
  mutations: {
    SET_ROUTES: (state, routes) => {
      state.addRoutes = routes
      state.routes = constantRoutes.concat(routes)
    },
    SET_DEFAULT_ROUTES: (state, routes) => {
      state.defaultRoutes = constantRoutes.concat(routes)
    },
    SET_TOPBAR_ROUTES: (state, routes) => {
      state.topbarRouters = routes
    },
    SET_SIDEBAR_ROUTERS: (state, routes) => {
      state.sidebarRouters = routes
    },
  },
  actions: {
    // 生成路由
    GenerateRoutes({ commit }) {
      return new Promise(resolve => {
        // 向后端请求路由数据
        getRouters().then(res => {
          const rawData = JSON.parse(JSON.stringify(res.data))
          console.log('[路由生成] 原始路由数据:', rawData.length, '条')

          // 使用安全的路由去重工具
          const cleanedData = safeRouteDeduplication(rawData)
          console.log('[路由生成] 去重后路由数据:', cleanedData.length, '条')

          // 特别检查门禁管理菜单
          const accessMenu = findMenuByName(cleanedData, '门禁管理')
          if (accessMenu) {
            console.log('[路由生成] 门禁管理菜单子项数量:', accessMenu.children ? accessMenu.children.length : 0)
            if (accessMenu.children) {
              accessMenu.children.forEach(child => {
                console.log('[路由生成] 门禁子菜单:', child.name, child.path)
              })
            }
          }

          const sdata = JSON.parse(JSON.stringify(cleanedData))
          const rdata = JSON.parse(JSON.stringify(cleanedData))

          const sidebarRoutes = filterAsyncRouter(sdata)
          const rewriteRoutes = filterAsyncRouter(rdata, false, true)
          const asyncRoutes = filterDynamicRoutes(dynamicRoutes)
          rewriteRoutes.push({ path: '*', redirect: '/404', hidden: true })

          // 直接添加路由，不再进行复杂的去重处理
          const routesToAdd = [...asyncRoutes, ...rewriteRoutes].filter(r => r.path !== '*')
          if (routesToAdd.length > 0) {
            console.log('[路由生成] 添加路由数量:', routesToAdd.length)
            router.addRoutes(routesToAdd)
          }
          commit('SET_ROUTES', rewriteRoutes)
          commit('SET_SIDEBAR_ROUTERS', constantRoutes.concat(sidebarRoutes))
          commit('SET_DEFAULT_ROUTES', sidebarRoutes)
          commit('SET_TOPBAR_ROUTES', sidebarRoutes)

          resolve(rewriteRoutes)
        }).catch(error => {
          console.error('[路由生成] 获取路由失败:', error)
          // 如果获取路由失败，使用默认路由
          const asyncRoutes = filterDynamicRoutes(dynamicRoutes)
          router.addRoutes(asyncRoutes)
          commit('SET_ROUTES', [])
          commit('SET_SIDEBAR_ROUTERS', constantRoutes)
          commit('SET_DEFAULT_ROUTES', [])
          commit('SET_TOPBAR_ROUTES', [])
          resolve([])
        })
      })
    }
  }
}

// 过滤重复路由的通用函数
// 移除复杂的去重函数，使用简单的方案

// 生成路由唯一标识符
function generateRouteKey(route) {
  // 结合路径、组件和父级信息生成唯一键
  const pathPart = route.path || 'no-path'
  const componentPart = route.component || 'no-component'
  const metaPart = route.meta && route.meta.title ? route.meta.title : 'no-title'
  return `${pathPart}-${componentPart}-${metaPart}`
}

// 生成唯一的路由名称
function generateUniqueName(route, seenNames) {
  let baseName = route.path ? route.path.replace(/[^a-zA-Z0-9]/g, '') : 'Route'
  if (route.meta && route.meta.title) {
    baseName = route.meta.title.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '')
  }

  let uniqueName = baseName
  let counter = 1

  while (seenNames.has(uniqueName)) {
    uniqueName = `${baseName}${counter}`
    counter++
  }

  return uniqueName
}

// 最终的路由名称去重函数
// 移除复杂的去重函数



// 遍历后台传来的路由字符串，转换为组件对象
function filterAsyncRouter(asyncRouterMap, lastRouter = false, type = false) {
  return asyncRouterMap.filter(route => {
    // 处理外部链接路径格式
    if (route.path && (route.path.indexOf('http://') === 0 || route.path.indexOf('https://') === 0)) {
      route.meta = route.meta || {}
      route.meta.isExternal = true
      // 外部链接不需要前导斜杠，直接返回false过滤掉，避免路由错误
      console.log(`[路由处理] 过滤外部链接: ${route.path}`)
      return false
    }
    
    if (type && route.children) {
      route.children = filterChildren(route.children)
    }
    if (route.component) {
      // Layout ParentView 组件特殊处理
      if (route.component === 'Layout') {
        route.component = Layout
      } else if (route.component === 'ParentView') {
        route.component = ParentView
      } else if (route.component === 'InnerLink') {
        route.component = InnerLink
      } else {
        route.component = loadView(route.component)
      }
    }
    if (route.children != null && route.children && route.children.length) {
      route.children = filterAsyncRouter(route.children, route, type)
    } else {
      delete route['children']
      delete route['redirect']
    }
    return true
  })
}

function filterChildren(childrenMap, lastRouter = false) {
  var children = []
  childrenMap.forEach(el => {
    el.path = lastRouter ? lastRouter.path + '/' + el.path : el.path
    if (el.children && el.children.length && el.component === 'ParentView') {
      children = children.concat(filterChildren(el.children, el))
    } else {
      children.push(el)
    }
  })
  return children
}

// 动态路由遍历，验证是否具备权限
export function filterDynamicRoutes(routes) {
  const res = []
  routes.forEach(route => {
    if (route.permissions) {
      if (auth.hasPermiOr(route.permissions)) {
        res.push(route)
      }
    } else if (route.roles) {
      if (auth.hasRoleOr(route.roles)) {
        res.push(route)
      }
    }
  })
  return res
}

export const loadView = (view) => {
  if (process.env.NODE_ENV === 'development') {
    return (resolve) => require([`@/views/${view}`], resolve)
  } else {
    // 使用 import 实现生产环境的路由懒加载
    return () => import(`@/views/${view}`)
  }
}



export default permission
