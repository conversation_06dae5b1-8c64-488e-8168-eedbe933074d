<template>
  <div class="app-container">
    <el-card class="box-card" v-loading="loading">
      <div slot="header" class="clearfix">
        <span>编辑流程定义</span>
      </div>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <!-- 基本信息部分 -->
        <el-divider content-position="left">基本信息</el-divider>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="流程名称" prop="workflowName">
              <el-input v-model="form.workflowName" placeholder="请输入流程名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="流程编码" prop="workflowCode">
              <el-input v-model="form.workflowCode" placeholder="请输入流程编码" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="业务类型" prop="businessType">
              <el-select v-model="form.businessType" placeholder="请选择业务类型" style="width: 100%">
                <el-option v-for="item in businessTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio label="0">启用</el-radio>
                <el-radio label="1">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入描述信息" />
        </el-form-item>
        
        <!-- 节点管理部分 -->
        <el-divider content-position="left">节点管理</el-divider>
        <div class="workflow-nodes">
          <el-card v-for="(node, index) in form.nodes" :key="index" class="node-card" :class="{ 'node-start': node.nodeType === '0', 'node-approval': node.nodeType === '1', 'node-end': node.nodeType === '2' }">
            <div slot="header" class="clearfix">
              <span>
                <el-tag v-if="node.nodeType === '0'" type="primary">开始节点</el-tag>
                <el-tag v-else-if="node.nodeType === '1'" type="info">审批节点</el-tag>
                <el-tag v-else-if="node.nodeType === '2'" type="success">结束节点</el-tag>
                {{ node.nodeName }}
              </span>
              <div class="node-actions">
                <el-button v-if="node.nodeType !== '0' && node.nodeType !== '2'" type="text" size="mini" @click="editNode(node, index)">编辑</el-button>
                <el-button v-if="node.nodeType !== '0' && node.nodeType !== '2'" type="text" size="mini" @click="removeNode(index)" style="color: #F56C6C">删除</el-button>
              </div>
            </div>
            <div class="node-content">
              <div v-if="node.nodeType === '1'">
                <div><strong>审批人：</strong>{{ getApproverDisplayName(node) }}</div>
                <div><strong>审批条件：</strong>{{ node.approvalCondition === 'ALL' ? '所有人' : '任意一人' }}</div>
              </div>
            </div>
            <div class="node-arrow" v-if="index !== form.nodes.length - 1">
              <i class="el-icon-arrow-down"></i>
            </div>
          </el-card>
          
          <el-button v-if="canAddApprovalNode" type="dashed" @click="addNode" style="width: 100%; margin-top: 10px">
            <i class="el-icon-plus"></i> 添加审批节点
          </el-button>
        </div>
        
        <el-form-item style="margin-top: 40px">
          <el-button type="primary" @click="submitForm">提交</el-button>
          <el-button @click="cancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 节点编辑对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="500px" append-to-body>
      <el-form ref="nodeForm" :model="nodeForm" :rules="nodeRules" label-width="100px">
        <el-form-item label="节点名称" prop="nodeName">
          <el-input v-model="nodeForm.nodeName" placeholder="请输入节点名称" />
        </el-form-item>
        <el-form-item label="审批人" prop="approvers">
          <el-select v-model="nodeForm.approvers" multiple filterable placeholder="请选择审批人" style="width: 100%">
            <el-option v-for="item in userOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="审批条件" prop="approvalCondition">
          <el-radio-group v-model="nodeForm.approvalCondition">
            <el-radio label="ANY">任意一人通过即可</el-radio>
            <el-radio label="ALL">所有人通过才行</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmNodeEdit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getWorkflow, updateWorkflow, getWorkflowOptions } from "@/api/approval/workflow";
import { listUser } from "@/api/system/user";

export default {
  name: "EditWorkflow",
  data() {
    return {
      // 表单参数
      form: {
        workflowId: undefined,
        workflowName: undefined,
        workflowCode: undefined,
        businessType: undefined,
        description: undefined,
        status: "0",
        nodes: []
      },
      // 表单校验规则
      rules: {
        workflowName: [
          { required: true, message: "流程名称不能为空", trigger: "blur" }
        ],
        workflowCode: [
          { required: true, message: "流程编码不能为空", trigger: "blur" }
        ],
        businessType: [
          { required: true, message: "业务类型不能为空", trigger: "change" }
        ]
      },
      // 业务类型选项
      businessTypeOptions: [],
      // 用户选项
      userOptions: [],
      // 节点表单
      nodeForm: {
        nodeName: "",
        approvers: [],
        approvalCondition: "ANY"
      },
      nodeRules: {
        nodeName: [
          { required: true, message: "节点名称不能为空", trigger: "blur" }
        ],
        approvers: [
          { required: true, message: "审批人不能为空", trigger: "change" }
        ],
        approvalCondition: [
          { required: true, message: "审批条件不能为空", trigger: "change" }
        ]
      },
      dialogVisible: false,
      dialogTitle: "添加节点",
      editingNodeIndex: -1,
      loading: false
    };
  },
  computed: {
    canAddApprovalNode() {
      // 总是允许添加审批节点，只要有节点数组
      return this.form.nodes && Array.isArray(this.form.nodes);
    }
  },
  created() {
    // 先获取需要的选项数据
    Promise.all([
      // 获取业务类型选项
      new Promise(resolve => {
        this.getBusinessTypeOptions();
        resolve();
      }),
      // 获取用户选项
      new Promise(resolve => {
        this.getUserOptions();
        resolve();
      })
    ]).then(() => {
      // 等待选项数据加载完成后再获取工作流数据
      console.log("选项数据加载完成，准备加载工作流数据...");
      setTimeout(() => {
        this.getWorkflowId();
      }, 300);
    });
  },
  mounted() {
    // 在组件挂载后添加全局保存前检查
    window.addEventListener('beforeunload', this.checkDataBeforeLeave);
  },
  beforeDestroy() {
    // 组件销毁前移除事件监听
    window.removeEventListener('beforeunload', this.checkDataBeforeLeave);
  },
  methods: {
    /** 获取流程ID */
    getWorkflowId() {
      const workflowId = this.$route.query.workflowId;
      const workflowCode = this.$route.query.workflowCode;
      const workflowName = this.$route.query.workflowName;
      const businessType = this.$route.query.businessType;
      const businessTypeName = this.$route.query.businessTypeName;
      const description = this.$route.query.description;
      const status = this.$route.query.status;
      
      if (workflowId) {
        // 统一使用字符串ID，避免Long类型转换问题
        this.form.workflowId = String(workflowId);
        
        // 初始化表单基础数据，从URL参数填充
        if (workflowName) this.form.workflowName = workflowName;
        if (workflowCode) this.form.workflowCode = workflowCode;
        if (businessType) this.form.businessType = businessType;
        if (description) this.form.description = description;
        if (status) this.form.status = status;
        
        // 如果传入了业务类型名称，确保业务类型选项中包含这个业务类型
        if (businessType && businessTypeName) {
          const hasBusinessType = this.businessTypeOptions.some(item => item.value === businessType);
          if (!hasBusinessType) {
            this.businessTypeOptions.push({
              value: businessType,
              label: businessTypeName
            });
          }
          this.form.businessTypeName = businessTypeName;
        }
        
        // 获取完整的流程详情数据
        this.getDetail();
      }
    },
    /** 获取流程详情 */
    getDetail() {
      this.loading = true; // Add loading indicator
      
      console.log("开始获取流程详情, 当前表单值:", {
        workflowId: this.form.workflowId,
        workflowName: this.form.workflowName,
        workflowCode: this.form.workflowCode,
        businessType: this.form.businessType
      });
      
      // 保存URL传入的名称，防止被后端返回的数据覆盖
      const initialWorkflowName = this.form.workflowName;
      
      getWorkflow(this.form.workflowId).then(res => {
        if (res.code === 200) {
          const data = res.data || {};
          console.log("获取到的流程详情数据:", data);
          
          // 处理基本信息 - 优先使用URL传入的数据
          this.form.workflowName = initialWorkflowName || data.workflowName;
          console.log("设置流程名称:", this.form.workflowName);
          
          // 处理节点数据，确保approvers字段和approvalCondition字段格式正确
          if (data.nodes && Array.isArray(data.nodes)) {
            console.log("原始节点数据:", data.nodes);

            data.nodes.forEach(node => {
              // 确保节点ID是字符串类型
              if (node.nodeId) {
                node.nodeId = String(node.nodeId);
              } else {
                // 如果没有节点ID，生成一个
                node.nodeId = this.generateId();
              }

              if (node.nodeType === "1") {
                console.log(`处理审批节点 ${node.nodeName}, 原始数据:`, {
                  approvers: node.approvers,
                  userIdList: node.userIdList,
                  userIds: node.userIds,
                  approverName: node.approverName
                });

                // 处理审批人数据
                if (!node.approvers || !Array.isArray(node.approvers) || node.approvers.length === 0) {
                  // 尝试从其他字段构建approvers
                  if (node.userIdList && Array.isArray(node.userIdList) && node.userIdList.length > 0) {
                    node.approvers = node.userIdList.map(id => String(id));
                    console.log(`为节点 ${node.nodeName} 从userIdList生成approvers:`, node.approvers);
                  } else if (node.userIds && node.userIds.trim() !== '') {
                    node.approvers = node.userIds.split(',').map(id => String(id.trim())).filter(id => id !== '');
                    console.log(`为节点 ${node.nodeName} 从userIds生成approvers:`, node.approvers);
                  } else {
                    // 如果没有任何审批人数据，设置一个默认值
                    console.warn(`节点 ${node.nodeName} 没有审批人数据，设置默认审批人`);
                    node.approvers = ['1']; // 默认设置admin用户
                    node.userIds = '1';
                    node.userIdList = [1];
                  }
                } else {
                  // 确保approvers中的值是字符串
                  node.approvers = node.approvers.map(id => String(id));
                }
                
                // 确保userIds和userIdList也被正确设置
                if (node.approvers && node.approvers.length > 0) {
                  node.userIds = node.approvers.join(',');
                  node.userIdList = [...node.approvers];
                }
                
                // 处理审批条件
                if (!node.approvalCondition) {
                  node.approvalCondition = "A"; // 默认为任意一人
                } else if (node.approvalCondition === "A") {
                  node.approvalCondition = "ANY";
                  console.log(`为节点 ${node.nodeName} 将approvalCondition从A修改为ANY`);
                } else if (node.approvalCondition === "L") {
                  node.approvalCondition = "ALL";
                  console.log(`为节点 ${node.nodeName} 将approvalCondition从L修改为ALL`);
                }
                
                console.log(`节点 ${node.nodeName} 处理后数据:`, {
                  approvers: node.approvers,
                  userIdList: node.userIdList,
                  userIds: node.userIds
                });
              }
            });
          }
          
          // 如果没有节点数据，创建默认节点结构
          if (!data.nodes || !Array.isArray(data.nodes) || data.nodes.length === 0) {
            data.nodes = [
              {
                nodeId: this.generateId(),
                nodeName: "开始",
                nodeType: "0",
                approvers: [],
                approverName: "",
                approvalCondition: "A",
                nextNodes: []
              }
            ];
          }
          
          // 更新表单数据 - 深拷贝以避免引用问题
          const formData = {
            ...this.form,
            ...data,
            workflowId: String(data.workflowId || this.form.workflowId), // 确保workflowId是字符串
            workflowName: this.form.workflowName || data.workflowName, // 优先使用URL传递的workflowName
            workflowCode: data.workflowCode || this.form.workflowCode, // 确保工作流编码被设置
            businessType: data.businessType || this.form.businessType, // 确保业务类型被设置
            status: data.status || this.form.status || "0", // 确保状态字段有值
            description: data.description || this.form.description || "", // 确保描述字段被设置
            nodes: data.nodes || []
          };

          this.form = JSON.parse(JSON.stringify(formData));

          // 确保节点数据完整性
          if (this.form.nodes && Array.isArray(this.form.nodes)) {
            // 检查是否有开始节点
            const hasStartNode = this.form.nodes.some(node => node.nodeType === "0");
            if (!hasStartNode) {
              console.log("添加缺失的开始节点");
              this.form.nodes.unshift({
                nodeId: this.generateId(),
                nodeName: "开始",
                nodeType: "0",
                approvers: [],
                approverName: "",
                approvalCondition: "A",
                nextNodes: []
              });
            }

            // 检查是否有结束节点
            const hasEndNode = this.form.nodes.some(node => node.nodeType === "2");
            if (!hasEndNode) {
              console.log("添加缺失的结束节点");
              this.form.nodes.push({
                nodeId: this.generateId(),
                nodeName: "结束",
                nodeType: "2",
                approvers: [],
                approverName: "",
                approvalCondition: "A",
                nextNodes: []
              });
            }

            console.log("最终节点数据:", this.form.nodes);
          } else {
            console.log("没有节点数据，初始化默认节点");
            // 如果没有节点数据，初始化默认节点
            this.form.nodes = [
              {
                nodeId: this.generateId(),
                nodeName: "开始",
                nodeType: "0", // 0-开始节点
                approvers: [],
                approverName: "",
                approvalCondition: "A",
                nextNodes: []
              },
              {
                nodeId: this.generateId(),
                nodeName: "审批节点",
                nodeType: "1", // 1-审批节点
                approvers: ['1'], // 默认admin用户
                approverName: "小强(admin)",
                approvalCondition: "A",
                userIds: "1",
                userIdList: [1],
                nextNodes: []
              },
              {
                nodeId: this.generateId(),
                nodeName: "结束",
                nodeType: "2", // 2-结束节点
                approvers: [],
                approverName: "",
                approvalCondition: "A",
                nextNodes: []
              }
            ];
          }
          
          console.log("最终设置的表单数据:", {
            workflowId: this.form.workflowId,
            workflowName: this.form.workflowName,
            workflowCode: this.form.workflowCode,
            businessType: this.form.businessType,
            status: this.form.status,
            description: this.form.description
          });

          // 确保业务类型选项包含当前业务类型
          if (this.form.businessType && this.businessTypeOptions.length > 0) {
            const hasBusinessType = this.businessTypeOptions.some(item => item.value === this.form.businessType);
            if (!hasBusinessType) {
              this.businessTypeOptions.push({
                value: this.form.businessType,
                label: this.form.businessTypeName || this.form.businessType
              });
            }
          }

          // 更新所有审批节点的审批人信息
          setTimeout(() => {
            if (this.form.nodes && Array.isArray(this.form.nodes)) {
              // 检查用户选项是否已加载
              if (this.userOptions && this.userOptions.length > 0) {
                console.log("用户选项已加载，立即更新审批人信息");
                this.form.nodes.forEach(node => {
                  if (node.nodeType === "1") {
                    this.updateNodeApprovers(node);
                  }
                });
              } else {
                console.log("用户选项尚未加载，等待用户选项加载完成后更新审批人信息");
                // 等待userOptions加载完成再更新，避免审批人名称无法显示
                // getUserOptions方法中会在数据加载后调用updateAllApproverNames
              }
            }
          }, 300);
          
          console.log("表单数据处理完成:", this.form);
          this.loading = false; // Turn off loading indicator
        } else {
          console.error("获取流程详情失败:", res.msg);
          this.$modal.msgError("获取流程详情失败: " + res.msg);
          this.loading = false; // Turn off loading indicator
        }
      }).catch(err => {
        console.error("获取流程详情出错:", err);
        this.$modal.msgError("获取流程详情出错，请稍后重试");
        this.loading = false; // Ensure loading indicator is turned off
      });
    },
    /** 获取业务类型选项 */
    getBusinessTypeOptions() {
      getWorkflowOptions().then(res => {
        if (res.code === 200 && res.data) {
          // 使用Map对象确保每个业务类型只出现一次
          const businessTypeMap = new Map();
          res.data.forEach(item => {
            if (!businessTypeMap.has(item.businessType)) {
              businessTypeMap.set(item.businessType, {
                value: item.businessType,
                label: item.businessTypeName || item.businessType
              });
            }
          });
          
          // 将Map转换回数组
          this.businessTypeOptions = Array.from(businessTypeMap.values());
        }
      });
    },
    /** 获取用户选项 */
    getUserOptions() {
      return listUser({ pageSize: 999 }).then(res => {
        if (res.code === 200) {
          this.userOptions = res.rows.map(user => {
            // 确保userId是字符串类型
            return { value: String(user.userId), label: `${user.nickName}(${user.userName})` };
          });
          console.log(`已加载 ${this.userOptions.length} 个用户选项`);
          
          // 如果表单节点已经存在，更新审批人名称
          this.updateAllApproverNames();
        }
        return res;
      });
    },
    /** 更新所有节点的审批人名称 */
    updateAllApproverNames() {
      if (this.form && this.form.nodes && Array.isArray(this.form.nodes)) {
        console.log(`开始更新所有节点的审批人名称，共 ${this.form.nodes.length} 个节点`);
        this.form.nodes.forEach(node => {
          if (node.nodeType === "1") {
            // 确保节点有审批人数据
            this.ensureNodeHasApprovers(node);
            this.updateNodeApprovers(node);
          }
        });
      }
    },
    /** 确保节点有审批人数据 */
    ensureNodeHasApprovers(node) {
      if (!node || node.nodeType !== "1") return;
      
      // 检查节点是否有任何审批人数据
      const hasApprovers = node.approvers && Array.isArray(node.approvers) && node.approvers.length > 0;
      const hasUserIds = node.userIds && node.userIds.trim() !== '';
      const hasUserIdList = node.userIdList && Array.isArray(node.userIdList) && node.userIdList.length > 0;
      
      if (!hasApprovers && !hasUserIds && !hasUserIdList) {
        console.warn(`节点 ${node.nodeName} 缺少审批人数据，尝试从服务器重新获取`);
        
        // 如果节点没有审批人数据，可以考虑以下几种方法：
        
        // 1. 从备份数据恢复（如果有）
        
        // 2. 从URL参数尝试恢复部分数据
        
        // 3. 记录警告，但不阻止继续使用
        console.warn(`节点 ${node.nodeName} 审批人数据不完整，可能需要重新编辑设置审批人`);
      }
    },
    /** 生成唯一ID */
    generateId() {
      return "node_" + new Date().getTime() + Math.floor(Math.random() * 1000);
    },
    /** 添加节点 */
    addNode() {
      this.dialogTitle = "添加审批节点";
      this.nodeForm = {
        nodeName: "审批节点",
        approvers: [],
        approvalCondition: "ANY"
      };
      this.editingNodeIndex = -1;
      this.dialogVisible = true;
    },
    /** 编辑节点 */
    editNode(node, index) {
      this.dialogTitle = "编辑审批节点";
      // 处理审批条件字段的反向映射
      let mappedCondition = "ANY";
      if (node.approvalCondition === "A") {
        mappedCondition = "ANY";
      } else if (node.approvalCondition === "L") {
        mappedCondition = "ALL";
      }
      
      console.log("编辑节点数据:", node);
      
      // 确保approvers是数组，按优先级尝试不同的来源
      let approvers = [];
      if (node.approvers && Array.isArray(node.approvers) && node.approvers.length > 0) {
        // 1. 首选已有的approvers数组
        approvers = node.approvers.map(id => String(id));
      } else if (node.userIdList && Array.isArray(node.userIdList) && node.userIdList.length > 0) {
        // 2. 其次使用userIdList
        approvers = node.userIdList.map(id => String(id));
      } else if (node.userIds) {
        // 3. 最后使用userIds字符串
        approvers = node.userIds.split(',').map(id => String(id.trim())).filter(id => id !== '');
      }
      
      console.log("处理后的approvers:", approvers);
      
      this.nodeForm = {
        nodeName: node.nodeName,
        approvers: approvers,
        approvalCondition: mappedCondition
      };
      this.editingNodeIndex = index;
      this.dialogVisible = true;
    },
    /** 移除节点 */
    removeNode(index) {
      this.$modal.confirm("确认删除该节点吗？").then(() => {
        // 删除节点
        this.form.nodes.splice(index, 1);
        
        // 如果删除了所有审批节点，需要删除结束节点
        const hasApprovalNode = this.form.nodes.some(node => node.nodeType === "1");
        if (!hasApprovalNode && this.form.nodes.length > 1) {
          // 只保留开始节点
          this.form.nodes = [this.form.nodes[0]];
        }
      }).catch(() => {});
    },
    /** 确认节点编辑 */
    confirmNodeEdit() {
      this.$refs.nodeForm.validate(valid => {
        if (valid) {
          // 检查是否有审批人
          if (!this.nodeForm.approvers || this.nodeForm.approvers.length === 0) {
            this.$modal.msgError("请选择审批人");
            return;
          }

          const approverNames = this.nodeForm.approvers.map(id => {
            const user = this.userOptions.find(u => u.value === id || u.value === String(id));
            return user ? user.label : "";
          }).filter(name => name).join(", ");
          
          // 将前端的approvalCondition值映射为后端需要的格式
          let approvalConditionValue = "A"; // 默认为A (ANY)
          if (this.nodeForm.approvalCondition === "ANY") {
            approvalConditionValue = "A";
          } else if (this.nodeForm.approvalCondition === "ALL") {
            approvalConditionValue = "L";
          }
          
          // 保持原有节点ID，如果是编辑现有节点
          const nodeId = this.editingNodeIndex >= 0 && this.form.nodes[this.editingNodeIndex]
            ? this.form.nodes[this.editingNodeIndex].nodeId
            : this.generateId();
            
          // 确保approvers中的每个ID是字符串类型
          const convertedApprovers = this.nodeForm.approvers.map(id => String(id));
          
          console.log("保存节点审批人:", convertedApprovers);
          
          const node = {
            nodeId: String(nodeId), // 确保nodeId是字符串类型
            nodeName: this.nodeForm.nodeName,
            nodeType: "1", // 1-审批节点
            approvers: convertedApprovers,
            approverName: approverNames,
            approvalCondition: approvalConditionValue, // 使用映射后的值
            nextNodes: [],
            // 添加审批类型信息，默认为指定人员
            approvalType: "0",
            // 同时设置所有相关字段，确保数据一致性
            userIds: convertedApprovers.join(','),
            userIdList: convertedApprovers,
            // 保留其他原有属性
            ...(this.editingNodeIndex >= 0 && this.form.nodes[this.editingNodeIndex]
                ? {
                    createTime: this.form.nodes[this.editingNodeIndex].createTime,
                    remark: this.form.nodes[this.editingNodeIndex].remark
                  }
                : {})
          };
          
          if (this.editingNodeIndex >= 0) {
            // 编辑现有节点
            this.form.nodes.splice(this.editingNodeIndex, 1, node);
          } else {
            // 添加新节点
            // 如果已有结束节点，则在结束节点前插入
            const endNodeIndex = this.form.nodes.findIndex(n => n.nodeType === "2");
            if (endNodeIndex > 0) {
              this.form.nodes.splice(endNodeIndex, 0, node);
            } else {
              this.form.nodes.push(node);
              
              // 如果这是第一个审批节点，需要添加结束节点
              if (this.form.nodes.length === 2) {
                this.form.nodes.push({
                  nodeId: this.generateId(),
                  nodeName: "结束",
                  nodeType: "2", // 2-结束节点
                  approvers: [],
                  approverName: "",
                  approvalCondition: "A", // 结束节点使用A
                  nextNodes: []
                });
              }
            }
          }
          
          this.dialogVisible = false;
        }
      });
    },
    /** 表单提交 */
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 检查节点是否完整
          if (this.form.nodes.length < 3) {
            this.$modal.msgError("请至少添加一个审批节点");
            return;
          }
          
          // 构建节点连接关系并确保数据格式一致性
          const processedForm = JSON.parse(JSON.stringify(this.form)); // 深拷贝，避免直接修改原对象
          
          // 确保workflowId是字符串类型
          processedForm.workflowId = String(processedForm.workflowId);
          
          for (let i = 0; i < processedForm.nodes.length - 1; i++) {
            // 确保nodeId是字符串
            processedForm.nodes[i].nodeId = String(processedForm.nodes[i].nodeId);
            
            // 确保下一个节点ID是字符串
            const nextNodeId = String(processedForm.nodes[i + 1].nodeId);
            processedForm.nodes[i].nextNodes = [nextNodeId];
            
            // 确保审批人信息格式一致
            if (processedForm.nodes[i].nodeType === '1') {
              // 处理审批节点
              if (processedForm.nodes[i].approvers && Array.isArray(processedForm.nodes[i].approvers)) {
                // 确保approvers中的值是字符串
                processedForm.nodes[i].approvers = processedForm.nodes[i].approvers.map(id => String(id));
                
                // 设置后端需要的userIds字段
                processedForm.nodes[i].userIds = processedForm.nodes[i].approvers.join(',');
                
                // 设置userIdList字段
                processedForm.nodes[i].userIdList = [...processedForm.nodes[i].approvers];
              } else if (processedForm.nodes[i].userIds) {
                // 如果没有approvers但有userIds，从userIds反推
                const userIdArray = processedForm.nodes[i].userIds.split(',').map(id => String(id.trim())).filter(id => id !== '');
                processedForm.nodes[i].approvers = userIdArray;
                processedForm.nodes[i].userIdList = userIdArray;
              } else if (processedForm.nodes[i].userIdList && Array.isArray(processedForm.nodes[i].userIdList)) {
                // 如果只有userIdList，从userIdList反推
                processedForm.nodes[i].approvers = processedForm.nodes[i].userIdList.map(id => String(id));
                processedForm.nodes[i].userIds = processedForm.nodes[i].approvers.join(',');
              }
            }
            
            // 修复approval_condition字段，确保只有单个字符
            if (processedForm.nodes[i].approvalCondition) {
              if (processedForm.nodes[i].approvalCondition === 'ANY') {
                processedForm.nodes[i].approvalCondition = 'A';
              } else if (processedForm.nodes[i].approvalCondition === 'ALL') {
                processedForm.nodes[i].approvalCondition = 'L';
              } else if (processedForm.nodes[i].approvalCondition.length > 1) {
                // 取第一个字符
                processedForm.nodes[i].approvalCondition = processedForm.nodes[i].approvalCondition.charAt(0);
              }
            }
          }
          
          // 处理最后一个节点
          if (processedForm.nodes.length > 0) {
            const lastIndex = processedForm.nodes.length - 1;
            processedForm.nodes[lastIndex].nodeId = String(processedForm.nodes[lastIndex].nodeId);
            
            if (processedForm.nodes[lastIndex].nodeType === '1') {
              if (processedForm.nodes[lastIndex].approvers && Array.isArray(processedForm.nodes[lastIndex].approvers)) {
                processedForm.nodes[lastIndex].approvers = processedForm.nodes[lastIndex].approvers.map(id => String(id));
                processedForm.nodes[lastIndex].userIds = processedForm.nodes[lastIndex].approvers.join(',');
                processedForm.nodes[lastIndex].userIdList = [...processedForm.nodes[lastIndex].approvers];
              } else if (processedForm.nodes[lastIndex].userIds) {
                const userIdArray = processedForm.nodes[lastIndex].userIds.split(',').map(id => String(id.trim())).filter(id => id !== '');
                processedForm.nodes[lastIndex].approvers = userIdArray;
                processedForm.nodes[lastIndex].userIdList = userIdArray;
              } else if (processedForm.nodes[lastIndex].userIdList && Array.isArray(processedForm.nodes[lastIndex].userIdList)) {
                processedForm.nodes[lastIndex].approvers = processedForm.nodes[lastIndex].userIdList.map(id => String(id));
                processedForm.nodes[lastIndex].userIds = processedForm.nodes[lastIndex].approvers.join(',');
              }
            }
            
            // 修复最后一个节点的approval_condition字段
            if (processedForm.nodes[lastIndex].approvalCondition) {
              if (processedForm.nodes[lastIndex].approvalCondition === 'ANY') {
                processedForm.nodes[lastIndex].approvalCondition = 'A';
              } else if (processedForm.nodes[lastIndex].approvalCondition === 'ALL') {
                processedForm.nodes[lastIndex].approvalCondition = 'L';
              } else if (processedForm.nodes[lastIndex].approvalCondition.length > 1) {
                // 取第一个字符
                processedForm.nodes[lastIndex].approvalCondition = processedForm.nodes[lastIndex].approvalCondition.charAt(0);
              }
            }
          }
          
          // 确保所有审批节点包含审批人信息
          let hasInvalidNode = false;
          for (const node of processedForm.nodes) {
            if (node.nodeType === '1' && (!node.userIds || node.userIds === '')) {
              hasInvalidNode = true;
              this.$modal.msgError(`节点"${node.nodeName}"缺少审批人信息，请编辑后重试`);
              return;
            }
          }
          
          if (hasInvalidNode) {
            return;
          }
          
          // 打印表单数据，便于调试
          console.log("提交表单数据:", JSON.stringify(processedForm));
          
          // 提交表单
          updateWorkflow(processedForm).then(res => {
            if (res.code === 200) {
              this.$modal.msgSuccess("修改成功");
              this.cancel();
            } else {
              this.$modal.msgError(res.msg || "保存失败");
            }
          }).catch(err => {
            console.error("保存流程定义出错", err);
            this.$modal.msgError("保存失败，请稍后重试");
          });
        }
      });
    },
    /** 取消按钮 */
    cancel() {
      // 直接跳转到流程定义列表页面
      this.$router.push('/approval/workflow');
    },
    /** 检查数据完整性 */
    checkDataBeforeLeave() {
      // 检查是否有节点的审批人数据不完整
      let hasIncompleteData = false;
      if (this.form && this.form.nodes) {
        for (const node of this.form.nodes) {
          if (node.nodeType === '1') {
            if (
              (!node.approvers || node.approvers.length === 0) && 
              (!node.userIds || node.userIds === '') &&
              (!node.userIdList || node.userIdList.length === 0)
            ) {
              console.error(`发现数据不完整的节点: ${node.nodeName}`, node);
              hasIncompleteData = true;
            }
          }
        }
      }
      
      if (hasIncompleteData) {
        console.error('警告: 部分审批节点数据不完整，可能导致保存后数据丢失');
      }
      
      // 这只是用于日志记录，不阻止用户离开页面
    },
    /** 更新节点审批人信息 */
    updateNodeApprovers(node) {
      if (!node || node.nodeType !== "1") return;
      
      console.log(`开始处理节点 ${node.nodeName} 的审批人信息:`, {
        approvers: node.approvers,
        userIds: node.userIds,
        userIdList: node.userIdList
      });
      
      // 确保有审批人信息，优先使用approvers字段
      let hasUpdated = false;
      
      if (node.approvers && Array.isArray(node.approvers) && node.approvers.length > 0) {
        // 从approvers更新其他字段
        node.userIds = node.approvers.join(',');
        node.userIdList = [...node.approvers].map(id => String(id));
        hasUpdated = true;
      } else if (node.userIdList && Array.isArray(node.userIdList) && node.userIdList.length > 0) {
        // 从userIdList更新approvers
        node.approvers = [...node.userIdList].map(id => String(id));
        node.userIds = node.approvers.join(',');
        hasUpdated = true;
      } else if (node.userIds && node.userIds.trim() !== '') {
        // 从userIds更新approvers
        node.approvers = node.userIds.split(',').map(id => String(id.trim())).filter(id => id !== '');
        node.userIdList = [...node.approvers];
        hasUpdated = true;
      }

      if (hasUpdated) {
        // 更新审批人名称
        this.updateApproverName(node);
        
        console.log(`节点 ${node.nodeName} 审批人信息更新完成:`, {
          approvers: node.approvers,
          userIds: node.userIds,
          userIdList: node.userIdList,
          approverName: node.approverName
        });
      } else {
        console.warn(`节点 ${node.nodeName} 没有有效的审批人信息`);
      }
    },
    
    /** 更新审批人名称 */
    updateApproverName(node) {
      if (!node || !node.approvers) return;

      // 确保approvers是数组
      if (!Array.isArray(node.approvers)) {
        if (typeof node.approvers === 'string') {
          node.approvers = node.approvers.split(',').map(id => String(id.trim())).filter(id => id !== '');
        } else {
          node.approvers = [];
        }
      }
      
      if (node.approvers.length === 0) return;
      
      // 判断用户选项是否已加载
      if (!this.userOptions || this.userOptions.length === 0) {
        console.warn('用户选项尚未加载，无法更新审批人名称');
        return;
      }
      
      // 根据审批人ID查找名称
      const approverNames = [];
      node.approvers.forEach(id => {
        const user = this.userOptions.find(u => u.value === id || u.value === String(id));
        if (user) {
          approverNames.push(user.label);
        } else {
          console.warn(`找不到ID为${id}的审批人信息`);
        }
      });
      
      if (approverNames.length > 0) {
        // 使用Vue.set确保响应式更新
        this.$set(node, 'approverName', approverNames.join(', '));
        console.log(`更新节点 ${node.nodeName} 的审批人名称为: ${node.approverName}`);

        // 强制更新视图
        this.$forceUpdate();
      } else {
        console.warn(`无法找到节点 ${node.nodeName} 的任何审批人名称`);
      }
    },

    /** 获取审批人显示名称 */
    getApproverDisplayName(node) {
      if (!node || node.nodeType !== '1') return '';

      // 优先使用approverName
      if (node.approverName && node.approverName.trim() !== '') {
        return node.approverName;
      }

      // 如果没有approverName，尝试从approvers和userOptions构建
      if (node.approvers && Array.isArray(node.approvers) && node.approvers.length > 0 && this.userOptions && this.userOptions.length > 0) {
        const approverNames = [];
        node.approvers.forEach(id => {
          const user = this.userOptions.find(u => u.value === id || u.value === String(id));
          if (user) {
            approverNames.push(user.label);
          }
        });
        if (approverNames.length > 0) {
          return approverNames.join(', ');
        }
      }

      // 如果还是没有，显示用户ID
      if (node.userIds && node.userIds.trim() !== '') {
        return `用户ID: ${node.userIds}`;
      }

      return '未设置';
    },
  }
};
</script>

<style scoped>
.workflow-nodes {
  margin-top: 20px;
}

.node-card {
  margin-bottom: 10px;
  position: relative;
}

.node-start {
  border-left: 4px solid #409EFF;
}

.node-approval {
  border-left: 4px solid #909399;
}

.node-end {
  border-left: 4px solid #67C23A;
}

.node-actions {
  float: right;
}

.node-content {
  padding: 10px 0;
}

.node-arrow {
  text-align: center;
  margin: 10px 0;
  color: #909399;
  font-size: 20px;
}

.el-button[type="dashed"] {
  border-style: dashed;
}
</style> 