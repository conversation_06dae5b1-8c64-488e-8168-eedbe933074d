<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="设备编码" prop="deviceCode">
        <el-input
          v-model="queryParams.deviceCode"
          placeholder="请输入设备编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备名称" prop="deviceName">
        <el-input
          v-model="queryParams.deviceName"
          placeholder="请输入设备名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="连接状态" prop="connectionStatus">
        <el-select v-model="queryParams.connectionStatus" placeholder="请选择连接状态" clearable>
          <el-option label="在线" value="1" />
          <el-option label="离线" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-connection"
          size="mini"
          :disabled="multiple"
          @click="handleBatchLogin"
          v-hasPermi="['access:control:login']"
        >批量登录</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-unlock"
          size="mini"
          :disabled="multiple"
          @click="handleBatchOpen"
          v-hasPermi="['access:control:batchOpen']"
        >批量开门</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-refresh"
          size="mini"
          @click="handleRefreshStatus"
          v-hasPermi="['access:control:status']"
        >刷新状态</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['access:control:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="deviceList" @selection-change="handleSelectionChange" row-key="id">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="设备编码" align="center" prop="deviceCode" width="120" />
      <el-table-column label="设备名称" align="center" prop="deviceName" width="150" :show-overflow-tooltip="true" />
      <el-table-column label="设备类型" align="center" prop="deviceType" width="120">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.access_device_type" :value="scope.row.deviceType"/>
        </template>
      </el-table-column>
      <el-table-column label="IP地址" align="center" prop="ipAddress" width="120" />
      <el-table-column label="端口" align="center" prop="port" width="80" />
      <el-table-column label="连接状态" align="center" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.connectionStatus === '1' ? 'success' : 'danger'">
            {{ scope.row.connectionStatus === '1' ? '在线' : '离线' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="安装位置" align="center" prop="installLocation" width="150" :show-overflow-tooltip="true" />
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="300">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.connectionStatus !== '1'"
            size="mini"
            type="success"
            icon="el-icon-connection"
            @click="handleLogin(scope.row)"
            v-hasPermi="['access:control:login']"
          >登录</el-button>
          <el-button
            v-if="scope.row.connectionStatus === '1'"
            size="mini"
            type="warning"
            icon="el-icon-close"
            @click="handleLogout(scope.row)"
            v-hasPermi="['access:control:logout']"
          >登出</el-button>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-unlock"
            @click="handleOpenDoor(scope.row)"
            v-hasPermi="['access:control:open']"
          >开门</el-button>
          <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)" v-hasPermi="['access:control:reboot']">
            <el-button size="mini" type="text">
              更多<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="reboot" icon="el-icon-refresh-right">重启设备</el-dropdown-item>
              <el-dropdown-item command="syncTime" icon="el-icon-time">同步时间</el-dropdown-item>
              <el-dropdown-item command="startListener" icon="el-icon-video-play">启动监听</el-dropdown-item>
              <el-dropdown-item command="stopListener" icon="el-icon-video-pause">停止监听</el-dropdown-item>
              <el-dropdown-item command="faceConfig" icon="el-icon-user">人脸配置</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 开门对话框 -->
    <el-dialog title="远程开门" :visible.sync="openDoorVisible" width="400px" append-to-body>
      <el-form ref="doorForm" :model="doorForm" :rules="doorRules" label-width="80px">
        <el-form-item label="设备名称">
          <el-input v-model="doorForm.deviceName" disabled />
        </el-form-item>
        <el-form-item label="门号" prop="doorNumber">
          <el-input-number v-model="doorForm.doorNumber" :min="1" :max="4" placeholder="请输入门号" />
        </el-form-item>
        <el-form-item label="开门时长" prop="duration">
          <el-input-number v-model="doorForm.duration" :min="1" :max="60" placeholder="秒" />
          <span style="margin-left: 10px;">秒</span>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmOpenDoor">确 定</el-button>
        <el-button @click="openDoorVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 人脸识别配置对话框 -->
    <el-dialog title="人脸识别配置" :visible.sync="faceConfigVisible" width="600px" append-to-body>
      <el-form ref="faceForm" :model="faceForm" label-width="120px">
        <el-form-item label="识别阈值">
          <el-slider v-model="faceForm.threshold" :min="0.1" :max="1.0" :step="0.01" show-input />
        </el-form-item>
        <el-form-item label="活体检测">
          <el-switch v-model="faceForm.livenessDetection" />
        </el-form-item>
        <el-form-item label="口罩检测">
          <el-switch v-model="faceForm.maskDetection" />
        </el-form-item>
        <el-form-item label="体温检测">
          <el-switch v-model="faceForm.temperatureDetection" />
        </el-form-item>
        <el-form-item label="最大人脸数">
          <el-input-number v-model="faceForm.maxFaceCount" :min="1" :max="10" />
        </el-form-item>
        <el-form-item label="识别超时">
          <el-input-number v-model="faceForm.recognitionTimeout" :min="1" :max="30" />
          <span style="margin-left: 10px;">秒</span>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmFaceConfig">确 定</el-button>
        <el-button @click="faceConfigVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDevice } from "@/api/access/device";

export default {
  name: "AccessDeviceControl",
  dicts: ['sys_normal_disable', 'access_device_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设备表格数据
      deviceList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceCode: null,
        deviceName: null,
        connectionStatus: null
      },
      // 开门对话框
      openDoorVisible: false,
      doorForm: {
        deviceCode: null,
        deviceName: null,
        doorNumber: 1,
        duration: 5
      },
      doorRules: {
        doorNumber: [
          { required: true, message: "门号不能为空", trigger: "blur" }
        ]
      },
      // 人脸配置对话框
      faceConfigVisible: false,
      faceForm: {
        deviceCode: null,
        threshold: 0.8,
        livenessDetection: true,
        maskDetection: false,
        temperatureDetection: false,
        maxFaceCount: 5,
        recognitionTimeout: 10
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询设备列表 */
    getList() {
      this.loading = true;
      
      // 临时使用模拟数据
      setTimeout(() => {
        this.deviceList = [
          {
            id: 1,
            deviceCode: 'ACCESS001',
            deviceName: '人脸门禁设备-001',
            deviceType: 'face_recognition',
            ipAddress: '*************',
            port: 8000,
            connectionStatus: '1',
            installLocation: '主入口',
            status: '0'
          },
          {
            id: 2,
            deviceCode: 'ACCESS002',
            deviceName: '人脸门禁设备-002',
            deviceType: 'face_recognition',
            ipAddress: '*************',
            port: 8000,
            connectionStatus: '0',
            installLocation: '侧门',
            status: '0'
          },
          {
            id: 3,
            deviceCode: 'ACCESS003',
            deviceName: '刷卡门禁设备-003',
            deviceType: 'card_reader',
            ipAddress: '*************',
            port: 8000,
            connectionStatus: '1',
            installLocation: '后门',
            status: '0'
          }
        ];
        this.total = 3;
        this.loading = false;
      }, 500);
      
      // 正式版本使用以下代码：
      // listDevice(this.queryParams).then(response => {
      //   this.deviceList = response.rows;
      //   this.total = response.total;
      //   this.loading = false;
      // });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 设备登录 */
    handleLogin(row) {
      this.$modal.loading("正在登录设备...");
      
      // 临时模拟登录成功
      setTimeout(() => {
        this.$modal.closeLoading();
        this.$modal.msgSuccess("设备登录成功");
        row.connectionStatus = '1';
      }, 1000);
    },
    /** 设备登出 */
    handleLogout(row) {
      this.$modal.confirm('是否确认登出设备"' + row.deviceName + '"？').then(() => {
        // 临时模拟登出成功
        row.connectionStatus = '0';
        this.$modal.msgSuccess("设备登出成功");
      }).catch(() => {});
    },
    /** 开门操作 */
    handleOpenDoor(row) {
      this.doorForm.deviceCode = row.deviceCode;
      this.doorForm.deviceName = row.deviceName;
      this.doorForm.doorNumber = 1;
      this.doorForm.duration = 5;
      this.openDoorVisible = true;
    },
    /** 确认开门 */
    confirmOpenDoor() {
      this.$refs["doorForm"].validate(valid => {
        if (valid) {
          this.$modal.loading("正在开门...");
          
          // 临时模拟开门成功
          setTimeout(() => {
            this.$modal.closeLoading();
            this.$modal.msgSuccess("开门成功");
            this.openDoorVisible = false;
          }, 1000);
        }
      });
    },
    /** 批量登录 */
    handleBatchLogin() {
      const devices = this.deviceList.filter(item => this.ids.includes(item.id));
      if (devices.length === 0) {
        this.$modal.msgError("请选择要登录的设备");
        return;
      }
      
      this.$modal.loading("正在批量登录设备...");
      
      // 临时模拟批量登录
      setTimeout(() => {
        devices.forEach(device => {
          device.connectionStatus = '1';
        });
        this.$modal.closeLoading();
        this.$modal.msgSuccess(`批量登录完成：成功 ${devices.length} 个`);
      }, 2000);
    },
    /** 批量开门 */
    handleBatchOpen() {
      const devices = this.deviceList.filter(item => this.ids.includes(item.id));
      if (devices.length === 0) {
        this.$modal.msgError("请选择要开门的设备");
        return;
      }
      
      this.$modal.loading("正在批量开门...");
      
      // 临时模拟批量开门
      setTimeout(() => {
        this.$modal.closeLoading();
        this.$modal.msgSuccess(`批量开门完成：成功 ${devices.length} 个`);
      }, 2000);
    },
    /** 刷新状态 */
    handleRefreshStatus() {
      this.$modal.loading("正在刷新设备状态...");
      
      // 临时模拟刷新状态
      setTimeout(() => {
        this.$modal.closeLoading();
        this.getList();
        this.$modal.msgSuccess("状态刷新成功");
      }, 1000);
    },
    /** 更多操作 */
    handleCommand(command, row) {
      switch (command) {
        case 'reboot':
          this.handleReboot(row);
          break;
        case 'syncTime':
          this.handleSyncTime(row);
          break;
        case 'startListener':
          this.handleStartListener(row);
          break;
        case 'stopListener':
          this.handleStopListener(row);
          break;
        case 'faceConfig':
          this.handleFaceConfig(row);
          break;
      }
    },
    /** 重启设备 */
    handleReboot(row) {
      this.$modal.confirm('是否确认重启设备"' + row.deviceName + '"？').then(() => {
        this.$modal.msgSuccess("设备重启成功");
      }).catch(() => {});
    },
    /** 同步时间 */
    handleSyncTime(row) {
      this.$modal.msgSuccess("时间同步成功");
    },
    /** 启动监听 */
    handleStartListener(row) {
      this.$modal.msgSuccess("事件监听启动成功");
    },
    /** 停止监听 */
    handleStopListener(row) {
      this.$modal.msgSuccess("事件监听停止成功");
    },
    /** 人脸配置 */
    handleFaceConfig(row) {
      this.faceForm.deviceCode = row.deviceCode;
      this.faceConfigVisible = true;
    },
    /** 确认人脸配置 */
    confirmFaceConfig() {
      this.$modal.msgSuccess("人脸识别配置成功");
      this.faceConfigVisible = false;
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('access/deviceControl/export', {
        ...this.queryParams
      }, `access_device_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
