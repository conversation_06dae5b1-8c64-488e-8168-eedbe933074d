import request from '@/utils/request'

// 服务器注册管理API

/**
 * 获取已注册的MQTT服务端列表
 */
export function getMqttServers() {
  return request({
    url: '/emqx/server-registry/mqtt/servers',
    method: 'get'
  })
}

/**
 * 获取所有已注册的服务端
 */
export function getAllRegisteredServers() {
  return request({
    url: '/emqx/server-registry/servers',
    method: 'get'
  })
}

/**
 * 获取服务端统计信息
 */
export function getServerStats() {
  return request({
    url: '/emqx/server-registry/stats/servers',
    method: 'get'
  })
}

/**
 * 手动注册MQTT服务端
 */
export function registerMqttServer(data) {
  return request({
    url: '/emqx/server-registry/mqtt/register',
    method: 'post',
    data: data
  })
}

/**
 * 注销MQTT服务端
 */
export function unregisterMqttServer(serverId) {
  return request({
    url: `/emqx/server-registry/mqtt/unregister/${serverId}`,
    method: 'post'
  })
}

/**
 * 更新服务端状态
 */
export function updateServerStatus(serverId, status) {
  return request({
    url: `/emqx/server-registry/server/${serverId}/status`,
    method: 'put',
    data: { status }
  })
}

/**
 * 获取服务端详细信息
 */
export function getServerDetail(serverId) {
  return request({
    url: `/emqx/server-registry/server/${serverId}`,
    method: 'get'
  })
}

/**
 * 测试服务端连接
 */
export function testServerConnection(serverId) {
  return request({
    url: `/emqx/server-registry/server/${serverId}/test`,
    method: 'post'
  })
}
