<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="模板名称" prop="templateName">
        <el-input
          v-model="queryParams.templateName"
          placeholder="请输入模板名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="模板类型" prop="templateType">
        <el-select v-model="queryParams.templateType" placeholder="请选择模板类型" clearable>
          <el-option label="出入库" value="inout" />
          <el-option label="盘点" value="inventory" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="启用" value="0" />
          <el-option label="停用" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['inout:printTemplate:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['inout:printTemplate:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['inout:printTemplate:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['inout:printTemplate:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="printTemplateList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="模板名称" align="center" prop="templateName" />
      <el-table-column label="模板编码" align="center" prop="templateCode" />
      <el-table-column label="模板类型" align="center" prop="templateType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.print_template_type" :value="scope.row.templateType"/>
        </template>
      </el-table-column>
      <el-table-column label="纸张大小" align="center" prop="paperSize" />
      <el-table-column label="打印方向" align="center" prop="orientation">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.print_orientation" :value="scope.row.orientation"/>
        </template>
      </el-table-column>
      <el-table-column label="默认模板" align="center" prop="isDefault">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isDefault" type="success">是</el-tag>
          <el-tag v-else type="info">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handlePreview(scope.row)"
            v-hasPermi="['inout:printTemplate:query']"
          >预览</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['inout:printTemplate:edit']"
          >修改</el-button>
          <el-button
            v-if="!scope.row.isDefault"
            size="mini"
            type="text"
            icon="el-icon-star-off"
            @click="handleSetDefault(scope.row)"
            v-hasPermi="['inout:printTemplate:edit']"
          >设为默认</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['inout:printTemplate:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改打印模板配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1400px" append-to-body class="template-config-dialog">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" size="small">
        <!-- 基本信息 -->
        <el-card shadow="never" style="margin-bottom: 15px;">
          <div slot="header" class="card-header">
            <span>基本信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="模板名称" prop="templateName">
                <el-input v-model="form.templateName" placeholder="请输入模板名称" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="模板编码" prop="templateCode">
                <el-input v-model="form.templateCode" placeholder="请输入模板编码" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="模板类型" prop="templateType">
                <el-select v-model="form.templateType" placeholder="请选择模板类型" style="width: 100%;">
                  <el-option label="出入库" value="inout" />
                  <el-option label="盘点" value="inventory" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 页面设置 -->
        <el-card shadow="never" style="margin-bottom: 15px;">
          <div slot="header" class="card-header">
            <span>页面设置</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="纸张大小" prop="paperSize">
                <el-select v-model="form.paperSize" placeholder="请选择纸张大小" style="width: 100%;">
                  <el-option label="A4 (210×297mm)" value="A4" />
                  <el-option label="A3 (297×420mm)" value="A3" />
                  <el-option label="A5 (148×210mm)" value="A5" />
                  <el-option label="Letter (216×279mm)" value="Letter" />
                  <el-option label="Legal (216×356mm)" value="Legal" />
                  <el-option label="B4 (250×353mm)" value="B4" />
                  <el-option label="B5 (176×250mm)" value="B5" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="打印方向" prop="orientation">
                <el-select v-model="form.orientation" placeholder="请选择打印方向" style="width: 100%;">
                  <el-option label="纵向 (Portrait)" value="portrait" />
                  <el-option label="横向 (Landscape)" value="landscape" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="页面利用模式" prop="pageSizeMode">
                <el-select v-model="form.pageSizeMode" placeholder="选择页面模式" style="width: 100%;">
                  <el-option label="标准尺寸" value="fixed" />
                  <el-option label="充分利用" value="fullpage" />
                  <el-option label="自适应" value="adaptive" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="默认缩放" prop="defaultScale">
                <el-input-number
                  v-model="form.defaultScale"
                  :min="50"
                  :max="200"
                  :step="10"
                  placeholder="缩放比例"
                  style="width: 100%;"
                />
                <span style="margin-left: 5px; color: #999; font-size: 12px;">%</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 字体和样式设置 -->
        <el-card shadow="never" style="margin-bottom: 15px;">
          <div slot="header" class="card-header">
            <span>字体和样式设置</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="字体系列" prop="fontFamily">
                <el-select v-model="form.fontFamily" placeholder="选择字体" style="width: 100%;">
                  <el-option label="微软雅黑" value="Microsoft YaHei" />
                  <el-option label="宋体" value="SimSun" />
                  <el-option label="黑体" value="SimHei" />
                  <el-option label="楷体" value="KaiTi" />
                  <el-option label="仿宋" value="FangSong" />
                  <el-option label="Arial" value="Arial" />
                  <el-option label="Times New Roman" value="Times New Roman" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="字体大小" prop="fontSize">
                <el-input-number
                  v-model="form.fontSize"
                  :min="8"
                  :max="72"
                  placeholder="字体大小"
                  style="width: 100%;"
                />
                <span style="margin-left: 5px; color: #999; font-size: 12px;">px</span>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="行间距" prop="lineHeight">
                <el-input-number
                  v-model="form.lineHeight"
                  :min="1.0"
                  :max="3.0"
                  :step="0.1"
                  :precision="1"
                  placeholder="行间距"
                  style="width: 100%;"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="颜色模式" prop="colorMode">
                <el-select v-model="form.colorMode" placeholder="选择颜色模式" style="width: 100%;">
                  <el-option label="彩色" value="color" />
                  <el-option label="灰度" value="grayscale" />
                  <el-option label="黑白" value="monochrome" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 边距设置 -->
        <el-card shadow="never" style="margin-bottom: 15px;">
          <div slot="header" class="card-header">
            <span>边距设置 (mm)</span>
            <el-button
              size="mini"
              type="text"
              @click="setOptimalMargins"
              style="float: right; margin-top: -2px;"
            >
              <i class="el-icon-magic-stick"></i> 智能优化
            </el-button>
          </div>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="上边距" prop="marginTop">
                <el-input-number
                  v-model="form.marginTop"
                  :min="0"
                  :max="50"
                  :precision="1"
                  placeholder="上边距"
                  style="width: 100%;"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="下边距" prop="marginBottom">
                <el-input-number
                  v-model="form.marginBottom"
                  :min="0"
                  :max="50"
                  :precision="1"
                  placeholder="下边距"
                  style="width: 100%;"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="左边距" prop="marginLeft">
                <el-input-number
                  v-model="form.marginLeft"
                  :min="0"
                  :max="50"
                  :precision="1"
                  placeholder="左边距"
                  style="width: 100%;"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="右边距" prop="marginRight">
                <el-input-number
                  v-model="form.marginRight"
                  :min="0"
                  :max="50"
                  :precision="1"
                  placeholder="右边距"
                  style="width: 100%;"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="页眉高度" prop="headerHeight">
                <el-input-number
                  v-model="form.headerHeight"
                  :min="0"
                  :max="100"
                  :precision="1"
                  placeholder="页眉高度"
                  style="width: 100%;"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="页脚高度" prop="footerHeight">
                <el-input-number
                  v-model="form.footerHeight"
                  :min="0"
                  :max="100"
                  :precision="1"
                  placeholder="页脚高度"
                  style="width: 100%;"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <div style="background: #f5f7fa; padding: 10px; border-radius: 4px; font-size: 12px; color: #666;">
                <i class="el-icon-info"></i>
                <strong>提示：</strong>
                充分利用模式建议边距设置为 3-8mm，标准模式建议 10-20mm
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 显示选项和高级设置 -->
        <el-card shadow="never" style="margin-bottom: 15px;">
          <div slot="header" class="card-header">
            <span>显示选项和高级设置</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="显示页眉">
                <el-switch v-model="form.showHeader" active-text="是" inactive-text="否"></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="显示页脚">
                <el-switch v-model="form.showFooter" active-text="是" inactive-text="否"></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="显示页码">
                <el-switch v-model="form.showPageNumber" active-text="是" inactive-text="否"></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="显示打印时间">
                <el-switch v-model="form.showPrintTime" active-text="是" inactive-text="否"></el-switch>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="显示二维码">
                <el-switch v-model="form.showQrCode" active-text="是" inactive-text="否"></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="打印背景">
                <el-switch v-model="form.printBackground" active-text="是" inactive-text="否"></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="自适应页面">
                <el-switch v-model="form.fitToPage" active-text="是" inactive-text="否"></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="表格边框">
                <el-switch v-model="form.showTableBorder" active-text="显示" inactive-text="隐藏"></el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 模板内容 -->
        <el-card shadow="never" style="margin-bottom: 15px;">
          <div slot="header" class="card-header">
            <span>模板内容</span>
            <div style="float: right;">
              <el-button size="mini" type="text" @click="insertVariable">
                <i class="el-icon-plus"></i> 插入变量
              </el-button>
              <el-button size="mini" type="text" @click="showVariableHelp">
                <i class="el-icon-question"></i> 变量说明
              </el-button>
            </div>
          </div>
          <el-tabs v-model="activeTab" type="card">
            <el-tab-pane label="页眉模板" name="header" v-if="form.showHeader">
              <el-form-item>
                <el-input
                  v-model="form.headerTemplate"
                  type="textarea"
                  :rows="5"
                  placeholder="请输入页眉模板内容，支持HTML和变量{variable}
常用变量：{companyName} {documentTitle} {printTime} {pageNumber}"
                  show-word-limit
                  maxlength="2000"
                />
              </el-form-item>
            </el-tab-pane>
            <el-tab-pane label="主体模板" name="body">
              <el-form-item>
                <el-input
                  v-model="form.bodyTemplate"
                  type="textarea"
                  :rows="10"
                  placeholder="请输入主体模板内容，支持HTML和变量{variable}
常用变量：{billNo} {applicantName} {businessType} {materialList}"
                  show-word-limit
                  maxlength="10000"
                />
              </el-form-item>
            </el-tab-pane>
            <el-tab-pane label="页脚模板" name="footer" v-if="form.showFooter">
              <el-form-item>
                <el-input
                  v-model="form.footerTemplate"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入页脚模板内容，支持HTML和变量{variable}
常用变量：{printTime} {pageNumber} {totalPages} {operator}"
                  show-word-limit
                  maxlength="1000"
                />
              </el-form-item>
            </el-tab-pane>
            <el-tab-pane label="自定义样式" name="css">
              <el-form-item>
                <el-input
                  v-model="form.cssStyles"
                  type="textarea"
                  :rows="8"
                  placeholder="请输入自定义CSS样式
例如：
table { width: 100% !important; }
th { background-color: #f5f5f5; }
.header { font-size: 18px; font-weight: bold; }"
                  show-word-limit
                  maxlength="5000"
                />
              </el-form-item>
            </el-tab-pane>
          </el-tabs>
        </el-card>

        <!-- 其他设置 -->
        <el-card shadow="never">
          <div slot="header" class="card-header">
            <span>其他设置</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="设为默认模板">
                <el-switch
                  v-model="form.isDefault"
                  active-text="是"
                  inactive-text="否"
                  active-color="#13ce66"
                ></el-switch>
                <div style="font-size: 12px; color: #999; margin-top: 5px;">
                  默认模板将在新建打印时自动选择
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="模板状态">
                <el-radio-group v-model="form.status">
                  <el-radio label="0">
                    <i class="el-icon-check" style="color: #67c23a;"></i> 启用
                  </el-radio>
                  <el-radio label="1">
                    <i class="el-icon-close" style="color: #f56c6c;"></i> 停用
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="模板版本" prop="version">
                <el-input
                  v-model="form.version"
                  placeholder="如：v1.0"
                  style="width: 100%;"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="备注说明" prop="remark">
                <el-input
                  v-model="form.remark"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入模板的用途说明、注意事项等备注信息"
                  show-word-limit
                  maxlength="500"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">
          <i class="el-icon-close"></i> 取消
        </el-button>
        <el-button type="info" @click="previewTemplate" :disabled="!form.bodyTemplate">
          <i class="el-icon-view"></i> 预览
        </el-button>
        <el-button type="success" @click="saveAndPreview">
          <i class="el-icon-document-checked"></i> 保存并预览
        </el-button>
        <el-button type="primary" @click="submitForm">
          <i class="el-icon-check"></i> 确定
        </el-button>
      </div>
    </el-dialog>

    <!-- 模板预览对话框 -->
    <el-dialog title="模板预览" :visible.sync="previewVisible" width="90%" append-to-body>
      <div class="preview-toolbar">
        <div class="toolbar-left">
          <el-button size="small" type="primary" icon="el-icon-view" @click="openPreviewInNewWindow">在新窗口预览</el-button>
          <el-button size="small" icon="el-icon-refresh" @click="refreshPreview">手动刷新</el-button>
        </div>
        <div class="toolbar-right">
          <el-tag size="mini" type="success">
            <i class="el-icon-success"></i> 实时预览已启用
          </el-tag>
          <span class="preview-tip">修改参数后将自动更新预览</span>
        </div>
      </div>
      <div class="print-preview-container">
        <div class="iframe-preview">
          <iframe
            ref="previewFrame"
            :srcdoc="previewBodyContent"
            style="width: 100%; height: 500px; border: 1px solid #ddd; border-radius: 4px;">
          </iframe>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="previewVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPrintTemplate, getPrintTemplate, delPrintTemplate, addPrintTemplate, updatePrintTemplate, setDefaultTemplate } from "@/api/inout/printTemplate";

export default {
  name: "PrintTemplate",
  dicts: ['print_template_type', 'print_orientation', 'sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 打印模板配置表格数据
      printTemplateList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 预览对话框
      previewVisible: false,
      previewContent: "",
      previewBodyContent: "",
      currentPreviewTemplate: null,
      // 防抖定时器
      refreshTimer: null,
      // 活动标签页
      activeTab: "body",
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        templateName: null,
        templateCode: null,
        templateType: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        templateName: [
          { required: true, message: "模板名称不能为空", trigger: "blur" }
        ],
        templateCode: [
          { required: true, message: "模板编码不能为空", trigger: "blur" }
        ],
        templateType: [
          { required: true, message: "模板类型不能为空", trigger: "change" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  beforeDestroy() {
    // 清理定时器
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }
  },
  watch: {
    // 监听表单参数变化，自动刷新预览
    'form.pageSize': {
      handler() {
        this.debounceRefreshPreview();
      },
      deep: true
    },
    'form.orientation': {
      handler() {
        this.debounceRefreshPreview();
      }
    },
    'form.marginTop': {
      handler() {
        this.debounceRefreshPreview();
      }
    },
    'form.marginBottom': {
      handler() {
        this.debounceRefreshPreview();
      }
    },
    'form.marginLeft': {
      handler() {
        this.debounceRefreshPreview();
      }
    },
    'form.marginRight': {
      handler() {
        this.debounceRefreshPreview();
      }
    },
    'form.fontSize': {
      handler() {
        this.debounceRefreshPreview();
      }
    },
    'form.fontFamily': {
      handler() {
        this.debounceRefreshPreview();
      }
    },
    'form.lineHeight': {
      handler() {
        this.debounceRefreshPreview();
      }
    },
    'form.showHeader': {
      handler() {
        this.debounceRefreshPreview();
      }
    },
    'form.showFooter': {
      handler() {
        this.debounceRefreshPreview();
      }
    },
    'form.showPageNumber': {
      handler() {
        this.debounceRefreshPreview();
      }
    },
    'form.headerTemplate': {
      handler() {
        this.debounceRefreshPreview();
      }
    },
    'form.bodyTemplate': {
      handler() {
        this.debounceRefreshPreview();
      }
    },
    'form.footerTemplate': {
      handler() {
        this.debounceRefreshPreview();
      }
    }
  },
  methods: {
    // 防抖刷新预览 - 避免频繁更新
    debounceRefreshPreview() {
      if (this.refreshTimer) {
        clearTimeout(this.refreshTimer);
      }
      this.refreshTimer = setTimeout(() => {
        if (this.previewVisible && this.currentPreviewTemplate) {
          this.refreshPreview();
        }
      }, 500); // 500ms 防抖延迟
    },

    /** 查询打印模板配置列表 */
    getList() {
      this.loading = true;
      listPrintTemplate(this.queryParams).then(response => {
        this.printTemplateList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        templateName: null,
        templateCode: null,
        templateType: "inout",
        paperSize: "A4",
        orientation: "portrait",
        pageSizeMode: "fixed",
        defaultScale: 100,
        marginTop: 10.0,
        marginBottom: 10.0,
        marginLeft: 10.0,
        marginRight: 10.0,
        headerHeight: 60.0,
        footerHeight: 30.0,
        fontFamily: "Microsoft YaHei",
        fontSize: 12,
        lineHeight: 1.4,
        colorMode: "color",
        showHeader: true,
        showFooter: true,
        showPageNumber: true,
        showPrintTime: true,
        showQrCode: false,
        printBackground: true,
        fitToPage: false,
        showTableBorder: true,
        headerTemplate: `<div class="print-header">
  <div class="company-info">
    <div class="company-name">{{companyName}}</div>
    <div style="font-size: 12px; color: #666;">
      打印时间：{{printTime}}<br>
      页码：第 {{pageNumber}} 页 / 共 {{totalPages}} 页
    </div>
  </div>
  <div class="document-title">出入库申请单</div>
</div>`,
        footerTemplate: `<div class="print-footer">
  <div class="footer-info">
    <div>系统生成时间：{{printTime}}</div>
    <div>睿云仓储管理系统</div>
    <div>第 {{pageNumber}} 页 / 共 {{totalPages}} 页</div>
  </div>
</div>`,
        bodyTemplate: `<!-- 基本信息区域 -->
<div class="info-section">
  <table class="info-table">
    <tr>
      <td class="label">申请单号:</td>
      <td>{{billNo}}</td>
      <td class="label">业务类型:</td>
      <td>{{businessTypeText}}</td>
    </tr>
    <tr>
      <td class="label">申请人:</td>
      <td>{{applicantName}}</td>
      <td class="label">申请部门:</td>
      <td>{{applicantDeptName}}</td>
    </tr>
    <tr>
      <td class="label">申请时间:</td>
      <td>{{createTime}}</td>
      <td class="label">审批状态:</td>
      <td>{{approvalStatusText}}</td>
    </tr>
    <tr>
      <td class="label">物料清单号:</td>
      <td colspan="3">{{materialBillCode}}</td>
    </tr>
  </table>
</div>

<!-- 物料明细清单 -->
<div class="section-title">物料明细清单</div>
<table class="material-table">
  <thead>
    <tr>
      <th style="width: 5%;">序号</th>
      <th style="width: 15%;">物料编码</th>
      <th style="width: 20%;">物料名称</th>
      <th style="width: 15%;">规格型号</th>
      <th style="width: 8%;">单位</th>
      <th style="width: 10%;">申请数量</th>
      <th style="width: 10%;">实际数量</th>
      <th style="width: 12%;">仓库位置</th>
      <th style="width: 5%;">备注</th>
    </tr>
  </thead>
  <tbody>
    {{#each materialList}}
    <tr>
      <td style="text-align: center;">{{add @index 1}}</td>
      <td>{{materialCode}}</td>
      <td class="text-left">{{materialName}}</td>
      <td>{{specification}}</td>
      <td style="text-align: center;">{{unit}}</td>
      <td style="text-align: right;">{{requestQuantity}}</td>
      <td style="text-align: right;">{{actualQuantity}}</td>
      <td>{{warehouseLocation}}</td>
      <td>{{remark}}</td>
    </tr>
    {{/each}}
  </tbody>
</table>

<!-- 相关人员信息 -->
<div class="section-title">相关人员信息</div>
<table class="info-table">
  <tr>
    <td class="label">申请人:</td>
    <td>{{applicantName}}</td>
    <td class="label">部门负责人:</td>
    <td>{{deptManagerName}}</td>
  </tr>
  <tr>
    <td class="label">仓库管理员:</td>
    <td>{{warehouseManagerName}}</td>
    <td class="label">领料人:</td>
    <td>{{receiverName}}</td>
  </tr>
</table>

<!-- 签名区域 -->
<div class="signature-section">
  <table class="signature-table">
    <tr>
      <td>申请人签字:<div class="signature-line"></div></td>
      <td>部门负责人:<div class="signature-line"></div></td>
      <td>仓库管理员:<div class="signature-line"></div></td>
      <td>审批人签字:<div class="signature-line"></div></td>
    </tr>
    <tr>
      <td>日期:_______</td>
      <td>日期:_______</td>
      <td>日期:_______</td>
      <td>日期:_______</td>
    </tr>
  </table>
</div>`,
        cssStyles: `/* 页面基础样式 */
* {
  box-sizing: border-box;
}

body {
  font-family: "Microsoft YaHei", "SimSun", sans-serif;
  font-size: 12px;
  line-height: 1.4;
  margin: 0;
  padding: 15mm;
  color: #333;
  background: white;
}

/* 页眉样式 */
.print-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 2px solid #333 !important;
  margin-bottom: 20px;
}

.company-name {
  font-size: 18px;
  font-weight: bold;
  color: #2c3e50;
}

.document-title {
  font-size: 24px;
  font-weight: bold;
  text-align: center;
  color: #2c3e50;
  letter-spacing: 3px;
}

/* 页脚样式 */
.print-footer {
  border-top: 1px solid #333 !important;
  padding: 10px 0;
  margin-top: 20px;
}

.footer-info {
  display: flex;
  justify-content: space-between;
  font-size: 10px;
  color: #666;
}

/* 信息表格样式 - 强制边框显示 */
.info-table {
  width: 100% !important;
  border-collapse: collapse !important;
  margin: 15px 0 !important;
  border: 2px solid #333 !important;
}

.info-table td {
  padding: 8px 12px !important;
  border: 1px solid #333 !important;
  vertical-align: middle !important;
  font-size: 12px !important;
}

.info-table .label {
  background-color: #f8f9fa !important;
  font-weight: bold !important;
  width: 120px !important;
  text-align: right !important;
}

/* 物料清单表格样式 - 强制边框显示 */
.material-table {
  width: 100% !important;
  border-collapse: collapse !important;
  border: 2px solid #333 !important;
  margin: 15px 0 !important;
}

.material-table th {
  background-color: #34495e !important;
  color: white !important;
  font-weight: bold !important;
  text-align: center !important;
  padding: 10px 6px !important;
  border: 1px solid #333 !important;
  font-size: 11px !important;
}

.material-table td {
  padding: 8px 6px !important;
  text-align: center !important;
  border: 1px solid #333 !important;
  font-size: 11px !important;
}

.material-table .text-left {
  text-align: left !important;
}

.material-table tbody tr:nth-child(even) {
  background-color: #f8f9fa !important;
}

/* 区域标题样式 */
.section-title {
  font-size: 16px !important;
  font-weight: bold !important;
  color: #2c3e50 !important;
  margin: 25px 0 10px 0 !important;
  padding-bottom: 5px !important;
  border-bottom: 2px solid #3498db !important;
}

/* 签名区域样式 */
.signature-section {
  margin-top: 30px !important;
  page-break-inside: avoid;
}

.signature-table {
  width: 100% !important;
  border-collapse: collapse !important;
  margin: 20px 0 !important;
  border: 2px solid #333 !important;
}

.signature-table td {
  padding: 20px 10px !important;
  text-align: center !important;
  border: 1px solid #333 !important;
  vertical-align: top !important;
}

.signature-line {
  border-bottom: 1px solid #333 !important;
  height: 30px !important;
  margin: 10px 0 !important;
  width: 80% !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

/* 通用表格样式强制应用 */
table {
  border-collapse: collapse !important;
  width: 100% !important;
}

table th,
table td {
  border: 1px solid #333 !important;
  padding: 8px !important;
  text-align: left !important;
  vertical-align: top !important;
}

table th {
  background-color: #f5f5f5 !important;
  font-weight: bold !important;
  text-align: center !important;
}

/* 打印优化 */
@media print {
  body {
    margin: 0 !important;
    padding: 10mm !important;
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
  }

  table {
    border-collapse: collapse !important;
  }

  table th,
  table td {
    border: 1px solid #000 !important;
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
  }

  .print-header {
    border-bottom: 2px solid #000 !important;
  }

  .print-footer {
    border-top: 1px solid #000 !important;
  }

  .signature-section {
    page-break-inside: avoid;
  }

  .section-title {
    border-bottom: 2px solid #000 !important;
  }
}

/* 预览模式样式 */
@media screen {
  body {
    background: #f5f5f5;
    padding: 20px;
  }

  .print-content {
    background: white;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    padding: 20px;
    margin: 0 auto;
    max-width: 210mm;
  }
}`,
        isDefault: false,
        status: "0",
        version: "v1.0",
        remark: null
      };
      this.resetForm("form");
    },

    // 智能优化边距
    setOptimalMargins() {
      if (this.form.pageSizeMode === 'fullpage') {
        // 充分利用模式：最小边距
        this.form.marginTop = 5.0;
        this.form.marginBottom = 5.0;
        this.form.marginLeft = 5.0;
        this.form.marginRight = 5.0;
        this.form.headerHeight = 40.0;
        this.form.footerHeight = 20.0;
        this.$message.success('已设置为充分利用纸张的最优边距');
      } else {
        // 标准模式：适中边距
        this.form.marginTop = 15.0;
        this.form.marginBottom = 15.0;
        this.form.marginLeft = 15.0;
        this.form.marginRight = 15.0;
        this.form.headerHeight = 60.0;
        this.form.footerHeight = 30.0;
        this.$message.success('已设置为标准模式的推荐边距');
      }
    },

    // 插入变量
    insertVariable() {
      const variables = [
        { label: '基本信息', options: [
          { label: '公司名称', value: '{{companyName}}' },
          { label: '单据编号', value: '{{billNo}}' },
          { label: '申请人', value: '{{applicantName}}' },
          { label: '申请部门', value: '{{applicantDeptName}}' },
          { label: '业务类型', value: '{{businessTypeText}}' },
          { label: '审批状态', value: '{{approvalStatusText}}' },
          { label: '物料清单号', value: '{{materialBillCode}}' }
        ]},
        { label: '时间信息', options: [
          { label: '创建时间', value: '{{createTime}}' },
          { label: '打印时间', value: '{{printTime}}' },
          { label: '当前页码', value: '{{pageNumber}}' },
          { label: '总页数', value: '{{totalPages}}' }
        ]},
        { label: '人员信息', options: [
          { label: '部门负责人', value: '{{deptManagerName}}' },
          { label: '仓库管理员', value: '{{warehouseManagerName}}' },
          { label: '领料人', value: '{{receiverName}}' },
          { label: '操作员', value: '{{operatorName}}' }
        ]},
        { label: '物料循环', options: [
          { label: '物料循环开始', value: '{{#each materialList}}' },
          { label: '物料编码', value: '{{materialCode}}' },
          { label: '物料名称', value: '{{materialName}}' },
          { label: '规格型号', value: '{{specification}}' },
          { label: '单位', value: '{{unit}}' },
          { label: '申请数量', value: '{{requestQuantity}}' },
          { label: '实际数量', value: '{{actualQuantity}}' },
          { label: '仓库位置', value: '{{warehouseLocation}}' },
          { label: '序号', value: '{{add @index 1}}' },
          { label: '物料循环结束', value: '{{/each}}' }
        ]}
      ];

      let optionsHtml = '';
      variables.forEach(group => {
        optionsHtml += `<optgroup label="${group.label}">`;
        group.options.forEach(option => {
          optionsHtml += `<option value="${option.value}">${option.label} - ${option.value}</option>`;
        });
        optionsHtml += '</optgroup>';
      });

      this.$msgbox({
        title: '插入变量',
        message: `
          <div style="margin-bottom: 15px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">选择要插入的变量：</label>
            <select id="variableSelect" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
              <option value="">请选择变量...</option>
              ${optionsHtml}
            </select>
          </div>
          <div style="margin-bottom: 10px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">预览：</label>
            <div id="variablePreview" style="background: #f5f5f5; padding: 10px; border-radius: 4px; min-height: 30px; font-family: monospace; font-size: 12px; color: #666;">
              选择变量后将在此显示预览
            </div>
          </div>
        `,
        dangerouslyUseHTMLString: true,
        showCancelButton: true,
        confirmButtonText: '复制到剪贴板',
        cancelButtonText: '取消',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            const select = document.getElementById('variableSelect');
            const selectedValue = select.value;
            if (selectedValue) {
              // 复制到剪贴板
              navigator.clipboard.writeText(selectedValue).then(() => {
                this.$message.success('变量已复制到剪贴板，请粘贴到模板中');
              }).catch(() => {
                this.$message.warning('复制失败，请手动复制：' + selectedValue);
              });
            } else {
              this.$message.warning('请先选择一个变量');
              return;
            }
          }
          done();
        }
      });

      // 添加选择变化事件
      this.$nextTick(() => {
        const select = document.getElementById('variableSelect');
        const preview = document.getElementById('variablePreview');
        if (select && preview) {
          select.addEventListener('change', (e) => {
            const value = e.target.value;
            if (value) {
              preview.textContent = value;
              preview.style.color = '#333';
            } else {
              preview.textContent = '选择变量后将在此显示预览';
              preview.style.color = '#666';
            }
          });
        }
      });
    },

    // 显示变量帮助
    showVariableHelp() {
      this.$alert(`
        <h4>可用变量说明：</h4>
        <p><strong>基本信息：</strong></p>
        <ul>
          <li><code>{{companyName}}</code> - 公司名称</li>
          <li><code>{{billNo}}</code> - 单据编号</li>
          <li><code>{{applicantName}}</code> - 申请人</li>
          <li><code>{{applicantDeptName}}</code> - 申请部门</li>
          <li><code>{{businessType}}</code> - 业务类型代码</li>
          <li><code>{{businessTypeText}}</code> - 业务类型名称</li>
          <li><code>{{approvalStatus}}</code> - 审批状态代码</li>
          <li><code>{{approvalStatusText}}</code> - 审批状态名称</li>
          <li><code>{{materialBillCode}}</code> - 物料清单号</li>
        </ul>
        <p><strong>时间信息：</strong></p>
        <ul>
          <li><code>{{createTime}}</code> - 创建时间</li>
          <li><code>{{printTime}}</code> - 打印时间</li>
          <li><code>{{pageNumber}}</code> - 当前页码</li>
          <li><code>{{totalPages}}</code> - 总页数</li>
        </ul>
        <p><strong>人员信息：</strong></p>
        <ul>
          <li><code>{{deptManagerName}}</code> - 部门负责人</li>
          <li><code>{{warehouseManagerName}}</code> - 仓库管理员</li>
          <li><code>{{receiverName}}</code> - 领料人</li>
          <li><code>{{operatorName}}</code> - 操作员</li>
        </ul>
        <p><strong>物料信息（循环使用）：</strong></p>
        <ul>
          <li><code>{{#each materialList}}</code> - 开始物料循环</li>
          <li><code>{{materialCode}}</code> - 物料编码</li>
          <li><code>{{materialName}}</code> - 物料名称</li>
          <li><code>{{specification}}</code> - 规格型号</li>
          <li><code>{{unit}}</code> - 单位</li>
          <li><code>{{requestQuantity}}</code> - 申请数量</li>
          <li><code>{{actualQuantity}}</code> - 实际数量</li>
          <li><code>{{warehouseLocation}}</code> - 仓库位置</li>
          <li><code>{{remark}}</code> - 备注</li>
          <li><code>{{add @index 1}}</code> - 序号（从1开始）</li>
          <li><code>{{/each}}</code> - 结束物料循环</li>
        </ul>
        <p><strong>使用示例：</strong></p>
        <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; font-size: 12px;">
&lt;table&gt;
  {{#each materialList}}
  &lt;tr&gt;
    &lt;td&gt;{{add @index 1}}&lt;/td&gt;
    &lt;td&gt;{{materialCode}}&lt;/td&gt;
    &lt;td&gt;{{materialName}}&lt;/td&gt;
    &lt;td&gt;{{requestQuantity}}&lt;/td&gt;
  &lt;/tr&gt;
  {{/each}}
&lt;/table&gt;
        </pre>
      `, '变量帮助', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '知道了',
        customClass: 'variable-help-dialog'
      });
    },

    // 预览模板
    previewTemplate() {
      if (!this.form.bodyTemplate) {
        this.$message.warning('请先设置主体模板内容');
        return;
      }

      // 生成模拟数据进行模板预览
      const mockData = this.generateMockData();
      this.currentPreviewTemplate = this.form;

      // 设置对话框预览内容（iframe安全预览）
      this.previewContent = this.renderTemplatePreview(this.form, mockData);
      this.previewBodyContent = this.renderTemplateBodyOnly(this.form, mockData);
      this.previewVisible = true;
    },

    // 保存并预览
    saveAndPreview() {
      this.submitForm().then(() => {
        this.previewTemplate();
      });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加打印模板配置";
      this.activeTab = "body";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getPrintTemplate(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改打印模板配置";
        this.activeTab = "body";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updatePrintTemplate(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPrintTemplate(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除打印模板配置编号为"' + ids + '"的数据项？').then(function() {
        return delPrintTemplate(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('inout/printTemplate/export', {
        ...this.queryParams
      }, `printTemplate_${new Date().getTime()}.xlsx`)
    },


    /** 设为默认模板 */
    handleSetDefault(row) {
      this.$modal.confirm('是否确认将"' + row.templateName + '"设为默认模板？').then(function() {
        return setDefaultTemplate(row.id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("设置成功");
      }).catch(() => {});
    },
    /** 预览模板 */
    handlePreview(row) {
      // 生成模拟数据进行模板预览
      const mockData = this.generateMockData();
      this.currentPreviewTemplate = row;

      // 设置对话框预览内容（iframe安全预览）
      this.previewContent = this.renderTemplatePreview(row, mockData);
      this.previewBodyContent = this.renderTemplatePreview(row, mockData);
      this.previewVisible = true;
    },

    /** 刷新预览 */
    refreshPreview() {
      if (this.currentPreviewTemplate) {
        const mockData = this.generateMockData();
        // 使用当前表单数据进行预览，而不是保存的模板数据
        const templateToUse = this.open ? this.form : this.currentPreviewTemplate;
        this.previewContent = this.renderTemplatePreview(templateToUse, mockData);
        this.previewBodyContent = this.renderTemplateBodyOnly(templateToUse, mockData);

        // 更新iframe内容
        this.$nextTick(() => {
          if (this.$refs.previewFrame) {
            this.$refs.previewFrame.srcdoc = this.previewContent;
          }
        });
      }
    },

    /** 在新窗口打开预览 */
    openPreviewInNewWindow() {
      if (this.previewContent) {
        const previewWindow = window.open('', '_blank', 'width=900,height=700,scrollbars=yes,resizable=yes');
        previewWindow.document.write(this.previewContent);
        previewWindow.document.close();
      }
    },

    /** 生成模拟数据 */
    generateMockData() {
      return {
        companyName: "睿云科技有限公司",
        billNo: "IO202507080001",
        businessType: "物料出库",
        applicantName: "张三",
        applicantDeptName: "生产部",
        applyTime: "2025-07-08 14:30:00",
        approvalStatusName: "已通过",
        materialBillCode: "MB202507080001",
        remark: "生产线急需用料，请优先处理",
        printTime: new Date().toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        }),
        pageNumber: "1",
        totalPages: "1",
        materialItems: [
          {
            materialCode: "M001",
            materialName: "优质碳素结构钢 Q235A",
            quantity: "100",
            unit: "kg",
            locationName: "A区-01-001",
            remark: "规格：20*30*500mm"
          },
          {
            materialCode: "M002",
            materialName: "内六角螺栓 M8×20",
            quantity: "500",
            unit: "个",
            locationName: "B区-02-015",
            remark: "304不锈钢材质"
          },
          {
            materialCode: "M003",
            materialName: "平垫片 φ8",
            quantity: "200",
            unit: "个",
            locationName: "B区-03-008",
            remark: "厚度1.5mm"
          },
          {
            materialCode: "M004",
            materialName: "弹簧垫圈 φ8",
            quantity: "200",
            unit: "个",
            locationName: "B区-03-009",
            remark: "65Mn材质"
          },
          {
            materialCode: "M005",
            materialName: "工业润滑油 46#",
            quantity: "20",
            unit: "L",
            locationName: "C区-01-005",
            remark: "美孚品牌"
          }
        ]
      };
    },

    /** 渲染模板预览 */
    renderTemplatePreview(template, data) {
      let html = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>模板预览</title>
          <style>
            body {
              font-family: '${template.fontFamily || 'SimSun'}', serif;
              margin-top: ${template.marginTop || 20}mm;
              margin-bottom: ${template.marginBottom || 20}mm;
              margin-left: ${template.marginLeft || 20}mm;
              margin-right: ${template.marginRight || 20}mm;
              font-size: ${template.fontSize || 12}px;
            }
            /* 基础样式 */
            table { width: 100%; border-collapse: collapse; margin: 10px 0; }
            th, td { border: 1px solid #333; padding: 10px; text-align: left; vertical-align: top; }
            th { background-color: #f8f9fa; font-weight: bold; text-align: center; }

            /* 页眉样式 */
            .print-header {
              border-bottom: 2px solid #333;
              padding-bottom: 15px;
              margin-bottom: 20px;
            }
            .company-info {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 10px;
            }
            .company-name {
              font-size: 20px;
              font-weight: bold;
              color: #333;
            }
            .document-title {
              text-align: center;
              font-size: 24px;
              font-weight: bold;
              color: #2c3e50;
              margin: 15px 0;
              letter-spacing: 2px;
            }

            /* 信息表格样式 */
            .info-table {
              margin: 15px 0;
              border-collapse: collapse;
              border: 1px solid #ddd;
              width: 100%;
            }
            .info-table td {
              padding: 8px 12px;
              border: 1px solid #ddd;
            }
            .info-table .label {
              background-color: #f8f9fa;
              font-weight: bold;
              width: 120px;
            }

            /* 物料清单表格样式 */
            .material-table {
              margin: 20px 0;
              border-collapse: collapse;
              border: 2px solid #34495e;
              width: 100%;
            }
            .material-table th {
              background-color: #34495e;
              color: white;
              font-weight: bold;
              text-align: center;
              padding: 12px 8px;
              border: 1px solid #2c3e50;
            }
            .material-table td {
              padding: 10px 8px;
              text-align: center;
              border: 1px solid #bdc3c7;
            }
            .material-table .text-left {
              text-align: left;
            }
            .material-table tbody tr:nth-child(even) {
              background-color: #f8f9fa;
            }
            .material-table tbody tr:hover {
              background-color: #e3f2fd;
            }

            /* 签名区域样式 */
            .signature-section {
              margin-top: 40px;
              border-top: 1px solid #ddd;
              padding-top: 20px;
            }
            .signature-table {
              border: none;
            }
            .signature-table td {
              border: none;
              text-align: center;
              padding: 20px 10px;
              vertical-align: top;
            }
            .signature-line {
              border-bottom: 1px solid #333;
              width: 120px;
              margin: 10px auto;
              height: 1px;
            }

            /* 页脚样式 */
            .print-footer {
              margin-top: 30px;
              border-top: 1px solid #ddd;
              padding-top: 15px;
              font-size: 10px;
              color: #666;
            }
            .footer-info {
              display: flex;
              justify-content: space-between;
              align-items: center;
            }

            /* 工具样式 */
            .section-title {
              font-size: 16px;
              font-weight: bold;
              color: #2c3e50;
              margin: 20px 0 10px 0;
              padding-bottom: 5px;
              border-bottom: 2px solid #3498db;
            }

            ${template.cssStyles || ''}
          </style>
        </head>
        <body>
      `;

      // 渲染页眉
      if (template.showHeader && template.headerTemplate) {
        html += this.replaceTemplateVariables(template.headerTemplate, data);
      }

      // 渲染主体内容
      if (template.bodyTemplate) {
        html += this.replaceTemplateVariables(template.bodyTemplate, data);
      } else {
        html += this.generateDefaultPreviewContent(data);
      }

      // 渲染页脚
      if (template.showFooter && template.footerTemplate) {
        html += this.replaceTemplateVariables(template.footerTemplate, data);
      }

      html += `
        </body>
        </html>
      `;

      return html;
    },

    /** 渲染模板预览（仅body内容，用于对话框预览） */
    renderTemplateBodyOnly(template, data) {
      let html = '';

      // 添加完整的CSS样式，使用更具体的选择器避免影响父页面
      html += `
        <style>
          /* 基础样式 - 仅应用于预览内容 */
          .print-preview table { width: 100%; border-collapse: collapse; margin: 10px 0; }
          .print-preview th, .print-preview td { border: 1px solid #333; padding: 10px; text-align: left; vertical-align: top; }
          .print-preview th { background-color: #f8f9fa; font-weight: bold; text-align: center; }

          /* 页眉样式 */
          .print-header {
            border-bottom: 2px solid #333;
            padding-bottom: 15px;
            margin-bottom: 20px;
          }
          .company-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
          }
          .company-name {
            font-size: 20px;
            font-weight: bold;
            color: #333;
          }
          .document-title {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin: 15px 0;
            letter-spacing: 2px;
          }

          /* 信息表格样式 */
          .info-table {
            margin: 15px 0;
            border-collapse: collapse;
            border: 1px solid #ddd;
            width: 100%;
          }
          .info-table td {
            padding: 8px 12px;
            border: 1px solid #ddd;
          }
          .info-table .label {
            background-color: #f8f9fa;
            font-weight: bold;
            width: 120px;
          }

          /* 物料清单表格样式 */
          .material-table {
            border-collapse: collapse;
            border: 2px solid #34495e;
            width: 100%;
            margin: 20px 0;
          }
          .material-table th {
            background-color: #34495e;
            color: white;
            font-weight: bold;
            text-align: center;
            padding: 12px 8px;
            border: 1px solid #2c3e50;
          }
          .material-table td {
            padding: 10px 8px;
            border: 1px solid #34495e;
            text-align: center;
          }

          /* 签字区域样式 */
          .signature-section {
            margin-top: 30px;
            border-top: 1px solid #ddd;
            padding-top: 20px;
          }
          .signature-table {
            border-collapse: collapse;
            width: 100%;
            border: 1px solid #333;
          }
          .signature-table th,
          .signature-table td {
            border: 1px solid #333;
            padding: 15px 10px;
            text-align: center;
            vertical-align: top;
          }
          .signature-table th {
            background-color: #f8f9fa;
            font-weight: bold;
          }

          /* 页脚样式 */
          .print-footer {
            margin-top: 30px;
            border-top: 1px solid #ddd;
            padding-top: 15px;
          }
          .footer-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #666;
          }

          /* 自定义样式 */
          ${template.cssStyles || ''}
        </style>
      `;

      // 添加内联样式以应用模板配置
      html += `<div style="
        font-family: '${template.fontFamily || 'SimSun'}', serif;
        font-size: ${template.fontSize || 12}px;
        padding-top: ${template.marginTop || 20}mm;
        padding-bottom: ${template.marginBottom || 20}mm;
        padding-left: ${template.marginLeft || 20}mm;
        padding-right: ${template.marginRight || 20}mm;
        line-height: 1.5;
      ">`;

      // 渲染页眉
      if (template.showHeader && template.headerTemplate) {
        html += this.replaceTemplateVariables(template.headerTemplate, data);
      }

      // 渲染主体内容
      if (template.bodyTemplate) {
        html += this.replaceTemplateVariables(template.bodyTemplate, data);
      } else {
        html += this.generateDefaultPreviewContent(data);
      }

      // 渲染页脚
      if (template.showFooter && template.footerTemplate) {
        html += this.replaceTemplateVariables(template.footerTemplate, data);
      }

      html += '</div>';

      return html;
    },

    /** 替换模板变量 */
    replaceTemplateVariables(template, data) {
      let result = template;

      // 替换简单变量
      for (const [key, value] of Object.entries(data)) {
        if (typeof value === 'string' || typeof value === 'number') {
          const placeholder = `{{${key}}}`;
          result = result.replace(new RegExp(placeholder, 'g'), value);
        }
      }

      // 处理物料清单明细循环 - 支持 {{#if}}{{#each}}{{else}}{{/each}}{{/if}} 嵌套语法
      if (result.includes('{{#if materialItems}}') && result.includes('{{/if}}')) {
        const materialItems = data.materialItems || [];

        // 匹配完整的if块，包括可能的else部分
        const ifMatch = result.match(/\{\{#if materialItems\}\}([\s\S]*?)\{\{\/if\}\}/);
        if (ifMatch) {
          let ifContent = ifMatch[1];
          let finalContent = '';

          if (materialItems.length > 0) {
            // 有数据时处理each循环
            const eachMatch = ifContent.match(/\{\{#each materialItems\}\}([\s\S]*?)\{\{\/each\}\}/);
            if (eachMatch) {
              const eachBlock = eachMatch[1];
              let itemsHtml = '';

              materialItems.forEach((item, index) => {
                let itemHtml = eachBlock;
                // 处理各种模板变量
                itemHtml = itemHtml.replace(/\{\{add @index 1\}\}/g, index + 1);
                itemHtml = itemHtml.replace(/\{\{@index\}\}/g, index + 1);
                itemHtml = itemHtml.replace(/\{\{materialCode\}\}/g, item.materialCode || '');
                itemHtml = itemHtml.replace(/\{\{materialName\}\}/g, item.materialName || '');
                itemHtml = itemHtml.replace(/\{\{quantity\}\}/g, item.quantity || '');
                itemHtml = itemHtml.replace(/\{\{unit\}\}/g, item.unit || '个');
                itemHtml = itemHtml.replace(/\{\{locationName\}\}/g, item.locationName || '');
                itemHtml = itemHtml.replace(/\{\{remark\}\}/g, item.remark || '-');
                itemsHtml += itemHtml;
              });

              // 替换each块为渲染后的内容
              finalContent = ifContent.replace(eachMatch[0], itemsHtml);

              // 移除else部分（如果存在）
              finalContent = finalContent.replace(/\{\{else\}\}[\s\S]*$/, '');
            } else {
              // 没有each循环，直接使用if内容
              finalContent = ifContent.replace(/\{\{else\}\}[\s\S]*$/, '');
            }
          } else {
            // 没有数据时使用else部分
            const elseMatch = ifContent.match(/\{\{else\}\}([\s\S]*?)$/);
            if (elseMatch) {
              finalContent = elseMatch[1];
            } else {
              finalContent = '<tr><td colspan="7" style="text-align: center; color: #999; padding: 20px;">暂无物料明细</td></tr>';
            }
          }

          result = result.replace(ifMatch[0], finalContent);
        }
      }

      return result;
    },

    /** 生成默认预览内容 */
    generateDefaultPreviewContent(data) {
      return `
        <!-- 页眉部分 -->
        <div class="print-header">
          <div class="company-info">
            <div class="company-name">${data.companyName || '睿云科技有限公司'}</div>
            <div style="font-size: 12px; color: #666;">
              打印时间：${data.printTime}<br>
              页码：第 ${data.pageNumber} 页 / 共 ${data.totalPages} 页
            </div>
          </div>
          <div class="document-title">出入库申请单</div>
        </div>

        <!-- 基本信息表格 -->
        <div class="section-title">基本信息</div>
        <table class="info-table">
          <tr>
            <td class="label">单据编号</td>
            <td>${data.billNo}</td>
            <td class="label">业务类型</td>
            <td>${data.businessType}</td>
          </tr>
          <tr>
            <td class="label">申请人</td>
            <td>${data.applicantName}</td>
            <td class="label">申请部门</td>
            <td>${data.applicantDeptName}</td>
          </tr>
          <tr>
            <td class="label">申请时间</td>
            <td>${data.applyTime}</td>
            <td class="label">审批状态</td>
            <td style="color: ${data.approvalStatusName === '已通过' ? '#27ae60' : '#e74c3c'}; font-weight: bold;">
              ${data.approvalStatusName}
            </td>
          </tr>
          <tr>
            <td class="label">物料清单编号</td>
            <td colspan="3">${data.materialBillCode || '-'}</td>
          </tr>
          <tr>
            <td class="label">备注说明</td>
            <td colspan="3">${data.remark || '-'}</td>
          </tr>
        </table>

        <!-- 物料清单明细 -->
        <div class="section-title">物料清单明细</div>
        <table class="material-table">
          <thead>
            <tr>
              <th style="width: 60px;">序号</th>
              <th style="width: 120px;">物料编码</th>
              <th class="text-left">物料名称</th>
              <th style="width: 100px;">申请数量</th>
              <th style="width: 80px;">单位</th>
              <th style="width: 120px;">存储位置</th>
              <th style="width: 100px;">备注</th>
            </tr>
          </thead>
          <tbody>
            ${data.materialItems && data.materialItems.length > 0 ?
              data.materialItems.map((item, index) => `
                <tr>
                  <td>${index + 1}</td>
                  <td>${item.materialCode}</td>
                  <td class="text-left">${item.materialName}</td>
                  <td>${item.quantity}</td>
                  <td>${item.unit || '个'}</td>
                  <td>${item.locationName}</td>
                  <td>${item.remark || '-'}</td>
                </tr>
              `).join('') :
              '<tr><td colspan="7" style="text-align: center; color: #999; padding: 20px;">暂无物料明细</td></tr>'
            }
          </tbody>
          <tfoot>
            <tr style="background-color: #f8f9fa; font-weight: bold;">
              <td colspan="3">合计</td>
              <td>${data.materialItems ? data.materialItems.length : 0} 项</td>
              <td colspan="3">-</td>
            </tr>
          </tfoot>
        </table>

        <!-- 签名区域 -->
        <div class="signature-section">
          <div class="section-title">审批签字</div>
          <table class="signature-table">
            <tr>
              <td>
                <div style="font-weight: bold; margin-bottom: 10px;">申请人</div>
                <div class="signature-line"></div>
                <div style="margin-top: 5px; font-size: 12px;">签字日期：_______</div>
              </td>
              <td>
                <div style="font-weight: bold; margin-bottom: 10px;">部门负责人</div>
                <div class="signature-line"></div>
                <div style="margin-top: 5px; font-size: 12px;">签字日期：_______</div>
              </td>
              <td>
                <div style="font-weight: bold; margin-bottom: 10px;">仓库管理员</div>
                <div class="signature-line"></div>
                <div style="margin-top: 5px; font-size: 12px;">签字日期：_______</div>
              </td>
              <td>
                <div style="font-weight: bold; margin-bottom: 10px;">领料人</div>
                <div class="signature-line"></div>
                <div style="margin-top: 5px; font-size: 12px;">签字日期：_______</div>
              </td>
            </tr>
          </table>
        </div>

        <!-- 页脚信息 -->
        <div class="print-footer">
          <div class="footer-info">
            <div>系统生成时间：${data.printTime}</div>
            <div>睿云仓储管理系统</div>
            <div>第 ${data.pageNumber} 页 / 共 ${data.totalPages} 页</div>
          </div>
        </div>
      `;
    },

    /** 获取默认页眉模板 */
    getDefaultHeaderTemplate() {
      return `<div class="print-header">
  <div class="company-info">
    <div class="company-name">{{companyName}}</div>
    <div style="font-size: 12px; color: #666;">
      打印时间：{{printTime}}<br>
      页码：第 {{pageNumber}} 页 / 共 {{totalPages}} 页
    </div>
  </div>
  <div class="document-title">出入库申请单</div>
</div>`;
    },

    /** 获取默认主体模板 */
    getDefaultBodyTemplate() {
      return `<div class="print-body">
  <!-- 基本信息表格 -->
  <table class="info-table">
    <tr>
      <td class="label">申请编号：</td>
      <td>{{inoutCode}}</td>
      <td class="label">申请类型：</td>
      <td>{{inoutType}}</td>
    </tr>
    <tr>
      <td class="label">申请人：</td>
      <td>{{applicantName}}</td>
      <td class="label">申请时间：</td>
      <td>{{applicationTime}}</td>
    </tr>
    <tr>
      <td class="label">物料清单：</td>
      <td>{{materialBillCode}}</td>
      <td class="label">审批状态：</td>
      <td>{{approvalStatusText}}</td>
    </tr>
    <tr>
      <td class="label">申请原因：</td>
      <td colspan="3">{{reason}}</td>
    </tr>
  </table>

  <!-- 物料清单明细 -->
  {{#if hasMaterialItems}}
  <div class="section-title">物料清单明细</div>
  <table class="material-table">
    <thead>
      <tr>
        <th>序号</th>
        <th>物料编码</th>
        <th>物料名称</th>
        <th>规格型号</th>
        <th>单位</th>
        <th>申请数量</th>
        <th>备注</th>
      </tr>
    </thead>
    <tbody>
      {{#each materialItems}}
      <tr>
        <td>{{@index}}</td>
        <td>{{materialCode}}</td>
        <td class="text-left">{{materialName}}</td>
        <td>{{specification}}</td>
        <td>{{unit}}</td>
        <td>{{quantity}}</td>
        <td class="text-left">{{remark}}</td>
      </tr>
      {{/each}}
    </tbody>
  </table>
  {{/if}}
</div>`;
    },

    /** 获取默认页脚模板 */
    getDefaultFooterTemplate() {
      return `<div class="print-footer">
  <div class="signature-section">
    <table class="signature-table">
      <tr>
        <td>申请人签字：<br><div class="signature-line"></div></td>
        <td>审批人签字：<br><div class="signature-line"></div></td>
        <td>领料人签字：<br><div class="signature-line"></div></td>
      </tr>
    </table>
  </div>
  <div class="footer-info">
    <span>打印时间：{{printTime}}</span>
    <span>第 {{pageNumber}} 页 / 共 {{totalPages}} 页</span>
  </div>
</div>`;
    },

    /** 获取默认CSS样式 */
    getDefaultCssStyles() {
      return `/* 基础样式 */
body {
  font-family: "SimSun", serif;
  font-size: 12px;
  line-height: 1.5;
  margin: 0;
  padding: 20mm;
}

/* 页眉样式 */
.print-header {
  border-bottom: 2px solid #333;
  padding-bottom: 15px;
  margin-bottom: 20px;
}

.company-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.company-name {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.document-title {
  text-align: center;
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
  margin: 15px 0;
  letter-spacing: 2px;
}

/* 信息表格样式 */
.info-table {
  border-collapse: collapse;
  border: 1px solid #ddd;
  width: 100%;
  margin: 15px 0;
}

.info-table td {
  padding: 8px 12px;
  border: 1px solid #ddd;
}

.info-table .label {
  background-color: #f8f9fa;
  font-weight: bold;
  width: 120px;
}

/* 物料清单表格样式 */
.material-table {
  border-collapse: collapse;
  border: 2px solid #34495e;
  width: 100%;
  margin: 20px 0;
}

.material-table th {
  background-color: #34495e;
  color: white;
  font-weight: bold;
  text-align: center;
  padding: 12px 8px;
  border: 1px solid #2c3e50;
}

.material-table td {
  padding: 10px 8px;
  text-align: center;
  border: 1px solid #bdc3c7;
}

.material-table .text-left {
  text-align: left;
}

.material-table tbody tr:nth-child(even) {
  background-color: #f8f9fa;
}

/* 签名区域样式 */
.signature-section {
  margin-top: 40px;
  border-top: 1px solid #ddd;
  padding-top: 20px;
}

.signature-table {
  border: none;
  width: 100%;
}

.signature-table td {
  border: none;
  text-align: center;
  padding: 20px 10px;
  vertical-align: top;
}

.signature-line {
  border-bottom: 1px solid #333;
  width: 120px;
  margin: 10px auto;
  height: 1px;
}

/* 页脚样式 */
.print-footer {
  margin-top: 30px;
  border-top: 1px solid #ddd;
  padding-top: 15px;
  font-size: 10px;
  color: #666;
}

.footer-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 工具样式 */
.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #2c3e50;
  margin: 20px 0 10px 0;
  padding-bottom: 5px;
  border-bottom: 2px solid #3498db;
}`;
    }
  }
};
</script>

<style scoped>
/* 确保表格样式一致性 */
.el-table {
  border-collapse: separate;
  border-spacing: 0;
}

.el-table .el-table__header-wrapper th {
  background-color: #f8f8f9 !important;
  color: #515a6e !important;
  height: 40px !important;
  font-size: 13px !important;
}

.el-table .el-table__body-wrapper .el-table__row {
  background-color: #fff;
}

.el-table .el-table__body-wrapper .el-table__row:hover {
  background-color: #f5f7fa;
}
.preview-toolbar {
  margin-bottom: 15px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-toolbar .toolbar-left {
  display: flex;
  gap: 10px;
}

.preview-toolbar .toolbar-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.preview-toolbar .preview-tip {
  font-size: 12px;
  color: #666;
  font-style: italic;
}

.iframe-preview {
  background: white;
  border-radius: 4px;
  overflow: hidden;
}

.print-preview-container {
  max-height: 70vh;
  overflow: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #f0f0f0;
  padding: 20px;
}

.print-preview-wrapper {
  /* 创建新的层叠上下文，隔离样式 */
  position: relative;
  z-index: 1;
  /* 重置所有可能影响的样式 */
  font-family: initial;
  font-size: initial;
  line-height: initial;
  color: initial;
  background: initial;
}

.print-preview {
  background: white;
  margin: 0 auto;
  box-shadow: 0 0 10px rgba(0,0,0,0.1);
  min-height: 400px;
  max-width: 210mm; /* A4纸张宽度 */
  /* 字体和边距由模板配置动态设置 */
}

/* 打印预览样式 */
.print-preview table {
  width: 100%;
  border-collapse: collapse;
  margin: 10px 0;
}

.print-preview th,
.print-preview td {
  border: 1px solid #333;
  padding: 10px;
  text-align: left;
  vertical-align: top;
}

.print-preview th {
  background-color: #f8f9fa;
  font-weight: bold;
  text-align: center;
}

/* 页眉样式 */
.print-preview .print-header {
  border-bottom: 2px solid #333;
  padding-bottom: 15px;
  margin-bottom: 20px;
}

.print-preview .company-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.print-preview .company-name {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.print-preview .document-title {
  text-align: center;
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
  margin: 15px 0;
  letter-spacing: 2px;
}

/* 信息表格样式 */
.print-preview .info-table {
  border-collapse: collapse;
  border: 1px solid #ddd;
  width: 100%;
  margin: 15px 0;
}

.print-preview .info-table td {
  padding: 8px 12px;
  border: 1px solid #ddd;
}

.print-preview .info-table .label {
  background-color: #f8f9fa;
  font-weight: bold;
  width: 120px;
}

/* 物料清单表格样式 */
.print-preview .material-table {
  border-collapse: collapse;
  border: 2px solid #34495e;
  width: 100%;
  margin: 20px 0;
}

.print-preview .material-table th {
  background-color: #34495e;
  color: white;
  font-weight: bold;
  text-align: center;
  padding: 12px 8px;
  border: 1px solid #2c3e50;
}

.print-preview .material-table td {
  padding: 10px 8px;
  text-align: center;
  border: 1px solid #bdc3c7;
}

.print-preview .material-table .text-left {
  text-align: left;
}

.print-preview .material-table tbody tr:nth-child(even) {
  background-color: #f8f9fa;
}

/* 签名区域样式 */
.print-preview .signature-section {
  margin-top: 40px;
  border-top: 1px solid #ddd;
  padding-top: 20px;
}

.print-preview .signature-table {
  border: none;
}

.print-preview .signature-table td {
  border: none;
  text-align: center;
  padding: 20px 10px;
  vertical-align: top;
}

.print-preview .signature-line {
  border-bottom: 1px solid #333;
  width: 120px;
  margin: 10px auto;
  height: 1px;
}

/* 页脚样式 */
.print-preview .print-footer {
  margin-top: 30px;
  border-top: 1px solid #ddd;
  padding-top: 15px;
  font-size: 10px;
  color: #666;
}

.print-preview .footer-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 工具样式 */
.print-preview .section-title {
  font-size: 16px;
  font-weight: bold;
  color: #2c3e50;
  margin: 20px 0 10px 0;
  padding-bottom: 5px;
  border-bottom: 2px solid #3498db;
}

/* 模板配置对话框样式 */
.template-config-dialog .el-dialog__body {
  padding: 15px 20px;
  max-height: 80vh;
  overflow-y: auto;
}

.template-config-dialog .card-header {
  font-weight: bold;
  color: #303133;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.template-config-dialog .el-card {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
}

.template-config-dialog .el-card__header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  padding: 12px 20px;
}

.template-config-dialog .el-form-item {
  margin-bottom: 18px;
}

.template-config-dialog .el-form-item__label {
  font-weight: 500;
  color: #606266;
}

.template-config-dialog .el-input-number {
  width: 100%;
}

.template-config-dialog .el-textarea__inner {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
}

.template-config-dialog .el-tabs__content {
  padding-top: 15px;
}

.template-config-dialog .dialog-footer {
  text-align: center;
  padding: 15px 0;
  border-top: 1px solid #e4e7ed;
  background-color: #fafafa;
}

.template-config-dialog .dialog-footer .el-button {
  margin: 0 8px;
  min-width: 90px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .template-config-dialog {
    width: 95% !important;
  }
}

/* 表单验证样式 */
.template-config-dialog .el-form-item.is-error .el-input__inner,
.template-config-dialog .el-form-item.is-error .el-textarea__inner {
  border-color: #f56c6c;
}

/* 开关样式优化 */
.template-config-dialog .el-switch__label {
  font-size: 12px;
  color: #606266;
}

.template-config-dialog .el-switch__label.is-active {
  color: #409eff;
}

/* 变量帮助对话框样式 */
.variable-help-dialog .el-message-box {
  width: 800px !important;
  max-width: 90vw;
}

.variable-help-dialog .el-message-box__content {
  max-height: 70vh;
  overflow-y: auto;
}

.variable-help-dialog code {
  background: #f1f2f6;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
  color: #e74c3c;
}

.variable-help-dialog pre {
  background: #f8f9fa !important;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 15px !important;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;
  font-size: 12px !important;
  line-height: 1.4;
  color: #495057 !important;
  overflow-x: auto;
}

.variable-help-dialog ul {
  margin: 10px 0;
  padding-left: 20px;
}

.variable-help-dialog li {
  margin: 5px 0;
  line-height: 1.6;
}

.variable-help-dialog h4 {
  color: #2c3e50;
  border-bottom: 2px solid #3498db;
  padding-bottom: 8px;
  margin-bottom: 15px;
}

.variable-help-dialog p strong {
  color: #34495e;
  font-size: 14px;
}
</style>
