<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards" v-if="showStats">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total">
              <i class="el-icon-monitor"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.totalCount || 0 }}</div>
              <div class="stats-label">设备总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon online">
              <i class="el-icon-success"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.onlineCount || 0 }}</div>
              <div class="stats-label">在线设备</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon offline">
              <i class="el-icon-error"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.offlineCount || 0 }}</div>
              <div class="stats-label">离线设备</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon rate">
              <i class="el-icon-pie-chart"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.onlineRate || '0%' }}</div>
              <div class="stats-label">在线率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 更新时间和状态 -->
    <div class="update-info" style="text-align: right; margin-bottom: 10px; color: #909399; font-size: 12px;">
      <span v-if="statistics.statisticsTime">
        最后更新: {{ parseTime(statistics.statisticsTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
      </span>
      <span style="margin-left: 10px;">
        <i :class="realTimeUpdate ? 'el-icon-video-play' : 'el-icon-video-pause'"
           :style="{ color: realTimeUpdate ? '#67C23A' : '#F56C6C' }"></i>
        {{ realTimeUpdate ? '实时更新中' : '已暂停更新' }}
      </span>
      <span style="margin-left: 10px;">
        更新间隔: {{ updateInterval }}秒
      </span>
    </div>

    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="设备编码" prop="deviceCode">
        <el-input
          v-model="queryParams.deviceCode"
          placeholder="请输入设备编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备名称" prop="deviceName">
        <el-input
          v-model="queryParams.deviceName"
          placeholder="请输入设备名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备类型" prop="deviceType">
        <el-select v-model="queryParams.deviceType" placeholder="请选择设备类型" clearable>
          <el-option
            v-for="dict in dict.type.outmistake_device_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="在线状态" prop="onlineStatus">
        <el-select v-model="queryParams.onlineStatus" placeholder="请选择在线状态" clearable>
          <el-option
            v-for="dict in dict.type.outmistake_online_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="注册方式" prop="registerProtocol">
        <el-select v-model="queryParams.registerProtocol" placeholder="请选择注册方式" clearable>
          <el-option
            v-for="dict in dict.type.outmistake_protocol_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['outmis:device:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['outmis:device:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['outmis:device:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['outmis:device:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          :type="realTimeUpdate ? 'success' : 'info'"
          plain
          :icon="realTimeUpdate ? 'el-icon-video-pause' : 'el-icon-video-play'"
          size="mini"
          @click="toggleRealTimeUpdate"
        >{{ realTimeUpdate ? '停止更新' : '开启更新' }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-refresh"
          size="mini"
          @click="handleSync"
          v-hasPermi="['outmis:device:edit']"
        >同步设备</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-refresh"
          size="mini"
          @click="refreshStatistics"
        >刷新统计</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :showStats.sync="showStats"></right-toolbar>
    </el-row>

    <!-- 设备列表 -->
    <el-table v-loading="loading" :data="deviceList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="设备编码" align="center" prop="deviceCode" />
      <el-table-column label="设备名称" align="center" prop="deviceName" />
      <el-table-column label="设备类型" align="center" prop="deviceType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.outmistake_device_type" :value="scope.row.deviceType"/>
        </template>
      </el-table-column>
      <el-table-column label="注册方式" align="center" prop="registerProtocol">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.outmistake_protocol_type" :value="scope.row.registerProtocol"/>
        </template>
      </el-table-column>
      <el-table-column label="IP地址" align="center" prop="ipAddress" />
      <el-table-column label="安装位置" align="center" prop="installationLocation" />
      <el-table-column label="在线状态" align="center" prop="onlineStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.outmistake_online_status" :value="scope.row.onlineStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="最后心跳" align="center" prop="lastHeartbeat" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.lastHeartbeat, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-dropdown size="mini" @command="(command) => handleDeviceOperation(command, scope.row)">
            <el-button type="primary" size="mini">
              操作<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="edit" icon="el-icon-edit" v-hasPermi="['outmis:device:edit']">修改</el-dropdown-item>
              <el-dropdown-item command="status" icon="el-icon-view">查看状态</el-dropdown-item>
              <el-dropdown-item command="disconnect" icon="el-icon-switch-button"
                               :disabled="scope.row.onlineStatus !== '1'"
                               v-hasPermi="['outmis:device:edit']">断开连接</el-dropdown-item>
              <el-dropdown-item command="restart" icon="el-icon-refresh"
                               v-hasPermi="['outmis:device:edit']">重启连接</el-dropdown-item>
              <el-dropdown-item command="delete" icon="el-icon-delete"
                               v-hasPermi="['outmis:device:remove']" divided>删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改设备对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="设备编码" prop="deviceCode">
          <el-input v-model="form.deviceCode" placeholder="请输入设备编码" />
        </el-form-item>
        <el-form-item label="设备名称" prop="deviceName">
          <el-input v-model="form.deviceName" placeholder="请输入设备名称" />
        </el-form-item>
        <el-form-item label="设备类型" prop="deviceType">
          <el-select v-model="form.deviceType" placeholder="请选择设备类型">
            <el-option
              v-for="dict in dict.type.outmistake_device_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="注册方式" prop="registerProtocol">
          <el-select v-model="form.registerProtocol" placeholder="请选择注册方式">
            <el-option
              v-for="dict in dict.type.outmistake_protocol_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="IP地址" prop="ipAddress">
          <el-input v-model="form.ipAddress" placeholder="请输入IP地址" />
        </el-form-item>
        <el-form-item label="端口号" prop="port">
          <el-input v-model="form.port" placeholder="请输入端口号" />
        </el-form-item>
        <el-form-item label="安装位置" prop="installationLocation">
          <el-input v-model="form.installationLocation" placeholder="请输入安装位置" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listDevice,
  getDevice,
  delDevice,
  addDevice,
  updateDevice,
  getDeviceStatistics,
  disconnectDevice,
  restartDevice,
  getDeviceStatus,
  syncDevices
} from "@/api/outmis/device";

export default {
  name: "Device",
  dicts: ['outmistake_device_type', 'outmistake_device_status', 'outmistake_online_status', 'outmistake_protocol_type', 'sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 显示统计信息
      showStats: true,
      // 总条数
      total: 0,
      // 设备表格数据
      deviceList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceCode: null,
        deviceName: null,
        deviceType: null,
        registerProtocol: null,
        onlineStatus: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        deviceCode: [
          { required: true, message: "设备编码不能为空", trigger: "blur" }
        ],
        deviceName: [
          { required: true, message: "设备名称不能为空", trigger: "blur" }
        ],
        deviceType: [
          { required: true, message: "设备类型不能为空", trigger: "change" }
        ],
        registerProtocol: [
          { required: true, message: "注册方式不能为空", trigger: "change" }
        ],
      },
      // 统计信息
      statistics: {
        totalCount: 0,
        onlineCount: 0,
        offlineCount: 0,
        faultCount: 0,
        maintenanceCount: 0,
        onlineRate: '0%',
        faultRate: '0%',
        statisticsTime: null,
        deviceTypeStats: [],
        onlineRateTrends: []
      },
      // 实时更新定时器
      statisticsTimer: null,
      deviceListTimer: null,
      // 实时更新开关
      realTimeUpdate: true,
      // 更新间隔（秒）
      updateInterval: 30
    };
  },
  created() {
    this.getList();
    this.getStatistics();
    this.startRealTimeUpdate();
  },
  beforeDestroy() {
    this.stopRealTimeUpdate();
  },
  methods: {
    /** 查询设备列表 */
    getList() {
      this.loading = true;
      listDevice(this.queryParams).then(response => {
        this.deviceList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取统计信息 */
    getStatistics() {
      getDeviceStatistics().then(response => {
        this.statistics = response.data;
      }).catch(error => {
        console.error('获取设备统计信息失败:', error);
        // 使用默认数据
        this.statistics = {
          totalCount: 0,
          onlineCount: 0,
          offlineCount: 0,
          onlineRate: '0%'
        };
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        deviceId: null,
        deviceCode: null,
        deviceName: null,
        deviceType: null,
        registerProtocol: "TCP",
        ipAddress: null,
        port: null,
        installationLocation: null,
        status: "0",
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.deviceId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加设备";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const deviceId = row.deviceId || this.ids
      getDevice(deviceId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改设备";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.deviceId != null) {
            updateDevice(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDevice(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const deviceIds = row.deviceId || this.ids;
      this.$modal.confirm('是否确认删除设备编号为"' + deviceIds + '"的数据项？').then(function() {
        return delDevice(deviceIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('outmis/device/export', {
        ...this.queryParams
      }, `device_${new Date().getTime()}.xlsx`)
    },

    /** 启动实时更新 */
    startRealTimeUpdate() {
      if (!this.realTimeUpdate) return;

      // 统计信息实时更新
      this.statisticsTimer = setInterval(() => {
        this.getStatistics();
      }, this.updateInterval * 1000);

      // 设备列表实时更新（间隔更长一些）
      this.deviceListTimer = setInterval(() => {
        this.getList();
      }, this.updateInterval * 2 * 1000);
    },

    /** 停止实时更新 */
    stopRealTimeUpdate() {
      if (this.statisticsTimer) {
        clearInterval(this.statisticsTimer);
        this.statisticsTimer = null;
      }
      if (this.deviceListTimer) {
        clearInterval(this.deviceListTimer);
        this.deviceListTimer = null;
      }
    },

    /** 切换实时更新状态 */
    toggleRealTimeUpdate() {
      this.realTimeUpdate = !this.realTimeUpdate;
      if (this.realTimeUpdate) {
        this.startRealTimeUpdate();
        this.$message.success('已开启实时更新');
      } else {
        this.stopRealTimeUpdate();
        this.$message.info('已关闭实时更新');
      }
    },

    /** 手动刷新统计信息 */
    refreshStatistics() {
      this.getStatistics();
      this.$message.success('统计信息已刷新');
    },

    /** 手动刷新设备列表 */
    refreshDeviceList() {
      this.getList();
      this.$message.success('设备列表已刷新');
    },

    /** 设备操作处理 */
    handleDeviceOperation(command, row) {
      switch (command) {
        case 'edit':
          this.handleUpdate(row);
          break;
        case 'status':
          this.handleViewStatus(row);
          break;
        case 'disconnect':
          this.handleDisconnect(row);
          break;
        case 'restart':
          this.handleRestart(row);
          break;
        case 'delete':
          this.handleDelete(row);
          break;
      }
    },

    /** 查看设备状态 */
    handleViewStatus(row) {
      this.$modal.loading("正在获取设备状态...");
      getDeviceStatus(row.deviceCode).then(response => {
        this.$modal.closeLoading();
        const statusInfo = response.data;
        let statusText = `
          <div style="text-align: left;">
            <p><strong>设备编码:</strong> ${statusInfo.clientId || row.deviceCode}</p>
            <p><strong>设备名称:</strong> ${statusInfo.clientName || row.deviceName}</p>
            <p><strong>连接状态:</strong> <span style="color: ${statusInfo.status === 'ONLINE' ? '#67C23A' : '#F56C6C'}">${statusInfo.status === 'ONLINE' ? '在线' : '离线'}</span></p>
            <p><strong>IP地址:</strong> ${statusInfo.ipAddress || '未知'}</p>
            <p><strong>协议类型:</strong> ${statusInfo.protocolType || '未知'}</p>
            <p><strong>最后连接时间:</strong> ${statusInfo.lastConnectTime ? this.parseTime(statusInfo.lastConnectTime) : '未知'}</p>
            <p><strong>发送消息数:</strong> ${statusInfo.sentMessageCount || 0}</p>
            <p><strong>接收消息数:</strong> ${statusInfo.receivedMessageCount || 0}</p>
            <p><strong>错误次数:</strong> ${statusInfo.errorCount || 0}</p>
          </div>
        `;
        this.$alert(statusText, '设备状态信息', {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定'
        });
      }).catch(() => {
        this.$modal.closeLoading();
        this.$modal.msgError("获取设备状态失败");
      });
    },

    /** 断开设备连接 */
    handleDisconnect(row) {
      this.$modal.confirm(`确认断开设备"${row.deviceName}"的连接吗？`).then(() => {
        this.$modal.loading("正在断开连接...");
        return disconnectDevice(row.deviceCode);
      }).then(() => {
        this.$modal.closeLoading();
        this.$modal.msgSuccess("设备连接已断开");
        this.getList();
      }).catch(() => {
        this.$modal.closeLoading();
      });
    },

    /** 重启设备连接 */
    handleRestart(row) {
      this.$modal.confirm(`确认重启设备"${row.deviceName}"的连接吗？`).then(() => {
        this.$modal.loading("正在重启连接...");
        return restartDevice(row.deviceCode);
      }).then(() => {
        this.$modal.closeLoading();
        this.$modal.msgSuccess("设备重启指令已发送");
        // 延迟刷新列表，等待设备重新连接
        setTimeout(() => {
          this.getList();
        }, 3000);
      }).catch(() => {
        this.$modal.closeLoading();
      });
    },

    /** 同步设备信息 */
    handleSync() {
      this.$modal.confirm('确认从统一注册管理同步设备信息吗？').then(() => {
        this.$modal.loading("正在同步设备信息...");
        return syncDevices();
      }).then((response) => {
        this.$modal.closeLoading();
        this.$modal.msgSuccess(response.msg || "设备信息同步完成");
        this.getList();
      }).catch(() => {
        this.$modal.closeLoading();
      });
    }
  }
};
</script>

<style scoped>
.stats-cards {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.online {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.offline {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.rate {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}
</style>
