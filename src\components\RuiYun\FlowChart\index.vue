<template>
  <div class="flow-chart-container" ref="chartContainer" :style="containerStyle">
    <div v-if="loading" class="loading-container">
      <el-spinner type="primary" />
    </div>
    <div v-else-if="!flowData || flowData.nodes.length === 0" class="empty-container">
      <el-empty description="暂无流程图数据"></el-empty>
    </div>
    <div v-else>
      <div style="margin-bottom: 10px; padding: 5px; background: #f0f0f0; font-size: 12px;">
        调试: FlowChart组件已加载，节点数量: {{ flowData ? flowData.nodes.length : 0 }}
        <br>审批记录数量: {{ debugInfo.recordsCount }}，实例状态: {{ debugInfo.instanceStatus }}
        <br>记录映射: {{ JSON.stringify(debugInfo.recordMap) }}
      </div>
      <!-- D3.js 流程图容器 -->
      <div ref="diagramContainer" class="diagram-container"></div>
    </div>
  </div>
</template>

<script>
import { getInstanceDiagram } from "@/api/approval/workflow"
import * as d3 from 'd3'

export default {
  name: "FlowChart",
  props: {
    instanceId: {
      type: [String, Number],
      required: true
    },
    width: {
      type: [String, Number],
      default: '100%'
    },
    height: {
      type: [String, Number],
      default: 200
    }
  },
  data() {
    return {
      loading: false,
      flowData: null,
      diagramData: null,
      debugInfo: {
        recordsCount: 0,
        instanceStatus: '',
        recordMap: {}
      }
    }
  },
  computed: {
    containerStyle() {
      return {
        width: typeof this.width === 'number' ? `${this.width}px` : this.width,
        height: typeof this.height === 'number' ? `${this.height}px` : this.height
      }
    }
  },
  async mounted() {
    await this.loadFlowData()
  },
  watch: {
    instanceId: {
      handler() {
        this.loadFlowData()
      },
      immediate: false
    }
  },
  methods: {
    async loadFlowData() {
      if (!this.instanceId) return

      this.loading = true

      try {
        // 使用专门的流程图API
        const response = await getInstanceDiagram(this.instanceId)

        if (response && response.code === 200 && response.data) {
          const { nodes, links, instance, records } = response.data

          console.log('FlowChart 流程图数据:', { nodes, links, instance, records })

          // 详细打印每个节点的状态
          if (nodes && nodes.length > 0) {
            console.log('=== 节点状态详情 ===')
            nodes.forEach((node, index) => {
              console.log(`节点${index + 1}: ${node.nodeName || node.name}`)
              console.log(`  - nodeId: ${node.nodeId}`)
              console.log(`  - nodeType: ${node.nodeType}`)
              console.log(`  - status: ${node.status}`)
              console.log(`  - approverNames: ${node.approverNames}`)
            })
            console.log('==================')
          }

          if (nodes && nodes.length > 0) {
            // 后端已经处理好了节点状态，直接使用
            this.flowData = { nodes: nodes }
            this.diagramData = {
              nodes: nodes,
              links: links || []
            }

            // 设置调试信息
            this.debugInfo = {
              recordsCount: records ? records.length : 0,
              instanceStatus: instance ? instance.status : 'unknown',
              recordMap: nodes.reduce((map, node) => {
                map[node.nodeId] = node.status
                return map
              }, {})
            }

            this.$nextTick(() => {
              this.renderDiagram()
            })
          } else {
            console.warn('FlowChart: 没有节点数据')
          }
        } else {
          console.error('FlowChart: API响应格式错误', response)
        }
      } catch (error) {
        console.error('获取流程图数据失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    generateLinks(nodes) {
      const links = []
      for (let i = 0; i < nodes.length - 1; i++) {
        links.push({
          source: nodes[i].nodeId,
          target: nodes[i + 1].nodeId
        })
      }
      return links
    },
    
    processNodes(nodes, records, instance) {
      const instanceStatus = instance ? instance.status : '0'
      
      // 更新调试信息
      this.debugInfo.instanceStatus = instanceStatus
      this.debugInfo.recordsCount = records ? records.length : 0
      
      console.log('FlowChart processNodes 输入:', { nodes, records, instance })
      
      // 构建审批记录映射
      const recordMap = {}
      if (records && records.length > 0) {
        records.forEach((record, index) => {
          console.log(`审批记录 ${index}:`, record)
          
          // 尝试多种可能的键值匹配
          const possibleKeys = [
            record.nodeId,
            record.nodeName,
            record.taskDefKey,
            record.activityId,
            String(record.nodeId),
            String(record.nodeName)
          ].filter(key => key != null && key !== '')
          
          possibleKeys.forEach(key => {
            // 检查多种可能的结果值
            if (record.result === '1' || record.result === 1 || 
                record.result === 'approve' || record.result === 'APPROVE' ||
                record.result === 'approved' || record.result === 'APPROVED' ||
                record.status === '1' || record.status === 1 ||
                record.status === 'approve' || record.status === 'APPROVE' ||
                record.status === 'approved' || record.status === 'APPROVED') {
              recordMap[key] = 'APPROVED'
              console.log(`设置节点 ${key} 为 APPROVED`)
            } else if (record.result === '2' || record.result === 2 || 
                       record.result === 'reject' || record.result === 'REJECT' ||
                       record.result === 'rejected' || record.result === 'REJECTED' ||
                       record.status === '2' || record.status === 2 ||
                       record.status === 'reject' || record.status === 'REJECT' ||
                       record.status === 'rejected' || record.status === 'REJECTED') {
              recordMap[key] = 'REJECTED'
              console.log(`设置节点 ${key} 为 REJECTED`)
            } else if (record.result === '3' || record.result === 3 || 
                       record.result === 'withdraw' || record.result === 'WITHDRAW' ||
                       record.result === 'canceled' || record.result === 'CANCELED' ||
                       record.status === '3' || record.status === 3 ||
                       record.status === 'withdraw' || record.status === 'WITHDRAW' ||
                       record.status === 'canceled' || record.status === 'CANCELED') {
              recordMap[key] = 'CANCELED'
              console.log(`设置节点 ${key} 为 CANCELED`)
            }
          })
        })
      }
      
      // 更新调试信息
      this.debugInfo.recordMap = recordMap
      
      console.log('FlowChart recordMap:', recordMap)

      // 确定当前活动节点
      let currentNodeId = null
      if (instance && instance.currentNodeId) {
        currentNodeId = instance.currentNodeId
      } else if (records && records.length > 0) {
        // 找到最新的未完成记录对应的下一个节点
        const latestRecord = records[records.length - 1]
        if (latestRecord) {
          // 如果最新记录是通过的，那么当前节点应该是下一个节点
          if (latestRecord.result === '1' || latestRecord.result === 1 || 
              latestRecord.result === 'approve' || latestRecord.result === 'APPROVE') {
            // 找到下一个审批节点
            const approvalNodes = nodes.filter(n => n.nodeType === '1')
            const currentIndex = approvalNodes.findIndex(n => 
              n.nodeId === latestRecord.nodeId || 
              n.nodeName === latestRecord.nodeName
            )
            if (currentIndex >= 0 && currentIndex < approvalNodes.length - 1) {
              currentNodeId = approvalNodes[currentIndex + 1].nodeId
            }
          } else {
            currentNodeId = latestRecord.nodeId
          }
        }
      }
      
      console.log('FlowChart currentNodeId:', currentNodeId)

      return nodes.map((node, index) => {
        let nodeStatus = 'WAITING'
        let displayName = node.nodeName || node.name

        if (node.nodeType === '0') {
          displayName = '开始'
          nodeStatus = 'APPROVED'
        } else if (node.nodeType === '2') {
          displayName = '结束'
          if (instanceStatus === '2' || instanceStatus === 2) {
            nodeStatus = 'APPROVED'
          } else if (instanceStatus === '3' || instanceStatus === 3) {
            nodeStatus = 'REJECTED'
          } else if (instanceStatus === '4' || instanceStatus === 4) {
            nodeStatus = 'CANCELED'
          } else {
            nodeStatus = 'WAITING'
          }
        } else if (node.nodeType === '1') {
          const approvalNodes = nodes.filter(n => n.nodeType === '1')
          const approvalIndex = approvalNodes.findIndex(n => n.nodeId === node.nodeId)
          const nodeNumber = approvalIndex >= 0 ? approvalIndex + 1 : (node.nodeOrder || 1)
          
          displayName = `第${nodeNumber}级审批`

          // 尝试多种可能的键值匹配
          const possibleKeys = [
            node.nodeId,
            node.nodeName,
            node.name,
            node.taskDefKey,
            node.activityId,
            String(node.nodeId),
            String(node.nodeName),
            String(node.name)
          ].filter(key => key != null && key !== '')
          
          console.log(`节点 ${displayName} 可能的键值:`, possibleKeys)
          
          // 检查是否在记录映射中找到状态
          let foundStatus = null
          for (const key of possibleKeys) {
            if (recordMap[key]) {
              foundStatus = recordMap[key]
              console.log(`节点 ${displayName} 找到状态: ${foundStatus} (通过键: ${key})`)
              break
            }
          }
          
          if (foundStatus) {
            nodeStatus = foundStatus
          } else if (currentNodeId && possibleKeys.includes(currentNodeId)) {
            nodeStatus = 'ACTIVE'
            console.log(`节点 ${displayName} 设置为活动状态`)
          } else {
            nodeStatus = 'WAITING'
            console.log(`节点 ${displayName} 设置为等待状态`)
          }
        }

        console.log(`最终节点状态 - ${displayName}: ${nodeStatus}`)

        return {
          ...node,
          nodeName: displayName,
          status: nodeStatus
        }
      })
    },

    /** 渲染流程图 */
    renderDiagram() {
      if (!this.diagramData || !this.diagramData.nodes || this.diagramData.nodes.length === 0) {
        return
      }

      // 检查容器是否存在
      const container = this.$refs.diagramContainer
      if (!container) {
        console.warn('FlowChart: 容器未找到，延迟渲染')
        this.$nextTick(() => {
          this.renderDiagram()
        })
        return
      }

      // 清空容器
      container.innerHTML = ''

      // 使用完整的D3.js渲染流程图实现
      this.renderDiagramWithD3(container, this.diagramData)
    },

    /** 使用D3.js渲染流程图 */
    renderDiagramWithD3(container, diagramData) {
      // 使用D3.js渲染流程图 - 圆点样式
      const width = container.clientWidth || 800
      const height = container.clientHeight || 200
      const circleRadius = 18 // 圆点半径

      const svg = d3.select(container)
        .append('svg')
        .attr('width', width)
        .attr('height', height)

      const nodes = diagramData.nodes
      const links = diagramData.links || []

      // 计算节点位置
      const nodePositions = []
      const xGap = Math.min(140, width / (nodes.length + 1))

      nodes.forEach((_, index) => {
        nodePositions.push({
          x: xGap * (index + 1),
          y: height / 2
        })
      })

      // 添加箭头标记
      svg.append('defs').append('marker')
        .attr('id', 'arrowhead')
        .attr('viewBox', '0 -5 10 10')
        .attr('refX', 8)
        .attr('refY', 0)
        .attr('orient', 'auto')
        .attr('markerWidth', 6)
        .attr('markerHeight', 6)
        .append('path')
        .attr('d', 'M0,-5L10,0L0,5')
        .attr('fill', '#999')

      // 绘制连线
      const lineGenerator = d3.line()
        .curve(d3.curveBasis)

      links.forEach((link) => {
        const sourceIndex = nodes.findIndex(n => String(n.nodeId) === String(link.source))
        const targetIndex = nodes.findIndex(n => String(n.nodeId) === String(link.target))

        if (sourceIndex >= 0 && targetIndex >= 0) {
          const sourcePos = nodePositions[sourceIndex]
          const targetPos = nodePositions[targetIndex]

          // 计算连线起点和终点（考虑圆点半径）
          const startX = sourcePos.x + circleRadius
          const startY = sourcePos.y
          const endX = targetPos.x - circleRadius
          const endY = targetPos.y

          svg.append('path')
            .attr('d', lineGenerator([[startX, startY], [endX, endY]]))
            .attr('stroke', '#999')
            .attr('stroke-width', 2)
            .attr('fill', 'none')
            .attr('marker-end', 'url(#arrowhead)')
        }
      })

      // 绘制节点
      const nodeGroups = svg.selectAll('.node-group')
        .data(nodes)
        .enter()
        .append('g')
        .attr('class', 'node-group')
        .attr('transform', (_, i) => `translate(${nodePositions[i].x}, ${nodePositions[i].y})`)

      // 添加文字标签（在圆点上方）
      nodeGroups.append('text')
        .attr('x', 0)
        .attr('y', -circleRadius - 10)
        .attr('text-anchor', 'middle')
        .attr('font-size', '12px')
        .attr('font-weight', 'bold')
        .attr('fill', '#333')
        .text(d => d.nodeName || d.name)

      // 添加圆点
      nodeGroups.append('circle')
        .attr('r', circleRadius)
        .attr('fill', d => {
          const color = this.getNodeColor(d.status)
          console.log(`节点 ${d.nodeName} 状态: ${d.status} -> 颜色: ${color}`)
          return color
        })
        .attr('stroke', '#fff')
        .attr('stroke-width', 2)

      // 添加状态图标
      nodeGroups.append('text')
        .attr('x', 0)
        .attr('y', 5)
        .attr('text-anchor', 'middle')
        .attr('font-size', '14px')
        .attr('fill', '#fff')
        .text(d => {
          const icon = this.getNodeIcon(d.status)
          console.log(`节点 ${d.nodeName} 状态: ${d.status} -> 图标: ${icon}`)
          return icon
        })
    },

    /** 获取节点颜色 */
    getNodeColor(status) {
      switch (status) {
        case 'APPROVED':
          return '#67C23A' // 绿色
        case 'REJECTED':
          return '#F56C6C' // 红色
        case 'ACTIVE':
          return '#409EFF' // 蓝色
        case 'CANCELED':
          return '#909399' // 灰色
        case 'WAITING':
        default:
          return '#E6E6E6' // 浅灰色
      }
    },

    /** 获取节点图标 */
    getNodeIcon(status) {
      switch (status) {
        case 'APPROVED':
          return '✓'
        case 'REJECTED':
          return '✗'
        case 'ACTIVE':
          return '●'
        case 'CANCELED':
          return '◐'
        case 'WAITING':
        default:
          return '○'
      }
    }
  }
}
</script>

<style scoped>
.flow-chart-container {
  position: relative;
  overflow-x: auto;
  overflow-y: hidden;
  padding: 20px 0;
}

.loading-container, .empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 150px;
}

.diagram-container {
  width: 100%;
  height: 100%;
  min-height: 150px;
}

/* 连接线动画 */
:deep(.flow-line-active) {
  animation: dash 2s linear infinite;
}

@keyframes dash {
  0% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: -16px;
  }
}
</style>
