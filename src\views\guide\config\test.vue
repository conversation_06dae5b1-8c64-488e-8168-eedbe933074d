<template>
  <div class="app-container">
    <div class="page-header">
      <h2>导寻配置测试页面</h2>
      <p>如果您看到这个页面，说明路由配置是正确的。</p>
    </div>

    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>测试信息</span>
      </div>

      <div class="test-info">
        <p><strong>当前路由：</strong>{{ $route.path }}</p>
        <p><strong>当前时间：</strong>{{ currentTime }}</p>
        <p><strong>路由名称：</strong>{{ $route.name }}</p>
        <p><strong>页面组件：</strong>GuideConfigTest</p>
      </div>

      <div class="test-actions">
        <el-button type="primary" @click="goBack">返回</el-button>
        <el-button type="success" @click="goToLog">测试日志页面</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: "GuideConfigTest",
  data() {
    return {
      currentTime: new Date().toLocaleString()
    };
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },
    goToLog() {
      this.$router.push('/guide/log');
    }
  }
};
</script>

<style scoped>
.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 20px;
}

.page-header p {
  margin: 0;
  color: #909399;
}

.test-info {
  margin-bottom: 20px;
}

.test-info p {
  margin: 10px 0;
  font-size: 14px;
}

.test-actions {
  margin-top: 20px;
}
</style>
