// EMQX客户端管理系统数据处理工具类

/**
 * 格式化数字显示
 * @param {number} num 数字
 * @returns {string} 格式化后的字符串
 */
export function formatNumber(num) {
  if (!num && num !== 0) return '0'
  
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

/**
 * 格式化时间显示
 * @param {number|string|Date} time 时间
 * @returns {string} 格式化后的时间字符串
 */
export function formatTime(time) {
  if (!time) return '-'
  
  const date = new Date(time)
  if (isNaN(date.getTime())) return '-'
  
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

/**
 * 格式化相对时间
 * @param {number|string|Date} time 时间
 * @returns {string} 相对时间字符串
 */
export function formatRelativeTime(time) {
  if (!time) return '-'
  
  const now = Date.now()
  const timestamp = new Date(time).getTime()
  const diff = now - timestamp
  
  if (diff < 60000) {
    return '刚刚'
  } else if (diff < 3600000) {
    return Math.floor(diff / 60000) + '分钟前'
  } else if (diff < 86400000) {
    return Math.floor(diff / 3600000) + '小时前'
  } else if (diff < 2592000000) {
    return Math.floor(diff / 86400000) + '天前'
  } else {
    return formatTime(time)
  }
}

/**
 * 格式化运行时间
 * @param {number} uptime 运行时间戳
 * @returns {string} 运行时间字符串
 */
export function formatUptime(uptime) {
  if (!uptime) return '-'
  
  const now = Date.now()
  const diff = now - uptime
  const days = Math.floor(diff / (24 * 60 * 60 * 1000))
  const hours = Math.floor((diff % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000))
  const minutes = Math.floor((diff % (60 * 60 * 1000)) / (60 * 1000))
  
  if (days > 0) {
    return `${days}天${hours}小时`
  } else if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes) {
  if (!bytes && bytes !== 0) return '0 B'
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let size = bytes
  let unitIndex = 0
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }
  
  return size.toFixed(1) + ' ' + units[unitIndex]
}

/**
 * 生成客户端ID
 * @param {string} deviceType 设备类型
 * @param {string} deviceId 设备ID
 * @returns {string} 客户端ID
 */
export function generateClientId(deviceType, deviceId) {
  const prefixMap = {
    '出库防错客户端': 'outmis_client_',
    '门禁设备客户端': 'access_client_',
    '视频监控客户端': 'video_client_',
    '智能导寻客户端': 'guide_client_',
    '库房组态客户端': 'dwms_client_',
    '称重设备客户端': 'weight_client_'
  }
  
  const prefix = prefixMap[deviceType] || 'device_client_'
  return prefix + deviceId
}

/**
 * 获取设备类型对应的服务器客户端ID
 * @param {string} deviceType 设备类型
 * @returns {string} 服务器客户端ID
 */
export function getServerClientId(deviceType) {
  const serverMap = {
    '出库防错客户端': 'outmis_server_mqtt',
    '门禁设备客户端': 'access_server_mqtt',
    '视频监控客户端': 'video_server_mqtt',
    '智能导寻客户端': 'guide_server_mqtt',
    '库房组态客户端': 'dwms_server_mqtt',
    '称重设备客户端': 'outmis_server_mqtt'
  }
  
  return serverMap[deviceType] || 'default_server_mqtt'
}

/**
 * 验证客户端ID格式
 * @param {string} clientId 客户端ID
 * @returns {boolean} 是否有效
 */
export function validateClientId(clientId) {
  if (!clientId || typeof clientId !== 'string') return false
  
  // 客户端ID规则：字母、数字、下划线，长度3-128
  const regex = /^[a-zA-Z0-9_]{3,128}$/
  return regex.test(clientId)
}

/**
 * 验证用户名格式
 * @param {string} username 用户名
 * @returns {boolean} 是否有效
 */
export function validateUsername(username) {
  if (!username || typeof username !== 'string') return false
  
  // 用户名规则：字母、数字、下划线，长度3-64
  const regex = /^[a-zA-Z0-9_]{3,64}$/
  return regex.test(username)
}

/**
 * 验证密码强度
 * @param {string} password 密码
 * @returns {object} 验证结果
 */
export function validatePassword(password) {
  const result = {
    valid: false,
    strength: 'weak',
    message: '',
    score: 0
  }
  
  if (!password) {
    result.message = '密码不能为空'
    return result
  }
  
  if (password.length < 6) {
    result.message = '密码长度至少6位'
    return result
  }
  
  let score = 0
  
  // 长度检查
  if (password.length >= 8) score += 1
  if (password.length >= 12) score += 1
  
  // 字符类型检查
  if (/[a-z]/.test(password)) score += 1
  if (/[A-Z]/.test(password)) score += 1
  if (/[0-9]/.test(password)) score += 1
  if (/[^a-zA-Z0-9]/.test(password)) score += 1
  
  result.score = score
  
  if (score >= 4) {
    result.strength = 'strong'
    result.valid = true
    result.message = '密码强度：强'
  } else if (score >= 2) {
    result.strength = 'medium'
    result.valid = true
    result.message = '密码强度：中等'
  } else {
    result.strength = 'weak'
    result.valid = password.length >= 6
    result.message = '密码强度：弱'
  }
  
  return result
}

/**
 * 解析MQTT主题
 * @param {string} topic 主题
 * @returns {object} 解析结果
 */
export function parseMqttTopic(topic) {
  if (!topic) return null
  
  const parts = topic.split('/')
  const result = {
    original: topic,
    parts: parts,
    levels: parts.length,
    hasWildcard: topic.includes('+') || topic.includes('#'),
    isValid: true
  }
  
  // 验证主题格式
  if (topic.includes('#') && !topic.endsWith('#')) {
    result.isValid = false
    result.error = '多级通配符#只能出现在主题末尾'
  }
  
  if (topic.includes('##')) {
    result.isValid = false
    result.error = '不能连续使用多级通配符'
  }
  
  return result
}

/**
 * 格式化MQTT主题
 * @param {string} topic 原始主题
 * @returns {string} 格式化后的主题
 */
export function formatMqttTopic(topic) {
  if (!topic) return ''
  
  // 移除多余的斜杠
  let formatted = topic.replace(/\/+/g, '/')
  
  // 移除开头和结尾的斜杠
  formatted = formatted.replace(/^\/|\/$/g, '')
  
  // 替换模板变量为通配符
  formatted = formatted.replace(/\{[^}]+\}/g, '+')
  
  return formatted
}

/**
 * 计算同步率
 * @param {number} matched 匹配数量
 * @param {number} total 总数量
 * @returns {number} 同步率（0-1）
 */
export function calculateSyncRate(matched, total) {
  if (!total || total === 0) return 0
  return Math.min(matched / total, 1)
}

/**
 * 获取状态标签类型
 * @param {string} status 状态
 * @param {string} category 分类
 * @returns {string} 标签类型
 */
export function getStatusTagType(status, category = 'default') {
  const statusMap = {
    server: {
      'ONLINE': 'success',
      'OFFLINE': 'danger',
      'CONNECTING': 'warning',
      'ERROR': 'danger'
    },
    device: {
      'ACTIVE': 'success',
      'INACTIVE': 'info',
      'DISABLED': 'danger',
      'EXPIRED': 'warning'
    },
    cleanup: {
      'COMPLETED': 'success',
      'RUNNING': 'warning',
      'FAILED': 'danger',
      'CANCELLED': 'info'
    }
  }
  
  return statusMap[category]?.[status] || 'info'
}

/**
 * 深度克隆对象
 * @param {any} obj 要克隆的对象
 * @returns {any} 克隆后的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime())
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  if (typeof obj === 'object') {
    const cloned = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key])
      }
    }
    return cloned
  }
}

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} delay 延迟时间
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, delay) {
  let timeoutId
  return function (...args) {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func.apply(this, args), delay)
  }
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} limit 时间限制
 * @returns {Function} 节流后的函数
 */
export function throttle(func, limit) {
  let inThrottle
  return function (...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// 导出所有工具函数
export default {
  formatNumber,
  formatTime,
  formatRelativeTime,
  formatUptime,
  formatFileSize,
  generateClientId,
  getServerClientId,
  validateClientId,
  validateUsername,
  validatePassword,
  parseMqttTopic,
  formatMqttTopic,
  calculateSyncRate,
  getStatusTagType,
  deepClone,
  debounce,
  throttle
}
