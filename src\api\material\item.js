import request from '@/utils/request'

// 查询物料清单明细列表
export function listMaterialBillItem(query) {
  return request({
    url: '/material/item/list',
    method: 'get',
    params: query
  })
}

// 根据物料清单ID查询明细列表
export function listMaterialBillItemByBillId(billId) {
  return request({
    url: '/material/item/listByBillId/' + billId,
    method: 'get'
  })
}

// 根据物料清单编号查询明细列表
export function listMaterialBillItemByBillCode(billCode) {
  return request({
    url: '/material/item/listByBillCode/' + billCode,
    method: 'get'
  })
}

// 查询物料清单明细详细
export function getMaterialBillItem(id) {
  return request({
    url: '/material/item/' + id,
    method: 'get'
  })
}

// 新增物料清单明细
export function addMaterialBillItem(data) {
  return request({
    url: '/material/item',
    method: 'post',
    data: data
  })
}

// 修改物料清单明细
export function updateMaterialBillItem(data) {
  return request({
    url: '/material/item',
    method: 'put',
    data: data
  })
}

// 删除物料清单明细
export function delMaterialBillItem(id) {
  return request({
    url: '/material/item/' + id,
    method: 'delete'
  })
}

// 批量新增物料清单明细
export function batchAddMaterialBillItem(data) {
  return request({
    url: '/material/item/batch',
    method: 'post',
    data: data
  })
}

// 检查物料是否已存在于清单中
export function checkMaterialExists(billId, materialId) {
  return request({
    url: '/material/item/checkMaterial/' + billId + '/' + materialId,
    method: 'get'
  })
}

// 获取物料清单明细统计信息
export function getMaterialBillItemStatistics(billId) {
  return request({
    url: '/material/item/statistics/' + billId,
    method: 'get'
  })
}

// 更新明细排序
export function updateItemSort(id, sortOrder) {
  return request({
    url: '/material/item/updateSort/' + id + '/' + sortOrder,
    method: 'put'
  })
}

// 获取最大排序号
export function getMaxSortOrder(billId) {
  return request({
    url: '/material/item/maxSort/' + billId,
    method: 'get'
  })
}

// 根据物料ID查询明细列表
export function listMaterialBillItemByMaterialId(materialId) {
  return request({
    url: '/material/item/listByMaterialId/' + materialId,
    method: 'get'
  })
}

// 批量更新明细重量
export function batchUpdateItemWeight(data) {
  return request({
    url: '/material/item/batchUpdateWeight',
    method: 'put',
    data: data
  })
}

// 重新计算并更新所有明细的总重量
export function recalculateItemWeight(billId) {
  return request({
    url: '/material/item/recalculateWeight/' + billId,
    method: 'put'
  })
}

// 添加物料到清单
export function addMaterialsToBill(billId, materialIds, quantities) {
  return request({
    url: '/material/item/addMaterials/' + billId,
    method: 'post',
    data: materialIds,
    params: { quantities: quantities }
  })
}

// 导入明细数据
export function importMaterialBillItem(billId, data, updateSupport) {
  return request({
    url: '/material/item/importData/' + billId,
    method: 'post',
    data: data,
    params: { updateSupport: updateSupport }
  })
}

// 调整明细顺序
export function adjustItemOrder(itemId, direction) {
  return request({
    url: '/material/item/adjustOrder/' + itemId + '/' + direction,
    method: 'put'
  })
}

// 复制明细到其他清单
export function copyItemsToOtherBill(targetBillId, sourceItemIds) {
  return request({
    url: '/material/item/copyToOtherBill/' + targetBillId,
    method: 'post',
    data: sourceItemIds
  })
}

// 导出物料清单明细
export function exportMaterialBillItem(query) {
  return request({
    url: '/material/item/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}
