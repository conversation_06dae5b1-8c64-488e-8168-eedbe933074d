<template>
  <div class="auto-subscription-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="page-title">
        <h2><i class="el-icon-magic-stick"></i> EMQX自动订阅管理</h2>
        <p>扫描EMQX主题并按模块自动分类订阅</p>
      </div>
      <div class="page-actions">
        <el-button type="primary" icon="el-icon-search" @click="scanAndClassify" :loading="scanning">
          扫描并分类
        </el-button>
        <el-button type="success" icon="el-icon-refresh" @click="initializeAllModules" :loading="initializing">
          初始化所有模块
        </el-button>
        <el-button type="info" icon="el-icon-setting" @click="showConfigDialog">
          配置管理
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total">
              <i class="el-icon-collection"></i>
            </div>
            <div class="stats-info">
              <div class="stats-value">{{ scanResult.totalTopics || 0 }}</div>
              <div class="stats-label">扫描主题总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon modules">
              <i class="el-icon-menu"></i>
            </div>
            <div class="stats-info">
              <div class="stats-value">{{ Object.keys(scanResult.moduleTopics || {}).length }}</div>
              <div class="stats-label">识别模块数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon devices">
              <i class="el-icon-cpu"></i>
            </div>
            <div class="stats-info">
              <div class="stats-value">{{ Object.keys(scanResult.deviceTypeTopics || {}).length }}</div>
              <div class="stats-label">设备类型数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon functions">
              <i class="el-icon-operation"></i>
            </div>
            <div class="stats-info">
              <div class="stats-value">{{ Object.keys(scanResult.functionTopics || {}).length }}</div>
              <div class="stats-label">功能类型数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 主要内容区域 -->
    <el-tabs v-model="activeTab" class="main-tabs">
      <!-- 模块管理 -->
      <el-tab-pane label="模块管理" name="modules">
        <el-card class="module-card">
          <div slot="header">
            <span>模块订阅管理</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="loadModuleConfigs">
              刷新
            </el-button>
          </div>
          
          <el-row :gutter="20">
            <el-col :span="8" v-for="(config, moduleName) in moduleConfigs" :key="moduleName">
              <el-card class="module-item" shadow="hover">
                <div class="module-header">
                  <div class="module-name">{{ config.description || moduleName }}</div>
                  <el-tag :type="config.autoSubscribe ? 'success' : 'info'" size="mini">
                    {{ config.autoSubscribe ? '已启用' : '已禁用' }}
                  </el-tag>
                </div>
                
                <div class="module-info">
                  <div class="info-item">
                    <span class="label">模块名称:</span>
                    <span class="value">{{ moduleName }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">客户端ID:</span>
                    <span class="value">{{ config.clientId }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">优先级:</span>
                    <span class="value">{{ config.priority }}</span>
                  </div>
                </div>
                
                <div class="module-actions">
                  <el-button size="mini" type="primary" @click="initializeModule(moduleName)">
                    初始化
                  </el-button>
                  <el-button size="mini" type="success" @click="autoSubscribeModule(moduleName)">
                    自动订阅
                  </el-button>
                  <el-button size="mini" type="warning" @click="syncModule(moduleName)">
                    同步
                  </el-button>
                  <el-button size="mini" type="info" @click="verifyModule(moduleName)">
                    验证
                  </el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-card>
      </el-tab-pane>

      <!-- 主题分类 -->
      <el-tab-pane label="主题分类" name="classification">
        <el-card class="classification-card">
          <div slot="header">
            <span>主题分类结果</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="scanAndClassify">
              重新扫描
            </el-button>
          </div>
          
          <el-tabs v-model="classificationTab" type="card">
            <!-- 按模块分类 -->
            <el-tab-pane label="按模块分类" name="module">
              <div v-for="(topics, module) in scanResult.moduleTopics" :key="module" class="classification-group">
                <div class="group-header">
                  <h4>{{ module }} 模块 ({{ topics.length }}个主题)</h4>
                  <el-button size="mini" type="primary" @click="subscribeModuleTopics(module, topics)">
                    订阅全部
                  </el-button>
                </div>
                <div class="topic-list">
                  <el-tag
                    v-for="topic in topics"
                    :key="topic.topic"
                    class="topic-tag"
                    :title="topic.topic"
                  >
                    {{ topic.topic }}
                  </el-tag>
                </div>
              </div>
            </el-tab-pane>

            <!-- 按设备类型分类 -->
            <el-tab-pane label="按设备类型分类" name="deviceType">
              <div v-for="(topics, deviceType) in scanResult.deviceTypeTopics" :key="deviceType" class="classification-group">
                <div class="group-header">
                  <h4>{{ deviceType }} 设备 ({{ topics.length }}个主题)</h4>
                  <el-button size="mini" type="success" @click="subscribeDeviceTypeTopics(deviceType, topics)">
                    订阅全部
                  </el-button>
                </div>
                <div class="topic-list">
                  <el-tag
                    v-for="topic in topics"
                    :key="topic.topic"
                    class="topic-tag"
                    type="success"
                    :title="topic.topic"
                  >
                    {{ topic.topic }}
                  </el-tag>
                </div>
              </div>
            </el-tab-pane>

            <!-- 按功能分类 -->
            <el-tab-pane label="按功能分类" name="function">
              <div v-for="(topics, func) in scanResult.functionTopics" :key="func" class="classification-group">
                <div class="group-header">
                  <h4>{{ func }} 功能 ({{ topics.length }}个主题)</h4>
                  <el-button size="mini" type="warning" @click="subscribeFunctionTopics(func, topics)">
                    订阅全部
                  </el-button>
                </div>
                <div class="topic-list">
                  <el-tag
                    v-for="topic in topics"
                    :key="topic.topic"
                    class="topic-tag"
                    type="warning"
                    :title="topic.topic"
                  >
                    {{ topic.topic }}
                  </el-tag>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-tab-pane>

      <!-- 推荐主题 -->
      <el-tab-pane label="推荐主题" name="recommended">
        <el-card class="recommended-card">
          <div slot="header">
            <span>模块推荐主题</span>
          </div>
          
          <el-select v-model="selectedModule" placeholder="选择模块" @change="loadRecommendedTopics" style="width: 200px; margin-bottom: 20px;">
            <el-option
              v-for="(config, moduleName) in moduleConfigs"
              :key="moduleName"
              :label="config.description || moduleName"
              :value="moduleName"
            />
          </el-select>
          
          <div v-if="recommendedTopics.length > 0" class="recommended-topics">
            <el-table :data="recommendedTopics" style="width: 100%">
              <el-table-column prop="topic" label="主题" min-width="300">
                <template slot-scope="scope">
                  <span class="topic-name">{{ scope.row.topic }}</span>
                </template>
              </el-table-column>
              
              <el-table-column prop="qos" label="QoS等级" width="100" align="center">
                <template slot-scope="scope">
                  <el-tag :type="getQosTagType(scope.row.qos)" size="small">
                    QoS {{ scope.row.qos }}
                  </el-tag>
                </template>
              </el-table-column>
              
              <el-table-column prop="description" label="描述" width="150" />
              
              <el-table-column label="操作" width="150" align="center">
                <template slot-scope="scope">
                  <el-button size="mini" type="primary" @click="subscribeRecommendedTopic(scope.row)">
                    订阅
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          
          <div v-else-if="selectedModule" class="empty-state">
            <p>该模块暂无推荐主题</p>
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <!-- 操作日志 -->
    <el-card class="log-card" style="margin-top: 20px;">
      <div slot="header">
        <span>操作日志</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="clearLogs">
          清空日志
        </el-button>
      </div>
      
      <div class="operation-logs">
        <el-timeline v-if="operationLogs.length > 0">
          <el-timeline-item
            v-for="(log, index) in operationLogs"
            :key="index"
            :type="log.success ? 'success' : 'danger'"
            :timestamp="log.timestamp"
          >
            <div class="log-content">
              <div class="log-operation">{{ log.operation }}</div>
              <div class="log-message" :class="log.success ? 'success' : 'error'">
                {{ log.message }}
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
        
        <div v-else class="empty-state">
          <p>暂无操作记录</p>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import {
  scanAndClassifyTopics,
  getAllModuleConfigs,
  initializeAllModules,
  initializeModuleSubscriptions,
  autoSubscribeForModule,
  autoSubscribeForDeviceType,
  autoSubscribeForFunction,
  syncModuleSubscriptions,
  verifyModuleSubscriptions,
  getRecommendedTopicsForModule
} from '@/api/emqx/autoSubscription'

export default {
  name: 'AutoSubscriptionManagement',
  data() {
    return {
      activeTab: 'modules',
      classificationTab: 'module',
      scanning: false,
      initializing: false,
      
      // 扫描结果
      scanResult: {
        totalTopics: 0,
        moduleTopics: {},
        deviceTypeTopics: {},
        functionTopics: {}
      },
      
      // 模块配置
      moduleConfigs: {},
      
      // 推荐主题
      selectedModule: '',
      recommendedTopics: [],
      
      // 操作日志
      operationLogs: []
    }
  },
  
  created() {
    this.init()
  },
  
  methods: {
    // 初始化
    async init() {
      await this.loadModuleConfigs()
      this.addLog('系统初始化', true, '自动订阅管理页面加载完成')
    },

    // 扫描并分类主题
    async scanAndClassify() {
      this.scanning = true
      try {
        this.addLog('扫描主题', true, '开始扫描EMQX中的主题...')

        const response = await scanAndClassifyTopics()
        if (response.code === 200) {
          this.scanResult = response.data
          this.addLog('扫描主题', true, `扫描完成，发现${this.scanResult.totalTopics}个主题`)
        } else {
          this.addLog('扫描主题', false, response.msg)
          this.$message.error('扫描失败: ' + response.msg)
        }
      } catch (error) {
        this.addLog('扫描主题', false, error.message)
        this.$message.error('扫描失败')
      } finally {
        this.scanning = false
      }
    },

    // 加载模块配置
    async loadModuleConfigs() {
      try {
        const response = await getAllModuleConfigs()
        if (response.code === 200) {
          this.moduleConfigs = response.data
        }
      } catch (error) {
        console.error('加载模块配置失败:', error)
      }
    },

    // 初始化所有模块
    async initializeAllModules() {
      this.initializing = true
      try {
        this.addLog('批量初始化', true, '开始初始化所有模块...')

        const response = await initializeAllModules()
        if (response.code === 200) {
          const result = response.data
          this.addLog('批量初始化', result.success,
            `初始化完成: 成功${result.successCount}, 失败${result.failureCount}`)

          if (result.success) {
            this.$message.success('所有模块初始化成功')
          } else {
            this.$message.warning(`部分模块初始化失败，请查看详细日志`)
          }
        } else {
          this.addLog('批量初始化', false, response.msg)
          this.$message.error('初始化失败: ' + response.msg)
        }
      } catch (error) {
        this.addLog('批量初始化', false, error.message)
        this.$message.error('初始化失败')
      } finally {
        this.initializing = false
      }
    },

    // 初始化单个模块
    async initializeModule(moduleName) {
      try {
        this.addLog('模块初始化', true, `开始初始化模块: ${moduleName}`)

        const response = await initializeModuleSubscriptions(moduleName)
        if (response.code === 200) {
          this.addLog('模块初始化', true, `模块${moduleName}初始化成功`)
          this.$message.success(`模块${moduleName}初始化成功`)
        } else {
          this.addLog('模块初始化', false, `模块${moduleName}初始化失败: ${response.msg}`)
          this.$message.error(`初始化失败: ${response.msg}`)
        }
      } catch (error) {
        this.addLog('模块初始化', false, `模块${moduleName}初始化异常: ${error.message}`)
        this.$message.error('初始化失败')
      }
    },

    // 模块自动订阅
    async autoSubscribeModule(moduleName) {
      try {
        this.addLog('自动订阅', true, `开始为模块${moduleName}自动订阅`)

        const response = await autoSubscribeForModule(moduleName)
        if (response.code === 200) {
          const result = response.data
          this.addLog('自动订阅', result.success,
            `模块${moduleName}自动订阅完成: 成功${result.successCount}, 失败${result.failureCount}`)

          if (result.success) {
            this.$message.success(`模块${moduleName}自动订阅成功`)
          } else {
            this.$message.warning(`模块${moduleName}部分订阅失败`)
          }
        } else {
          this.addLog('自动订阅', false, `模块${moduleName}自动订阅失败: ${response.msg}`)
          this.$message.error(`自动订阅失败: ${response.msg}`)
        }
      } catch (error) {
        this.addLog('自动订阅', false, `模块${moduleName}自动订阅异常: ${error.message}`)
        this.$message.error('自动订阅失败')
      }
    },

    // 同步模块订阅
    async syncModule(moduleName) {
      try {
        this.addLog('同步订阅', true, `开始同步模块${moduleName}的订阅`)

        const response = await syncModuleSubscriptions(moduleName)
        if (response.code === 200) {
          const result = response.data
          this.addLog('同步订阅', result.success,
            `模块${moduleName}同步完成: 同步${result.syncedCount}个主题`)

          if (result.success) {
            this.$message.success(`模块${moduleName}同步成功`)
          } else {
            this.$message.error(`同步失败: ${result.error}`)
          }
        } else {
          this.addLog('同步订阅', false, `模块${moduleName}同步失败: ${response.msg}`)
          this.$message.error(`同步失败: ${response.msg}`)
        }
      } catch (error) {
        this.addLog('同步订阅', false, `模块${moduleName}同步异常: ${error.message}`)
        this.$message.error('同步失败')
      }
    },

    // 验证模块订阅
    async verifyModule(moduleName) {
      try {
        this.addLog('验证订阅', true, `开始验证模块${moduleName}的订阅状态`)

        const response = await verifyModuleSubscriptions(moduleName)
        if (response.code === 200) {
          const result = response.data
          const rate = (result.subscriptionRate * 100).toFixed(1)
          this.addLog('验证订阅', true,
            `模块${moduleName}验证完成: 订阅率${rate}% (${result.existingCount}/${result.recommendedCount})`)

          this.$message.success(`验证完成，订阅率: ${rate}%`)
        } else {
          this.addLog('验证订阅', false, `模块${moduleName}验证失败: ${response.msg}`)
          this.$message.error(`验证失败: ${response.msg}`)
        }
      } catch (error) {
        this.addLog('验证订阅', false, `模块${moduleName}验证异常: ${error.message}`)
        this.$message.error('验证失败')
      }
    },

    // 订阅模块主题
    async subscribeModuleTopics(module, topics) {
      try {
        this.addLog('批量订阅', true, `开始为模块${module}订阅${topics.length}个主题`)

        const response = await autoSubscribeForModule(module)
        if (response.code === 200) {
          const result = response.data
          this.addLog('批量订阅', result.success,
            `模块${module}批量订阅完成: 成功${result.successCount}, 失败${result.failureCount}`)

          if (result.success) {
            this.$message.success(`模块${module}批量订阅成功`)
          } else {
            this.$message.warning(`模块${module}部分订阅失败`)
          }
        } else {
          this.addLog('批量订阅', false, `模块${module}批量订阅失败: ${response.msg}`)
          this.$message.error(`批量订阅失败: ${response.msg}`)
        }
      } catch (error) {
        this.addLog('批量订阅', false, `模块${module}批量订阅异常: ${error.message}`)
        this.$message.error('批量订阅失败')
      }
    },

    // 订阅设备类型主题
    async subscribeDeviceTypeTopics(deviceType, topics) {
      try {
        this.addLog('设备订阅', true, `开始为设备类型${deviceType}订阅${topics.length}个主题`)

        const response = await autoSubscribeForDeviceType(deviceType)
        if (response.code === 200) {
          const result = response.data
          this.addLog('设备订阅', result.success,
            `设备类型${deviceType}订阅完成: 成功${result.successCount}, 失败${result.failureCount}`)

          if (result.success) {
            this.$message.success(`设备类型${deviceType}订阅成功`)
          } else {
            this.$message.warning(`设备类型${deviceType}部分订阅失败`)
          }
        } else {
          this.addLog('设备订阅', false, `设备类型${deviceType}订阅失败: ${response.msg}`)
          this.$message.error(`订阅失败: ${response.msg}`)
        }
      } catch (error) {
        this.addLog('设备订阅', false, `设备类型${deviceType}订阅异常: ${error.message}`)
        this.$message.error('订阅失败')
      }
    },

    // 订阅功能主题
    async subscribeFunctionTopics(func, topics) {
      try {
        this.addLog('功能订阅', true, `开始为功能${func}订阅${topics.length}个主题`)

        const response = await autoSubscribeForFunction(func)
        if (response.code === 200) {
          const result = response.data
          this.addLog('功能订阅', result.success,
            `功能${func}订阅完成: 成功${result.successCount}, 失败${result.failureCount}`)

          if (result.success) {
            this.$message.success(`功能${func}订阅成功`)
          } else {
            this.$message.warning(`功能${func}部分订阅失败`)
          }
        } else {
          this.addLog('功能订阅', false, `功能${func}订阅失败: ${response.msg}`)
          this.$message.error(`订阅失败: ${response.msg}`)
        }
      } catch (error) {
        this.addLog('功能订阅', false, `功能${func}订阅异常: ${error.message}`)
        this.$message.error('订阅失败')
      }
    },

    // 加载推荐主题
    async loadRecommendedTopics() {
      if (!this.selectedModule) return

      try {
        const response = await getRecommendedTopicsForModule(this.selectedModule)
        if (response.code === 200) {
          this.recommendedTopics = response.data
        }
      } catch (error) {
        console.error('加载推荐主题失败:', error)
        this.recommendedTopics = []
      }
    },

    // 订阅推荐主题
    async subscribeRecommendedTopic(topic) {
      try {
        this.addLog('推荐订阅', true, `订阅推荐主题: ${topic.topic}`)

        // 这里可以调用单个主题订阅API
        this.$message.success(`主题${topic.topic}订阅成功`)
        this.addLog('推荐订阅', true, `主题${topic.topic}订阅成功`)
      } catch (error) {
        this.addLog('推荐订阅', false, `主题${topic.topic}订阅失败: ${error.message}`)
        this.$message.error('订阅失败')
      }
    },

    // 显示配置对话框
    showConfigDialog() {
      this.$message.info('配置管理功能开发中...')
    },

    // 添加操作日志
    addLog(operation, success, message) {
      this.operationLogs.unshift({
        operation,
        success,
        message,
        timestamp: new Date().toLocaleTimeString()
      })

      // 限制日志数量
      if (this.operationLogs.length > 100) {
        this.operationLogs = this.operationLogs.slice(0, 100)
      }
    },

    // 清空日志
    clearLogs() {
      this.operationLogs = []
      this.$message.success('日志已清空')
    },

    // 获取QoS标签类型
    getQosTagType(qos) {
      const typeMap = { 0: 'info', 1: 'success', 2: 'warning' }
      return typeMap[qos] || 'info'
    }
  }
}
</script>

<style scoped>
.auto-subscription-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title h2 {
  margin: 0;
  color: #303133;
}

.page-title p {
  margin: 5px 0 0 0;
  color: #909399;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stats-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  margin-right: 15px;
}

.stats-icon.total { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.stats-icon.modules { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.stats-icon.devices { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.stats-icon.functions { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

.stats-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.module-item {
  margin-bottom: 20px;
  height: 200px;
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.module-name {
  font-weight: bold;
  color: #303133;
}

.module-info {
  margin-bottom: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 12px;
}

.info-item .label {
  color: #909399;
}

.info-item .value {
  color: #303133;
  font-family: 'Courier New', monospace;
}

.module-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.classification-group {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.group-header h4 {
  margin: 0;
  color: #303133;
}

.topic-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.topic-tag {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.topic-name {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.recommended-topics {
  margin-top: 20px;
}

.operation-logs {
  max-height: 300px;
  overflow-y: auto;
}

.log-content {
  padding: 5px 0;
}

.log-operation {
  font-weight: bold;
  color: #303133;
}

.log-message.success {
  color: #67c23a;
}

.log-message.error {
  color: #f56c6c;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}
</style>
