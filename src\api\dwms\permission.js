import request from '@/utils/request'

// 查询权限列表
export function listPermissions(query) {
  return request({
    url: '/dwms/permission/list',
    method: 'get',
    params: query
  })
}

// 查询权限详细
export function getPermission(id) {
  return request({
    url: '/dwms/permission/' + id,
    method: 'get'
  })
}

// 新增权限
export function addPermission(data) {
  return request({
    url: '/dwms/permission',
    method: 'post',
    data: data
  })
}

// 修改权限
export function updatePermission(data) {
  return request({
    url: '/dwms/permission',
    method: 'put',
    data: data
  })
}

// 删除权限
export function delPermission(id) {
  return request({
    url: '/dwms/permission/' + id,
    method: 'delete'
  })
}

// 获取权限树
export function getPermissionTree() {
  return request({
    url: '/dwms/permission/tree',
    method: 'get'
  })
}

// 根据模块获取权限列表
export function getPermissionsByModule(module) {
  return request({
    url: '/dwms/permission/module/' + module,
    method: 'get'
  })
}

// 根据角色获取权限列表
export function getPermissionsByRole(roleId) {
  return request({
    url: '/dwms/permission/role/' + roleId,
    method: 'get'
  })
}

// 为角色分配权限
export function assignPermissionsToRole(data) {
  return request({
    url: '/dwms/permission/assign/role',
    method: 'post',
    data: data
  })
}

// 为用户分配权限
export function assignPermissionsToUser(data) {
  return request({
    url: '/dwms/permission/assign/user',
    method: 'post',
    data: data
  })
}

// 检查用户权限
export function checkUserPermission(userId, permissionCode) {
  return request({
    url: '/dwms/permission/check',
    method: 'get',
    params: {
      userId,
      permissionCode
    }
  })
}

// 获取用户所有权限
export function getUserPermissions(userId) {
  return request({
    url: '/dwms/permission/user/' + userId,
    method: 'get'
  })
}

// 获取用户权限代码列表
export function getUserPermissionCodes(userId) {
  return request({
    url: '/dwms/permission/user/' + userId + '/codes',
    method: 'get'
  })
}

// 获取菜单权限
export function getMenuPermissions() {
  return request({
    url: '/dwms/permission/menu',
    method: 'get'
  })
}

// 获取按钮权限
export function getButtonPermissions() {
  return request({
    url: '/dwms/permission/button',
    method: 'get'
  })
}

// 获取数据权限
export function getDataPermissions() {
  return request({
    url: '/dwms/permission/data',
    method: 'get'
  })
}

// 同步系统权限
export function syncSystemPermissions() {
  return request({
    url: '/dwms/permission/sync',
    method: 'post'
  })
}

// 批量启用/禁用权限
export function batchUpdateStatus(data) {
  return request({
    url: '/dwms/permission/batch/status',
    method: 'put',
    data: data
  })
}

// 复制权限
export function copyPermission(id) {
  return request({
    url: '/dwms/permission/copy/' + id,
    method: 'post'
  })
}

// 移动权限
export function movePermission(data) {
  return request({
    url: '/dwms/permission/move',
    method: 'put',
    data: data
  })
}

// 获取权限统计信息
export function getPermissionStats() {
  return request({
    url: '/dwms/permission/stats',
    method: 'get'
  })
}

// 导出权限配置
export function exportPermissions(query) {
  return request({
    url: '/dwms/permission/export',
    method: 'post',
    data: query
  })
}

// 导入权限配置
export function importPermissions(fileName) {
  return request({
    url: '/dwms/permission/import',
    method: 'post',
    params: { fileName }
  })
}
