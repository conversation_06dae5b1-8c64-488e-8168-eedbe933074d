<template>
  <div class="app-container">
    <el-card class="filter-container" shadow="hover">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
        <el-form-item label="容器编号" prop="containerCode">
          <el-input
            v-model="queryParams.containerCode"
            placeholder="请输入容器编号"
            clearable
            prefix-icon="el-icon-document"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="容器号" prop="containerNo">
          <el-input
            v-model="queryParams.containerNo"
            placeholder="请输入容器号"
            clearable
            prefix-icon="el-icon-goods"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="容器类型" prop="containerType">
          <el-select v-model="queryParams.containerType" placeholder="请选择容器类型" clearable>
            <el-option label="桶" value="B" />
            <el-option label="箱" value="C" />
            <el-option label="罐" value="T" />
            <el-option label="其他" value="O" />
          </el-select>
        </el-form-item>
        <el-form-item label="容器等级" prop="containerGrade">
          <el-select v-model="queryParams.containerGrade" placeholder="请选择容器等级" clearable>
            <el-option label="普通" value="普通" />
            <el-option label="防腐" value="防腐" />
            <el-option label="防爆" value="防爆" />
            <el-option label="密封" value="密封" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option label="空闲" value="0" />
            <el-option label="在用" value="1" />
            <el-option label="维护" value="2" />
            <el-option label="报废" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="table-container" shadow="hover" style="margin-top: 10px;">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['warehouse:container:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['warehouse:container:edit']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['warehouse:container:remove']"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['warehouse:container:export']"
          >导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="containerList" @selection-change="handleSelectionChange" border stripe highlight-current-row>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="容器ID" align="center" prop="id" width="80" />
        <el-table-column label="容器编号" align="center" prop="containerCode" min-width="120" />
        <el-table-column label="容器号" align="center" prop="containerNo" min-width="120" />
        <el-table-column label="容器类型" align="center" prop="containerType" width="100">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.containerType == 'B'" type="success">桶</el-tag>
            <el-tag v-else-if="scope.row.containerType == 'C'" type="primary">箱</el-tag>
            <el-tag v-else-if="scope.row.containerType == 'T'" type="warning">罐</el-tag>
            <el-tag v-else>{{ scope.row.containerType }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="容器等级" align="center" prop="containerGrade" width="100">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.containerGrade == '普通'" type="info">普通</el-tag>
            <el-tag v-else-if="scope.row.containerGrade == '防腐'" type="primary">防腐</el-tag>
            <el-tag v-else-if="scope.row.containerGrade == '防爆'" type="warning">防爆</el-tag>
            <el-tag v-else-if="scope.row.containerGrade == '密封'" type="success">密封</el-tag>
            <el-tag v-else>{{ scope.row.containerGrade }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="规格" align="center" prop="specification" min-width="100" show-overflow-tooltip />
        <el-table-column label="材质" align="center" prop="material" min-width="100" show-overflow-tooltip />
        <el-table-column label="最大承重" align="center" prop="maxWeight" width="100">
          <template slot-scope="scope">
            {{ scope.row.maxWeight }} kg
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status" width="80">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status == '0'" type="success">空闲</el-tag>
            <el-tag v-else-if="scope.row.status == '1'" type="primary">在用</el-tag>
            <el-tag v-else-if="scope.row.status == '2'" type="warning">维护</el-tag>
            <el-tag v-else-if="scope.row.status == '3'" type="danger">报废</el-tag>
            <el-tag v-else>{{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="关联状态" align="center" prop="relationStatus" width="100">
          <template slot-scope="scope">
            <el-tag v-if="!scope.row.locationId" type="info">未关联</el-tag>
            <el-tag v-else-if="scope.row.locationStatus == '3'" type="danger">关联失效</el-tag>
            <el-tag v-else type="success">关联中</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="下次检验" align="center" prop="nextCheckTime" width="100">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.nextCheckTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
              v-hasPermi="['warehouse:container:query']"
            >详情</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['warehouse:container:edit']"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['warehouse:container:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改容器信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="650px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="当前存放货位" prop="locationId" required>
              <el-select v-model="form.locationId" placeholder="请选择货位" clearable style="width: 100%" @change="handleLocationChange">
                <el-option
                  v-for="item in locationOptions"
                  :key="item.id"
                  :label="item.locationCode"
                  :value="item.id"
                  :disabled="item.status === '2' || item.status === '3'"
                >
                  <span>{{ item.locationCode }}</span>
                  <span v-if="item.status === '2'" style="float: right; color: #E6A23C; font-size: 13px">(已锁定)</span>
                  <span v-if="item.status === '3'" style="float: right; color: #F56C6C; font-size: 13px">(已禁用)</span>
                </el-option>
              </el-select>
              <div class="location-status-tip" v-if="selectedLocation">
                <span v-if="selectedLocation.status === '0'" style="color: #67C23A">
                  <i class="el-icon-success"></i> 货位空闲
                </span>
                <span v-else-if="selectedLocation.status === '1'" style="color: #409EFF">
                  <i class="el-icon-info"></i> 货位已占用
                </span>
              </div>
              <div class="location-status-warning" v-if="locationWarning">
                <i class="el-icon-warning"></i> {{ locationWarning }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="容器号" prop="containerNo">
              <el-input v-model="form.containerNo" placeholder="自动生成,可修改" @input="generateContainerCode" />
              <el-button v-if="!form.id" size="mini" type="text" icon="el-icon-magic-stick" @click="generateContainerNo">
                自动生成
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="容器编号" prop="containerCode">
              <el-input v-model="form.containerCode" placeholder="自动生成" :disabled="true" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="容器类型" prop="containerType">
              <el-select v-model="form.containerType" placeholder="请选择容器类型" style="width: 100%">
                <el-option label="桶" value="B" />
                <el-option label="箱" value="C" />
                <el-option label="罐" value="T" />
                <el-option label="其他" value="O" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="容器等级" prop="containerGrade">
              <el-select v-model="form.containerGrade" placeholder="请选择容器等级" style="width: 100%">
                <el-option label="普通" value="普通" />
                <el-option label="防腐" value="防腐" />
                <el-option label="防爆" value="防爆" />
                <el-option label="密封" value="密封" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="规格" prop="specification">
              <el-input v-model="form.specification" placeholder="请输入规格" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="材质" prop="material">
              <el-input v-model="form.material" placeholder="请输入材质" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="适用物料类型" prop="suitableMaterialType">
              <el-input v-model="form.suitableMaterialType" placeholder="请输入适用物料类型" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最大承重(kg)" prop="maxWeight">
              <el-input-number v-model="form.maxWeight" :min="0" :precision="1" :step="100" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
                <el-option label="空闲" value="0" />
                <el-option label="在用" value="1" />
                <el-option label="维护" value="2" />
                <el-option label="报废" value="3" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="最后清洗时间" prop="lastCleanTime">
              <el-date-picker clearable
                v-model="form.lastCleanTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择最后清洗时间"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="下次检验时间" prop="nextCheckTime">
              <el-date-picker clearable
                v-model="form.nextCheckTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择下次检验时间"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="二维码URL" prop="qrCode">
              <el-input v-model="form.qrCode" placeholder="请输入二维码URL" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="RFID标签编码" prop="rfidCode">
              <el-input v-model="form.rfidCode" placeholder="请输入RFID标签编码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    
    <!-- 容器详情对话框 -->
    <el-dialog title="容器详情" :visible.sync="viewDialog" width="700px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="容器编号">{{ viewForm.containerCode }}</el-descriptions-item>
        <el-descriptions-item label="容器号">{{ viewForm.containerNo }}</el-descriptions-item>
        <el-descriptions-item label="容器类型">
          <el-tag v-if="viewForm.containerType == 'B'" type="success">桶</el-tag>
          <el-tag v-else-if="viewForm.containerType == 'C'" type="primary">箱</el-tag>
          <el-tag v-else-if="viewForm.containerType == 'T'" type="warning">罐</el-tag>
          <el-tag v-else>{{ viewForm.containerType }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="容器等级">
          <el-tag v-if="viewForm.containerGrade == '普通'" type="info">普通</el-tag>
          <el-tag v-else-if="viewForm.containerGrade == '防腐'" type="primary">防腐</el-tag>
          <el-tag v-else-if="viewForm.containerGrade == '防爆'" type="warning">防爆</el-tag>
          <el-tag v-else-if="viewForm.containerGrade == '密封'" type="success">密封</el-tag>
          <el-tag v-else>{{ viewForm.containerGrade }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="规格">{{ viewForm.specification }}</el-descriptions-item>
        <el-descriptions-item label="材质">{{ viewForm.material }}</el-descriptions-item>
        <el-descriptions-item label="适用物料类型">{{ viewForm.suitableMaterialType }}</el-descriptions-item>
        <el-descriptions-item label="最大承重">{{ viewForm.maxWeight }} kg</el-descriptions-item>
        <el-descriptions-item label="当前存放货位">{{ viewForm.locationCode || '-' }}</el-descriptions-item>
        <el-descriptions-item label="货位状态" v-if="viewForm.locationId">
          <el-tag v-if="viewForm.locationStatus == '0'" type="success">空闲</el-tag>
          <el-tag v-else-if="viewForm.locationStatus == '1'" type="primary">占用</el-tag>
          <el-tag v-else-if="viewForm.locationStatus == '2'" type="warning">锁定</el-tag>
          <el-tag v-else-if="viewForm.locationStatus == '3'" type="danger">禁用</el-tag>
          <el-tag v-else>{{ viewForm.locationStatus }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="最后关联货位" v-if="viewForm.lastLocationCode && viewForm.locationStatus == '3'">{{ viewForm.lastLocationCode }}</el-descriptions-item>
        <el-descriptions-item label="当前存放物料">{{ viewForm.currentMaterialId ? viewForm.currentMaterialId : '-' }}</el-descriptions-item>
        <el-descriptions-item label="最后清洗时间">{{ viewForm.lastCleanTime ? parseTime(viewForm.lastCleanTime) : '-' }}</el-descriptions-item>
        <el-descriptions-item label="下次检验时间">{{ viewForm.nextCheckTime ? parseTime(viewForm.nextCheckTime) : '-' }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag v-if="viewForm.status == '0'" type="success">空闲</el-tag>
          <el-tag v-else-if="viewForm.status == '1'" type="primary">在用</el-tag>
          <el-tag v-else-if="viewForm.status == '2'" type="warning">维护</el-tag>
          <el-tag v-else-if="viewForm.status == '3'" type="danger">报废</el-tag>
          <el-tag v-else>{{ viewForm.status }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="关联状态">
          <el-tag v-if="!viewForm.locationId" type="info">未关联</el-tag>
          <el-tag v-else-if="viewForm.locationStatus == '3'" type="danger">关联失效</el-tag>
          <el-tag v-else type="success">关联中</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="容器号二维码" :span="1">
          <div class="qrcode-container">
            <div id="qrcode-no" ref="qrcodeNo"></div>
            <div class="code-text">容器号: {{ viewForm.containerNo }}</div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="容器编号二维码" :span="1">
          <div class="qrcode-container">
            <div id="qrcode-code" ref="qrcodeCode"></div>
            <div class="code-text">容器编号: {{ viewForm.containerCode }}</div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="容器号条形码" :span="2">
          <div class="barcode-container">
            <svg id="barcode-svg"></svg>
            <div class="code-text">容器号: {{ viewForm.containerNo }}</div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ viewForm.createTime ? parseTime(viewForm.createTime) : '-' }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ viewForm.remark }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewDialog = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listContainer, getContainer, delContainer, addContainer, updateContainer, checkContainerNoUnique } from "@/api/warehouse/container"
import { listLocation } from "@/api/warehouse/location"
import JsBarcode from 'jsbarcode'
import QRCode from 'qrcode'

export default {
  name: "Container",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 容器信息表格数据
      containerList: [],
      // 库位选项
      locationOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示详情弹出层
      viewDialog: false,
      // 详情表单
      viewForm: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        containerCode: null,
        containerNo: null,
        containerType: null,
        containerGrade: null,
        specification: null,
        material: null,
        maxWeight: null,
        locationId: null,
        currentMaterialId: null,
        lastCleanTime: null,
        nextCheckTime: null,
        qrCode: null,
        rfidCode: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        containerNo: [
          { required: true, message: "容器号不能为空", trigger: "blur" },
          { validator: this.validateContainerNo, trigger: "blur" }
        ],
        locationId: [
          { required: true, message: "当前存放货位不能为空", trigger: "change" }
        ],
        containerType: [
          { required: true, message: "容器类型不能为空", trigger: "change" }
        ],
        containerGrade: [
          { required: true, message: "容器等级不能为空", trigger: "change" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ]
      },
      // 当前选中的货位
      selectedLocation: null,
      // 货位状态提示
      locationWarning: null
    };
  },
  created() {
    this.getList();
    this.getLocationOptions();
  },
  methods: {
    /** 查询容器信息列表 */
    getList() {
      this.loading = true;
      listContainer(this.queryParams).then(response => {
        this.containerList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询库位选项 */
    getLocationOptions() {
      listLocation().then(response => {
        this.locationOptions = response.rows;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        containerCode: null,
        containerNo: null,
        containerType: "B",
        containerGrade: "普通",
        specification: null,
        material: null,
        suitableMaterialType: null,
        maxWeight: 100,
        locationId: null,
        currentMaterialId: null,
        lastCleanTime: null,
        nextCheckTime: null,
        qrCode: null,
        rfidCode: null,
        status: "0",
        remark: null
      };
      this.selectedLocation = null;
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加容器信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getContainer(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改容器信息";
      });
    },
    /** 查看详情按钮操作 */
    handleView(row) {
      this.viewForm = {};
      getContainer(row.id).then(response => {
        this.viewForm = response.data;
        this.viewDialog = true;
        
        // 生成二维码和条形码
        this.$nextTick(() => {
          this.generateQRCodeForNo();
          this.generateQRCodeForCode();
          this.generateBarCode();
        });
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 确保容器编号已生成
          if (!this.form.containerCode && this.form.containerNo && this.selectedLocation) {
            this.form.containerCode = this.selectedLocation.locationCode + '-' + this.form.containerNo;
          }
          
          if (this.form.id != null) {
            updateContainer(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addContainer(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除容器信息编号为"' + ids + '"的数据项？').then(() => {
        return delContainer(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('warehouse/container/export', {
        ...this.queryParams
      }, `container_${new Date().getTime()}.xlsx`)
    },
    // 处理货位选择变更
    handleLocationChange(locationId) {
      this.locationWarning = null;
      
      if (!locationId) {
        this.selectedLocation = null;
        return;
      }
      
      // 查找选中的货位信息
      this.selectedLocation = this.locationOptions.find(item => item.id === locationId);
      
      if (this.selectedLocation) {
        // 检查货位状态
        if (this.selectedLocation.status === '2') {
          this.locationWarning = "当前货位已锁定，不可被使用";
          this.form.locationId = null;
          this.selectedLocation = null;
          return;
        } else if (this.selectedLocation.status === '3') {
          this.locationWarning = "当前货位已禁用，不可被使用";
          this.form.locationId = null;
          this.selectedLocation = null;
          return;
        }
        
        // 如果已有容器号,则重新生成编号
        if (this.form.containerNo) {
          this.generateContainerCode();
        }
      }
    },
    
    // 生成容器号
    generateContainerNo() {
      // 生成6位随机数，前缀为CN
      const randomNum = Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
      this.form.containerNo = 'CN' + randomNum;
      
      // 生成容器编号
      this.generateContainerCode();
    },
    
    // 生成容器编号
    generateContainerCode() {
      if (this.selectedLocation && this.form.containerNo) {
        // 容器编号 = 货位编号 + 容器号
        this.form.containerCode = this.selectedLocation.locationCode + '-' + this.form.containerNo;
      }
    },
    
    // 验证容器号唯一性
    validateContainerNo(rule, value, callback) {
      if (value && this.form.id === null) {
        // 调用后端API验证容器号唯一性
        checkContainerNoUnique(value).then(response => {
          if (!response.data) {
            callback(new Error('容器号已存在'));
          } else {
            callback();
          }
        });
      } else {
        callback();
      }
    },
    
    // 为容器号生成二维码
    generateQRCodeForNo() {
      if (this.viewForm.containerNo) {
        const qrcodeElement = document.getElementById('qrcode-no');
        if (qrcodeElement) {
          // 清除旧的二维码
          qrcodeElement.innerHTML = '';
          
          // 使用qrcode库生成高清二维码
          QRCode.toCanvas(qrcodeElement, this.viewForm.containerNo, {
            width: 180,
            margin: 2,
            errorCorrectionLevel: 'H', // 高容错率
            color: {
              dark: '#000000',
              light: '#ffffff'
            },
            scale: 4, // 增加缩放比例，提高清晰度
            rendererOpts: {
              quality: 1.0
            }
          }, (error) => {
            if (error) {
              console.error('容器号二维码生成错误:', error);
            } else {
              // 添加边框和标题
              const canvas = qrcodeElement.querySelector('canvas');
              if (canvas) {
                canvas.style.border = '1px solid #dcdfe6';
                canvas.style.borderRadius = '4px';
                canvas.style.padding = '5px';
              }
              
              // 添加容器号标签
              const label = document.createElement('div');
              label.className = 'qrcode-label';
              label.textContent = this.viewForm.containerNo;
              qrcodeElement.appendChild(label);
            }
          });
        }
      }
    },
    
    // 为容器编号生成二维码
    generateQRCodeForCode() {
      if (this.viewForm.containerCode) {
        const qrcodeElement = document.getElementById('qrcode-code');
        if (qrcodeElement) {
          // 清除旧的二维码
          qrcodeElement.innerHTML = '';
          
          // 使用qrcode库生成高清二维码
          QRCode.toCanvas(qrcodeElement, this.viewForm.containerCode, {
            width: 180,
            margin: 2,
            errorCorrectionLevel: 'H', // 高容错率
            color: {
              dark: '#000000',
              light: '#ffffff'
            },
            scale: 4, // 增加缩放比例，提高清晰度
            rendererOpts: {
              quality: 1.0
            }
          }, (error) => {
            if (error) {
              console.error('容器编号二维码生成错误:', error);
            } else {
              // 添加边框和标题
              const canvas = qrcodeElement.querySelector('canvas');
              if (canvas) {
                canvas.style.border = '1px solid #dcdfe6';
                canvas.style.borderRadius = '4px';
                canvas.style.padding = '5px';
              }
              
              // 添加容器编号标签
              const label = document.createElement('div');
              label.className = 'qrcode-label';
              label.textContent = this.viewForm.containerCode;
              qrcodeElement.appendChild(label);
            }
          });
        }
      }
    },
    
    // 生成容器号条形码
    generateBarCode() {
      if (this.viewForm.containerNo) {
        this.$nextTick(() => {
          try {
            // 生成条形码
            JsBarcode('#barcode-svg', this.viewForm.containerNo, {
              format: 'CODE128',
              lineColor: '#000000',
              width: 2,
              height: 50,
              displayValue: true,
              fontSize: 12
            });
          } catch (error) {
            console.error('条形码生成错误:', error);
          }
        });
      }
    },
  }
};
</script>

<style scoped>
.filter-container {
  margin-bottom: 10px;
}
.table-container {
  margin-bottom: 20px;
}
.el-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
.qrcode-container, .barcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 10px 0;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}
.qrcode-container canvas {
  display: block;
  margin: 0 auto;
}
.code-text {
  margin-top: 10px;
  font-size: 14px;
  color: #606266;
  font-weight: bold;
}
.qrcode-label {
  margin-top: 8px;
  font-size: 12px;
  color: #409EFF;
  background-color: #ecf5ff;
  padding: 2px 8px;
  border-radius: 4px;
  border: 1px solid #d9ecff;
  display: inline-block;
}
.location-status-tip {
  margin-top: 10px;
  font-size: 14px;
  color: #606266;
}
.location-status-warning {
  margin-top: 10px;
  font-size: 14px;
  color: #F56C6C;
}
</style>
