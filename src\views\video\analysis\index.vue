<template>
  <div class="app-container">
    <!-- AI智能分析开发中页面 -->
    <div class="development-notice">
      <el-card class="notice-card" shadow="hover">
        <div class="notice-content">
          <div class="notice-icon">
            <i class="el-icon-cpu"></i>
          </div>
          <div class="notice-text">
            <h2>AI 智能分析</h2>
            <h3>功能开发中...</h3>
            <p class="description">
              我们正在开发强大的AI智能分析功能，包括：
            </p>
            <div class="feature-list">
              <div class="feature-item">
                <i class="el-icon-view"></i>
                <span>人脸识别与身份验证</span>
              </div>
              <div class="feature-item">
                <i class="el-icon-user"></i>
                <span>行为分析与异常检测</span>
              </div>
              <div class="feature-item">
                <i class="el-icon-search"></i>
                <span>物体检测与分类识别</span>
              </div>
              <div class="feature-item">
                <i class="el-icon-location"></i>
                <span>运动轨迹追踪分析</span>
              </div>
              <div class="feature-item">
                <i class="el-icon-warning"></i>
                <span>智能告警与事件推送</span>
              </div>
              <div class="feature-item">
                <i class="el-icon-data-analysis"></i>
                <span>数据统计与趋势分析</span>
              </div>
            </div>
            <div class="progress-info">
              <p><strong>开发进度：</strong></p>
              <el-progress :percentage="developmentProgress" :stroke-width="8" color="#409EFF"></el-progress>
              <p class="progress-text">预计完成时间：2025年Q3</p>
            </div>
            <div class="contact-info">
              <p>如有疑问或建议，请联系开发团队</p>
              <el-button type="primary" icon="el-icon-message" @click="showContact">联系开发团队</el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 功能预览卡片 -->
    <el-card style="margin-top: 20px;">
      <div slot="header">
        <span>功能预览</span>
      </div>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="preview-card">
            <div class="preview-icon">
              <i class="el-icon-view" style="color: #67C23A;"></i>
            </div>
            <h4>人脸识别</h4>
            <p>高精度人脸检测和身份验证</p>
            <el-button size="mini" type="success" disabled>开发中</el-button>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="preview-card">
            <div class="preview-icon">
              <i class="el-icon-user" style="color: #E6A23C;"></i>
            </div>
            <h4>行为分析</h4>
            <p>智能分析异常行为和事件</p>
            <el-button size="mini" type="warning" disabled>开发中</el-button>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="preview-card">
            <div class="preview-icon">
              <i class="el-icon-search" style="color: #F56C6C;"></i>
            </div>
            <h4>物体检测</h4>
            <p>实时检测和分类各种物体</p>
            <el-button size="mini" type="danger" disabled>开发中</el-button>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
export default {
  name: "VideoAnalysis",
  data() {
    return {
      developmentProgress: 35
    };
  },
  methods: {
    showContact() {
      this.$alert(
        '开发团队联系方式：\n邮箱：<EMAIL>\n电话：400-123-4567\n微信群：扫描二维码加入开发交流群',
        '联系开发团队',
        {
          confirmButtonText: '确定',
          type: 'info'
        }
      );
    }
  }
};
</script>

<style scoped>
.development-notice {
  padding: 40px 20px;
  text-align: center;
}

.notice-card {
  max-width: 800px;
  margin: 0 auto;
}

.notice-content {
  display: flex;
  align-items: flex-start;
  text-align: left;
}

.notice-icon {
  margin-right: 30px;
  flex-shrink: 0;
}

.notice-icon i {
  font-size: 80px;
  color: #409EFF;
}

.notice-text {
  flex: 1;
}

.notice-text h2 {
  color: #303133;
  margin: 0 0 10px 0;
  font-size: 28px;
}

.notice-text h3 {
  color: #606266;
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: normal;
}

.description {
  color: #606266;
  margin-bottom: 20px;
  font-size: 16px;
  line-height: 1.6;
}

.feature-list {
  margin-bottom: 30px;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  color: #606266;
}

.feature-item i {
  margin-right: 10px;
  color: #409EFF;
  font-size: 16px;
  width: 20px;
}

.progress-info {
  margin-bottom: 30px;
}

.progress-info p {
  margin: 10px 0;
  color: #606266;
}

.progress-text {
  font-size: 14px;
  color: #909399;
}

.contact-info {
  text-align: center;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.contact-info p {
  margin-bottom: 15px;
  color: #606266;
}

.preview-card {
  text-align: center;
  padding: 30px 20px;
  border: 1px solid #EBEEF5;
  border-radius: 8px;
  transition: all 0.3s;
}

.preview-card:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.preview-icon {
  margin-bottom: 15px;
}

.preview-icon i {
  font-size: 40px;
}

.preview-card h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.preview-card p {
  margin: 0 0 15px 0;
  color: #606266;
  font-size: 14px;
}
</style>
