<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="page-title">
        <h2><i class="el-icon-connection"></i> 主题订阅管理</h2>
        <p>实时管理EMQX客户端的主题订阅，支持动态增删改查</p>
      </div>
      <div class="page-actions">
        <el-button type="primary" icon="el-icon-refresh" @click="refreshData">刷新数据</el-button>
        <el-button type="success" icon="el-icon-plus" @click="showAddDialog">添加订阅</el-button>
        <el-button type="info" icon="el-icon-operation" @click="showBatchDialog">批量操作</el-button>
        <el-button type="warning" icon="el-icon-download" @click="exportSubscriptions">导出配置</el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon clients">
              <i class="el-icon-user"></i>
            </div>
            <div class="stats-info">
              <div class="stats-value">{{ stats.totalClients || 0 }}</div>
              <div class="stats-label">活跃客户端</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon subscriptions">
              <i class="el-icon-connection"></i>
            </div>
            <div class="stats-info">
              <div class="stats-value">{{ stats.totalSubscriptions || 0 }}</div>
              <div class="stats-label">总订阅数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon average">
              <i class="el-icon-data-analysis"></i>
            </div>
            <div class="stats-info">
              <div class="stats-value">{{ formatNumber(stats.avgSubscriptionsPerClient) }}</div>
              <div class="stats-label">平均订阅数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon active">
              <i class="el-icon-star-on"></i>
            </div>
            <div class="stats-info">
              <div class="stats-value">{{ stats.mostActiveClient || 'N/A' }}</div>
              <div class="stats-label">最活跃客户端</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索和过滤 -->
    <el-card class="search-card">
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索客户端ID或主题"
            clearable
            style="width: 200px"
            @keyup.enter.native="handleSearch"
          />
        </el-form-item>
        
        <el-form-item label="客户端ID">
          <el-input
            v-model="searchForm.clientId"
            placeholder="客户端ID"
            clearable
            style="width: 200px"
            @keyup.enter.native="handleSearch"
          />
        </el-form-item>
        
        <el-form-item label="主题">
          <el-input
            v-model="searchForm.topic"
            placeholder="主题模式"
            clearable
            style="width: 200px"
            @keyup.enter.native="handleSearch"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 订阅列表 -->
    <el-card class="table-card">
      <div slot="header">
        <span>订阅列表</span>
        <div style="float: right;">
          <el-button type="text" icon="el-icon-refresh" @click="loadSubscriptions">刷新</el-button>
        </div>
      </div>
      
      <el-table
        v-loading="loading"
        :data="subscriptionList"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        
        <el-table-column label="客户端ID" prop="clientId" min-width="200">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.clientId" placement="top">
              <span class="client-id">{{ scope.row.clientId }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        
        <el-table-column label="主题" prop="topic" min-width="250">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.topic" placement="top">
              <span class="topic-name">{{ scope.row.topic }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        
        <el-table-column label="QoS等级" prop="qos" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getQosTagType(scope.row.qos)" size="small">
              QoS {{ scope.row.qos }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="订阅时间" prop="timestamp" width="180" align="center">
          <template slot-scope="scope">
            {{ formatTime(scope.row.timestamp) }}
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag type="success" size="small">活跃</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" type="primary" @click="editSubscription(scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" @click="removeSubscription(scope.row)">删除</el-button>
            <el-button size="mini" type="info" @click="verifySubscription(scope.row)">验证</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="loadSubscriptions"
      />
    </el-card>

    <!-- 动态主题管理组件 -->
    <DynamicTopicManager
      ref="dynamicTopicManager"
      @refresh="refreshData"
    />
  </div>
</template>

<script>
import {
  getActiveSubscriptions,
  searchSubscriptions,
  getSubscriptionStats,
  verifySubscription as verifySubscriptionApi,
  exportSubscriptions as exportSubscriptionsApi
} from '@/api/emqx/subscription'
import DynamicTopicManager from '../topicManagement/DynamicTopicManager.vue'

export default {
  name: 'SubscriptionManagement',
  components: {
    DynamicTopicManager
  },
  data() {
    return {
      loading: false,
      
      // 统计数据
      stats: {},
      
      // 搜索表单
      searchForm: {
        keyword: '',
        clientId: '',
        topic: ''
      },
      
      // 订阅列表
      subscriptionList: [],
      selectedSubscriptions: [],
      
      // 分页参数
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10
      }
    }
  },
  
  created() {
    this.init()
  },
  
  methods: {
    // 初始化
    async init() {
      await this.loadStats()
      await this.loadSubscriptions()
    },
    
    // 加载统计数据
    async loadStats() {
      try {
        const response = await getSubscriptionStats()
        if (response.code === 200) {
          this.stats = response.data
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },
    
    // 加载订阅列表
    async loadSubscriptions() {
      this.loading = true
      try {
        const response = await getActiveSubscriptions(this.queryParams)
        if (response.code === 200) {
          this.subscriptionList = response.rows || []
          this.total = response.total || 0
        }
      } catch (error) {
        this.$message.error('加载订阅列表失败')
        console.error(error)
      } finally {
        this.loading = false
      }
    },
    
    // 搜索订阅
    async handleSearch() {
      try {
        const response = await searchSubscriptions(this.searchForm)
        if (response.code === 200) {
          this.subscriptionList = response.data || []
          this.total = this.subscriptionList.length
        }
      } catch (error) {
        this.$message.error('搜索失败')
        console.error(error)
      }
    },
    
    // 重置搜索
    resetSearch() {
      this.searchForm = {
        keyword: '',
        clientId: '',
        topic: ''
      }
      this.loadSubscriptions()
    },
    
    // 刷新数据
    refreshData() {
      this.init()
    },
    
    // 显示添加对话框
    showAddDialog() {
      this.$refs.dynamicTopicManager.showAddDialog()
    },
    
    // 显示批量操作对话框
    showBatchDialog() {
      this.$refs.dynamicTopicManager.showBatchDialog()
    },
    
    // 编辑订阅
    editSubscription(subscription) {
      this.$refs.dynamicTopicManager.showEditDialog(subscription)
    },
    
    // 删除订阅
    async removeSubscription(subscription) {
      await this.$refs.dynamicTopicManager.removeTopic(subscription)
    },
    
    // 验证订阅
    async verifySubscription(subscription) {
      try {
        const response = await verifySubscriptionApi(subscription.clientId, subscription.topic)
        if (response.code === 200) {
          const result = response.data
          if (result.exists) {
            this.$message.success('订阅验证成功，状态正常')
          } else {
            this.$message.warning('订阅不存在或已失效')
          }
        }
      } catch (error) {
        this.$message.error('验证失败')
        console.error(error)
      }
    },
    
    // 导出订阅配置
    async exportSubscriptions() {
      try {
        const response = await exportSubscriptionsApi()
        if (response.code === 200) {
          // 创建下载链接
          const blob = new Blob([JSON.stringify(response.data, null, 2)], {
            type: 'application/json'
          })
          const url = window.URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = url
          link.download = `subscriptions_${new Date().getTime()}.json`
          link.click()
          window.URL.revokeObjectURL(url)
          
          this.$message.success('导出成功')
        }
      } catch (error) {
        this.$message.error('导出失败')
        console.error(error)
      }
    },
    
    // 处理选择变化
    handleSelectionChange(selection) {
      this.selectedSubscriptions = selection
    },
    
    // 工具方法
    getQosTagType(qos) {
      const typeMap = { 0: 'info', 1: 'success', 2: 'warning' }
      return typeMap[qos] || 'info'
    },
    
    formatTime(timestamp) {
      if (!timestamp) return '-'
      return new Date(timestamp).toLocaleString()
    },
    
    formatNumber(num) {
      if (num === undefined || num === null) return '0'
      return Number(num).toFixed(1)
    }
  }
}
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title h2 {
  margin: 0;
  color: #303133;
}

.page-title p {
  margin: 5px 0 0 0;
  color: #909399;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stats-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  margin-right: 15px;
}

.stats-icon.clients { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.stats-icon.subscriptions { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.stats-icon.average { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.stats-icon.active { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

.stats-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.search-card, .table-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.search-form {
  margin-bottom: 0;
}

.client-id, .topic-name {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}
</style>
