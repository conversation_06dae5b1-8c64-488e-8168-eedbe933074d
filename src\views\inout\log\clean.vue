<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span class="card-title">
          <i class="el-icon-brush"></i>
          出入库日志清理
        </span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回</el-button>
      </div>

      <!-- 清理配置 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card shadow="hover" class="config-card">
            <div slot="header">
              <i class="el-icon-setting"></i>
              清理配置
            </div>
            
            <el-form :model="cleanConfig" :rules="configRules" ref="configForm" label-width="120px">
              <el-form-item label="保留天数" prop="retentionDays">
                <el-input-number
                  v-model="cleanConfig.retentionDays"
                  :min="1"
                  :max="365"
                  placeholder="请输入保留天数"
                  style="width: 100%"
                />
                <div class="form-tip">清理此天数之前的日志数据</div>
              </el-form-item>

              <el-form-item label="操作类型" prop="operationType">
                <el-select v-model="cleanConfig.operationType" placeholder="请选择操作类型" clearable style="width: 100%">
                  <el-option label="全部" value="" />
                  <el-option label="入库" value="1" />
                  <el-option label="出库" value="2" />
                </el-select>
                <div class="form-tip">选择要清理的操作类型，不选择则清理全部</div>
              </el-form-item>

              <el-form-item label="操作结果" prop="operationResult">
                <el-select v-model="cleanConfig.operationResult" placeholder="请选择操作结果" clearable style="width: 100%">
                  <el-option label="全部" value="" />
                  <el-option label="成功" value="1" />
                  <el-option label="失败" value="2" />
                </el-select>
                <div class="form-tip">选择要清理的操作结果，不选择则清理全部</div>
              </el-form-item>

              <el-form-item label="批量大小" prop="batchSize">
                <el-input-number
                  v-model="cleanConfig.batchSize"
                  :min="100"
                  :max="10000"
                  :step="100"
                  placeholder="请输入批量大小"
                  style="width: 100%"
                />
                <div class="form-tip">每次清理的记录数量，建议1000-5000</div>
              </el-form-item>

              <el-form-item label="自动清理" prop="autoClean">
                <el-switch
                  v-model="cleanConfig.autoClean"
                  active-text="开启"
                  inactive-text="关闭"
                />
                <div class="form-tip">开启后系统将定时自动清理日志</div>
              </el-form-item>

              <el-form-item label="清理时间" prop="cleanTime" v-if="cleanConfig.autoClean">
                <el-time-picker
                  v-model="cleanConfig.cleanTime"
                  format="HH:mm"
                  value-format="HH:mm"
                  placeholder="选择清理时间"
                  style="width: 100%"
                />
                <div class="form-tip">每日自动清理的时间</div>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="saveConfig" :loading="configLoading">
                  <i class="el-icon-check"></i>
                  保存配置
                </el-button>
                <el-button @click="resetConfig">
                  <i class="el-icon-refresh"></i>
                  重置
                </el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card shadow="hover" class="preview-card">
            <div slot="header">
              <i class="el-icon-view"></i>
              清理预览
            </div>

            <div class="preview-content">
              <el-button type="info" @click="previewClean" :loading="previewLoading" style="width: 100%; margin-bottom: 20px;">
                <i class="el-icon-search"></i>
                预览清理数据
              </el-button>

              <div v-if="previewData" class="preview-result">
                <el-descriptions :column="1" border>
                  <el-descriptions-item label="清理日期范围">
                    {{ previewData.dateRange }}
                  </el-descriptions-item>
                  <el-descriptions-item label="预计清理数量">
                    <el-tag type="warning">{{ previewData.totalCount }}</el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="入库日志">
                    {{ previewData.inboundCount }}
                  </el-descriptions-item>
                  <el-descriptions-item label="出库日志">
                    {{ previewData.outboundCount }}
                  </el-descriptions-item>
                  <el-descriptions-item label="成功操作">
                    {{ previewData.successCount }}
                  </el-descriptions-item>
                  <el-descriptions-item label="失败操作">
                    {{ previewData.errorCount }}
                  </el-descriptions-item>
                  <el-descriptions-item label="预计释放空间">
                    <el-tag type="success">{{ previewData.estimatedSpace }}</el-tag>
                  </el-descriptions-item>
                </el-descriptions>

                <div class="clean-actions" style="margin-top: 20px;">
                  <el-button 
                    type="danger" 
                    @click="executeClean" 
                    :loading="cleanLoading"
                    :disabled="previewData.totalCount === 0"
                    style="width: 100%;"
                  >
                    <i class="el-icon-delete"></i>
                    执行清理 ({{ previewData.totalCount }} 条记录)
                  </el-button>
                </div>
              </div>

              <div v-else class="no-preview">
                <el-empty description="点击预览按钮查看清理数据" />
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 清理历史 -->
      <el-card shadow="hover" class="history-card" style="margin-top: 20px;">
        <div slot="header">
          <i class="el-icon-time"></i>
          清理历史
        </div>

        <el-table :data="cleanHistory" v-loading="historyLoading" stripe>
          <el-table-column label="清理时间" prop="cleanTime" width="180">
            <template slot-scope="scope">
              {{ parseTime(scope.row.cleanTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
            </template>
          </el-table-column>
          <el-table-column label="清理类型" prop="cleanType" width="120">
            <template slot-scope="scope">
              <el-tag :type="scope.row.cleanType === 'auto' ? 'success' : 'primary'" size="mini">
                {{ scope.row.cleanType === 'auto' ? '自动' : '手动' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="清理条件" prop="cleanCondition" show-overflow-tooltip />
          <el-table-column label="清理数量" prop="cleanCount" width="100">
            <template slot-scope="scope">
              <el-tag type="warning" size="mini">{{ scope.row.cleanCount }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="耗时" prop="duration" width="100">
            <template slot-scope="scope">
              {{ scope.row.duration }}s
            </template>
          </el-table-column>
          <el-table-column label="状态" prop="status" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'" size="mini">
                {{ scope.row.status === 'success' ? '成功' : '失败' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作人" prop="operator" width="120" />
        </el-table>

        <pagination
          v-show="historyTotal > 0"
          :total="historyTotal"
          :page.sync="historyQuery.pageNum"
          :limit.sync="historyQuery.pageSize"
          @pagination="getCleanHistory"
        />
      </el-card>
    </el-card>
  </div>
</template>

<script>
import { getCleanConfig, updateCleanConfig, previewClean, executeClean, getCleanHistory } from "@/api/inout/log";

export default {
  name: "InoutLogClean",
  data() {
    return {
      // 清理配置
      cleanConfig: {
        retentionDays: 90,
        operationType: '',
        operationResult: '',
        batchSize: 1000,
        autoClean: false,
        cleanTime: '02:00'
      },
      // 配置验证规则
      configRules: {
        retentionDays: [
          { required: true, message: '保留天数不能为空', trigger: 'blur' },
          { type: 'number', min: 1, max: 365, message: '保留天数必须在1-365之间', trigger: 'blur' }
        ],
        batchSize: [
          { required: true, message: '批量大小不能为空', trigger: 'blur' },
          { type: 'number', min: 100, max: 10000, message: '批量大小必须在100-10000之间', trigger: 'blur' }
        ]
      },
      // 加载状态
      configLoading: false,
      previewLoading: false,
      cleanLoading: false,
      historyLoading: false,
      // 预览数据
      previewData: null,
      // 清理历史
      cleanHistory: [],
      historyTotal: 0,
      historyQuery: {
        pageNum: 1,
        pageSize: 10
      }
    };
  },
  created() {
    this.loadConfig();
    this.getCleanHistory();
  },
  methods: {
    /** 加载清理配置 */
    loadConfig() {
      getCleanConfig().then(response => {
        if (response.data) {
          this.cleanConfig = { ...this.cleanConfig, ...response.data };
        }
      });
    },
    /** 保存配置 */
    saveConfig() {
      this.$refs.configForm.validate(valid => {
        if (valid) {
          this.configLoading = true;
          updateCleanConfig(this.cleanConfig).then(response => {
            this.$modal.msgSuccess("配置保存成功");
          }).finally(() => {
            this.configLoading = false;
          });
        }
      });
    },
    /** 重置配置 */
    resetConfig() {
      this.$refs.configForm.resetFields();
      this.loadConfig();
    },
    /** 预览清理数据 */
    previewClean() {
      this.previewLoading = true;
      const params = {
        retentionDays: this.cleanConfig.retentionDays,
        operationType: this.cleanConfig.operationType,
        operationResult: this.cleanConfig.operationResult
      };
      
      previewClean(params).then(response => {
        this.previewData = response.data;
      }).finally(() => {
        this.previewLoading = false;
      });
    },
    /** 执行清理 */
    executeClean() {
      this.$modal.confirm(`确认清理 ${this.previewData.totalCount} 条日志记录？此操作不可恢复！`).then(() => {
        this.cleanLoading = true;
        const params = {
          retentionDays: this.cleanConfig.retentionDays,
          operationType: this.cleanConfig.operationType,
          operationResult: this.cleanConfig.operationResult,
          batchSize: this.cleanConfig.batchSize
        };
        
        executeClean(params).then(response => {
          this.$modal.msgSuccess("日志清理完成");
          this.previewData = null;
          this.getCleanHistory();
        }).finally(() => {
          this.cleanLoading = false;
        });
      });
    },
    /** 获取清理历史 */
    getCleanHistory() {
      this.historyLoading = true;
      getCleanHistory(this.historyQuery).then(response => {
        this.cleanHistory = response.rows || [];
        this.historyTotal = response.total || 0;
      }).finally(() => {
        this.historyLoading = false;
      });
    },
    /** 返回 */
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style scoped>
.card-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.config-card,
.preview-card,
.history-card {
  border-radius: 8px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.preview-content {
  min-height: 400px;
}

.preview-result {
  margin-top: 20px;
}

.no-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
}

.clean-actions {
  text-align: center;
}

.el-descriptions {
  margin-bottom: 20px;
}

.el-card {
  margin-bottom: 20px;
}

.el-card:last-child {
  margin-bottom: 0;
}
</style>
