import request from '@/utils/request'

// 查询出入库明细列表
export function listDetail(query) {
  return request({
    url: '/inout/detail/list',
    method: 'get',
    params: query
  })
}

// 查询出入库明细详细
export function getDetail(id) {
  return request({
    url: '/inout/detail/' + id,
    method: 'get'
  })
}

// 新增出入库明细
export function addDetail(data) {
  return request({
    url: '/inout/detail',
    method: 'post',
    data: data
  })
}

// 修改出入库明细
export function updateDetail(data) {
  return request({
    url: '/inout/detail',
    method: 'put',
    data: data
  })
}

// 删除出入库明细
export function delDetail(id) {
  return request({
    url: '/inout/detail/' + id,
    method: 'delete'
  })
}

// 根据单据编号查询审批通过的申请数据
export function getApprovedInoutByBillNo(billNo) {
  return request({
    url: '/inout/detail/approvedInout/' + billNo,
    method: 'get'
  })
}

// 开始执行出入库操作
export function startExecution(id) {
  return request({
    url: '/inout/detail/startExecution/' + id,
    method: 'post'
  })
}

// 完成执行出入库操作
export function completeExecution(id, data) {
  return request({
    url: '/inout/detail/completeExecution/' + id,
    method: 'post',
    data: data
  })
}

// 获取执行状态
export function getExecutionStatus(id) {
  return request({
    url: '/inout/detail/executionStatus/' + id,
    method: 'get'
  })
}
