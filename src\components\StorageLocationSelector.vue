<template>
  <div class="storage-location-selector">
    <!-- 存放类型选择 -->
    <el-form-item label="存放类型" prop="storageType">
      <el-radio-group v-model="form.storageType" @change="handleStorageTypeChange">
        <el-radio label="location">货位</el-radio>
        <el-radio label="container">容器</el-radio>
      </el-radio-group>
    </el-form-item>

    <!-- 货位选择 -->
    <el-form-item
      v-if="form.storageType === 'location'"
      label="货位编号"
      prop="locationCode"
    >
      <el-select
        v-model="form.locationCode"
        placeholder="请输入或选择货位编号"
        filterable
        clearable
        allow-create
        default-first-option
        remote
        :remote-method="searchLocation"
        :loading="locationLoading"
        @change="handleLocationChange"
        style="width: 100%"
      >
        <el-option
          v-for="item in filteredLocationOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left">{{ item.label }}</span>
          <span style="float: right; color: #8492a6; font-size: 13px">
            {{ getLocationDesc(item) }}
          </span>
        </el-option>
      </el-select>
    </el-form-item>

    <!-- 容器选择 -->
    <el-form-item
      v-if="form.storageType === 'container'"
      label="容器编号"
      prop="containerCode"
    >
      <el-select
        v-model="form.containerCode"
        placeholder="请输入或选择容器编号"
        filterable
        clearable
        allow-create
        default-first-option
        remote
        :remote-method="searchContainer"
        :loading="containerLoading"
        @change="handleContainerChange"
        style="width: 100%"
      >
        <el-option
          v-for="item in filteredContainerOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left">{{ item.label }}</span>
          <span style="float: right; color: #8492a6; font-size: 13px">
            {{ getContainerDesc(item) }}
          </span>
        </el-option>
      </el-select>
    </el-form-item>

    <!-- 存放位置描述 -->
    <el-form-item v-if="storageLocationDesc" label="存放位置">
      <el-tag type="info">{{ storageLocationDesc }}</el-tag>
    </el-form-item>
  </div>
</template>

<script>
import { listLocationSelector } from "@/api/warehouse/location"
import { listContainerSelector } from "@/api/warehouse/container"

export default {
  name: "StorageLocationSelector",
  props: {
    // 表单数据
    value: {
      type: Object,
      default: () => ({
        storageType: null,
        locationCode: null,
        containerCode: null
      })
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        storageType: null,
        locationCode: null,
        containerCode: null
      },
      locationOptions: [],
      containerOptions: [],
      filteredLocationOptions: [],
      filteredContainerOptions: [],
      locationLoading: false,
      containerLoading: false
    }
  },
  computed: {
    storageLocationDesc() {
      if (this.form.storageType === 'location' && this.form.locationCode) {
        return `货位: ${this.form.locationCode}`
      } else if (this.form.storageType === 'container' && this.form.containerCode) {
        const container = this.containerOptions.find(item => item.value === this.form.containerCode)
        return `容器: ${this.form.containerCode}${container && container.containerNo ? ' (' + container.containerNo + ')' : ''}`
      }
      return null
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal) {
          this.form = { ...newVal }
        }
      },
      immediate: true,
      deep: true
    },
    form: {
      handler(newVal) {
        this.$emit('input', newVal)
        this.$emit('change', newVal)
      },
      deep: true
    }
  },
  created() {
    console.log('StorageLocationSelector: 组件创建完成，使用懒加载模式')
    // 不在初始化时加载数据，改为懒加载模式
  },
  methods: {
    // 初始化数据 (已废弃，改为懒加载模式)
    // async initializeData() {
    //   console.log('StorageLocationSelector: 开始初始化数据')
    //   try {
    //     // 分别加载，避免同时请求导致问题
    //     await this.loadLocationOptions()
    //     await this.loadContainerOptions()
    //     console.log('StorageLocationSelector: 数据初始化完成')
    //   } catch (error) {
    //     console.error('StorageLocationSelector: 数据初始化失败', error)
    //     // 即使初始化失败，也不应该阻塞UI
    //   }
    // },

    // 加载货位选项
    async loadLocationOptions() {
      // 防止重复加载
      if (this.locationLoading) {
        console.log('StorageLocationSelector: 货位正在加载中，跳过')
        return
      }

      try {
        console.log('StorageLocationSelector: 开始加载货位选项')
        this.locationLoading = true

        // 添加超时处理
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('请求超时')), 10000)
        })

        const response = await Promise.race([
          listLocationSelector(),
          timeoutPromise
        ])

        console.log('StorageLocationSelector: 货位API响应', response)

        if (response && response.code === 200) {
          this.locationOptions = response.data || []
          this.filteredLocationOptions = this.locationOptions
          console.log('StorageLocationSelector: 货位选项加载成功', this.locationOptions.length, '个')
        } else {
          console.error('StorageLocationSelector: 货位API返回错误', response)
          this.locationOptions = []
          this.filteredLocationOptions = []
          if (response && response.msg) {
            this.$message.error('加载货位列表失败: ' + response.msg)
          }
        }
      } catch (error) {
        console.error('StorageLocationSelector: 加载货位列表异常:', error)
        this.locationOptions = []
        this.filteredLocationOptions = []

        // 只在非超时错误时显示错误消息
        if (error.message !== '请求超时') {
          this.$message.error('加载货位列表失败: ' + error.message)
        } else {
          this.$message.error('加载货位列表超时，请检查网络连接')
        }
      } finally {
        this.locationLoading = false
      }
    },

    // 加载容器选项
    async loadContainerOptions() {
      // 防止重复加载
      if (this.containerLoading) {
        console.log('StorageLocationSelector: 容器正在加载中，跳过')
        return
      }

      try {
        console.log('StorageLocationSelector: 开始加载容器选项')
        this.containerLoading = true

        // 添加超时处理
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('请求超时')), 10000)
        })

        const response = await Promise.race([
          listContainerSelector(),
          timeoutPromise
        ])

        console.log('StorageLocationSelector: 容器API响应', response)

        if (response && response.code === 200) {
          this.containerOptions = response.data || []
          this.filteredContainerOptions = this.containerOptions
          console.log('StorageLocationSelector: 容器选项加载成功', this.containerOptions.length, '个')
        } else {
          console.error('StorageLocationSelector: 容器API返回错误', response)
          this.containerOptions = []
          this.filteredContainerOptions = []
          if (response && response.msg) {
            this.$message.error('加载容器列表失败: ' + response.msg)
          }
        }
      } catch (error) {
        console.error('StorageLocationSelector: 加载容器列表异常:', error)
        this.containerOptions = []
        this.filteredContainerOptions = []

        // 只在非超时错误时显示错误消息
        if (error.message !== '请求超时') {
          this.$message.error('加载容器列表失败: ' + error.message)
        } else {
          this.$message.error('加载容器列表超时，请检查网络连接')
        }
      } finally {
        this.containerLoading = false
      }
    },

    // 懒加载货位选项
    async loadLocationOptionsLazy() {
      // 如果已经加载过，直接返回
      if (this.locationOptions.length > 0) {
        this.filteredLocationOptions = this.locationOptions
        return
      }

      // 如果正在加载，不重复加载
      if (this.locationLoading) {
        return
      }

      try {
        console.log('StorageLocationSelector: 懒加载货位选项')
        this.locationLoading = true

        const response = await listLocationSelector()
        console.log('StorageLocationSelector: 货位API响应', response)

        if (response && response.code === 200) {
          this.locationOptions = response.data || []
          this.filteredLocationOptions = this.locationOptions
          console.log('StorageLocationSelector: 货位选项懒加载成功', this.locationOptions.length, '个')
        } else {
          console.error('StorageLocationSelector: 货位API返回错误', response)
          this.locationOptions = []
          this.filteredLocationOptions = []
          if (response && response.msg) {
            this.$message.warning('加载货位列表失败: ' + response.msg)
          }
        }
      } catch (error) {
        console.error('StorageLocationSelector: 懒加载货位列表异常:', error)
        this.locationOptions = []
        this.filteredLocationOptions = []
        this.$message.warning('加载货位列表失败，请稍后重试')
      } finally {
        this.locationLoading = false
      }
    },

    // 懒加载容器选项
    async loadContainerOptionsLazy() {
      // 如果已经加载过，直接返回
      if (this.containerOptions.length > 0) {
        this.filteredContainerOptions = this.containerOptions
        return
      }

      // 如果正在加载，不重复加载
      if (this.containerLoading) {
        return
      }

      try {
        console.log('StorageLocationSelector: 懒加载容器选项')
        this.containerLoading = true

        const response = await listContainerSelector()
        console.log('StorageLocationSelector: 容器API响应', response)

        if (response && response.code === 200) {
          this.containerOptions = response.data || []
          this.filteredContainerOptions = this.containerOptions
          console.log('StorageLocationSelector: 容器选项懒加载成功', this.containerOptions.length, '个')
        } else {
          console.error('StorageLocationSelector: 容器API返回错误', response)
          this.containerOptions = []
          this.filteredContainerOptions = []
          if (response && response.msg) {
            this.$message.warning('加载容器列表失败: ' + response.msg)
          }
        }
      } catch (error) {
        console.error('StorageLocationSelector: 懒加载容器列表异常:', error)
        this.containerOptions = []
        this.filteredContainerOptions = []
        this.$message.warning('加载容器列表失败，请稍后重试')
      } finally {
        this.containerLoading = false
      }
    },

    // 搜索货位
    searchLocation(query) {
      if (query !== '') {
        this.filteredLocationOptions = this.locationOptions.filter(item => {
          return item.label.toLowerCase().includes(query.toLowerCase()) ||
                 item.value.toLowerCase().includes(query.toLowerCase())
        })
      } else {
        this.filteredLocationOptions = this.locationOptions
      }
    },

    // 搜索容器
    searchContainer(query) {
      if (query !== '') {
        this.filteredContainerOptions = this.containerOptions.filter(item => {
          return item.label.toLowerCase().includes(query.toLowerCase()) ||
                 item.value.toLowerCase().includes(query.toLowerCase()) ||
                 (item.containerNo && item.containerNo.toLowerCase().includes(query.toLowerCase()))
        })
      } else {
        this.filteredContainerOptions = this.containerOptions
      }
    },

    // 存放类型改变
    handleStorageTypeChange(value) {
      console.log('StorageLocationSelector: 存放类型改变为', value)

      // 清空之前的选择
      this.form.locationCode = null
      this.form.containerCode = null

      // 懒加载对应的数据
      if (value === 'location') {
        console.log('StorageLocationSelector: 懒加载货位数据')
        this.loadLocationOptionsLazy()
      } else if (value === 'container') {
        console.log('StorageLocationSelector: 懒加载容器数据')
        this.loadContainerOptionsLazy()
      }

      // 触发change事件
      this.emitChange()
    },

    // 货位选择改变
    handleLocationChange(value) {
      this.form.containerCode = null
    },

    // 容器选择改变
    handleContainerChange(value) {
      this.form.locationCode = null
    },

    // 获取货位描述
    getLocationDesc(item) {
      const parts = []
      if (item.rowNum && item.columnNum) {
        parts.push(`${item.rowNum}行${item.columnNum}列`)
      }
      if (item.layerCode) {
        parts.push(`${item.layerCode}层`)
      }
      return parts.join(' ')
    },

    // 获取容器描述
    getContainerDesc(item) {
      const parts = []
      if (item.containerType) {
        parts.push(item.containerType)
      }
      if (item.specification) {
        parts.push(item.specification)
      }
      return parts.join(' ')
    },

    // 重置选择
    reset() {
      this.form = {
        storageType: null,
        locationCode: null,
        containerCode: null
      }
    },

    // 验证
    validate() {
      if (!this.form.storageType) {
        this.$message.warning('请选择存放类型')
        return false
      }
      if (this.form.storageType === 'location' && !this.form.locationCode) {
        this.$message.warning('请选择货位')
        return false
      }
      if (this.form.storageType === 'container' && !this.form.containerCode) {
        this.$message.warning('请选择容器')
        return false
      }
      return true
    }
  }
}
</script>

<style scoped>
.storage-location-selector {
  width: 100%;
}

.el-select {
  width: 100%;
}

.el-form-item {
  margin-bottom: 18px;
}
</style>
