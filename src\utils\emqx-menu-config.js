// EMQX客户端管理系统菜单配置

export const emqxMenuConfig = {
  // 主菜单配置
  mainMenu: {
    path: '/emqx',
    name: 'Emqx',
    title: 'EMQX管理',
    icon: 'monitor',
    roles: ['admin', 'emqx_admin'],
    redirect: '/emqx/dashboard'
  },
  
  // 子菜单配置
  subMenus: [
    {
      path: 'dashboard',
      name: 'EmqxDashboard',
      title: '系统概览',
      icon: 'dashboard',
      component: '@/views/emqx/dashboard/index',
      meta: {
        affix: true,
        noCache: false,
        description: '查看EMQX系统整体状态和关键指标'
      }
    },
    {
      path: 'serverRegistry',
      name: 'ServerRegistry',
      title: '服务器客户端',
      icon: 'connection',
      component: '@/views/emqx/serverRegistry/index',
      meta: {
        perms: ['emqx:server:list'],
        description: '管理5个MQTT服务器客户端的注册和状态'
      }
    },
    {
      path: 'deviceAuth',
      name: 'DeviceAuth',
      title: '设备认证',
      icon: 'key',
      component: '@/views/emqx/deviceAuth/index',
      meta: {
        perms: ['emqx:device:list'],
        description: '管理设备客户端的认证信息和权限'
      }
    },
    {
      path: 'dataStream',
      name: 'DataStream',
      title: '数据分流',
      icon: 'data-line',
      component: '@/views/emqx/dataStream/index',
      meta: {
        perms: ['emqx:stream:list'],
        description: '监控和管理MQTT消息数据分流'
      }
    },
    {
      path: 'cleanup',
      name: 'Cleanup',
      title: '清理管理',
      icon: 'delete',
      component: '@/views/emqx/cleanup/index',
      meta: {
        perms: ['emqx:cleanup:list'],
        description: '检测和清理重复的客户端连接'
      }
    },
    {
      path: 'topicSync',
      name: 'TopicSync',
      title: '主题同步',
      icon: 'refresh',
      component: '@/views/emqx/topicSync/index',
      meta: {
        perms: ['emqx:topic:sync'],
        description: '同步主题配置到EMQX，解决格式不一致问题'
      }
    },
    {
      path: 'monitoring',
      name: 'EmqxMonitoring',
      title: '实时监控',
      icon: 'monitor',
      component: '@/views/emqx/monitoring/index',
      meta: {
        perms: ['emqx:monitor:view'],
        description: '实时监控EMQX服务器状态和客户端连接'
      }
    },
    {
      path: 'settings',
      name: 'EmqxSettings',
      title: '系统设置',
      icon: 'setting',
      component: '@/views/emqx/settings/index',
      meta: {
        perms: ['emqx:settings:manage'],
        description: '配置EMQX系统参数和选项'
      }
    }
  ]
}

// 权限配置
export const emqxPermissions = {
  // 服务器客户端权限
  server: [
    'emqx:server:list',      // 查看服务器列表
    'emqx:server:create',    // 注册服务器
    'emqx:server:update',    // 更新服务器信息
    'emqx:server:delete',    // 删除服务器
    'emqx:server:test'       // 测试服务器连接
  ],
  
  // 设备认证权限
  device: [
    'emqx:device:list',      // 查看设备列表
    'emqx:device:create',    // 创建设备认证
    'emqx:device:update',    // 更新设备认证
    'emqx:device:delete',    // 删除设备认证
    'emqx:device:batch',     // 批量操作
    'emqx:device:test'       // 测试设备连接
  ],
  
  // 数据分流权限
  stream: [
    'emqx:stream:list',      // 查看数据流
    'emqx:stream:process',   // 处理消息
    'emqx:stream:batch',     // 批量处理
    'emqx:stream:simulate',  // 模拟数据
    'emqx:stream:clean'      // 清理统计
  ],
  
  // 清理管理权限
  cleanup: [
    'emqx:cleanup:list',     // 查看清理历史
    'emqx:cleanup:check',    // 检查重复
    'emqx:cleanup:preview',  // 预览清理
    'emqx:cleanup:execute',  // 执行清理
    'emqx:cleanup:safety'    // 安全检查
  ],
  
  // 主题同步权限
  topic: [
    'emqx:topic:sync',       // 主题同步
    'emqx:topic:diagnose',   // 格式诊断
    'emqx:topic:compare',    // 主题比较
    'emqx:topic:fix'         // 修复主题
  ],
  
  // 监控权限
  monitor: [
    'emqx:monitor:view',     // 查看监控
    'emqx:monitor:client',   // 客户端管理
    'emqx:monitor:alert',    // 告警管理
    'emqx:monitor:history'   // 历史数据
  ],
  
  // 设置权限
  settings: [
    'emqx:settings:manage',  // 管理设置
    'emqx:settings:export',  // 导出配置
    'emqx:settings:import'   // 导入配置
  ]
}

// 菜单图标配置
export const menuIcons = {
  dashboard: 'el-icon-dashboard',
  connection: 'el-icon-connection',
  key: 'el-icon-key',
  'data-line': 'el-icon-data-line',
  delete: 'el-icon-delete',
  refresh: 'el-icon-refresh',
  monitor: 'el-icon-monitor',
  setting: 'el-icon-setting'
}

// 快速操作配置
export const quickActions = {
  dashboard: [
    {
      name: '注册服务器',
      icon: 'el-icon-plus',
      path: '/emqx/serverRegistry',
      permission: 'emqx:server:create'
    },
    {
      name: '创建认证',
      icon: 'el-icon-key',
      path: '/emqx/deviceAuth',
      permission: 'emqx:device:create'
    },
    {
      name: '查看数据流',
      icon: 'el-icon-data-line',
      path: '/emqx/dataStream',
      permission: 'emqx:stream:list'
    },
    {
      name: '检查重复',
      icon: 'el-icon-search',
      path: '/emqx/cleanup',
      permission: 'emqx:cleanup:check'
    }
  ]
}

// 状态配置
export const statusConfig = {
  server: {
    ONLINE: { text: '在线', type: 'success', color: '#67c23a' },
    OFFLINE: { text: '离线', type: 'danger', color: '#f56c6c' },
    CONNECTING: { text: '连接中', type: 'warning', color: '#e6a23c' },
    ERROR: { text: '错误', type: 'danger', color: '#f56c6c' }
  },
  
  device: {
    ACTIVE: { text: '活跃', type: 'success', color: '#67c23a' },
    INACTIVE: { text: '未激活', type: 'info', color: '#909399' },
    DISABLED: { text: '已禁用', type: 'danger', color: '#f56c6c' },
    EXPIRED: { text: '已过期', type: 'warning', color: '#e6a23c' }
  },
  
  cleanup: {
    COMPLETED: { text: '已完成', type: 'success', color: '#67c23a' },
    RUNNING: { text: '运行中', type: 'warning', color: '#e6a23c' },
    FAILED: { text: '失败', type: 'danger', color: '#f56c6c' },
    CANCELLED: { text: '已取消', type: 'info', color: '#909399' }
  }
}

// 设备类型配置
export const deviceTypeConfig = {
  types: [
    {
      value: '出库防错客户端',
      label: '出库防错客户端',
      prefix: 'outmis_client_',
      server: 'outmis_server_mqtt',
      description: '用于出库防错系统的MQTT客户端'
    },
    {
      value: '门禁设备客户端',
      label: '门禁设备客户端',
      prefix: 'access_client_',
      server: 'access_server_mqtt',
      description: '用于门禁管理系统的MQTT客户端'
    },
    {
      value: '视频监控客户端',
      label: '视频监控客户端',
      prefix: 'video_client_',
      server: 'video_server_mqtt',
      description: '用于视频监控系统的MQTT客户端'
    },
    {
      value: '智能导寻客户端',
      label: '智能导寻客户端',
      prefix: 'guide_client_',
      server: 'guide_server_mqtt',
      description: '用于智能导寻系统的MQTT客户端'
    },
    {
      value: '库房组态客户端',
      label: '库房组态客户端',
      prefix: 'dwms_client_',
      server: 'dwms_server_mqtt',
      description: '用于库房组态系统的MQTT客户端'
    },
    {
      value: '称重设备客户端',
      label: '称重设备客户端',
      prefix: 'weight_client_',
      server: 'outmis_server_mqtt',
      description: '用于称重设备的MQTT客户端'
    }
  ],
  
  // 设备类型到服务器映射
  serverMapping: {
    '出库防错客户端': 'outmis_server_mqtt',
    '门禁设备客户端': 'access_server_mqtt',
    '视频监控客户端': 'video_server_mqtt',
    '智能导寻客户端': 'guide_server_mqtt',
    '库房组态客户端': 'dwms_server_mqtt',
    '称重设备客户端': 'outmis_server_mqtt'
  }
}

// 导出默认配置
export default {
  emqxMenuConfig,
  emqxPermissions,
  menuIcons,
  quickActions,
  statusConfig,
  deviceTypeConfig
}
