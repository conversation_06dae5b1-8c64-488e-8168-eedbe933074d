import request from '@/utils/request'

// 查询货位信息列表
export function listLocation(query) {
  return request({
    url: '/warehouse/location/list',
    method: 'get',
    params: query
  })
}

// 查询货位信息详细
export function getLocation(id) {
  return request({
    url: '/warehouse/location/' + id,
    method: 'get'
  })
}

// 新增货位信息
export function addLocation(data) {
  return request({
    url: '/warehouse/location',
    method: 'post',
    data: data
  })
}

// 修改货位信息
export function updateLocation(data) {
  return request({
    url: '/warehouse/location',
    method: 'put',
    data: data
  })
}

// 删除货位信息
export function delLocation(id) {
  return request({
    url: '/warehouse/location/' + id,
    method: 'delete'
  })
}

// 获取货位选择器列表
export function listLocationSelector() {
  return request({
    url: '/warehouse/location/selector',
    method: 'get'
  })
}

// 根据货位编号获取仓库信息
export function getWarehouseByLocationCode(locationCode) {
  return request({
    url: '/warehouse/location/warehouse/' + locationCode,
    method: 'get'
  })
}

// 批量生成货位
export function batchGenerateLocation(data) {
  return request({
    url: '/warehouse/location/batchGenerate',
    method: 'post',
    data: data
  })
}

// 获取库位状态统计
export function getLocationStatusStats(query) {
  return request({
    url: '/warehouse/location/statusStats',
    method: 'get',
    params: query
  })
}
