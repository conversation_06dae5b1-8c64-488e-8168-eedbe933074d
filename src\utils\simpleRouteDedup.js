/**
 * 简单而安全的路由去重工具
 * 专门解决门禁管理菜单显示问题
 */

// 强力路由去重函数 - 彻底解决重复问题
export function simpleRouteDeduplication(routes) {
  if (!Array.isArray(routes)) {
    console.warn('[强力去重] 输入不是数组，返回空数组')
    return []
  }

  console.log(`[强力去重] 开始处理，输入路由数量: ${routes.length}`)

  const seenNames = new Set()
  const seenPaths = new Set()
  const seenFullPaths = new Set()
  const result = []
  
  function processRoute(route, level = 0, parentPath = '') {
    if (!route || typeof route !== 'object') {
      return null
    }

    const indent = '  '.repeat(level)

    // 跳过没有名称和路径的路由
    if (!route.name && !route.path) {
      console.log(`${indent}[强力去重] 跳过无效路由`)
      return null
    }

    // 创建完整路径用于更精确的去重
    const fullPath = parentPath ? `${parentPath}/${route.path}` : route.path

    // 检查路由名称重复 - 更严格的检查
    if (route.name && seenNames.has(route.name)) {
      console.log(`${indent}[强力去重] 跳过重复路由名称: ${route.name}`)
      return null
    }

    // 检查相对路径重复（只对顶级路由检查，子路由使用完整路径）
    if (level === 0 && route.path &&
        route.path !== '/' &&
        route.path !== '*' &&
        route.path !== '#' &&
        !route.path.startsWith('http') &&
        seenPaths.has(route.path)) {
      console.log(`${indent}[强力去重] 跳过重复顶级路径: ${route.path}`)
      return null
    }

    // 检查完整路径重复
    if (fullPath &&
        fullPath !== '/' &&
        fullPath !== '*' &&
        fullPath !== '#' &&
        !fullPath.startsWith('http') &&
        seenFullPaths.has(fullPath)) {
      console.log(`${indent}[强力去重] 跳过重复完整路径: ${fullPath}`)
      return null
    }
    
    // 创建新的路由对象
    const newRoute = { ...route }
    
    // 记录已处理的路由
    if (route.name) {
      seenNames.add(route.name)
    }
    // 只记录顶级路径，子路径使用完整路径
    if (level === 0 && route.path && route.path !== '/' && route.path !== '*' && route.path !== '#') {
      seenPaths.add(route.path)
    }
    if (fullPath && fullPath !== '/' && fullPath !== '*' && fullPath !== '#') {
      seenFullPaths.add(fullPath)
    }

    // 处理子路由
    if (route.children && Array.isArray(route.children) && route.children.length > 0) {
      const cleanChildren = []
      route.children.forEach(child => {
        const cleanChild = processRoute(child, level + 1, fullPath)
        if (cleanChild) {
          cleanChildren.push(cleanChild)
        }
      })

      if (cleanChildren.length > 0) {
        newRoute.children = cleanChildren
      } else {
        delete newRoute.children
      }
    }

    console.log(`${indent}[强力去重] 保留路由: ${route.name || route.path} (${fullPath})`)
    return newRoute
  }
  
  // 处理所有路由
  routes.forEach(route => {
    const cleanRoute = processRoute(route)
    if (cleanRoute) {
      result.push(cleanRoute)
    }
  })
  
  console.log(`[强力去重] 处理完成，输出路由数量: ${result.length}`)
  console.log(`[强力去重] 唯一路由名称数量: ${seenNames.size}`)
  console.log(`[强力去重] 唯一路径数量: ${seenPaths.size}`)
  console.log(`[强力去重] 唯一完整路径数量: ${seenFullPaths.size}`)
  
  return result
}

// 专门针对门禁管理的路由修复
export function fixAccessControlRoutes(routes) {
  console.log('[门禁修复] 开始修复门禁管理路由')
  
  function findAccessControlMenu(routeList) {
    for (let route of routeList) {
      if (route.name === 'Access' || route.path === 'access') {
        return route
      }
      if (route.children && route.children.length > 0) {
        const found = findAccessControlMenu(route.children)
        if (found) return found
      }
    }
    return null
  }
  
  const accessMenu = findAccessControlMenu(routes)
  if (accessMenu) {
    console.log(`[门禁修复] 找到门禁管理菜单，当前子菜单数量: ${accessMenu.children ? accessMenu.children.length : 0}`)
    
    // 确保所有门禁子菜单都存在
    const expectedSubMenus = [
      { name: 'AccessDevice', path: 'device', component: 'access/device/index' },
      { name: 'FaceRecord', path: 'faceRecord', component: 'access/faceRecord/index' },
      { name: 'AccessRecord', path: 'record', component: 'access/record/index' },
      { name: 'PersonApproval', path: 'approval', component: 'access/approval/index' },
      { name: 'AccessPermission', path: 'permission', component: 'access/permission/index' },
      { name: 'UserFaceInfo', path: 'userFace', component: 'access/userFace/index' },
      { name: 'DeviceParams', path: 'params', component: 'access/params/index' },
      { name: 'EmqxConfig', path: 'emqx', component: 'access/emqx/index' }
    ]
    
    if (!accessMenu.children) {
      accessMenu.children = []
    }
    
    const existingPaths = new Set(accessMenu.children.map(child => child.path))
    
    expectedSubMenus.forEach(subMenu => {
      if (!existingPaths.has(subMenu.path)) {
        console.log(`[门禁修复] 添加缺失的子菜单: ${subMenu.name}`)
        accessMenu.children.push({
          ...subMenu,
          meta: { title: subMenu.name, icon: 'system' }
        })
      }
    })
    
    console.log(`[门禁修复] 修复后子菜单数量: ${accessMenu.children.length}`)
  } else {
    console.log('[门禁修复] 未找到门禁管理菜单')
  }
  
  return routes
}

// 组合使用的安全去重函数
export function safeRouteDeduplication(routes) {
  console.log('[安全去重] 开始安全的路由去重处理')
  
  try {
    // 1. 先进行简单去重
    let cleanedRoutes = simpleRouteDeduplication(routes)
    
    // 2. 然后修复门禁管理路由
    cleanedRoutes = fixAccessControlRoutes(cleanedRoutes)
    
    console.log('[安全去重] 安全去重处理完成')
    return cleanedRoutes
    
  } catch (error) {
    console.error('[安全去重] 处理失败，返回原始路由:', error)
    return routes
  }
}
