# 菜单图标系统使用指南

## 概述

本项目为智能仓储管理系统设计了一套完整的企业化工科技风格图标系统，包含主菜单图标、子菜单图标和通用操作图标。

## 设计理念

- **企业化工风格**：采用现代化的工业设计语言
- **科技蓝主题**：以蓝色系为主色调，体现科技感
- **功能导向**：每个图标都与具体功能紧密关联
- **一致性**：统一的设计规范和视觉风格

## 快速开始

### 1. 组件引入

```vue
<template>
  <div>
    <!-- 基础用法 -->
    <menu-icon icon="system-setting" />
    
    <!-- 指定大小和主题 -->
    <menu-icon icon="device-management" size="large" theme="primary" />
    
    <!-- 启用动画效果 -->
    <menu-icon icon="warehouse-management" animated />
  </div>
</template>
```

### 2. 全局注册

在 `main.js` 中引入：

```javascript
import ComponentsPlugin from '@/components'
Vue.use(ComponentsPlugin)
```

## 组件属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| icon | String | '' | 图标名称或菜单名称 |
| menuPath | String | '' | 菜单路径（用于自动匹配图标） |
| size | String | 'medium' | 图标大小：small/medium/large/xlarge |
| theme | String | 'primary' | 主题色：primary/secondary/success/warning/danger/info/light/dark |
| color | String | '' | 自定义颜色 |
| animated | Boolean | false | 是否启用动画 |
| fallback | String | 'el-icon-menu' | 后备图标 |

## 图标分类

### 主菜单图标

适用于系统主要功能模块：

- `system-setting` - 系统管理
- `device-management` - 设备管理
- `material-management` - 物料管理
- `warehouse-management` - 仓库管理
- `unified-registry` - 统一注册管理
- `mqtt-protocol` - MQTT管理
- `emqx-server` - EMQX管理
- `sip-protocol` - SIP Server管理
- `netty-server` - NETTY管理
- `access-control` - 门禁管理
- `system-monitor` - 系统监控
- `system-tools` - 系统工具
- `approval-workflow` - 审批管理
- `data-visualization` - 大屏展示

### 子菜单图标

适用于具体功能页面：

#### 系统管理
- `user-management` - 用户管理
- `role-management` - 角色管理
- `menu-management` - 菜单管理
- `department-management` - 部门管理
- `position-management` - 岗位管理
- `dictionary-management` - 字典管理
- `system-config` - 参数设置
- `notice-management` - 通知公告

#### 设备管理
- `device-category` - 产品分类
- `product-management` - 产品管理
- `device-list` - 设备管理
- `device-group` - 设备分组
- `firmware-management` - 固件管理
- `protocol-management` - 协议管理
- `scene-management` - 场景联动

#### 物料管理
- `material-info` - 物料信息
- `material-list` - 物料清单
- `inventory-management` - 物料库存
- `material-query` - 物料查询
- `weight-management` - 重量管理

#### 仓库管理
- `warehouse-info` - 仓库信息
- `warehouse-zone` - 库区管理
- `warehouse-rack` - 货架管理
- `warehouse-location` - 货位管理

### 通用操作图标

适用于各种操作按钮：

- `add-item` - 新增
- `edit-item` - 修改
- `delete-item` - 删除
- `export-data` - 导出
- `import-data` - 导入
- `search-data` - 查询
- `refresh-data` - 刷新
- `view-details` - 查看
- `settings` - 设置

## 主题色彩

系统提供8种预设主题色：

- `primary` - #1e3a8a（主蓝色）
- `secondary` - #3b82f6（次蓝色）
- `success` - #10b981（成功绿色）
- `warning` - #f59e0b（警告橙色）
- `danger` - #ef4444（危险红色）
- `info` - #6b7280（信息灰色）
- `light` - #f8fafc（浅色）
- `dark` - #1f2937（深色）

## 使用示例

### 在菜单中使用

```vue
<template>
  <el-menu>
    <el-menu-item index="1">
      <menu-icon icon="system-setting" />
      <span>系统管理</span>
    </el-menu-item>
    <el-submenu index="2">
      <template slot="title">
        <menu-icon icon="device-management" />
        <span>设备管理</span>
      </template>
      <el-menu-item index="2-1">
        <menu-icon icon="device-category" size="small" />
        <span>产品分类</span>
      </el-menu-item>
    </el-submenu>
  </el-menu>
</template>
```

### 在按钮中使用

```vue
<template>
  <div>
    <el-button type="primary">
      <menu-icon icon="add-item" size="small" />
      新增
    </el-button>
    <el-button type="success">
      <menu-icon icon="edit-item" size="small" />
      修改
    </el-button>
    <el-button type="danger">
      <menu-icon icon="delete-item" size="small" />
      删除
    </el-button>
  </div>
</template>
```

### 在卡片标题中使用

```vue
<template>
  <el-card>
    <div slot="header">
      <menu-icon icon="statistics-dashboard" theme="primary" />
      <span>数据统计</span>
    </div>
    <div>卡片内容</div>
  </el-card>
</template>
```

## 自定义图标

如需添加新图标，请在 `@/utils/menu-icons.js` 中：

1. 在相应的图标配置对象中添加映射关系
2. 在 `svgIcons` 对象中添加SVG图标定义
3. 确保图标符合设计规范

```javascript
// 添加新的图标映射
export const subMenuIcons = {
  // ... 现有图标
  'new-feature': 'new-feature-icon'
}

// 添加SVG图标定义
export const svgIcons = {
  // ... 现有图标
  'new-feature-icon': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="..."/>
    </svg>
  `
}
```

## 最佳实践

1. **语义化命名**：图标名称应该清晰表达功能含义
2. **尺寸适配**：根据使用场景选择合适的图标尺寸
3. **主题一致**：在同一模块中保持主题色的一致性
4. **动画适度**：仅在需要引起注意时使用动画效果
5. **后备方案**：为每个图标提供Element UI图标作为后备

## 浏览器兼容性

- 现代浏览器：完全支持
- IE11+：支持基础功能
- 移动端：完全支持

## 更新日志

### v1.0.0
- 初始版本发布
- 包含60+个企业化工科技风格图标
- 支持8种主题色彩
- 提供完整的组件化解决方案
