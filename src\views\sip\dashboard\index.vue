<template>
  <div class="sip-dashboard">
    <!-- 状态卡片 -->
    <el-row :gutter="20" class="status-cards">
      <el-col :span="6">
        <el-card class="status-card">
          <div class="card-content">
            <div class="card-icon server-icon">
              <i class="el-icon-phone"></i>
            </div>
            <div class="card-info">
              <div class="card-title">SIP 服务器</div>
              <div class="card-value" :class="serverStatus.running ? 'status-running' : 'status-stopped'">
                {{ serverStatus.running ? '运行中' : '已停止' }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="status-card">
          <div class="card-content">
            <div class="card-icon devices-icon">
              <i class="el-icon-monitor"></i>
            </div>
            <div class="card-info">
              <div class="card-title">在线设备</div>
              <div class="card-value">{{ statistics.onlineDevices || 0 }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="status-card">
          <div class="card-content">
            <div class="card-icon sessions-icon">
              <i class="el-icon-link"></i>
            </div>
            <div class="card-info">
              <div class="card-title">活跃会话</div>
              <div class="card-value">{{ statistics.activeSessions || 0 }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="status-card">
          <div class="card-content">
            <div class="card-icon media-icon">
              <i class="el-icon-video-play"></i>
            </div>
            <div class="card-info">
              <div class="card-title">媒体流</div>
              <div class="card-value">{{ statistics.activeStreams || 0 }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-section">
      <el-col :span="12">
        <el-card title="设备注册趋势">
          <div slot="header">
            <span>设备注册趋势</span>
            <el-button-group style="float: right;">
              <el-button size="mini" @click="changeTimeRange('1h')" :type="timeRange === '1h' ? 'primary' : ''">1小时</el-button>
              <el-button size="mini" @click="changeTimeRange('24h')" :type="timeRange === '24h' ? 'primary' : ''">24小时</el-button>
              <el-button size="mini" @click="changeTimeRange('7d')" :type="timeRange === '7d' ? 'primary' : ''">7天</el-button>
            </el-button-group>
          </div>
          <div ref="deviceChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card title="会话统计">
          <div slot="header">
            <span>会话统计</span>
            <el-switch
              v-model="realTimeMode"
              style="float: right;"
              active-text="实时"
              inactive-text="历史">
            </el-switch>
          </div>
          <div ref="sessionChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细信息 -->
    <el-row :gutter="20" class="details-section">
      <el-col :span="8">
        <el-card title="服务器信息">
          <div class="info-list">
            <div class="info-item">
              <span class="info-label">服务器IP:</span>
              <span class="info-value">{{ serverStatus.ip || 'N/A' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">监听端口:</span>
              <span class="info-value">{{ serverStatus.port || 5060 }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">SIP域:</span>
              <span class="info-value">{{ serverStatus.domain || 'N/A' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">TCP支持:</span>
              <span class="info-value">{{ serverStatus.tcpEnabled ? '启用' : '禁用' }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card title="统计信息">
          <div class="info-list">
            <div class="info-item">
              <span class="info-label">总设备数:</span>
              <span class="info-value">{{ statistics.totalDevices || 0 }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">总注册数:</span>
              <span class="info-value">{{ statistics.totalRegistrations || 0 }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">总会话数:</span>
              <span class="info-value">{{ statistics.totalSessions || 0 }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">分配端口:</span>
              <span class="info-value">{{ statistics.allocatedPorts || 0 }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card title="快速操作">
          <div class="quick-actions">
            <el-button type="primary" icon="el-icon-refresh" @click="refreshData">刷新数据</el-button>
            <el-button type="success" icon="el-icon-monitor" @click="$router.push('/sip/devices')">设备管理</el-button>
            <el-button type="info" icon="el-icon-link" @click="$router.push('/sip/sessions')">会话管理</el-button>
            <el-button type="warning" icon="el-icon-setting" @click="$router.push('/sip/settings')">系统设置</el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 设备状态分布 -->
    <el-row :gutter="20" class="device-status-section">
      <el-col :span="12">
        <el-card title="设备状态分布">
          <div ref="deviceStatusChart" style="height: 250px;"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card title="最近注册设备">
          <el-table :data="recentDevices" style="width: 100%" size="mini">
            <el-table-column prop="deviceId" label="设备ID" width="150"></el-table-column>
            <el-table-column prop="ip" label="IP地址" width="120"></el-table-column>
            <el-table-column prop="registerTime" label="注册时间">
              <template slot-scope="scope">
                {{ formatTime(scope.row.registerTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="80">
              <template slot-scope="scope">
                <el-tag :type="scope.row.status === 'ONLINE' ? 'success' : 'danger'" size="mini">
                  {{ scope.row.status === 'ONLINE' ? '在线' : '离线' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getSipServerStatus, getSipServerStatistics } from '@/api/sip/server'
import { getSipDevices } from '@/api/sip/device'
import * as echarts from 'echarts'

export default {
  name: 'SipDashboard',
  data() {
    return {
      serverStatus: {},
      statistics: {},
      recentDevices: [],
      timeRange: '1h',
      realTimeMode: true,
      deviceChart: null,
      sessionChart: null,
      deviceStatusChart: null,
      refreshTimer: null
    }
  },
  mounted() {
    this.initData()
    this.initCharts()
    this.startAutoRefresh()
  },
  beforeDestroy() {
    this.stopAutoRefresh()
    if (this.deviceChart) {
      this.deviceChart.dispose()
    }
    if (this.sessionChart) {
      this.sessionChart.dispose()
    }
    if (this.deviceStatusChart) {
      this.deviceStatusChart.dispose()
    }
  },
  methods: {
    async initData() {
      try {
        const [statusRes, statsRes, devicesRes] = await Promise.all([
          getSipServerStatus(),
          getSipServerStatistics(),
          getSipDevices({ pageSize: 5, sortBy: 'registerTime', sortOrder: 'desc' })
        ])
        this.serverStatus = statusRes.data.server
        this.statistics = statusRes.data
        this.recentDevices = devicesRes.data.data || []
      } catch (error) {
        this.$message.error('获取数据失败: ' + error.message)
      }
    },
    
    initCharts() {
      this.$nextTick(() => {
        this.initDeviceChart()
        this.initSessionChart()
        this.initDeviceStatusChart()
      })
    },
    
    initDeviceChart() {
      this.deviceChart = echarts.init(this.$refs.deviceChart)
      // 设备注册趋势图表配置...
    },
    
    initSessionChart() {
      this.sessionChart = echarts.init(this.$refs.sessionChart)
      // 会话统计图表配置...
    },
    
    initDeviceStatusChart() {
      this.deviceStatusChart = echarts.init(this.$refs.deviceStatusChart)
      // 设备状态分布饼图配置...
    },
    
    changeTimeRange(range) {
      this.timeRange = range
      this.updateCharts()
    },
    
    updateCharts() {
      // 更新图表数据
    },
    
    startAutoRefresh() {
      this.refreshTimer = setInterval(() => {
        if (this.realTimeMode) {
          this.refreshData()
        }
      }, 5000)
    },
    
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },
    
    refreshData() {
      this.initData()
    },
    
    formatTime(timestamp) {
      if (!timestamp) return 'N/A'
      return new Date(timestamp).toLocaleString()
    }
  }
}
</script>

<style lang="scss" scoped>
.sip-dashboard {
  padding: 20px;
  
  .status-cards {
    margin-bottom: 20px;
    
    .status-card {
      .card-content {
        display: flex;
        align-items: center;
        
        .card-icon {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 15px;
          
          i {
            font-size: 24px;
            color: white;
          }
          
          &.server-icon {
            background: linear-gradient(45deg, #409EFF, #67C23A);
          }
          
          &.devices-icon {
            background: linear-gradient(45deg, #67C23A, #E6A23C);
          }
          
          &.sessions-icon {
            background: linear-gradient(45deg, #E6A23C, #F56C6C);
          }
          
          &.media-icon {
            background: linear-gradient(45deg, #F56C6C, #909399);
          }
        }
        
        .card-info {
          .card-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
          }
          
          .card-value {
            font-size: 24px;
            font-weight: bold;
            
            &.status-running {
              color: #67C23A;
            }
            
            &.status-stopped {
              color: #F56C6C;
            }
          }
        }
      }
    }
  }
  
  .charts-section {
    margin-bottom: 20px;
  }
  
  .details-section {
    margin-bottom: 20px;
    
    .info-list {
      .info-item {
        display: flex;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .info-label {
          color: #666;
        }
        
        .info-value {
          font-weight: bold;
        }
      }
    }
    
    .quick-actions {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
  }
  
  .device-status-section {
    // 设备状态区域样式
  }
}
</style>
