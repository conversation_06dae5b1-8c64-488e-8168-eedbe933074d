<template>
  <div class="api-test">
    <h2>SIP API 测试页面</h2>
    
    <el-card class="test-card">
      <div slot="header">
        <span>API 测试结果</span>
        <div style="float: right;">
          <el-button style="padding: 3px 0; margin-right: 10px;" type="text" @click="clearCache">清除缓存</el-button>
          <el-button style="padding: 3px 0" type="text" @click="testAllApis">测试所有API</el-button>
        </div>
      </div>
      
      <div class="test-results">
        <div v-for="(result, index) in testResults" :key="index" class="test-item">
          <el-tag :type="result.success ? 'success' : 'danger'">
            {{ result.success ? '✓' : '✗' }}
          </el-tag>
          <span class="api-url">{{ result.url }}</span>
          <span class="api-status">{{ result.status }}</span>
          <div v-if="result.data" class="api-data">
            <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import request from '@/utils/request'

export default {
  name: 'SipApiTest',
  data() {
    return {
      testResults: []
    }
  },
  mounted() {
    this.testAllApis()
  },
  methods: {
    clearCache() {
      // 清除浏览器缓存
      if ('caches' in window) {
        caches.keys().then(names => {
          names.forEach(name => {
            caches.delete(name)
          })
        })
      }

      // 清除localStorage
      localStorage.clear()

      // 清除sessionStorage
      sessionStorage.clear()

      this.$message.success('缓存已清除，请刷新页面')

      // 3秒后自动刷新页面
      setTimeout(() => {
        window.location.reload()
      }, 3000)
    },

    async testAllApis() {
      this.testResults = []

      const apis = [
        { url: '/sip/monitoring/server-status', name: 'SIP服务器状态' },
        { url: '/sip/monitoring/metrics', name: 'SIP监控指标' },
        { url: '/video/performance/server-status', name: 'ZLM服务器状态' },
        { url: '/video/performance/metrics', name: 'ZLM性能指标' },
        { url: '/sip/testing/status', name: 'SIP测试状态' },
        { url: '/video/test/status', name: 'ZLM测试状态' },
        { url: '/api/sip/sessions?page=1&size=20', name: 'SIP会话列表' },
        { url: '/api/sip/devices?page=1&size=20', name: 'SIP设备列表' }
      ]

      for (const api of apis) {
        try {
          const response = await request({
            url: api.url,
            method: 'get',
            headers: {
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache'
            }
          })

          this.testResults.push({
            url: api.url,
            name: api.name,
            success: true,
            status: `${response.code || 200} - ${api.name}`,
            data: response.data || response
          })
        } catch (error) {
          this.testResults.push({
            url: api.url,
            name: api.name,
            success: false,
            status: `错误: ${error.message}`,
            data: error.response ? error.response.data : null
          })
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.api-test {
  padding: 20px;
  
  .test-card {
    margin-top: 20px;
  }
  
  .test-results {
    .test-item {
      margin-bottom: 15px;
      padding: 10px;
      border: 1px solid #eee;
      border-radius: 4px;
      
      .api-url {
        margin-left: 10px;
        font-family: monospace;
        font-weight: bold;
      }
      
      .api-status {
        margin-left: 10px;
        color: #666;
      }
      
      .api-data {
        margin-top: 10px;
        background: #f5f5f5;
        padding: 10px;
        border-radius: 4px;
        
        pre {
          margin: 0;
          font-size: 12px;
          max-height: 200px;
          overflow-y: auto;
        }
      }
    }
  }
}
</style>
