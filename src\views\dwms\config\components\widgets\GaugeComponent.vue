<template>
  <div class="gauge-component" :style="{ width: width + 'px', height: height + 'px' }">
    <!-- 圆形仪表盘 -->
    <div v-if="config.type === 'circle'" class="circle-gauge">
      <div ref="circleGauge" :style="{ width: '100%', height: '100%' }"></div>
    </div>
    
    <!-- 线性仪表盘 -->
    <div v-else-if="config.type === 'linear'" class="linear-gauge">
      <div class="gauge-title" v-if="config.title">{{ config.title }}</div>
      <div class="gauge-bar">
        <div class="gauge-track"></div>
        <div 
          class="gauge-fill" 
          :style="{ 
            width: percentage + '%', 
            backgroundColor: config.color || '#409EFF' 
          }"
        ></div>
      </div>
      <div class="gauge-value">
        {{ displayValue }}
        <span class="gauge-unit" v-if="config.unit">{{ config.unit }}</span>
      </div>
    </div>
    
    <!-- 数字仪表盘 -->
    <div v-else-if="config.type === 'number'" class="number-gauge">
      <div class="gauge-title" v-if="config.title">{{ config.title }}</div>
      <div class="gauge-number" :style="{ color: config.color || '#409EFF' }">
        {{ displayValue }}
        <span class="gauge-unit" v-if="config.unit">{{ config.unit }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'GaugeComponent',
  props: {
    config: {
      type: Object,
      default: () => ({})
    },
    data: {
      type: Object,
      default: () => ({})
    },
    width: {
      type: Number,
      default: 200
    },
    height: {
      type: Number,
      default: 200
    }
  },
  data() {
    return {
      chart: null
    }
  },
  computed: {
    currentValue() {
      return this.data?.value || 0
    },
    minValue() {
      return this.config.min || this.data?.min || 0
    },
    maxValue() {
      return this.config.max || this.data?.max || 100
    },
    percentage() {
      const range = this.maxValue - this.minValue
      return range > 0 ? ((this.currentValue - this.minValue) / range) * 100 : 0
    },
    displayValue() {
      if (typeof this.currentValue === 'number') {
        return this.currentValue.toFixed(this.config.decimals || 1)
      }
      return this.currentValue
    }
  },
  mounted() {
    if (this.config.type === 'circle') {
      this.initCircleGauge()
    }
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
  },
  watch: {
    data: {
      handler() {
        if (this.config.type === 'circle' && this.chart) {
          this.updateCircleGauge()
        }
      },
      deep: true
    },
    width() {
      this.$nextTick(() => {
        if (this.chart) {
          this.chart.resize()
        }
      })
    },
    height() {
      this.$nextTick(() => {
        if (this.chart) {
          this.chart.resize()
        }
      })
    }
  },
  methods: {
    // 初始化圆形仪表盘
    initCircleGauge() {
      if (!this.$refs.circleGauge) return
      
      this.chart = echarts.init(this.$refs.circleGauge)
      this.updateCircleGauge()
    },
    
    // 更新圆形仪表盘
    updateCircleGauge() {
      if (!this.chart) return
      
      const option = {
        series: [
          {
            type: 'gauge',
            center: ['50%', '60%'],
            startAngle: 200,
            endAngle: -20,
            min: this.minValue,
            max: this.maxValue,
            splitNumber: 10,
            itemStyle: {
              color: this.config.color || '#409EFF'
            },
            progress: {
              show: true,
              width: 30
            },
            pointer: {
              show: false
            },
            axisLine: {
              lineStyle: {
                width: 30
              }
            },
            axisTick: {
              distance: -45,
              splitNumber: 5,
              lineStyle: {
                width: 2,
                color: '#999'
              }
            },
            splitLine: {
              distance: -52,
              length: 14,
              lineStyle: {
                width: 3,
                color: '#999'
              }
            },
            axisLabel: {
              distance: -20,
              color: '#999',
              fontSize: 12
            },
            anchor: {
              show: false
            },
            title: {
              show: !!this.config.title,
              offsetCenter: [0, '-10%'],
              fontSize: 14,
              color: '#333'
            },
            detail: {
              valueAnimation: true,
              width: '60%',
              lineHeight: 40,
              borderRadius: 8,
              offsetCenter: [0, '35%'],
              fontSize: 20,
              fontWeight: 'bolder',
              formatter: (value) => {
                return value.toFixed(this.config.decimals || 1) + (this.config.unit || '')
              },
              color: 'inherit'
            },
            data: [
              {
                value: this.currentValue,
                name: this.config.title || ''
              }
            ]
          }
        ]
      }
      
      this.chart.setOption(option, true)
    }
  }
}
</script>

<style lang="scss" scoped>
.gauge-component {
  display: flex;
  flex-direction: column;
  
  .circle-gauge {
    flex: 1;
  }
  
  .linear-gauge {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 16px;
    
    .gauge-title {
      font-size: 14px;
      color: #333;
      margin-bottom: 12px;
      text-align: center;
    }
    
    .gauge-bar {
      position: relative;
      height: 20px;
      margin-bottom: 12px;
      
      .gauge-track {
        width: 100%;
        height: 100%;
        background: #e4e7ed;
        border-radius: 10px;
      }
      
      .gauge-fill {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        border-radius: 10px;
        transition: width 0.3s ease;
      }
    }
    
    .gauge-value {
      font-size: 18px;
      font-weight: bold;
      color: #333;
      text-align: center;
      
      .gauge-unit {
        font-size: 14px;
        font-weight: normal;
        color: #666;
        margin-left: 4px;
      }
    }
  }
  
  .number-gauge {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 16px;
    
    .gauge-title {
      font-size: 14px;
      color: #333;
      margin-bottom: 12px;
    }
    
    .gauge-number {
      font-size: 24px;
      font-weight: bold;
      
      .gauge-unit {
        font-size: 16px;
        font-weight: normal;
        margin-left: 4px;
      }
    }
  }
}
</style>
