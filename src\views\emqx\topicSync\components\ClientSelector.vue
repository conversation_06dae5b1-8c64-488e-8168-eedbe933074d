<template>
  <div class="client-selector">
    <!-- 客户端选择对话框 -->
    <el-dialog
      title="选择EMQX客户端"
      :visible.sync="dialogVisible"
      width="800px"
      :before-close="handleClose"
    >
      <div class="client-selector-content">
        <!-- 搜索和筛选 -->
        <div class="filter-section">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索客户端ID或用户名"
                prefix-icon="el-icon-search"
                clearable
                @input="filterClients"
              />
            </el-col>
            <el-col :span="8">
              <el-select
                v-model="statusFilter"
                placeholder="状态筛选"
                clearable
                @change="filterClients"
              >
                <el-option label="已连接" value="connected" />
                <el-option label="系统默认" value="system_default" />
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-button type="primary" icon="el-icon-refresh" @click="loadClients">
                刷新
              </el-button>
            </el-col>
          </el-row>
        </div>

        <!-- 推荐客户端 -->
        <div v-if="recommendedClients.length > 0" class="recommended-section">
          <h4><i class="el-icon-star-on"></i> 推荐客户端</h4>
          <div class="client-cards">
            <div
              v-for="client in recommendedClients"
              :key="'rec-' + client.clientId"
              class="client-card recommended"
              :class="{ selected: selectedClientId === client.clientId }"
              @click="selectClient(client)"
            >
              <div class="client-header">
                <span class="client-id">{{ client.clientId }}</span>
                <el-tag type="warning" size="mini">推荐</el-tag>
              </div>
              <div class="client-info">
                <p><strong>用户名:</strong> {{ client.username || '-' }}</p>
                <p><strong>状态:</strong> 
                  <el-tag :type="getStatusType(client.status)" size="mini">
                    {{ getStatusText(client.status) }}
                  </el-tag>
                </p>
                <p><strong>推荐原因:</strong> {{ client.reason }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 所有客户端列表 -->
        <div class="clients-section">
          <h4><i class="el-icon-connection"></i> 所有可用客户端</h4>
          <div class="client-table">
            <el-table
              :data="filteredClients"
              height="300"
              @row-click="selectClientFromTable"
              highlight-current-row
              :current-row-key="selectedClientId"
              row-key="clientId"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column prop="clientId" label="客户端ID" min-width="150">
                <template slot-scope="scope">
                  <span class="client-id-cell">
                    {{ scope.row.clientId }}
                    <el-tag v-if="scope.row.recommended" type="warning" size="mini">推荐</el-tag>
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="username" label="用户名" min-width="120" />
              <el-table-column prop="status" label="状态" width="100">
                <template slot-scope="scope">
                  <el-tag :type="getStatusType(scope.row.status)" size="mini">
                    {{ getStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="ipAddress" label="IP地址" width="120" />
              <el-table-column prop="protocol" label="协议" width="80" />
              <el-table-column label="操作" width="100">
                <template slot-scope="scope">
                  <el-button
                    type="primary"
                    size="mini"
                    @click.stop="selectClient(scope.row)"
                  >
                    选择
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :disabled="!selectedClientId"
          @click="confirmSelection"
        >
          确认选择
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAvailableClients, recommendClients } from '@/api/emqx/topicClientMapping'

export default {
  name: 'ClientSelector',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    topic: {
      type: String,
      default: ''
    },
    deviceType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: false,
      allClients: [],
      filteredClients: [],
      recommendedClients: [],
      selectedClientId: '',
      searchKeyword: '',
      statusFilter: '',
      loading: false
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.loadClients()
        this.loadRecommendations()
      }
    }
  },
  methods: {
    async loadClients() {
      this.loading = true
      try {
        const response = await getAvailableClients()
        if (response.code === 200) {
          this.allClients = response.data || []
          this.filterClients()
        } else {
          this.$message.error('获取客户端列表失败: ' + response.msg)
        }
      } catch (error) {
        console.error('获取客户端列表失败:', error)
        this.$message.error('获取客户端列表失败')
      } finally {
        this.loading = false
      }
    },

    async loadRecommendations() {
      if (!this.topic) return

      try {
        const response = await recommendClients(this.topic, this.deviceType)
        if (response.code === 200) {
          this.recommendedClients = (response.data || []).slice(0, 3) // 只显示前3个推荐
        }
      } catch (error) {
        console.error('获取推荐客户端失败:', error)
      }
    },

    filterClients() {
      let filtered = [...this.allClients]

      // 关键词搜索
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        filtered = filtered.filter(client =>
          (client.clientId || '').toLowerCase().includes(keyword) ||
          (client.username || '').toLowerCase().includes(keyword)
        )
      }

      // 状态筛选
      if (this.statusFilter) {
        filtered = filtered.filter(client => client.status === this.statusFilter)
      }

      this.filteredClients = filtered
    },

    selectClient(client) {
      this.selectedClientId = client.clientId
    },

    selectClientFromTable(row) {
      this.selectClient(row)
    },

    confirmSelection() {
      if (!this.selectedClientId) {
        this.$message.warning('请选择一个客户端')
        return
      }

      const selectedClient = this.allClients.find(c => c.clientId === this.selectedClientId)
      this.$emit('client-selected', selectedClient)
      this.handleClose()
    },

    handleClose() {
      this.dialogVisible = false
      this.selectedClientId = ''
      this.searchKeyword = ''
      this.statusFilter = ''
      this.$emit('update:visible', false)
    },

    getStatusType(status) {
      switch (status) {
        case 'connected': return 'success'
        case 'system_default': return 'warning'
        default: return 'info'
      }
    },

    getStatusText(status) {
      switch (status) {
        case 'connected': return '已连接'
        case 'system_default': return '系统默认'
        default: return status
      }
    }
  }
}
</script>

<style scoped>
.client-selector-content {
  max-height: 600px;
  overflow-y: auto;
}

.filter-section {
  margin-bottom: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
}

.recommended-section {
  margin-bottom: 20px;
}

.recommended-section h4,
.clients-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.client-cards {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.client-card {
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s;
  min-width: 200px;
  flex: 1;
}

.client-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.client-card.selected {
  border-color: #409eff;
  background: #ecf5ff;
}

.client-card.recommended {
  border-color: #e6a23c;
}

.client-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.client-id {
  font-weight: bold;
  color: #303133;
}

.client-info p {
  margin: 5px 0;
  font-size: 14px;
  color: #606266;
}

.client-id-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dialog-footer {
  text-align: right;
}
</style>
