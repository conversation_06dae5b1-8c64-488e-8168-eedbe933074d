import request from '@/utils/request'

// 检查出入库申请审批状态
export function checkApprovalStatus(billNo) {
  return request({
    url: '/inout/operation/checkApproval',
    method: 'get',
    params: { billNo }
  })
}

// 开始出入库操作
export function startOperation(data) {
  return request({
    url: '/inout/operation/start',
    method: 'post',
    data: data
  })
}

// 更新操作进度
export function updateOperationProgress(detailId, stage, progressData) {
  return request({
    url: '/inout/operation/progress',
    method: 'put',
    data: {
      detailId,
      stage,
      ...progressData
    }
  })
}

// 完成出入库操作
export function completeOperation(data) {
  return request({
    url: '/inout/operation/complete',
    method: 'put',
    data: data
  })
}

// 处理操作异常
export function handleOperationError(detailId, errorMessage) {
  return request({
    url: '/inout/operation/error',
    method: 'put',
    data: {
      detailId,
      errorMessage
    }
  })
}

// 人员刷卡认证
export function authenticateByCard(data) {
  return request({
    url: '/inout/operation/auth/card',
    method: 'post',
    data: data
  })
}

// 人员刷脸认证
export function authenticateByFace(data) {
  return request({
    url: '/inout/operation/auth/face',
    method: 'post',
    data: data
  })
}

// 称重传感器数据处理
export function processWeightSensor(data) {
  return request({
    url: '/inout/operation/weight',
    method: 'post',
    data: data
  })
}

// 获取操作状态
export function getOperationStatus(detailId) {
  return request({
    url: `/inout/operation/status/${detailId}`,
    method: 'get'
  })
}

// 批量开始操作
export function batchStartOperation(data) {
  return request({
    url: '/inout/operation/batch/start',
    method: 'post',
    data: data
  })
}

// 获取操作统计
export function getOperationStatistics(billId) {
  return request({
    url: '/inout/operation/statistics',
    method: 'get',
    params: { billId }
  })
}

// 查询出入库日志列表
export function listLog(query) {
  return request({
    url: '/inout/operation/logs',
    method: 'get',
    params: query
  })
}

// 查询出入库日志详细
export function getLog(id) {
  return request({
    url: `/inout/operation/logs/${id}`,
    method: 'get'
  })
}

// 删除出入库日志
export function delLog(ids) {
  return request({
    url: `/inout/operation/logs/${ids}`,
    method: 'delete'
  })
}

// 导出出入库日志
export function exportLog(query) {
  return request({
    url: '/inout/operation/logs/export',
    method: 'post',
    data: query
  })
}

// 清理历史日志
export function cleanHistoryLogs(data) {
  return request({
    url: '/inout/operation/logs/clean',
    method: 'post',
    data: data
  })
}

// 获取日志清理配置
export function getCleanConfig() {
  return request({
    url: '/inout/operation/logs/clean/config',
    method: 'get'
  })
}

// 更新日志清理配置
export function updateCleanConfig(data) {
  return request({
    url: '/inout/operation/logs/clean/config',
    method: 'put',
    data: data
  })
}

// 预览清理数据
export function previewClean(params) {
  return request({
    url: '/inout/operation/logs/preview',
    method: 'get',
    params: params
  })
}

// 执行清理
export function executeClean(data) {
  return request({
    url: '/inout/operation/logs/execute',
    method: 'post',
    data: data
  })
}

// 获取清理历史
export function getCleanHistory(query) {
  return request({
    url: '/inout/operation/logs/clean/history',
    method: 'get',
    params: query
  })
}

// 获取操作轨迹
export function getOperationTrace(detailId) {
  return request({
    url: `/inout/operation/logs/trace/${detailId}`,
    method: 'get'
  })
}

// 获取异常日志统计
export function getErrorStatistics(params) {
  return request({
    url: '/inout/operation/logs/error/statistics',
    method: 'get',
    params: params
  })
}

// 获取操作效率统计
export function getEfficiencyStatistics(params) {
  return request({
    url: '/inout/operation/logs/efficiency/statistics',
    method: 'get',
    params: params
  })
}
