<template>
  <div class="realtime-chart">
    <div class="chart-header" v-if="showHeader">
      <div class="chart-title">
        <span>{{ title }}</span>
        <el-tag v-if="isConnected" type="success" size="mini">
          <i class="el-icon-connection"></i> 实时连接
        </el-tag>
        <el-tag v-else type="danger" size="mini">
          <i class="el-icon-warning"></i> 连接断开
        </el-tag>
      </div>
      <div class="chart-controls">
        <el-button-group size="mini">
          <el-button :type="isPaused ? 'warning' : 'primary'" @click="togglePause">
            <i :class="isPaused ? 'el-icon-video-play' : 'el-icon-video-pause'"></i>
            {{ isPaused ? '继续' : '暂停' }}
          </el-button>
          <el-button @click="clearData">
            <i class="el-icon-delete"></i>
            清空
          </el-button>
          <el-button @click="downloadChart">
            <i class="el-icon-download"></i>
            下载
          </el-button>
        </el-button-group>
      </div>
    </div>
    
    <base-chart
      ref="chart"
      :option="chartOption"
      :width="width"
      :height="height"
      :loading="loading"
      @chart-ready="onChartReady"
    />
    
    <div class="chart-footer" v-if="showFooter">
      <div class="data-info">
        <span>数据点数: {{ dataPoints }}</span>
        <span>更新频率: {{ updateInterval }}ms</span>
        <span>最后更新: {{ lastUpdateTime }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import BaseChart from './BaseChart.vue'
import { subscribeRealtimeData } from '@/api/monitor/device'

export default {
  name: 'RealtimeChart',
  components: {
    BaseChart
  },
  
  props: {
    // 图表标题
    title: {
      type: String,
      default: '实时监控'
    },
    // 图表宽度
    width: {
      type: String,
      default: '100%'
    },
    // 图表高度
    height: {
      type: String,
      default: '400px'
    },
    // 数据源配置
    dataSource: {
      type: Object,
      required: true
    },
    // 最大数据点数
    maxDataPoints: {
      type: Number,
      default: 100
    },
    // 更新间隔(毫秒)
    updateInterval: {
      type: Number,
      default: 1000
    },
    // 是否显示头部
    showHeader: {
      type: Boolean,
      default: true
    },
    // 是否显示底部
    showFooter: {
      type: Boolean,
      default: true
    },
    // 图表类型
    chartType: {
      type: String,
      default: 'line'
    },
    // Y轴配置
    yAxisConfig: {
      type: Object,
      default: () => ({
        min: 0,
        max: 100,
        name: '值'
      })
    },
    // 系列配置
    seriesConfig: {
      type: Array,
      default: () => [
        {
          name: '数据',
          color: '#409EFF'
        }
      ]
    }
  },
  
  data() {
    return {
      loading: false,
      isConnected: false,
      isPaused: false,
      dataPoints: 0,
      lastUpdateTime: '',
      
      // WebSocket连接
      wsConnection: null,
      
      // 图表数据
      chartData: {
        timestamps: [],
        series: []
      },
      
      // 定时器
      updateTimer: null
    }
  },
  
  computed: {
    chartOption() {
      return {
        title: {
          text: this.title,
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            let result = `${params[0].axisValue}<br/>`
            params.forEach(param => {
              result += `${param.marker}${param.seriesName}: ${param.value}<br/>`
            })
            return result
          }
        },
        legend: {
          data: this.seriesConfig.map(s => s.name),
          top: 30
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.chartData.timestamps,
          boundaryGap: false,
          axisLabel: {
            formatter: (value) => {
              const date = new Date(value)
              return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`
            }
          }
        },
        yAxis: {
          type: 'value',
          name: this.yAxisConfig.name,
          min: this.yAxisConfig.min,
          max: this.yAxisConfig.max,
          axisLabel: {
            formatter: '{value}'
          }
        },
        series: this.generateSeries()
      }
    }
  },
  
  mounted() {
    this.initRealtimeData()
  },
  
  beforeDestroy() {
    this.cleanup()
  },
  
  methods: {
    /** 初始化实时数据 */
    initRealtimeData() {
      // 初始化数据结构
      this.chartData.series = this.seriesConfig.map(() => [])
      
      // 连接WebSocket或启动定时器
      if (this.dataSource.type === 'websocket') {
        this.connectWebSocket()
      } else if (this.dataSource.type === 'polling') {
        this.startPolling()
      } else if (this.dataSource.type === 'mock') {
        this.startMockData()
      }
    },
    
    /** 连接WebSocket */
    connectWebSocket() {
      try {
        this.wsConnection = subscribeRealtimeData((data) => {
          if (!this.isPaused) {
            this.addDataPoint(data)
          }
        })
        
        this.wsConnection.onopen = () => {
          this.isConnected = true
          this.$emit('connected')
        }
        
        this.wsConnection.onclose = () => {
          this.isConnected = false
          this.$emit('disconnected')
          // 尝试重连
          setTimeout(() => {
            if (!this.isConnected) {
              this.connectWebSocket()
            }
          }, 5000)
        }
        
        this.wsConnection.onerror = (error) => {
          console.error('WebSocket连接错误:', error)
          this.isConnected = false
          this.$emit('error', error)
        }
      } catch (error) {
        console.error('WebSocket连接失败:', error)
        this.startMockData() // 降级到模拟数据
      }
    },
    
    /** 开始轮询 */
    startPolling() {
      this.updateTimer = setInterval(async () => {
        if (!this.isPaused) {
          try {
            const response = await this.dataSource.api()
            this.addDataPoint(response.data)
            this.isConnected = true
          } catch (error) {
            console.error('轮询数据失败:', error)
            this.isConnected = false
          }
        }
      }, this.updateInterval)
    },
    
    /** 开始模拟数据 */
    startMockData() {
      this.isConnected = true
      this.updateTimer = setInterval(() => {
        if (!this.isPaused) {
          const mockData = this.generateMockData()
          this.addDataPoint(mockData)
        }
      }, this.updateInterval)
    },
    
    /** 生成模拟数据 */
    generateMockData() {
      const data = {}
      this.seriesConfig.forEach((series, index) => {
        // 生成随机数据，可以根据需要调整算法
        const baseValue = 50
        const variation = 20
        const trend = Math.sin(Date.now() / 10000) * 10
        data[series.name] = baseValue + trend + (Math.random() - 0.5) * variation
      })
      return data
    },
    
    /** 添加数据点 */
    addDataPoint(data) {
      const timestamp = new Date()
      
      // 添加时间戳
      this.chartData.timestamps.push(timestamp)
      
      // 添加系列数据
      this.seriesConfig.forEach((series, index) => {
        const value = data[series.name] || 0
        this.chartData.series[index].push(value)
      })
      
      // 限制数据点数量
      if (this.chartData.timestamps.length > this.maxDataPoints) {
        this.chartData.timestamps.shift()
        this.chartData.series.forEach(series => {
          series.shift()
        })
      }
      
      // 更新统计信息
      this.dataPoints = this.chartData.timestamps.length
      this.lastUpdateTime = timestamp.toLocaleTimeString()
      
      // 触发图表更新
      this.$emit('data-update', data)
    },
    
    /** 生成图表系列配置 */
    generateSeries() {
      return this.seriesConfig.map((config, index) => ({
        name: config.name,
        type: this.chartType,
        data: this.chartData.series[index] || [],
        smooth: true,
        symbol: 'none',
        lineStyle: {
          width: 2,
          color: config.color
        },
        areaStyle: this.chartType === 'area' ? {
          opacity: 0.3,
          color: config.color
        } : undefined
      }))
    },
    
    /** 图表准备就绪 */
    onChartReady(chart) {
      this.$emit('chart-ready', chart)
    },
    
    /** 切换暂停状态 */
    togglePause() {
      this.isPaused = !this.isPaused
      this.$emit('pause-change', this.isPaused)
    },
    
    /** 清空数据 */
    clearData() {
      this.chartData.timestamps = []
      this.chartData.series = this.seriesConfig.map(() => [])
      this.dataPoints = 0
      this.lastUpdateTime = ''
      this.$emit('data-clear')
    },
    
    /** 下载图表 */
    downloadChart() {
      if (this.$refs.chart) {
        const filename = `${this.title}_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}`
        this.$refs.chart.downloadChart(filename)
      }
    },
    
    /** 设置Y轴范围 */
    setYAxisRange(min, max) {
      this.yAxisConfig.min = min
      this.yAxisConfig.max = max
    },
    
    /** 添加系列 */
    addSeries(config) {
      this.seriesConfig.push(config)
      this.chartData.series.push([])
    },
    
    /** 移除系列 */
    removeSeries(index) {
      if (index >= 0 && index < this.seriesConfig.length) {
        this.seriesConfig.splice(index, 1)
        this.chartData.series.splice(index, 1)
      }
    },
    
    /** 获取当前数据 */
    getCurrentData() {
      return {
        timestamps: [...this.chartData.timestamps],
        series: this.chartData.series.map(s => [...s])
      }
    },
    
    /** 导出数据 */
    exportData(format = 'json') {
      const data = this.getCurrentData()
      
      if (format === 'csv') {
        return this.exportToCSV(data)
      } else {
        return JSON.stringify(data, null, 2)
      }
    },
    
    /** 导出为CSV */
    exportToCSV(data) {
      const headers = ['时间戳', ...this.seriesConfig.map(s => s.name)]
      const rows = data.timestamps.map((timestamp, index) => {
        return [
          timestamp.toISOString(),
          ...data.series.map(series => series[index] || '')
        ]
      })
      
      const csvContent = [headers, ...rows]
        .map(row => row.join(','))
        .join('\n')
      
      return csvContent
    },
    
    /** 清理资源 */
    cleanup() {
      if (this.wsConnection) {
        this.wsConnection.close()
        this.wsConnection = null
      }
      
      if (this.updateTimer) {
        clearInterval(this.updateTimer)
        this.updateTimer = null
      }
    }
  }
}
</script>

<style scoped>
.realtime-chart {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.chart-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: bold;
  color: #303133;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.chart-footer {
  padding: 8px 15px;
  background: #f5f7fa;
  border-top: 1px solid #e4e7ed;
  font-size: 12px;
  color: #606266;
}

.data-info {
  display: flex;
  gap: 20px;
}

.data-info span {
  display: flex;
  align-items: center;
}
</style>
