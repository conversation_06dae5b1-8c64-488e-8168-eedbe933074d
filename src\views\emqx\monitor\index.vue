<template>
  <div class="app-container">
    <el-card class="box-card" v-loading="dataLoading" element-loading-text="加载数据中...">
      <div slot="header" class="clearfix">
        <span>EMQX服务器实时监控</span>
        <div style="float: right;">
          <el-tag
            :type="dataStatus"
            size="mini"
            style="margin-right: 10px;"
          >
            {{ dataStatusText }}
          </el-tag>
          <el-button
            style="padding: 3px 8px; margin-right: 8px;"
            type="text"
            @click="validateDataAndRetry"
            size="small"
          >
            <i class="el-icon-check"></i> 验证数据
          </el-button>
          <el-button
            style="padding: 3px 8px"
            type="text"
            @click="refreshData"
            :loading="dataLoading"
          >
            <i class="el-icon-refresh" v-if="!dataLoading"></i> 强制刷新
          </el-button>
        </div>
      </div>



      <!-- 概览统计 -->
      <el-row :gutter="20" class="overview-stats">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon connections">
              <i class="el-icon-connection"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ overview.totalClients || 0 }}</div>
              <div class="stat-label">在线连接数</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon messages">
              <i class="el-icon-message"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ overview.messagesReceived || 0 }}</div>
              <div class="stat-label">接收消息数</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon topics">
              <i class="el-icon-document"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ topicStats.count || overview.topics || 0 }}</div>
              <div class="stat-label">主题数</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon subscriptions">
              <i class="el-icon-star-on"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ subscriptionStats.count || overview.subscriptions || 0 }}</div>
              <div class="stat-label">订阅数</div>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 系统信息 -->
      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="12">
          <el-card>
            <div slot="header">
              <span>系统信息</span>
            </div>
            <div class="system-info">
              <div class="info-item">
                <span class="label">版本:</span>
                <span class="value">{{ overview.version || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <span class="label">状态:</span>
                <el-tag :type="overview.status === 'running' ? 'success' : 'danger'">
                  {{ overview.status === 'running' ? '运行中' : '离线' }}
                </el-tag>
              </div>
              <div class="info-item">
                <span class="label">运行时间:</span>
                <span class="value">{{ formatUptime(overview.uptime) }}</span>
              </div>
            </div>
          </el-card>
        </el-col>

      </el-row>

      <!-- 连接列表 -->
      <el-card style="margin-top: 20px;">
        <div slot="header">
          <span>连接列表</span>
          <el-button style="float: right;" size="mini" @click="refreshClients">
            <i class="el-icon-refresh"></i> 刷新列表
          </el-button>
        </div>
        
        <el-table
          v-loading="clientsLoading"
          :data="clientsList"
          style="width: 100%"
          :default-sort="{prop: 'connected_at', order: 'descending'}"
        >
          <el-table-column prop="clientid" label="客户端ID" width="200" show-overflow-tooltip />
          <el-table-column prop="username" label="用户名" width="120" />
          <el-table-column prop="ipaddress" label="IP地址" width="140" />
          <el-table-column prop="port" label="端口" width="80" />
          <el-table-column prop="deviceType" label="设备类型" width="120">
            <template slot-scope="scope">
              <el-tag :type="scope.row.deviceTypeClass" size="mini">
                {{ scope.row.deviceType }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template slot-scope="scope">
              <el-tag type="success" size="mini">{{ scope.row.status }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="connected_at" label="连接时间" sortable />
          <el-table-column label="操作" width="120" fixed="right">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="primary"
                @click="showClientDetail(scope.row)"
              >
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <pagination
          v-show="clientsTotal > 0"
          :total="clientsTotal"
          :page.sync="clientsQueryParams.pageNum"
          :limit.sync="clientsQueryParams.pageSize"
          @pagination="getClientsList"
        />
      </el-card>
    </el-card>

    <!-- 客户端详情弹窗 -->
    <el-dialog
      title="客户端详情"
      :visible.sync="clientDetailVisible"
      width="800px"
      @close="closeClientDetail"
    >
      <div v-loading="clientDetailLoading">
        <el-tabs v-model="activeTab" type="border-card">
          <!-- 基本信息 -->
          <el-tab-pane label="基本信息" name="basic">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="客户端ID">
                {{ clientDetail.clientid || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="用户名">
                {{ clientDetail.username || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="IP地址">
                {{ clientDetail.ip_address || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="端口">
                {{ clientDetail.port || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="协议版本">
                {{ clientDetail.proto_ver || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="保持连接">
                {{ clientDetail.keepalive || '-' }}秒
              </el-descriptions-item>
              <el-descriptions-item label="连接时间">
                {{ formatTime(clientDetail.connected_at) }}
              </el-descriptions-item>
              <el-descriptions-item label="创建时间">
                {{ formatTime(clientDetail.created_at) }}
              </el-descriptions-item>
              <el-descriptions-item label="清理会话">
                {{ clientDetail.clean_start ? '是' : '否' }}
              </el-descriptions-item>
              <el-descriptions-item label="持久会话">
                {{ clientDetail.is_persistent ? '是' : '否' }}
              </el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>

          <!-- 连接统计 -->
          <el-tab-pane label="连接统计" name="stats">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-card>
                  <div slot="header">消息统计</div>
                  <el-descriptions :column="1" border>
                    <el-descriptions-item label="接收消息数">
                      {{ clientDetail.recv_msg || 0 }}
                    </el-descriptions-item>
                    <el-descriptions-item label="发送消息数">
                      {{ clientDetail.send_msg || 0 }}
                    </el-descriptions-item>
                    <el-descriptions-item label="接收字节数">
                      {{ clientDetail.recv_oct || 0 }}
                    </el-descriptions-item>
                    <el-descriptions-item label="发送字节数">
                      {{ clientDetail.send_oct || 0 }}
                    </el-descriptions-item>
                  </el-descriptions>
                </el-card>
              </el-col>
              <el-col :span="12">
                <el-card>
                  <div slot="header">连接统计</div>
                  <el-descriptions :column="1" border>
                    <el-descriptions-item label="接收包数">
                      {{ clientDetail.recv_pkt || 0 }}
                    </el-descriptions-item>
                    <el-descriptions-item label="发送包数">
                      {{ clientDetail.send_pkt || 0 }}
                    </el-descriptions-item>
                    <el-descriptions-item label="订阅数">
                      {{ clientDetail.subscriptions_cnt || 0 }}
                    </el-descriptions-item>
                    <el-descriptions-item label="消息队列长度">
                      {{ clientDetail.mqueue_len || 0 }}
                    </el-descriptions-item>
                  </el-descriptions>
                </el-card>
              </el-col>
            </el-row>
          </el-tab-pane>

          <!-- 订阅信息 -->
          <el-tab-pane label="订阅信息" name="subscriptions">
            <el-table
              v-loading="subscriptionsLoading"
              :data="clientSubscriptions"
              style="width: 100%"
            >
              <el-table-column prop="topic" label="主题" show-overflow-tooltip />
              <el-table-column prop="qos" label="QoS" width="80" />
              <el-table-column prop="nl" label="No Local" width="100">
                <template slot-scope="scope">
                  {{ scope.row.nl ? '是' : '否' }}
                </template>
              </el-table-column>
              <el-table-column prop="rap" label="Retain As Published" width="150">
                <template slot-scope="scope">
                  {{ scope.row.rap ? '是' : '否' }}
                </template>
              </el-table-column>
              <el-table-column prop="rh" label="Retain Handling" width="120" />
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getOverview, getClients, getMessageStats, getTopicStats, getSubscriptionStats, getClientInfo, getClientSubscriptions } from "@/api/emqx/monitor"

export default {
  name: "EmqxMonitor",
  data() {
    return {
      // 概览数据
      overview: {
        totalClients: 0,
        messagesReceived: 0,
        messagesSent: 0,
        topics: 0,
        subscriptions: 0,
        version: 'N/A',
        status: 'unknown',
        uptime: 'N/A'
      },

      // 消息统计
      messageStats: {
        received: 0,
        sent: 0,
        dropped: 0
      },
      // 主题统计
      topicStats: {
        count: 0,
        max: 0
      },
      // 订阅统计
      subscriptionStats: {
        count: 0,
        max: 0
      },
      // 客户端列表
      clientsList: [],
      clientsTotal: 0,
      clientsLoading: false,
      clientsQueryParams: {
        pageNum: 1,
        pageSize: 10
      },
      // 自动刷新定时器
      refreshTimer: null,
      // 数据加载状态
      dataLoading: false,
      // 数据状态
      lastUpdateTime: null,
      // 客户端详情弹窗
      clientDetailVisible: false,
      clientDetailLoading: false,
      clientDetail: {},
      activeTab: 'basic',
      // 客户端订阅信息
      clientSubscriptions: [],
      subscriptionsLoading: false
    }
  },
  computed: {
    // 数据状态
    dataStatus() {
      const hasValidData = this.overview.totalClients > 0 ||
                          this.overview.messagesReceived > 0 ||
                          this.overview.messagesSent > 0 ||
                          this.clientsList.length > 0

      if (this.dataLoading) return 'info'
      if (hasValidData) return 'success'
      return 'warning'
    },
    // 数据状态文本
    dataStatusText() {
      if (this.dataLoading) return '加载中'

      const hasValidData = this.overview.totalClients > 0 ||
                          this.overview.messagesReceived > 0 ||
                          this.overview.messagesSent > 0 ||
                          this.clientsList.length > 0

      if (hasValidData) {
        return this.lastUpdateTime ? `数据正常 (${this.formatTime(this.lastUpdateTime)})` : '数据正常'
      }
      return '数据异常'
    }
  },
  async created() {
    console.log('EMQX监控页面创建')
    // 确保数据初始化完成
    await this.initData()
  },
  mounted() {
    console.log('EMQX监控页面挂载完成')
    // 确保DOM渲染完成后再开始自动刷新
    this.$nextTick(() => {
      // 验证数据是否正确加载，如果没有则重试
      setTimeout(() => {
        this.validateDataAndRetry()
      }, 1000)
      this.startAutoRefresh()
    })

    // 监听页面可见性变化
    this.handleVisibilityChange = () => {
      if (!document.hidden) {
        console.log('页面重新获得焦点，刷新数据')
        this.refreshData()
      }
    }
    document.addEventListener('visibilitychange', this.handleVisibilityChange)
  },
  beforeDestroy() {
    console.log('EMQX监控页面即将销毁，清理定时器和事件监听器')
    this.stopAutoRefresh()
    // 清理页面可见性监听器
    if (this.handleVisibilityChange) {
      document.removeEventListener('visibilitychange', this.handleVisibilityChange)
    }
  },
  destroyed() {
    console.log('EMQX监控页面已销毁')
  },
  methods: {
    /** 初始化数据 */
    async initData() {
      this.dataLoading = true
      console.log('开始初始化EMQX监控数据...')

      try {
        // 串行加载数据，避免并发请求导致的数据不一致
        await this.getOverview()

        await this.getMessageStats()
        await this.getTopicStats()
        await this.getSubscriptionStats()
        await this.getClientsList()

        // 更新最后更新时间
        this.lastUpdateTime = new Date()
        console.log('数据初始化完成')
      } catch (error) {
        console.error('初始化数据失败:', error)
        this.$message.error('数据加载失败，请刷新重试')
      } finally {
        this.dataLoading = false
      }
    },
    /** 获取概览数据 */
    async getOverview() {
      try {
        const response = await getOverview()
        if (response && response.code === 200 && response.data) {
          // 直接替换整个overview对象
          this.overview = {
            totalClients: response.data.totalClients || response.data.connectedClients || 0,
            messagesReceived: response.data.messagesReceived || 0,
            messagesSent: response.data.messagesSent || 0,
            topics: response.data.topics || 0,
            subscriptions: response.data.subscriptions || 0,
            version: response.data.version || (response.data.system && response.data.system.version) || 'N/A',
            status: response.data.status || 'running',
            uptime: response.data.uptime || (response.data.system && response.data.system.uptime) || 'N/A'
          }
          console.log('概览数据已更新:', this.overview)
        } else {
          console.warn('概览数据响应格式异常:', response)
        }
      } catch (error) {
        console.error('获取概览数据失败:', error)
        this.$message.error('获取概览数据失败')
      }
    },

    /** 获取客户端列表 */
    async getClientsList() {
      this.clientsLoading = true
      try {
        const response = await getClients(this.clientsQueryParams)
        if (response && response.code === 200) {
          this.clientsList = response.rows || []
          this.clientsTotal = response.total || 0
        } else {
          console.warn('客户端列表响应格式异常:', response)
          this.clientsList = []
          this.clientsTotal = 0
        }
        console.log('客户端列表已更新:', this.clientsList.length, '条记录')
      } catch (error) {
        console.error('获取客户端列表失败:', error)
        this.clientsList = []
        this.clientsTotal = 0
        this.$message.error('获取客户端列表失败')
      } finally {
        this.clientsLoading = false
      }
    },
    /** 刷新所有数据 */
    async refreshData() {
      this.dataLoading = true
      try {
        // 清空现有数据，强制重新加载
        this.overview = {
          totalClients: 0,
          messagesReceived: 0,
          messagesSent: 0,
          topics: 0,
          subscriptions: 0,
          version: 'N/A',
          status: 'unknown',
          uptime: 'N/A'
        }

        this.messageStats = { received: 0, sent: 0, dropped: 0 }
        this.topicStats = { count: 0, max: 0 }
        this.subscriptionStats = { count: 0, max: 0 }
        this.clientsList = []
        this.clientsTotal = 0

        // 强制Vue重新渲染
        this.$forceUpdate()

        // 重新加载数据
        await this.initData()
        this.$message.success('数据已刷新')
      } catch (error) {
        console.error('刷新数据失败:', error)
        this.$message.error('刷新数据失败')
      } finally {
        this.dataLoading = false
      }
    },
    /** 刷新客户端列表 */
    refreshClients() {
      this.getClientsList()
    },
    /** 格式化运行时间 */
    formatUptime(uptime) {
      if (!uptime || uptime === 'N/A' || uptime === 0) return 'N/A'

      if (typeof uptime === 'number') {
        let seconds = uptime

        // 如果是毫秒格式，转换为秒
        if (uptime > 1000000) {
          seconds = Math.floor(uptime / 1000)
        }

        const days = Math.floor(seconds / 86400)
        const hours = Math.floor((seconds % 86400) / 3600)
        const minutes = Math.floor((seconds % 3600) / 60)

        if (days > 0) {
          return `${days}天${hours}小时${minutes}分钟`
        } else if (hours > 0) {
          return `${hours}小时${minutes}分钟`
        } else {
          return `${minutes}分钟`
        }
      }

      return uptime.toString()
    },
    /** 获取消息统计 */
    async getMessageStats() {
      try {
        const response = await getMessageStats()
        if (response && response.code === 200 && response.data) {
          this.messageStats = {
            received: response.data.received || 0,
            sent: response.data.sent || 0,
            dropped: response.data.dropped || 0
          }
          console.log('消息统计已更新:', this.messageStats)
        } else {
          console.warn('消息统计响应格式异常:', response)
        }
      } catch (error) {
        console.error('获取消息统计失败:', error)
      }
    },
    /** 获取主题统计 */
    async getTopicStats() {
      try {
        const response = await getTopicStats()
        if (response && response.code === 200 && response.data) {
          this.topicStats = {
            count: response.data.count || 0,
            max: response.data.max || 0
          }
          console.log('主题统计已更新:', this.topicStats)
        } else {
          console.warn('主题统计响应格式异常:', response)
        }
      } catch (error) {
        console.error('获取主题统计失败:', error)
      }
    },
    /** 获取订阅统计 */
    async getSubscriptionStats() {
      try {
        const response = await getSubscriptionStats()
        if (response && response.code === 200 && response.data) {
          this.subscriptionStats = {
            count: response.data.count || 0,
            max: response.data.max || 0
          }
          console.log('订阅统计已更新:', this.subscriptionStats)
        } else {
          console.warn('订阅统计响应格式异常:', response)
        }
      } catch (error) {
        console.error('获取订阅统计失败:', error)
      }
    },
    /** 开始自动刷新 */
    startAutoRefresh() {
      // 清除可能存在的定时器
      this.stopAutoRefresh()

      this.refreshTimer = setInterval(async () => {
        try {
          console.log('自动刷新数据...')
          await this.initData()
        } catch (error) {
          console.error('自动刷新失败:', error)
        }
      }, 30000) // 30秒刷新一次

      console.log('自动刷新已启动')
    },
    /** 停止自动刷新 */
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
        console.log('自动刷新已停止')
      }
    },
    /** 验证数据并重试 */
    validateDataAndRetry() {
      // 检查关键数据是否为空或异常
      const hasValidData = this.overview.totalClients > 0 ||
                          this.overview.messagesReceived > 0 ||
                          this.overview.messagesSent > 0 ||
                          this.clientsList.length > 0

      if (!hasValidData && !this.dataLoading) {
        console.warn('检测到数据可能未正确加载，尝试重新获取...')
        this.refreshData()
      }
    },
    /** 显示客户端详情 */
    async showClientDetail(client) {
      this.clientDetailVisible = true
      this.clientDetailLoading = true
      this.activeTab = 'basic'

      // 清空之前的订阅信息
      this.clientSubscriptions = []

      try {
        // 获取客户端详情
        const response = await getClientInfo(client.clientid)
        if (response && response.code === 200 && response.data) {
          this.clientDetail = response.data
        } else {
          this.clientDetail = client
        }
      } catch (error) {
        console.error('获取客户端详情失败:', error)
        this.clientDetail = client
      } finally {
        this.clientDetailLoading = false
      }

      // 获取订阅信息
      await this.getClientSubscriptions(client.clientid || client.client_id)
    },
    /** 获取客户端订阅信息 */
    async getClientSubscriptions(clientId) {
      this.subscriptionsLoading = true
      try {
        const response = await getClientSubscriptions(clientId)
        console.log('客户端订阅信息响应:', response)

        // 确保返回的是数组
        let subscriptions = []
        if (response && response.code === 200) {
          if (Array.isArray(response.data)) {
            subscriptions = response.data
          } else if (response.data && Array.isArray(response.data.data)) {
            subscriptions = response.data.data
          } else if (response.data && typeof response.data === 'object') {
            // 如果是对象，尝试转换为数组
            subscriptions = Object.values(response.data).filter(item =>
              item && typeof item === 'object' && item.topic
            )
          }
        }

        this.clientSubscriptions = subscriptions
        console.log('客户端订阅信息已更新:', this.clientSubscriptions.length, '条记录')
      } catch (error) {
        console.error('获取客户端订阅信息失败:', error)
        this.clientSubscriptions = []
      } finally {
        this.subscriptionsLoading = false
      }
    },
    /** 关闭客户端详情弹窗 */
    closeClientDetail() {
      this.clientDetailVisible = false
      this.clientDetail = {}
      this.clientSubscriptions = []
    },
    /** 格式化时间 */
    formatTime(time) {
      if (!time) return '-'
      return new Date(time).toLocaleString()
    }
  }
}
</script>

<style scoped>
.overview-stats {
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border-left: 4px solid #409EFF;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stat-icon.connections { background: #409EFF; }
.stat-icon.messages { background: #67C23A; }
.stat-icon.topics { background: #E6A23C; }
.stat-icon.subscriptions { background: #F56C6C; }

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.system-info, .connection-stats {
  padding: 10px 0;
}

.info-item, .stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #EBEEF5;
}

.info-item:last-child, .stat-item:last-child {
  border-bottom: none;
}

.label {
  font-weight: 500;
  color: #606266;
}

.value {
  color: #303133;
}

/* 客户端详情弹窗样式 */
.el-dialog__body {
  padding: 10px 20px;
}

.el-tabs--border-card {
  border: 1px solid #dcdfe6;
}

.el-descriptions {
  margin-bottom: 20px;
}

.el-descriptions__label {
  font-weight: 600;
}
</style>
