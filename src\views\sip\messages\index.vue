<template>
  <div class="sip-messages">
    <div class="page-header">
      <h2>SIP 消息管理</h2>
      <p>管理 SIP 协议消息和 MANSCDP+XML 消息交互</p>
    </div>
    
    <el-card>
      <div class="coming-soon">
        <i class="el-icon-message" style="font-size: 64px; color: #67C23A;"></i>
        <h3>功能开发中...</h3>
        <p>SIP 消息管理功能正在开发中，敬请期待！</p>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'SipMessages'
}
</script>

<style lang="scss" scoped>
.sip-messages {
  padding: 20px;
  
  .page-header {
    margin-bottom: 20px;
    
    h2 {
      margin: 0 0 8px 0;
      color: #303133;
    }
    
    p {
      margin: 0;
      color: #606266;
    }
  }
  
  .coming-soon {
    text-align: center;
    padding: 60px 20px;
    
    h3 {
      margin: 20px 0 10px 0;
      color: #303133;
    }
    
    p {
      color: #606266;
    }
  }
}
</style>
