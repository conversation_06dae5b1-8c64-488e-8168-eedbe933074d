<template>
  <div class="mqtt-dashboard">
    <!-- 状态卡片 -->
    <el-row :gutter="20" class="status-cards">
      <el-col :span="6">
        <el-card class="status-card">
          <div class="card-content">
            <div class="card-icon broker-icon">
              <i class="el-icon-connection"></i>
            </div>
            <div class="card-info">
              <div class="card-title">Broker 状态</div>
              <div class="card-value" :class="brokerStatus.running ? 'status-running' : 'status-stopped'">
                {{ brokerStatus.running ? '运行中' : '已停止' }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="status-card">
          <div class="card-content">
            <div class="card-icon clients-icon">
              <i class="el-icon-user"></i>
            </div>
            <div class="card-info">
              <div class="card-title">在线客户端</div>
              <div class="card-value">{{ statistics.onlineClients || 0 }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="status-card">
          <div class="card-content">
            <div class="card-icon messages-icon">
              <i class="el-icon-message"></i>
            </div>
            <div class="card-info">
              <div class="card-title">消息总数</div>
              <div class="card-value">{{ statistics.totalMessages || 0 }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="status-card">
          <div class="card-content">
            <div class="card-icon topics-icon">
              <i class="el-icon-menu"></i>
            </div>
            <div class="card-info">
              <div class="card-title">活跃主题</div>
              <div class="card-value">{{ statistics.activeTopics || 0 }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-section">
      <el-col :span="12">
        <el-card title="连接趋势">
          <div slot="header">
            <span>连接趋势</span>
            <el-button-group style="float: right;">
              <el-button size="mini" @click="changeTimeRange('1h')" :type="timeRange === '1h' ? 'primary' : ''">1小时</el-button>
              <el-button size="mini" @click="changeTimeRange('24h')" :type="timeRange === '24h' ? 'primary' : ''">24小时</el-button>
              <el-button size="mini" @click="changeTimeRange('7d')" :type="timeRange === '7d' ? 'primary' : ''">7天</el-button>
            </el-button-group>
          </div>
          <div ref="connectionChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card title="消息流量">
          <div slot="header">
            <span>消息流量</span>
            <el-switch
              v-model="realTimeMode"
              style="float: right;"
              active-text="实时"
              inactive-text="历史">
            </el-switch>
          </div>
          <div ref="messageChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细信息 -->
    <el-row :gutter="20" class="details-section">
      <el-col :span="8">
        <el-card title="系统信息">
          <div class="info-list">
            <div class="info-item">
              <span class="info-label">服务器版本:</span>
              <span class="info-value">{{ brokerStatus.version || 'N/A' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">启动时间:</span>
              <span class="info-value">{{ formatTime(brokerStatus.startTime) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">运行时长:</span>
              <span class="info-value">{{ formatDuration(brokerStatus.uptime) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">监听端口:</span>
              <span class="info-value">{{ brokerStatus.port || 1883 }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card title="性能指标">
          <div class="info-list">
            <div class="info-item">
              <span class="info-label">CPU使用率:</span>
              <span class="info-value">{{ statistics.cpuUsage || 0 }}%</span>
            </div>
            <div class="info-item">
              <span class="info-label">内存使用:</span>
              <span class="info-value">{{ formatBytes(statistics.memoryUsage) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">网络流量:</span>
              <span class="info-value">{{ formatBytes(statistics.networkTraffic) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">消息速率:</span>
              <span class="info-value">{{ statistics.messageRate || 0 }}/秒</span>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card title="快速操作">
          <div class="quick-actions">
            <el-button type="primary" icon="el-icon-refresh" @click="refreshData">刷新数据</el-button>
            <el-button type="success" icon="el-icon-view" @click="$router.push('/mqtt/clients')">查看客户端</el-button>
            <el-button type="info" icon="el-icon-message" @click="$router.push('/mqtt/messages')">消息管理</el-button>
            <el-button type="warning" icon="el-icon-setting" @click="$router.push('/mqtt/settings')">系统设置</el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getBrokerStatus, getBrokerStatistics } from '@/api/mqtt/broker'
import * as echarts from 'echarts'

export default {
  name: 'MqttDashboard',
  data() {
    return {
      brokerStatus: {},
      statistics: {},
      timeRange: '1h',
      realTimeMode: true,
      connectionChart: null,
      messageChart: null,
      refreshTimer: null
    }
  },
  mounted() {
    this.initData()
    this.initCharts()
    this.startAutoRefresh()
  },
  beforeDestroy() {
    this.stopAutoRefresh()
    if (this.connectionChart) {
      this.connectionChart.dispose()
    }
    if (this.messageChart) {
      this.messageChart.dispose()
    }
  },
  methods: {
    async initData() {
      try {
        const [statusRes, statsRes] = await Promise.all([
          getBrokerStatus(),
          getBrokerStatistics()
        ])
        this.brokerStatus = statusRes.data
        this.statistics = statsRes.data
      } catch (error) {
        this.$message.error('获取数据失败: ' + error.message)
      }
    },
    
    initCharts() {
      this.$nextTick(() => {
        this.initConnectionChart()
        this.initMessageChart()
      })
    },
    
    initConnectionChart() {
      this.connectionChart = echarts.init(this.$refs.connectionChart)
      // 图表配置...
    },
    
    initMessageChart() {
      this.messageChart = echarts.init(this.$refs.messageChart)
      // 图表配置...
    },
    
    changeTimeRange(range) {
      this.timeRange = range
      this.updateCharts()
    },
    
    updateCharts() {
      // 更新图表数据
    },
    
    startAutoRefresh() {
      this.refreshTimer = setInterval(() => {
        if (this.realTimeMode) {
          this.refreshData()
        }
      }, 5000)
    },
    
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },
    
    refreshData() {
      this.initData()
    },
    
    formatTime(timestamp) {
      if (!timestamp) return 'N/A'
      return new Date(timestamp).toLocaleString()
    },
    
    formatDuration(seconds) {
      if (!seconds) return 'N/A'
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      return `${hours}小时${minutes}分钟`
    },
    
    formatBytes(bytes) {
      if (!bytes) return '0 B'
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(1024))
      return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
    }
  }
}
</script>

<style lang="scss" scoped>
.mqtt-dashboard {
  padding: 20px;
  
  .status-cards {
    margin-bottom: 20px;
    
    .status-card {
      .card-content {
        display: flex;
        align-items: center;
        
        .card-icon {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 15px;
          
          i {
            font-size: 24px;
            color: white;
          }
          
          &.broker-icon {
            background: linear-gradient(45deg, #409EFF, #67C23A);
          }
          
          &.clients-icon {
            background: linear-gradient(45deg, #67C23A, #E6A23C);
          }
          
          &.messages-icon {
            background: linear-gradient(45deg, #E6A23C, #F56C6C);
          }
          
          &.topics-icon {
            background: linear-gradient(45deg, #F56C6C, #909399);
          }
        }
        
        .card-info {
          .card-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
          }
          
          .card-value {
            font-size: 24px;
            font-weight: bold;
            
            &.status-running {
              color: #67C23A;
            }
            
            &.status-stopped {
              color: #F56C6C;
            }
          }
        }
      }
    }
  }
  
  .charts-section {
    margin-bottom: 20px;
  }
  
  .details-section {
    .info-list {
      .info-item {
        display: flex;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .info-label {
          color: #666;
        }
        
        .info-value {
          font-weight: bold;
        }
      }
    }
    
    .quick-actions {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
  }
}
</style>
