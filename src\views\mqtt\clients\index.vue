<template>
  <div class="mqtt-clients">
    <!-- 搜索和操作栏 -->
    <div class="search-bar">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="客户端ID">
          <el-input
            v-model="searchForm.clientId"
            placeholder="请输入客户端ID"
            clearable
            @keyup.enter.native="handleSearch">
          </el-input>
        </el-form-item>
        <el-form-item label="IP地址">
          <el-input
            v-model="searchForm.ipAddress"
            placeholder="请输入IP地址"
            clearable
            @keyup.enter.native="handleSearch">
          </el-input>
        </el-form-item>
        <el-form-item label="连接状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="已连接" value="connected"></el-option>
            <el-option label="已断开" value="disconnected"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
      
      <div class="action-buttons">
        <el-button type="success" icon="el-icon-refresh" @click="refreshData">刷新</el-button>
        <el-button type="warning" icon="el-icon-download" @click="exportData">导出</el-button>
        <el-button type="danger" icon="el-icon-delete" @click="batchDisconnect" :disabled="!selectedClients.length">
          批量断开
        </el-button>
      </div>
    </div>

    <!-- 客户端列表 -->
    <el-table
      v-loading="loading"
      :data="clientList"
      @selection-change="handleSelectionChange"
      style="width: 100%">
      
      <el-table-column type="selection" width="55"></el-table-column>
      
      <el-table-column prop="clientId" label="客户端ID" min-width="150">
        <template slot-scope="scope">
          <el-link type="primary" @click="showClientDetails(scope.row)">
            {{ scope.row.clientId }}
          </el-link>
        </template>
      </el-table-column>
      
      <el-table-column prop="ipAddress" label="IP地址" width="120"></el-table-column>
      
      <el-table-column prop="port" label="端口" width="80"></el-table-column>
      
      <el-table-column prop="protocol" label="协议" width="80">
        <template slot-scope="scope">
          <el-tag size="mini" :type="scope.row.protocol === 'MQTT' ? 'success' : 'info'">
            {{ scope.row.protocol }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="status" label="状态" width="80">
        <template slot-scope="scope">
          <el-tag size="mini" :type="scope.row.status === 'connected' ? 'success' : 'danger'">
            {{ scope.row.status === 'connected' ? '已连接' : '已断开' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="connectTime" label="连接时间" width="160">
        <template slot-scope="scope">
          {{ formatTime(scope.row.connectTime) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="lastActivity" label="最后活动" width="160">
        <template slot-scope="scope">
          {{ formatTime(scope.row.lastActivity) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="subscriptions" label="订阅数" width="80" align="center"></el-table-column>
      
      <el-table-column prop="messagesSent" label="发送消息" width="100" align="center"></el-table-column>
      
      <el-table-column prop="messagesReceived" label="接收消息" width="100" align="center"></el-table-column>
      
      <el-table-column label="操作" width="200" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="primary" @click="showClientDetails(scope.row)">详情</el-button>
          <el-button size="mini" type="info" @click="showSubscriptions(scope.row)">订阅</el-button>
          <el-button 
            size="mini" 
            type="danger" 
            @click="disconnectClient(scope.row)"
            :disabled="scope.row.status !== 'connected'">
            断开
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total">
      </el-pagination>
    </div>

    <!-- 客户端详情对话框 -->
    <el-dialog title="客户端详情" :visible.sync="detailDialogVisible" width="800px">
      <div v-if="selectedClient" class="client-details">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="客户端ID">{{ selectedClient.clientId }}</el-descriptions-item>
          <el-descriptions-item label="IP地址">{{ selectedClient.ipAddress }}</el-descriptions-item>
          <el-descriptions-item label="端口">{{ selectedClient.port }}</el-descriptions-item>
          <el-descriptions-item label="协议版本">{{ selectedClient.protocolVersion }}</el-descriptions-item>
          <el-descriptions-item label="Keep Alive">{{ selectedClient.keepAlive }}秒</el-descriptions-item>
          <el-descriptions-item label="Clean Session">{{ selectedClient.cleanSession ? '是' : '否' }}</el-descriptions-item>
          <el-descriptions-item label="连接时间">{{ formatTime(selectedClient.connectTime) }}</el-descriptions-item>
          <el-descriptions-item label="最后活动">{{ formatTime(selectedClient.lastActivity) }}</el-descriptions-item>
          <el-descriptions-item label="发送消息数">{{ selectedClient.messagesSent }}</el-descriptions-item>
          <el-descriptions-item label="接收消息数">{{ selectedClient.messagesReceived }}</el-descriptions-item>
          <el-descriptions-item label="订阅数量">{{ selectedClient.subscriptions }}</el-descriptions-item>
          <el-descriptions-item label="用户代理">{{ selectedClient.userAgent || 'N/A' }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="sendMessageToClient">发送消息</el-button>
      </div>
    </el-dialog>

    <!-- 订阅列表对话框 -->
    <el-dialog title="订阅列表" :visible.sync="subscriptionDialogVisible" width="600px">
      <el-table :data="subscriptionList" style="width: 100%">
        <el-table-column prop="topic" label="主题" min-width="200"></el-table-column>
        <el-table-column prop="qos" label="QoS" width="80" align="center"></el-table-column>
        <el-table-column prop="subscribeTime" label="订阅时间" width="160">
          <template slot-scope="scope">
            {{ formatTime(scope.row.subscribeTime) }}
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="subscriptionDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  getOnlineClients, 
  getClientDetails, 
  disconnectClient, 
  batchDisconnectClients,
  getClientSubscriptions,
  exportClients 
} from '@/api/mqtt/client'

export default {
  name: 'MqttClients',
  data() {
    return {
      loading: false,
      clientList: [],
      selectedClients: [],
      selectedClient: null,
      subscriptionList: [],
      searchForm: {
        clientId: '',
        ipAddress: '',
        status: ''
      },
      pagination: {
        page: 1,
        size: 20,
        total: 0
      },
      detailDialogVisible: false,
      subscriptionDialogVisible: false
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    async loadData() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.page,
          size: this.pagination.size,
          ...this.searchForm
        }
        const response = await getOnlineClients(params)
        this.clientList = response.data.data
        this.pagination.total = response.data.total
      } catch (error) {
        this.$message.error('获取客户端列表失败: ' + error.message)
      } finally {
        this.loading = false
      }
    },
    
    handleSearch() {
      this.pagination.page = 1
      this.loadData()
    },
    
    resetSearch() {
      this.searchForm = {
        clientId: '',
        ipAddress: '',
        status: ''
      }
      this.handleSearch()
    },
    
    refreshData() {
      this.loadData()
    },
    
    handleSelectionChange(selection) {
      this.selectedClients = selection
    },
    
    handleSizeChange(val) {
      this.pagination.size = val
      this.loadData()
    },
    
    handleCurrentChange(val) {
      this.pagination.page = val
      this.loadData()
    },
    
    async showClientDetails(client) {
      try {
        const response = await getClientDetails(client.clientId)
        this.selectedClient = response.data
        this.detailDialogVisible = true
      } catch (error) {
        this.$message.error('获取客户端详情失败: ' + error.message)
      }
    },
    
    async showSubscriptions(client) {
      try {
        const response = await getClientSubscriptions(client.clientId)
        this.subscriptionList = response.data
        this.subscriptionDialogVisible = true
      } catch (error) {
        this.$message.error('获取订阅列表失败: ' + error.message)
      }
    },
    
    async disconnectClient(client) {
      try {
        await this.$confirm(`确定要断开客户端 ${client.clientId} 的连接吗？`, '确认操作', {
          type: 'warning'
        })
        
        await disconnectClient(client.clientId, '管理员手动断开')
        this.$message.success('客户端已断开连接')
        this.loadData()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('断开连接失败: ' + error.message)
        }
      }
    },
    
    async batchDisconnect() {
      try {
        await this.$confirm(`确定要断开选中的 ${this.selectedClients.length} 个客户端的连接吗？`, '确认操作', {
          type: 'warning'
        })
        
        const clientIds = this.selectedClients.map(client => client.clientId)
        await batchDisconnectClients(clientIds, '管理员批量断开')
        this.$message.success('批量断开连接成功')
        this.loadData()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('批量断开连接失败: ' + error.message)
        }
      }
    },
    
    async exportData() {
      try {
        const params = { ...this.searchForm }
        await exportClients(params)
        this.$message.success('导出成功')
      } catch (error) {
        this.$message.error('导出失败: ' + error.message)
      }
    },
    
    sendMessageToClient() {
      // 实现发送消息功能
      this.$message.info('发送消息功能开发中...')
    },
    
    formatTime(timestamp) {
      if (!timestamp) return 'N/A'
      return new Date(timestamp).toLocaleString()
    }
  }
}
</script>

<style lang="scss" scoped>
.mqtt-clients {
  padding: 20px;
  
  .search-bar {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    
    .search-form {
      flex: 1;
    }
    
    .action-buttons {
      margin-left: 20px;
    }
  }
  
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
  
  .client-details {
    margin-bottom: 20px;
  }
}
</style>
