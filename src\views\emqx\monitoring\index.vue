<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="page-title">
        <h2><i class="el-icon-monitor"></i> EMQX实时监控</h2>
        <p>实时监控EMQX服务器状态、连接数、消息流量等关键指标</p>
      </div>
      <div class="page-actions">
        <el-button type="primary" icon="el-icon-refresh" @click="refreshData" :loading="loading">
          刷新数据
        </el-button>
        <el-switch
          v-model="autoRefresh"
          active-text="自动刷新"
          inactive-text="手动刷新"
          @change="toggleAutoRefresh"
        />
      </div>
    </div>

    <!-- 实时状态卡片 -->
    <el-row :gutter="20" class="status-cards">
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-content">
            <div class="status-icon server" :class="getServerStatusClass()">
              <i class="el-icon-cpu"></i>
            </div>
            <div class="status-info">
              <div class="status-value">{{ serverStatus.status }}</div>
              <div class="status-label">服务器状态</div>
              <div class="status-detail">运行时间: {{ formatUptime(serverStatus.uptime) }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-content">
            <div class="status-icon connections">
              <i class="el-icon-connection"></i>
            </div>
            <div class="status-info">
              <div class="status-value">{{ formatNumber(metrics.connections) }}</div>
              <div class="status-label">当前连接数</div>
              <div class="status-detail">最大: {{ formatNumber(metrics.maxConnections) }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-content">
            <div class="status-icon messages">
              <i class="el-icon-message"></i>
            </div>
            <div class="status-info">
              <div class="status-value">{{ formatNumber(metrics.messagesPerSecond) }}/s</div>
              <div class="status-label">消息速率</div>
              <div class="status-detail">总计: {{ formatNumber(metrics.totalMessages) }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-content">
            <div class="status-icon topics">
              <i class="el-icon-menu"></i>
            </div>
            <div class="status-info">
              <div class="status-value">{{ formatNumber(metrics.topics) }}</div>
              <div class="status-label">活跃主题数</div>
              <div class="status-detail">订阅: {{ formatNumber(metrics.subscriptions) }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表监控 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card class="chart-card">
          <div slot="header">
            <span><i class="el-icon-data-line"></i> 连接数趋势</span>
            <div style="float: right;">
              <el-radio-group v-model="timeRange" size="mini" @change="updateCharts">
                <el-radio-button label="5m">5分钟</el-radio-button>
                <el-radio-button label="1h">1小时</el-radio-button>
                <el-radio-button label="6h">6小时</el-radio-button>
              </el-radio-group>
            </div>
          </div>
          <div id="connectionsChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="chart-card">
          <div slot="header">
            <span><i class="el-icon-pie-chart"></i> 消息流量</span>
          </div>
          <div id="messagesChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-card class="chart-card">
          <div slot="header">
            <span><i class="el-icon-cpu"></i> 系统资源</span>
          </div>
          <div id="resourceChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="chart-card">
          <div slot="header">
            <span><i class="el-icon-data-analysis"></i> 主题活跃度</span>
          </div>
          <div id="topicsChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 客户端连接列表 -->
    <el-card class="clients-card">
      <div slot="header">
        <span><i class="el-icon-user"></i> 活跃客户端连接</span>
        <div style="float: right;">
          <el-input
            v-model="clientSearch"
            placeholder="搜索客户端ID"
            prefix-icon="el-icon-search"
            size="small"
            style="width: 200px; margin-right: 10px;"
            clearable
          />
          <el-button type="text" icon="el-icon-refresh" @click="loadClients">刷新</el-button>
        </div>
      </div>
      
      <el-table
        :data="filteredClients"
        v-loading="clientsLoading"
        size="small"
        max-height="400"
      >
        <el-table-column prop="clientId" label="客户端ID" min-width="200">
          <template slot-scope="scope">
            <span class="client-id">{{ scope.row.clientId }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="username" label="用户名" width="150" />
        
        <el-table-column prop="ipAddress" label="IP地址" width="120" />
        
        <el-table-column prop="connectedAt" label="连接时间" width="160">
          <template slot-scope="scope">
            {{ formatTime(scope.row.connectedAt) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="keepAlive" label="心跳间隔" width="100">
          <template slot-scope="scope">
            {{ scope.row.keepAlive }}s
          </template>
        </el-table-column>
        
        <el-table-column prop="subscriptions" label="订阅数" width="80" />
        
        <el-table-column prop="messagesSent" label="发送消息" width="100">
          <template slot-scope="scope">
            {{ formatNumber(scope.row.messagesSent) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="messagesReceived" label="接收消息" width="100">
          <template slot-scope="scope">
            {{ formatNumber(scope.row.messagesReceived) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="viewClientDetail(scope.row)"
            >
              详情
            </el-button>
            <el-button
              size="mini"
              type="danger"
              @click="disconnectClient(scope.row)"
            >
              断开
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 告警信息 -->
    <el-card class="alerts-card" v-if="alerts.length > 0">
      <div slot="header">
        <span><i class="el-icon-warning"></i> 系统告警</span>
        <el-tag type="danger" size="small">{{ alerts.length }} 条告警</el-tag>
      </div>
      
      <div class="alerts-list">
        <div
          v-for="alert in alerts"
          :key="alert.id"
          class="alert-item"
          :class="getAlertClass(alert.level)"
        >
          <div class="alert-icon">
            <i :class="getAlertIcon(alert.level)"></i>
          </div>
          <div class="alert-content">
            <div class="alert-title">{{ alert.title }}</div>
            <div class="alert-message">{{ alert.message }}</div>
            <div class="alert-time">{{ formatTime(alert.timestamp) }}</div>
          </div>
          <div class="alert-actions">
            <el-button size="mini" @click="acknowledgeAlert(alert)">确认</el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 客户端详情对话框 -->
    <el-dialog
      title="客户端详情"
      :visible.sync="clientDetailVisible"
      width="800px"
    >
      <div v-if="selectedClient" class="client-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="客户端ID">
            {{ selectedClient.clientId }}
          </el-descriptions-item>
          <el-descriptions-item label="用户名">
            {{ selectedClient.username }}
          </el-descriptions-item>
          <el-descriptions-item label="IP地址">
            {{ selectedClient.ipAddress }}
          </el-descriptions-item>
          <el-descriptions-item label="端口">
            {{ selectedClient.port }}
          </el-descriptions-item>
          <el-descriptions-item label="协议版本">
            {{ selectedClient.protocolVersion }}
          </el-descriptions-item>
          <el-descriptions-item label="Clean Session">
            {{ selectedClient.cleanSession ? '是' : '否' }}
          </el-descriptions-item>
          <el-descriptions-item label="连接时间">
            {{ formatTime(selectedClient.connectedAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="心跳间隔">
            {{ selectedClient.keepAlive }}s
          </el-descriptions-item>
        </el-descriptions>

        <!-- 订阅主题 -->
        <div class="subscriptions-section">
          <h4>订阅主题 ({{ (selectedClient.subscriptionList && selectedClient.subscriptionList.length) || 0 }})</h4>
          <el-table :data="selectedClient.subscriptionList" size="small" max-height="200">
            <el-table-column prop="topic" label="主题" />
            <el-table-column prop="qos" label="QoS" width="80" />
            <el-table-column prop="subscribedAt" label="订阅时间" width="160">
              <template slot-scope="scope">
                {{ formatTime(scope.row.subscribedAt) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import {
  getServerStatus,
  getRealTimeMetrics,
  getActiveClients,
  getClientDetail,
  disconnectClient as disconnectClientApi,
  getSystemAlerts,
  acknowledgeAlert as acknowledgeAlertApi,
  getHistoricalData
} from '@/api/emqx/monitoring'

export default {
  name: 'EmqxMonitoring',
  data() {
    return {
      loading: false,
      clientsLoading: false,
      autoRefresh: true,
      timeRange: '1h',
      clientSearch: '',
      
      // 服务器状态
      serverStatus: {
        status: 'running',
        uptime: 0,
        version: '5.0.0'
      },
      
      // 实时指标
      metrics: {
        connections: 0,
        maxConnections: 1000,
        messagesPerSecond: 0,
        totalMessages: 0,
        topics: 0,
        subscriptions: 0
      },
      
      // 客户端列表
      clients: [],
      
      // 告警列表
      alerts: [],
      
      // 对话框
      clientDetailVisible: false,
      selectedClient: null,
      
      // 图表实例
      connectionsChart: null,
      messagesChart: null,
      resourceChart: null,
      topicsChart: null,
      
      // 定时器
      refreshTimer: null
    }
  },
  
  computed: {
    filteredClients() {
      if (!this.clientSearch) {
        return this.clients
      }
      return this.clients.filter(client =>
        client.clientId.toLowerCase().includes(this.clientSearch.toLowerCase())
      )
    }
  },
  
  created() {
    this.loadData()
    this.loadAlerts()
  },
  
  mounted() {
    this.$nextTick(() => {
      this.initCharts()
      this.toggleAutoRefresh()
    })
  },
  
  beforeDestroy() {
    this.stopAutoRefresh()
    this.destroyCharts()
  },
  
  methods: {
    async loadData() {
      this.loading = true
      try {
        await Promise.all([
          this.loadServerStatus(),
          this.loadMetrics(),
          this.loadClients()
        ])
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },
    
    async loadServerStatus() {
      try {
        const response = await getServerStatus()
        if (response.code === 200) {
          this.serverStatus = response.data
        }
      } catch (error) {
        console.error('加载服务器状态失败:', error)
      }
    },

    async loadMetrics() {
      try {
        const response = await getRealTimeMetrics()
        if (response.code === 200) {
          this.metrics = response.data
        }
      } catch (error) {
        console.error('加载实时指标失败:', error)
      }
    },
    
    async loadClients() {
      this.clientsLoading = true
      try {
        const response = await getActiveClients({
          search: this.clientSearch
        })
        if (response.code === 200) {
          this.clients = response.data || []
        }
      } catch (error) {
        console.error('加载客户端列表失败:', error)
      } finally {
        this.clientsLoading = false
      }
    },
    
    async loadAlerts() {
      try {
        const response = await getSystemAlerts()
        if (response.code === 200) {
          this.alerts = response.data || []
        }
      } catch (error) {
        console.error('加载系统告警失败:', error)
      }
    },
    
    refreshData() {
      this.loadData()
      this.updateCharts()
    },
    
    toggleAutoRefresh() {
      if (this.autoRefresh) {
        this.startAutoRefresh()
      } else {
        this.stopAutoRefresh()
      }
    },
    
    startAutoRefresh() {
      this.refreshTimer = setInterval(() => {
        this.loadData()
        this.updateCharts()
      }, 5000) // 5秒刷新一次
    },
    
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },
    
    viewClientDetail(client) {
      this.selectedClient = client
      this.clientDetailVisible = true
    },
    
    async disconnectClient(client) {
      this.$confirm(`确定要断开客户端 ${client.clientId} 的连接吗？`, '确认操作', {
        type: 'warning'
      }).then(async () => {
        try {
          const response = await disconnectClientApi(client.clientId)
          if (response.code === 200) {
            this.$message.success('客户端连接已断开')
            this.loadClients()
          } else {
            this.$message.error('断开连接失败: ' + response.msg)
          }
        } catch (error) {
          this.$message.error('断开连接失败')
        }
      })
    },

    async acknowledgeAlert(alert) {
      try {
        const response = await acknowledgeAlertApi(alert.id)
        if (response.code === 200) {
          this.alerts = this.alerts.filter(a => a.id !== alert.id)
          this.$message.success('告警已确认')
        } else {
          this.$message.error('确认告警失败: ' + response.msg)
        }
      } catch (error) {
        this.$message.error('确认告警失败')
      }
    },
    
    // 图表相关方法
    initCharts() {
      this.initConnectionsChart()
      this.initMessagesChart()
      this.initResourceChart()
      this.initTopicsChart()
    },
    
    initConnectionsChart() {
      const chartDom = document.getElementById('connectionsChart')
      if (!chartDom) return
      
      this.connectionsChart = echarts.init(chartDom)
      this.updateConnectionsChart()
    },
    
    initMessagesChart() {
      const chartDom = document.getElementById('messagesChart')
      if (!chartDom) return
      
      this.messagesChart = echarts.init(chartDom)
      this.updateMessagesChart()
    },
    
    initResourceChart() {
      const chartDom = document.getElementById('resourceChart')
      if (!chartDom) return
      
      this.resourceChart = echarts.init(chartDom)
      this.updateResourceChart()
    },
    
    initTopicsChart() {
      const chartDom = document.getElementById('topicsChart')
      if (!chartDom) return
      
      this.topicsChart = echarts.init(chartDom)
      this.updateTopicsChart()
    },
    
    updateCharts() {
      this.updateConnectionsChart()
      this.updateMessagesChart()
      this.updateResourceChart()
      this.updateTopicsChart()
    },
    
    updateConnectionsChart() {
      if (!this.connectionsChart) return
      
      // 生成模拟时间序列数据
      const times = []
      const values = []
      const now = new Date()
      const minutes = this.timeRange === '5m' ? 5 : this.timeRange === '1h' ? 60 : 360
      
      for (let i = minutes; i >= 0; i--) {
        const time = new Date(now.getTime() - i * 60 * 1000)
        times.push(time.getHours() + ':' + String(time.getMinutes()).padStart(2, '0'))
        values.push(Math.floor(Math.random() * 100) + this.metrics.connections - 50)
      }
      
      const option = {
        tooltip: { trigger: 'axis' },
        xAxis: { type: 'category', data: times },
        yAxis: { type: 'value', name: '连接数' },
        series: [{
          name: '连接数',
          type: 'line',
          smooth: true,
          data: values,
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(64, 158, 255, 0.8)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
            ])
          }
        }]
      }
      
      this.connectionsChart.setOption(option)
    },
    
    updateMessagesChart() {
      if (!this.messagesChart) return
      
      const option = {
        tooltip: { trigger: 'item' },
        legend: { orient: 'vertical', left: 'left' },
        series: [{
          name: '消息类型',
          type: 'pie',
          radius: '50%',
          data: [
            { value: 35, name: 'PUBLISH' },
            { value: 25, name: 'SUBSCRIBE' },
            { value: 20, name: 'CONNECT' },
            { value: 15, name: 'DISCONNECT' },
            { value: 5, name: 'PINGREQ' }
          ]
        }]
      }
      
      this.messagesChart.setOption(option)
    },
    
    updateResourceChart() {
      if (!this.resourceChart) return
      
      const option = {
        tooltip: { trigger: 'axis' },
        legend: { data: ['CPU使用率', '内存使用率'] },
        xAxis: { type: 'category', data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'] },
        yAxis: { type: 'value', max: 100, name: '使用率(%)' },
        series: [
          {
            name: 'CPU使用率',
            type: 'line',
            data: [20, 25, 30, 45, 35, 28]
          },
          {
            name: '内存使用率',
            type: 'line',
            data: [60, 65, 70, 75, 72, 68]
          }
        ]
      }
      
      this.resourceChart.setOption(option)
    },
    
    updateTopicsChart() {
      if (!this.topicsChart) return
      
      const option = {
        tooltip: { trigger: 'axis' },
        xAxis: { type: 'category', data: ['系统主题', '设备主题', '业务主题', '监控主题'] },
        yAxis: { type: 'value', name: '活跃度' },
        series: [{
          name: '主题活跃度',
          type: 'bar',
          data: [85, 92, 78, 65],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#83bff6' },
              { offset: 1, color: '#188df0' }
            ])
          }
        }]
      }
      
      this.topicsChart.setOption(option)
    },
    
    destroyCharts() {
      if (this.connectionsChart) {
        this.connectionsChart.dispose()
        this.connectionsChart = null
      }
      if (this.messagesChart) {
        this.messagesChart.dispose()
        this.messagesChart = null
      }
      if (this.resourceChart) {
        this.resourceChart.dispose()
        this.resourceChart = null
      }
      if (this.topicsChart) {
        this.topicsChart.dispose()
        this.topicsChart = null
      }
    },
    
    // 工具方法
    getServerStatusClass() {
      return this.serverStatus.status === 'running' ? 'success' : 'danger'
    },
    
    getAlertClass(level) {
      return `alert-${level}`
    },
    
    getAlertIcon(level) {
      const iconMap = {
        'info': 'el-icon-info',
        'warning': 'el-icon-warning',
        'error': 'el-icon-error'
      }
      return iconMap[level] || 'el-icon-info'
    },
    
    formatNumber(num) {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return (num && num.toString()) || '0'
    },
    
    formatUptime(uptime) {
      const now = Date.now()
      const diff = now - uptime
      const days = Math.floor(diff / (24 * 60 * 60 * 1000))
      const hours = Math.floor((diff % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000))
      return `${days}天${hours}小时`
    },
    
    formatTime(time) {
      if (!time) return '-'
      return new Date(time).toLocaleString()
    }
  }
}
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title h2 {
  margin: 0;
  color: #303133;
}

.page-title p {
  margin: 5px 0 0 0;
  color: #909399;
  font-size: 14px;
}

.status-cards {
  margin-bottom: 20px;
}

.status-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.status-content {
  display: flex;
  align-items: center;
}

.status-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  margin-right: 15px;
}

.status-icon.server.success { background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%); }
.status-icon.server.danger { background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%); }
.status-icon.connections { background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%); }
.status-icon.messages { background: linear-gradient(135deg, #e6a23c 0%, #ebb563 100%); }
.status-icon.topics { background: linear-gradient(135deg, #909399 0%, #a6a9ad 100%); }

.status-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.status-label {
  font-size: 14px;
  color: #909399;
  margin: 5px 0;
}

.status-detail {
  font-size: 12px;
  color: #c0c4cc;
}

.chart-card, .clients-card, .alerts-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.client-id {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.alerts-list {
  max-height: 300px;
  overflow-y: auto;
}

.alert-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-left: 4px solid;
  margin-bottom: 10px;
  background: #f8f9fa;
  border-radius: 4px;
}

.alert-item.alert-info { border-left-color: #409eff; }
.alert-item.alert-warning { border-left-color: #e6a23c; }
.alert-item.alert-error { border-left-color: #f56c6c; }

.alert-icon {
  margin-right: 15px;
  font-size: 20px;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 5px;
}

.alert-message {
  color: #606266;
  font-size: 14px;
  margin-bottom: 5px;
}

.alert-time {
  color: #909399;
  font-size: 12px;
}

.client-detail {
  padding: 10px 0;
}

.subscriptions-section {
  margin-top: 20px;
}

.subscriptions-section h4 {
  margin-bottom: 15px;
  color: #303133;
}
</style>
