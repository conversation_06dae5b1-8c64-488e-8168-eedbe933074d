import request from '@/utils/request'

// 获取节点列表
export function getNodes() {
  return request({
    url: '/emqx/manage/nodes',
    method: 'get'
  })
}

// 获取节点详情
export function getNodeInfo(node) {
  return request({
    url: '/emqx/manage/nodes/' + node,
    method: 'get'
  })
}

// 获取系统信息
export function getSystemInfo() {
  return request({
    url: '/emqx/manage/system_info',
    method: 'get'
  })
}

// 获取客户端列表
export function getClients(query) {
  return request({
    url: '/emqx/manage/clients',
    method: 'get',
    params: query
  })
}

// 获取客户端详情
export function getClientInfo(clientId) {
  return request({
    url: '/emqx/manage/clients/' + clientId,
    method: 'get'
  })
}

// 断开客户端连接
export function disconnectClient(clientId) {
  return request({
    url: '/emqx/manage/clients/' + clientId,
    method: 'delete'
  })
}

// 获取订阅列表
export function getSubscriptions(query) {
  return request({
    url: '/emqx/manage/subscriptions',
    method: 'get',
    params: query
  })
}

// 发布消息
export function publishMessage(data) {
  return request({
    url: '/emqx/manage/publish',
    method: 'post',
    data: data
  })
}

// 获取统计信息
export function getStats() {
  return request({
    url: '/emqx/manage/stats',
    method: 'get'
  })
}

// 获取监控指标
export function getMetrics() {
  return request({
    url: '/emqx/manage/metrics',
    method: 'get'
  })
}

// 获取告警信息
export function getAlarms() {
  return request({
    url: '/emqx/manage/alarms',
    method: 'get'
  })
}

// 获取规则列表
export function getRules() {
  return request({
    url: '/emqx/manage/rules',
    method: 'get'
  })
}

// 创建规则
export function createRule(data) {
  return request({
    url: '/emqx/manage/rules',
    method: 'post',
    data: data
  })
}

// 删除规则
export function deleteRule(ruleId) {
  return request({
    url: '/emqx/manage/rules/' + ruleId,
    method: 'delete'
  })
}

// 创建WebHook
export function createWebhook(data) {
  return request({
    url: '/emqx/manage/webhooks',
    method: 'post',
    data: data
  })
} 