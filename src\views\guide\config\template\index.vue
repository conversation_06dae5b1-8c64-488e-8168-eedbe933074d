<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="模板名称" prop="templateName">
        <el-input
          v-model="queryParams.templateName"
          placeholder="请输入模板名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="模板编码" prop="templateCode">
        <el-input
          v-model="queryParams.templateCode"
          placeholder="请输入模板编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="模板类型" prop="templateType">
        <el-select v-model="queryParams.templateType" placeholder="请选择模板类型" clearable>
          <el-option label="系统模板" value="system" />
          <el-option label="自定义模板" value="custom" />
        </el-select>
      </el-form-item>
      <el-form-item label="导寻方式" prop="guideMethod">
        <el-select v-model="queryParams.guideMethod" placeholder="请选择导寻方式" clearable>
          <el-option
            v-for="dict in dict.type.guide_method_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="启用" value="1" />
          <el-option label="禁用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['guide:config:template:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['guide:config:template:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['guide:config:template:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['guide:config:template:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="templateList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="模板ID" align="center" prop="templateId" />
      <el-table-column label="模板名称" align="center" prop="templateName" />
      <el-table-column label="模板编码" align="center" prop="templateCode" />
      <el-table-column label="模板类型" align="center" prop="templateType">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.templateType === 'system'" type="info">系统模板</el-tag>
          <el-tag v-else type="primary">自定义模板</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="导寻方式" align="center" prop="guideMethod">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.guide_method_type" :value="scope.row.guideMethod"/>
        </template>
      </el-table-column>
      <el-table-column label="优先级" align="center" prop="priority" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            active-value="1"
            inactive-value="0"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="默认模板" align="center" prop="isDefault">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isDefault === '1'" type="success">是</el-tag>
          <el-tag v-else type="info">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['guide:config:template:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-copy-document"
            @click="handleCopy(scope.row)"
            v-hasPermi="['guide:config:template:add']"
          >复制</el-button>
          <el-button
            v-if="scope.row.isDefault !== '1'"
            size="mini"
            type="text"
            icon="el-icon-star-off"
            @click="handleSetDefault(scope.row)"
            v-hasPermi="['guide:config:template:edit']"
          >设为默认</el-button>
          <el-button
            v-if="scope.row.templateType !== 'system'"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['guide:config:template:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改导寻配置模板对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="模板名称" prop="templateName">
              <el-input v-model="form.templateName" placeholder="请输入模板名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="模板编码" prop="templateCode">
              <el-input v-model="form.templateCode" placeholder="请输入模板编码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="导寻方式" prop="guideMethod">
              <el-select v-model="form.guideMethod" placeholder="请选择导寻方式">
                <el-option
                  v-for="dict in dict.type.guide_method_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-input-number v-model="form.priority" :min="1" :max="10" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="模板描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入模板描述" />
        </el-form-item>
        
        <!-- 语音配置 -->
        <el-divider content-position="left">语音配置</el-divider>
        <el-row>
          <el-col :span="8">
            <el-form-item label="启用语音" prop="voiceEnabled">
              <el-switch v-model="form.voiceEnabled" active-value="1" inactive-value="0" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="语音类型" prop="voiceType">
              <el-select v-model="form.voiceType" placeholder="请选择语音类型">
                <el-option
                  v-for="dict in dict.type.guide_voice_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="语音速度" prop="voiceSpeed">
              <el-input-number v-model="form.voiceSpeed" :min="1" :max="10" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="语音音量" prop="voiceVolume">
              <el-input-number v-model="form.voiceVolume" :min="1" :max="10" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="语音模板" prop="voiceTemplate">
          <el-input v-model="form.voiceTemplate" type="textarea" :rows="3" placeholder="请输入语音模板内容，支持变量：${materialCode}、${locationNames}" />
        </el-form-item>
        
        <!-- 灯光配置 -->
        <el-divider content-position="left">灯光配置</el-divider>
        <el-row>
          <el-col :span="6">
            <el-form-item label="启用灯光" prop="lightEnabled">
              <el-switch v-model="form.lightEnabled" active-value="1" inactive-value="0" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="灯光颜色" prop="lightColor">
              <el-select v-model="form.lightColor" placeholder="请选择灯光颜色">
                <el-option
                  v-for="dict in dict.type.guide_light_color"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="灯光亮度" prop="lightBrightness">
              <el-input-number v-model="form.lightBrightness" :min="1" :max="100" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="灯光模式" prop="lightMode">
              <el-select v-model="form.lightMode" placeholder="请选择灯光模式">
                <el-option
                  v-for="dict in dict.type.guide_light_mode"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="持续时间(毫秒)" prop="lightDuration">
              <el-input-number v-model="form.lightDuration" :min="1000" :max="60000" :step="1000" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 复制模板对话框 -->
    <el-dialog title="复制模板" :visible.sync="copyOpen" width="500px" append-to-body>
      <el-form ref="copyForm" :model="copyForm" :rules="copyRules" label-width="120px">
        <el-form-item label="新模板名称" prop="templateName">
          <el-input v-model="copyForm.templateName" placeholder="请输入新模板名称" />
        </el-form-item>
        <el-form-item label="新模板编码" prop="templateCode">
          <el-input v-model="copyForm.templateCode" placeholder="请输入新模板编码" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitCopy">确 定</el-button>
        <el-button @click="cancelCopy">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTemplate, getTemplate, delTemplate, addTemplate, updateTemplate, setDefaultTemplate, copyTemplate, checkTemplateCodeUnique } from "@/api/guide/config";

export default {
  name: "GuideConfigTemplate",
  dicts: ['guide_method_type', 'guide_voice_type', 'guide_light_color', 'guide_light_mode'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 导寻配置模板表格数据
      templateList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示复制弹出层
      copyOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        templateName: null,
        templateCode: null,
        templateType: null,
        guideMethod: null,
        status: null
      },
      // 表单参数
      form: {},
      // 复制表单参数
      copyForm: {},
      // 当前复制的模板ID
      copyTemplateId: null,
      // 表单校验
      rules: {
        templateName: [
          { required: true, message: "模板名称不能为空", trigger: "blur" }
        ],
        templateCode: [
          { required: true, message: "模板编码不能为空", trigger: "blur" },
          { validator: this.validateTemplateCode, trigger: "blur" }
        ],
        guideMethod: [
          { required: true, message: "导寻方式不能为空", trigger: "change" }
        ]
      },
      // 复制表单校验
      copyRules: {
        templateName: [
          { required: true, message: "新模板名称不能为空", trigger: "blur" }
        ],
        templateCode: [
          { required: true, message: "新模板编码不能为空", trigger: "blur" },
          { validator: this.validateCopyTemplateCode, trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询导寻配置模板列表 */
    getList() {
      this.loading = true;
      listTemplate(this.queryParams).then(response => {
        this.templateList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        templateId: null,
        templateName: null,
        templateCode: null,
        templateType: "custom",
        description: null,
        guideMethod: "voice_light",
        voiceEnabled: "1",
        voiceTemplate: "请注意，物料编码 ${materialCode} 位于 ${locationNames} 位置，请及时处理。",
        voiceType: "female",
        voiceSpeed: 5,
        voiceVolume: 8,
        lightEnabled: "1",
        lightColor: "red",
        lightBrightness: 80,
        lightMode: "blink",
        lightDuration: 5000,
        priority: 5,
        status: "1",
        isDefault: "0",
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.templateId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加导寻配置模板";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const templateId = row.templateId || this.ids
      getTemplate(templateId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改导寻配置模板";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.templateId != null) {
            updateTemplate(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTemplate(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const templateIds = row.templateId || this.ids;
      this.$modal.confirm('是否确认删除导寻配置模板编号为"' + templateIds + '"的数据项？').then(function() {
        return delTemplate(templateIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('guide/config/template/export', {
        ...this.queryParams
      }, `template_${new Date().getTime()}.xlsx`)
    },
    /** 状态修改 */
    handleStatusChange(row) {
      let text = row.status === "1" ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '""' + row.templateName + '"模板吗？').then(function() {
        return updateTemplate(row);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function() {
        row.status = row.status === "0" ? "1" : "0";
      });
    },
    /** 设置默认模板 */
    handleSetDefault(row) {
      this.$modal.confirm('确认要将"' + row.templateName + '"设置为默认模板吗？').then(function() {
        return setDefaultTemplate(row.templateId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("设置成功");
      }).catch(() => {});
    },
    /** 复制模板 */
    handleCopy(row) {
      this.copyTemplateId = row.templateId;
      this.copyForm = {
        templateName: row.templateName + "_副本",
        templateCode: row.templateCode + "_COPY"
      };
      this.copyOpen = true;
    },
    /** 提交复制 */
    submitCopy() {
      this.$refs["copyForm"].validate(valid => {
        if (valid) {
          copyTemplate(this.copyTemplateId, this.copyForm).then(response => {
            this.$modal.msgSuccess("复制成功");
            this.copyOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 取消复制 */
    cancelCopy() {
      this.copyOpen = false;
      this.copyForm = {};
      this.copyTemplateId = null;
    },
    /** 校验模板编码 */
    validateTemplateCode(rule, value, callback) {
      if (value) {
        checkTemplateCodeUnique(value, this.form.templateId).then(response => {
          if (response.data) {
            callback();
          } else {
            callback(new Error("模板编码已存在"));
          }
        });
      } else {
        callback();
      }
    },
    /** 校验复制模板编码 */
    validateCopyTemplateCode(rule, value, callback) {
      if (value) {
        checkTemplateCodeUnique(value, null).then(response => {
          if (response.data) {
            callback();
          } else {
            callback(new Error("模板编码已存在"));
          }
        });
      } else {
        callback();
      }
    }
  }
};
</script>
