<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span style="font-size: 18px; font-weight: bold;">
          <i class="el-icon-printer"></i> 单据打印
        </span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="handleRefresh">
          <i class="el-icon-refresh"></i> 刷新
        </el-button>
      </div>

      <!-- 打印设置区域 -->
      <el-row :gutter="20">
        <!-- 左侧：打印设置 -->
        <el-col :span="8" :md="8" :sm="24" :xs="24" class="print-settings-col">
          <el-card shadow="never" style="height: auto; min-height: 600px;">
            <div slot="header">
              <i class="el-icon-setting"></i> 打印设置
            </div>
            
            <el-form :model="printForm" label-width="100px" size="small" style="padding-bottom: 20px;">
              <!-- 模板选择 -->
              <el-form-item label="打印模板" required>
                <el-select 
                  v-model="printForm.templateId" 
                  placeholder="请选择打印模板"
                  style="width: 100%"
                  @change="handleTemplateChange"
                >
                  <el-option
                    v-for="template in templateList"
                    :key="template.id"
                    :label="template.templateName"
                    :value="template.id"
                  >
                    <span style="float: left">{{ template.templateName }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">
                      {{ template.isDefault === '1' ? '默认' : '' }}
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>

              <!-- 单据编号输入 -->
              <el-form-item label="单据编号" required>
                <el-input
                  v-model="printForm.billNo"
                  placeholder="请输入单据编号或物料清单编号，如：650824098693120 或 M633223861530704832"
                  @keyup.enter.native="handleSearch"
                >
                  <el-button slot="append" icon="el-icon-search" @click="handleSearch">查询</el-button>
                </el-input>
              </el-form-item>

              <!-- 单据信息显示 -->
              <el-form-item label="单据信息" v-if="billInfo">
                <div class="bill-info-simple">
                  <p><strong>{{ billInfo.applicantName }}</strong> | {{ getBusinessTypeText(billInfo.businessType) }}</p>
                  <p>
                    <el-tag :type="getApprovalStatusType(billInfo.approvalStatus)" size="mini">
                      {{ getApprovalStatusText(billInfo.approvalStatus) }}
                    </el-tag>
                  </p>
                </div>
              </el-form-item>



              <!-- 操作按钮 -->
              <el-form-item style="margin-bottom: 0;">
                <div style="display: flex; flex-direction: column; gap: 10px;">
                  <el-button
                    type="primary"
                    icon="el-icon-view"
                    @click="handlePreview"
                    :disabled="!canPrint"
                    :loading="previewLoading"
                    style="width: 100%;"
                  >{{ previewLoading ? '预览中...' : '预览' }}</el-button>
                  <el-button
                    type="success"
                    icon="el-icon-printer"
                    @click="handlePrint"
                    :disabled="!canPrint"
                    :loading="printLoading"
                    style="width: 100%;"
                  >{{ printLoading ? '打印中...' : '打印' }}</el-button>
                </div>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>

        <!-- 右侧：预览区域 -->
        <el-col :span="16" :md="16" :sm="24" :xs="24" class="print-preview-col">
          <el-card shadow="never" style="height: auto; min-height: 500px;">
            <div slot="header">
              <i class="el-icon-view"></i> 打印预览
            </div>

            <div class="preview-container" v-loading="previewLoading" style="min-height: 500px; overflow-y: auto; background: #f5f5f5; padding: 20px; display: flex; justify-content: center; align-items: flex-start;">
              <div class="preview-page" :style="pageStyle" :data-mode="printForm.pageSizeMode" v-if="previewContent">
                <div v-html="previewContent" class="preview-content"></div>
              </div>
              <div v-else class="preview-placeholder" style="text-align: center; margin-top: 100px;">
                <i class="el-icon-document" style="font-size: 48px; color: #ccc;"></i>
                <p style="color: #999; margin-top: 20px;">请选择模板和输入单据编号后查看预览</p>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>

    <!-- 打印历史记录 -->
    <el-card class="box-card" style="margin-top: 20px;">
      <div slot="header">
        <i class="el-icon-time"></i> 打印历史
      </div>
      
      <el-table :data="printHistory" size="small" max-height="200">
        <el-table-column prop="billNo" label="单据编号" width="150"></el-table-column>
        <el-table-column prop="templateName" label="使用模板" width="120"></el-table-column>
        <el-table-column prop="printTime" label="打印时间" width="160"></el-table-column>
        <el-table-column prop="copies" label="打印份数" width="80" align="center"></el-table-column>
        <el-table-column prop="operator" label="操作人" width="100"></el-table-column>
        <el-table-column label="操作" width="100" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="handleRepeat(scope.row)">重新打印</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>


  </div>
</template>

<script>
import { listPrintTemplate } from "@/api/inout/printTemplate"
import { getInoutByBillNo, getInoutByMaterialBillCode } from "@/api/inout/inout"
import { getInoutPrintPreview, printInoutDocument, recordPrintHistory, getPrinters, getPrinterInfo, testPrinter } from "@/api/inout/print"

export default {
  name: "InoutPrint",
  data() {
    return {
      // 打印表单
      printForm: {
        templateId: null,
        billNo: '',
        copies: 1,
        paperSize: 'A4',
        orientation: 'portrait',
        printerName: 'default',
        quality: 'normal',
        silentPrint: false,
        autoClose: true,
        showPreview: true,
        layout: 'center',
        scale: 100,
        pageSizeMode: 'fixed' // 'fixed' 固定尺寸, 'adaptive' 自适应
      },
      // 模板列表
      templateList: [],
      // 打印机列表
      printerList: [],
      // 单据信息
      billInfo: null,
      // 预览内容
      previewContent: '',
      // 预览加载状态
      previewLoading: false,
      // 打印加载状态
      printLoading: false,
      // 缩放比例
      zoomLevel: 1,
      // 打印历史
      printHistory: [],
      // 简化的打印设置
      printSettings: {
        marginTop: 10,
        marginRight: 10,
        marginBottom: 10,
        marginLeft: 10,
        fontSize: 12,
        fontFamily: 'Microsoft YaHei',
        printBackground: true,
        colorMode: 'color'
      }
    };
  },
  computed: {
    // 是否可以打印
    canPrint() {
      const hasTemplate = !!this.printForm.templateId;
      const hasBillNo = !!this.printForm.billNo && this.printForm.billNo.trim() !== '';
      const hasBillInfo = !!this.billInfo;
      const result = hasTemplate && hasBillNo && hasBillInfo;

      console.log('canPrint 计算:', {
        templateId: this.printForm.templateId,
        hasTemplate: hasTemplate,
        billNo: this.printForm.billNo,
        hasBillNo: hasBillNo,
        billInfo: !!this.billInfo,
        hasBillInfo: hasBillInfo,
        result: result
      });
      return result;
    },
    // 页面样式
    pageStyle() {
      return this.getLayoutStyles();
    },

    // 获取页面信息文本
    pageInfoText() {
      const { width, height } = this.getPageDimensions();
      const widthMM = Math.round(width * 25.4 / 96);
      const heightMM = Math.round(height * 25.4 / 96);
      return `${this.printForm.paperSize} (${widthMM}×${heightMM}mm)`;
    }
  },

  watch: {
    // 监听缩放比例变化
    'printForm.scale'(newVal) {
      this.zoomLevel = newVal / 100;
    },

    // 监听版式变化
    'printForm.layout'() {
      this.$nextTick(() => {
        this.applyPreviewStyles();
      });
    },

    // 监听页面大小模式变化
    'printForm.pageSizeMode'() {
      // 重新优化模板设置
      this.optimizeTemplateForFullWidth();

      this.$nextTick(() => {
        this.applyPreviewStyles();
        this.adjustPreviewSize();
        // 如果有预览内容，重新加载以应用新设置
        if (this.previewContent && this.billInfo) {
          this.loadPreview();
        }
      });
    },

    // 监听纸张大小和方向变化
    'printForm.paperSize'() {
      this.$nextTick(() => {
        this.applyPreviewStyles();
      });
    },

    'printForm.orientation'() {
      this.$nextTick(() => {
        this.applyPreviewStyles();
      });
    }
  },

  created() {
    this.loadTemplates();
    this.loadPrintHistory();
  },
  methods: {
    // 加载模板列表
    async loadTemplates() {
      try {
        const response = await listPrintTemplate({
          templateType: 'inout',
          status: '0'
        });
        this.templateList = response.rows || [];
        
        // 自动选择默认模板
        const defaultTemplate = this.templateList.find(t => t.isDefault === '1' || t.isDefault === 1);
        if (defaultTemplate) {
          this.printForm.templateId = defaultTemplate.id;
          console.log('自动选择默认模板:', defaultTemplate.templateName, 'ID:', defaultTemplate.id);
        } else if (this.templateList.length > 0) {
          // 如果没有默认模板，选择第一个
          this.printForm.templateId = this.templateList[0].id;
          console.log('选择第一个模板:', this.templateList[0].templateName, 'ID:', this.templateList[0].id);
        }
      } catch (error) {
        this.$message.error('加载模板列表失败：' + error.message);
      }
    },

    // 模板变化处理
    handleTemplateChange() {
      // 优化模板设置以充分利用纸张
      this.optimizeTemplateForFullWidth();

      if (this.billInfo) {
        this.loadPreview();
      }
    },

    // 优化模板设置以充分利用纸张宽度
    optimizeTemplateForFullWidth() {
      const template = this.templateList.find(t => t.id === this.printForm.templateId);
      if (template) {
        // 动态调整模板边距以充分利用纸张
        if (this.printForm.pageSizeMode === 'fullpage') {
          // 充分利用模式：最小边距
          template.marginTop = 5;
          template.marginBottom = 5;
          template.marginLeft = 5;
          template.marginRight = 5;
          template.fontSize = 10;
          template.lineHeight = 1.1;
        } else {
          // 标准模式：适中边距
          template.marginTop = Math.min(template.marginTop || 20, 12);
          template.marginBottom = Math.min(template.marginBottom || 20, 12);
          template.marginLeft = Math.min(template.marginLeft || 20, 12);
          template.marginRight = Math.min(template.marginRight || 20, 12);
        }

        // 优化CSS样式，移除宽度限制
        if (template.cssStyles) {
          template.cssStyles = template.cssStyles
            .replace(/max-width:\s*\d+mm/g, 'max-width: none')
            .replace(/width:\s*210mm/g, 'width: 100%')
            + `
            /* 充分利用纸张宽度的优化样式 */
            table { width: 100% !important; max-width: none !important; table-layout: auto; }
            body { max-width: none !important; width: 100% !important; }
            .preview-content { max-width: none !important; width: 100% !important; }
            `;
        }

        console.log('模板优化完成:', {
          templateId: template.id,
          margins: {
            top: template.marginTop,
            bottom: template.marginBottom,
            left: template.marginLeft,
            right: template.marginRight
          },
          fontSize: template.fontSize,
          pageSizeMode: this.printForm.pageSizeMode
        });
      }
    },

    // 查询单据
    async handleSearch() {
      if (!this.printForm.billNo.trim()) {
        this.$message.warning('请输入单据编号或物料清单编号');
        return;
      }

      try {
        let response;
        const inputValue = this.printForm.billNo.trim();

        // 智能识别输入类型：以M开头的认为是物料清单编号，否则认为是单据编号
        if (inputValue.startsWith('M')) {
          // 物料清单编号查询
          response = await getInoutByMaterialBillCode(inputValue);
        } else {
          // 单据编号查询
          response = await getInoutByBillNo(inputValue);
        }

        if (response.code === 200 && response.data) {
          this.billInfo = response.data;
          this.$message.success('单据查询成功');

          console.log('查询成功后状态:', {
            billNo: this.printForm.billNo,
            templateId: this.printForm.templateId,
            billInfo: !!this.billInfo,
            templateListLength: this.templateList.length
          });

          // 确保模板已选择，然后自动加载预览
          if (!this.printForm.templateId && this.templateList.length > 0) {
            const defaultTemplate = this.templateList.find(t => t.isDefault === '1' || t.isDefault === 1);
            if (defaultTemplate) {
              this.printForm.templateId = defaultTemplate.id;
              console.log('设置默认模板:', defaultTemplate.templateName, 'ID:', defaultTemplate.id);
            } else {
              this.printForm.templateId = this.templateList[0].id;
              console.log('设置第一个模板:', this.templateList[0].templateName, 'ID:', this.templateList[0].id);
            }
          }

          // 使用 $nextTick 确保状态更新完成后再加载预览
          this.$nextTick(() => {
            console.log('$nextTick 后状态:', {
              billNo: this.printForm.billNo,
              templateId: this.printForm.templateId,
              billInfo: !!this.billInfo,
              canPrint: this.canPrint
            });

            // 自动加载预览
            if (this.printForm.templateId) {
              console.log('开始加载预览，模板ID:', this.printForm.templateId, '单据ID:', this.billInfo.id);
              this.loadPreview();
            }
          });
        } else {
          this.$message.error('未找到该编号对应的记录');
          this.billInfo = null;
          this.previewContent = '';
        }
      } catch (error) {
        this.$message.error('查询单据失败：' + error.message);
        this.billInfo = null;
        this.previewContent = '';
      }
    },

    // 加载预览
    async loadPreview() {
      console.log('loadPreview 调用，canPrint:', this.canPrint);
      console.log('模板ID:', this.printForm.templateId, '单据编号:', this.printForm.billNo, '单据信息:', !!this.billInfo);

      if (!this.canPrint) {
        console.log('不满足打印条件，退出预览加载');
        return;
      }

      this.previewLoading = true;
      try {
        console.log('开始调用预览API，单据ID:', this.billInfo.id, '模板ID:', this.printForm.templateId);
        const response = await getInoutPrintPreview(this.billInfo.id, this.printForm.templateId);
        console.log('预览API响应:', response);
        console.log('响应类型:', typeof response);
        console.log('响应字段:', Object.keys(response));

        if (response.code === 200) {
          // 从日志看，HTML内容在msg字段中，这可能是后端的特殊处理
          let htmlContent = response.data;

          // 如果data字段为空或不是HTML，尝试从msg字段获取
          if (!htmlContent || !htmlContent.includes('<html>')) {
            htmlContent = response.msg;
          }

          // 优化HTML内容以充分利用宽度
          this.previewContent = this.optimizePreviewContentWidth(htmlContent);
          console.log('预览内容设置成功，长度:', this.previewContent ? this.previewContent.length : 0);
          console.log('HTML内容预览:', this.previewContent ? this.previewContent.substring(0, 200) + '...' : '无内容');

          // 应用版式设置
          this.$nextTick(() => {
            this.applyPreviewStyles();
          });
        } else {
          this.$message.error('生成预览失败：' + response.msg);
          console.error('预览失败:', response);
        }
      } catch (error) {
        this.$message.error('加载预览失败：' + error.message);
        console.error('预览加载异常:', error);
      } finally {
        this.previewLoading = false;
      }
    },

    // 优化预览内容宽度
    optimizePreviewContentWidth(htmlContent) {
      if (!htmlContent) return htmlContent;

      // 移除固定宽度限制的CSS
      let optimizedContent = htmlContent
        .replace(/max-width:\s*210mm/g, 'max-width: none')
        .replace(/width:\s*210mm/g, 'width: 100%')
        .replace(/max-width:\s*\d+mm/g, 'max-width: none');

      // 添加充分利用宽度的样式和强制边框样式
      const widthOptimizationCSS = `
        <style>
          /* 充分利用宽度优化样式 */
          body {
            max-width: none !important;
            width: 100% !important;
            margin: 0 !important;
            padding: ${this.printForm.pageSizeMode === 'fullpage' ? '5mm' : '10mm'} !important;
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
          }

          /* 强制表格边框显示 */
          table {
            width: 100% !important;
            max-width: none !important;
            table-layout: auto !important;
            margin: 5px 0 !important;
            border-collapse: collapse !important;
            border: 2px solid #333 !important;
          }

          th, td {
            padding: ${this.printForm.pageSizeMode === 'fullpage' ? '2px 3px' : '4px 6px'} !important;
            font-size: ${this.printForm.pageSizeMode === 'fullpage' ? '10px' : '11px'} !important;
            word-wrap: break-word !important;
            border: 1px solid #333 !important;
            text-align: left !important;
            vertical-align: top !important;
          }

          th {
            background-color: #f5f5f5 !important;
            font-weight: bold !important;
            text-align: center !important;
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
          }

          /* 强制其他边框元素显示 */
          .print-header {
            border-bottom: 2px solid #333 !important;
            padding-bottom: 15px !important;
            margin-bottom: 20px !important;
          }

          .print-footer {
            border-top: 1px solid #333 !important;
            padding-top: 10px !important;
            margin-top: 20px !important;
          }

          .section-title {
            border-bottom: 2px solid #3498db !important;
            padding-bottom: 5px !important;
            margin: 25px 0 10px 0 !important;
            font-weight: bold !important;
          }

          .signature-table {
            border: 2px solid #333 !important;
          }

          .signature-table td {
            border: 1px solid #333 !important;
            padding: 20px 10px !important;
          }

          .signature-line {
            border-bottom: 1px solid #333 !important;
            height: 30px !important;
            margin: 10px 0 !important;
          }

          .preview-content {
            max-width: none !important;
            width: 100% !important;
          }

          .print-header, .print-footer, .info-section, .signature-section {
            width: 100% !important;
            max-width: none !important;
          }

          /* 打印模式强制边框 */
          @media print {
            table, th, td {
              border: 1px solid #000 !important;
              -webkit-print-color-adjust: exact !important;
              color-adjust: exact !important;
            }
            .print-header {
              border-bottom: 2px solid #000 !important;
            }
            .print-footer {
              border-top: 1px solid #000 !important;
            }
            .section-title {
              border-bottom: 2px solid #000 !important;
            }
          }
        </style>
      `;

      // 在</head>前插入优化样式
      if (optimizedContent.includes('</head>')) {
        optimizedContent = optimizedContent.replace('</head>', widthOptimizationCSS + '</head>');
      } else {
        // 如果没有head标签，在开头添加
        optimizedContent = widthOptimizationCSS + optimizedContent;
      }

      return optimizedContent;
    },

    // 预览
    handlePreview() {
      if (!this.canPrint) {
        this.$message.warning('请先选择模板和查询单据');
        return;
      }
      
      if (this.previewContent) {
        const previewWindow = window.open('', '_blank', 'width=900,height=700');
        previewWindow.document.write(this.previewContent);
        previewWindow.document.close();
      } else {
        this.loadPreview();
      }
    },

    // 打印
    async handlePrint() {
      if (!this.canPrint) {
        this.$message.warning('请先选择模板和查询单据');
        return;
      }

      this.printLoading = true;
      try {
        // 调用后端打印接口
        const response = await printInoutDocument(this.billInfo.id, this.printForm.templateId);
        if (response.code === 200) {
          // 根据打印设置执行打印
          await this.executePrint(response.data);

          // 记录打印历史
          await this.recordPrint();

          this.$message.success('打印任务已发送');
        } else {
          this.$message.error('打印失败：' + response.msg);
        }
      } catch (error) {
        this.$message.error('打印失败：' + error.message);
      } finally {
        this.printLoading = false;
      }
    },

    // 执行打印
    async executePrint(printContent) {
      const content = this.previewContent || printContent;

      // 如果启用了静默打印，尝试使用高级打印API
      if (this.printForm.silentPrint && this.supportAdvancedPrint()) {
        await this.advancedPrint(content);
      } else {
        // 使用传统的浏览器打印
        await this.browserPrint(content);
      }
    },

    // 检查是否支持高级打印功能
    supportAdvancedPrint() {
      // 检查是否支持Web API的打印功能
      return 'print' in window && 'navigator' in window && 'mediaDevices' in navigator;
    },

    // 高级打印（支持更多选项）
    async advancedPrint(content) {
      try {
        // 创建打印样式
        const printStyles = this.generatePrintStyles();

        // 创建完整的打印文档
        const fullContent = `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <title>出入库单据打印</title>
            ${printStyles}
          </head>
          <body>
            ${content}
          </body>
          </html>
        `;

        // 根据打印份数循环打印
        for (let i = 0; i < this.printForm.copies; i++) {
          const printWindow = window.open('', '_blank');
          printWindow.document.write(fullContent);
          printWindow.document.close();

          // 等待文档加载完成
          await new Promise(resolve => {
            printWindow.onload = resolve;
            setTimeout(resolve, 1000); // 超时保护
          });

          // 设置打印选项
          if (printWindow.print) {
            printWindow.focus();
            printWindow.print();

            // 如果设置了自动关闭
            if (this.printForm.autoClose) {
              setTimeout(() => {
                printWindow.close();
              }, 2000);
            }
          }

          // 多份打印之间的延迟
          if (i < this.printForm.copies - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }
      } catch (error) {
        console.error('高级打印失败，回退到浏览器打印:', error);
        await this.browserPrint(content);
      }
    },

    // 传统浏览器打印
    async browserPrint(content) {
      const printStyles = this.generatePrintStyles();

      const fullContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>出入库单据打印</title>
          ${printStyles}
        </head>
        <body>
          ${content}
        </body>
        </html>
      `;

      // 根据打印份数循环打印
      for (let i = 0; i < this.printForm.copies; i++) {
        const printWindow = window.open('', '_blank');
        printWindow.document.write(fullContent);
        printWindow.document.close();
        printWindow.focus();
        printWindow.print();

        if (this.printForm.autoClose) {
          setTimeout(() => {
            printWindow.close();
          }, 3000);
        }

        // 多份打印之间的延迟
        if (i < this.printForm.copies - 1) {
          await new Promise(resolve => setTimeout(resolve, 1500));
        }
      }
    },

    // 生成打印样式
    generatePrintStyles() {
      const settings = this.printSettings;

      // 根据页面模式调整边距
      let margins;
      if (this.printForm.pageSizeMode === 'fullpage') {
        // 充分利用纸张模式：最小边距
        margins = '3mm 3mm 3mm 3mm';
      } else {
        // 标准模式：使用默认边距
        margins = `${settings.marginTop}mm ${settings.marginRight}mm ${settings.marginBottom}mm ${settings.marginLeft}mm`;
      }

      return `
        <style>
          @media print {
            @page {
              size: ${this.printForm.paperSize};
              orientation: ${this.printForm.orientation};
              margin: ${margins};
            }

            body {
              font-family: '${settings.fontFamily}', Arial, sans-serif;
              font-size: ${this.printForm.pageSizeMode === 'fullpage' ? Math.max(settings.fontSize - 2, 10) : settings.fontSize}px;
              line-height: ${this.printForm.pageSizeMode === 'fullpage' ? '1.2' : '1.4'};
              color: ${settings.colorMode === 'monochrome' ? '#000' : '#000'};
              background: ${settings.printBackground ? 'white' : 'transparent'};
              margin: 0;
              padding: 0;
              width: 100%;
              max-width: none; /* 移除宽度限制 */
            }

            .no-print {
              display: none !important;
            }

            table {
              border-collapse: collapse;
              width: 100% !important;
              max-width: none !important; /* 移除表格宽度限制 */
              page-break-inside: avoid;
              margin: 10px 0;
              table-layout: auto; /* 让表格自动调整列宽 */
            }

            th, td {
              border: 1px solid ${settings.colorMode === 'monochrome' ? '#000' : '#333'};
              padding: ${this.printForm.pageSizeMode === 'fullpage' ? '3px 5px' : '6px 8px'};
              text-align: left;
              font-size: ${this.printForm.pageSizeMode === 'fullpage' ? Math.max(settings.fontSize - 3, 9) : settings.fontSize}px;
              line-height: ${this.printForm.pageSizeMode === 'fullpage' ? '1.1' : '1.3'};
              word-wrap: break-word;
              overflow-wrap: break-word;
            }

            th {
              background-color: #f5f5f5;
              font-weight: bold;
              text-align: center;
            }

            .page-break {
              page-break-before: always;
            }

            .print-header {
              text-align: center;
              font-size: ${this.printForm.pageSizeMode === 'fullpage' ? settings.fontSize + 2 : settings.fontSize + 6}px;
              font-weight: bold;
              margin-bottom: ${this.printForm.pageSizeMode === 'fullpage' ? '10px' : '20px'};
            }

            h1, h2, h3 {
              margin: ${this.printForm.pageSizeMode === 'fullpage' ? '5px 0' : '10px 0'} !important;
              font-size: ${this.printForm.pageSizeMode === 'fullpage' ? settings.fontSize + 1 : settings.fontSize + 4}px !important;
              line-height: ${this.printForm.pageSizeMode === 'fullpage' ? '1.1' : '1.3'} !important;
            }

            p, div {
              margin: ${this.printForm.pageSizeMode === 'fullpage' ? '2px 0' : '5px 0'} !important;
              font-size: ${this.printForm.pageSizeMode === 'fullpage' ? Math.max(settings.fontSize - 1, 10) : settings.fontSize}px !important;
              line-height: ${this.printForm.pageSizeMode === 'fullpage' ? '1.2' : '1.4'} !important;
            }

            .print-footer {
              ${settings.printHeaders ? '' : 'display: none;'}
              position: fixed;
              bottom: 0;
              width: 100%;
              text-align: center;
              font-size: ${settings.fontSize - 2}px;
              color: #666;
            }

            ${settings.customCSS}
          }

          @media screen {
            body {
              font-family: '${settings.fontFamily}', Arial, sans-serif;
              padding: 20px;
            }
          }
        </style>
      `;
    },



    // 增加缩放比例
    increaseScale() {
      if (this.printForm.scale < 200) {
        this.printForm.scale += 10;
        this.updatePreviewScale();
      }
    },

    // 减少缩放比例
    decreaseScale() {
      if (this.printForm.scale > 50) {
        this.printForm.scale -= 10;
        this.updatePreviewScale();
      }
    },

    // 更新预览缩放
    updatePreviewScale() {
      this.zoomLevel = this.printForm.scale / 100;
      // 如果有预览内容，重新应用样式
      if (this.previewContent) {
        this.$nextTick(() => {
          this.applyPreviewStyles();
        });
      }
    },

    // 应用预览样式
    applyPreviewStyles() {
      const previewPage = document.querySelector('.preview-page');
      if (previewPage) {
        const layoutStyles = this.getLayoutStyles();
        Object.assign(previewPage.style, layoutStyles);

        // 添加版式类名以便CSS选择器识别
        previewPage.className = `preview-page layout-${this.printForm.layout}`;

        // 直接设置内容样式
        const previewContent = previewPage.querySelector('.preview-content');
        if (previewContent) {
          const tables = previewContent.querySelectorAll('table');
          const divs = previewContent.querySelectorAll('div');
          const headers = previewContent.querySelectorAll('h1, h2, h3, h4, h5, h6');

          // 强制应用表格边框样式
          tables.forEach(table => {
            table.style.borderCollapse = 'collapse';
            table.style.border = '2px solid #333';
            table.style.width = '100%';

            // 强制应用单元格边框
            const cells = table.querySelectorAll('th, td');
            cells.forEach(cell => {
              cell.style.border = '1px solid #333';
              cell.style.padding = '8px';
              cell.style.textAlign = cell.tagName === 'TH' ? 'center' : 'left';
              if (cell.tagName === 'TH') {
                cell.style.backgroundColor = '#f5f5f5';
                cell.style.fontWeight = 'bold';
              }
            });
          });

          // 强制应用其他边框元素
          const borderElements = previewContent.querySelectorAll('.print-header, .print-footer, .section-title');
          borderElements.forEach(element => {
            if (element.classList.contains('print-header')) {
              element.style.borderBottom = '2px solid #333';
            } else if (element.classList.contains('print-footer')) {
              element.style.borderTop = '1px solid #333';
            } else if (element.classList.contains('section-title')) {
              element.style.borderBottom = '2px solid #3498db';
            }
          });

          switch (this.printForm.layout) {
            case 'center':
              previewContent.style.textAlign = 'center';
              previewContent.style.display = 'flex';
              previewContent.style.flexDirection = 'column';
              previewContent.style.alignItems = 'center';
              tables.forEach(table => {
                table.style.marginLeft = 'auto';
                table.style.marginRight = 'auto';
              });
              headers.forEach(header => {
                header.style.textAlign = 'center';
              });
              break;
            case 'left':
              previewContent.style.textAlign = 'left';
              previewContent.style.display = 'block';
              tables.forEach(table => {
                table.style.marginLeft = '0';
                table.style.marginRight = 'auto';
              });
              break;
            case 'right':
              previewContent.style.textAlign = 'right';
              previewContent.style.display = 'block';
              tables.forEach(table => {
                table.style.marginLeft = 'auto';
                table.style.marginRight = '0';
              });
              break;
            case 'fill':
              previewContent.style.textAlign = 'center';
              previewContent.style.display = 'flex';
              previewContent.style.flexDirection = 'column';
              previewContent.style.alignItems = 'center';
              previewContent.style.width = '100%';
              tables.forEach(table => {
                table.style.width = '100%';
                table.style.marginLeft = 'auto';
                table.style.marginRight = 'auto';
              });
              headers.forEach(header => {
                header.style.textAlign = 'center';
              });
              break;
          }
        }
      }
    },

    // 获取页面固定尺寸（像素）
    getPageDimensions() {
      let width, height;

      // 根据纸张大小设置固定尺寸 - 按96dpi转换
      switch (this.printForm.paperSize) {
        case 'A4':
          // A4: 210mm × 297mm = 794px × 1123px (96dpi)
          width = this.printForm.orientation === 'portrait' ? 794 : 1123;
          height = this.printForm.orientation === 'portrait' ? 1123 : 794;
          break;
        case 'A3':
          // A3: 297mm × 420mm = 1123px × 1587px
          width = this.printForm.orientation === 'portrait' ? 1123 : 1587;
          height = this.printForm.orientation === 'portrait' ? 1587 : 1123;
          break;
        case 'A5':
          // A5: 148mm × 210mm = 559px × 794px
          width = this.printForm.orientation === 'portrait' ? 559 : 794;
          height = this.printForm.orientation === 'portrait' ? 794 : 559;
          break;
        default:
          width = 794;
          height = 1123;
      }

      return { width, height };
    },

    // 获取版式样式
    getLayoutStyles() {
      const scale = this.printForm.scale / 100;

      let baseStyles;

      if (this.printForm.pageSizeMode === 'fixed') {
        // 标准尺寸模式
        const { width, height } = this.getPageDimensions();
        baseStyles = {
          width: `${width}px`,
          height: `${height}px`,
          transform: `scale(${scale})`,
          transformOrigin: 'top center',
          transition: 'transform 0.3s ease',
          backgroundColor: 'white',
          boxShadow: '0 2px 12px 0 rgba(0, 0, 0, 0.1)',
          border: '1px solid #ddd',
          padding: '15px',
          boxSizing: 'border-box',
          position: 'relative',
          overflow: 'hidden'
        };
      } else if (this.printForm.pageSizeMode === 'fullpage') {
        // 充分利用纸张模式
        const { width, height } = this.getPageDimensions();
        baseStyles = {
          width: `${width}px`,
          height: `${height}px`,
          transform: `scale(${scale})`,
          transformOrigin: 'top center',
          transition: 'transform 0.3s ease',
          backgroundColor: 'white',
          boxShadow: '0 2px 12px 0 rgba(0, 0, 0, 0.1)',
          border: '1px solid #ddd',
          padding: '5px', // 最小边距，最大化内容区域
          boxSizing: 'border-box',
          position: 'relative',
          overflow: 'hidden'
        };
      } else {
        // 自适应模式
        baseStyles = {
          transform: `scale(${scale})`,
          transformOrigin: 'top center',
          transition: 'transform 0.3s ease',
          backgroundColor: 'white',
          boxShadow: '0 2px 12px 0 rgba(0, 0, 0, 0.1)',
          border: '1px solid #ddd',
          padding: '20px',
          boxSizing: 'border-box',
          position: 'relative',
          width: 'fit-content',
          minWidth: '400px',
          minHeight: '500px'
        };
      }

      switch (this.printForm.layout) {
        case 'center':
          return {
            ...baseStyles,
            margin: '0 auto',
            display: 'block'
          };
        case 'left':
          return {
            ...baseStyles,
            margin: '0',
            display: 'block'
          };
        case 'right':
          return {
            ...baseStyles,
            margin: '0 0 0 auto',
            display: 'block'
          };
        case 'fill':
          if (this.printForm.pageSizeMode === 'adaptive') {
            return {
              ...baseStyles,
              width: '100%',
              margin: '0 auto',
              display: 'block'
            };
          } else {
            return {
              ...baseStyles,
              margin: '0 auto',
              display: 'block'
            };
          }
        default:
          return {
            ...baseStyles,
            margin: '0 auto',
            display: 'block'
          };
      }
    },

    // 设置版式
    setLayout(layout) {
      this.printForm.layout = layout;
      this.$nextTick(() => {
        this.applyPreviewStyles();
      });
    },

    // 重置缩放
    resetScale() {
      this.printForm.scale = 100;
      this.updatePreviewScale();
    },

    // 记录打印历史
    async recordPrint() {
      try {
        const template = this.templateList.find(t => t.id === this.printForm.templateId);
        await recordPrintHistory({
          billNo: this.printForm.billNo,
          templateId: this.printForm.templateId,
          templateName: template ? template.templateName : '',
          copies: this.printForm.copies,
          paperSize: this.printForm.paperSize,
          orientation: this.printForm.orientation,
          printerName: this.printForm.printerName,
          quality: this.printForm.quality
        });

        // 刷新打印历史
        this.loadPrintHistory();
      } catch (error) {
        console.error('记录打印历史失败:', error);
      }
    },

    // 加载打印机列表
    async loadPrinters() {
      try {
        // 调用后端API获取打印机列表
        const response = await getPrinters();
        if (response.code === 200) {
          this.printerList = response.data || [];
        } else {
          console.error('获取打印机列表失败:', response.msg);
          this.printerList = [
            { name: 'default', displayName: '系统默认打印机', status: '就绪' }
          ];
        }
      } catch (error) {
        console.error('获取打印机列表失败:', error);
        this.printerList = [
          { name: 'default', displayName: '系统默认打印机', status: '就绪' }
        ];
      }
    },

    // 测试打印机连接
    async testPrinterConnection() {
      if (!this.printForm.printerName || this.printForm.printerName === 'default') {
        this.$message.info('默认打印机无需测试');
        return;
      }

      try {
        this.$message.info('正在测试打印机连接...');
        const response = await testPrinter(this.printForm.printerName);
        if (response.code === 200) {
          this.$message.success('打印机连接正常');
        } else {
          this.$message.error('打印机连接失败：' + response.msg);
        }
      } catch (error) {
        console.error('测试打印机连接失败:', error);
        this.$message.error('测试打印机连接失败：' + error.message);
      }
    },

    // 加载打印历史
    loadPrintHistory() {
      // 这里可以调用API获取打印历史，暂时使用模拟数据
      this.printHistory = [
        {
          billNo: '650824098693120',
          templateName: '默认模板',
          printTime: '2025-07-09 14:30:00',
          copies: 1,
          operator: '当前用户'
        }
      ];
    },

    // 重新打印
    handleRepeat(row) {
      this.printForm.billNo = row.billNo;
      this.handleSearch();
    },

    // 刷新
    handleRefresh() {
      this.loadTemplates();
      this.loadPrintHistory();
    },

    // 放大
    zoomIn() {
      this.zoomLevel = Math.min(this.zoomLevel + 0.1, 2);
    },

    // 缩小
    zoomOut() {
      this.zoomLevel = Math.max(this.zoomLevel - 0.1, 0.5);
    },

    // 重置缩放
    resetZoom() {
      this.zoomLevel = 1;
    },

    // 获取业务类型文本
    getBusinessTypeText(businessType) {
      const typeMap = {
        'material': '物料出库',
        'product': '产品入库',
        'return': '退料入库',
        'transfer': '调拨'
      };
      return typeMap[businessType] || businessType;
    },

    // 获取审批状态类型
    getApprovalStatusType(status) {
      const typeMap = {
        '0': 'warning',
        '1': 'success', 
        '2': 'danger',
        '3': 'info'
      };
      return typeMap[status] || 'info';
    },

    // 获取审批状态文本
    getApprovalStatusText(status) {
      const textMap = {
        '0': '审批中',
        '1': '已通过',
        '2': '已驳回', 
        '3': '已撤销'
      };
      return textMap[status] || '未知';
    },

    // 防抖函数
    debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    },

    // 调整预览大小
    adjustPreviewSize() {
      // 在固定尺寸和充分利用模式下自动调整缩放
      if (this.printForm.pageSizeMode === 'adaptive') {
        return;
      }

      this.$nextTick(() => {
        const container = document.querySelector('.preview-container');

        if (container) {
          const containerWidth = container.clientWidth - 40; // 减去padding
          const containerHeight = container.clientHeight - 40;
          const { width: pageWidth, height: pageHeight } = this.getPageDimensions();

          // 计算合适的缩放比例
          const scaleX = containerWidth / pageWidth;
          const scaleY = containerHeight / pageHeight;
          const optimalScale = Math.min(scaleX, scaleY, 1); // 不超过100%

          // 根据容器大小自动调整预览页面缩放
          if (optimalScale < 0.8) {
            // 容器太小时自动缩放
            this.printForm.scale = Math.max(Math.round(optimalScale * 100), 30);
          } else if (containerWidth > pageWidth * 1.2 && this.printForm.scale < 100) {
            // 容器足够大时恢复正常大小
            this.printForm.scale = 100;
          }

          this.applyPreviewStyles();
        }
      });
    }
  },

  mounted() {
    this.loadTemplates();
    this.loadPrintHistory();
    this.loadPrinters();

    // 添加窗口大小变化监听器
    this.handleResize = this.debounce(() => {
      this.adjustPreviewSize();
    }, 300);

    window.addEventListener('resize', this.handleResize);

    // 初始调整预览大小
    this.$nextTick(() => {
      this.adjustPreviewSize();
    });
  },

  beforeDestroy() {
    if (this.handleResize) {
      window.removeEventListener('resize', this.handleResize);
    }
  }
};
</script>

<style scoped>
.bill-info {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
}

.bill-info p {
  margin: 5px 0;
}

/* 简化版单据信息样式 */
.bill-info-simple {
  background-color: #f0f9ff;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 3px solid #409eff;
  font-size: 13px;
}

.bill-info-simple p {
  margin: 3px 0;
  line-height: 1.4;
}

.preview-container {
  min-height: 500px;
  max-height: 600px;
  overflow: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #f5f7fa;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.preview-page {
  margin: 0 auto;
  background-color: white;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  transition: transform 0.3s ease;
  /* 固定尺寸由 getLayoutStyles 方法控制 */
}

.preview-content {
  width: 100%;
  height: 100%;
}

/* 版式样式 - 居中显示 */
.preview-page[style*="center"] .preview-content {
  text-align: center;
}

.preview-page[style*="center"] .preview-content table {
  margin-left: auto !important;
  margin-right: auto !important;
}

.preview-page[style*="center"] .preview-content h1,
.preview-page[style*="center"] .preview-content h2,
.preview-page[style*="center"] .preview-content h3,
.preview-page[style*="center"] .preview-content div {
  text-align: center !important;
}

/* 版式样式 - 左对齐 */
.preview-page[style*="left"] .preview-content {
  text-align: left;
}

.preview-page[style*="left"] .preview-content table {
  margin-left: 0 !important;
  margin-right: auto !important;
}

/* 版式样式 - 右对齐 */
.preview-page[style*="right"] .preview-content {
  text-align: right;
}

.preview-page[style*="right"] .preview-content table {
  margin-left: auto !important;
  margin-right: 0 !important;
}

/* 版式样式 - 铺满页面 */
.preview-page[style*="fill"] {
  width: 100% !important;
}

.preview-page[style*="fill"] .preview-content {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  text-align: center !important;
}

.preview-page[style*="fill"] .preview-content table {
  width: 100% !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

.preview-page[style*="fill"] .preview-content h1,
.preview-page[style*="fill"] .preview-content h2,
.preview-page[style*="fill"] .preview-content h3,
.preview-page[style*="fill"] .preview-content div {
  text-align: center !important;
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
}

.preview-placeholder i {
  font-size: 48px;
  margin-bottom: 16px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

/* 优化表单布局 */
.el-form-item {
  margin-bottom: 18px;
}

.el-form-item:last-child {
  margin-bottom: 0;
}

/* 单据信息样式 */
.bill-info {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  border-left: 4px solid #409eff;
  margin-top: 10px;
}

.bill-info p {
  margin: 5px 0;
  font-size: 14px;
  line-height: 1.5;
}

/* 按钮组优化 */
.el-button + .el-button {
  margin-left: 0;
  margin-top: 10px;
}

/* 滚动条样式 */
.preview-container::-webkit-scrollbar {
  width: 6px;
}

.preview-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.preview-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.preview-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式优化 */
@media (max-width: 1600px) {
  .el-row .el-col {
    margin-bottom: 20px;
  }

  .el-card {
    height: auto !important;
  }

  /* 调整预览区域的缩放控制按钮 */
  .el-button-group {
    margin-bottom: 10px;
  }
}

@media (max-width: 1200px) {
  /* 在中等屏幕上调整列布局 */
  .print-settings-col {
    flex: 0 0 35% !important;
    max-width: 35% !important;
  }

  .print-preview-col {
    flex: 0 0 65% !important;
    max-width: 65% !important;
  }

  .preview-container {
    min-height: 400px !important;
    max-height: 500px !important;
  }

  .preview-page {
    min-width: 400px !important;
  }
}

@media (max-width: 992px) {
  /* 在小屏幕上垂直堆叠 */
  .print-settings-col,
  .print-preview-col {
    flex: 0 0 100% !important;
    max-width: 100% !important;
  }

  .preview-container {
    min-height: 300px !important;
    max-height: 400px !important;
  }

  .preview-page {
    min-width: 300px !important;
    transform-origin: top left !important;
  }

  /* 调整按钮组在小屏幕上的显示 */
  .el-button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
  }

  .el-button-group .el-button {
    margin: 0 !important;
    flex: 1;
    min-width: 80px;
  }
}

@media (max-width: 768px) {
  /* 在移动设备上进一步优化 */
  .app-container {
    padding: 10px !important;
  }

  .el-card {
    margin-bottom: 15px;
  }

  .el-card__body {
    padding: 15px !important;
  }

  .preview-container {
    padding: 10px !important;
    min-height: 250px !important;
    max-height: 350px !important;
  }

  .preview-page {
    min-width: 250px !important;
    padding: 10px !important;
  }

  /* 表单项在移动设备上的优化 */
  .el-form-item__label {
    width: 80px !important;
    font-size: 13px;
  }

  .el-form-item__content {
    margin-left: 80px !important;
  }

  /* 按钮在移动设备上的优化 */
  .el-button-group .el-button {
    font-size: 12px;
    padding: 8px 10px;
  }
}

/* 卡片内容区域优化 */
.el-card__body {
  padding: 20px;
  height: auto;
}

/* 输入组样式优化 */
.el-input-group__append {
  padding: 0 15px;
}

/* 预览控制按钮响应式优化 */
.preview-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: center;
}

@media (max-width: 992px) {
  .preview-controls {
    float: none !important;
    width: 100%;
    justify-content: center;
    margin-top: 10px;
  }

  .scale-controls,
  .layout-controls {
    margin: 0 !important;
  }
}

@media (max-width: 768px) {
  .preview-controls {
    flex-direction: column;
    gap: 8px;
  }

  .scale-controls .el-button,
  .layout-controls .el-button {
    font-size: 11px;
    padding: 6px 8px;
  }
}

/* 确保预览页面在不同尺寸下的自适应 */
.preview-page {
  max-width: 100%;
  overflow: hidden;
}

.preview-content {
  overflow-x: auto;
}

.preview-content table {
  max-width: 100%;
  word-wrap: break-word;
  table-layout: auto;
}

/* 优化表格在小屏幕上的显示 */
@media (max-width: 768px) {
  .preview-content table {
    font-size: 12px;
  }

  .preview-content th,
  .preview-content td {
    padding: 4px 6px !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100px;
  }
}

/* 充分利用纸张模式的样式优化 */
.preview-page[data-mode="fullpage"] {
  padding: 5px !important;
}

.preview-page[data-mode="fullpage"] .preview-content {
  width: 100%;
  height: 100%;
}

.preview-page[data-mode="fullpage"] .preview-content table {
  width: 100% !important;
  max-width: none !important;
  margin: 5px 0 !important;
  font-size: 11px;
  table-layout: auto !important;
}

.preview-page[data-mode="fullpage"] .preview-content th,
.preview-page[data-mode="fullpage"] .preview-content td {
  padding: 3px 4px !important;
  font-size: 10px;
  line-height: 1.1;
  word-wrap: break-word;
}

.preview-page[data-mode="fullpage"] .preview-content th {
  background-color: #f5f5f5 !important;
  font-weight: bold;
  text-align: center;
}

.preview-page[data-mode="fullpage"] .preview-content h1,
.preview-page[data-mode="fullpage"] .preview-content h2,
.preview-page[data-mode="fullpage"] .preview-content h3 {
  margin: 5px 0 !important;
  font-size: 14px;
  line-height: 1.1;
}

.preview-page[data-mode="fullpage"] .preview-content p,
.preview-page[data-mode="fullpage"] .preview-content div {
  margin: 2px 0 !important;
  font-size: 11px;
  line-height: 1.2;
}

/* 移除所有宽度限制 */
.preview-page .preview-content {
  max-width: none !important;
  width: 100% !important;
}

.preview-page .preview-content table {
  max-width: none !important;
  width: 100% !important;
}
</style>
