<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-people">
            <svg-icon icon-class="message" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">总主题数</div>
            <div class="card-panel-num">{{ statistics.totalTopics }}</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-message">
            <svg-icon icon-class="online" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">活跃主题</div>
            <div class="card-panel-num">{{ statistics.activeTopics }}</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-money">
            <svg-icon icon-class="component" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">设备类型</div>
            <div class="card-panel-num">{{ statistics.deviceTypes }}</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-shopping">
            <svg-icon icon-class="chart" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">订阅数</div>
            <div class="card-panel-num">{{ statistics.subscribeTopics }}</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 查询表单 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="设备类型" prop="deviceType">
        <el-select v-model="queryParams.deviceType" placeholder="请选择设备类型" clearable>
          <el-option v-for="deviceType in deviceTypeOptions" :key="deviceType" :label="deviceType" :value="deviceType" />
        </el-select>
      </el-form-item>
      <el-form-item label="主题类型" prop="topicType">
        <el-select v-model="queryParams.topicType" placeholder="请选择主题类型" clearable>
          <el-option label="发布主题" value="PUBLISH" />
          <el-option label="订阅主题" value="SUBSCRIBE" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="isEnabled">
        <el-select v-model="queryParams.isEnabled" placeholder="请选择状态" clearable>
          <el-option label="启用" value="1" />
          <el-option label="停用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['emqx:topic:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['emqx:topic:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['emqx:topic:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['emqx:topic:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 主题列表 -->
    <el-table v-loading="loading" :data="topicList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主题ID" align="center" prop="topicId" width="80" />
      <el-table-column label="设备类型" align="center" prop="deviceType" width="120" />
      <el-table-column label="主题模式" align="center" prop="topicPattern" :show-overflow-tooltip="true" />
      <el-table-column label="主题类型" align="center" prop="topicType" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.topicType === 'PUBLISH' ? 'success' : 'info'">
            {{ scope.row.topicType === 'PUBLISH' ? '发布' : '订阅' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="QoS等级" align="center" prop="qos" width="80" />
      <el-table-column label="描述" align="center" prop="description" :show-overflow-tooltip="true" />
      <el-table-column label="状态" align="center" prop="isEnabled" width="80">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.isEnabled" active-value="1" inactive-value="0" @change="handleStatusChange(scope.row)"></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['emqx:topic:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['emqx:topic:remove']">删除</el-button>
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleTest(scope.row)">测试</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改主题配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="设备类型" prop="deviceType">
          <el-select v-model="form.deviceType" placeholder="请选择设备类型" @change="onDeviceTypeChange">
            <el-option v-for="deviceType in deviceTypeOptions" :key="deviceType" :label="deviceType" :value="deviceType" />
          </el-select>
        </el-form-item>
        <el-form-item label="主题模式" prop="topicPattern">
          <el-input v-model="form.topicPattern" placeholder="请输入主题模式，如：warehouse/weight/{deviceCode}/data" />
          <div style="color: #909399; font-size: 12px; margin-top: 4px;">
            支持变量：{deviceCode}、{clientId}、{deviceType}，支持通配符：+（单层）、#（多层）
          </div>
        </el-form-item>
        <el-form-item label="主题类型" prop="topicType">
          <el-radio-group v-model="form.topicType">
            <el-radio label="PUBLISH">发布主题</el-radio>
            <el-radio label="SUBSCRIBE">订阅主题</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="QoS等级" prop="qos">
          <el-select v-model="form.qos" placeholder="请选择QoS等级">
            <el-option label="0 - 最多一次" :value="0" />
            <el-option label="1 - 至少一次" :value="1" />
            <el-option label="2 - 恰好一次" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="EMQX客户端" prop="clientId">
          <el-select
            v-model="form.clientId"
            placeholder="请选择EMQX客户端"
            filterable
            :loading="clientLoading"
            @focus="loadAvailableClients"
            @change="onClientChange"
            @visible-change="onClientDropdownVisibleChange"
          >
            <el-option
              v-for="client in availableClients"
              :key="client.clientId"
              :label="getClientLabel(client)"
              :value="client.clientId"
            >
              <span style="float: left">{{ client.clientId }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">
                {{ getClientStatusText(client.status) }}
                <el-tag v-if="client.recommended" type="warning" size="mini" style="margin-left: 5px">推荐</el-tag>
              </span>
            </el-option>
          </el-select>
          <div style="color: #909399; font-size: 12px; margin-top: 4px;">
            <span v-if="availableClients.length === 0 && !clientLoading">
              <el-button type="text" size="mini" @click="loadAvailableClients" style="padding: 0; color: #f56c6c;">
                <i class="el-icon-refresh"></i> 点击加载客户端列表（必选）
              </el-button>
            </span>
            <span v-else-if="clientLoading">
              <i class="el-icon-loading"></i> 正在加载客户端列表...
            </span>
            <span v-else>
              <span style="color: #f56c6c;">*</span> 必须选择EMQX客户端，推荐使用系统默认客户端
              <el-button type="text" size="mini" @click="loadAvailableClients" style="padding: 0; margin-left: 10px;">
                <i class="el-icon-refresh"></i> 刷新
              </el-button>
            </span>
          </div>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入主题描述" />
        </el-form-item>
        <el-form-item label="状态" prop="isEnabled">
          <el-radio-group v-model="form.isEnabled">
            <el-radio label="1">启用</el-radio>
            <el-radio label="0">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 主题测试对话框 -->
    <el-dialog title="主题测试" :visible.sync="testOpen" width="800px" append-to-body>
      <el-form :model="testForm" label-width="100px">
        <el-form-item label="设备编码">
          <el-input v-model="testForm.deviceCode" placeholder="请输入设备编码" @input="updateActualTopic" />
        </el-form-item>
        <el-form-item label="实际主题">
          <el-input v-model="testForm.actualTopic" readonly />
        </el-form-item>
        <el-form-item label="测试消息" v-if="testForm.topicType === 'PUBLISH'">
          <el-input v-model="testForm.testMessage" type="textarea" placeholder="请输入测试消息内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="executeTest">执行测试</el-button>
        <el-button @click="testOpen = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTopic, getTopic, delTopic, addTopic, updateTopic, getTopicStatistics, getDeviceTypes, validateTopicPattern } from "@/api/emqx/topic";
import { getAvailableClients, subscribeTopicToClient, recommendClients } from "@/api/emqx/topicClientMapping";

export default {
  name: "EmqxTopic",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 主题表格数据
      topicList: [],
      // 设备类型选项
      deviceTypeOptions: [],
      // 可用客户端列表
      availableClients: [],
      // 客户端加载状态
      clientLoading: false,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 测试弹出层
      testOpen: false,
      // 统计数据
      statistics: {
        totalTopics: 0,
        activeTopics: 0,
        deviceTypes: 0,
        publishTopics: 0,
        subscribeTopics: 0
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceType: null,
        topicType: null,
        isEnabled: null
      },
      // 表单参数
      form: {},
      // 测试表单
      testForm: {},
      // 表单校验
      rules: {
        deviceType: [
          { required: true, message: "设备类型不能为空", trigger: "change" }
        ],
        topicPattern: [
          { required: true, message: "主题模式不能为空", trigger: "blur" },
          { validator: this.validateTopic, trigger: "blur" }
        ],
        topicType: [
          { required: true, message: "主题类型不能为空", trigger: "change" }
        ],
        qos: [
          { required: true, message: "QoS等级不能为空", trigger: "change" }
        ],
        clientId: [
          {
            validator: (rule, value, callback) => {
              console.log('=== 客户端验证开始 ===');
              console.log('验证值:', value);
              console.log('表单clientId:', this.form.clientId);
              console.log('可用客户端数量:', this.availableClients.length);
              console.log('客户端加载状态:', this.clientLoading);
              console.log('可用客户端列表:', this.availableClients.map(c => c.clientId));

              // 使用表单中的实际值，而不是验证器传入的值
              const actualValue = value || this.form.clientId;
              console.log('实际验证值:', actualValue);

              if (!actualValue) {
                // 检查是否有可用的客户端列表
                if (this.clientLoading) {
                  console.log('客户端验证 - 正在加载中，跳过验证');
                  callback(); // 加载中时不显示错误
                  return;
                }

                if (this.availableClients.length === 0) {
                  console.log('客户端验证 - 客户端列表为空');
                  callback(new Error('正在加载客户端列表，请稍候...'));
                } else {
                  console.log('客户端验证 - 未选择客户端');
                  callback(new Error('请选择EMQX客户端'));
                }
              } else {
                // 检查选择的客户端是否在可用列表中
                const clientExists = this.availableClients.find(client => client.clientId === actualValue);
                console.log('客户端是否存在:', !!clientExists);

                if (this.availableClients.length > 0 && !clientExists) {
                  console.log('客户端验证 - 选择的客户端不在列表中');
                  callback(new Error('选择的客户端不在可用列表中'));
                } else {
                  console.log('客户端验证 - 验证通过');
                  callback();
                }
              }
              console.log('=== 客户端验证结束 ===');
            },
            trigger: ['change', 'blur']
          }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getStatistics();
    this.getDeviceTypeOptions();
    // 预加载客户端列表
    this.$nextTick(() => {
      this.loadAvailableClients();
    });
  },
  methods: {
    /** 查询主题列表 */
    getList() {
      this.loading = true;
      listTopic(this.queryParams).then(response => {
        this.topicList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    /** 获取统计数据 */
    getStatistics() {
      getTopicStatistics().then(response => {
        this.statistics = response.data;
      }).catch(() => {
        console.log('获取统计数据失败');
      });
    },
    /** 获取设备类型选项 */
    getDeviceTypeOptions() {
      getDeviceTypes().then(response => {
        this.deviceTypeOptions = response.data;
      }).catch(() => {
        // 默认设备类型
        this.deviceTypeOptions = [
          '出库防错设备', '温湿度传感器', 'RFID读写器',
          '条码扫描器', '称重设备', 'AGV小车',
          '货架传感器', '门禁设备'
        ];
      });
    },
    /** 主题模式验证 */
    validateTopic(rule, value, callback) {
      if (!value) {
        callback();
        return;
      }

      // 前端验证MQTT主题模式格式
      const mqttTopicValidation = this.validateMqttTopicPattern(value);
      if (!mqttTopicValidation.valid) {
        callback(new Error(mqttTopicValidation.message));
        return;
      }

      // 如果前端验证通过，再调用后端验证（可选）
      // 注释掉后端验证，因为它可能不支持MQTT通配符
      callback();

      // validateTopicPattern({ topicPattern: value }).then(response => {
      //   callback();
      // }).catch(error => {
      //   callback(new Error('主题模式格式不正确'));
      // });
    },

    /** 验证MQTT主题模式格式 */
    validateMqttTopicPattern(topicPattern) {
      // MQTT主题模式验证规则
      const rules = {
        // 不能为空
        notEmpty: topicPattern && topicPattern.trim().length > 0,
        // 不能以/开头或结尾（除非是根主题）
        noLeadingTrailingSlash: !topicPattern.startsWith('/') && !topicPattern.endsWith('/'),
        // 不能包含连续的斜杠
        noConsecutiveSlashes: !topicPattern.includes('//'),
        // 通配符规则检查
        validWildcards: this.validateWildcards(topicPattern),
        // 变量格式检查
        validVariables: this.validateVariables(topicPattern)
      };

      if (!rules.notEmpty) {
        return { valid: false, message: '主题模式不能为空' };
      }

      if (!rules.noLeadingTrailingSlash) {
        return { valid: false, message: '主题模式不能以/开头或结尾' };
      }

      if (!rules.noConsecutiveSlashes) {
        return { valid: false, message: '主题模式不能包含连续的斜杠' };
      }

      if (!rules.validWildcards.valid) {
        return { valid: false, message: rules.validWildcards.message };
      }

      if (!rules.validVariables.valid) {
        return { valid: false, message: rules.validVariables.message };
      }

      return { valid: true, message: '主题模式格式正确' };
    },

    /** 验证通配符使用 */
    validateWildcards(topicPattern) {
      const segments = topicPattern.split('/');

      for (let i = 0; i < segments.length; i++) {
        const segment = segments[i];

        // 检查#通配符（多层通配符）
        if (segment.includes('#')) {
          // #必须是独立的段，且只能出现在最后
          if (segment !== '#') {
            return { valid: false, message: '#通配符必须独占一个层级' };
          }
          if (i !== segments.length - 1) {
            return { valid: false, message: '#通配符只能出现在主题的最后' };
          }
        }

        // 检查+通配符（单层通配符）
        if (segment.includes('+')) {
          // +必须是独立的段
          if (segment !== '+') {
            return { valid: false, message: '+通配符必须独占一个层级' };
          }
        }
      }

      return { valid: true };
    },

    /** 验证变量格式 */
    validateVariables(topicPattern) {
      // 检查变量格式 {variableName}
      const variablePattern = /\{([^}]+)\}/g;
      const matches = topicPattern.match(variablePattern);

      if (matches) {
        for (const match of matches) {
          const variableName = match.slice(1, -1); // 去掉{}

          // 变量名不能为空
          if (!variableName.trim()) {
            return { valid: false, message: '变量名不能为空' };
          }

          // 变量名只能包含字母、数字、下划线
          if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(variableName)) {
            return { valid: false, message: `变量名"${variableName}"格式不正确，只能包含字母、数字、下划线，且不能以数字开头` };
          }
        }
      }

      return { valid: true };
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        topicId: null,
        deviceType: null,
        topicPattern: null,
        topicType: null,
        qos: 1,
        clientId: null,
        description: null,
        isEnabled: "1"
      };
      this.resetForm("form");
    },

    /** 确保表单数据完整性 */
    ensureFormDataIntegrity() {
      // 确保所有必需的字段都存在
      if (!this.form.hasOwnProperty('clientId')) {
        this.form.clientId = null;
      }
      if (!this.form.hasOwnProperty('qos')) {
        this.form.qos = 1;
      }
      if (!this.form.hasOwnProperty('isEnabled')) {
        this.form.isEnabled = "1";
      }

      console.log('表单数据完整性检查完成:', this.form);
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.topicId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加主题配置";
      // 立即加载可用客户端列表
      this.$nextTick(() => {
        this.loadAvailableClients();
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const topicId = row.topicId || this.ids
      getTopic(topicId).then(response => {
        this.form = response.data;

        // 确保表单数据完整性
        this.ensureFormDataIntegrity();

        console.log('修改模式 - 获取到的表单数据:', this.form);

        this.open = true;
        this.title = "修改主题配置";

        // 强制重置表单验证状态
        this.forceResetValidation();

        // 先加载客户端列表，然后处理表单数据
        this.$nextTick(async () => {
          await this.loadAvailableClients();

          // 如果表单中没有clientId，尝试自动选择推荐的客户端
          if (!this.form.clientId && this.availableClients.length > 0) {
            const recommendedClient = this.availableClients.find(client => client.recommended);
            if (recommendedClient) {
              this.form.clientId = recommendedClient.clientId;
              console.log('修改模式 - 自动选择推荐客户端:', recommendedClient.clientId);
            }
          }

          // 清除可能的验证错误
          this.$nextTick(() => {
            if (this.$refs.form) {
              this.$refs.form.clearValidate();
              // 特别清除clientId字段的验证错误
              setTimeout(() => {
                this.$refs.form.clearValidate(['clientId']);
              }, 200);
            }
          });
        });
      }).catch(error => {
        console.error('获取主题信息失败:', error);
        this.$modal.msgError('获取主题信息失败');
      });
    },
    /** 提交按钮 */
    submitForm() {
      console.log('=== 开始提交表单 ===');
      console.log('当前表单数据:', JSON.stringify(this.form, null, 2));
      console.log('可用客户端列表:', this.availableClients.map(c => c.clientId));
      console.log('客户端加载状态:', this.clientLoading);

      // 如果客户端列表还在加载中，等待加载完成
      if (this.clientLoading) {
        this.$message.warning('客户端列表正在加载中，请稍候...');
        return;
      }

      // 预检查：确保必要的数据已准备好
      if (!this.form.clientId && this.availableClients.length > 0) {
        console.log('尝试自动选择推荐客户端...');
        // 尝试自动选择推荐客户端
        const recommendedClient = this.availableClients.find(client => client.recommended);
        if (recommendedClient) {
          this.form.clientId = recommendedClient.clientId;
          console.log('自动选择了推荐客户端:', recommendedClient.clientId);
          this.$message.info(`已自动选择推荐客户端: ${recommendedClient.clientId}`);

          // 触发客户端选择变化事件
          this.onClientChange(recommendedClient.clientId);
        }
      }

      // 强制更新表单验证
      this.$nextTick(() => {
        console.log('开始表单验证...');
        this.$refs["form"].validate((valid, invalidFields) => {
          console.log('表单验证结果:', valid);
          console.log('表单最终数据:', JSON.stringify(this.form, null, 2));

          if (invalidFields) {
            console.log('验证失败的字段:', invalidFields);

            // 提供更详细的错误信息
            const errorMessages = [];
            Object.keys(invalidFields).forEach(field => {
              const fieldErrors = invalidFields[field];
              if (fieldErrors && fieldErrors.length > 0) {
                errorMessages.push(`${field}: ${fieldErrors[0].message}`);
              }
            });

            if (errorMessages.length > 0) {
              this.$message.error(`表单验证失败：${errorMessages.join('; ')}`);
            }
          }

          if (valid) {
            console.log('表单验证通过，开始提交...');
            this.doSubmit();
          } else {
            console.log('表单验证失败，停止提交');
            return false;
          }
        });
      });
    },

    /** 执行提交操作 */
    doSubmit() {
      if (this.form.topicId != null) {
        updateTopic(this.form).then(response => {
          this.$modal.msgSuccess("修改成功");
          this.open = false;
          this.getList();
          this.getStatistics();

          // 如果选择了客户端且主题类型为订阅，自动订阅到客户端
          if (this.form.clientId && this.form.topicType === 'SUBSCRIBE') {
            this.autoSubscribeToClient(this.form.topicId);
          }
        }).catch(error => {
          console.error('修改主题失败:', error);
          this.$modal.msgError('修改主题失败: ' + (error.message || '未知错误'));
        });
      } else {
        addTopic(this.form).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
          this.getStatistics();

          // 如果选择了客户端且主题类型为订阅，自动订阅到客户端
          if (this.form.clientId && this.form.topicType === 'SUBSCRIBE' && response.data) {
            // 使用返回的主题ID进行自动订阅
            const topicId = response.data.topicId || response.data;
            this.autoSubscribeToClient(topicId);
          }
        }).catch(error => {
          console.error('新增主题失败:', error);
          this.$modal.msgError('新增主题失败: ' + (error.message || '未知错误'));
        });
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const topicIds = row.topicId ? [row.topicId] : this.ids;
      this.$modal.confirm('是否确认删除主题配置编号为"' + topicIds + '"的数据项？').then(function() {
        return delTopic(topicIds);
      }).then(() => {
        this.getList();
        this.getStatistics();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('emqx/topic/export', {
        ...this.queryParams
      }, `topic_${new Date().getTime()}.xlsx`)
    },
    /** 状态修改 */
    handleStatusChange(row) {
      let text = row.isEnabled === "1" ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '""' + row.topicPattern + '"主题吗？').then(function() {
        return updateTopic(row);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function() {
        row.isEnabled = row.isEnabled === "1" ? "0" : "1";
      });
    },
    /** 设备类型变化处理 */
    onDeviceTypeChange(deviceType) {
      // 根据设备类型生成默认主题模式
      const topicTemplates = {
        '出库防错设备': 'dwms/outmistake/{deviceCode}/data',
        '温湿度传感器': 'dwms/env/{deviceCode}/data',
        'RFID读写器': 'dwms/rfid/{deviceCode}/scan',
        '条码扫描器': 'dwms/barcode/{deviceCode}/scan',
        '称重设备': 'dwms/weight/{deviceCode}/data',
        'AGV小车': 'dwms/agv/{deviceCode}/status',
        '货架传感器': 'dwms/shelf/{deviceCode}/data',
        '门禁设备': 'dwms/access/{deviceCode}/event'
      };

      if (topicTemplates[deviceType]) {
        this.form.topicPattern = topicTemplates[deviceType];

        // 设备类型和主题模式都有了，推荐合适的客户端
        this.$nextTick(() => {
          this.recommendClientsForTopic();
        });
      }
    },
    /** 主题测试 */
    handleTest(row) {
      this.testForm = {
        topicId: row.topicId,
        topicPattern: row.topicPattern,
        topicType: row.topicType,
        deviceCode: '',
        actualTopic: '',
        testMessage: ''
      };
      this.testOpen = true;
    },
    /** 更新实际主题 */
    updateActualTopic() {
      if (this.testForm.deviceCode && this.testForm.topicPattern) {
        this.testForm.actualTopic = this.testForm.topicPattern
          .replace('{deviceCode}', this.testForm.deviceCode)
          .replace('{clientId}', `client_${this.testForm.deviceCode}`)
          .replace('{deviceType}', this.testForm.deviceType || 'device');
      }
    },
    /** 执行测试 */
    executeTest() {
      if (!this.testForm.deviceCode) {
        this.$modal.msgWarning("请输入设备编码");
        return;
      }

      this.updateActualTopic();

      // 这里可以调用后端API进行实际的MQTT测试
      this.$modal.msgSuccess("测试完成，主题：" + this.testForm.actualTopic);
    },

    // ==================== 客户端管理相关方法 ====================

    /** 加载可用的EMQX客户端列表 */
    async loadAvailableClients() {
      // 避免重复加载
      if (this.clientLoading) {
        return Promise.resolve();
      }

      console.log('开始加载客户端列表...');
      this.clientLoading = true;

      try {
        const response = await getAvailableClients();
        console.log('客户端API响应:', response);
        if (response.code === 200) {
          this.availableClients = response.data || [];

          // 如果没有客户端，创建一个默认的系统客户端选项
          if (this.availableClients.length === 0) {
            this.availableClients = [{
              clientId: 'dwms_topic_manager',
              username: 'dwms_system',
              status: 'system_default',
              recommended: true,
              description: '系统默认客户端（将自动创建）'
            }];
            this.$message.info('已为您提供系统默认客户端选项');
          }

          // 如果表单中没有选择客户端，自动选择推荐的客户端
          if (!this.form.clientId && this.availableClients.length > 0) {
            const recommendedClient = this.availableClients.find(client => client.recommended);
            if (recommendedClient) {
              this.form.clientId = recommendedClient.clientId;
              this.$message.success(`已自动选择推荐客户端: ${recommendedClient.clientId}`);

              // 触发表单重新验证
              this.$nextTick(() => {
                if (this.$refs.form) {
                  this.$refs.form.validateField('clientId');
                }
              });
            }
          }
        } else {
          console.warn('获取客户端列表失败:', response.msg);
          // 提供默认客户端选项
          this.availableClients = [
            {
              clientId: 'dwms_topic_manager',
              username: 'dwms_system',
              status: 'system_default',
              recommended: true,
              description: '系统默认客户端（将自动创建）'
            },
            {
              clientId: '168_0_126_8081',
              username: 'admin',
              status: 'connected',
              recommended: false,
              description: '当前连接的客户端'
            }
          ];
          // 自动选择默认客户端
          if (!this.form.clientId) {
            this.form.clientId = 'dwms_topic_manager';
            this.$message.warning('无法获取客户端列表，已自动选择系统默认客户端');
          }
        }
      } catch (error) {
        console.error('获取客户端列表失败:', error);
        // 提供默认客户端选项
        this.availableClients = [
          {
            clientId: 'dwms_topic_manager',
            username: 'dwms_system',
            status: 'system_default',
            recommended: true,
            description: '系统默认客户端（将自动创建）'
          },
          {
            clientId: '168_0_126_8081',
            username: 'admin',
            status: 'connected',
            recommended: false,
            description: '当前连接的客户端'
          }
        ];
        // 自动选择默认客户端
        if (!this.form.clientId) {
          this.form.clientId = 'dwms_topic_manager';
          this.$message.warning('网络异常，已自动选择系统默认客户端');
        }
      } finally {
        this.clientLoading = false;
      }
    },

    /** 客户端选择变化处理 */
    onClientChange(clientId) {
      console.log('客户端选择变化:', clientId);
      console.log('当前表单clientId:', this.form.clientId);
      console.log('可用客户端列表:', this.availableClients);

      // 确保表单数据同步
      this.form.clientId = clientId;

      const selectedClient = this.availableClients.find(client => client.clientId === clientId);
      if (selectedClient) {
        console.log('选择的客户端:', selectedClient);

        // 可以在这里根据客户端类型调整其他表单字段
        if (selectedClient.status === 'system_default') {
          // 系统默认客户端，可以设置一些默认值
          if (!this.form.qos) {
            this.form.qos = 1; // 默认QoS等级
          }
        }
      } else if (clientId) {
        console.warn('选择的客户端不在可用列表中:', clientId);
      }

      // 清除客户端字段的验证错误
      this.$nextTick(() => {
        if (this.$refs.form) {
          console.log('触发clientId字段重新验证');
          // 先清除验证错误，再重新验证
          this.$refs.form.clearValidate(['clientId']);
          setTimeout(() => {
            this.$refs.form.validateField('clientId');
          }, 100);
        }
      });
    },

    /** 手动清除并重新验证客户端字段 */
    revalidateClientId() {
      if (this.$refs.form) {
        console.log('手动重新验证clientId字段');
        this.$refs.form.clearValidate(['clientId']);
        this.$nextTick(() => {
          this.$refs.form.validateField('clientId');
        });
      }
    },

    /** 客户端下拉框可见性变化处理 */
    onClientDropdownVisibleChange(visible) {
      console.log('客户端下拉框可见性变化:', visible);
      if (!visible && this.form.clientId) {
        // 下拉框关闭且有选择值时，重新验证
        this.$nextTick(() => {
          this.revalidateClientId();
        });
      }
    },



    /** 获取客户端显示标签 */
    getClientLabel(client) {
      let label = client.clientId;
      if (client.username && client.username !== client.clientId) {
        label += ` (${client.username})`;
      }
      return label;
    },

    /** 获取客户端状态文本 */
    getClientStatusText(status) {
      switch (status) {
        case 'connected': return '已连接';
        case 'system_default': return '系统默认';
        case 'disconnected': return '已断开';
        default: return status || '未知';
      }
    },

    /** 推荐客户端 */
    async recommendClientsForTopic() {
      if (!this.form.topicPattern || !this.form.deviceType) {
        return;
      }

      try {
        const response = await recommendClients(this.form.topicPattern, this.form.deviceType);
        if (response.code === 200) {
          const recommendations = response.data || [];
          if (recommendations.length > 0) {
            // 自动选择推荐分数最高的客户端
            const bestClient = recommendations[0];
            this.form.clientId = bestClient.clientId;

            this.$modal.msgSuccess(`已为您推荐客户端: ${bestClient.clientId} (${bestClient.reason})`);
          }
        }
      } catch (error) {
        console.error('获取推荐客户端失败:', error);
      }
    },

    /** 自动订阅主题到选定的客户端 */
    async autoSubscribeToClient(topicConfigId) {
      if (!this.form.clientId || !this.form.topicPattern) {
        return;
      }

      try {
        const response = await subscribeTopicToClient(
          topicConfigId,
          this.form.clientId,
          this.form.topicPattern,
          this.form.qos || 1
        );

        if (response.code === 200 && response.data.success) {
          this.$modal.msgSuccess(`主题已自动订阅到客户端: ${this.form.clientId}`);
        } else {
          console.warn('自动订阅失败:', response.data.message);
        }
      } catch (error) {
        console.error('自动订阅失败:', error);
      }
    }
  }
};
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}

.card-panel {
  height: 108px;
  cursor: pointer;
  font-size: 12px;
  position: relative;
  overflow: hidden;
  color: #666;
  background: #fff;
  box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
  border-color: rgba(0, 0, 0, .05);
  border-radius: 4px;
}

.card-panel:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-panel-icon-wrapper {
  float: left;
  margin: 14px 0 0 14px;
  padding: 16px;
  transition: all 0.38s ease-out;
  border-radius: 6px;
}

.card-panel-icon {
  float: left;
  font-size: 48px;
}

.card-panel-description {
  float: right;
  font-weight: bold;
  margin: 26px;
  margin-left: 0px;
}

.card-panel-text {
  line-height: 18px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 16px;
  margin-bottom: 12px;
}

.card-panel-num {
  font-size: 20px;
}

.icon-people {
  background: #40c9c6;
}

.icon-message {
  background: #36a3f7;
}

.icon-money {
  background: #f4516c;
}

.icon-shopping {
  background: #34bfa3;
}

.icon-people .card-panel-icon {
  color: #40c9c6;
}

.icon-message .card-panel-icon {
  color: #36a3f7;
}

.icon-money .card-panel-icon {
  color: #f4516c;
}

.icon-shopping .card-panel-icon {
  color: #34bfa3;
}
</style>
