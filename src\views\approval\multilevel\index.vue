<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>多级审批管理</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="handleCreate">创建多级审批</el-button>
      </div>

      <!-- 搜索区域 -->
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="流程名称" prop="workflowName">
          <el-input
            v-model="queryParams.workflowName"
            placeholder="请输入流程名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="业务类型" prop="businessType">
          <el-select v-model="queryParams.businessType" placeholder="请选择业务类型" clearable>
            <el-option v-for="item in businessTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 工具栏 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleCreate"
            v-hasPermi="['approval:multilevel:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="info"
            plain
            icon="el-icon-sort"
            size="mini"
            @click="toggleExpandAll"
          >展开/折叠</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <!-- 多级审批列表 -->
      <el-table
        v-if="refreshTable"
        v-loading="loading"
        :data="workflowList"
        row-key="workflowId"
        :default-expand-all="isExpandAll"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      >
        <el-table-column prop="workflowName" label="流程名称" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="businessType" label="业务类型" width="120">
          <template slot-scope="scope">
            <el-tag :type="getBusinessTypeTagType(scope.row.businessType)">
              {{ getBusinessTypeName(scope.row.businessType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="levelCount" label="审批级别" width="100" align="center">
          <template slot-scope="scope">
            <el-tag type="success">{{ scope.row.levelCount || 0 }}级</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="280" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
              v-hasPermi="['approval:multilevel:query']"
            >查看</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['approval:multilevel:edit']"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-copy-document"
              @click="handleCopy(scope.row)"
              v-hasPermi="['approval:multilevel:add']"
            >复制</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['approval:multilevel:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 创建/编辑多级审批对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <multilevel-form
        v-if="open"
        ref="multilevelForm"
        :form-data="form"
        :is-edit="isEdit"
        @submit="handleSubmit"
        @cancel="cancel"
      />
    </el-dialog>

    <!-- 查看多级审批详情对话框 -->
    <el-dialog title="多级审批详情" :visible.sync="viewOpen" width="1000px" append-to-body>
      <multilevel-detail
        v-if="viewOpen"
        :workflow-id="currentWorkflowId"
      />
    </el-dialog>
  </div>
</template>

<script>
import { listWorkflow, getWorkflow, delWorkflow, getWorkflowOptions } from "@/api/approval/workflow";
import { getWorkflowLevels } from "@/api/approval/multilevel";
import MultilevelForm from './components/MultilevelForm';
import MultilevelDetail from './components/MultilevelDetail';

export default {
  name: "MultilevelApproval",
  dicts: ['sys_normal_disable'],
  components: {
    MultilevelForm,
    MultilevelDetail
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工作流表格数据
      workflowList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看弹出层
      viewOpen: false,
      // 是否展开，默认全部展开
      isExpandAll: true,
      // 重新渲染表格状态
      refreshTable: true,
      // 是否编辑模式
      isEdit: false,
      // 当前工作流ID
      currentWorkflowId: null,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        workflowName: null,
        businessType: null
      },
      // 表单参数
      form: {},
      // 业务类型选项
      businessTypeOptions: []
    };
  },
  created() {
    this.getList();
    this.getBusinessTypeOptions();
  },
  methods: {
    /** 查询工作流列表 */
    getList() {
      this.loading = true;
      listWorkflow(this.queryParams).then(response => {
        // 为每个工作流获取级别信息
        const promises = response.rows.map(workflow => {
          return getWorkflowLevels(workflow.workflowId).then(levelResponse => {
            workflow.levelCount = levelResponse.data ? levelResponse.data.length : 0;
            return workflow;
          }).catch(() => {
            workflow.levelCount = 0;
            return workflow;
          });
        });
        
        Promise.all(promises).then(workflowsWithLevels => {
          this.workflowList = workflowsWithLevels;
          this.total = response.total;
          this.loading = false;
        });
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleCreate() {
      this.reset();
      this.open = true;
      this.title = "创建多级审批";
      this.isEdit = false;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const workflowId = row.workflowId;
      getWorkflow(workflowId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改多级审批";
        this.isEdit = true;
      });
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.currentWorkflowId = row.workflowId;
      this.viewOpen = true;
    },
    /** 复制按钮操作 */
    handleCopy(row) {
      const workflowId = row.workflowId;
      getWorkflow(workflowId).then(response => {
        this.form = { ...response.data };
        this.form.workflowId = null;
        this.form.workflowName = this.form.workflowName + " - 副本";
        this.form.workflowCode = null;
        this.open = true;
        this.title = "复制多级审批";
        this.isEdit = false;
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const workflowIds = row.workflowId || this.ids;
      this.$modal.confirm('是否确认删除工作流编号为"' + workflowIds + '"的数据项？').then(function() {
        return delWorkflow(workflowIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 展开/折叠操作 */
    toggleExpandAll() {
      this.refreshTable = false;
      this.isExpandAll = !this.isExpandAll;
      this.$nextTick(() => {
        this.refreshTable = true;
      });
    },
    /** 提交表单 */
    handleSubmit(formData) {
      // 表单提交逻辑在子组件中处理
      this.open = false;
      this.getList();
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        workflowId: null,
        workflowName: null,
        businessType: null,
        description: null,
        levels: []
      };
      this.resetForm("form");
    },
    /** 获取业务类型选项 */
    getBusinessTypeOptions() {
      getWorkflowOptions().then(res => {
        if (res.code === 200 && res.data) {
          // 使用Map对象确保每个业务类型只出现一次
          const businessTypeMap = new Map();
          res.data.forEach(item => {
            if (!businessTypeMap.has(item.businessType)) {
              businessTypeMap.set(item.businessType, {
                value: item.businessType,
                label: item.businessTypeName || item.businessType
              });
            }
          });

          // 将Map转换回数组
          this.businessTypeOptions = Array.from(businessTypeMap.values());
        }
      });
    },
    /** 获取业务类型名称 */
    getBusinessTypeName(businessType) {
      const option = this.businessTypeOptions.find(item => item.value === businessType);
      return option ? option.label : businessType;
    },
    /** 获取业务类型标签类型 */
    getBusinessTypeTagType(businessType) {
      const typeMap = {
        'MATERIAL_APPROVAL': 'primary',
        'MATERIAL_BILL': 'success',
        'PURCHASE_APPROVAL': 'warning',
        'EXPENSE_APPROVAL': 'danger'
      };
      return typeMap[businessType] || 'info';
    }
  }
};
</script>
