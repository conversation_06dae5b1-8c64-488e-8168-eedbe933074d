<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="page-title">
        <h2><i class="el-icon-key"></i> 设备认证管理</h2>
        <p>管理设备客户端的认证信息，支持账号密码和授权码认证</p>
      </div>
      <div class="page-actions">
        <el-button type="primary" icon="el-icon-plus" @click="showCreateDialog">
          创建认证
        </el-button>
        <el-button type="success" icon="el-icon-upload2" @click="showBatchDialog">
          批量导入
        </el-button>
        <el-button type="info" icon="el-icon-refresh" @click="refreshData" :loading="loading">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 设备类型选择和预览 -->
    <el-card class="preview-card" v-if="showPreview">
      <div slot="header">
        <span><i class="el-icon-view"></i> 客户端ID预览</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="showPreview = false">
          <i class="el-icon-close"></i>
        </el-button>
      </div>
      <el-form label-width="80px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="设备类型">
              <el-select v-model="previewDeviceType" placeholder="选择设备类型" @change="updatePreview">
                <el-option
                  v-for="(prefix, type) in deviceTypes"
                  :key="type"
                  :label="type"
                  :value="type"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备ID">
              <el-input v-model="previewDeviceId" placeholder="输入设备ID" @input="updatePreview" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <div class="preview-result">
              <div class="preview-label">生成的客户端ID:</div>
              <div class="preview-value">{{ previewResult.clientId || '请选择设备类型和输入设备ID' }}</div>
              <div class="preview-label">归属服务器:</div>
              <div class="preview-value">{{ previewResult.serverClientId || '-' }}</div>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 统计信息 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <div class="stat-item">
          <div class="stat-icon device">
            <i class="el-icon-mobile-phone"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.totalDevices }}</div>
            <div class="stat-label">总设备数</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-item">
          <div class="stat-icon active">
            <i class="el-icon-success"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.activeDevices }}</div>
            <div class="stat-label">活跃设备</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-item">
          <div class="stat-icon types">
            <i class="el-icon-menu"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ Object.keys(deviceTypes).length }}</div>
            <div class="stat-label">设备类型</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-item">
          <div class="stat-icon servers">
            <i class="el-icon-connection"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.serverCount }}</div>
            <div class="stat-label">服务器数</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 搜索和筛选 -->
    <el-card class="filter-card">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索设备ID、客户端ID或用户名"
            prefix-icon="el-icon-search"
            clearable
            @input="handleSearch"
          />
        </el-col>
        <el-col :span="4">
          <el-select v-model="deviceTypeFilter" placeholder="设备类型" clearable @change="handleSearch">
            <el-option
              v-for="(prefix, type) in deviceTypes"
              :key="type"
              :label="type"
              :value="type"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="statusFilter" placeholder="状态" clearable @change="handleSearch">
            <el-option label="活跃" value="active" />
            <el-option label="禁用" value="disabled" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" icon="el-icon-view" @click="showPreview = !showPreview">
            {{ showPreview ? '隐藏' : '显示' }}预览
          </el-button>
          <el-button type="warning" icon="el-icon-connection" @click="testAllConnections">
            批量测试
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 认证列表 -->
    <el-card class="list-card">
      <div slot="header">
        <span><i class="el-icon-list"></i> 设备认证列表</span>
        <div style="float: right;">
          <el-tag type="info" size="mini">共 {{ filteredList.length }} 条记录</el-tag>
        </div>
      </div>

      <el-table
        :data="paginatedList"
        v-loading="loading"
        element-loading-text="加载中..."
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="deviceId" label="设备ID" width="120">
          <template slot-scope="scope">
            <el-tag size="mini" :type="getDeviceTypeTag(scope.row.deviceType)">
              {{ scope.row.deviceId }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="deviceType" label="设备类型" width="140">
          <template slot-scope="scope">
            <span class="device-type">{{ scope.row.deviceType }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="clientId" label="客户端ID" min-width="180">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.clientId" placement="top">
              <span class="client-id">{{ scope.row.clientId }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        
        <el-table-column prop="username" label="用户名" width="150">
          <template slot-scope="scope">
            <span class="username">{{ scope.row.username }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="serverClientId" label="归属服务器" width="180">
          <template slot-scope="scope">
            <el-tag size="small" type="info">{{ scope.row.serverClientId }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="80">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.active"
              active-color="#13ce66"
              inactive-color="#ff4949"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        
        <el-table-column prop="createTime" label="创建时间" width="160">
          <template slot-scope="scope">
            {{ formatTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-view"
              @click="viewDetail(scope.row)"
            >
              详情
            </el-button>
            <el-button
              size="mini"
              type="success"
              icon="el-icon-connection"
              @click="testConnection(scope.row)"
              :loading="scope.row.testing"
            >
              测试
            </el-button>
            <el-dropdown @command="handleCommand" trigger="click">
              <el-button size="mini" type="text">
                更多<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :command="{action: 'edit', row: scope.row}">
                  <i class="el-icon-edit"></i> 编辑
                </el-dropdown-item>
                <el-dropdown-item :command="{action: 'resetPassword', row: scope.row}">
                  <i class="el-icon-refresh"></i> 重置密码
                </el-dropdown-item>
                <el-dropdown-item :command="{action: 'delete', row: scope.row}" divided>
                  <i class="el-icon-delete"></i> 删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="filteredList.length"
        />
      </div>
    </el-card>

    <!-- 创建认证对话框 -->
    <el-dialog
      title="创建设备认证"
      :visible.sync="createDialogVisible"
      width="600px"
      :before-close="closeCreateDialog"
    >
      <el-form
        ref="createForm"
        :model="createForm"
        :rules="createRules"
        label-width="120px"
      >
        <el-form-item label="设备ID" prop="deviceId">
          <el-input v-model="createForm.deviceId" placeholder="请输入设备ID" @input="updateCreatePreview" />
        </el-form-item>
        <el-form-item label="设备类型" prop="deviceType">
          <el-select v-model="createForm.deviceType" placeholder="请选择设备类型" @change="updateCreatePreview">
            <el-option
              v-for="(prefix, type) in deviceTypes"
              :key="type"
              :label="type"
              :value="type"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="用户名" prop="username">
          <el-input v-model="createForm.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="createForm.password"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="授权码">
          <el-input v-model="createForm.authCode" placeholder="可选，留空自动生成" />
        </el-form-item>
        <el-form-item label="预览信息">
          <div class="create-preview">
            <div><strong>客户端ID:</strong> {{ createPreview.clientId || '请填写设备信息' }}</div>
            <div><strong>归属服务器:</strong> {{ createPreview.serverClientId || '-' }}</div>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeCreateDialog">取消</el-button>
        <el-button type="primary" @click="submitCreate" :loading="createLoading">
          创建
        </el-button>
      </div>
    </el-dialog>

    <!-- 批量导入对话框 -->
    <el-dialog
      title="批量导入设备认证"
      :visible.sync="batchDialogVisible"
      width="800px"
      :before-close="closeBatchDialog"
    >
      <div class="batch-import">
        <el-alert
          title="批量导入说明"
          type="info"
          :closable="false"
          style="margin-bottom: 20px;"
        >
          <div slot="description">
            <p>1. 支持JSON格式的设备认证数据导入</p>
            <p>2. 每个设备需包含：deviceId, deviceType, username, password</p>
            <p>3. authCode为可选字段，留空将自动生成</p>
          </div>
        </el-alert>
        
        <el-input
          v-model="batchData"
          type="textarea"
          :rows="10"
          placeholder="请输入JSON格式的设备数据，例如：
[
  {
    &quot;deviceId&quot;: &quot;001&quot;,
    &quot;deviceType&quot;: &quot;出库防错客户端&quot;,
    &quot;username&quot;: &quot;device_001&quot;,
    &quot;password&quot;: &quot;password123&quot;
  }
]"
        />
        
        <div class="batch-actions" style="margin-top: 20px;">
          <el-button @click="validateBatchData">验证数据</el-button>
          <el-button type="primary" @click="submitBatch" :loading="batchLoading">
            导入
          </el-button>
        </div>
        
        <div v-if="batchValidation.length > 0" class="validation-result">
          <h4>验证结果:</h4>
          <el-table :data="batchValidation" size="small" max-height="200">
            <el-table-column prop="deviceId" label="设备ID" width="100" />
            <el-table-column prop="status" label="状态" width="80">
              <template slot-scope="scope">
                <el-tag :type="scope.row.valid ? 'success' : 'danger'" size="mini">
                  {{ scope.row.valid ? '有效' : '无效' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="message" label="说明" />
          </el-table>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeBatchDialog">取消</el-button>
      </div>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog
      title="设备认证详情"
      :visible.sync="detailDialogVisible"
      width="700px"
      :before-close="closeDetailDialog"
    >
      <div v-if="selectedDevice" class="device-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="设备ID">
            {{ selectedDevice.deviceId }}
          </el-descriptions-item>
          <el-descriptions-item label="设备类型">
            {{ selectedDevice.deviceType }}
          </el-descriptions-item>
          <el-descriptions-item label="客户端ID" :span="2">
            {{ selectedDevice.clientId }}
          </el-descriptions-item>
          <el-descriptions-item label="用户名">
            {{ selectedDevice.username }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="selectedDevice.active ? 'success' : 'danger'">
              {{ selectedDevice.active ? '活跃' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="归属服务器" :span="2">
            {{ selectedDevice.serverClientId }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatTime(selectedDevice.createTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="授权码">
            {{ selectedDevice.authCode || '无' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getSupportedDeviceTypes,
  previewClientId,
  createDeviceAuth,
  validateDeviceAuth,
  batchCreateDeviceAuth,
  testDeviceConnection,
  getAuthStatistics,
  getDeviceAuthList
} from '@/api/emqx/deviceAuth'

export default {
  name: 'DeviceAuth',
  data() {
    return {
      loading: false,
      deviceTypes: {},
      authList: [],
      filteredList: [],
      
      // 搜索和筛选
      searchKeyword: '',
      deviceTypeFilter: '',
      statusFilter: '',
      
      // 分页
      pagination: {
        currentPage: 1,
        pageSize: 20
      },
      
      // 预览
      showPreview: false,
      previewDeviceType: '',
      previewDeviceId: '',
      previewResult: {},
      
      // 统计
      stats: {
        totalDevices: 0,
        activeDevices: 0,
        serverCount: 0
      },
      
      // 创建对话框
      createDialogVisible: false,
      createLoading: false,
      createForm: {
        deviceId: '',
        deviceType: '',
        username: '',
        password: '',
        authCode: ''
      },
      createPreview: {},
      createRules: {
        deviceId: [
          { required: true, message: '请输入设备ID', trigger: 'blur' }
        ],
        deviceType: [
          { required: true, message: '请选择设备类型', trigger: 'change' }
        ],
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
        ]
      },
      
      // 批量导入
      batchDialogVisible: false,
      batchLoading: false,
      batchData: '',
      batchValidation: [],
      
      // 详情对话框
      detailDialogVisible: false,
      selectedDevice: null,
      
      // 选中的行
      selectedRows: []
    }
  },
  
  computed: {
    paginatedList() {
      // 确保 filteredList 是数组
      if (!Array.isArray(this.filteredList)) {
        return []
      }
      const start = (this.pagination.currentPage - 1) * this.pagination.pageSize
      const end = start + this.pagination.pageSize
      return this.filteredList.slice(start, end)
    }
  },
  
  created() {
    this.loadData()
  },
  
  methods: {
    async loadData() {
      this.loading = true
      try {
        await Promise.all([
          this.loadDeviceTypes(),
          this.loadAuthList(),
          this.loadStats()
        ])
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },
    
    async loadDeviceTypes() {
      try {
        const response = await getSupportedDeviceTypes()
        if (response.code === 200) {
          this.deviceTypes = response.data || {}
        }
      } catch (error) {
        console.error('加载设备类型失败:', error)
      }
    },
    
    async loadAuthList() {
      try {
        const response = await getDeviceAuthList()
        if (response.code === 200) {
          // 确保 authList 是数组
          this.authList = Array.isArray(response.data) ? response.data : []
          this.handleSearch()
        } else {
          // 如果响应失败，设置为空数组
          this.authList = []
          this.handleSearch()
        }
      } catch (error) {
        console.error('加载设备认证列表失败:', error)
        this.$message.error('加载设备认证列表失败')
        // 出错时也设置为空数组
        this.authList = []
        this.handleSearch()
      }
    },
    
    async loadStats() {
      try {
        const response = await getAuthStatistics()
        if (response.code === 200) {
          this.stats = response.data || this.stats
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },
    
    handleSearch() {
      // 确保 authList 是数组
      let filtered = Array.isArray(this.authList) ? this.authList : []

      // 关键词搜索
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        filtered = filtered.filter(item =>
          item.deviceId.toLowerCase().includes(keyword) ||
          item.clientId.toLowerCase().includes(keyword) ||
          item.username.toLowerCase().includes(keyword)
        )
      }

      // 设备类型筛选
      if (this.deviceTypeFilter) {
        filtered = filtered.filter(item => item.deviceType === this.deviceTypeFilter)
      }

      // 状态筛选
      if (this.statusFilter) {
        const isActive = this.statusFilter === 'active'
        filtered = filtered.filter(item => item.active === isActive)
      }

      // 确保 filteredList 是数组
      this.filteredList = Array.isArray(filtered) ? filtered : []
      this.pagination.currentPage = 1
    },
    
    async updatePreview() {
      if (this.previewDeviceType && this.previewDeviceId) {
        try {
          const response = await previewClientId(this.previewDeviceType, this.previewDeviceId)
          if (response.code === 200) {
            this.previewResult = response.data
          }
        } catch (error) {
          console.error('预览失败:', error)
        }
      } else {
        this.previewResult = {}
      }
    },
    
    async updateCreatePreview() {
      if (this.createForm.deviceType && this.createForm.deviceId) {
        try {
          const response = await previewClientId(this.createForm.deviceType, this.createForm.deviceId)
          if (response.code === 200) {
            this.createPreview = response.data
          }
        } catch (error) {
          console.error('预览失败:', error)
        }
      } else {
        this.createPreview = {}
      }
    },
    
    refreshData() {
      this.loadData()
    },
    
    showCreateDialog() {
      this.createDialogVisible = true
    },
    
    closeCreateDialog() {
      this.createDialogVisible = false
      this.resetCreateForm()
    },
    
    resetCreateForm() {
      this.$refs.createForm?.resetFields()
      this.createForm = {
        deviceId: '',
        deviceType: '',
        username: '',
        password: '',
        authCode: ''
      }
      this.createPreview = {}
    },
    
    submitCreate() {
      this.$refs.createForm.validate(async (valid) => {
        if (valid) {
          this.createLoading = true
          try {
            const response = await createDeviceAuth(this.createForm)
            if (response.code === 200) {
              this.$message.success('创建成功')
              this.closeCreateDialog()
              this.loadAuthList()
            } else {
              this.$message.error('创建失败: ' + response.msg)
            }
          } catch (error) {
            this.$message.error('创建失败')
          } finally {
            this.createLoading = false
          }
        }
      })
    },
    
    showBatchDialog() {
      this.batchDialogVisible = true
    },
    
    closeBatchDialog() {
      this.batchDialogVisible = false
      this.batchData = ''
      this.batchValidation = []
    },
    
    validateBatchData() {
      try {
        const data = JSON.parse(this.batchData)
        if (!Array.isArray(data)) {
          this.$message.error('数据格式错误，请输入数组格式')
          return
        }
        
        this.batchValidation = data.map(item => {
          const validation = {
            deviceId: item.deviceId || '未知',
            valid: true,
            message: '数据有效'
          }
          
          if (!item.deviceId) {
            validation.valid = false
            validation.message = '缺少设备ID'
          } else if (!item.deviceType) {
            validation.valid = false
            validation.message = '缺少设备类型'
          } else if (!item.username) {
            validation.valid = false
            validation.message = '缺少用户名'
          } else if (!item.password) {
            validation.valid = false
            validation.message = '缺少密码'
          } else if (!this.deviceTypes[item.deviceType]) {
            validation.valid = false
            validation.message = '不支持的设备类型'
          }
          
          return validation
        })
        
        this.$message.success('数据验证完成')
      } catch (error) {
        this.$message.error('JSON格式错误: ' + error.message)
      }
    },
    
    async submitBatch() {
      if (!this.batchData) {
        this.$message.error('请输入批量数据')
        return
      }
      
      try {
        const data = JSON.parse(this.batchData)
        this.batchLoading = true
        
        const response = await batchCreateDeviceAuth({ devices: data })
        if (response.code === 200) {
          const result = response.data
          this.$message.success(`批量导入完成：成功 ${result.success} 条，失败 ${result.failure} 条`)
          this.closeBatchDialog()
          this.loadAuthList()
        } else {
          this.$message.error('批量导入失败: ' + response.msg)
        }
      } catch (error) {
        this.$message.error('批量导入失败')
      } finally {
        this.batchLoading = false
      }
    },
    
    viewDetail(row) {
      this.selectedDevice = row
      this.detailDialogVisible = true
    },
    
    closeDetailDialog() {
      this.detailDialogVisible = false
      this.selectedDevice = null
    },
    
    async testConnection(row) {
      this.$set(row, 'testing', true)
      try {
        const response = await testDeviceConnection({
          clientId: row.clientId,
          username: row.username,
          password: row.password || 'test_password'
        })
        if (response.code === 200) {
          this.$message.success('连接测试成功')
        } else {
          this.$message.error('连接测试失败: ' + response.msg)
        }
      } catch (error) {
        this.$message.error('连接测试失败')
      } finally {
        this.$set(row, 'testing', false)
      }
    },
    
    testAllConnections() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择要测试的设备')
        return
      }
      
      this.$confirm(`确定要测试选中的 ${this.selectedRows.length} 个设备的连接吗？`, '确认操作', {
        type: 'warning'
      }).then(() => {
        this.selectedRows.forEach(row => {
          this.testConnection(row)
        })
      })
    },
    
    handleStatusChange(row) {
      this.$message.info('状态切换功能开发中...')
    },
    
    handleCommand(command) {
      const { action, row } = command
      switch (action) {
        case 'edit':
          this.$message.info('编辑功能开发中...')
          break
        case 'resetPassword':
          this.$message.info('重置密码功能开发中...')
          break
        case 'delete':
          this.deleteDevice(row)
          break
      }
    },
    
    deleteDevice(row) {
      this.$confirm('确定要删除此设备认证吗？删除后设备将无法连接。', '确认操作', {
        type: 'warning'
      }).then(() => {
        this.$message.info('删除功能开发中...')
      })
    },
    
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.currentPage = 1
    },
    
    handleCurrentChange(val) {
      this.pagination.currentPage = val
    },
    
    // 工具方法
    getDeviceTypeTag(deviceType) {
      const tagMap = {
        '出库防错客户端': 'danger',
        '门禁设备客户端': 'success',
        '视频监控客户端': 'primary',
        '智能导寻客户端': 'warning',
        '库房组态客户端': 'info',
        '称重设备客户端': 'danger'
      }
      return tagMap[deviceType] || ''
    },
    
    formatTime(time) {
      if (!time) return '-'
      return new Date(time).toLocaleString()
    }
  }
}
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title h2 {
  margin: 0;
  color: #303133;
}

.page-title p {
  margin: 5px 0 0 0;
  color: #909399;
  font-size: 14px;
}

.preview-card {
  margin-bottom: 20px;
}

.preview-result {
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
}

.preview-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 5px;
}

.preview-value {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  margin-right: 15px;
}

.stat-icon.device { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.stat-icon.active { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.stat-icon.types { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.stat-icon.servers { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.filter-card, .list-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.client-id, .username {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.device-type {
  font-weight: 500;
}

.create-preview {
  padding: 10px;
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  font-size: 14px;
}

.create-preview div {
  margin-bottom: 5px;
}

.batch-import {
  padding: 10px 0;
}

.validation-result {
  margin-top: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
}

.device-detail {
  padding: 10px 0;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
