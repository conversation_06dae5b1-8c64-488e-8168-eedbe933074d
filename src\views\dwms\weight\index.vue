<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="传感器ID" prop="sensorId">
        <el-input
          v-model="queryParams.sensorId"
          placeholder="请输入传感器ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="库房ID" prop="warehouseId">
        <el-input-number v-model="queryParams.warehouseId" placeholder="请输入库房ID" clearable />
      </el-form-item>
      <el-form-item label="时间范围">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['dwms:weight:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['dwms:weight:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['dwms:weight:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-view"
          size="mini"
          @click="handleRealtime"
        >实时监控</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 实时监控面板 -->
    <el-card v-if="showRealtime" class="mb8">
      <div slot="header">
        <span>实时重量监控</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="showRealtime = false">关闭</el-button>
      </div>
      <el-row :gutter="20">
        <el-col :span="6" v-for="sensor in realtimeSensors" :key="sensor.sensorId">
          <el-card class="sensor-card">
            <div class="sensor-info">
              <h4>{{ sensor.sensorId }}</h4>
              <div class="weight-value">{{ sensor.weight }} kg</div>
              <div class="sensor-status" :class="sensor.status">
                {{ sensor.status === 'online' ? '在线' : '离线' }}
              </div>
              <div class="update-time">{{ sensor.updateTime }}</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>

    <el-table v-loading="loading" :data="weightList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="记录ID" align="center" prop="id" />
      <el-table-column label="传感器ID" align="center" prop="sensorId" />
      <el-table-column label="重量值" align="center" prop="weight">
        <template slot-scope="scope">
          <span>{{ scope.row.weight }} kg</span>
        </template>
      </el-table-column>
      <el-table-column label="库房ID" align="center" prop="warehouseId" />
      <el-table-column label="位置信息" align="center" prop="location" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 'normal' ? 'success' : 'danger'">
            {{ scope.row.status === 'normal' ? '正常' : '异常' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="记录时间" align="center" prop="timestamp" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.timestamp, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['dwms:weight:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加重量数据对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="传感器ID" prop="sensorId">
          <el-input v-model="form.sensorId" placeholder="请输入传感器ID" />
        </el-form-item>
        <el-form-item label="重量值" prop="weight">
          <el-input-number v-model="form.weight" placeholder="请输入重量值" :precision="2" />
        </el-form-item>
        <el-form-item label="库房ID" prop="warehouseId">
          <el-input-number v-model="form.warehouseId" placeholder="请输入库房ID" />
        </el-form-item>
        <el-form-item label="位置信息" prop="location">
          <el-input v-model="form.location" placeholder="请输入位置信息" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择状态">
            <el-option label="正常" value="normal" />
            <el-option label="异常" value="abnormal" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog title="重量数据详情" :visible.sync="viewOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="记录ID">{{ viewData.id }}</el-descriptions-item>
        <el-descriptions-item label="传感器ID">{{ viewData.sensorId }}</el-descriptions-item>
        <el-descriptions-item label="重量值">{{ viewData.weight }} kg</el-descriptions-item>
        <el-descriptions-item label="库房ID">{{ viewData.warehouseId }}</el-descriptions-item>
        <el-descriptions-item label="位置信息">{{ viewData.location }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="viewData.status === 'normal' ? 'success' : 'danger'">
            {{ viewData.status === 'normal' ? '正常' : '异常' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="记录时间">{{ parseTime(viewData.timestamp, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(viewData.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
// import { listWeight, getWeight, delWeight, addWeight } from "@/api/dwms/weight";

export default {
  name: "Weight",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 重量数据表格数据
      weightList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查看详情弹出层
      viewOpen: false,
      // 查看数据
      viewData: {},
      // 显示实时监控
      showRealtime: false,
      // 实时传感器数据
      realtimeSensors: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        sensorId: null,
        warehouseId: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        sensorId: [
          { required: true, message: "传感器ID不能为空", trigger: "blur" }
        ],
        weight: [
          { required: true, message: "重量值不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询重量数据列表 */
    getList() {
      this.loading = true;

      // 使用模拟数据
      setTimeout(() => {
        this.weightList = [
          {
            id: 1,
            sensorId: 'WS001',
            weight: 125.6,
            warehouseId: 1,
            location: 'A区-01货架',
            status: 'normal',
            timestamp: new Date().toISOString(),
            createTime: new Date().toISOString()
          },
          {
            id: 2,
            sensorId: 'WS002',
            weight: 89.3,
            warehouseId: 1,
            location: 'A区-02货架',
            status: 'normal',
            timestamp: new Date().toISOString(),
            createTime: new Date().toISOString()
          },
          {
            id: 3,
            sensorId: 'WS003',
            weight: 0.0,
            warehouseId: 1,
            location: 'B区-01货架',
            status: 'abnormal',
            timestamp: new Date().toISOString(),
            createTime: new Date().toISOString()
          }
        ];
        this.total = this.weightList.length;
        this.loading = false;
      }, 500);

      // 实际API调用（当后端准备好时启用）
      /*
      listWeight(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.weightList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        console.error('获取重量数据失败:', error);
        this.loading = false;
        this.$message.error('获取重量数据失败');
      });
      */
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        sensorId: null,
        weight: null,
        warehouseId: null,
        location: null,
        status: "normal"
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加重量数据";
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 模拟新增成功
          this.$message.success("新增成功");
          this.open = false;
          this.getList();

          // 实际API调用（当后端准备好时启用）
          /*
          addWeight(this.form).then(response => {
            this.$message.success("新增成功");
            this.open = false;
            this.getList();
          }).catch(error => {
            this.$message.error("新增失败");
          });
          */
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('是否确认删除重量数据编号为"' + ids + '"的数据项？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 模拟删除成功
        this.getList();
        this.$message.success("删除成功");

        // 实际API调用（当后端准备好时启用）
        /*
        return delWeight(ids);
        }).then(() => {
          this.getList();
          this.$message.success("删除成功");
        }).catch(error => {
          this.$message.error("删除失败");
        */
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('dwms/weight/export', {
        ...this.queryParams
      }, `weight_${new Date().getTime()}.xlsx`)
    },
    /** 查看详情 */
    handleView(row) {
      this.viewData = row;
      this.viewOpen = true;
    },
    /** 实时监控 */
    handleRealtime() {
      this.showRealtime = !this.showRealtime;
      if (this.showRealtime) {
        this.loadRealtimeData();
        // 每5秒刷新一次实时数据
        this.realtimeTimer = setInterval(() => {
          this.loadRealtimeData();
        }, 5000);
      } else {
        if (this.realtimeTimer) {
          clearInterval(this.realtimeTimer);
        }
      }
    },
    /** 加载实时数据 */
    loadRealtimeData() {
      // 模拟实时数据，实际应该调用API
      this.realtimeSensors = [
        {
          sensorId: 'WS001',
          weight: (Math.random() * 1000).toFixed(2),
          status: 'online',
          updateTime: new Date().toLocaleTimeString()
        },
        {
          sensorId: 'WS002',
          weight: (Math.random() * 1000).toFixed(2),
          status: 'online',
          updateTime: new Date().toLocaleTimeString()
        },
        {
          sensorId: 'WS003',
          weight: (Math.random() * 1000).toFixed(2),
          status: Math.random() > 0.8 ? 'offline' : 'online',
          updateTime: new Date().toLocaleTimeString()
        }
      ];
    }
  },
  beforeDestroy() {
    if (this.realtimeTimer) {
      clearInterval(this.realtimeTimer);
    }
  }
};
</script>

<style scoped>
.sensor-card {
  margin-bottom: 10px;
}

.sensor-info {
  text-align: center;
}

.weight-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin: 10px 0;
}

.sensor-status.online {
  color: #67C23A;
}

.sensor-status.offline {
  color: #F56C6C;
}

.update-time {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style>
