import request from '@/utils/request'

// MQTT 认证管理 API

/**
 * 获取客户端凭据列表
 */
export function getClientCredentials(params) {
  return request({
    url: '/api/mqtt/auth/credentials',
    method: 'get',
    params
  })
}

/**
 * 添加客户端凭据
 */
export function addClientCredential(data) {
  return request({
    url: '/api/mqtt/auth/credentials',
    method: 'post',
    data
  })
}

/**
 * 更新客户端凭据
 */
export function updateClientCredential(username, data) {
  return request({
    url: `/api/mqtt/auth/credentials/${username}`,
    method: 'put',
    data
  })
}

/**
 * 删除客户端凭据
 */
export function deleteClientCredential(username) {
  return request({
    url: `/api/mqtt/auth/credentials/${username}`,
    method: 'delete'
  })
}

/**
 * 获取客户端凭据详情
 */
export function getClientCredentialDetails(username) {
  return request({
    url: `/api/mqtt/auth/credentials/${username}`,
    method: 'get'
  })
}

/**
 * 批量导入客户端凭据
 */
export function batchImportCredentials(data) {
  return request({
    url: '/api/mqtt/auth/credentials/batch-import',
    method: 'post',
    data
  })
}

/**
 * 导出客户端凭据
 */
export function exportCredentials(params) {
  return request({
    url: '/api/mqtt/auth/credentials/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 验证客户端凭据
 */
export function validateCredential(data) {
  return request({
    url: '/api/mqtt/auth/validate',
    method: 'post',
    data
  })
}

/**
 * 获取认证统计
 */
export function getAuthStatistics() {
  return request({
    url: '/api/mqtt/auth/statistics',
    method: 'get'
  })
}

/**
 * 获取认证日志
 */
export function getAuthLogs(params) {
  return request({
    url: '/api/mqtt/auth/logs',
    method: 'get',
    params
  })
}

/**
 * 重置客户端密码
 */
export function resetClientPassword(username, newPassword) {
  return request({
    url: `/api/mqtt/auth/credentials/${username}/reset-password`,
    method: 'post',
    data: { newPassword }
  })
}

/**
 * 启用/禁用客户端
 */
export function toggleClientStatus(username, enabled) {
  return request({
    url: `/api/mqtt/auth/credentials/${username}/toggle`,
    method: 'post',
    data: { enabled }
  })
}

/**
 * 搜索客户端凭据
 */
export function searchCredentials(keyword, params) {
  return request({
    url: '/api/mqtt/auth/credentials/search',
    method: 'get',
    params: { keyword, ...params }
  })
}
