/**
 * 路由重复问题修复补丁
 * 用于解决Vue Router重复路由定义警告和布局嵌套问题
 */
import router from '@/router'

// 标记是否已应用补丁
let isPatched = false

// 存储已添加的路由名称
const addedRouteNames = new Set()

/**
 * 应用路由修复补丁
 */
export function applyRouteFixPatch() {
  if (isPatched) {
    console.log('[路由补丁] 补丁已应用，跳过重复处理')
    return
  }

  console.log('[路由补丁] 开始应用路由重复修复补丁...')

  // 保存原始的addRoutes方法
  const originalAddRoutes = router.addRoutes

  // 重写addRoutes方法，添加去重逻辑
  router.addRoutes = function(routes) {
    console.log('[路由补丁] 拦截addRoutes调用，路由数量:', routes.length)

    // 过滤重复路由
    const filteredRoutes = filterDuplicateRoutes(routes)

    console.log('[路由补丁] 过滤后路由数量:', filteredRoutes.length)

    if (filteredRoutes.length > 0) {
      return originalAddRoutes.call(this, filteredRoutes)
    }
  }

  // 清理现有路由中的重复项
  cleanExistingRoutes()

  // 强制清理Vue Router内部的路由表
  clearVueRouterCache()

  isPatched = true
  console.log('[路由补丁] 路由修复补丁应用完成')
}

/**
 * 过滤重复路由
 */
function filterDuplicateRoutes(routes) {
  const filtered = []
  const seenPaths = new Set()

  routes.forEach(route => {
    // 生成唯一的路由标识符
    const routeKey = generateRouteKey(route)

    if (route.name && addedRouteNames.has(route.name)) {
      console.log(`[路由补丁] 跳过重复路由名称: ${route.name}`)
      return
    }

    // 检查路径重复
    if (route.path && route.path !== '#' && seenPaths.has(route.path)) {
      console.log(`[路由补丁] 跳过重复路径: ${route.path}`)
      return
    }

    // 为没有名称的路由生成唯一名称
    if (!route.name && route.path && route.path !== '#') {
      route.name = generateUniqueRouteName(route)
    }

    // 递归处理子路由
    if (route.children && route.children.length > 0) {
      route.children = filterDuplicateRoutes(route.children)
    }

    // 记录路由名称和路径
    if (route.name) {
      addedRouteNames.add(route.name)
    }
    if (route.path && route.path !== '#') {
      seenPaths.add(route.path)
    }

    filtered.push(route)
  })

  return filtered
}

/**
 * 清理现有路由中的重复项
 */
function cleanExistingRoutes() {
  console.log('[路由补丁] 清理现有路由重复项...')
  
  // 获取当前路由表
  const routes = router.options.routes || []
  
  // 收集已存在的路由名称
  function collectRouteNames(routes) {
    routes.forEach(route => {
      if (route.name) {
        addedRouteNames.add(route.name)
      }
      if (route.children) {
        collectRouteNames(route.children)
      }
    })
  }
  
  collectRouteNames(routes)
  console.log('[路由补丁] 已收集现有路由名称:', addedRouteNames.size, '个')
}

/**
 * 重置路由补丁状态（用于测试）
 */
export function resetRouteFixPatch() {
  isPatched = false
  addedRouteNames.clear()
  console.log('[路由补丁] 补丁状态已重置')
}

/**
 * 生成路由唯一标识符
 */
function generateRouteKey(route) {
  const parts = []
  if (route.path) parts.push(route.path)
  if (route.name) parts.push(route.name)
  if (route.component) parts.push(route.component.toString())
  return parts.join('|')
}

/**
 * 生成唯一的路由名称
 */
function generateUniqueRouteName(route) {
  let baseName = route.path ? route.path.replace(/[^a-zA-Z0-9]/g, '') : 'Route'
  if (!baseName) baseName = 'Route'

  let counter = 1
  let uniqueName = baseName

  while (addedRouteNames.has(uniqueName)) {
    uniqueName = `${baseName}${counter}`
    counter++
  }

  return uniqueName
}

/**
 * 强制清理Vue Router缓存
 */
function clearVueRouterCache() {
  console.log('[路由补丁] 清理Vue Router缓存...')

  // 重新创建路由匹配器
  const newRouter = new router.constructor({
    mode: router.mode,
    base: router.options.base,
    routes: router.options.routes
  })

  // 替换路由匹配器
  router.matcher = newRouter.matcher

  console.log('[路由补丁] Vue Router缓存清理完成')
}

/**
 * 获取补丁状态
 */
export function getRouteFixPatchStatus() {
  return {
    isPatched,
    addedRouteCount: addedRouteNames.size,
    addedRouteNames: Array.from(addedRouteNames)
  }
}
