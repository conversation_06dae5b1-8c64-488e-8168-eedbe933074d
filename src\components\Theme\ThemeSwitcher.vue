<template>
  <div class="theme-switcher">
    <!-- 主题切换按钮 -->
    <el-dropdown @command="handleThemeChange" trigger="click" placement="bottom-end">
      <el-button type="text" class="theme-button">
        <i :class="currentThemeIcon"></i>
        <span v-if="showText">{{ currentThemeName }}</span>
      </el-button>
      
      <el-dropdown-menu slot="dropdown" class="theme-dropdown">
        <div class="theme-header">
          <span>选择主题</span>
        </div>
        
        <div class="theme-options">
          <div
            class="theme-option"
            v-for="theme in themes"
            :key="theme.key"
            :class="{ active: currentTheme === theme.key }"
            @click="handleThemeChange(theme.key)"
          >
            <div class="theme-preview" :style="{ background: theme.preview }">
              <div class="preview-content">
                <div class="preview-header" :style="{ background: theme.colors.primary }"></div>
                <div class="preview-sidebar" :style="{ background: theme.colors.sidebar }"></div>
                <div class="preview-main" :style="{ background: theme.colors.background }"></div>
              </div>
            </div>
            <div class="theme-info">
              <div class="theme-name">{{ theme.name }}</div>
              <div class="theme-desc">{{ theme.description }}</div>
            </div>
            <i class="el-icon-check theme-check" v-if="currentTheme === theme.key"></i>
          </div>
        </div>
        
        <div class="theme-footer">
          <el-button size="mini" type="text" @click="openCustomTheme">
            <i class="el-icon-setting"></i>
            自定义主题
          </el-button>
        </div>
      </el-dropdown-menu>
    </el-dropdown>
    
    <!-- 自定义主题对话框 -->
    <el-dialog
      title="自定义主题"
      :visible.sync="customThemeVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="custom-theme-content">
        <el-form :model="customTheme" label-width="100px" size="small">
          <el-form-item label="主题名称">
            <el-input v-model="customTheme.name" placeholder="请输入主题名称" />
          </el-form-item>
          
          <el-form-item label="主色调">
            <el-color-picker v-model="customTheme.colors.primary" />
            <span class="color-value">{{ customTheme.colors.primary }}</span>
          </el-form-item>
          
          <el-form-item label="成功色">
            <el-color-picker v-model="customTheme.colors.success" />
            <span class="color-value">{{ customTheme.colors.success }}</span>
          </el-form-item>
          
          <el-form-item label="警告色">
            <el-color-picker v-model="customTheme.colors.warning" />
            <span class="color-value">{{ customTheme.colors.warning }}</span>
          </el-form-item>
          
          <el-form-item label="危险色">
            <el-color-picker v-model="customTheme.colors.danger" />
            <span class="color-value">{{ customTheme.colors.danger }}</span>
          </el-form-item>
          
          <el-form-item label="背景色">
            <el-color-picker v-model="customTheme.colors.background" />
            <span class="color-value">{{ customTheme.colors.background }}</span>
          </el-form-item>
          
          <el-form-item label="侧边栏色">
            <el-color-picker v-model="customTheme.colors.sidebar" />
            <span class="color-value">{{ customTheme.colors.sidebar }}</span>
          </el-form-item>
          
          <el-form-item label="文本色">
            <el-color-picker v-model="customTheme.colors.text" />
            <span class="color-value">{{ customTheme.colors.text }}</span>
          </el-form-item>
        </el-form>
        
        <div class="theme-preview-large">
          <div class="preview-title">预览效果</div>
          <div class="preview-container" :style="getPreviewStyle()">
            <div class="preview-header-large" :style="{ background: customTheme.colors.primary }">
              <span style="color: white;">头部区域</span>
            </div>
            <div class="preview-body">
              <div class="preview-sidebar-large" :style="{ background: customTheme.colors.sidebar }">
                <div class="sidebar-item" style="color: white;">菜单项1</div>
                <div class="sidebar-item" style="color: white;">菜单项2</div>
              </div>
              <div class="preview-content-large" :style="{ background: customTheme.colors.background, color: customTheme.colors.text }">
                <div class="content-card">
                  <h4>卡片标题</h4>
                  <p>这是卡片内容区域</p>
                  <el-button :style="{ background: customTheme.colors.primary, borderColor: customTheme.colors.primary }" type="primary" size="mini">
                    主要按钮
                  </el-button>
                  <el-button :style="{ background: customTheme.colors.success, borderColor: customTheme.colors.success }" type="success" size="mini">
                    成功按钮
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div slot="footer">
        <el-button @click="customThemeVisible = false">取消</el-button>
        <el-button @click="resetCustomTheme">重置</el-button>
        <el-button type="primary" @click="saveCustomTheme">保存并应用</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ThemeSwitcher',
  
  props: {
    // 是否显示文本
    showText: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      // 当前主题
      currentTheme: 'light',
      // 自定义主题对话框显示状态
      customThemeVisible: false,
      
      // 预定义主题
      themes: [
        {
          key: 'light',
          name: '浅色主题',
          description: '经典的浅色主题',
          icon: 'el-icon-sunny',
          preview: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          colors: {
            primary: '#409EFF',
            success: '#67C23A',
            warning: '#E6A23C',
            danger: '#F56C6C',
            background: '#ffffff',
            sidebar: '#304156',
            text: '#303133'
          }
        },
        {
          key: 'dark',
          name: '深色主题',
          description: '护眼的深色主题',
          icon: 'el-icon-moon',
          preview: 'linear-gradient(135deg, #2c3e50 0%, #34495e 100%)',
          colors: {
            primary: '#409EFF',
            success: '#67C23A',
            warning: '#E6A23C',
            danger: '#F56C6C',
            background: '#1e1e1e',
            sidebar: '#2c3e50',
            text: '#ffffff'
          }
        },
        {
          key: 'blue',
          name: '蓝色主题',
          description: '清新的蓝色主题',
          icon: 'el-icon-star-on',
          preview: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          colors: {
            primary: '#1890ff',
            success: '#52c41a',
            warning: '#faad14',
            danger: '#f5222d',
            background: '#f0f2f5',
            sidebar: '#001529',
            text: '#000000'
          }
        },
        {
          key: 'green',
          name: '绿色主题',
          description: '自然的绿色主题',
          icon: 'el-icon-star-on',
          preview: 'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)',
          colors: {
            primary: '#52c41a',
            success: '#73d13d',
            warning: '#faad14',
            danger: '#f5222d',
            background: '#f6ffed',
            sidebar: '#162312',
            text: '#000000'
          }
        },
        {
          key: 'purple',
          name: '紫色主题',
          description: '优雅的紫色主题',
          icon: 'el-icon-star-on',
          preview: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          colors: {
            primary: '#722ed1',
            success: '#52c41a',
            warning: '#faad14',
            danger: '#f5222d',
            background: '#f9f0ff',
            sidebar: '#22075e',
            text: '#000000'
          }
        }
      ],
      
      // 自定义主题
      customTheme: {
        name: '我的主题',
        colors: {
          primary: '#409EFF',
          success: '#67C23A',
          warning: '#E6A23C',
          danger: '#F56C6C',
          background: '#ffffff',
          sidebar: '#304156',
          text: '#303133'
        }
      }
    }
  },
  
  computed: {
    currentThemeIcon() {
      const theme = this.themes.find(t => t.key === this.currentTheme)
      return theme ? theme.icon : 'el-icon-sunny'
    },
    
    currentThemeName() {
      const theme = this.themes.find(t => t.key === this.currentTheme)
      return theme ? theme.name : '浅色主题'
    }
  },
  
  mounted() {
    this.loadTheme()
  },
  
  methods: {
    /** 加载主题 */
    loadTheme() {
      const savedTheme = localStorage.getItem('theme')
      if (savedTheme) {
        this.currentTheme = savedTheme
        this.applyTheme(savedTheme)
      }
    },
    
    /** 处理主题切换 */
    handleThemeChange(themeKey) {
      this.currentTheme = themeKey
      this.applyTheme(themeKey)
      this.saveTheme(themeKey)
      this.$emit('theme-change', themeKey)
    },
    
    /** 应用主题 */
    applyTheme(themeKey) {
      const theme = this.themes.find(t => t.key === themeKey)
      if (!theme) return
      
      // 设置CSS变量
      const root = document.documentElement
      Object.entries(theme.colors).forEach(([key, value]) => {
        root.style.setProperty(`--theme-${key}`, value)
      })
      
      // 设置body类名
      document.body.className = document.body.className.replace(/theme-\w+/g, '')
      document.body.classList.add(`theme-${themeKey}`)
      
      // 更新Element UI主题
      this.updateElementTheme(theme.colors)
    },
    
    /** 更新Element UI主题 */
    updateElementTheme(colors) {
      // 动态更新Element UI的CSS变量
      const root = document.documentElement
      root.style.setProperty('--el-color-primary', colors.primary)
      root.style.setProperty('--el-color-success', colors.success)
      root.style.setProperty('--el-color-warning', colors.warning)
      root.style.setProperty('--el-color-danger', colors.danger)
    },
    
    /** 保存主题 */
    saveTheme(themeKey) {
      localStorage.setItem('theme', themeKey)
    },
    
    /** 打开自定义主题 */
    openCustomTheme() {
      this.customThemeVisible = true
      // 使用当前主题作为基础
      const currentThemeData = this.themes.find(t => t.key === this.currentTheme)
      if (currentThemeData) {
        this.customTheme.colors = { ...currentThemeData.colors }
      }
    },
    
    /** 重置自定义主题 */
    resetCustomTheme() {
      this.customTheme = {
        name: '我的主题',
        colors: {
          primary: '#409EFF',
          success: '#67C23A',
          warning: '#E6A23C',
          danger: '#F56C6C',
          background: '#ffffff',
          sidebar: '#304156',
          text: '#303133'
        }
      }
    },
    
    /** 保存自定义主题 */
    saveCustomTheme() {
      // 生成自定义主题key
      const customKey = 'custom_' + Date.now()
      
      // 添加到主题列表
      const customThemeData = {
        key: customKey,
        name: this.customTheme.name,
        description: '自定义主题',
        icon: 'el-icon-star-on',
        preview: `linear-gradient(135deg, ${this.customTheme.colors.primary} 0%, ${this.customTheme.colors.sidebar} 100%)`,
        colors: { ...this.customTheme.colors },
        custom: true
      }
      
      // 移除之前的自定义主题
      this.themes = this.themes.filter(t => !t.custom)
      this.themes.push(customThemeData)
      
      // 应用自定义主题
      this.handleThemeChange(customKey)
      
      // 保存自定义主题到本地存储
      localStorage.setItem('customThemes', JSON.stringify([customThemeData]))
      
      this.customThemeVisible = false
      this.$message.success('自定义主题保存成功')
    },
    
    /** 获取预览样式 */
    getPreviewStyle() {
      return {
        background: this.customTheme.colors.background,
        color: this.customTheme.colors.text,
        border: `1px solid ${this.customTheme.colors.primary}`
      }
    },
    
    /** 加载自定义主题 */
    loadCustomThemes() {
      const customThemes = localStorage.getItem('customThemes')
      if (customThemes) {
        try {
          const themes = JSON.parse(customThemes)
          this.themes = [...this.themes.filter(t => !t.custom), ...themes]
        } catch (error) {
          console.error('加载自定义主题失败:', error)
        }
      }
    }
  },
  
  created() {
    this.loadCustomThemes()
  }
}
</script>

<style scoped>
.theme-switcher {
  display: inline-block;
}

.theme-button {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.theme-button:hover {
  background: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.theme-dropdown {
  width: 320px;
  padding: 0;
}

.theme-header {
  padding: 15px 20px 10px;
  font-weight: bold;
  color: #303133;
  border-bottom: 1px solid #f0f0f0;
}

.theme-options {
  padding: 10px;
  max-height: 400px;
  overflow-y: auto;
}

.theme-option {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.theme-option:hover {
  background: #f5f7fa;
}

.theme-option.active {
  background: #ecf5ff;
  border: 1px solid #409EFF;
}

.theme-preview {
  width: 60px;
  height: 40px;
  border-radius: 6px;
  margin-right: 12px;
  position: relative;
  overflow: hidden;
}

.preview-content {
  width: 100%;
  height: 100%;
  position: relative;
}

.preview-header {
  height: 8px;
  width: 100%;
}

.preview-sidebar {
  position: absolute;
  left: 0;
  top: 8px;
  bottom: 0;
  width: 20px;
}

.preview-main {
  position: absolute;
  left: 20px;
  top: 8px;
  right: 0;
  bottom: 0;
}

.theme-info {
  flex: 1;
}

.theme-name {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.theme-desc {
  font-size: 12px;
  color: #909399;
}

.theme-check {
  color: #409EFF;
  font-size: 16px;
}

.theme-footer {
  padding: 10px 20px 15px;
  border-top: 1px solid #f0f0f0;
  text-align: center;
}

/* 自定义主题对话框 */
.custom-theme-content {
  max-height: 600px;
  overflow-y: auto;
}

.color-value {
  margin-left: 10px;
  font-size: 12px;
  color: #909399;
  font-family: monospace;
}

.theme-preview-large {
  margin-top: 20px;
  border-top: 1px solid #f0f0f0;
  padding-top: 20px;
}

.preview-title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #303133;
}

.preview-container {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-header-large {
  height: 40px;
  display: flex;
  align-items: center;
  padding: 0 15px;
  font-size: 14px;
  font-weight: bold;
}

.preview-body {
  display: flex;
  height: 120px;
}

.preview-sidebar-large {
  width: 80px;
  padding: 10px;
}

.sidebar-item {
  font-size: 12px;
  padding: 5px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.preview-content-large {
  flex: 1;
  padding: 15px;
}

.content-card {
  background: rgba(255, 255, 255, 0.8);
  padding: 15px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.content-card h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
}

.content-card p {
  margin: 0 0 15px 0;
  font-size: 12px;
  color: #666;
}
</style>
