<template>
  <div class="device-3d" :style="containerStyle">
    <div ref="container" class="three-container"></div>
    <div v-if="showControls" class="controls">
      <el-button-group size="mini">
        <el-button icon="el-icon-refresh-left" @click="resetView">重置</el-button>
        <el-button icon="el-icon-zoom-in" @click="zoomIn">放大</el-button>
        <el-button icon="el-icon-zoom-out" @click="zoomOut">缩小</el-button>
      </el-button-group>
    </div>
    <div v-if="showInfo" class="device-info">
      <div class="info-item">
        <span class="label">设备名称:</span>
        <span class="value">{{ config.deviceName || '未知设备' }}</span>
      </div>
      <div class="info-item">
        <span class="label">状态:</span>
        <span class="value" :class="statusClass">{{ statusText }}</span>
      </div>
      <div v-if="config.showTemperature" class="info-item">
        <span class="label">温度:</span>
        <span class="value">{{ temperature }}°C</span>
      </div>
    </div>
  </div>
</template>

<script>
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader'

export default {
  name: 'Device3D',
  props: {
    config: {
      type: Object,
      default: () => ({})
    },
    status: {
      type: String,
      default: 'offline' // online, offline, warning, error
    },
    temperature: {
      type: Number,
      default: 25
    },
    width: {
      type: Number,
      default: 300
    },
    height: {
      type: Number,
      default: 300
    }
  },
  data() {
    return {
      scene: null,
      camera: null,
      renderer: null,
      controls: null,
      deviceModel: null,
      statusLight: null,
      animationId: null,
      isLoaded: false
    }
  },
  computed: {
    containerStyle() {
      return {
        width: this.width + 'px',
        height: this.height + 'px',
        position: 'relative'
      }
    },
    showControls() {
      return this.config.showControls !== false
    },
    showInfo() {
      return this.config.showInfo !== false
    },
    statusClass() {
      return `status-${this.status}`
    },
    statusText() {
      const statusMap = {
        online: '在线',
        offline: '离线',
        warning: '警告',
        error: '错误'
      }
      return statusMap[this.status] || '未知'
    },
    statusColor() {
      const colorMap = {
        online: 0x00ff00,
        offline: 0x666666,
        warning: 0xffaa00,
        error: 0xff0000
      }
      return colorMap[this.status] || 0x666666
    }
  },
  watch: {
    status() {
      this.updateStatusLight()
    },
    temperature() {
      this.updateTemperatureEffect()
    }
  },
  mounted() {
    this.init3D()
    this.loadDeviceModel()
    this.animate()
  },
  beforeDestroy() {
    this.cleanup()
  },
  methods: {
    init3D() {
      // 创建场景
      this.scene = new THREE.Scene()
      this.scene.background = new THREE.Color(this.config.backgroundColor || 0xf0f0f0)
      
      // 创建相机
      this.camera = new THREE.PerspectiveCamera(
        75,
        this.width / this.height,
        0.1,
        1000
      )
      this.camera.position.set(5, 5, 5)
      
      // 创建渲染器
      this.renderer = new THREE.WebGLRenderer({ antialias: true })
      this.renderer.setSize(this.width, this.height)
      this.renderer.shadowMap.enabled = true
      this.renderer.shadowMap.type = THREE.PCFSoftShadowMap
      
      this.$refs.container.appendChild(this.renderer.domElement)
      
      // 创建控制器
      this.controls = new OrbitControls(this.camera, this.renderer.domElement)
      this.controls.enableDamping = true
      this.controls.dampingFactor = 0.05
      
      // 添加光源
      this.addLights()
      
      // 添加地面
      this.addGround()
    },
    
    addLights() {
      // 环境光
      const ambientLight = new THREE.AmbientLight(0x404040, 0.6)
      this.scene.add(ambientLight)
      
      // 方向光
      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
      directionalLight.position.set(10, 10, 5)
      directionalLight.castShadow = true
      directionalLight.shadow.mapSize.width = 2048
      directionalLight.shadow.mapSize.height = 2048
      this.scene.add(directionalLight)
      
      // 点光源（状态指示灯）
      this.statusLight = new THREE.PointLight(this.statusColor, 1, 10)
      this.statusLight.position.set(0, 3, 0)
      this.scene.add(this.statusLight)
    },
    
    addGround() {
      const groundGeometry = new THREE.PlaneGeometry(10, 10)
      const groundMaterial = new THREE.MeshLambertMaterial({ 
        color: this.config.groundColor || 0xcccccc 
      })
      const ground = new THREE.Mesh(groundGeometry, groundMaterial)
      ground.rotation.x = -Math.PI / 2
      ground.receiveShadow = true
      this.scene.add(ground)
    },
    
    loadDeviceModel() {
      const modelType = this.config.modelType || 'box'
      
      if (modelType === 'gltf' && this.config.modelUrl) {
        this.loadGLTFModel()
      } else {
        this.createBasicModel(modelType)
      }
    },
    
    loadGLTFModel() {
      const loader = new GLTFLoader()
      
      loader.load(
        this.config.modelUrl,
        (gltf) => {
          this.deviceModel = gltf.scene
          this.deviceModel.scale.setScalar(this.config.scale || 1)
          this.deviceModel.position.set(0, 0, 0)
          
          // 启用阴影
          this.deviceModel.traverse((child) => {
            if (child.isMesh) {
              child.castShadow = true
              child.receiveShadow = true
            }
          })
          
          this.scene.add(this.deviceModel)
          this.isLoaded = true
          this.updateStatusLight()
        },
        (progress) => {
          console.log('模型加载进度:', (progress.loaded / progress.total * 100) + '%')
        },
        (error) => {
          console.error('模型加载失败:', error)
          this.createBasicModel('box') // 回退到基础模型
        }
      )
    },
    
    createBasicModel(type) {
      let geometry
      
      switch (type) {
        case 'sphere':
          geometry = new THREE.SphereGeometry(1, 32, 32)
          break
        case 'cylinder':
          geometry = new THREE.CylinderGeometry(0.8, 0.8, 2, 32)
          break
        case 'cone':
          geometry = new THREE.ConeGeometry(1, 2, 32)
          break
        default:
          geometry = new THREE.BoxGeometry(2, 2, 2)
      }
      
      const material = new THREE.MeshPhongMaterial({ 
        color: this.config.deviceColor || 0x409EFF 
      })
      
      this.deviceModel = new THREE.Mesh(geometry, material)
      this.deviceModel.position.set(0, 1, 0)
      this.deviceModel.castShadow = true
      this.deviceModel.receiveShadow = true
      
      this.scene.add(this.deviceModel)
      this.isLoaded = true
      this.updateStatusLight()
    },
    
    updateStatusLight() {
      if (!this.statusLight) return
      
      this.statusLight.color.setHex(this.statusColor)
      
      // 根据状态调整光强度
      switch (this.status) {
        case 'online':
          this.statusLight.intensity = 1
          break
        case 'warning':
          this.statusLight.intensity = 0.8
          break
        case 'error':
          this.statusLight.intensity = 1.2
          break
        default:
          this.statusLight.intensity = 0.3
      }
    },
    
    updateTemperatureEffect() {
      if (!this.deviceModel) return
      
      // 根据温度调整设备颜色
      if (this.temperature > 60) {
        // 高温红色
        this.deviceModel.material.color.setHex(0xff4444)
      } else if (this.temperature > 40) {
        // 中温橙色
        this.deviceModel.material.color.setHex(0xff8844)
      } else {
        // 正常温度
        this.deviceModel.material.color.setHex(this.config.deviceColor || 0x409EFF)
      }
    },
    
    animate() {
      this.animationId = requestAnimationFrame(this.animate)
      
      // 更新控制器
      this.controls.update()
      
      // 设备旋转动画
      if (this.deviceModel && this.config.autoRotate) {
        this.deviceModel.rotation.y += 0.01
      }
      
      // 状态灯闪烁效果
      if (this.status === 'warning' || this.status === 'error') {
        const time = Date.now() * 0.005
        this.statusLight.intensity = 0.5 + 0.5 * Math.sin(time)
      }
      
      // 渲染场景
      this.renderer.render(this.scene, this.camera)
    },
    
    resetView() {
      this.camera.position.set(5, 5, 5)
      this.camera.lookAt(0, 0, 0)
      this.controls.reset()
    },
    
    zoomIn() {
      this.camera.position.multiplyScalar(0.9)
    },
    
    zoomOut() {
      this.camera.position.multiplyScalar(1.1)
    },
    
    cleanup() {
      if (this.animationId) {
        cancelAnimationFrame(this.animationId)
      }
      
      if (this.renderer) {
        this.renderer.dispose()
      }
      
      if (this.scene) {
        this.scene.clear()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.device-3d {
  position: relative;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  
  .three-container {
    width: 100%;
    height: 100%;
  }
  
  .controls {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
  }
  
  .device-info {
    position: absolute;
    bottom: 10px;
    left: 10px;
    background: rgba(255, 255, 255, 0.9);
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    
    .info-item {
      display: flex;
      margin-bottom: 4px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .label {
        color: #666;
        margin-right: 8px;
      }
      
      .value {
        font-weight: 500;
        
        &.status-online {
          color: #67C23A;
        }
        
        &.status-offline {
          color: #909399;
        }
        
        &.status-warning {
          color: #E6A23C;
        }
        
        &.status-error {
          color: #F56C6C;
        }
      }
    }
  }
}
</style>
