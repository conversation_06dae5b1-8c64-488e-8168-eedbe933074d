<template>
  <div class="app-container">
    <el-card class="box-card" v-loading="loading">
      <!-- 顶部操作区 -->
      <div slot="header" class="card-header">
        <div class="title">审批详情</div>
        <div class="operations">
          <el-button size="small" icon="el-icon-back" @click="goBack">返回</el-button>
          
          <!-- 根据不同状态显示不同操作按钮 -->
          <template v-if="canHandle">
            <el-button type="primary" size="small" icon="el-icon-check" @click="showApproveDialog('approve')">同意</el-button>
            <el-button type="danger" size="small" icon="el-icon-close" @click="showApproveDialog('reject')">驳回</el-button>
            <el-button size="small" icon="el-icon-refresh-right" @click="showTransferDialog">转交</el-button>
          </template>
          
          <template v-if="isMine && approvalDetail.status === 'PENDING'">
            <el-button type="warning" size="small" icon="el-icon-delete" @click="cancelApproval">撤销</el-button>
          </template>
          
          <template v-if="approvalDetail.status === 'PENDING'">
            <el-button size="small" icon="el-icon-bell" @click="urgeApproval">催办</el-button>
          </template>
          
          <template v-if="isMine && approvalDetail.status === 'REJECTED'">
            <el-button type="primary" size="small" icon="el-icon-refresh" @click="showReapplyDialog">重新提交</el-button>
          </template>
        </div>
      </div>

      <!-- 审批基本信息 -->
      <el-row :gutter="20" class="detail-section">
        <el-col :span="24">
          <div class="title-block">
            <span class="title">{{ approvalDetail.businessTitle || '审批详情' }}</span>
            <el-tag :type="getStatusType(approvalDetail.status)" size="small">{{ getStatusText(approvalDetail.status) }}</el-tag>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="label">流程类型：</span>
            <span class="value">{{ approvalDetail.workflowName || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="label">业务名称：</span>
            <span class="value">{{ approvalDetail.businessName || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="label">申请人：</span>
            <span class="value">{{ approvalDetail.applyUserName || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="label">申请时间：</span>
            <span class="value">{{ approvalDetail.createTime ? parseTime(approvalDetail.createTime) : '-' }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="label">当前节点：</span>
            <span class="value">{{ approvalDetail.currentNodeName || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="label">审批单号：</span>
            <span class="value">{{ approvalDetail.instanceId || instanceId || '-' }}</span>
          </div>
        </el-col>
      </el-row>

      <!-- 审批流程图 -->
      <div class="section-title">审批流程图</div>
      <div class="flow-chart-wrapper">
        <flow-chart :instanceId="instanceId" height="200" />
      </div>

      <!-- 业务表单内容 -->
      <div class="section-title">业务表单内容</div>
      <div class="business-content" v-if="approvalDetail.businessContent">
        <pre>{{ formatBusinessContent(approvalDetail.businessContent) }}</pre>
      </div>
      <el-empty v-else description="暂无业务表单内容"></el-empty>

      <!-- 审批操作区域 -->
      <div class="section-title">审批操作</div>



      <!-- 只有在流程进行中且有权限时才显示审批操作按钮 -->
      <div class="approval-actions" v-if="showApprovalActions">
        <el-button
          type="success"
          size="medium"
          icon="el-icon-check"
          @click="showApproveDialog('approve')"
          :loading="submitting">
          同意
        </el-button>
        <el-button
          type="danger"
          size="medium"
          icon="el-icon-close"
          @click="showApproveDialog('reject')"
          :loading="submitting">
          驳回
        </el-button>
        <el-button
          type="warning"
          size="medium"
          icon="el-icon-share"
          @click="showTransferDialog"
          :loading="submitting">
          转交
        </el-button>
      </div>

      <!-- 流程已结束时的状态显示 -->
      <div v-else-if="isProcessFinished" class="approval-actions">
        <el-alert
          :title="getProcessFinishedMessage()"
          :type="getStatusType(approvalDetail.status)"
          :closable="false"
          show-icon>
          <template slot="title">
            <span style="font-size: 16px; font-weight: bold;">
              {{ getProcessFinishedMessage() }}
            </span>
          </template>
          <div style="margin-top: 8px; color: #666;">
            该审批流程已结束，不能再进行修改操作
          </div>
        </el-alert>
      </div>

      <!-- 流程进行中但无权限时的提示 -->
      <div v-else-if="approvalDetail.status === '0'" class="approval-actions">
        <el-alert
          title="当前流程正在进行中，您暂无审批权限"
          type="warning"
          :closable="false"
          show-icon>
        </el-alert>
      </div>

      <!-- 审批记录 -->
      <div class="section-title">审批记录</div>
      <el-timeline>
        <el-timeline-item
          v-for="(record, index) in approvalRecords"
          :key="index"
          :timestamp="parseTime(record.operateTime || record.operationTime || record.createTime)"
          :type="getTimelineItemType(record.action)">
          <div class="timeline-content">
            <div class="timeline-title">
              <span>{{ record.operateBy || record.approverName || '系统' }} - {{ getActionText(record.action) }}</span>
              <span class="node-name">{{ record.nodeName || '-' }}</span>
            </div>
            <div class="timeline-comment" v-if="record.comment || record.opinion">
              {{ record.comment || record.opinion }}
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </el-card>

    <!-- 审批对话框 -->
    <el-dialog
      :title="approveDialogType === 'approve' ? '同意审批' : '驳回审批'"
      :visible.sync="approveDialogVisible"
      width="500px">
      <el-form ref="approveForm" :model="approveForm" label-width="80px">
        <el-form-item label="审批意见" prop="opinion">
          <el-input
            type="textarea"
            v-model="approveForm.opinion"
            :placeholder="approveDialogType === 'approve' ? '请输入同意意见（选填）' : '请输入驳回原因（必填）'"
            :rows="4"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="approveDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleApprove" :loading="submitting">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 转交对话框 -->
    <el-dialog
      title="转交审批"
      :visible.sync="transferDialogVisible"
      width="500px">
      <el-form ref="transferForm" :model="transferForm" label-width="80px">
        <el-form-item label="转交给" prop="targetUserId" :rules="[{ required: true, message: '请选择转交人', trigger: 'change' }]">
          <el-select v-model="transferForm.targetUserId" placeholder="请选择转交人" filterable>
            <el-option
              v-for="item in userOptions"
              :key="item.userId"
              :label="item.nickName"
              :value="item.userId">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="转交原因" prop="comment">
          <el-input
            type="textarea"
            v-model="transferForm.comment"
            placeholder="请输入转交原因（选填）"
            :rows="4"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="transferDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleTransfer" :loading="submitting">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 重新提交对话框 -->
    <el-dialog
      title="重新提交审批"
      :visible.sync="reapplyDialogVisible"
      width="500px">
      <el-form ref="reapplyForm" :model="reapplyForm" label-width="80px">
        <el-form-item label="备注说明" prop="comment">
          <el-input
            type="textarea"
            v-model="reapplyForm.comment"
            placeholder="请输入重新提交说明（选填）"
            :rows="4"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="reapplyDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleReapply" :loading="submitting">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getApprovalDetail,
  getApprovalHistory,
  approve,
  cancelApproval,
  transferApproval,
  urgeApproval,
  reapplyApproval
} from '@/api/approval/workflow'
import FlowChart from '@/components/RuiYun/FlowChart'
import { parseTime } from '@/utils/ruiyun'
import { getUserProfile, listUser } from "@/api/system/user";

export default {
  name: "ApprovalDetail",
  components: {
    FlowChart
  },
  data() {
    return {
      instanceId: this.$route.params.instanceId || null,
      loading: false,
      submitting: false,
      approvalDetail: {},
      approvalRecords: [],
      
      // 审批对话框
      approveDialogVisible: false,
      approveDialogType: 'approve', // approve or reject
      approveForm: {
        instanceId: null,
        opinion: '',
        action: ''
      },
      
      // 转交对话框
      transferDialogVisible: false,
      transferForm: {
        instanceId: null,
        targetUserId: null,
        comment: ''
      },
      userOptions: [],
      
      // 重新提交对话框
      reapplyDialogVisible: false,
      reapplyForm: {
        instanceId: null,
        comment: ''
      },
      
      // 当前用户信息
      userInfo: {}
    }
  },
  computed: {
    // 是否是自己提交的审批
    isMine() {
      return this.approvalDetail.applyUserId === this.userInfo.userId
    },
    // 是否可以处理审批
    canHandle() {
      return this.approvalDetail.canHandle
    },
    // 是否显示审批操作按钮
    showApprovalActions() {
      // 修复：只有审批状态为审批中(0)时才显示操作按钮
      if (this.approvalDetail.status !== '0') {
        return false;
      }

      // 申请人不能审批自己的申请
      if (this.isMine) {
        return false;
      }

      // 检查当前用户是否在当前审批人列表中
      const currentApprovers = this.approvalDetail.currentApprovers;
      const currentUserId = this.userInfo.userId;

      if (!currentApprovers || !currentUserId) {
        return false;
      }

      // 超级管理员可以审批任何进行中的流程，但不能操作已结束的流程
      if (this.userInfo.userName === 'admin' && this.approvalDetail.status === '0') {
        return true;
      }

      // currentApprovers可能是逗号分隔的字符串
      const approverList = String(currentApprovers).split(',').map(id => id.trim());
      return approverList.includes(String(currentUserId));
    },

    // 流程是否已结束
    isProcessFinished() {
      return this.approvalDetail.status === '1' || this.approvalDetail.status === '2' || this.approvalDetail.status === '3';
    }
  },
  created() {
    // 确保instanceId是字符串类型
    this.instanceId = this.$route.params.instanceId ? String(this.$route.params.instanceId) : null;
    console.log("审批详情页面，实例ID类型:", typeof this.instanceId, "值:", this.instanceId);
    
    if (this.instanceId) {
      this.loadApprovalDetail()
      // 审批详情接口已经包含历史记录，不需要单独调用
      // this.loadApprovalHistory()
    }
    this.getUserInfo()
  },
  methods: {
    // 格式化时间
    parseTime(time, pattern) {
      if (arguments.length === 0 || !time) {
        return null
      }
      const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
      let date
      if (typeof time === 'object') {
        date = time
      } else {
        if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
          time = parseInt(time)
        } else if (typeof time === 'string') {
          time = time.replace(new RegExp(/-/gm), '/').replace('T', ' ').replace(new RegExp(/\.[\d]{3}/gm), '')
        }
        if ((typeof time === 'number') && (time.toString().length === 10)) {
          time = time * 1000
        }
        date = new Date(time)
      }
      const formatObj = {
        y: date.getFullYear(),
        m: date.getMonth() + 1,
        d: date.getDate(),
        h: date.getHours(),
        i: date.getMinutes(),
        s: date.getSeconds(),
        a: date.getDay()
      }
      const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
        let value = formatObj[key]
        // Note: getDay() returns 0 on Sunday
        if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }
        if (result.length > 0 && value < 10) {
          value = '0' + value
        }
        return value || 0
      })
      return time_str
    },
    
    // 加载审批详情
    loadApprovalDetail() {
      this.loading = true
      // 确保使用字符串类型的instanceId
      getApprovalDetail({ instanceId: String(this.instanceId) })
        .then(response => {
          const data = response.data || {}
          console.log("获取到的审批详情:", data);

          // 重新组织数据结构，将嵌套的数据平铺到approvalDetail中
          this.approvalDetail = {
            // 实例基本信息
            instanceId: data.instance?.instanceId,
            businessTitle: data.instance?.businessTitle,
            businessType: data.instance?.businessType,
            businessName: data.instance?.businessTitle, // 业务名称使用businessTitle
            status: data.instance?.status,
            applyUserId: data.instance?.applyUserId,
            applyUserName: data.instance?.applyUserName,
            applicantName: data.instance?.applyUserName, // 申请人名称
            applyDeptId: data.instance?.applyDeptId,
            applyDeptName: data.instance?.applyDeptName,
            currentNodeId: data.instance?.currentNodeId,
            currentNodeName: data.instance?.currentNodeName,
            currentApprovers: data.instance?.currentApprovers,
            createTime: data.instance?.createTime,
            startTime: data.instance?.startTime,
            finishTime: data.instance?.finishTime,
            remark: data.instance?.remark,
            priority: data.instance?.priority,
            businessContent: data.instance?.remark, // 业务表单内容使用remark字段

            // 工作流信息
            workflowId: data.workflow?.workflowId,
            workflowName: data.workflow?.workflowName,
            workflowCode: data.workflow?.workflowCode,

            // 原始数据保留
            instance: data.instance,
            workflow: data.workflow,
            nodes: data.nodes,
            records: data.records
          }

          // 提取审批记录并添加发起记录
          if (data.records && Array.isArray(data.records)) {
            this.approvalRecords = data.records
          }

          // 修复：添加发起记录到审批记录列表
          if (data.instance) {
            const startRecord = {
              nodeName: '发起申请',
              operateBy: data.instance.applyUserName,
              approverName: data.instance.applyUserName,
              action: 'start',
              operateTime: data.instance.createTime,
              operationTime: data.instance.createTime,
              createTime: data.instance.createTime,
              comment: '发起审批申请',
              opinion: '发起审批申请'
            }

            // 将发起记录添加到记录列表
            this.approvalRecords.push(startRecord)

            // 优化排序：按时间倒序排列，最新的在上面，发起申请在最下面
            this.approvalRecords.sort((a, b) => {
              const timeA = new Date(a.operateTime || a.operationTime || a.createTime)
              const timeB = new Date(b.operateTime || b.operationTime || b.createTime)

              // 发起申请始终在最下面
              if (a.action === 'start') return 1
              if (b.action === 'start') return -1

              // 其他记录按时间倒序（最新的在上面）
              return timeB - timeA
            })
          }
        })
        .catch(error => {
          this.$message.error('加载审批详情失败: ' + (error.message || error));
          console.error('加载审批详情失败', error)
        })
        .finally(() => {
          this.loading = false
        })
    },
    
    // 加载审批历史
    loadApprovalHistory() {
      // 确保使用正确的参数格式
      const params = { instanceId: String(this.instanceId) };
      console.log("加载审批历史，参数:", params);
      
      getApprovalHistory(params)
        .then(response => {
          if (response.code === 200) {
            this.approvalRecords = response.data || []
          } else {
            this.$message.error(response.msg || '加载审批历史失败')
          }
        })
        .catch(error => {
          this.$message.error('加载审批历史失败: ' + (error.message || error))
          console.error('加载审批历史失败', error)
        })
    },
    
    // 获取用户信息
    getUserInfo() {
      getUserProfile().then(response => {
        this.userInfo = response.data || {}
      })
    },
    
    // 获取审批状态类型
    getStatusType(status) {
      // 兼容数字和字符串格式
      switch (String(status)) {
        case '0':
        case 'PENDING':
          return 'warning'
        case '1':
        case 'APPROVED':
          return 'success'
        case '2':
        case 'REJECTED':
          return 'error'
        case '3':
        case 'CANCELED':
          return 'info'
        default:
          return 'info'
      }
    },
    
    // 获取审批状态文字
    getStatusText(status) {
      // 兼容数字和字符串格式
      switch (String(status)) {
        case '0':
        case 'PENDING':
          return '审批中'
        case '1':
        case 'APPROVED':
          return '已通过'
        case '2':
        case 'REJECTED':
          return '已驳回'
        case '3':
        case 'CANCELED':
          return '已撤销'
        case '4':
        case 'TERMINATED':
          return '已终止'
        default:
          return '未知状态'
      }
    },

    // 获取流程结束消息
    getProcessFinishedMessage() {
      switch (String(this.approvalDetail.status)) {
        case '1':
        case 'APPROVED':
          return '流程已完成，审批通过'
        case '2':
        case 'REJECTED':
          return '流程已结束，审批被驳回'
        case '3':
        case 'CANCELED':
          return '流程已结束，申请已撤销'
        case '4':
        case 'TERMINATED':
          return '流程已结束，审批已终止'
        default:
          return '流程已结束'
      }
    },
    
    // 获取操作文字
    getActionText(action) {
      // 兼容数字和字符串格式
      switch (String(action)) {
        case '0':
        case 'CREATE':
        case 'start':
          return '发起审批'
        case '1':
        case 'APPROVE':
          return '同意'
        case '2':
        case 'REJECT':
          return '驳回'
        case '3':
        case 'CANCEL':
          return '撤销'
        case '4':
        case 'TRANSFER':
          return '转交'
        case '5':
        case 'URGE':
          return '催办'
        case '6':
        case 'REAPPLY':
          return '重新提交'
        default:
          return action || '未知操作'
      }
    },
    
    // 获取时间线项目类型
    getTimelineItemType(action) {
      // 兼容数字和字符串格式
      switch (String(action)) {
        case '0':
        case 'CREATE':
        case 'REAPPLY':
          return 'primary'
        case '1':
        case 'APPROVE':
          return 'success'
        case '2':
        case 'REJECT':
          return 'danger'
        case '3':
        case 'CANCEL':
          return 'info'
        case '4':
        case 'TRANSFER':
          return 'warning'
        case '5':
        case 'URGE':
          return ''
        default:
          return ''
      }
    },
    
    // 显示审批对话框
    showApproveDialog(type) {
      this.approveDialogType = type
      this.approveForm = {
        instanceId: this.instanceId,
        opinion: '',
        action: type === 'approve' ? '1' : '2'  // 1=通过, 2=驳回
      }
      this.approveDialogVisible = true
    },
    
    // 显示转交对话框
    showTransferDialog() {
      this.transferForm = {
        instanceId: this.instanceId,
        targetUserId: null,
        comment: ''
      }
      this.loadUserOptions()
      this.transferDialogVisible = true
    },
    
    // 显示重新提交对话框
    showReapplyDialog() {
      this.reapplyForm = {
        instanceId: this.instanceId,
        comment: ''
      }
      this.reapplyDialogVisible = true
    },
    
    // 加载用户选项
    loadUserOptions() {
      listUser({ pageSize: 100 }).then(response => {
        this.userOptions = response.rows || []
      }).catch(error => {
        console.error('加载用户列表失败:', error)
        this.$message.error('加载用户列表失败')
      })
    },
    
    // 处理审批
    handleApprove() {
      if (this.approveDialogType === 'reject' && !this.approveForm.opinion) {
        this.$message.warning('请填写驳回原因')
        return
      }

      // 确保instanceId是字符串类型
      const data = {
        instanceId: String(this.approveForm.instanceId),
        action: this.approveForm.action,
        opinion: this.approveForm.opinion
      };
      
      console.log("提交审批参数:", data);
      
      this.submitting = true
      approve(data)
        .then((response) => {
          if (response.code === 200) {
            this.$message.success(this.approveDialogType === 'approve' ? '审批已通过' : '审批已驳回')
            this.approveDialogVisible = false
            this.refreshData()
          } else {
            this.$message.error(response.msg || '操作失败')
          }
        })
        .catch((error) => {
          this.$message.error('操作失败: ' + (error.message || error))
        })
        .finally(() => {
          this.submitting = false
        })
    },
    
    // 处理转交
    handleTransfer() {
      this.$refs.transferForm.validate(valid => {
        if (!valid) return
        
        // 确保instanceId是字符串类型
        const data = {
          instanceId: String(this.transferForm.instanceId),
          targetUserId: this.transferForm.targetUserId,
          opinion: this.transferForm.comment
        };
        
        console.log("提交转交参数:", data);
        
        this.submitting = true
        transferApproval(data)
          .then((response) => {
            if (response.code === 200) {
              this.$message.success('已成功转交')
              this.transferDialogVisible = false
              this.refreshData()
            } else {
              this.$message.error(response.msg || '操作失败')
            }
          })
          .catch((error) => {
            this.$message.error('操作失败: ' + (error.message || error))
          })
          .finally(() => {
            this.submitting = false
          })
      })
    },
    
    // 处理重新提交
    handleReapply() {
      // 确保instanceId是字符串类型
      const data = {
        instanceId: String(this.reapplyForm.instanceId),
        comment: this.reapplyForm.comment
      };
      
      console.log("重新提交审批参数:", data);
      
      this.submitting = true
      reapplyApproval(data)
        .then((response) => {
          if (response.code === 200) {
            this.$message.success('审批已重新提交')
            this.reapplyDialogVisible = false
            this.refreshData()
          } else {
            this.$message.error(response.msg || '操作失败')
          }
        })
        .catch((error) => {
          this.$message.error('操作失败: ' + (error.message || error))
        })
        .finally(() => {
          this.submitting = false
        })
    },
    
    // 撤销审批
    cancelApproval() {
      this.$confirm('确认撤销此审批申请?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const data = { instanceId: String(this.instanceId) };
        console.log("撤销审批参数:", data);
        
        cancelApproval(data)
          .then((response) => {
            if (response.code === 200) {
              this.$message.success('审批已撤销')
              this.refreshData()
            } else {
              this.$message.error(response.msg || '操作失败')
            }
          })
          .catch((error) => {
            this.$message.error('操作失败: ' + (error.message || error))
          })
      }).catch(() => {})
    },
    
    // 催办审批
    urgeApproval() {
      this.$confirm('确认催办此审批?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const data = { instanceId: String(this.instanceId) };
        console.log("催办审批参数:", data);
        
        urgeApproval(data)
          .then((response) => {
            if (response.code === 200) {
              this.$message.success('已发送催办通知')
            } else {
              this.$message.error(response.msg || '操作失败')
            }
          })
          .catch((error) => {
            this.$message.error('操作失败: ' + (error.message || error))
          })
      }).catch(() => {})
    },
    
    // 刷新数据
    refreshData() {
      this.loadApprovalDetail()
      // 审批详情接口已经包含历史记录，不需要单独调用
      // this.loadApprovalHistory()
    },
    
    // 返回
    goBack() {
      this.$router.go(-1)
    },
    
    // 格式化业务内容
    formatBusinessContent(content) {
      try {
        if (!content) {
          return "无业务内容数据";
        }
        
        if (typeof content === 'string') {
          // 尝试解析JSON字符串
          try {
            const obj = JSON.parse(content);
            return JSON.stringify(obj, null, 2);
          } catch (e) {
            // 不是有效的JSON，直接返回原字符串
            return content;
          }
        } else if (typeof content === 'object') {
          // 已经是对象，直接美化输出
          return JSON.stringify(content, null, 2);
        }
        // 其他类型，转为字符串
        return String(content);
      } catch (e) {
        console.error("格式化业务内容失败:", e);
        return content || "无法显示业务内容";
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .title {
      font-size: 16px;
      font-weight: bold;
    }
  }
  
  .detail-section {
    margin-bottom: 20px;
    
    .title-block {
      margin-bottom: 15px;
      
      .title {
        font-size: 18px;
        font-weight: bold;
        margin-right: 10px;
      }
    }
    
    .info-item {
      margin-bottom: 10px;
      font-size: 14px;
      
      .label {
        color: #909399;
        margin-right: 5px;
      }
    }
  }
  
  .section-title {
    font-size: 16px;
    font-weight: bold;
    margin: 20px 0 15px;
    padding-left: 10px;
    border-left: 3px solid #409EFF;
  }
  
  .flow-chart-wrapper {
    border: 1px solid #EBEEF5;
    border-radius: 4px;
    min-height: 200px;
    margin-bottom: 20px;
  }
  
  .business-content {
    background-color: #F8F8F8;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    
    pre {
      white-space: pre-wrap;
      word-wrap: break-word;
      font-family: Consolas, Monaco, 'Andale Mono', monospace;
      margin: 0;
    }
  }
  
  .approval-actions {
    background-color: #F8F9FA;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    text-align: center;
    border: 1px solid #E9ECEF;

    .el-button {
      margin: 0 10px;
      min-width: 100px;

      &.el-button--success {
        background-color: #67C23A;
        border-color: #67C23A;

        &:hover {
          background-color: #85CE61;
          border-color: #85CE61;
        }
      }

      &.el-button--danger {
        background-color: #F56C6C;
        border-color: #F56C6C;

        &:hover {
          background-color: #F78989;
          border-color: #F78989;
        }
      }

      &.el-button--warning {
        background-color: #E6A23C;
        border-color: #E6A23C;

        &:hover {
          background-color: #EBB563;
          border-color: #EBB563;
        }
      }
    }
  }

  .timeline-content {
    .timeline-title {
      font-weight: bold;
      margin-bottom: 5px;
      display: flex;
      justify-content: space-between;

      .node-name {
        color: #909399;
        font-size: 13px;
        font-weight: normal;
      }
    }

    .timeline-comment {
      color: #606266;
      font-size: 14px;
      background-color: #F8F8F8;
      padding: 8px 12px;
      border-radius: 4px;
    }
  }
}
</style>