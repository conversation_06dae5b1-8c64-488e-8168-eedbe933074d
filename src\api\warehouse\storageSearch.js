import request from '@/utils/request'

// 搜索存储位置（同时搜索货位和容器）
export function searchStorageLocation(query) {
  return request({
    url: '/warehouse/storage/search',
    method: 'get',
    params: { query }
  })
}

// 搜索货位
export function searchLocation(query) {
  return request({
    url: '/warehouse/location/search',
    method: 'get',
    params: { 
      locationCode: query,
      locationName: query,
      pageSize: 20
    }
  })
}

// 搜索容器
export function searchContainer(query) {
  return request({
    url: '/warehouse/container/search',
    method: 'get',
    params: { 
      containerCode: query,
      containerNo: query,
      pageSize: 20
    }
  })
}

// 组合搜索货位和容器
export function searchStorageLocationCombined(query) {
  if (!query || query.trim() === '') {
    return Promise.resolve({
      code: 200,
      data: []
    })
  }

  // 同时搜索货位和容器
  const locationPromise = request({
    url: '/warehouse/location/list',
    method: 'get',
    params: {
      locationCode: query,
      pageSize: 10
    }
  }).catch(() => ({ rows: [] }))

  const containerPromise = request({
    url: '/warehouse/container/list',
    method: 'get',
    params: {
      containerCode: query,
      pageSize: 10
    }
  }).catch(() => ({ rows: [] }))

  return Promise.all([locationPromise, containerPromise]).then(([locationRes, containerRes]) => {
    const locations = (locationRes.rows || []).map(item => ({
      type: 'location',
      code: item.locationCode,
      name: item.locationName || '',
      description: `货位 | ${item.locationCode}${item.locationName ? ' - ' + item.locationName : ''}`,
      warehouseName: item.warehouseName || '',
      zoneName: item.zoneName || '',
      rackName: item.rackName || '',
      fullInfo: `${item.warehouseName || ''} ${item.zoneName || ''} ${item.rackName || ''}`.trim()
    }))

    const containers = (containerRes.rows || []).map(item => ({
      type: 'container',
      code: item.containerCode,
      name: item.containerNo || '',
      description: `容器 | ${item.containerCode}${item.containerNo ? ' - ' + item.containerNo : ''}`,
      containerType: item.containerType || '',
      specification: item.specification || '',
      status: item.status || '',
      fullInfo: `${item.containerType || ''} ${item.specification || ''}`.trim()
    }))

    return {
      code: 200,
      data: [...locations, ...containers]
    }
  })
}
