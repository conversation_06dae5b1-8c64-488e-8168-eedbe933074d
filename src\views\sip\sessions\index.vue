<template>
  <div class="sip-sessions">
    <!-- 搜索和操作栏 -->
    <div class="search-bar">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="会话ID">
          <el-input
            v-model="searchForm.sessionId"
            placeholder="请输入会话ID"
            clearable
            @keyup.enter.native="handleSearch">
          </el-input>
        </el-form-item>
        <el-form-item label="设备ID">
          <el-input
            v-model="searchForm.deviceId"
            placeholder="请输入设备ID"
            clearable
            @keyup.enter.native="handleSearch">
          </el-input>
        </el-form-item>
        <el-form-item label="Call-ID">
          <el-input
            v-model="searchForm.callId"
            placeholder="请输入Call-ID"
            clearable
            @keyup.enter.native="handleSearch">
          </el-input>
        </el-form-item>
        <el-form-item label="会话状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="初始化" value="INITIAL"></el-option>
            <el-option label="邀请中" value="INVITING"></el-option>
            <el-option label="已建立" value="ESTABLISHED"></el-option>
            <el-option label="已确认" value="CONFIRMED"></el-option>
            <el-option label="已终止" value="TERMINATED"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
      
      <div class="action-buttons">
        <el-button type="success" icon="el-icon-refresh" @click="refreshData">刷新</el-button>
        <el-button type="info" icon="el-icon-view" @click="showActiveSessions">活跃会话</el-button>
        <el-button type="warning" icon="el-icon-download" @click="exportData">导出</el-button>
        <el-button type="danger" icon="el-icon-delete" @click="batchTerminate" :disabled="!selectedSessions.length">
          批量终止
        </el-button>
      </div>
    </div>

    <!-- 会话列表 -->
    <el-table
      v-loading="loading"
      :data="sessionList"
      @selection-change="handleSelectionChange"
      style="width: 100%">
      
      <el-table-column type="selection" width="55"></el-table-column>
      
      <el-table-column prop="sessionId" label="会话ID" min-width="200">
        <template slot-scope="scope">
          <el-link type="primary" @click="showSessionDetails(scope.row)">
            {{ scope.row.sessionId }}
          </el-link>
        </template>
      </el-table-column>
      
      <el-table-column prop="deviceId" label="设备ID" min-width="150">
        <template slot-scope="scope">
          <el-link type="primary" @click="viewDevice(scope.row.deviceId)">
            {{ scope.row.deviceId }}
          </el-link>
        </template>
      </el-table-column>
      
      <el-table-column prop="callId" label="Call-ID" min-width="200">
        <template slot-scope="scope">
          <span class="call-id">{{ scope.row.callId }}</span>
        </template>
      </el-table-column>
      
      <el-table-column prop="sessionType" label="会话类型" width="100">
        <template slot-scope="scope">
          <el-tag size="mini" :type="getSessionTypeColor(scope.row.sessionType)">
            {{ getSessionTypeText(scope.row.sessionType) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="status" label="状态" width="100">
        <template slot-scope="scope">
          <el-tag size="mini" :type="getStatusColor(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="mediaType" label="媒体类型" width="100">
        <template slot-scope="scope">
          <el-tag size="mini" type="info">
            {{ scope.row.mediaType || 'N/A' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="localPort" label="本地端口" width="100" align="center">
        <template slot-scope="scope">
          {{ scope.row.localPort || 'N/A' }}
        </template>
      </el-table-column>
      
      <el-table-column prop="remotePort" label="远程端口" width="100" align="center">
        <template slot-scope="scope">
          {{ scope.row.remotePort || 'N/A' }}
        </template>
      </el-table-column>
      
      <el-table-column prop="createTime" label="创建时间" width="160">
        <template slot-scope="scope">
          {{ formatTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="lastActivity" label="最后活动" width="160">
        <template slot-scope="scope">
          {{ formatTime(scope.row.lastActivity) }}
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="200" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="primary" @click="showSessionDetails(scope.row)">详情</el-button>
          <el-button size="mini" type="info" @click="showMediaInfo(scope.row)">媒体</el-button>
          <el-button 
            size="mini" 
            type="danger" 
            @click="terminateSession(scope.row)"
            :disabled="scope.row.status === 'TERMINATED'">
            终止
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total">
      </el-pagination>
    </div>

    <!-- 会话详情对话框 -->
    <el-dialog title="会话详情" :visible.sync="detailDialogVisible" width="800px">
      <div v-if="selectedSession" class="session-details">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="会话ID">{{ selectedSession.sessionId }}</el-descriptions-item>
          <el-descriptions-item label="设备ID">{{ selectedSession.deviceId }}</el-descriptions-item>
          <el-descriptions-item label="Call-ID">{{ selectedSession.callId }}</el-descriptions-item>
          <el-descriptions-item label="会话类型">{{ getSessionTypeText(selectedSession.sessionType) }}</el-descriptions-item>
          <el-descriptions-item label="会话状态">
            <el-tag :type="getStatusColor(selectedSession && selectedSession.status)">
              {{ getStatusText(selectedSession && selectedSession.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="媒体类型">{{ selectedSession.mediaType || 'N/A' }}</el-descriptions-item>
          <el-descriptions-item label="本地IP">{{ selectedSession.localIp || 'N/A' }}</el-descriptions-item>
          <el-descriptions-item label="本地端口">{{ selectedSession.localPort || 'N/A' }}</el-descriptions-item>
          <el-descriptions-item label="远程IP">{{ selectedSession.remoteIp || 'N/A' }}</el-descriptions-item>
          <el-descriptions-item label="远程端口">{{ selectedSession.remotePort || 'N/A' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatTime(selectedSession.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="最后活动">{{ formatTime(selectedSession.lastActivity) }}</el-descriptions-item>
        </el-descriptions>
        
        <div v-if="selectedSession.sdp" class="sdp-section" style="margin-top: 20px;">
          <h4>SDP 信息</h4>
          <el-input
            type="textarea"
            :rows="8"
            :value="selectedSession.sdp"
            readonly
            placeholder="SDP 信息">
          </el-input>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button v-if="selectedSession" type="info" @click="showMediaInfo(selectedSession)">查看媒体信息</el-button>
        <el-button
          v-if="selectedSession"
          type="danger"
          @click="terminateSession(selectedSession)"
          :disabled="selectedSession && selectedSession.status === 'TERMINATED'">
          终止会话
        </el-button>
      </div>
    </el-dialog>

    <!-- 媒体信息对话框 -->
    <el-dialog title="媒体信息" :visible.sync="mediaDialogVisible" width="600px">
      <div v-if="mediaInfo" class="media-info">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="媒体类型">{{ mediaInfo.mediaType }}</el-descriptions-item>
          <el-descriptions-item label="编码格式">{{ mediaInfo.codec }}</el-descriptions-item>
          <el-descriptions-item label="RTP端口">{{ mediaInfo.rtpPort }}</el-descriptions-item>
          <el-descriptions-item label="RTCP端口">{{ mediaInfo.rtcpPort }}</el-descriptions-item>
          <el-descriptions-item label="传输协议">{{ mediaInfo.transport }}</el-descriptions-item>
          <el-descriptions-item label="媒体状态">
            <el-tag :type="mediaInfo.status === 'ACTIVE' ? 'success' : 'danger'">
              {{ mediaInfo.status === 'ACTIVE' ? '活跃' : '停止' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发送包数">{{ mediaInfo.sentPackets || 0 }}</el-descriptions-item>
          <el-descriptions-item label="接收包数">{{ mediaInfo.receivedPackets || 0 }}</el-descriptions-item>
          <el-descriptions-item label="发送字节">{{ formatBytes(mediaInfo.sentBytes) }}</el-descriptions-item>
          <el-descriptions-item label="接收字节">{{ formatBytes(mediaInfo.receivedBytes) }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="mediaDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  getSipSessions, 
  getSipSessionDetails, 
  terminateSession,
  batchTerminateSessions,
  getActiveSessions,
  getSessionMediaInfo,
  exportSessions 
} from '@/api/sip/session'

export default {
  name: 'SipSessions',
  data() {
    return {
      loading: false,
      sessionList: [],
      selectedSessions: [],
      selectedSession: null,
      mediaInfo: null,
      searchForm: {
        sessionId: '',
        deviceId: '',
        callId: '',
        status: ''
      },
      pagination: {
        page: 1,
        size: 20,
        total: 0
      },
      detailDialogVisible: false,
      mediaDialogVisible: false
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    async loadData() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.page,
          size: this.pagination.size,
          ...this.searchForm
        }
        const response = await getSipSessions(params)
        this.sessionList = response.data.data
        this.pagination.total = response.data.total
      } catch (error) {
        this.$message.error('获取会话列表失败: ' + error.message)
      } finally {
        this.loading = false
      }
    },
    
    handleSearch() {
      this.pagination.page = 1
      this.loadData()
    },
    
    resetSearch() {
      this.searchForm = {
        sessionId: '',
        deviceId: '',
        callId: '',
        status: ''
      }
      this.handleSearch()
    },
    
    refreshData() {
      this.loadData()
    },
    
    handleSelectionChange(selection) {
      this.selectedSessions = selection
    },
    
    handleSizeChange(val) {
      this.pagination.size = val
      this.loadData()
    },
    
    handleCurrentChange(val) {
      this.pagination.page = val
      this.loadData()
    },
    
    async showSessionDetails(session) {
      try {
        const response = await getSipSessionDetails(session.sessionId)
        this.selectedSession = response.data
        this.detailDialogVisible = true
      } catch (error) {
        this.$message.error('获取会话详情失败: ' + error.message)
      }
    },
    
    async showMediaInfo(session) {
      try {
        const response = await getSessionMediaInfo(session.sessionId)
        this.mediaInfo = response.data
        this.mediaDialogVisible = true
      } catch (error) {
        this.$message.error('获取媒体信息失败: ' + error.message)
      }
    },
    
    async showActiveSessions() {
      try {
        const response = await getActiveSessions()
        this.sessionList = response.data
        this.pagination.total = response.data.length
        this.$message.success(`当前有 ${response.data.length} 个活跃会话`)
      } catch (error) {
        this.$message.error('获取活跃会话失败: ' + error.message)
      }
    },
    
    async terminateSession(session) {
      try {
        await this.$confirm(`确定要终止会话 ${session.sessionId} 吗？`, '确认操作', {
          type: 'warning'
        })
        
        await terminateSession(session.sessionId, '管理员手动终止')
        this.$message.success('会话已终止')
        this.loadData()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('终止会话失败: ' + error.message)
        }
      }
    },
    
    async batchTerminate() {
      try {
        await this.$confirm(`确定要终止选中的 ${this.selectedSessions.length} 个会话吗？`, '确认操作', {
          type: 'warning'
        })
        
        const sessionIds = this.selectedSessions.map(session => session.sessionId)
        await batchTerminateSessions(sessionIds, '管理员批量终止')
        this.$message.success('批量终止会话成功')
        this.loadData()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('批量终止会话失败: ' + error.message)
        }
      }
    },
    
    async exportData() {
      try {
        const params = { ...this.searchForm }
        await exportSessions(params)
        this.$message.success('导出成功')
      } catch (error) {
        this.$message.error('导出失败: ' + error.message)
      }
    },
    
    viewDevice(deviceId) {
      this.$router.push(`/sip/devices?deviceId=${deviceId}`)
    },
    
    getSessionTypeText(type) {
      const typeMap = {
        'INVITE': '邀请',
        'PLAY': '播放',
        'PLAYBACK': '回放',
        'DOWNLOAD': '下载',
        'TALK': '对讲'
      }
      return typeMap[type] || type
    },
    
    getSessionTypeColor(type) {
      const colorMap = {
        'INVITE': 'primary',
        'PLAY': 'success',
        'PLAYBACK': 'info',
        'DOWNLOAD': 'warning',
        'TALK': 'danger'
      }
      return colorMap[type] || 'info'
    },
    
    getStatusText(status) {
      if (!status) return '未知'
      const statusMap = {
        'INITIAL': '初始化',
        'INVITING': '邀请中',
        'ESTABLISHED': '已建立',
        'CONFIRMED': '已确认',
        'TERMINATED': '已终止'
      }
      return statusMap[status] || status
    },

    getStatusColor(status) {
      if (!status) return 'info'
      const colorMap = {
        'INITIAL': 'info',
        'INVITING': 'warning',
        'ESTABLISHED': 'success',
        'CONFIRMED': 'success',
        'TERMINATED': 'danger'
      }
      return colorMap[status] || 'info'
    },
    
    formatTime(timestamp) {
      if (!timestamp) return 'N/A'
      return new Date(timestamp).toLocaleString()
    },
    
    formatBytes(bytes) {
      if (!bytes) return '0 B'
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(1024))
      return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
    }
  }
}
</script>

<style lang="scss" scoped>
.sip-sessions {
  padding: 20px;
  
  .search-bar {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    
    .search-form {
      flex: 1;
    }
    
    .action-buttons {
      margin-left: 20px;
    }
  }
  
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
  
  .session-details {
    margin-bottom: 20px;
  }
  
  .sdp-section {
    h4 {
      margin-bottom: 10px;
      color: #333;
    }
  }
  
  .call-id {
    font-family: monospace;
    font-size: 12px;
    color: #666;
  }
  
  .media-info {
    margin-bottom: 20px;
  }
}
</style>
