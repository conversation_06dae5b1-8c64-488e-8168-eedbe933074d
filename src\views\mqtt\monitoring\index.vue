<template>
  <div class="mqtt-monitoring">
    <div class="page-header">
      <h2>MQTT 实时监控</h2>
      <p>实时监控 MQTT 服务器的连接状态和消息流量</p>
    </div>
    
    <el-card>
      <div class="coming-soon">
        <i class="el-icon-monitor" style="font-size: 64px; color: #E6A23C;"></i>
        <h3>功能开发中...</h3>
        <p>MQTT 实时监控功能正在开发中，敬请期待！</p>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'MqttMonitoring'
}
</script>

<style lang="scss" scoped>
.mqtt-monitoring {
  padding: 20px;
  
  .page-header {
    margin-bottom: 20px;
    
    h2 {
      margin: 0 0 8px 0;
      color: #303133;
    }
    
    p {
      margin: 0;
      color: #606266;
    }
  }
  
  .coming-soon {
    text-align: center;
    padding: 60px 20px;
    
    h3 {
      margin: 20px 0 10px 0;
      color: #303133;
    }
    
    p {
      color: #606266;
    }
  }
}
</style>
