import request from '@/utils/request'

// 查询门禁设备参数配置列表
export function listConfig(query) {
  return request({
    url: '/access/config/list',
    method: 'get',
    params: query
  })
}

// 查询门禁设备参数配置详细
export function getConfig(configId) {
  return request({
    url: '/access/config/' + configId,
    method: 'get'
  })
}

// 新增门禁设备参数配置
export function addConfig(data) {
  return request({
    url: '/access/config',
    method: 'post',
    data: data
  })
}

// 修改门禁设备参数配置
export function updateConfig(data) {
  return request({
    url: '/access/config',
    method: 'put',
    data: data
  })
}

// 删除门禁设备参数配置
export function delConfig(configId) {
  return request({
    url: '/access/config/' + configId,
    method: 'delete'
  })
}

// 根据设备ID查询配置列表
export function getConfigByDeviceId(deviceId) {
  return request({
    url: '/access/config/device/' + deviceId,
    method: 'get'
  })
}

// 根据设备ID和配置分组查询配置
export function getConfigByGroup(deviceId, configGroup) {
  return request({
    url: '/access/config/device/' + deviceId + '/group/' + configGroup,
    method: 'get'
  })
}

// 批量保存设备配置
export function batchSaveConfig(deviceId, configList) {
  return request({
    url: '/access/config/device/' + deviceId + '/batch',
    method: 'post',
    data: configList
  })
}

// 获取设备配置（按分组）
export function getDeviceConfigByGroup(deviceId) {
  return request({
    url: '/access/config/device/' + deviceId + '/groups',
    method: 'get'
  })
}

// 获取设备配置值
export function getConfigValue(deviceId, configKey, defaultValue) {
  return request({
    url: '/access/config/device/' + deviceId + '/value',
    method: 'get',
    params: {
      configKey: configKey,
      defaultValue: defaultValue
    }
  })
}

// 设置设备配置值
export function setConfigValue(deviceId, configKey, configValue) {
  return request({
    url: '/access/config/device/' + deviceId + '/value',
    method: 'put',
    data: {
      configKey: configKey,
      configValue: configValue
    }
  })
}

// 重置设备配置为默认值
export function resetConfigToDefault(deviceId) {
  return request({
    url: '/access/config/device/' + deviceId + '/reset',
    method: 'post'
  })
}

// 导入设备配置
export function importConfig(deviceId, configData) {
  return request({
    url: '/access/config/device/' + deviceId + '/import',
    method: 'post',
    data: configData
  })
}

// 导出设备配置
export function exportConfig(deviceId) {
  return request({
    url: '/access/config/device/' + deviceId + '/export',
    method: 'get'
  })
}

// 验证配置值
export function validateConfig(config) {
  return request({
    url: '/access/config/validate',
    method: 'post',
    data: config
  })
}
