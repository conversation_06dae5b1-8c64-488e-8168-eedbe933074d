<template>
  <div class="notification-container">
    <el-badge :value="unreadCount" :max="99" class="badge" v-show="unreadCount > 0">
      <el-tooltip content="审批通知" placement="bottom">
        <div class="notification-icon" @click="showNotifications">
          <svg-icon icon-class="bell" />
        </div>
      </el-tooltip>
    </el-badge>
    <div class="notification-icon" v-show="unreadCount === 0" @click="showNotifications">
      <el-tooltip content="审批通知" placement="bottom">
        <svg-icon icon-class="bell" />
      </el-tooltip>
    </div>

    <!-- 通知弹出抽屉 -->
    <el-drawer
      :visible.sync="drawerVisible"
      direction="rtl"
      size="350px"
      :show-close="true"
      :with-header="false"
      @close="closeDrawer">
      <div class="notification-drawer-header">
        <div class="title">审批通知</div>
        <div class="actions">
          <el-button type="text" size="mini" @click="readAll" :disabled="unreadCount === 0">全部已读</el-button>
        </div>
      </div>
      <div class="notification-list">
        <el-empty description="暂无通知" v-if="notifications.length === 0"></el-empty>
        <div v-else>
          <div 
            v-for="(item, index) in notifications" 
            :key="index" 
            class="notification-item"
            :class="{'unread': !item.isRead}"
            @click="viewNotification(item)">
            <div class="notification-title">
              <span>{{ item.title }}</span>
              <span class="notification-time">{{ formatTime(item.createTime) }}</span>
            </div>
            <div class="notification-content">{{ item.content }}</div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { getApprovalNotifications, markNotificationRead } from '@/api/approval/workflow'
import { parseTime } from '@/utils'

export default {
  name: "NotificationComponent",
  data() {
    return {
      drawerVisible: false,
      notifications: [],
      unreadCount: 0,
      loading: false,
      pollingTimer: null
    }
  },
  created() {
    this.fetchNotifications()
    // 只有在有权限时才启动轮询
    if (this.$auth && this.$auth.hasPermi && this.$auth.hasPermi('approval:notification:list')) {
      this.pollingTimer = setInterval(() => {
        this.fetchNotifications()
      }, 60000)
    }
  },
  beforeDestroy() {
    // 在组件销毁前清除定时器
    if (this.pollingTimer) {
      clearInterval(this.pollingTimer)
    }
  },
  methods: {
    // 获取通知列表
    fetchNotifications() {
      // 检查用户是否有通知权限，避免权限错误
      if (!this.$auth || !this.$auth.hasPermi || !this.$auth.hasPermi('approval:notification:list')) {
        console.log('用户无审批通知权限，跳过通知获取');
        this.notifications = [];
        this.unreadCount = 0;
        return;
      }

      getApprovalNotifications({ pageSize: 50 }).then(response => {
        if (response && response.rows) {
          this.notifications = response.rows || []
          this.unreadCount = this.notifications.filter(item => !item.isRead).length
        } else {
          this.notifications = [];
          this.unreadCount = 0;
        }
      }).catch(error => {
        console.log('获取审批通知失败:', error);
        // 检查是否是权限错误或XML解析错误
        if (error.message && (error.message.includes('XML') || error.message.includes('权限') || error.message.includes('401'))) {
          console.log('权限不足或服务异常，静默处理');
        }
        // 静默处理错误，不影响用户体验
        this.notifications = [];
        this.unreadCount = 0;
      })
    },
    // 显示通知抽屉
    showNotifications() {
      // 检查权限
      if (!this.$auth || !this.$auth.hasPermi || !this.$auth.hasPermi('approval:notification:list')) {
        this.$message.warning('您没有查看审批通知的权限');
        return;
      }

      this.drawerVisible = true
      this.fetchNotifications()
    },
    // 关闭通知抽屉
    closeDrawer() {
      this.drawerVisible = false
    },
    // 查看通知详情
    viewNotification(notification) {
      if (!notification.isRead) {
        // 标记为已读
        markNotificationRead(notification.notificationId).then(() => {
          notification.isRead = true
          this.unreadCount = Math.max(0, this.unreadCount - 1)
        })
      }
      
      // 跳转到对应的审批详情页面
      if (notification.instanceId) {
        this.$router.push({
          path: '/approval/detail',
          query: { instanceId: notification.instanceId }
        })
        this.closeDrawer()
      }
    },
    // 全部标记为已读
    readAll() {
      this.$confirm('确认将所有通知标记为已读?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const request = {
          url: '/approval/notification/readAll',
          method: 'put'
        }
        this.$request(request).then(() => {
          this.notifications.forEach(item => {
            item.isRead = true
          })
          this.unreadCount = 0
          this.$message.success('已全部标记为已读')
        })
      }).catch(() => {})
    },
    // 格式化时间
    formatTime(time) {
      return parseTime(time, '{y}-{m}-{d} {h}:{i}')
    }
  }
}
</script>

<style lang="scss" scoped>
.notification-container {
  display: inline-block;
  height: 100%;
  
  .notification-icon {
    height: 50px;
    line-height: 50px;
    padding: 0 8px;
    cursor: pointer;
    font-size: 18px;
    color: #5a5e66;
    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }
  
  .badge {
    line-height: normal;
  }
}

.notification-drawer-header {
  padding: 15px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .title {
    font-size: 16px;
    font-weight: bold;
  }
}

.notification-list {
  overflow-y: auto;
  height: calc(100% - 50px);
}

.notification-item {
  padding: 12px 15px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  
  &:hover {
    background-color: #f5f7fa;
  }
  
  &.unread {
    background-color: #f0f9eb;
  }
  
  .notification-title {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    font-weight: bold;
  }
  
  .notification-time {
    font-size: 12px;
    color: #909399;
    font-weight: normal;
  }
  
  .notification-content {
    color: #606266;
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
}
</style> 