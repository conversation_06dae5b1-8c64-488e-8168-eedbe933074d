import request from '@/utils/request'

// 查询物料清单列表
export function listMaterialBill(query) {
  return request({
    url: '/material/bill/list',
    method: 'get',
    params: query
  })
}

// 查询物料清单详细
export function getMaterialBill(id) {
  return request({
    url: '/material/bill/' + id,
    method: 'get'
  })
}

// 根据物料清单编号查询详细信息
export function getMaterialBillByCode(billCode) {
  return request({
    url: '/material/bill/code/' + billCode,
    method: 'get'
  })
}

// 新增物料清单
export function addMaterialBill(data) {
  return request({
    url: '/material/bill',
    method: 'post',
    data: data
  })
}

// 修改物料清单
export function updateMaterialBill(data) {
  return request({
    url: '/material/bill',
    method: 'put',
    data: data
  })
}

// 删除物料清单
export function delMaterialBill(id) {
  return request({
    url: '/material/bill/' + id,
    method: 'delete'
  })
}

// 锁定物料清单
export function lockMaterialBill(id) {
  return request({
    url: '/material/bill/lock/' + id,
    method: 'put'
  })
}

// 解锁物料清单
export function unlockMaterialBill(id) {
  return request({
    url: '/material/bill/unlock/' + id,
    method: 'put'
  })
}

// 生成物料清单编号
export function generateBillCode() {
  return request({
    url: '/material/bill/generateCode',
    method: 'get'
  })
}

// 检查物料清单编号是否存在
export function checkBillCode(billCode) {
  return request({
    url: '/material/bill/checkCode/' + billCode,
    method: 'get'
  })
}

// 更新物料清单统计信息
export function updateBillStatistics(id) {
  return request({
    url: '/material/bill/updateStatistics/' + id,
    method: 'put'
  })
}

// 查询锁定的物料清单列表
export function listLockedMaterialBill(query) {
  return request({
    url: '/material/bill/locked',
    method: 'get',
    params: query
  })
}

// 根据状态查询物料清单列表
export function listMaterialBillByStatus(status) {
  return request({
    url: '/material/bill/status/' + status,
    method: 'get'
  })
}

// 根据录入方式查询物料清单列表
export function listMaterialBillByInputMethod(inputMethod) {
  return request({
    url: '/material/bill/inputMethod/' + inputMethod,
    method: 'get'
  })
}

// 查询物料清单统计信息
export function getMaterialBillStatistics() {
  return request({
    url: '/material/bill/statistics',
    method: 'get'
  })
}

// 批量更新物料清单状态
export function batchUpdateBillStatus(ids, status) {
  return request({
    url: '/material/bill/batchUpdateStatus',
    method: 'put',
    data: ids,
    params: { status: status }
  })
}

// 复制物料清单
export function copyMaterialBill(sourceId, newBillName) {
  return request({
    url: '/material/bill/copy/' + sourceId,
    method: 'post',
    params: { newBillName: newBillName }
  })
}

// 导入物料清单数据
export function importMaterialBill(data, updateSupport) {
  return request({
    url: '/material/bill/importData',
    method: 'post',
    data: data,
    params: { updateSupport: updateSupport }
  })
}

// 重量校验
export function validateWeight(id, actualWeight) {
  return request({
    url: '/material/bill/validateWeight/' + id,
    method: 'get',
    params: { actualWeight: actualWeight }
  })
}

// 导出物料清单
export function exportMaterialBill(query) {
  return request({
    url: '/material/bill/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}

// 下载物料清单导入模板
export function importTemplate() {
  return request({
    url: '/material/bill/importTemplate',
    method: 'post',
    responseType: 'blob'
  })
}
