<template>
  <div class="app-container">
    <h2>打印预览测试页面</h2>
    
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>测试打印预览功能</span>
      </div>
      
      <el-form :inline="true">
        <el-form-item label="出入库申请ID:">
          <el-input v-model="testInoutId" placeholder="请输入出入库申请ID" style="width: 200px;"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="testPrintPreview">测试打印预览</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="success" @click="testPrintAPI">测试打印API</el-button>
        </el-form-item>
      </el-form>
      
      <div v-if="apiResponse" style="margin-top: 20px;">
        <h4>API响应结果：</h4>
        <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; max-height: 300px; overflow: auto;">{{ JSON.stringify(apiResponse, null, 2) }}</pre>
      </div>
    </el-card>

    <!-- 打印预览对话框 -->
    <el-dialog
      title="打印预览"
      :visible.sync="printPreviewVisible"
      width="90%"
      :before-close="handleClose"
      append-to-body
    >
      <PrintPreview
        v-if="printPreviewVisible"
        :business-id="currentPrintId"
        business-type="inout"
        :visible="printPreviewVisible"
        @close="printPreviewVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script>
import { getInoutPrintPreview, generateInoutPrintContent, getInoutPrintData } from "@/api/inout/print";
import PrintPreview from "@/components/PrintPreview";

export default {
  name: "PrintPreviewTest",
  components: {
    PrintPreview
  },
  data() {
    return {
      testInoutId: '16', // 默认测试ID
      currentPrintId: null,
      printPreviewVisible: false,
      apiResponse: null
    };
  },
  methods: {
    // 测试打印预览
    testPrintPreview() {
      if (!this.testInoutId) {
        this.$message.warning('请输入出入库申请ID');
        return;
      }
      
      this.currentPrintId = parseInt(this.testInoutId);
      this.printPreviewVisible = true;
    },
    
    // 测试打印API
    async testPrintAPI() {
      if (!this.testInoutId) {
        this.$message.warning('请输入出入库申请ID');
        return;
      }
      
      try {
        this.$message.info('正在测试打印API...');
        
        // 测试获取打印数据API
        console.log('测试 getInoutPrintData API...');
        const dataResponse = await getInoutPrintData(this.testInoutId);
        console.log('getInoutPrintData 响应:', dataResponse);
        
        // 测试打印预览API
        console.log('测试 getInoutPrintPreview API...');
        const previewResponse = await getInoutPrintPreview(this.testInoutId);
        console.log('getInoutPrintPreview 响应:', previewResponse);
        
        // 测试生成打印内容API
        console.log('测试 generateInoutPrintContent API...');
        const contentResponse = await generateInoutPrintContent(this.testInoutId);
        console.log('generateInoutPrintContent 响应:', contentResponse);
        
        this.apiResponse = {
          dataAPI: dataResponse,
          previewAPI: previewResponse,
          contentAPI: contentResponse
        };
        
        this.$message.success('API测试完成，请查看响应结果');
        
      } catch (error) {
        console.error('API测试失败:', error);
        this.$message.error('API测试失败: ' + error.message);
        this.apiResponse = {
          error: error.message,
          details: error
        };
      }
    },
    
    // 关闭对话框
    handleClose() {
      this.printPreviewVisible = false;
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style>
