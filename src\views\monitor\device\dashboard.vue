<template>
  <div class="device-monitor-dashboard">
    <!-- 顶部统计卡片 -->
    <el-row :gutter="20" class="dashboard-stats">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon online">
              <i class="el-icon-success"></i>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ deviceStats.onlineCount }}</div>
              <div class="stat-label">在线设备</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon offline">
              <i class="el-icon-error"></i>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ deviceStats.offlineCount }}</div>
              <div class="stat-label">离线设备</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon warning">
              <i class="el-icon-warning"></i>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ deviceStats.alertCount }}</div>
              <div class="stat-label">告警数量</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon health">
              <i class="el-icon-medal"></i>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ deviceStats.avgHealthScore }}%</div>
              <div class="stat-label">平均健康度</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="dashboard-charts">
      <el-col :span="12">
        <el-card class="chart-card">
          <div slot="header" class="card-header">
            <span>设备状态分布</span>
            <el-button type="text" @click="refreshDeviceStatus">刷新</el-button>
          </div>
          <div ref="deviceStatusChart" class="chart-container"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="chart-card">
          <div slot="header" class="card-header">
            <span>健康评分趋势</span>
            <el-button type="text" @click="refreshHealthTrend">刷新</el-button>
          </div>
          <div ref="healthTrendChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 设备列表和告警 -->
    <el-row :gutter="20" class="dashboard-details">
      <el-col :span="16">
        <el-card class="device-list-card">
          <div slot="header" class="card-header">
            <span>设备状态列表</span>
            <div class="header-actions">
              <el-input
                v-model="deviceFilter"
                placeholder="搜索设备"
                size="small"
                style="width: 200px; margin-right: 10px;"
                prefix-icon="el-icon-search"
              />
              <el-button type="primary" size="small" @click="refreshDeviceList">刷新</el-button>
            </div>
          </div>
          
          <el-table
            :data="filteredDeviceList"
            v-loading="deviceListLoading"
            height="400"
            size="small"
          >
            <el-table-column prop="deviceId" label="设备ID" width="120" />
            <el-table-column prop="deviceName" label="设备名称" width="150" />
            <el-table-column prop="deviceType" label="设备类型" width="100" />
            <el-table-column label="在线状态" width="100">
              <template slot-scope="scope">
                <el-tag :type="scope.row.isOnline ? 'success' : 'danger'" size="small">
                  {{ scope.row.isOnline ? '在线' : '离线' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="健康评分" width="100">
              <template slot-scope="scope">
                <el-progress
                  :percentage="scope.row.healthScore"
                  :color="getHealthColor(scope.row.healthScore)"
                  :show-text="false"
                  :stroke-width="8"
                />
                <span class="health-score">{{ scope.row.healthScore }}%</span>
              </template>
            </el-table-column>
            <el-table-column prop="lastUpdateTime" label="最后更新" width="150" />
            <el-table-column label="操作" width="150">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="viewDeviceDetail(scope.row)">详情</el-button>
                <el-button type="text" size="small" @click="checkDeviceHealth(scope.row)">检查</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card class="alert-list-card">
          <div slot="header" class="card-header">
            <span>最新告警</span>
            <el-button type="text" @click="viewAllAlerts">查看全部</el-button>
          </div>
          
          <div class="alert-list" v-loading="alertListLoading">
            <div
              v-for="alert in recentAlerts"
              :key="alert.alertId"
              class="alert-item"
              :class="'alert-' + alert.alertLevel.toLowerCase()"
            >
              <div class="alert-header">
                <span class="alert-title">{{ alert.message }}</span>
                <span class="alert-time">{{ alert.createTime }}</span>
              </div>
              <div class="alert-content">
                <span class="alert-device">设备: {{ alert.deviceId }}</span>
                <el-tag :type="getAlertLevelType(alert.alertLevel)" size="mini">
                  {{ alert.alertLevel }}
                </el-tag>
              </div>
            </div>
            
            <div v-if="recentAlerts.length === 0" class="no-alerts">
              <i class="el-icon-success"></i>
              <p>暂无告警信息</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getDeviceStatistics, getDeviceList, getRecentAlerts, getDeviceHealthTrend } from '@/api/monitor/device'

export default {
  name: 'DeviceMonitorDashboard',
  data() {
    return {
      // 设备统计数据
      deviceStats: {
        onlineCount: 0,
        offlineCount: 0,
        alertCount: 0,
        avgHealthScore: 0
      },
      
      // 设备列表
      deviceList: [],
      deviceListLoading: false,
      deviceFilter: '',
      
      // 告警列表
      recentAlerts: [],
      alertListLoading: false,
      
      // 图表实例
      deviceStatusChart: null,
      healthTrendChart: null,
      
      // 定时器
      refreshTimer: null
    }
  },
  
  computed: {
    filteredDeviceList() {
      if (!this.deviceFilter) {
        return this.deviceList
      }
      return this.deviceList.filter(device => 
        device.deviceId.toLowerCase().includes(this.deviceFilter.toLowerCase()) ||
        device.deviceName.toLowerCase().includes(this.deviceFilter.toLowerCase())
      )
    }
  },
  
  mounted() {
    this.initDashboard()
    this.startAutoRefresh()
  },
  
  beforeDestroy() {
    this.stopAutoRefresh()
    if (this.deviceStatusChart) {
      this.deviceStatusChart.dispose()
    }
    if (this.healthTrendChart) {
      this.healthTrendChart.dispose()
    }
  },
  
  methods: {
    /** 初始化仪表盘 */
    async initDashboard() {
      await Promise.all([
        this.loadDeviceStatistics(),
        this.loadDeviceList(),
        this.loadRecentAlerts()
      ])
      
      this.$nextTick(() => {
        this.initCharts()
      })
    },
    
    /** 加载设备统计数据 */
    async loadDeviceStatistics() {
      try {
        const response = await getDeviceStatistics()
        this.deviceStats = response.data
      } catch (error) {
        console.error('加载设备统计失败:', error)
        // 使用模拟数据
        this.deviceStats = {
          onlineCount: 45,
          offlineCount: 5,
          alertCount: 3,
          avgHealthScore: 85
        }
      }
    },
    
    /** 加载设备列表 */
    async loadDeviceList() {
      this.deviceListLoading = true
      try {
        const response = await getDeviceList({ pageNum: 1, pageSize: 20 })
        this.deviceList = response.rows || []
      } catch (error) {
        console.error('加载设备列表失败:', error)
        // 使用模拟数据
        this.deviceList = [
          {
            deviceId: 'DEV001',
            deviceName: 'RFID读写器01',
            deviceType: 'RFID',
            isOnline: true,
            healthScore: 95,
            lastUpdateTime: '2025-01-27 10:30:00'
          },
          {
            deviceId: 'DEV002',
            deviceName: '称重传感器01',
            deviceType: 'WEIGHT',
            isOnline: true,
            healthScore: 88,
            lastUpdateTime: '2025-01-27 10:29:45'
          },
          {
            deviceId: 'DEV003',
            deviceName: '门禁控制器01',
            deviceType: 'ACCESS',
            isOnline: false,
            healthScore: 65,
            lastUpdateTime: '2025-01-27 09:15:20'
          }
        ]
      } finally {
        this.deviceListLoading = false
      }
    },
    
    /** 加载最新告警 */
    async loadRecentAlerts() {
      this.alertListLoading = true
      try {
        const response = await getRecentAlerts({ limit: 10 })
        this.recentAlerts = response.data || []
      } catch (error) {
        console.error('加载告警列表失败:', error)
        // 使用模拟数据
        this.recentAlerts = [
          {
            alertId: 'ALT001',
            deviceId: 'DEV003',
            message: '设备离线',
            alertLevel: 'CRITICAL',
            createTime: '10:15'
          },
          {
            alertId: 'ALT002',
            deviceId: 'DEV005',
            message: 'CPU使用率过高',
            alertLevel: 'WARNING',
            createTime: '09:45'
          }
        ]
      } finally {
        this.alertListLoading = false
      }
    },
    
    /** 初始化图表 */
    initCharts() {
      this.initDeviceStatusChart()
      this.initHealthTrendChart()
    },
    
    /** 初始化设备状态图表 */
    initDeviceStatusChart() {
      this.deviceStatusChart = echarts.init(this.$refs.deviceStatusChart)
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 10,
          data: ['在线', '离线', '故障']
        },
        series: [
          {
            name: '设备状态',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['60%', '50%'],
            data: [
              { value: this.deviceStats.onlineCount, name: '在线', itemStyle: { color: '#67C23A' } },
              { value: this.deviceStats.offlineCount, name: '离线', itemStyle: { color: '#F56C6C' } },
              { value: 2, name: '故障', itemStyle: { color: '#E6A23C' } }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      
      this.deviceStatusChart.setOption(option)
    },
    
    /** 初始化健康趋势图表 */
    async initHealthTrendChart() {
      this.healthTrendChart = echarts.init(this.$refs.healthTrendChart)
      
      try {
        const response = await getDeviceHealthTrend({ days: 7 })
        const trendData = response.data || []
        
        const option = {
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            data: ['平均健康度', '在线率']
          },
          xAxis: {
            type: 'category',
            data: trendData.map(item => item.date) || ['01-21', '01-22', '01-23', '01-24', '01-25', '01-26', '01-27']
          },
          yAxis: {
            type: 'value',
            min: 0,
            max: 100,
            axisLabel: {
              formatter: '{value}%'
            }
          },
          series: [
            {
              name: '平均健康度',
              type: 'line',
              data: trendData.map(item => item.healthScore) || [82, 85, 88, 86, 89, 87, 85],
              itemStyle: { color: '#409EFF' },
              smooth: true
            },
            {
              name: '在线率',
              type: 'line',
              data: trendData.map(item => item.onlineRate) || [95, 98, 96, 97, 99, 98, 96],
              itemStyle: { color: '#67C23A' },
              smooth: true
            }
          ]
        }
        
        this.healthTrendChart.setOption(option)
      } catch (error) {
        console.error('加载健康趋势数据失败:', error)
      }
    },
    
    /** 获取健康度颜色 */
    getHealthColor(score) {
      if (score >= 90) return '#67C23A'
      if (score >= 70) return '#E6A23C'
      return '#F56C6C'
    },
    
    /** 获取告警级别类型 */
    getAlertLevelType(level) {
      const typeMap = {
        'CRITICAL': 'danger',
        'WARNING': 'warning',
        'INFO': 'info'
      }
      return typeMap[level] || 'info'
    },
    
    /** 刷新设备状态图表 */
    refreshDeviceStatus() {
      this.loadDeviceStatistics().then(() => {
        this.initDeviceStatusChart()
      })
    },
    
    /** 刷新健康趋势图表 */
    refreshHealthTrend() {
      this.initHealthTrendChart()
    },
    
    /** 刷新设备列表 */
    refreshDeviceList() {
      this.loadDeviceList()
    },
    
    /** 查看设备详情 */
    viewDeviceDetail(device) {
      this.$router.push({
        path: '/monitor/device/detail',
        query: { deviceId: device.deviceId }
      })
    },
    
    /** 检查设备健康 */
    checkDeviceHealth(device) {
      this.$message.info(`正在检查设备 ${device.deviceId} 的健康状态...`)
      // 这里可以调用健康检查API
    },
    
    /** 查看所有告警 */
    viewAllAlerts() {
      this.$router.push('/monitor/alert/list')
    },
    
    /** 开始自动刷新 */
    startAutoRefresh() {
      this.refreshTimer = setInterval(() => {
        this.loadDeviceStatistics()
        this.loadRecentAlerts()
      }, 30000) // 30秒刷新一次
    },
    
    /** 停止自动刷新 */
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    }
  }
}
</script>

<style scoped>
.device-monitor-dashboard {
  padding: 20px;
}

.dashboard-stats {
  margin-bottom: 20px;
}

.stat-card {
  height: 100px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stat-icon.online {
  background: linear-gradient(135deg, #67C23A, #85CE61);
}

.stat-icon.offline {
  background: linear-gradient(135deg, #F56C6C, #F78989);
}

.stat-icon.warning {
  background: linear-gradient(135deg, #E6A23C, #EEBE77);
}

.stat-icon.health {
  background: linear-gradient(135deg, #409EFF, #66B1FF);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.dashboard-charts {
  margin-bottom: 20px;
}

.chart-card {
  height: 400px;
}

.chart-container {
  height: 320px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
}

.device-list-card {
  height: 500px;
}

.alert-list-card {
  height: 500px;
}

.alert-list {
  max-height: 420px;
  overflow-y: auto;
}

.alert-item {
  padding: 12px;
  border-left: 4px solid #ddd;
  margin-bottom: 10px;
  background: #f9f9f9;
  border-radius: 4px;
}

.alert-item.alert-critical {
  border-left-color: #F56C6C;
}

.alert-item.alert-warning {
  border-left-color: #E6A23C;
}

.alert-item.alert-info {
  border-left-color: #409EFF;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.alert-title {
  font-weight: bold;
  color: #303133;
}

.alert-time {
  font-size: 12px;
  color: #909399;
}

.alert-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.alert-device {
  font-size: 12px;
  color: #606266;
}

.health-score {
  margin-left: 8px;
  font-size: 12px;
  color: #606266;
}

.no-alerts {
  text-align: center;
  padding: 40px 0;
  color: #909399;
}

.no-alerts i {
  font-size: 48px;
  margin-bottom: 10px;
  color: #67C23A;
}
</style>
