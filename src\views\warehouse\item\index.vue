<template>
  <div class="app-container">
    <el-card class="filter-container" shadow="hover">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
        <el-form-item label="物品编号" prop="itemCode">
          <el-input
            v-model="queryParams.itemCode"
            placeholder="请输入物品编号"
            clearable
            prefix-icon="el-icon-document"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="物品名称" prop="itemName">
          <el-input
            v-model="queryParams.itemName"
            placeholder="请输入物品名称"
            clearable
            prefix-icon="el-icon-goods"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="物品类别" prop="category">
          <el-select v-model="queryParams.category" placeholder="请选择物品类别" clearable>
            <el-option label="原材料" value="0" />
            <el-option label="成品" value="1" />
            <el-option label="半成品" value="2" />
            <el-option label="耗材" value="3" />
            <el-option label="工具" value="4" />
          </el-select>
        </el-form-item>
        <el-form-item label="危险等级" prop="hazardLevel">
          <el-select v-model="queryParams.hazardLevel" placeholder="请选择危险等级" clearable>
            <el-option label="安全" value="0" />
            <el-option label="低危" value="1" />
            <el-option label="中危" value="2" />
            <el-option label="高危" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="存放库位" prop="locationId">
          <el-select v-model="queryParams.locationId" placeholder="请选择库位" clearable>
            <el-option
              v-for="item in locationOptions"
              :key="item.id"
              :label="item.locationCode"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="table-container" shadow="hover" style="margin-top: 10px;">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['warehouse:item:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['warehouse:item:edit']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['warehouse:item:remove']"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['warehouse:item:export']"
          >导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="itemList" @selection-change="handleSelectionChange" border stripe highlight-current-row>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="物品ID" align="center" prop="id" width="80" />
        <el-table-column label="物品编号" align="center" prop="itemCode" min-width="120" />
        <el-table-column label="物品名称" align="center" prop="itemName" min-width="120" show-overflow-tooltip />
        <el-table-column label="物品类别" align="center" prop="category" width="100">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.category == '0'" type="success">原材料</el-tag>
            <el-tag v-else-if="scope.row.category == '1'" type="primary">成品</el-tag>
            <el-tag v-else-if="scope.row.category == '2'" type="warning">半成品</el-tag>
            <el-tag v-else-if="scope.row.category == '3'" type="info">耗材</el-tag>
            <el-tag v-else-if="scope.row.category == '4'" type="info">工具</el-tag>
            <el-tag v-else>{{ scope.row.category }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="危险等级" align="center" prop="hazardLevel" width="90">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.hazardLevel == '0'" type="success">安全</el-tag>
            <el-tag v-else-if="scope.row.hazardLevel == '1'" type="info">低危</el-tag>
            <el-tag v-else-if="scope.row.hazardLevel == '2'" type="warning">中危</el-tag>
            <el-tag v-else-if="scope.row.hazardLevel == '3'" type="danger">高危</el-tag>
            <el-tag v-else>{{ scope.row.hazardLevel }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="规格" align="center" prop="specification" min-width="100" show-overflow-tooltip />
        <el-table-column label="单位" align="center" prop="unit" width="80" />
        <el-table-column label="数量" align="center" prop="quantity" width="90" />
        <el-table-column label="库位编号" align="center" prop="locationCode" min-width="120" />
        <el-table-column label="有效期" align="center" prop="expiryDate" width="120">
          <template slot-scope="scope">
            <span v-if="scope.row.expiryDate">{{ parseTime(scope.row.expiryDate, '{y}-{m}-{d}') }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
              v-hasPermi="['warehouse:item:query']"
            >详情</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['warehouse:item:edit']"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['warehouse:item:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改物品信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="650px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="物品编号" prop="itemCode">
              <el-input v-model="form.itemCode" placeholder="请输入物品编号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物品名称" prop="itemName">
              <el-input v-model="form.itemName" placeholder="请输入物品名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="物品类别" prop="category">
              <el-select v-model="form.category" placeholder="请选择物品类别" style="width: 100%">
                <el-option label="原材料" value="0" />
                <el-option label="成品" value="1" />
                <el-option label="半成品" value="2" />
                <el-option label="耗材" value="3" />
                <el-option label="工具" value="4" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="危险等级" prop="hazardLevel">
              <el-radio-group v-model="form.hazardLevel">
                <el-radio label="0">安全</el-radio>
                <el-radio label="1">低危</el-radio>
                <el-radio label="2">中危</el-radio>
                <el-radio label="3">高危</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="规格" prop="specification">
              <el-input v-model="form.specification" placeholder="请输入规格" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位" prop="unit">
              <el-select v-model="form.unit" placeholder="请选择单位" style="width: 100%">
                <el-option label="个" value="个" />
                <el-option label="箱" value="箱" />
                <el-option label="件" value="件" />
                <el-option label="kg" value="kg" />
                <el-option label="g" value="g" />
                <el-option label="L" value="L" />
                <el-option label="mL" value="mL" />
                <el-option label="m" value="m" />
                <el-option label="cm" value="cm" />
                <el-option label="m²" value="m²" />
                <el-option label="m³" value="m³" />
                <el-option label="套" value="套" />
                <el-option label="台" value="台" />
                <el-option label="包" value="包" />
                <el-option label="桶" value="桶" />
                <el-option label="袋" value="袋" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="数量" prop="quantity">
              <el-input-number v-model="form.quantity" :min="0" :precision="2" :step="1" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="库位" prop="locationId">
              <el-cascader
                v-model="locationCascader"
                :options="warehouseZoneOptions"
                :props="{ checkStrictly: true, value: 'id', label: 'label', children: 'children', emitPath: false }"
                @change="handleLocationChange"
                placeholder="请选择库位"
                style="width: 100%"
                clearable
              ></el-cascader>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="有效期" prop="expiryDate">
              <el-date-picker
                v-model="form.expiryDate"
                type="date"
                placeholder="选择有效期"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="供应商" prop="supplier">
              <el-input v-model="form.supplier" placeholder="请输入供应商" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 物品详情对话框 -->
    <el-dialog title="物品详情" :visible.sync="viewDialog" width="700px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="物品编号">{{ viewForm.itemCode }}</el-descriptions-item>
        <el-descriptions-item label="物品名称">{{ viewForm.itemName }}</el-descriptions-item>
        <el-descriptions-item label="物品类别">
          <el-tag v-if="viewForm.category == '0'" type="success">原材料</el-tag>
          <el-tag v-else-if="viewForm.category == '1'" type="primary">成品</el-tag>
          <el-tag v-else-if="viewForm.category == '2'" type="warning">半成品</el-tag>
          <el-tag v-else-if="viewForm.category == '3'" type="info">耗材</el-tag>
          <el-tag v-else-if="viewForm.category == '4'" type="info">工具</el-tag>
          <el-tag v-else>{{ viewForm.category }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="危险等级">
          <el-tag v-if="viewForm.hazardLevel == '0'" type="success">安全</el-tag>
          <el-tag v-else-if="viewForm.hazardLevel == '1'" type="info">低危</el-tag>
          <el-tag v-else-if="viewForm.hazardLevel == '2'" type="warning">中危</el-tag>
          <el-tag v-else-if="viewForm.hazardLevel == '3'" type="danger">高危</el-tag>
          <el-tag v-else>{{ viewForm.hazardLevel }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="规格">{{ viewForm.specification }}</el-descriptions-item>
        <el-descriptions-item label="单位">{{ viewForm.unit }}</el-descriptions-item>
        <el-descriptions-item label="数量">{{ viewForm.quantity }}</el-descriptions-item>
        <el-descriptions-item label="存放库位">{{ viewForm.locationCode }}</el-descriptions-item>
        <el-descriptions-item label="有效期">{{ viewForm.expiryDate ? parseTime(viewForm.expiryDate) : '-' }}</el-descriptions-item>
        <el-descriptions-item label="供应商">{{ viewForm.supplier || '-' }}</el-descriptions-item>
        <el-descriptions-item label="入库时间">{{ viewForm.createTime ? parseTime(viewForm.createTime) : '-' }}</el-descriptions-item>
        <el-descriptions-item label="最后更新">{{ viewForm.updateTime ? parseTime(viewForm.updateTime) : '-' }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ viewForm.remark }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewDialog = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listItem, getItem, delItem, addItem, updateItem } from "@/api/warehouse/item"
import { listLocation } from "@/api/warehouse/location"
import { listWarehouse } from "@/api/warehouse/warehouse"
import { listZone } from "@/api/warehouse/zone"

export default {
  name: "Item",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 物品信息表格数据
      itemList: [],
      // 库位选项
      locationOptions: [],
      // 级联选择的仓库区位选项
      warehouseZoneOptions: [],
      // 级联选择的区位值
      locationCascader: null,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示详情弹出层
      viewDialog: false,
      // 详情表单
      viewForm: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        itemCode: null,
        itemName: null,
        category: null,
        hazardLevel: null,
        locationId: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        itemCode: [
          { required: true, message: "物品编号不能为空", trigger: "blur" }
        ],
        itemName: [
          { required: true, message: "物品名称不能为空", trigger: "blur" }
        ],
        category: [
          { required: true, message: "物品类别不能为空", trigger: "change" }
        ],
        hazardLevel: [
          { required: true, message: "危险等级不能为空", trigger: "change" }
        ],
        unit: [
          { required: true, message: "单位不能为空", trigger: "change" }
        ],
        quantity: [
          { required: true, message: "数量不能为空", trigger: "blur" }
        ]
      }
    }
  },
  created() {
    this.getList()
    this.getLocationOptions()
    this.getWarehouseZoneOptions()
  },
  methods: {
    /** 查询物品信息列表 */
    getList() {
      this.loading = true
      listItem(this.queryParams).then(response => {
        this.itemList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 查询库位选项 */
    getLocationOptions() {
      listLocation({ status: "0" }).then(response => {
        this.locationOptions = response.rows
      })
    },
    /** 查询级联选择的仓库区位选项 */
    getWarehouseZoneOptions() {
      const warehouseOptions = []
      
      // 获取所有仓库
      listWarehouse().then(response => {
        const warehouses = response.rows
        
        // 获取所有区域
        listZone().then(zoneResponse => {
          const zones = zoneResponse.rows
          
          // 获取所有库位
          listLocation().then(locationResponse => {
            const locations = locationResponse.rows
            
            // 构建级联选择数据
            warehouses.forEach(warehouse => {
              const warehouseNode = {
                id: 'w_' + warehouse.id,
                label: warehouse.warehouseName,
                children: []
              }
              
              // 添加区域子节点
              const warehouseZones = zones.filter(zone => zone.warehouseId === warehouse.id)
              warehouseZones.forEach(zone => {
                const zoneNode = {
                  id: 'z_' + zone.id,
                  label: zone.zoneName,
                  children: []
                }
                
                // 添加库位子节点
                const zoneLocations = locations.filter(location => location.zoneId === zone.id)
                zoneLocations.forEach(location => {
                  zoneNode.children.push({
                    id: location.id,
                    label: location.locationCode,
                    status: location.status
                  })
                })
                
                warehouseNode.children.push(zoneNode)
              })
              
              warehouseOptions.push(warehouseNode)
            })
            
            this.warehouseZoneOptions = warehouseOptions
          })
        })
      })
    },
    // 库位级联选择器变化
    handleLocationChange(val) {
      this.form.locationId = val
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        itemCode: null,
        itemName: null,
        category: "0",
        hazardLevel: "0",
        specification: null,
        unit: "个",
        quantity: 0,
        locationId: null,
        expiryDate: null,
        supplier: null,
        remark: null
      }
      this.locationCascader = null
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加物品信息"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getItem(id).then(response => {
        this.form = response.data
        this.locationCascader = this.form.locationId
        this.open = true
        this.title = "修改物品信息"
      })
    },
    /** 查看详情按钮操作 */
    handleView(row) {
      this.viewForm = {}
      getItem(row.id).then(response => {
        this.viewForm = response.data
        this.viewDialog = true
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateItem(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addItem(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除物品信息编号为"' + ids + '"的数据项？').then(() => {
        return delItem(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('warehouse/item/export', {
        ...this.queryParams
      }, `item_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>

<style scoped>
.filter-container {
  margin-bottom: 10px;
}
.table-container {
  margin-bottom: 20px;
}
.el-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style> 