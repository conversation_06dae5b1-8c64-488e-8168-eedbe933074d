<template>
  <div class="weight-monitor">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon">
            <i class="el-icon-scale-to-original"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.sensorCount || 0 }}</div>
            <div class="stat-label">传感器数量</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon warning">
            <i class="el-icon-warning"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.alertCount || 0 }}</div>
            <div class="stat-label">告警数量</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon success">
            <i class="el-icon-data-analysis"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ formatWeight(stats.avgWeight) }}</div>
            <div class="stat-label">平均重量</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon info">
            <i class="el-icon-data-board"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.totalCount || 0 }}</div>
            <div class="stat-label">数据总量</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 实时监控 -->
    <el-card class="realtime-monitor" shadow="never">
      <div slot="header" class="card-header">
        <span>实时监控</span>
        <div class="header-actions">
          <el-button 
            type="text" 
            icon="el-icon-refresh" 
            @click="refreshRealTimeData"
            :loading="realtimeLoading"
          >
            刷新
          </el-button>
          <el-switch
            v-model="autoRefresh"
            active-text="自动刷新"
            @change="toggleAutoRefresh"
          ></el-switch>
        </div>
      </div>
      
      <el-row :gutter="20">
        <el-col 
          v-for="sensor in realtimeData" 
          :key="sensor.sensorId" 
          :span="8"
          style="margin-bottom: 20px;"
        >
          <div class="sensor-card" :class="{ 'alert': sensor.alertStatus === 1 }">
            <div class="sensor-header">
              <div class="sensor-name">{{ sensor.deviceName || sensor.sensorId }}</div>
              <div class="sensor-status">
                <el-tag 
                  :type="sensor.alertStatus === 1 ? 'danger' : 'success'" 
                  size="mini"
                >
                  {{ sensor.alertStatus === 1 ? '告警' : '正常' }}
                </el-tag>
              </div>
            </div>
            <div class="sensor-content">
              <div class="weight-display">
                <span class="weight-value">{{ formatWeight(sensor.weightValue) }}</span>
                <span class="weight-unit">{{ sensor.unit || 'kg' }}</span>
              </div>
              <div class="sensor-info">
                <div class="info-item" v-if="sensor.location">
                  <i class="el-icon-location"></i>
                  <span>{{ sensor.location }}</span>
                </div>
                <div class="info-item" v-if="sensor.temperature">
                  <i class="el-icon-partly-cloudy"></i>
                  <span>{{ sensor.temperature }}°C</span>
                </div>
                <div class="info-item" v-if="sensor.humidity">
                  <i class="el-icon-heavy-rain"></i>
                  <span>{{ sensor.humidity }}%</span>
                </div>
                <div class="info-item" v-if="sensor.batteryLevel">
                  <i class="el-icon-mobile"></i>
                  <span>{{ sensor.batteryLevel }}%</span>
                </div>
              </div>
              <div class="sensor-time">
                <i class="el-icon-time"></i>
                <span>{{ formatTime(sensor.recordTime) }}</span>
              </div>
            </div>
            <div class="sensor-actions">
              <el-button 
                type="text" 
                size="mini" 
                @click="showTrendChart(sensor.sensorId)"
              >
                趋势图
              </el-button>
              <el-button 
                type="text" 
                size="mini" 
                @click="showSensorDetail(sensor)"
              >
                详情
              </el-button>
            </div>
          </div>
        </el-col>
      </el-row>
      
      <div v-if="realtimeData.length === 0" class="empty-data">
        <el-empty description="暂无实时数据"></el-empty>
      </div>
    </el-card>

    <!-- 历史数据 -->
    <el-card class="history-data" shadow="never">
      <div slot="header" class="card-header">
        <span>历史数据</span>
        <div class="header-actions">
          <el-button type="primary" icon="el-icon-search" @click="queryHistoryData">查询</el-button>
          <el-button type="success" icon="el-icon-download" @click="exportData">导出</el-button>
        </div>
      </div>
      
      <!-- 查询条件 -->
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
        <el-form-item label="传感器ID" prop="sensorId">
          <el-input
            v-model="queryParams.sensorId"
            placeholder="请输入传感器ID"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="设备名称" prop="deviceName">
          <el-input
            v-model="queryParams.deviceName"
            placeholder="请输入设备名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="位置" prop="location">
          <el-input
            v-model="queryParams.location"
            placeholder="请输入位置"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="告警状态" prop="alertStatus">
          <el-select v-model="queryParams.alertStatus" placeholder="请选择告警状态" clearable>
            <el-option label="正常" value="0"></el-option>
            <el-option label="告警" value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="记录时间">
          <el-date-picker
            v-model="dateRange"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="weightList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="传感器ID" align="center" prop="sensorId" />
        <el-table-column label="设备名称" align="center" prop="deviceName" />
        <el-table-column label="重量值" align="center" prop="weightValue">
          <template slot-scope="scope">
            <span>{{ formatWeight(scope.row.weightValue) }} {{ scope.row.unit || 'kg' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="位置" align="center" prop="location" />
        <el-table-column label="告警状态" align="center" prop="alertStatus">
          <template slot-scope="scope">
            <el-tag :type="scope.row.alertStatus === 1 ? 'danger' : 'success'">
              {{ scope.row.alertStatus === 1 ? '告警' : '正常' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="温度" align="center" prop="temperature">
          <template slot-scope="scope">
            <span v-if="scope.row.temperature">{{ scope.row.temperature }}°C</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="湿度" align="center" prop="humidity">
          <template slot-scope="scope">
            <span v-if="scope.row.humidity">{{ scope.row.humidity }}%</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="电量" align="center" prop="batteryLevel">
          <template slot-scope="scope">
            <span v-if="scope.row.batteryLevel">{{ scope.row.batteryLevel }}%</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="记录时间" align="center" prop="recordTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.recordTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
            >查看</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-data-line"
              @click="showTrendChart(scope.row.sensorId)"
            >趋势</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 趋势图对话框 -->
    <el-dialog title="重量趋势图" :visible.sync="trendDialogVisible" width="80%" top="5vh">
      <div class="trend-controls">
        <el-radio-group v-model="trendHours" @change="loadTrendData">
          <el-radio-button :label="1">1小时</el-radio-button>
          <el-radio-button :label="6">6小时</el-radio-button>
          <el-radio-button :label="24">24小时</el-radio-button>
          <el-radio-button :label="72">3天</el-radio-button>
          <el-radio-button :label="168">7天</el-radio-button>
        </el-radio-group>
      </div>
      <div ref="trendChart" style="height: 400px;"></div>
    </el-dialog>

    <!-- 传感器详情对话框 -->
    <el-dialog title="传感器详情" :visible.sync="detailDialogVisible" width="600px">
      <el-descriptions :column="2" border v-if="currentSensor">
        <el-descriptions-item label="传感器ID">{{ currentSensor.sensorId }}</el-descriptions-item>
        <el-descriptions-item label="设备名称">{{ currentSensor.deviceName }}</el-descriptions-item>
        <el-descriptions-item label="当前重量">
          {{ formatWeight(currentSensor.weightValue) }} {{ currentSensor.unit || 'kg' }}
        </el-descriptions-item>
        <el-descriptions-item label="告警状态">
          <el-tag :type="currentSensor.alertStatus === 1 ? 'danger' : 'success'">
            {{ currentSensor.alertStatus === 1 ? '告警' : '正常' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="位置">{{ currentSensor.location || '-' }}</el-descriptions-item>
        <el-descriptions-item label="温度">
          {{ currentSensor.temperature ? currentSensor.temperature + '°C' : '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="湿度">
          {{ currentSensor.humidity ? currentSensor.humidity + '%' : '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="电量">
          {{ currentSensor.batteryLevel ? currentSensor.batteryLevel + '%' : '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="最大重量">
          {{ currentSensor.maxWeight ? formatWeight(currentSensor.maxWeight) + ' ' + (currentSensor.unit || 'kg') : '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="最小重量">
          {{ currentSensor.minWeight ? formatWeight(currentSensor.minWeight) + ' ' + (currentSensor.unit || 'kg') : '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="记录时间" span="2">
          {{ parseTime(currentSensor.recordTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import { 
  listWeightData, 
  getWeightStats, 
  getRealTimeWeightData, 
  getWeightTrendData,
  exportWeightData 
} from '@/api/dwms/weight'
import * as echarts from 'echarts'

export default {
  name: 'WeightMonitor',
  data() {
    return {
      // 加载状态
      loading: true,
      realtimeLoading: false,
      
      // 列表数据
      weightList: [],
      total: 0,
      
      // 实时数据
      realtimeData: [],
      autoRefresh: false,
      refreshTimer: null,
      
      // 统计数据
      stats: {},
      
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        sensorId: null,
        deviceName: null,
        location: null,
        alertStatus: null
      },
      
      // 日期范围
      dateRange: [],
      
      // 显示搜索条件
      showSearch: true,
      
      // 选中数组
      ids: [],
      single: true,
      multiple: true,
      
      // 趋势图
      trendDialogVisible: false,
      trendChart: null,
      trendSensorId: null,
      trendHours: 24,
      
      // 详情对话框
      detailDialogVisible: false,
      currentSensor: null
    }
  },
  created() {
    this.getList()
    this.getStats()
    this.getRealTimeData()
  },
  beforeDestroy() {
    this.clearRefreshTimer()
    if (this.trendChart) {
      this.trendChart.dispose()
    }
  },
  methods: {
    /** 查询重量传感器数据列表 */
    getList() {
      this.loading = true
      this.addDateRange(this.queryParams, this.dateRange)
      listWeightData(this.queryParams).then(response => {
        this.weightList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    
    /** 查询统计数据 */
    getStats() {
      getWeightStats().then(response => {
        this.stats = response.data || {}
      })
    },
    
    /** 获取实时数据 */
    getRealTimeData() {
      this.realtimeLoading = true
      getRealTimeWeightData().then(response => {
        this.realtimeData = response.data || []
        this.realtimeLoading = false
      }).catch(() => {
        this.realtimeLoading = false
      })
    },
    
    /** 刷新实时数据 */
    refreshRealTimeData() {
      this.getRealTimeData()
      this.getStats()
    },
    
    /** 切换自动刷新 */
    toggleAutoRefresh(enabled) {
      if (enabled) {
        this.refreshTimer = setInterval(() => {
          this.refreshRealTimeData()
        }, 30000) // 30秒刷新一次
      } else {
        this.clearRefreshTimer()
      }
    },
    
    /** 清除刷新定时器 */
    clearRefreshTimer() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },
    
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    
    /** 查看操作 */
    handleView(row) {
      this.currentSensor = row
      this.detailDialogVisible = true
    },
    
    /** 显示传感器详情 */
    showSensorDetail(sensor) {
      this.currentSensor = sensor
      this.detailDialogVisible = true
    },
    
    /** 显示趋势图 */
    showTrendChart(sensorId) {
      this.trendSensorId = sensorId
      this.trendDialogVisible = true
      this.$nextTick(() => {
        this.initTrendChart()
        this.loadTrendData()
      })
    },
    
    /** 初始化趋势图 */
    initTrendChart() {
      if (this.trendChart) {
        this.trendChart.dispose()
      }
      this.trendChart = echarts.init(this.$refs.trendChart)
    },
    
    /** 加载趋势数据 */
    loadTrendData() {
      if (!this.trendSensorId) return
      
      getWeightTrendData(this.trendSensorId, this.trendHours).then(response => {
        const data = response.data || []
        this.renderTrendChart(data)
      })
    },
    
    /** 渲染趋势图 */
    renderTrendChart(data) {
      const times = data.map(item => item.time)
      const weights = data.map(item => item.weight)
      const temperatures = data.map(item => item.temperature)
      
      const option = {
        title: {
          text: `传感器 ${this.trendSensorId} 重量趋势`,
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        legend: {
          data: ['重量', '温度'],
          bottom: 0
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: times,
          axisLabel: {
            formatter: function(value) {
              return value.split(' ')[1] // 只显示时间部分
            }
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '重量(kg)',
            position: 'left'
          },
          {
            type: 'value',
            name: '温度(°C)',
            position: 'right'
          }
        ],
        series: [
          {
            name: '重量',
            type: 'line',
            data: weights,
            smooth: true,
            itemStyle: {
              color: '#409EFF'
            }
          },
          {
            name: '温度',
            type: 'line',
            yAxisIndex: 1,
            data: temperatures,
            smooth: true,
            itemStyle: {
              color: '#67C23A'
            }
          }
        ]
      }
      
      this.trendChart.setOption(option)
    },
    
    /** 查询历史数据 */
    queryHistoryData() {
      this.handleQuery()
    },
    
    /** 导出数据 */
    exportData() {
      this.download('dwms/weight/export', {
        ...this.queryParams
      }, `weight_data_${new Date().getTime()}.xlsx`)
    },
    
    /** 格式化重量 */
    formatWeight(weight) {
      if (weight === null || weight === undefined) return '-'
      return Number(weight).toFixed(2)
    },
    
    /** 格式化时间 */
    formatTime(time) {
      if (!time) return '-'
      return this.parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')
    }
  }
}
</script>

<style lang="scss" scoped>
.weight-monitor {
  padding: 20px;

  .stats-cards {
    margin-bottom: 20px;

    .stat-card {
      display: flex;
      align-items: center;
      padding: 20px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);

      .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        background: #409EFF;
        color: #fff;
        font-size: 24px;

        &.warning {
          background: #E6A23C;
        }

        &.success {
          background: #67C23A;
        }

        &.info {
          background: #909399;
        }
      }

      .stat-content {
        flex: 1;

        .stat-value {
          font-size: 28px;
          font-weight: bold;
          color: #333;
          line-height: 1;
          margin-bottom: 8px;
        }

        .stat-label {
          font-size: 14px;
          color: #666;
        }
      }
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-actions {
      display: flex;
      align-items: center;
      gap: 16px;
    }
  }

  .realtime-monitor {
    margin-bottom: 20px;

    .sensor-card {
      background: #fff;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      padding: 16px;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        transform: translateY(-2px);
      }

      &.alert {
        border-color: #F56C6C;
        background: #fef0f0;
      }

      .sensor-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .sensor-name {
          font-size: 16px;
          font-weight: 500;
          color: #333;
        }
      }

      .sensor-content {
        .weight-display {
          text-align: center;
          margin-bottom: 16px;

          .weight-value {
            font-size: 32px;
            font-weight: bold;
            color: #409EFF;
          }

          .weight-unit {
            font-size: 16px;
            color: #666;
            margin-left: 4px;
          }
        }

        .sensor-info {
          margin-bottom: 12px;

          .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
            font-size: 12px;
            color: #666;

            i {
              margin-right: 4px;
              width: 14px;
            }
          }
        }

        .sensor-time {
          display: flex;
          align-items: center;
          font-size: 12px;
          color: #999;

          i {
            margin-right: 4px;
          }
        }
      }

      .sensor-actions {
        margin-top: 12px;
        text-align: center;
        border-top: 1px solid #f0f0f0;
        padding-top: 12px;
      }
    }

    .empty-data {
      text-align: center;
      padding: 40px 0;
    }
  }

  .history-data {
    .trend-controls {
      margin-bottom: 20px;
      text-align: center;
    }
  }
}

// 全局样式覆盖
::v-deep .el-card__header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

::v-deep .el-card__body {
  padding: 20px;
}

::v-deep .el-table {
  .el-table__row {
    &:hover {
      background-color: #f5f7fa;
    }
  }
}

::v-deep .el-descriptions {
  .el-descriptions__label {
    font-weight: 500;
  }
}
</style>
