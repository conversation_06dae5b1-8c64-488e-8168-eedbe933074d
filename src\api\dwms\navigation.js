import request from '@/utils/request'

// 查询库房地图列表
export function listWarehouseMaps(query) {
  return request({
    url: '/dwms/navigation/map/list',
    method: 'get',
    params: query
  })
}

// 查询库房地图详细
export function getWarehouseMap(id) {
  return request({
    url: '/dwms/navigation/map/' + id,
    method: 'get'
  })
}

// 新增库房地图
export function addWarehouseMap(data) {
  return request({
    url: '/dwms/navigation/map',
    method: 'post',
    data: data
  })
}

// 修改库房地图
export function updateWarehouseMap(data) {
  return request({
    url: '/dwms/navigation/map',
    method: 'put',
    data: data
  })
}

// 删除库房地图
export function delWarehouseMap(id) {
  return request({
    url: '/dwms/navigation/map/' + id,
    method: 'delete'
  })
}

// 根据库房ID获取地图
export function getWarehouseMapByWarehouseId(warehouseId) {
  return request({
    url: '/dwms/navigation/map/warehouse/' + warehouseId,
    method: 'get'
  })
}

// 路径规划
export function planPath(data) {
  return request({
    url: '/dwms/navigation/path/plan',
    method: 'post',
    data: data
  })
}

// 获取最短路径
export function getShortestPath(warehouseId, startPoint, endPoint) {
  return request({
    url: '/dwms/navigation/path/shortest',
    method: 'get',
    params: {
      warehouseId,
      startPoint,
      endPoint
    }
  })
}

// 获取推荐路径
export function getRecommendPath(warehouseId, startPoint, endPoint, avoidCongestion) {
  return request({
    url: '/dwms/navigation/path/recommend',
    method: 'get',
    params: {
      warehouseId,
      startPoint,
      endPoint,
      avoidCongestion
    }
  })
}

// 获取库房区域列表
export function getWarehouseAreas(warehouseId) {
  return request({
    url: '/dwms/navigation/areas/' + warehouseId,
    method: 'get'
  })
}

// 获取库房设备位置
export function getDeviceLocations(warehouseId) {
  return request({
    url: '/dwms/navigation/devices/' + warehouseId,
    method: 'get'
  })
}

// 搜索位置
export function searchLocation(warehouseId, keyword) {
  return request({
    url: '/dwms/navigation/search',
    method: 'get',
    params: {
      warehouseId,
      keyword
    }
  })
}

// 获取当前位置
export function getCurrentLocation(deviceId, personId) {
  return request({
    url: '/dwms/navigation/location/current',
    method: 'get',
    params: {
      deviceId,
      personId
    }
  })
}

// 更新位置信息
export function updateLocation(data) {
  return request({
    url: '/dwms/navigation/location/update',
    method: 'post',
    data: data
  })
}

// 获取导航指引
export function getNavigationGuidance(data) {
  return request({
    url: '/dwms/navigation/guidance',
    method: 'post',
    data: data
  })
}

// 获取实时拥堵信息
export function getCongestionInfo(warehouseId) {
  return request({
    url: '/dwms/navigation/congestion/' + warehouseId,
    method: 'get'
  })
}

// 保存导航历史
export function saveNavigationHistory(data) {
  return request({
    url: '/dwms/navigation/history',
    method: 'post',
    data: data
  })
}

// 获取导航历史
export function getNavigationHistory(personId, deviceId, limit) {
  return request({
    url: '/dwms/navigation/history',
    method: 'get',
    params: {
      personId,
      deviceId,
      limit
    }
  })
}

// 获取热门目的地
export function getPopularDestinations(warehouseId) {
  return request({
    url: '/dwms/navigation/destinations/popular/' + warehouseId,
    method: 'get'
  })
}

// 添加地图标注
export function addMapMarker(data) {
  return request({
    url: '/dwms/navigation/marker',
    method: 'post',
    data: data
  })
}

// 获取地图标注
export function getMapMarkers(warehouseId) {
  return request({
    url: '/dwms/navigation/marker/' + warehouseId,
    method: 'get'
  })
}

// 删除地图标注
export function removeMapMarker(markerId) {
  return request({
    url: '/dwms/navigation/marker/' + markerId,
    method: 'delete'
  })
}

// 导出地图配置
export function exportMapConfig(query) {
  return request({
    url: '/dwms/navigation/export',
    method: 'get',
    params: query
  })
}
