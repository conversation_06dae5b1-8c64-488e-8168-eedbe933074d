<svg viewBox="0 0 200 80" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景光晕效果 -->
  <defs>
    <radialGradient id="glow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#60a5fa;stop-opacity:0.3"/>
      <stop offset="100%" style="stop-color:#60a5fa;stop-opacity:0"/>
    </radialGradient>
    <linearGradient id="chemicalGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981"/>
      <stop offset="50%" style="stop-color:#3b82f6"/>
      <stop offset="100%" style="stop-color:#8b5cf6"/>
    </linearGradient>
    <linearGradient id="textGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ffffff"/>
      <stop offset="50%" style="stop-color:#60a5fa"/>
      <stop offset="100%" style="stop-color:#10b981"/>
    </linearGradient>
  </defs>
  
  <!-- 背景光晕 -->
  <circle cx="40" cy="40" r="35" fill="url(#glow)"/>
  
  <!-- 外圆环 - 代表全球化视野 -->
  <circle cx="40" cy="40" r="32" fill="none" stroke="currentColor" stroke-width="2" opacity="0.4"/>
  <circle cx="40" cy="40" r="28" fill="none" stroke="currentColor" stroke-width="1" opacity="0.2"/>
  
  <!-- 内部六边形 - 代表化工分子结构 -->
  <polygon points="40,20 52,26 52,38 40,44 28,38 28,26" fill="url(#chemicalGrad)" opacity="0.2" stroke="currentColor" stroke-width="1.5"/>
  
  <!-- 中心原子核 -->
  <circle cx="40" cy="40" r="5" fill="url(#chemicalGrad)"/>
  <circle cx="40" cy="40" r="3" fill="currentColor"/>
  
  <!-- 电子轨道线 -->
  <ellipse cx="40" cy="40" rx="20" ry="10" fill="none" stroke="currentColor" stroke-width="1.5" opacity="0.6" transform="rotate(30 40 40)"/>
  <ellipse cx="40" cy="40" rx="20" ry="10" fill="none" stroke="currentColor" stroke-width="1.5" opacity="0.6" transform="rotate(-30 40 40)"/>
  <ellipse cx="40" cy="40" rx="16" ry="8" fill="none" stroke="currentColor" stroke-width="1" opacity="0.4" transform="rotate(60 40 40)"/>
  
  <!-- 电子点 -->
  <circle cx="60" cy="40" r="2.5" fill="#10b981"/>
  <circle cx="20" cy="40" r="2.5" fill="#f59e0b"/>
  <circle cx="50" cy="20" r="2.5" fill="#3b82f6"/>
  <circle cx="30" cy="60" r="2.5" fill="#8b5cf6"/>
  <circle cx="50" cy="60" r="2" fill="#ef4444"/>
  <circle cx="30" cy="20" r="2" fill="#06b6d4"/>
  
  <!-- 数据流线条 -->
  <path d="M75 25 Q85 20 95 25 Q105 30 115 25 Q125 20 135 25" fill="none" stroke="currentColor" stroke-width="2" opacity="0.5"/>
  <path d="M75 40 Q85 35 95 40 Q105 45 115 40 Q125 35 135 40" fill="none" stroke="currentColor" stroke-width="2.5" opacity="0.7"/>
  <path d="M75 55 Q85 50 95 55 Q105 60 115 55 Q125 50 135 55" fill="none" stroke="currentColor" stroke-width="2" opacity="0.5"/>
  
  <!-- 智能连接点 -->
  <circle cx="95" cy="25" r="1.5" fill="currentColor" opacity="0.6"/>
  <circle cx="115" cy="40" r="2" fill="currentColor" opacity="0.8"/>
  <circle cx="95" cy="55" r="1.5" fill="currentColor" opacity="0.6"/>
  <circle cx="85" cy="40" r="1" fill="currentColor" opacity="0.5"/>
  <circle cx="125" cy="40" r="1" fill="currentColor" opacity="0.5"/>
  
  <!-- 文字部分 -->
  <text x="145" y="35" font-family="'Microsoft YaHei', sans-serif" font-size="24" font-weight="800" fill="url(#textGrad)">
    睿云
  </text>
  <text x="145" y="55" font-family="Arial, sans-serif" font-size="12" font-weight="500" fill="currentColor" opacity="0.8">
    RUIYUN
  </text>
  
  <!-- 装饰性元素 -->
  <path d="M140 20 L145 15 L150 20" fill="none" stroke="currentColor" stroke-width="1" opacity="0.4"/>
  <path d="M140 65 L145 70 L150 65" fill="none" stroke="currentColor" stroke-width="1" opacity="0.4"/>
  
  <!-- 微粒效果 -->
  <circle cx="15" cy="15" r="1" fill="currentColor" opacity="0.3"/>
  <circle cx="65" cy="15" r="1.5" fill="currentColor" opacity="0.4"/>
  <circle cx="15" cy="65" r="1" fill="currentColor" opacity="0.3"/>
  <circle cx="65" cy="65" r="1.5" fill="currentColor" opacity="0.4"/>
  <circle cx="180" cy="20" r="1" fill="currentColor" opacity="0.3"/>
  <circle cx="180" cy="60" r="1" fill="currentColor" opacity="0.3"/>
</svg>
