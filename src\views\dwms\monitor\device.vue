<template>
  <div class="device-monitor">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon">
            <i class="el-icon-cpu"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.totalCount || 0 }}</div>
            <div class="stat-label">设备总数</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon success">
            <i class="el-icon-success"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.onlineCount || 0 }}</div>
            <div class="stat-label">在线设备</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon danger">
            <i class="el-icon-error"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.offlineCount || 0 }}</div>
            <div class="stat-label">离线设备</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon warning">
            <i class="el-icon-warning"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.alertCount || 0 }}</div>
            <div class="stat-label">告警设备</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 设备状态概览 -->
    <el-card class="device-overview" shadow="never">
      <div slot="header" class="card-header">
        <span>设备状态概览</span>
        <div class="header-actions">
          <el-button 
            type="text" 
            icon="el-icon-refresh" 
            @click="refreshDeviceData"
            :loading="overviewLoading"
          >
            刷新
          </el-button>
          <el-switch
            v-model="autoRefresh"
            active-text="自动刷新"
            @change="toggleAutoRefresh"
          ></el-switch>
        </div>
      </div>
      
      <!-- 设备类型筛选 -->
      <div class="device-filter">
        <el-radio-group v-model="selectedDeviceType" @change="filterDevicesByType">
          <el-radio-button label="">全部</el-radio-button>
          <el-radio-button label="weight">重量传感器</el-radio-button>
          <el-radio-button label="access">门禁设备</el-radio-button>
          <el-radio-button label="camera">监控摄像头</el-radio-button>
          <el-radio-button label="beacon">蓝牙信标</el-radio-button>
        </el-radio-group>
      </div>
      
      <!-- 设备卡片列表 -->
      <el-row :gutter="20">
        <el-col 
          v-for="device in filteredDevices" 
          :key="device.deviceId" 
          :span="8"
          style="margin-bottom: 20px;"
        >
          <div class="device-card" :class="getDeviceCardClass(device)">
            <div class="device-header">
              <div class="device-info">
                <div class="device-name">{{ device.deviceName }}</div>
                <div class="device-id">{{ device.deviceId }}</div>
              </div>
              <div class="device-status">
                <el-tag 
                  :type="getStatusTagType(device.status)" 
                  size="mini"
                >
                  {{ getStatusText(device.status) }}
                </el-tag>
                <el-tag 
                  v-if="device.alertLevel > 0"
                  :type="getAlertTagType(device.alertLevel)" 
                  size="mini"
                  style="margin-left: 8px;"
                >
                  {{ getAlertText(device.alertLevel) }}
                </el-tag>
              </div>
            </div>
            
            <div class="device-content">
              <div class="device-details">
                <div class="detail-item">
                  <i class="el-icon-location"></i>
                  <span>{{ device.location || '未知位置' }}</span>
                </div>
                <div class="detail-item">
                  <i class="el-icon-monitor"></i>
                  <span>{{ getDeviceTypeName(device.deviceType) }}</span>
                </div>
                <div class="detail-item" v-if="device.deviceIp">
                  <i class="el-icon-connection"></i>
                  <span>{{ device.deviceIp }}</span>
                </div>
                <div class="detail-item" v-if="device.lastHeartbeat">
                  <i class="el-icon-time"></i>
                  <span>{{ formatTime(device.lastHeartbeat) }}</span>
                </div>
              </div>
            </div>
            
            <div class="device-actions">
              <el-button 
                type="text" 
                size="mini" 
                @click="showDeviceDetail(device)"
              >
                详情
              </el-button>
              <el-button 
                type="text" 
                size="mini" 
                @click="showDeviceConfig(device)"
                :disabled="device.status !== 1"
              >
                配置
              </el-button>
              <el-button 
                type="text" 
                size="mini" 
                @click="restartDevice(device.deviceId)"
                :disabled="device.status !== 1"
              >
                重启
              </el-button>
            </div>
          </div>
        </el-col>
      </el-row>
      
      <div v-if="filteredDevices.length === 0" class="empty-data">
        <el-empty description="暂无设备数据"></el-empty>
      </div>
    </el-card>

    <!-- 设备列表 -->
    <el-card class="device-list" shadow="never">
      <div slot="header" class="card-header">
        <span>设备列表</span>
        <div class="header-actions">
          <el-button type="primary" icon="el-icon-search" @click="queryDeviceData">查询</el-button>
          <el-button type="success" icon="el-icon-download" @click="exportData">导出</el-button>
        </div>
      </div>
      
      <!-- 查询条件 -->
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
        <el-form-item label="设备ID" prop="deviceId">
          <el-input
            v-model="queryParams.deviceId"
            placeholder="请输入设备ID"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="设备名称" prop="deviceName">
          <el-input
            v-model="queryParams.deviceName"
            placeholder="请输入设备名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="设备类型" prop="deviceType">
          <el-select v-model="queryParams.deviceType" placeholder="请选择设备类型" clearable>
            <el-option label="重量传感器" value="weight"></el-option>
            <el-option label="门禁设备" value="access"></el-option>
            <el-option label="监控摄像头" value="camera"></el-option>
            <el-option label="蓝牙信标" value="beacon"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择设备状态" clearable>
            <el-option label="在线" value="1"></el-option>
            <el-option label="离线" value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="位置" prop="location">
          <el-input
            v-model="queryParams.location"
            placeholder="请输入位置"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="deviceList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="设备ID" align="center" prop="deviceId" />
        <el-table-column label="设备名称" align="center" prop="deviceName" />
        <el-table-column label="设备类型" align="center" prop="deviceType">
          <template slot-scope="scope">
            <span>{{ getDeviceTypeName(scope.row.deviceType) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="告警级别" align="center" prop="alertLevel">
          <template slot-scope="scope">
            <el-tag 
              v-if="scope.row.alertLevel > 0"
              :type="getAlertTagType(scope.row.alertLevel)"
            >
              {{ getAlertText(scope.row.alertLevel) }}
            </el-tag>
            <span v-else>正常</span>
          </template>
        </el-table-column>
        <el-table-column label="位置" align="center" prop="location" />
        <el-table-column label="IP地址" align="center" prop="deviceIp" />
        <el-table-column label="厂商" align="center" prop="manufacturer" />
        <el-table-column label="最后心跳" align="center" prop="lastHeartbeat" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.lastHeartbeat, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
            >查看</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-refresh"
              @click="restartDevice(scope.row.deviceId)"
              :disabled="scope.row.status !== 1"
            >重启</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 设备详情对话框 -->
    <el-dialog title="设备详情" :visible.sync="detailDialogVisible" width="800px">
      <el-descriptions :column="2" border v-if="currentDevice">
        <el-descriptions-item label="设备ID">{{ currentDevice.deviceId }}</el-descriptions-item>
        <el-descriptions-item label="设备名称">{{ currentDevice.deviceName }}</el-descriptions-item>
        <el-descriptions-item label="设备类型">{{ getDeviceTypeName(currentDevice.deviceType) }}</el-descriptions-item>
        <el-descriptions-item label="设备状态">
          <el-tag :type="getStatusTagType(currentDevice.status)">
            {{ getStatusText(currentDevice.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="告警级别">
          <el-tag 
            v-if="currentDevice.alertLevel > 0"
            :type="getAlertTagType(currentDevice.alertLevel)"
          >
            {{ getAlertText(currentDevice.alertLevel) }}
          </el-tag>
          <span v-else>正常</span>
        </el-descriptions-item>
        <el-descriptions-item label="位置">{{ currentDevice.location || '-' }}</el-descriptions-item>
        <el-descriptions-item label="IP地址">{{ currentDevice.deviceIp || '-' }}</el-descriptions-item>
        <el-descriptions-item label="MAC地址">{{ currentDevice.deviceMac || '-' }}</el-descriptions-item>
        <el-descriptions-item label="设备版本">{{ currentDevice.deviceVersion || '-' }}</el-descriptions-item>
        <el-descriptions-item label="厂商">{{ currentDevice.manufacturer || '-' }}</el-descriptions-item>
        <el-descriptions-item label="型号">{{ currentDevice.deviceModel || '-' }}</el-descriptions-item>
        <el-descriptions-item label="安装时间">
          {{ parseTime(currentDevice.installTime, '{y}-{m}-{d}') || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="维护人员">{{ currentDevice.maintainer || '-' }}</el-descriptions-item>
        <el-descriptions-item label="最后心跳">
          {{ parseTime(currentDevice.lastHeartbeat, '{y}-{m}-{d} {h}:{i}:{s}') || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="描述" span="2">{{ currentDevice.description || '-' }}</el-descriptions-item>
      </el-descriptions>
      
      <!-- 设备操作 -->
      <div class="device-operations" style="margin-top: 20px;">
        <el-button 
          type="primary" 
          @click="showDeviceConfig(currentDevice)"
          :disabled="currentDevice.status !== 1"
        >
          设备配置
        </el-button>
        <el-button 
          type="warning" 
          @click="restartDevice(currentDevice.deviceId)"
          :disabled="currentDevice.status !== 1"
        >
          重启设备
        </el-button>
        <el-button 
          type="info" 
          @click="showDeviceLogs(currentDevice.deviceId)"
        >
          查看日志
        </el-button>
        <el-button 
          type="success" 
          @click="diagnoseDevice(currentDevice.deviceId)"
        >
          故障诊断
        </el-button>
      </div>
    </el-dialog>

    <!-- 设备配置对话框 -->
    <el-dialog title="设备配置" :visible.sync="configDialogVisible" width="600px">
      <el-form ref="configForm" :model="deviceConfig" label-width="120px">
        <el-form-item label="心跳间隔(秒)">
          <el-input-number v-model="deviceConfig.heartbeatInterval" :min="10" :max="300"></el-input-number>
        </el-form-item>
        <el-form-item label="数据上报间隔(秒)">
          <el-input-number v-model="deviceConfig.dataReportInterval" :min="30" :max="3600"></el-input-number>
        </el-form-item>
        <el-form-item label="告警阈值(%)">
          <el-input-number v-model="deviceConfig.alertThreshold" :min="0" :max="100"></el-input-number>
        </el-form-item>
        <el-form-item label="自动重启">
          <el-switch v-model="deviceConfig.autoRestart"></el-switch>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="configDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="updateDeviceConfig">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 设备日志对话框 -->
    <el-dialog title="设备日志" :visible.sync="logsDialogVisible" width="80%">
      <div class="logs-container">
        <div class="logs-header">
          <el-select v-model="logLevel" placeholder="日志级别" style="width: 120px;">
            <el-option label="INFO" value="INFO"></el-option>
            <el-option label="WARN" value="WARN"></el-option>
            <el-option label="ERROR" value="ERROR"></el-option>
          </el-select>
          <el-input-number v-model="logLines" :min="50" :max="1000" style="margin-left: 10px; width: 120px;"></el-input-number>
          <el-button type="primary" @click="loadDeviceLogs" style="margin-left: 10px;">刷新</el-button>
        </div>
        <div class="logs-content">
          <pre v-for="(log, index) in deviceLogs" :key="index" class="log-line">{{ log }}</pre>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  listDeviceStatus, 
  getDeviceStats, 
  restartDevice,
  getDeviceConfig,
  updateDeviceConfig,
  getDeviceLogs,
  diagnoseDevice
} from '@/api/dwms/device'

export default {
  name: 'DeviceMonitor',
  data() {
    return {
      // 加载状态
      loading: true,
      overviewLoading: false,
      
      // 列表数据
      deviceList: [],
      total: 0,
      
      // 概览数据
      overviewDevices: [],
      filteredDevices: [],
      selectedDeviceType: '',
      autoRefresh: false,
      refreshTimer: null,
      
      // 统计数据
      stats: {},
      
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceId: null,
        deviceName: null,
        deviceType: null,
        status: null,
        location: null
      },
      
      // 显示搜索条件
      showSearch: true,
      
      // 选中数组
      ids: [],
      single: true,
      multiple: true,
      
      // 对话框
      detailDialogVisible: false,
      configDialogVisible: false,
      logsDialogVisible: false,
      currentDevice: null,
      
      // 设备配置
      deviceConfig: {
        heartbeatInterval: 30,
        dataReportInterval: 60,
        alertThreshold: 80,
        autoRestart: true
      },
      
      // 设备日志
      deviceLogs: [],
      logLevel: 'INFO',
      logLines: 100
    }
  },
  created() {
    this.getList()
    this.getStats()
    this.getOverviewDevices()
  },
  beforeDestroy() {
    this.clearRefreshTimer()
  },
  methods: {
    /** 查询设备状态列表 */
    getList() {
      this.loading = true
      listDeviceStatus(this.queryParams).then(response => {
        this.deviceList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    
    /** 查询统计数据 */
    getStats() {
      getDeviceStats().then(response => {
        this.stats = response.data || {}
      })
    },
    
    /** 获取概览设备数据 */
    getOverviewDevices() {
      this.overviewLoading = true
      listDeviceStatus({ pageNum: 1, pageSize: 100 }).then(response => {
        this.overviewDevices = response.rows || []
        this.filterDevicesByType()
        this.overviewLoading = false
      }).catch(() => {
        this.overviewLoading = false
      })
    },
    
    /** 根据类型筛选设备 */
    filterDevicesByType() {
      if (this.selectedDeviceType === '') {
        this.filteredDevices = this.overviewDevices
      } else {
        this.filteredDevices = this.overviewDevices.filter(device => 
          device.deviceType === this.selectedDeviceType
        )
      }
    },
    
    /** 刷新设备数据 */
    refreshDeviceData() {
      this.getStats()
      this.getOverviewDevices()
    },
    
    /** 切换自动刷新 */
    toggleAutoRefresh(enabled) {
      if (enabled) {
        this.refreshTimer = setInterval(() => {
          this.refreshDeviceData()
        }, 30000) // 30秒刷新一次
      } else {
        this.clearRefreshTimer()
      }
    },
    
    /** 清除刷新定时器 */
    clearRefreshTimer() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },
    
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    
    /** 查看操作 */
    handleView(row) {
      this.currentDevice = row
      this.detailDialogVisible = true
    },
    
    /** 修改操作 */
    handleUpdate(row) {
      // TODO: 实现设备信息修改
      this.$message.info('设备信息修改功能开发中...')
    },
    
    /** 显示设备详情 */
    showDeviceDetail(device) {
      this.currentDevice = device
      this.detailDialogVisible = true
    },
    
    /** 显示设备配置 */
    showDeviceConfig(device) {
      this.currentDevice = device
      getDeviceConfig(device.deviceId).then(response => {
        this.deviceConfig = response.data || {}
        this.configDialogVisible = true
      })
    },
    
    /** 更新设备配置 */
    updateDeviceConfig() {
      updateDeviceConfig(this.currentDevice.deviceId, this.deviceConfig).then(() => {
        this.$message.success('配置更新成功')
        this.configDialogVisible = false
      })
    },
    
    /** 重启设备 */
    restartDevice(deviceId) {
      this.$confirm(`确定要重启设备 ${deviceId} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        restartDevice(deviceId).then(() => {
          this.$message.success('重启指令已发送')
        })
      })
    },
    
    /** 显示设备日志 */
    showDeviceLogs(deviceId) {
      this.currentDevice = { deviceId }
      this.logsDialogVisible = true
      this.loadDeviceLogs()
    },
    
    /** 加载设备日志 */
    loadDeviceLogs() {
      getDeviceLogs(this.currentDevice.deviceId, this.logLevel, this.logLines).then(response => {
        this.deviceLogs = response.data || []
      })
    },
    
    /** 设备故障诊断 */
    diagnoseDevice(deviceId) {
      diagnoseDevice(deviceId).then(response => {
        const diagnosis = response.data
        let message = `设备 ${deviceId} 诊断结果:\n`
        message += `状态: ${diagnosis.status}\n`
        if (diagnosis.issues && diagnosis.issues.length > 0) {
          message += `问题: ${diagnosis.issues.join(', ')}\n`
        }
        message += `诊断时间: ${this.parseTime(diagnosis.diagnosisTime, '{y}-{m}-{d} {h}:{i}:{s}')}`
        
        this.$alert(message, '诊断结果', {
          confirmButtonText: '确定',
          type: diagnosis.status === 'healthy' ? 'success' : 'warning'
        })
      })
    },
    
    /** 查询设备数据 */
    queryDeviceData() {
      this.handleQuery()
    },
    
    /** 导出数据 */
    exportData() {
      this.download('dwms/device/export', {
        ...this.queryParams
      }, `device_status_${new Date().getTime()}.xlsx`)
    },
    
    /** 获取设备卡片样式类 */
    getDeviceCardClass(device) {
      if (device.alertLevel > 0) {
        return 'alert'
      } else if (device.status === 1) {
        return 'online'
      } else {
        return 'offline'
      }
    },
    
    /** 获取状态标签类型 */
    getStatusTagType(status) {
      return status === 1 ? 'success' : 'danger'
    },
    
    /** 获取状态文本 */
    getStatusText(status) {
      return status === 1 ? '在线' : '离线'
    },
    
    /** 获取告警标签类型 */
    getAlertTagType(alertLevel) {
      const types = { 1: 'warning', 2: 'danger', 3: 'danger' }
      return types[alertLevel] || 'info'
    },
    
    /** 获取告警文本 */
    getAlertText(alertLevel) {
      const texts = { 1: '警告', 2: '严重', 3: '紧急' }
      return texts[alertLevel] || '正常'
    },
    
    /** 获取设备类型名称 */
    getDeviceTypeName(deviceType) {
      const typeMap = {
        weight: '重量传感器',
        access: '门禁设备',
        camera: '监控摄像头',
        beacon: '蓝牙信标'
      }
      return typeMap[deviceType] || deviceType
    },
    
    /** 格式化时间 */
    formatTime(time) {
      if (!time) return '-'
      return this.parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')
    }
  }
}
</script>

<style lang="scss" scoped>
.device-monitor {
  padding: 20px;

  .stats-cards {
    margin-bottom: 20px;

    .stat-card {
      display: flex;
      align-items: center;
      padding: 20px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);

      .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        background: #409EFF;
        color: #fff;
        font-size: 24px;

        &.success {
          background: #67C23A;
        }

        &.danger {
          background: #F56C6C;
        }

        &.warning {
          background: #E6A23C;
        }
      }

      .stat-content {
        flex: 1;

        .stat-value {
          font-size: 28px;
          font-weight: bold;
          color: #333;
          line-height: 1;
          margin-bottom: 8px;
        }

        .stat-label {
          font-size: 14px;
          color: #666;
        }
      }
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-actions {
      display: flex;
      align-items: center;
      gap: 16px;
    }
  }

  .device-overview {
    margin-bottom: 20px;

    .device-filter {
      margin-bottom: 20px;
      text-align: center;
    }

    .device-card {
      background: #fff;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      padding: 16px;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        transform: translateY(-2px);
      }

      &.online {
        border-color: #67C23A;
        background: #f0f9ff;
      }

      &.offline {
        border-color: #F56C6C;
        background: #fef0f0;
      }

      &.alert {
        border-color: #E6A23C;
        background: #fdf6ec;
        animation: pulse 2s infinite;
      }

      .device-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;

        .device-info {
          .device-name {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
          }

          .device-id {
            font-size: 12px;
            color: #999;
          }
        }

        .device-status {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          gap: 4px;
        }
      }

      .device-content {
        .device-details {
          .detail-item {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
            font-size: 12px;
            color: #666;

            i {
              margin-right: 4px;
              width: 14px;
            }
          }
        }
      }

      .device-actions {
        margin-top: 12px;
        text-align: center;
        border-top: 1px solid #f0f0f0;
        padding-top: 12px;
      }
    }

    .empty-data {
      text-align: center;
      padding: 40px 0;
    }
  }

  .device-list {
    .empty-data {
      text-align: center;
      padding: 40px 0;
    }
  }
}

.device-operations {
  text-align: center;

  .el-button {
    margin: 0 8px;
  }
}

.logs-container {
  .logs-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e4e7ed;
  }

  .logs-content {
    max-height: 400px;
    overflow-y: auto;
    background: #f5f5f5;
    padding: 12px;
    border-radius: 4px;

    .log-line {
      margin: 0;
      padding: 2px 0;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      line-height: 1.4;
      color: #333;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(230, 162, 60, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(230, 162, 60, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(230, 162, 60, 0);
  }
}

// 全局样式覆盖
::v-deep .el-card__header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

::v-deep .el-card__body {
  padding: 20px;
}

::v-deep .el-table {
  .el-table__row {
    &:hover {
      background-color: #f5f7fa;
    }
  }
}

::v-deep .el-descriptions {
  .el-descriptions__label {
    font-weight: 500;
  }
}

::v-deep .el-radio-group {
  .el-radio-button__inner {
    padding: 8px 16px;
  }
}
</style>
