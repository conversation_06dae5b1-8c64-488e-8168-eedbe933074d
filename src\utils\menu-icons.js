/**
 * 企业化工科技风格菜单图标配置
 * 为智能仓储管理系统设计的专业图标系统
 */

// 主菜单图标配置 - 企业化工科技蓝风格
export const mainMenuIcons = {
  // 系统管理
  'system': 'system-setting',
  '系统管理': 'system-setting',
  
  // 设备管理 / IoT管理
  'iot': 'device-management',
  '设备管理': 'device-management',
  'device': 'device-management',
  
  // 物料管理
  'material': 'material-management',
  '物料管理': 'material-management',
  
  // 仓库管理
  'warehouse': 'warehouse-management',
  '仓库管理': 'warehouse-management',
  
  // 统一注册管理
  'unified': 'unified-registry',
  '统一注册管理': 'unified-registry',
  
  // MQTT管理
  'mqtt': 'mqtt-protocol',
  'MQTT管理': 'mqtt-protocol',
  
  // EMQX管理
  'emqx': 'emqx-server',
  'EMQX管理': 'emqx-server',
  
  // SIP Server管理
  'sip': 'sip-protocol',
  'SIP Server': 'sip-protocol',
  
  // NETTY管理
  'netty': 'netty-server',
  'NETTY管理': 'netty-server',
  
  // 门禁管理
  'access': 'access-control',
  '门禁管理': 'access-control',
  
  // 监控管理
  'monitor': 'system-monitor',
  '系统监控': 'system-monitor',
  
  // 工具管理
  'tool': 'system-tools',
  '系统工具': 'system-tools',
  
  // 审批管理
  'approval': 'approval-workflow',
  '审批管理': 'approval-workflow',
  
  // 大屏展示
  'bigScreen': 'data-visualization',
  '大屏展示': 'data-visualization'
}

// 子菜单图标配置 - 按功能分类
export const subMenuIcons = {
  // 系统管理子菜单
  'user': 'user-management',
  '用户管理': 'user-management',
  'role': 'role-management', 
  '角色管理': 'role-management',
  'menu': 'menu-management',
  '菜单管理': 'menu-management',
  'dept': 'department-management',
  '部门管理': 'department-management',
  'post': 'position-management',
  '岗位管理': 'position-management',
  'dict': 'dictionary-management',
  '字典管理': 'dictionary-management',
  'config': 'system-config',
  '参数设置': 'system-config',
  'notice': 'notice-management',
  '通知公告': 'notice-management',
  
  // 设备管理子菜单
  'category': 'device-category',
  '产品分类': 'device-category',
  'product': 'product-management',
  '产品管理': 'product-management',
  'device': 'device-list',
  '设备管理': 'device-list',
  'group': 'device-group',
  '设备分组': 'device-group',
  'firmware': 'firmware-management',
  '固件管理': 'firmware-management',
  'protocol': 'protocol-management',
  '协议管理': 'protocol-management',
  'scene': 'scene-management',
  '场景联动': 'scene-management',
  
  // 物料管理子菜单
  'material-info': 'material-info',
  '物料信息': 'material-info',
  'material-list': 'material-list',
  '物料清单': 'material-list',
  'stock': 'inventory-management',
  '物料库存': 'inventory-management',
  'query': 'material-query',
  '物料查询': 'material-query',
  'weight': 'weight-management',
  '重量管理': 'weight-management',
  
  // 仓库管理子菜单
  'warehouse-info': 'warehouse-info',
  '仓库信息': 'warehouse-info',
  'zone': 'warehouse-zone',
  '库区管理': 'warehouse-zone',
  'rack': 'warehouse-rack',
  '货架管理': 'warehouse-rack',
  'location': 'warehouse-location',
  '货位管理': 'warehouse-location',
  
  // 统一注册管理子菜单
  'registry': 'registry-dashboard',
  '注册首页': 'registry-dashboard',
  'dashboard': 'statistics-dashboard',
  '统计仪表板': 'statistics-dashboard',
  'server': 'server-management',
  '服务端管理': 'server-management',
  'client': 'client-management',
  '客户端管理': 'client-management',
  'auth': 'auth-management',
  '认证管理': 'auth-management',
  'data': 'data-management',
  '数据管理': 'data-management',
  'cleanup': 'data-cleanup',
  '数据清理': 'data-cleanup',
  'logs': 'log-management',
  '日志管理': 'log-management',
  
  // 监控管理子菜单
  'online': 'online-users',
  '在线用户': 'online-users',
  'job': 'scheduled-tasks',
  '定时任务': 'scheduled-tasks',
  'druid': 'database-monitor',
  '数据监控': 'database-monitor',
  'server-info': 'server-info',
  '服务监控': 'server-info',
  'cache': 'cache-monitor',
  '缓存监控': 'cache-monitor',
  'operlog': 'operation-log',
  '操作日志': 'operation-log',
  'logininfor': 'login-log',
  '登录日志': 'login-log',
  
  // 工具管理子菜单
  'build': 'form-builder',
  '表单构建': 'form-builder',
  'gen': 'code-generator',
  '代码生成': 'code-generator',
  'swagger': 'api-docs',
  '系统接口': 'api-docs',
  
  // 审批管理子菜单
  'instances': 'approval-instances',
  '审批实例': 'approval-instances',
  'management': 'approval-management',
  '审批管理': 'approval-management',
  
  // 通用功能图标
  'add': 'add-item',
  '新增': 'add-item',
  'edit': 'edit-item',
  '修改': 'edit-item',
  'delete': 'delete-item',
  '删除': 'delete-item',
  'export': 'export-data',
  '导出': 'export-data',
  'import': 'import-data',
  '导入': 'import-data',
  'search': 'search-data',
  '查询': 'search-data',
  'refresh': 'refresh-data',
  '刷新': 'refresh-data',
  'view': 'view-details',
  '查看': 'view-details',
  'setting': 'settings',
  '设置': 'settings'
}

// SVG图标定义 - 企业化工科技风格
export const svgIcons = {
  // 系统管理类图标
  'system-setting': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5a3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97c0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1c0 .***********.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.**********.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.***********.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z"/>
    </svg>
  `,
  
  'user-management': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2s-.89 2-2 2s-2-.89-2-2m4 18v-6h2.5l-2.54-7.63A1.5 1.5 0 0 0 18.54 8H17.5c-.8 0-1.54.37-2 1l-3.72 5.63L11 14l2.5-3.22C14.34 9.78 15.89 9 17.5 9h1.04c.83 0 1.56.5 1.88 1.22L23 18h-3v4h-2M12.5 11.5c.83 0 1.5-.67 1.5-1.5s-.67-1.5-1.5-1.5S11 9.17 11 10s.67 1.5 1.5 1.5M5.5 6c1.11 0 2-.89 2-2s-.89-2-2-2s-2 .89-2 2s.89 2 2 2M7.5 22v-7H9L7.1 9.28c-.18-.67-.73-1.15-1.42-1.26C5.49 8 5.32 8 5.15 8H4.5c-.4 0-.78.12-1.08.34L2 9.5L3 11l1.08-.66C4.24 10.24 4.37 10.24 4.5 10.3L6 15H4v7h3.5Z"/>
    </svg>
  `,
  
  'role-management': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 9H14V4H19V9ZM12 13C14.67 13 20 14.33 20 17V20H4V17C4 14.33 9.33 13 12 13Z"/>
    </svg>
  `,
  
  'menu-management': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M3 6h18v2H3V6m0 5h18v2H3v-2m0 5h18v2H3v-2Z"/>
    </svg>
  `,
  
  // 设备管理类图标
  'device-management': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M17 1H7C5.9 1 5 1.9 5 3V21C5 22.1 5.9 23 7 23H17C18.1 23 19 22.1 19 21V3C19 1.9 18.1 1 17 1M17 19H7V5H17V19M16 13H13V16H11V13H8L12 9L16 13Z"/>
    </svg>
  `,
  
  'device-category': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M10 4H4C2.89 4 2 4.89 2 6V18C2 19.11 2.89 20 4 20H10C11.11 20 12 19.11 12 18V6C12 4.89 11.11 4 10 4M10 18H4V6H10V18M20 4H14C12.89 4 12 4.89 12 6V12C12 13.11 12.89 14 14 14H20C21.11 14 22 13.11 22 12V6C22 4.89 21.11 4 20 4M20 12H14V6H20V12M20 16H14C12.89 16 12 16.89 12 18V20C12 21.11 12.89 22 14 22H20C21.11 22 22 21.11 22 20V18C22 16.89 21.11 16 20 16M20 20H14V18H20V20Z"/>
    </svg>
  `,
  
  // 物料管理类图标
  'material-management': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2L2 7L12 12L22 7L12 2M2 17L12 22L22 17M2 12L12 17L22 12"/>
    </svg>
  `,
  
  'inventory-management': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M5 6H23V18H5V6M14 9C14 10.11 13.1 11 12 11S10 10.11 10 9S10.9 7 12 7S14 7.89 14 9M12 17C15.87 17 19 15.87 19 14.5V13.26C17.88 14.24 15.05 14.8 12 14.8S6.12 14.24 5 13.26V14.5C5 15.87 8.13 17 12 17M1 10H3V20H19V22H1V10Z"/>
    </svg>
  `,
  
  // 仓库管理类图标
  'warehouse-management': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 3L2 12H5V20H19V12H22L12 3M12 7.7L17 12V18H15V14H9V18H7V12L12 7.7M11 15H13V17H11V15Z"/>
    </svg>
  `,
  
  'warehouse-zone': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3M19 19H5V5H19V19M17 17H7V15H17V17M17 13H7V11H17V13M17 9H7V7H17V9Z"/>
    </svg>
  `,
  
  // 协议管理类图标
  'mqtt-protocol': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M2 17H22V19H2V17M1.15 12.95L4 15.8L6.85 12.95C7.54 12.26 8.76 12.26 9.45 12.95S10.24 14.17 9.55 14.86L5.41 19L1.27 14.86C0.58 14.17 0.58 12.95 1.27 12.26S2.49 11.57 3.18 12.26L1.15 12.95M23 8V10H1V8H23M23 4V6H1V4H23Z"/>
    </svg>
  `,
  
  'emqx-server': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M4 1C2.89 1 2 1.89 2 3V7C2 8.11 2.89 9 4 9H1V11H4C5.11 11 6 10.11 6 9V3C6 1.89 5.11 1 4 1M4 3H4V7H4V3M7 3V5H9V7H7V9H9V11H11V9H13V11H15V9H13V7H15V5H13V3H11V5H9V3H7M18 5V7H16V9H18V11H20V9H22V7H20V5H18M6 13V15H4V17H6V19H8V17H10V19H12V17H10V15H12V13H10V15H8V13H6M14 13V15H16V13H14M18 13V15H20V13H18M14 17V19H16V17H14M18 17V19H20V17H18Z"/>
    </svg>
  `,
  
  // 统一注册管理类图标
  'unified-registry': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2C17.5 2 22 6.5 22 12S17.5 22 12 22S2 17.5 2 12S6.5 2 12 2M12 4C7.58 4 4 7.58 4 12S7.58 20 12 20S20 16.42 20 12S16.42 4 12 4M12 6C15.31 6 18 8.69 18 12S15.31 18 12 18S6 15.31 6 12S8.69 6 12 6M12 8C9.79 8 8 9.79 8 12S9.79 16 12 16S16 14.21 16 12S14.21 8 12 8Z"/>
    </svg>
  `,
  
  'server-management': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M4 1C2.9 1 2 1.9 2 3V7C2 8.11 2.89 9 4 9H20C21.11 9 22 8.11 22 7V3C22 1.89 21.11 1 20 1H4M4 3H20V7H4V3M4 11C2.9 11 2 11.9 2 13V17C2 18.11 2.89 19 4 19H20C21.11 19 22 18.11 22 17V13C22 11.89 21.11 11 20 11H4M4 13H20V17H4V13M6 5H8V5H6V5M6 15H8V15H6V15Z"/>
    </svg>
  `,
  
  'client-management': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M17 1H7C5.9 1 5 1.9 5 3V21C5 22.1 5.9 23 7 23H17C18.1 23 19 22.1 19 21V3C19 1.9 18.1 1 17 1M17 19H7V5H17V19Z"/>
    </svg>
  `,
  
  // 监控管理类图标
  'system-monitor': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M21 16V4H3V16H21M21 2C22.1 2 23 2.9 23 4V16C23 17.1 22.1 18 21 18H14L16 21V22H8V21L10 18H3C1.9 18 1 17.1 1 16V4C1 2.9 1.9 2 3 2H21M5 6H19V14H5V6Z"/>
    </svg>
  `,
  
  'statistics-dashboard': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3M19 19H5V5H19V19M7 10H9V17H7V10M11 7H13V17H11V7M15 13H17V17H15V13Z"/>
    </svg>
  `,
  
  // 门禁管理类图标
  'access-control': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1M12 7C13.4 7 14.8 8.6 14.8 10V11H16V18H8V11H9.2V10C9.2 8.6 10.6 7 12 7M12 8.2C11.2 8.2 10.4 8.7 10.4 10V11H13.6V10C13.6 8.7 12.8 8.2 12 8.2Z"/>
    </svg>
  `,
  
  // 工具类图标
  'system-tools': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M22.7 19L13.6 9.9C14.5 7.6 14 4.9 12.1 3C10.1 1 7.1 0.6 4.7 1.7L9 6L6 9L1.6 4.7C0.4 7.1 0.9 10.1 2.9 12.1C4.8 14 7.5 14.5 9.8 13.6L18.9 22.7C19.3 23.1 19.9 23.1 20.3 22.7L22.6 20.4C23.1 20 23.1 19.3 22.7 19Z"/>
    </svg>
  `,
  
  // 审批管理类图标
  'approval-workflow': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M14 2H6C4.89 2 4 2.9 4 4V20C4 21.11 4.89 22 6 22H18C19.11 22 20 21.11 20 20V8L14 2M18 20H6V4H13V9H18V20M9.54 15.65L8.21 14.32L7.5 15.03L9.54 17.07L16.5 10.11L15.79 9.4L9.54 15.65Z"/>
    </svg>
  `,
  
  // 数据可视化类图标
  'data-visualization': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M21 16V4H3V16H21M21 2C22.1 2 23 2.9 23 4V16C23 17.1 22.1 18 21 18H14L16 21V22H8V21L10 18H3C1.9 18 1 17.1 1 16V4C1 2.9 1.9 2 3 2H21M5 6H19V14H5V6M6 7V13H8V10H10V13H12V8H14V13H16V9H18V13H18V7H6Z"/>
    </svg>
  `,

  // 更多子菜单图标
  'department-management': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 5.5A3.5 3.5 0 0 1 15.5 9A3.5 3.5 0 0 1 12 12.5A3.5 3.5 0 0 1 8.5 9A3.5 3.5 0 0 1 12 5.5M5 8C5.56 8 6.08.29 6.54.54C6.84.79 7.15.94 7.5 1.03V3.5C7.5 4.33 8.17 5 9 5S10.5 4.33 10.5 3.5V1.03C10.85.94 11.16.79 11.46.54C11.92.29 12.44 8 13 8C14.11 8 15 8.89 15 10V22H9V10C9 8.89 9.89 8 11 8H5M1 10V12H3V10H1M5 10V12H7V10H5M17 10V12H19V10H17M21 10V12H23V10H21Z"/>
    </svg>
  `,

  'position-management': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M20 6H12L10 4H4C2.9 4 2 4.9 2 6V18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V8C22 6.9 21.1 6 20 6M20 18H4V8H20V18M15 10V12H9V10H15M13 14V16H9V14H13Z"/>
    </svg>
  `,

  'dictionary-management': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M18 22C19.1 22 20 21.11 20 20V4C20 2.9 19.1 2 18 2H6C4.89 2 4 2.9 4 4V20C4 21.11 4.89 22 6 22H18M18 20H6V4H7V9L9.5 7.5L12 9V4H18V20M13 10.5H17V12H13V10.5M13 13.5H17V15H13V13.5M13 16.5H17V18H13V16.5M9 16.5H11V18H9V16.5Z"/>
    </svg>
  `,

  'notice-management': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M21 19V20H3V19L5 17V11C5 7.9 7.03 5.17 10 4.29C10 4.19 10 4.1 10 4C10 2.9 10.9 2 12 2S14 2.9 14 4C14 4.1 14 4.19 14 4.29C16.97 5.17 19 7.9 19 11V17L21 19M14 21C14 22.1 13.1 23 12 23S10 22.1 10 21"/>
    </svg>
  `,

  'product-management': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2L2 7L12 12L22 7L12 2M2 17L12 22L22 17M2 12L12 17L22 12"/>
    </svg>
  `,

  'device-list': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M17 1H7C5.9 1 5 1.9 5 3V21C5 22.1 5.9 23 7 23H17C18.1 23 19 22.1 19 21V3C19 1.9 18.1 1 17 1M17 19H7V5H17V19M16 13H13V16H11V13H8L12 9L16 13Z"/>
    </svg>
  `,

  'device-group': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M16 4C16.88 4 17.67 4.5 18 5.26L19 7H20C21.11 7 22 7.89 22 9V19C22 20.11 21.11 21 20 21H4C2.89 21 2 20.11 2 19V9C2 7.89 2.89 7 4 7H5L6 5.26C6.33 4.5 7.12 4 8 4H16M16 6H8L7 8H17L16 6M4 9V19H20V9H4M6 11H18V17H6V11Z"/>
    </svg>
  `,

  'firmware-management': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M14 2H6C4.89 2 4 2.9 4 4V20C4 21.11 4.89 22 6 22H18C19.11 22 20 21.11 20 20V8L14 2M18 20H6V4H13V9H18V20M9 13V19H7V13H9M12 10V19H10V10H12M15 16V19H13V16H15Z"/>
    </svg>
  `,

  'protocol-management': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M2 17H22V19H2V17M1.15 12.95L4 15.8L6.85 12.95C7.54 12.26 8.76 12.26 9.45 12.95S10.24 14.17 9.55 14.86L5.41 19L1.27 14.86C0.58 14.17 0.58 12.95 1.27 12.26S2.49 11.57 3.18 12.26L1.15 12.95M23 8V10H1V8H23M23 4V6H1V4H23Z"/>
    </svg>
  `,

  'scene-management': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2M21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 9H14V4H19V9Z"/>
    </svg>
  `,

  'material-info': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M13 9H11V7H13M13 17H11V11H13M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22S22 17.52 22 12S17.52 2 12 2M12 20C7.59 20 4 16.41 4 12S7.59 4 12 4S20 7.59 20 12S16.41 20 12 20Z"/>
    </svg>
  `,

  'material-list': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3M19 19H5V5H19V19M17 17H7V15H17V17M17 13H7V11H17V13M17 9H7V7H17V9Z"/>
    </svg>
  `,

  'material-query': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M9.5 3C13.09 3 16 5.91 16 9.5C16 11.11 15.41 12.59 14.44 13.73L14.71 14H15.5L20.5 19L19 20.5L14 15.5V14.71L13.73 14.44C12.59 15.41 11.11 16 9.5 16C5.91 16 3 13.09 3 9.5S5.91 3 9.5 3M9.5 5C7 5 5 7 5 9.5S7 14 9.5 14S14 12 14 9.5S12 5 9.5 5Z"/>
    </svg>
  `,

  'weight-management': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 3C13.11 3 14 3.89 14 5S13.11 7 12 7S10 6.11 10 5S10.89 3 12 3M7 12C7 10.9 7.9 10 9 10S11 10.9 11 12S10.1 14 9 14S7 13.1 7 12M13 12C13 10.9 13.9 10 15 10S17 10.9 17 12S16.1 14 15 14S13 13.1 13 12M12 8C8.69 8 6 10.69 6 14S8.69 20 12 20S18 17.31 18 14S15.31 8 12 8M12 18C9.79 18 8 16.21 8 14S9.79 10 12 10S16 11.79 16 14S14.21 18 12 18Z"/>
    </svg>
  `,

  'warehouse-info': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 3L2 12H5V20H19V12H22L12 3M12 7.7L17 12V18H15V14H9V18H7V12L12 7.7M11 15H13V17H11V15Z"/>
    </svg>
  `,

  'warehouse-rack': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M4 2V22H6V20H18V22H20V2H18V4H6V2H4M6 6H18V8H6V6M6 10H18V12H6V10M6 14H18V16H6V14M6 18H18V18H6V18Z"/>
    </svg>
  `,

  'warehouse-location': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2C15.31 2 18 4.66 18 7.95C18 12.41 12 19 12 19S6 12.41 6 7.95C6 4.66 8.69 2 12 2M12 6C10.9 6 10 6.9 10 8S10.9 10 12 10S14 9.1 14 8S13.1 6 12 6M20 19C20 21.21 16.42 23 12 23S4 21.21 4 19C4 17.71 5.22 16.56 7.11 15.94L7.75 16.74C6.67 17.19 6 17.81 6 18.5C6 19.88 8.69 21 12 21S18 19.88 18 18.5C18 17.81 17.33 17.19 16.25 16.74L16.89 15.94C18.78 16.56 20 17.71 20 19Z"/>
    </svg>
  `,

  'registry-dashboard': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M13 3V9H21V3M13 21H21V11H13M3 21H11V15H3M3 13H11V3H3V13Z"/>
    </svg>
  `,

  'auth-management': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1M12 7C13.4 7 14.8 8.6 14.8 10V11H16V18H8V11H9.2V10C9.2 8.6 10.6 7 12 7M12 8.2C11.2 8.2 10.4 8.7 10.4 10V11H13.6V10C13.6 8.7 12.8 8.2 12 8.2Z"/>
    </svg>
  `,

  'data-management': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 3C7.58 3 4 4.79 4 7S7.58 11 12 11S20 9.21 20 7S16.42 3 12 3M4 9V12C4 14.21 7.58 16 12 16S20 14.21 20 12V9C20 11.21 16.42 13 12 13S4 11.21 4 9M4 14V17C4 19.21 7.58 21 12 21S20 19.21 20 17V14C20 16.21 16.42 18 12 18S4 16.21 4 14Z"/>
    </svg>
  `,

  'data-cleanup': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M19 4H15.5L14.5 3H9.5L8.5 4H5V6H19M6 19C6 20.1 6.9 21 8 21H16C17.1 21 18 20.1 18 19V7H6V19Z"/>
    </svg>
  `,

  'log-management': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M14 2H6C4.89 2 4 2.9 4 4V20C4 21.11 4.89 22 6 22H18C19.11 22 20 21.11 20 20V8L14 2M18 20H6V4H13V9H18V20Z"/>
    </svg>
  `,

  'online-users': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 9H14V4H19V9ZM12 13C14.67 13 20 14.33 20 17V20H4V17C4 14.33 9.33 13 12 13Z"/>
    </svg>
  `,

  'scheduled-tasks': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22S22 17.5 22 12S17.5 2 12 2M12 20C7.59 20 4 16.41 4 12S7.59 4 12 4S20 7.59 20 12S16.41 20 12 20M12.5 7V12.25L17 14.92L16.25 16.15L11 13V7H12.5Z"/>
    </svg>
  `,

  'database-monitor': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 3C7.58 3 4 4.79 4 7S7.58 11 12 11S20 9.21 20 7S16.42 3 12 3M4 9V12C4 14.21 7.58 16 12 16S20 14.21 20 12V9C20 11.21 16.42 13 12 13S4 11.21 4 9M4 14V17C4 19.21 7.58 21 12 21S20 19.21 20 17V14C20 16.21 16.42 18 12 18S4 16.21 4 14Z"/>
    </svg>
  `,

  'server-info': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M4 1C2.9 1 2 1.9 2 3V7C2 8.11 2.89 9 4 9H20C21.11 9 22 8.11 22 7V3C22 1.89 21.11 1 20 1H4M4 3H20V7H4V3M4 11C2.9 11 2 11.9 2 13V17C2 18.11 2.89 19 4 19H20C21.11 19 22 18.11 22 17V13C22 11.89 21.11 11 20 11H4M4 13H20V17H4V13M6 5H8V5H6V5M6 15H8V15H6V15Z"/>
    </svg>
  `,

  'cache-monitor': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2C17.5 2 22 6.5 22 12S17.5 22 12 22S2 17.5 2 12S6.5 2 12 2M12 4C7.58 4 4 7.58 4 12S7.58 20 12 20S20 16.42 20 12S16.42 4 12 4M12 6C15.31 6 18 8.69 18 12S15.31 18 12 18S6 15.31 6 12S8.69 6 12 6M12 8C9.79 8 8 9.79 8 12S9.79 16 12 16S16 14.21 16 12S14.21 8 12 8Z"/>
    </svg>
  `,

  'operation-log': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M14 2H6C4.89 2 4 2.9 4 4V20C4 21.11 4.89 22 6 22H18C19.11 22 20 21.11 20 20V8L14 2M18 20H6V4H13V9H18V20M9 13V19H7V13H9M12 10V19H10V10H12M15 16V19H13V16H15Z"/>
    </svg>
  `,

  'login-log': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M10 17V14H3V10H10V7L15 12L10 17M10 2H19C20.11 2 21 2.9 21 4V20C21 21.11 20.11 22 19 22H10C8.89 22 8 21.11 8 20V18H10V20H19V4H10V6H8V4C8 2.89 8.89 2 10 2Z"/>
    </svg>
  `,

  'form-builder': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3M19 19H5V5H19V19M17 17H7V15H17V17M17 13H7V11H17V13M17 9H7V7H17V9Z"/>
    </svg>
  `,

  'code-generator': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M8 3C6.9 3 6 3.9 6 5V9C6 10.1 6.9 11 8 11H16C17.1 11 18 10.1 18 9V5C18 3.9 17.1 3 16 3H8M8 5H16V9H8V5M5 13C3.9 13 3 13.9 3 15V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V15C21 13.9 20.1 13 19 13H5M5 15H19V19H5V15Z"/>
    </svg>
  `,

  'api-docs': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M14 2H6C4.89 2 4 2.9 4 4V20C4 21.11 4.89 22 6 22H18C19.11 22 20 21.11 20 20V8L14 2M18 20H6V4H13V9H18V20M9 13V19H7V13H9M12 10V19H10V10H12M15 16V19H13V16H15Z"/>
    </svg>
  `,

  'approval-instances': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M14 2H6C4.89 2 4 2.9 4 4V20C4 21.11 4.89 22 6 22H18C19.11 22 20 21.11 20 20V8L14 2M18 20H6V4H13V9H18V20M9.54 15.65L8.21 14.32L7.5 15.03L9.54 17.07L16.5 10.11L15.79 9.4L9.54 15.65Z"/>
    </svg>
  `,

  // 通用操作图标
  'add-item': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M19 13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
    </svg>
  `,

  'edit-item': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M20.71 7.04C21.1 6.65 21.1 6 20.71 5.63L18.37 3.29C18 2.9 17.35 2.9 16.96 3.29L15.12 5.12L18.87 8.87M3 17.25V21H6.75L17.81 9.93L14.06 6.18L3 17.25Z"/>
    </svg>
  `,

  'delete-item': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M19 4H15.5L14.5 3H9.5L8.5 4H5V6H19M6 19C6 20.1 6.9 21 8 21H16C17.1 21 18 20.1 18 19V7H6V19Z"/>
    </svg>
  `,

  'export-data': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M14 2H6C4.89 2 4 2.9 4 4V20C4 21.11 4.89 22 6 22H18C19.11 22 20 21.11 20 20V8L14 2M18 20H6V4H13V9H18V20M12 12L16 16H13.5V19H10.5V16H8L12 12Z"/>
    </svg>
  `,

  'import-data': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M14 2H6C4.89 2 4 2.9 4 4V20C4 21.11 4.89 22 6 22H18C19.11 22 20 21.11 20 20V8L14 2M18 20H6V4H13V9H18V20M12 19L8 15H10.5V12H13.5V15H16L12 19Z"/>
    </svg>
  `,

  'search-data': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M9.5 3C13.09 3 16 5.91 16 9.5C16 11.11 15.41 12.59 14.44 13.73L14.71 14H15.5L20.5 19L19 20.5L14 15.5V14.71L13.73 14.44C12.59 15.41 11.11 16 9.5 16C5.91 16 3 13.09 3 9.5S5.91 3 9.5 3M9.5 5C7 5 5 7 5 9.5S7 14 9.5 14S14 12 14 9.5S12 5 9.5 5Z"/>
    </svg>
  `,

  'refresh-data': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4C7.58 4 4 7.58 4 12S7.58 20 12 20C15.73 20 18.84 17.45 19.73 14H17.65C16.83 16.33 14.61 18 12 18C8.69 18 6 15.31 6 12S8.69 6 12 6C13.66 6 15.14 6.69 16.22 7.78L13 11H20V4L17.65 6.35Z"/>
    </svg>
  `,

  'view-details': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 9C13.38 9 14.5 10.12 14.5 11.5S13.38 14 12 14S9.5 12.88 9.5 11.5S10.62 9 12 9M12 7C9.52 7 7.5 9.02 7.5 11.5S9.52 16 12 16S16.5 13.98 16.5 11.5S14.48 7 12 7M12 4.5C17 4.5 21.27 7.61 23 12C21.27 16.39 17 19.5 12 19.5S2.73 16.39 1 12C2.73 7.61 7 4.5 12 4.5Z"/>
    </svg>
  `,

  'settings': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5a3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97c0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1c0 .***********.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.**********.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.***********.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z"/>
    </svg>
  `,

  // 协议相关图标
  'sip-protocol': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M6.62 10.79C8.06 13.62 10.38 15.94 13.21 17.38L15.41 15.18C15.69 14.9 16.08 14.82 16.43 14.93C17.55 15.3 18.75 15.5 20 15.5C20.55 15.5 21 15.95 21 16.5V20C21 20.55 20.55 21 20 21C10.61 21 3 13.39 3 4C3 3.45 3.45 3 4 3H7.5C8.05 3 8.5 3.45 8.5 4C8.5 5.25 8.7 6.45 9.07 7.57C9.18 7.92 9.1 8.31 8.82 8.59L6.62 10.79Z"/>
    </svg>
  `,

  'netty-server': `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M4 1C2.9 1 2 1.9 2 3V7C2 8.11 2.89 9 4 9H20C21.11 9 22 8.11 22 7V3C22 1.89 21.11 1 20 1H4M4 3H20V7H4V3M4 11C2.9 11 2 11.9 2 13V17C2 18.11 2.89 19 4 19H20C21.11 19 22 18.11 22 17V13C22 11.89 21.11 11 20 11H4M4 13H20V17H4V13M6 5H8V5H6V5M6 15H8V15H6V15Z"/>
    </svg>
  `
}

// 获取菜单图标的函数
export function getMenuIcon(menuName, menuPath = '') {
  // 优先匹配菜单名称
  if (mainMenuIcons[menuName]) {
    return mainMenuIcons[menuName]
  }
  
  if (subMenuIcons[menuName]) {
    return subMenuIcons[menuName]
  }
  
  // 其次匹配路径
  if (menuPath) {
    const pathKey = menuPath.split('/').pop() || menuPath
    if (mainMenuIcons[pathKey]) {
      return mainMenuIcons[pathKey]
    }
    if (subMenuIcons[pathKey]) {
      return subMenuIcons[pathKey]
    }
  }
  
  // 默认图标
  return 'system-setting'
}

// 获取SVG图标内容
export function getSvgIcon(iconName) {
  return svgIcons[iconName] || svgIcons['system-setting']
}

// 图标颜色主题配置
export const iconThemes = {
  primary: '#1e3a8a',      // 主蓝色
  secondary: '#3b82f6',    // 次蓝色
  success: '#10b981',      // 成功绿色
  warning: '#f59e0b',      // 警告橙色
  danger: '#ef4444',       // 危险红色
  info: '#6b7280',         // 信息灰色
  light: '#f8fafc',        // 浅色
  dark: '#1f2937'          // 深色
}

export default {
  mainMenuIcons,
  subMenuIcons,
  svgIcons,
  getMenuIcon,
  getSvgIcon,
  iconThemes
}
