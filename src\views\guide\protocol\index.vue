<template>
  <div class="app-container">
    <div class="page-header">
      <h3>智能导寻协议管理</h3>
      <p>管理智能导寻系统的通信协议，包括位置定位、路径规划、导航指令等。</p>
    </div>
    
    <el-alert
      title="功能开发中"
      type="info"
      description="智能导寻协议管理功能正在开发中，敬请期待..."
      show-icon
      :closable="false">
    </el-alert>

    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="8">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>位置查询</span>
          </div>
          <div class="text item">
            <p>查询设备或人员的实时位置</p>
            <el-button type="primary" size="small" disabled>位置查询</el-button>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>导航控制</span>
          </div>
          <div class="text item">
            <p>发送导航指令和路径规划</p>
            <el-button type="success" size="small" disabled>导航控制</el-button>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>协议配置</span>
          </div>
          <div class="text item">
            <p>配置导寻系统通信协议</p>
            <el-button type="warning" size="small" disabled>协议配置</el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "GuideProtocol",
  data() {
    return {};
  }
};
</script>

<style scoped>
.page-header {
  margin-bottom: 20px;
}
.text {
  font-size: 14px;
}
.item {
  margin-bottom: 18px;
}
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both
}
.box-card {
  width: 100%;
}
</style>
