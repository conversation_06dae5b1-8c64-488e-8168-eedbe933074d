module.exports = {
  // 行宽
  printWidth: 100,
  
  // 缩进
  tabWidth: 2,
  useTabs: false,
  
  // 分号
  semi: false,
  
  // 引号
  singleQuote: true,
  quoteProps: 'as-needed',
  
  // JSX引号
  jsxSingleQuote: true,
  
  // 尾随逗号
  trailingComma: 'none',
  
  // 括号间距
  bracketSpacing: true,
  
  // JSX括号
  bracketSameLine: false,
  
  // 箭头函数参数括号
  arrowParens: 'avoid',
  
  // 换行符
  endOfLine: 'lf',
  
  // HTML空白敏感性
  htmlWhitespaceSensitivity: 'css',
  
  // Vue文件中的脚本和样式标签缩进
  vueIndentScriptAndStyle: false,
  
  // 嵌入式语言格式化
  embeddedLanguageFormatting: 'auto',
  
  // 单个属性的HTML元素不换行
  singleAttributePerLine: false,
  
  // 覆盖特定文件的配置
  overrides: [
    {
      files: '*.vue',
      options: {
        parser: 'vue'
      }
    },
    {
      files: '*.json',
      options: {
        parser: 'json',
        trailingComma: 'none'
      }
    },
    {
      files: '*.md',
      options: {
        parser: 'markdown',
        proseWrap: 'preserve'
      }
    },
    {
      files: '*.yaml',
      options: {
        parser: 'yaml'
      }
    },
    {
      files: '*.yml',
      options: {
        parser: 'yaml'
      }
    }
  ]
}
