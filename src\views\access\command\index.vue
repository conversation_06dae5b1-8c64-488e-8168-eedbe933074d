<template>
  <div class="app-container">
    <div class="page-header">
      <h3>门禁命令管理</h3>
      <p>管理门禁设备的控制命令，包括开门、关门、权限设置等操作。</p>
    </div>
    
    <el-alert
      title="功能开发中"
      type="info"
      description="门禁命令管理功能正在开发中，敬请期待..."
      show-icon
      :closable="false">
    </el-alert>

    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="8">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>开门命令</span>
          </div>
          <div class="text item">
            <p>远程控制门禁设备开门</p>
            <el-button type="primary" size="small" disabled>执行开门</el-button>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>权限同步</span>
          </div>
          <div class="text item">
            <p>同步用户权限到门禁设备</p>
            <el-button type="success" size="small" disabled>同步权限</el-button>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>设备配置</span>
          </div>
          <div class="text item">
            <p>配置门禁设备参数</p>
            <el-button type="warning" size="small" disabled>设备配置</el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "AccessCommand",
  data() {
    return {};
  }
};
</script>

<style scoped>
.page-header {
  margin-bottom: 20px;
}
.text {
  font-size: 14px;
}
.item {
  margin-bottom: 18px;
}
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both
}
.box-card {
  width: 100%;
}
</style>
