import request from '@/utils/request'

// 查询出库防错设备参数配置列表
export function listConfig(query) {
  return request({
    url: '/outmis/config/list',
    method: 'get',
    params: query
  })
}

// 查询出库防错设备参数配置详细
export function getConfig(configId) {
  return request({
    url: '/outmis/config/' + configId,
    method: 'get'
  })
}

// 根据设备ID查询配置列表
export function getConfigByDeviceId(deviceId) {
  return request({
    url: '/outmis/config/device/' + deviceId,
    method: 'get'
  })
}

// 根据配置分组查询配置列表
export function getConfigByGroup(deviceId, configGroup) {
  return request({
    url: '/outmis/config/device/' + deviceId + '/group/' + configGroup,
    method: 'get'
  })
}

// 根据设备ID和配置键查询配置
export function getConfigByDeviceIdAndKey(deviceId, configKey) {
  return request({
    url: '/outmis/config/device/' + deviceId + '/key/' + configKey,
    method: 'get'
  })
}

// 新增出库防错设备参数配置
export function addConfig(data) {
  return request({
    url: '/outmis/config',
    method: 'post',
    data: data
  })
}

// 修改出库防错设备参数配置
export function updateConfig(data) {
  return request({
    url: '/outmis/config',
    method: 'put',
    data: data
  })
}

// 删除出库防错设备参数配置
export function delConfig(configId) {
  return request({
    url: '/outmis/config/' + configId,
    method: 'delete'
  })
}

// 初始化设备默认配置
export function initDeviceConfig(deviceId) {
  return request({
    url: '/outmis/config/init/' + deviceId,
    method: 'post'
  })
}

// 批量保存设备配置
export function batchSaveConfig(configList) {
  return request({
    url: '/outmis/config/batch',
    method: 'post',
    data: configList
  })
}
