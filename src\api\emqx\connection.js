import request from '@/utils/request'

// 获取MQTT连接统计信息
export function getConnectionStats() {
  return request({
    url: '/emqx/connection/stats',
    method: 'get'
  })
}

// 手动清理重复连接
export function manualCleanup() {
  return request({
    url: '/emqx/connection/cleanup',
    method: 'post'
  })
}

// 断开指定客户端连接
export function disconnectClient(clientId) {
  return request({
    url: '/emqx/connection/disconnect/' + clientId,
    method: 'post'
  })
}

// 获取指定客户端连接信息
export function getConnectionInfo(clientId) {
  return request({
    url: '/emqx/connection/info/' + clientId,
    method: 'get'
  })
}
