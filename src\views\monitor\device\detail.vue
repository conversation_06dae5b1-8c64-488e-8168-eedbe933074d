<template>
  <div class="device-detail">
    <!-- 设备基本信息 -->
    <el-card class="device-info-card" v-loading="loading">
      <div slot="header" class="card-header">
        <span>设备详情 - {{ deviceInfo.deviceName }}</span>
        <div class="header-actions">
          <el-button type="primary" size="small" @click="performHealthCheck">健康检查</el-button>
          <el-button type="warning" size="small" @click="restartDevice">重启设备</el-button>
          <el-button type="info" size="small" @click="refreshData">刷新</el-button>
        </div>
      </div>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="info-section">
            <h4>基本信息</h4>
            <el-descriptions :column="1" border>
              <el-descriptions-item label="设备ID">{{ deviceInfo.deviceId }}</el-descriptions-item>
              <el-descriptions-item label="设备名称">{{ deviceInfo.deviceName }}</el-descriptions-item>
              <el-descriptions-item label="设备类型">{{ deviceInfo.deviceType }}</el-descriptions-item>
              <el-descriptions-item label="设备版本">{{ deviceInfo.deviceVersion }}</el-descriptions-item>
              <el-descriptions-item label="固件版本">{{ deviceInfo.firmwareVersion }}</el-descriptions-item>
              <el-descriptions-item label="设备位置">{{ deviceInfo.location }}</el-descriptions-item>
            </el-descriptions>
          </div>
        </el-col>
        
        <el-col :span="8">
          <div class="info-section">
            <h4>运行状态</h4>
            <el-descriptions :column="1" border>
              <el-descriptions-item label="在线状态">
                <el-tag :type="deviceInfo.isOnline ? 'success' : 'danger'">
                  {{ deviceInfo.isOnline ? '在线' : '离线' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="最后在线时间">{{ deviceInfo.lastOnlineTime }}</el-descriptions-item>
              <el-descriptions-item label="最后心跳时间">{{ deviceInfo.lastHeartbeatTime }}</el-descriptions-item>
              <el-descriptions-item label="运行时长">{{ deviceInfo.uptimeHours }}小时</el-descriptions-item>
              <el-descriptions-item label="重启次数">{{ deviceInfo.restartCount }}</el-descriptions-item>
              <el-descriptions-item label="错误次数">{{ deviceInfo.errorCount }}</el-descriptions-item>
            </el-descriptions>
          </div>
        </el-col>
        
        <el-col :span="8">
          <div class="info-section">
            <h4>健康评分</h4>
            <div class="health-score-display">
              <div class="score-circle">
                <el-progress
                  type="circle"
                  :percentage="deviceInfo.healthScore"
                  :color="getHealthColor(deviceInfo.healthScore)"
                  :width="120"
                  :stroke-width="8"
                >
                  <span class="score-text">{{ deviceInfo.healthScore }}%</span>
                </el-progress>
              </div>
              <div class="health-details">
                <div class="health-item">
                  <span class="label">连接性:</span>
                  <span class="value">{{ deviceInfo.connectivityScore }}%</span>
                </div>
                <div class="health-item">
                  <span class="label">性能:</span>
                  <span class="value">{{ deviceInfo.performanceScore }}%</span>
                </div>
                <div class="health-item">
                  <span class="label">稳定性:</span>
                  <span class="value">{{ deviceInfo.stabilityScore }}%</span>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 监控指标 -->
    <el-row :gutter="20" class="metrics-row">
      <el-col :span="12">
        <el-card class="metrics-card">
          <div slot="header" class="card-header">
            <span>实时指标</span>
            <el-button type="text" @click="refreshMetrics">刷新</el-button>
          </div>
          <div ref="metricsChart" class="chart-container"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="metrics-card">
          <div slot="header" class="card-header">
            <span>性能趋势</span>
            <el-button type="text" @click="refreshTrend">刷新</el-button>
          </div>
          <div ref="trendChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细数据标签页 -->
    <el-card class="detail-tabs-card">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="监控指标" name="metrics">
          <el-table :data="metricsData" v-loading="metricsLoading" size="small">
            <el-table-column prop="metricName" label="指标名称" width="150" />
            <el-table-column prop="currentValue" label="当前值" width="120" />
            <el-table-column prop="unit" label="单位" width="80" />
            <el-table-column prop="threshold" label="阈值" width="120" />
            <el-table-column label="状态" width="100">
              <template slot-scope="scope">
                <el-tag :type="getMetricStatusType(scope.row.status)" size="small">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="updateTime" label="更新时间" />
          </el-table>
        </el-tab-pane>
        
        <el-tab-pane label="告警记录" name="alerts">
          <el-table :data="alertsData" v-loading="alertsLoading" size="small">
            <el-table-column prop="alertId" label="告警ID" width="120" />
            <el-table-column prop="alertType" label="告警类型" width="120" />
            <el-table-column label="告警级别" width="100">
              <template slot-scope="scope">
                <el-tag :type="getAlertLevelType(scope.row.alertLevel)" size="small">
                  {{ scope.row.alertLevel }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="message" label="告警消息" />
            <el-table-column label="状态" width="100">
              <template slot-scope="scope">
                <el-tag :type="getAlertStatusType(scope.row.status)" size="small">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="发生时间" width="150" />
            <el-table-column label="操作" width="120">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="viewAlertDetail(scope.row)">详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        
        <el-tab-pane label="操作日志" name="logs">
          <el-table :data="logsData" v-loading="logsLoading" size="small">
            <el-table-column prop="logId" label="日志ID" width="120" />
            <el-table-column prop="operation" label="操作类型" width="120" />
            <el-table-column prop="operator" label="操作人" width="100" />
            <el-table-column prop="description" label="操作描述" />
            <el-table-column prop="result" label="结果" width="80">
              <template slot-scope="scope">
                <el-tag :type="scope.row.result === 'SUCCESS' ? 'success' : 'danger'" size="small">
                  {{ scope.row.result }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="操作时间" width="150" />
          </el-table>
        </el-tab-pane>
        
        <el-tab-pane label="配置信息" name="config">
          <el-form :model="deviceConfig" label-width="120px" size="small">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="心跳间隔(秒)">
                  <el-input-number v-model="deviceConfig.heartbeatInterval" :min="10" :max="300" />
                </el-form-item>
                <el-form-item label="数据上报间隔(秒)">
                  <el-input-number v-model="deviceConfig.reportInterval" :min="5" :max="600" />
                </el-form-item>
                <el-form-item label="连接超时(秒)">
                  <el-input-number v-model="deviceConfig.connectionTimeout" :min="5" :max="60" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="重试次数">
                  <el-input-number v-model="deviceConfig.retryCount" :min="1" :max="10" />
                </el-form-item>
                <el-form-item label="日志级别">
                  <el-select v-model="deviceConfig.logLevel" placeholder="请选择">
                    <el-option label="DEBUG" value="DEBUG" />
                    <el-option label="INFO" value="INFO" />
                    <el-option label="WARN" value="WARN" />
                    <el-option label="ERROR" value="ERROR" />
                  </el-select>
                </el-form-item>
                <el-form-item label="启用调试">
                  <el-switch v-model="deviceConfig.debugEnabled" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item>
              <el-button type="primary" @click="saveConfig">保存配置</el-button>
              <el-button @click="resetConfig">重置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import {
  getDeviceDetail,
  getDeviceMetrics,
  getDeviceConfig,
  updateDeviceConfig,
  performHealthCheck,
  restartDevice,
  getDeviceLogs
} from '@/api/monitor/device'
import { getAlertList } from '@/api/monitor/device'

export default {
  name: 'DeviceDetail',
  data() {
    return {
      loading: false,
      deviceId: '',
      activeTab: 'metrics',
      
      // 设备信息
      deviceInfo: {
        deviceId: '',
        deviceName: '',
        deviceType: '',
        deviceVersion: '',
        firmwareVersion: '',
        location: '',
        isOnline: false,
        lastOnlineTime: '',
        lastHeartbeatTime: '',
        uptimeHours: 0,
        restartCount: 0,
        errorCount: 0,
        healthScore: 0,
        connectivityScore: 0,
        performanceScore: 0,
        stabilityScore: 0
      },
      
      // 监控指标
      metricsData: [],
      metricsLoading: false,
      
      // 告警数据
      alertsData: [],
      alertsLoading: false,
      
      // 日志数据
      logsData: [],
      logsLoading: false,
      
      // 设备配置
      deviceConfig: {
        heartbeatInterval: 30,
        reportInterval: 60,
        connectionTimeout: 30,
        retryCount: 3,
        logLevel: 'INFO',
        debugEnabled: false
      },
      
      // 图表实例
      metricsChart: null,
      trendChart: null
    }
  },
  
  created() {
    this.deviceId = this.$route.query.deviceId
    if (this.deviceId) {
      this.loadDeviceDetail()
    }
  },
  
  beforeDestroy() {
    if (this.metricsChart) {
      this.metricsChart.dispose()
    }
    if (this.trendChart) {
      this.trendChart.dispose()
    }
  },
  
  methods: {
    /** 加载设备详情 */
    async loadDeviceDetail() {
      this.loading = true
      try {
        const response = await getDeviceDetail(this.deviceId)
        this.deviceInfo = response.data
        
        // 加载其他数据
        await Promise.all([
          this.loadMetricsData(),
          this.loadDeviceConfig()
        ])
        
        this.$nextTick(() => {
          this.initCharts()
        })
      } catch (error) {
        console.error('加载设备详情失败:', error)
        this.$message.error('加载设备详情失败')
        // 使用模拟数据
        this.loadMockData()
      } finally {
        this.loading = false
      }
    },
    
    /** 加载模拟数据 */
    loadMockData() {
      this.deviceInfo = {
        deviceId: this.deviceId || 'DEV001',
        deviceName: 'RFID读写器01',
        deviceType: 'RFID',
        deviceVersion: 'v2.1.0',
        firmwareVersion: 'fw1.5.2',
        location: '仓库A-01区',
        isOnline: true,
        lastOnlineTime: '2025-01-27 10:30:00',
        lastHeartbeatTime: '2025-01-27 10:29:45',
        uptimeHours: 168.5,
        restartCount: 2,
        errorCount: 1,
        healthScore: 95,
        connectivityScore: 98,
        performanceScore: 92,
        stabilityScore: 95
      }
      
      this.metricsData = [
        { metricName: 'CPU使用率', currentValue: '15.2', unit: '%', threshold: '< 80', status: '正常', updateTime: '2025-01-27 10:29:45' },
        { metricName: '内存使用率', currentValue: '45.8', unit: '%', threshold: '< 90', status: '正常', updateTime: '2025-01-27 10:29:45' },
        { metricName: '网络延迟', currentValue: '12', unit: 'ms', threshold: '< 100', status: '正常', updateTime: '2025-01-27 10:29:45' },
        { metricName: '温度', currentValue: '42.5', unit: '°C', threshold: '< 70', status: '正常', updateTime: '2025-01-27 10:29:45' }
      ]
      
      this.$nextTick(() => {
        this.initCharts()
      })
    },
    
    /** 加载监控指标数据 */
    async loadMetricsData() {
      this.metricsLoading = true
      try {
        const response = await getDeviceMetrics(this.deviceId, { latest: true })
        this.metricsData = response.data || []
      } catch (error) {
        console.error('加载监控指标失败:', error)
      } finally {
        this.metricsLoading = false
      }
    },
    
    /** 加载设备配置 */
    async loadDeviceConfig() {
      try {
        const response = await getDeviceConfig(this.deviceId)
        this.deviceConfig = { ...this.deviceConfig, ...response.data }
      } catch (error) {
        console.error('加载设备配置失败:', error)
      }
    },
    
    /** 初始化图表 */
    initCharts() {
      this.initMetricsChart()
      this.initTrendChart()
    },
    
    /** 初始化指标图表 */
    initMetricsChart() {
      this.metricsChart = echarts.init(this.$refs.metricsChart)
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['CPU', '内存', '网络', '温度']
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value}%'
          }
        },
        series: [
          {
            name: '使用率',
            type: 'bar',
            data: [15.2, 45.8, 12, 42.5],
            itemStyle: {
              color: function(params) {
                const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666']
                return colors[params.dataIndex]
              }
            }
          }
        ]
      }
      
      this.metricsChart.setOption(option)
    },
    
    /** 初始化趋势图表 */
    initTrendChart() {
      this.trendChart = echarts.init(this.$refs.trendChart)
      
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['CPU', '内存', '网络延迟']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['10:00', '10:05', '10:10', '10:15', '10:20', '10:25', '10:30']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: 'CPU',
            type: 'line',
            data: [12, 15, 18, 16, 14, 15, 15],
            smooth: true,
            itemStyle: { color: '#5470c6' }
          },
          {
            name: '内存',
            type: 'line',
            data: [42, 44, 46, 45, 46, 45, 46],
            smooth: true,
            itemStyle: { color: '#91cc75' }
          },
          {
            name: '网络延迟',
            type: 'line',
            data: [8, 12, 15, 10, 11, 12, 12],
            smooth: true,
            itemStyle: { color: '#fac858' }
          }
        ]
      }
      
      this.trendChart.setOption(option)
    },
    
    /** 标签页切换 */
    async handleTabClick(tab) {
      if (tab.name === 'alerts' && this.alertsData.length === 0) {
        await this.loadAlertsData()
      } else if (tab.name === 'logs' && this.logsData.length === 0) {
        await this.loadLogsData()
      }
    },
    
    /** 加载告警数据 */
    async loadAlertsData() {
      this.alertsLoading = true
      try {
        const response = await getAlertList({ deviceId: this.deviceId })
        this.alertsData = response.rows || []
      } catch (error) {
        console.error('加载告警数据失败:', error)
        // 模拟数据
        this.alertsData = [
          {
            alertId: 'ALT001',
            alertType: '连接异常',
            alertLevel: 'WARNING',
            message: '设备连接不稳定',
            status: 'RESOLVED',
            createTime: '2025-01-27 09:15:00'
          }
        ]
      } finally {
        this.alertsLoading = false
      }
    },
    
    /** 加载日志数据 */
    async loadLogsData() {
      this.logsLoading = true
      try {
        const response = await getDeviceLogs(this.deviceId, { pageNum: 1, pageSize: 20 })
        this.logsData = response.rows || []
      } catch (error) {
        console.error('加载日志数据失败:', error)
        // 模拟数据
        this.logsData = [
          {
            logId: 'LOG001',
            operation: '健康检查',
            operator: 'system',
            description: '执行设备健康检查',
            result: 'SUCCESS',
            createTime: '2025-01-27 10:00:00'
          }
        ]
      } finally {
        this.logsLoading = false
      }
    },
    
    /** 获取健康度颜色 */
    getHealthColor(score) {
      if (score >= 90) return '#67C23A'
      if (score >= 70) return '#E6A23C'
      return '#F56C6C'
    },
    
    /** 获取指标状态类型 */
    getMetricStatusType(status) {
      const typeMap = {
        '正常': 'success',
        '警告': 'warning',
        '异常': 'danger'
      }
      return typeMap[status] || 'info'
    },
    
    /** 获取告警级别类型 */
    getAlertLevelType(level) {
      const typeMap = {
        'CRITICAL': 'danger',
        'WARNING': 'warning',
        'INFO': 'info'
      }
      return typeMap[level] || 'info'
    },
    
    /** 获取告警状态类型 */
    getAlertStatusType(status) {
      const typeMap = {
        'ACTIVE': 'danger',
        'ACKNOWLEDGED': 'warning',
        'RESOLVED': 'success'
      }
      return typeMap[status] || 'info'
    },
    
    /** 执行健康检查 */
    async performHealthCheck() {
      try {
        this.$message.info('正在执行健康检查...')
        await performHealthCheck(this.deviceId)
        this.$message.success('健康检查完成')
        this.refreshData()
      } catch (error) {
        this.$message.error('健康检查失败: ' + error.message)
      }
    },
    
    /** 重启设备 */
    async restartDevice() {
      try {
        await this.$confirm('确认要重启设备吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        this.$message.info('正在重启设备...')
        await restartDevice(this.deviceId)
        this.$message.success('设备重启命令已发送')
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('重启设备失败: ' + error.message)
        }
      }
    },
    
    /** 保存配置 */
    async saveConfig() {
      try {
        await updateDeviceConfig(this.deviceId, this.deviceConfig)
        this.$message.success('配置保存成功')
      } catch (error) {
        this.$message.error('保存配置失败: ' + error.message)
      }
    },
    
    /** 重置配置 */
    resetConfig() {
      this.loadDeviceConfig()
    },
    
    /** 查看告警详情 */
    viewAlertDetail(alert) {
      this.$router.push({
        path: '/monitor/alert/detail',
        query: { alertId: alert.alertId }
      })
    },
    
    /** 刷新数据 */
    refreshData() {
      this.loadDeviceDetail()
    },
    
    /** 刷新指标 */
    refreshMetrics() {
      this.loadMetricsData()
      this.initMetricsChart()
    },
    
    /** 刷新趋势 */
    refreshTrend() {
      this.initTrendChart()
    }
  }
}
</script>

<style scoped>
.device-detail {
  padding: 20px;
}

.device-info-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.info-section {
  margin-bottom: 20px;
}

.info-section h4 {
  margin-bottom: 15px;
  color: #303133;
  border-bottom: 2px solid #409EFF;
  padding-bottom: 5px;
}

.health-score-display {
  display: flex;
  align-items: center;
  gap: 20px;
}

.score-circle {
  flex-shrink: 0;
}

.score-text {
  font-size: 18px;
  font-weight: bold;
}

.health-details {
  flex: 1;
}

.health-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  padding: 5px 0;
  border-bottom: 1px solid #f0f0f0;
}

.health-item .label {
  color: #606266;
}

.health-item .value {
  font-weight: bold;
  color: #303133;
}

.metrics-row {
  margin-bottom: 20px;
}

.metrics-card {
  height: 400px;
}

.chart-container {
  height: 320px;
}

.detail-tabs-card {
  min-height: 500px;
}
</style>
