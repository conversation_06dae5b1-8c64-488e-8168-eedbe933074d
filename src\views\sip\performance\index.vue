<template>
  <div class="app-container">
    <!-- SIP视频服务状态 -->
    <el-card class="server-status-card" shadow="never">
      <div slot="header">
        <div class="server-header">
          <div class="server-title">
            <i class="el-icon-monitor"></i>
            <span>SIP视频服务性能监控</span>
            <el-tag :type="sipServerStatus.online ? 'success' : 'danger'" size="mini">
              SIP: {{ sipServerStatus.online ? '在线' : '离线' }}
            </el-tag>
            <el-tag :type="zlmServerStatus.online ? 'success' : 'danger'" size="mini">
              ZLM: {{ zlmServerStatus.online ? '在线' : '离线' }}
            </el-tag>
          </div>
          <div class="server-info">
            <span>SIP服务器: {{ sipServerStatus.host }}:{{ sipServerStatus.port }}</span>
            <span>ZLM服务器: {{ zlmServerStatus.host }}:{{ zlmServerStatus.port }}</span>
            <span>版本: {{ zlmServerStatus.version || '获取中...' }}</span>
            <span v-if="showUptime">运行时间: {{ zlmServerStatus.uptime || '计算中...' }}</span>
            <el-button type="primary" size="mini" @click="refreshData" :loading="loading">
              <i class="el-icon-refresh"></i> 刷新
            </el-button>
            <el-button type="text" size="mini" @click="showUptime = !showUptime" title="切换运行时间显示">
              <i class="el-icon-time"></i> {{ showUptime ? '隐藏' : '显示' }}运行时间
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 核心性能指标 -->
      <el-row :gutter="20" class="metrics-row">
        <!-- SIP设备指标 -->
        <el-col :span="3">
          <div class="metric-item">
            <div class="metric-icon sip-devices">
              <i class="el-icon-phone"></i>
            </div>
            <div class="metric-info">
              <div class="metric-title">SIP设备</div>
              <div class="metric-value">{{ sipMetrics.deviceCount }}</div>
              <div class="metric-subtitle">个设备</div>
            </div>
          </div>
        </el-col>
        
        <!-- SIP会话指标 -->
        <el-col :span="3">
          <div class="metric-item">
            <div class="metric-icon sip-sessions">
              <i class="el-icon-connection"></i>
            </div>
            <div class="metric-info">
              <div class="metric-title">活跃会话</div>
              <div class="metric-value">{{ sipMetrics.activeSessions }}</div>
              <div class="metric-subtitle">个会话</div>
            </div>
          </div>
        </el-col>
        
        <!-- ZLM流数指标 -->
        <el-col :span="3">
          <div class="metric-item">
            <div class="metric-icon streams">
              <i class="el-icon-video-camera"></i>
            </div>
            <div class="metric-info">
              <div class="metric-title">活跃流数</div>
              <div class="metric-value">{{ zlmMetrics.activeStreams }}</div>
              <div class="metric-subtitle">个流</div>
            </div>
          </div>
        </el-col>
        
        <!-- ZLM连接数 -->
        <el-col :span="3">
          <div class="metric-item">
            <div class="metric-icon connections">
              <i class="el-icon-link"></i>
            </div>
            <div class="metric-info">
              <div class="metric-title">ZLM连接</div>
              <div class="metric-value">{{ zlmMetrics.connections }}</div>
              <div class="metric-subtitle">个连接</div>
            </div>
          </div>
        </el-col>
        
        <!-- 带宽使用 -->
        <el-col :span="3">
          <div class="metric-item">
            <div class="metric-icon bandwidth">
              <i class="el-icon-upload"></i>
            </div>
            <div class="metric-info">
              <div class="metric-title">带宽使用</div>
              <div class="metric-value">{{ formatBandwidth(zlmMetrics.bandwidth) }}</div>
              <div class="metric-subtitle">Mbps</div>
            </div>
          </div>
        </el-col>
        
        <!-- CPU使用率 -->
        <el-col :span="3">
          <div class="metric-item">
            <div class="metric-icon cpu">
              <i class="el-icon-cpu"></i>
            </div>
            <div class="metric-info">
              <div class="metric-title">CPU使用率</div>
              <div class="metric-value">{{ zlmMetrics.cpuUsage }}%</div>
              <div class="metric-subtitle">处理器</div>
            </div>
          </div>
        </el-col>
        
        <!-- 内存使用 -->
        <el-col :span="3">
          <div class="metric-item">
            <div class="metric-icon memory">
              <i class="el-icon-pie-chart"></i>
            </div>
            <div class="metric-info">
              <div class="metric-title">内存使用</div>
              <div class="metric-value">{{ formatMemory(zlmMetrics.memoryUsage) }}</div>
              <div class="metric-subtitle">GB</div>
            </div>
          </div>
        </el-col>
        
        <!-- 平均延迟 -->
        <el-col :span="3">
          <div class="metric-item">
            <div class="metric-icon network">
              <i class="el-icon-timer"></i>
            </div>
            <div class="metric-info">
              <div class="metric-title">平均延迟</div>
              <div class="metric-value">{{ zlmMetrics.avgLatency }}</div>
              <div class="metric-subtitle">ms</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 详细监控图表 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- SIP服务监控 -->
      <el-col :span="12">
        <el-card title="SIP服务监控" shadow="hover">
          <div class="chart-container">
            <div class="chart-placeholder">
              <i class="el-icon-phone" style="font-size: 48px; color: #409EFF;"></i>
              <p>SIP服务监控图表</p>
              <p class="chart-desc">设备注册、会话建立、消息处理等实时监控</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- ZLMediaKit监控 -->
      <el-col :span="12">
        <el-card title="ZLMediaKit监控" shadow="hover">
          <div class="chart-container">
            <div class="chart-placeholder">
              <i class="el-icon-video-camera" style="font-size: 48px; color: #67C23A;"></i>
              <p>ZLMediaKit监控图表</p>
              <p class="chart-desc">流媒体处理、带宽使用、性能指标等监控</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统资源监控 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card title="系统资源监控" shadow="hover">
          <div class="resource-monitoring">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="resource-item">
                  <h4>CPU使用率趋势</h4>
                  <div class="chart-placeholder small">
                    <i class="el-icon-cpu" style="font-size: 32px; color: #E6A23C;"></i>
                    <p>CPU监控图表</p>
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="resource-item">
                  <h4>内存使用趋势</h4>
                  <div class="chart-placeholder small">
                    <i class="el-icon-pie-chart" style="font-size: 32px; color: #F56C6C;"></i>
                    <p>内存监控图表</p>
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="resource-item">
                  <h4>网络流量趋势</h4>
                  <div class="chart-placeholder small">
                    <i class="el-icon-upload" style="font-size: 32px; color: #909399;"></i>
                    <p>网络监控图表</p>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getSipMetrics, getSipServerStatus, getZlmMetrics, getZlmServerStatus } from '@/api/sip/performance'

export default {
  name: 'SipPerformanceMonitoring',
  data() {
    return {
      loading: false,
      showUptime: false,
      
      // SIP服务器状态
      sipServerStatus: {
        online: false,
        host: 'localhost',
        port: 5060
      },
      
      // ZLMediaKit服务器状态
      zlmServerStatus: {
        online: false,
        host: 'localhost',
        port: 8080,
        containerName: 'zlmediakit',
        version: '',
        uptime: ''
      },
      
      // SIP监控指标
      sipMetrics: {
        deviceCount: 0,
        activeSessions: 0,
        messageCount: 0,
        registeredDevices: 0
      },
      
      // ZLMediaKit监控指标
      zlmMetrics: {
        activeStreams: 0,
        connections: 0,
        bandwidth: 0,
        cpuUsage: 0,
        memoryUsage: 0,
        avgLatency: 0
      }
    }
  },
  
  created() {
    this.refreshData()
    // 设置定时刷新
    this.timer = setInterval(() => {
      this.refreshData()
    }, 30000) // 30秒刷新一次
  },
  
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  
  methods: {
    async refreshData() {
      this.loading = true
      try {
        await Promise.all([
          this.refreshSipData(),
          this.refreshZlmData()
        ])
      } catch (error) {
        console.error('刷新监控数据失败:', error)
        this.$message.error('刷新监控数据失败')
      } finally {
        this.loading = false
      }
    },
    
    async refreshSipData() {
      try {
        const [statusResponse, metricsResponse] = await Promise.all([
          getSipServerStatus().catch(err => ({ code: 500, error: err })),
          getSipMetrics().catch(err => ({ code: 500, error: err }))
        ])

        if (statusResponse.code === 200) {
          this.sipServerStatus.online = statusResponse.data.online
        } else {
          this.sipServerStatus.online = false
        }

        if (metricsResponse.code === 200) {
          this.sipMetrics = metricsResponse.data
        } else {
          this.sipMetrics = {
            deviceCount: 0,
            activeSessions: 0,
            messageCount: 0,
            registeredDevices: 0
          }
        }
      } catch (error) {
        // 使用模拟数据作为后备
        this.sipServerStatus.online = false
        this.sipMetrics = {
          deviceCount: 0,
          activeSessions: 0,
          messageCount: 0,
          registeredDevices: 0
        }
        console.warn('SIP监控数据获取异常，使用默认数据:', error.message)
      }
    },

    async refreshZlmData() {
      try {
        const [statusResponse, metricsResponse] = await Promise.all([
          getZlmServerStatus().catch(err => ({ code: 500, error: err })),
          getZlmMetrics().catch(err => ({ code: 500, error: err }))
        ])

        if (statusResponse.code === 200) {
          this.zlmServerStatus.online = statusResponse.data.online
          this.zlmServerStatus.version = statusResponse.data.version
          this.zlmServerStatus.uptime = statusResponse.data.uptime
        } else {
          this.zlmServerStatus.online = false
        }

        if (metricsResponse.code === 200) {
          this.zlmMetrics = metricsResponse.data
        } else {
          this.zlmMetrics = {
            activeStreams: 0,
            connections: 0,
            bandwidth: 0,
            cpuUsage: 0,
            memoryUsage: 0,
            avgLatency: 0
          }
        }
      } catch (error) {
        // 使用模拟数据作为后备
        this.zlmServerStatus.online = false
        this.zlmMetrics = {
          activeStreams: 0,
          connections: 0,
          bandwidth: 0,
          cpuUsage: 0,
          memoryUsage: 0,
          avgLatency: 0
        }
        console.warn('ZLMediaKit监控数据获取异常，使用默认数据:', error.message)
      }
    },
    
    formatBandwidth(bandwidth) {
      if (!bandwidth) return '0'
      return (bandwidth / 1024 / 1024).toFixed(2)
    },
    
    formatMemory(memory) {
      if (!memory) return '0'
      return (memory / 1024 / 1024 / 1024).toFixed(2)
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.server-status-card {
  margin-bottom: 20px;

  .server-header {
    .server-title {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 10px;

      i {
        font-size: 20px;
        color: #409EFF;
      }

      span {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }
    }

    .server-info {
      display: flex;
      align-items: center;
      gap: 20px;
      flex-wrap: wrap;

      span {
        color: #606266;
        font-size: 14px;
      }
    }
  }
}

.metrics-row {
  margin-top: 20px;

  .metric-item {
    display: flex;
    align-items: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #409EFF;

    .metric-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;

      i {
        font-size: 24px;
        color: white;
      }

      &.sip-devices {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }

      &.sip-sessions {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }

      &.streams {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }

      &.connections {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      }

      &.bandwidth {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
      }

      &.cpu {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
      }

      &.memory {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
      }

      &.network {
        background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
      }
    }

    .metric-info {
      flex: 1;

      .metric-title {
        font-size: 14px;
        color: #606266;
        margin-bottom: 5px;
      }

      .metric-value {
        font-size: 24px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 2px;
      }

      .metric-subtitle {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}

.chart-container {
  height: 300px;

  .chart-placeholder {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 8px;

    p {
      margin: 10px 0 5px 0;
      font-size: 16px;
      color: #303133;
    }

    .chart-desc {
      font-size: 14px;
      color: #909399;
      margin: 0;
    }

    &.small {
      height: 200px;

      p {
        font-size: 14px;
        margin: 5px 0;
      }
    }
  }
}

.resource-monitoring {
  .resource-item {
    h4 {
      margin: 0 0 15px 0;
      color: #303133;
      font-size: 16px;
    }
  }
}
</style>
