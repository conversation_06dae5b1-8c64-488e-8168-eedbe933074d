import request from '@/utils/request'

// 查询设备状态列表
export function listDeviceStatus(query) {
  return request({
    url: '/dwms/device/list',
    method: 'get',
    params: query
  })
}

// 查询设备状态详细
export function getDeviceStatus(id) {
  return request({
    url: '/dwms/device/' + id,
    method: 'get'
  })
}

// 新增设备状态
export function addDeviceStatus(data) {
  return request({
    url: '/dwms/device',
    method: 'post',
    data: data
  })
}

// 修改设备状态
export function updateDeviceStatus(data) {
  return request({
    url: '/dwms/device',
    method: 'put',
    data: data
  })
}

// 删除设备状态
export function delDeviceStatus(id) {
  return request({
    url: '/dwms/device/' + id,
    method: 'delete'
  })
}

// 根据设备ID查询设备状态
export function getDeviceStatusByDeviceId(deviceId) {
  return request({
    url: '/dwms/device/device/' + deviceId,
    method: 'get'
  })
}

// 根据库房ID查询设备状态列表
export function getDeviceStatusByWarehouseId(warehouseId) {
  return request({
    url: '/dwms/device/warehouse/' + warehouseId,
    method: 'get'
  })
}

// 根据设备类型查询设备状态列表
export function getDeviceStatusByType(deviceType, warehouseId) {
  return request({
    url: '/dwms/device/type/' + deviceType,
    method: 'get',
    params: { warehouseId }
  })
}

// 查询在线设备列表
export function getOnlineDevices(warehouseId) {
  return request({
    url: '/dwms/device/online/' + warehouseId,
    method: 'get'
  })
}

// 查询离线设备列表
export function getOfflineDevices(warehouseId) {
  return request({
    url: '/dwms/device/offline/' + warehouseId,
    method: 'get'
  })
}

// 查询告警设备列表
export function getAlertDevices(warehouseId) {
  return request({
    url: '/dwms/device/alert/' + warehouseId,
    method: 'get'
  })
}

// 更新设备心跳时间
export function updateDeviceHeartbeat(deviceId) {
  return request({
    url: '/dwms/device/heartbeat/' + deviceId,
    method: 'post'
  })
}

// 批量更新设备状态
export function batchUpdateDeviceStatus(data) {
  return request({
    url: '/dwms/device/batch',
    method: 'put',
    data: data
  })
}

// 查询设备统计数据
export function getDeviceStats(warehouseId) {
  return request({
    url: '/dwms/device/stats/' + (warehouseId || ''),
    method: 'get'
  })
}

// 查询超时未心跳的设备
export function getTimeoutDevices(timeoutMinutes) {
  return request({
    url: '/dwms/device/timeout',
    method: 'get',
    params: { timeoutMinutes }
  })
}

// 根据位置查询设备列表
export function getDevicesByLocation(location, warehouseId) {
  return request({
    url: '/dwms/device/location',
    method: 'get',
    params: { location, warehouseId }
  })
}

// 设备状态检查
export function checkDeviceStatus(warehouseId) {
  return request({
    url: '/dwms/device/check',
    method: 'post',
    params: { warehouseId }
  })
}

// 重启设备
export function restartDevice(deviceId) {
  return request({
    url: '/dwms/device/restart/' + deviceId,
    method: 'post'
  })
}

// 获取设备配置
export function getDeviceConfig(deviceId) {
  return request({
    url: '/dwms/device/config/' + deviceId,
    method: 'get'
  })
}

// 更新设备配置
export function updateDeviceConfig(deviceId, config) {
  return request({
    url: '/dwms/device/config/' + deviceId,
    method: 'put',
    data: config
  })
}

// 获取设备日志
export function getDeviceLogs(deviceId, level, lines) {
  return request({
    url: '/dwms/device/logs/' + deviceId,
    method: 'get',
    params: { level, lines }
  })
}

// 设备故障诊断
export function diagnoseDevice(deviceId) {
  return request({
    url: '/dwms/device/diagnose/' + deviceId,
    method: 'post'
  })
}

// 获取设备性能数据
export function getDevicePerformanceData(deviceId, hours) {
  return request({
    url: '/dwms/device/performance/' + deviceId,
    method: 'get',
    params: { hours }
  })
}

// 获取设备告警历史
export function getDeviceAlertHistory(deviceId, days) {
  return request({
    url: '/dwms/device/alert/history/' + deviceId,
    method: 'get',
    params: { days }
  })
}

// 清理设备历史数据
export function cleanDeviceHistoryData(retentionDays) {
  return request({
    url: '/dwms/device/clean',
    method: 'delete',
    params: { retentionDays }
  })
}

// 导出设备状态
export function exportDeviceStatus(query) {
  return request({
    url: '/dwms/device/export',
    method: 'get',
    params: query
  })
}
