import request from '@/utils/request'

// 查询主题配置列表
export function listTopic(query) {
  return request({
    url: '/outmis/topic/list',
    method: 'get',
    params: query
  })
}

// 查询主题配置详细
export function getTopic(topicId) {
  return request({
    url: '/outmis/topic/' + topicId,
    method: 'get'
  })
}

// 新增主题配置
export function addTopic(data) {
  return request({
    url: '/outmis/topic',
    method: 'post',
    data: data
  })
}

// 修改主题配置
export function updateTopic(data) {
  return request({
    url: '/outmis/topic',
    method: 'put',
    data: data
  })
}

// 删除主题配置
export function delTopic(topicId) {
  return request({
    url: '/outmis/topic/' + topicId,
    method: 'delete'
  })
}

// 获取主题统计信息
export function getTopicStatistics() {
  return request({
    url: '/outmis/topic/statistics',
    method: 'get'
  })
}

// 测试主题连接
export function testTopic(data) {
  return request({
    url: '/outmis/topic/test',
    method: 'post',
    data: data
  })
}

// 获取设备主题配置
export function getDeviceTopics(deviceType) {
  return request({
    url: '/outmis/topic/device/' + deviceType,
    method: 'get'
  })
}

// 批量生成设备主题
export function generateDeviceTopics(data) {
  return request({
    url: '/outmis/topic/generate',
    method: 'post',
    data: data
  })
}
