import request from '@/utils/request'

// 查询审批流程列表
export function listWorkflow(query) {
  return request({
    url: '/approval/workflow/list',
    method: 'get',
    params: query
  })
}

// 查询审批流程详情
export function getWorkflow(workflowId) {
  // 确保workflowId是字符串类型
  const idStr = String(workflowId);
  return request({
    url: '/approval/workflow/' + idStr,
    method: 'get'
  })
}

// 验证工作流数据
function validateWorkflowData(data) {
  if (!data) {
    console.error('工作流数据为空');
    return;
  }
  
  // 确保ID是字符串类型
  if (data.workflowId) {
    data.workflowId = String(data.workflowId);
  }
  
  // 确保业务类型和名称正确
  if (!data.businessType) {
    console.warn('警告: 业务类型为空');
  }
  
  // 保留业务类型名称，后端可能需要
  if (data.businessTypeName) {
    data.businessTypeName = String(data.businessTypeName);
  }
  
  // 检查审批节点是否正确设置了审批人
  if (data.nodes && Array.isArray(data.nodes)) {
    for (const node of data.nodes) {
      // 确保nodeId是字符串
      if (node.nodeId) {
        node.nodeId = String(node.nodeId);
      }
      
      if (node.nodeType === '1') {
        // 审批节点必须有审批人
        let hasApprovers = node.approvers && Array.isArray(node.approvers) && node.approvers.length > 0;
        let hasUserIds = node.userIds && node.userIds.trim() !== '';
        let hasUserIdList = node.userIdList && Array.isArray(node.userIdList) && node.userIdList.length > 0;
        
        if (!hasApprovers && !hasUserIds && !hasUserIdList) {
          console.error(`审批节点 "${node.nodeName}" 没有设置审批人`, node);
        }
        
        // 清理可能存在的空字符串
        if (hasApprovers) {
          node.approvers = node.approvers.map(id => String(id)).filter(id => id.trim() !== '');
          hasApprovers = node.approvers.length > 0;
        }
        
        if (hasUserIdList) {
          node.userIdList = node.userIdList.map(id => String(id)).filter(id => id.trim() !== '');
          hasUserIdList = node.userIdList.length > 0;
        }
        
        if (hasUserIds) {
          const ids = node.userIds.split(',').map(id => String(id.trim())).filter(id => id !== '');
          node.userIds = ids.join(',');
          hasUserIds = node.userIds !== '';
        }
        
        // 确保字段之间数据一致性
        if (hasApprovers && !hasUserIds) {
          node.userIds = node.approvers.join(',');
          console.log(`为节点 "${node.nodeName}" 从approvers生成userIds: ${node.userIds}`);
        }
        
        if (hasApprovers && !hasUserIdList) {
          node.userIdList = [...node.approvers];
          console.log(`为节点 "${node.nodeName}" 从approvers生成userIdList`);
        }
        
        if (!hasApprovers && hasUserIds) {
          node.approvers = node.userIds.split(',').map(id => String(id.trim())).filter(id => id !== '');
          console.log(`为节点 "${node.nodeName}" 从userIds生成approvers`);
        }
        
        if (!hasApprovers && hasUserIdList) {
          node.approvers = node.userIdList.map(id => String(id));
          console.log(`为节点 "${node.nodeName}" 从userIdList生成approvers`);
        }
        
        if (!hasUserIds && hasUserIdList) {
          node.userIds = node.userIdList.map(id => String(id)).join(',');
        }
        
        // 修复approvalCondition值
        if (node.approvalCondition) {
          if (node.approvalCondition === 'ANY') {
            node.approvalCondition = 'A';
          } else if (node.approvalCondition === 'ALL') {
            node.approvalCondition = 'L';
          } else if (node.approvalCondition.length > 1 && node.approvalCondition !== 'ALL' && node.approvalCondition !== 'ANY') {
            // 取第一个字符
            node.approvalCondition = node.approvalCondition.charAt(0);
          }
        }
      }
      
      // 确保nextNodes是数组
      if (!node.nextNodes) {
        node.nextNodes = [];
      } else if (!Array.isArray(node.nextNodes)) {
        node.nextNodes = [String(node.nextNodes)];
      } else {
        node.nextNodes = node.nextNodes.map(id => String(id));
      }
    }
    
    // 重建节点连接关系
    for (let i = 0; i < data.nodes.length - 1; i++) {
      data.nodes[i].nextNodes = [String(data.nodes[i + 1].nodeId)];
    }
    
    // 确保最后一个节点的nextNodes为空数组
    if (data.nodes.length > 0) {
      data.nodes[data.nodes.length - 1].nextNodes = [];
    }
  }
}

// 新增审批流程
export function addWorkflow(data) {
  // 验证数据完整性
  validateWorkflowData(data);
  
  return request({
    url: '/approval/workflow',
    method: 'post',
    data: data
  })
}

// 修改审批流程
export function updateWorkflow(data) {
  // 验证数据完整性
  validateWorkflowData(data);
  
  return request({
    url: '/approval/workflow',
    method: 'put',
    data: data
  })
}

// 删除审批流程
export function delWorkflow(workflowId) {
  // 确保workflowId是字符串类型，避免大整数精度问题
  const idStr = String(workflowId);
  return request({
    url: '/approval/workflow/' + idStr,
    method: 'delete'
  })
}

// 导出审批流程
export function exportWorkflow(query) {
  return request({
    url: '/approval/workflow/export',
    method: 'get',
    params: query
  })
}

// 启动审批流程
export function startProcess(data) {
  return request({
    url: '/approval/process/start',
    method: 'post',
    data: data
  })
}

// 审批操作
export function approve(data) {
  // 确保instanceId是字符串类型
  if (data && data.instanceId) {
    data.instanceId = String(data.instanceId);
  } else {
    console.error('审批参数错误: instanceId不能为空');
    return Promise.reject(new Error('审批参数错误: instanceId不能为空'));
  }
  
  console.log('提交审批参数:', data);
  
  return request({
    url: '/approval/process/approve',
    method: 'post',
    data: data
  })
}

// 撤销审批
export function cancelApproval(data) {
  return request({
    url: '/approval/process/cancel',
    method: 'post',
    data: data
  })
}

// 撤销申请（根据实例ID）
export function withdrawApproval(instanceId, opinion = '用户撤销申请') {
  return request({
    url: '/approval/process/cancel',
    method: 'post',
    data: {
      instanceId: String(instanceId),
      opinion: opinion
    }
  })
}

// 撤销申请（根据业务ID）
export function withdrawApprovalByBusiness(businessId, businessType, opinion = '用户撤销申请') {
  return request({
    url: '/approval/process/cancel/business',
    method: 'post',
    data: {
      businessId: String(businessId),
      businessType: businessType,
      opinion: opinion
    }
  })
}

// 转交审批
export function transferApproval(data) {
  return request({
    url: '/approval/process/transfer',
    method: 'post',
    data: data
  })
}

// 催办审批
export function urgeApproval(data) {
  return request({
    url: '/approval/process/urge',
    method: 'post',
    data: data
  })
}

// 查询待我审批列表
export function listTodoApproval(query) {
  return request({
    url: '/approval/list/todo',
    method: 'get',
    params: query
  })
}

// 查询我已审批列表
export function listDoneApproval(query) {
  return request({
    url: '/approval/process/done',
    method: 'get',
    params: query
  })
}

// 查询我发起的审批列表
export function listMyApproval(query) {
  return request({
    url: '/approval/process/my',
    method: 'get',
    params: query
  })
}

// 获取审批详情
export function getApprovalDetail(params) {
  // 支持两种方式：通过instanceId或通过businessId+businessType
  if (params.instanceId) {
    // 通过实例ID获取详情
    const instanceId = params.instanceId;
    if (!instanceId || instanceId === 'undefined' || instanceId === undefined) {
      console.error('获取审批详情失败: instanceId不能为空或undefined');
      return Promise.reject(new Error('审批实例ID不能为空或undefined'));
    }

    const idStr = String(instanceId);
    return request({
      url: '/approval/process/detail/' + idStr,
      method: 'get'
    })
  } else if (params.businessId && params.businessType) {
    // 通过业务ID和业务类型获取详情
    return request({
      url: '/approval/process/detail/business',
      method: 'get',
      params: {
        businessId: params.businessId,
        businessType: params.businessType
      }
    })
  } else {
    console.error('获取审批详情失败: 必须提供instanceId或businessId+businessType');
    return Promise.reject(new Error('参数错误：必须提供instanceId或businessId+businessType'));
  }
}

// 获取审批历史
export function getApprovalHistory(query) {
  return request({
    url: '/approval/process/history',
    method: 'get',
    params: query
  })
}

// 获取最新的审批实例
export function getLatestInstance(query) {
  return request({
    url: '/approval/process/latest',
    method: 'get',
    params: query
  })
}

// 获取流程类型选项
export function getWorkflowOptions(query) {
  return request({
    url: '/approval/workflow/options',
    method: 'get',
    params: query || {}  // Ensure params is always an object, even if query is undefined
  })
}

// 获取业务类型字典
export function getBusinessTypes() {
  return request({
    url: '/system/dict/data/type/workflow_business_type',
    method: 'get'
  })
}

// 重新提交审批
export function reapplyApproval(data) {
  return request({
    url: '/approval/process/reapply',
    method: 'post',
    data: data
  })
}

// 获取流程图数据
export function getWorkflowDiagram(workflowId) {
  // 确保workflowId是字符串类型
  const idStr = String(workflowId);
  return request({
    url: '/approval/workflow/diagram/' + idStr,
    method: 'get'
  })
}

// 获取实例流程图数据（显示当前节点状态）
export function getInstanceDiagram(instanceId) {
  // 确保instanceId是字符串类型
  const idStr = String(instanceId);
  return request({
    url: '/approval/process/diagram/' + idStr,
    method: 'get'
  })
}

// 更新业务状态
export function updateBusinessStatus(data) {
  return request({
    url: '/approval/process/update-status',
    method: 'post',
    data: data
  })
}

// 统一调用审批流程（供其他模块调用）
export function invokeApproval(data) {
  return request({
    url: '/approval/process/invoke',
    method: 'post',
    data: data
  })
}

// 获取审批通知
export function getApprovalNotifications(query) {
  return request({
    url: '/approval/notification/list',
    method: 'get',
    params: query
  })
}

// 标记通知为已读
export function markNotificationRead(notificationId) {
  // 确保notificationId是字符串类型
  const idStr = String(notificationId);
  return request({
    url: '/approval/notification/read/' + idStr,
    method: 'put'
  })
}

// 获取特定业务类型的流程模板列表
export function getWorkflowsByBusinessType(businessType) {
  return request({
    url: '/approval/workflow/byBusinessType',
    method: 'get',
    params: { businessType }
  })
}

// 获取可用的审批工作流
export function getAvailableWorkflows(businessType) {
  return request({
    url: '/approval/workflow/available',
    method: 'get',
    params: {
      businessType: businessType
    }
  })
} 