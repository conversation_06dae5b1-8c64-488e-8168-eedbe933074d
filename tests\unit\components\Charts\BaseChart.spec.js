import { shallowMount, createLocalVue } from '@vue/test-utils'
import BaseChart from '@/components/Charts/BaseChart.vue'
import * as echarts from 'echarts'

// 模拟echarts
jest.mock('echarts', () => ({
  init: jest.fn(() => ({
    setOption: jest.fn(),
    dispose: jest.fn(),
    resize: jest.fn(),
    on: jest.fn(),
    showLoading: jest.fn(),
    hideLoading: jest.fn(),
    getDataURL: jest.fn(() => 'data:image/png;base64,test'),
    clear: jest.fn()
  }))
}))

const localVue = createLocalVue()

describe('BaseChart.vue', () => {
  let wrapper
  let mockChart

  beforeEach(() => {
    mockChart = {
      setOption: jest.fn(),
      dispose: jest.fn(),
      resize: jest.fn(),
      on: jest.fn(),
      showLoading: jest.fn(),
      hideLoading: jest.fn(),
      getDataURL: jest.fn(() => 'data:image/png;base64,test'),
      clear: jest.fn()
    }
    
    echarts.init.mockReturnValue(mockChart)
    
    wrapper = shallowMount(BaseChart, {
      localVue,
      propsData: {
        option: {
          title: { text: 'Test Chart' },
          series: [{ type: 'line', data: [1, 2, 3] }]
        }
      }
    })
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.destroy()
    }
    jest.clearAllMocks()
  })

  describe('组件初始化', () => {
    it('应该正确渲染', () => {
      expect(wrapper.exists()).toBe(true)
      expect(wrapper.find('.base-chart').exists()).toBe(true)
    })

    it('应该接收正确的props', () => {
      expect(wrapper.props('option')).toEqual({
        title: { text: 'Test Chart' },
        series: [{ type: 'line', data: [1, 2, 3] }]
      })
      expect(wrapper.props('width')).toBe('100%')
      expect(wrapper.props('height')).toBe('400px')
    })

    it('应该初始化ECharts实例', async () => {
      await wrapper.vm.$nextTick()
      expect(echarts.init).toHaveBeenCalled()
      expect(wrapper.vm.chart).toBe(mockChart)
    })
  })

  describe('图表配置', () => {
    it('应该设置图表选项', async () => {
      await wrapper.vm.$nextTick()
      expect(mockChart.setOption).toHaveBeenCalled()
    })

    it('应该合并默认配置', () => {
      const option = { series: [{ type: 'bar', data: [1, 2, 3] }] }
      const mergedOption = wrapper.vm.mergeDefaultOption(option)
      
      expect(mergedOption).toHaveProperty('animation')
      expect(mergedOption).toHaveProperty('tooltip')
      expect(mergedOption).toHaveProperty('grid')
      expect(mergedOption.series).toEqual(option.series)
    })

    it('应该深度合并对象', () => {
      const target = { a: { b: 1 }, c: 2 }
      const source = { a: { d: 3 }, e: 4 }
      const result = wrapper.vm.deepMerge(target, source)
      
      expect(result).toEqual({
        a: { b: 1, d: 3 },
        c: 2,
        e: 4
      })
    })
  })

  describe('响应式功能', () => {
    it('应该监听窗口大小变化', () => {
      const resizeSpy = jest.spyOn(wrapper.vm, 'resize')
      wrapper.setProps({ autoResize: true })
      
      // 模拟窗口大小变化
      window.dispatchEvent(new Event('resize'))
      
      // 由于使用了debounce，需要等待
      setTimeout(() => {
        expect(resizeSpy).toHaveBeenCalled()
      }, 150)
    })

    it('应该手动调整图表大小', () => {
      wrapper.vm.resize()
      expect(mockChart.resize).toHaveBeenCalled()
    })
  })

  describe('加载状态', () => {
    it('应该显示加载动画', async () => {
      wrapper.setProps({ loading: true })
      await wrapper.vm.$nextTick()
      expect(mockChart.showLoading).toHaveBeenCalled()
    })

    it('应该隐藏加载动画', async () => {
      wrapper.setProps({ loading: false })
      await wrapper.vm.$nextTick()
      expect(mockChart.hideLoading).toHaveBeenCalled()
    })
  })

  describe('事件处理', () => {
    it('应该绑定点击事件', async () => {
      wrapper.setProps({ clickable: true })
      await wrapper.vm.$nextTick()
      expect(mockChart.on).toHaveBeenCalledWith('click', wrapper.vm.handleChartClick)
    })

    it('应该触发图表点击事件', () => {
      const params = { dataIndex: 0, value: 10 }
      wrapper.vm.handleChartClick(params)
      expect(wrapper.emitted('chart-click')).toBeTruthy()
      expect(wrapper.emitted('chart-click')[0]).toEqual([params])
    })

    it('应该触发图例变化事件', () => {
      const params = { selected: { series1: true } }
      wrapper.vm.handleLegendChange(params)
      expect(wrapper.emitted('legend-change')).toBeTruthy()
      expect(wrapper.emitted('legend-change')[0]).toEqual([params])
    })
  })

  describe('工具方法', () => {
    it('应该获取图表实例', () => {
      expect(wrapper.vm.getChart()).toBe(mockChart)
    })

    it('应该获取数据URL', () => {
      const dataURL = wrapper.vm.getDataURL()
      expect(dataURL).toBe('data:image/png;base64,test')
      expect(mockChart.getDataURL).toHaveBeenCalled()
    })

    it('应该下载图表', () => {
      // 模拟创建下载链接
      const createElementSpy = jest.spyOn(document, 'createElement')
      const mockLink = {
        download: '',
        href: '',
        click: jest.fn()
      }
      createElementSpy.mockReturnValue(mockLink)

      wrapper.vm.downloadChart('test-chart')
      
      expect(mockLink.download).toBe('test-chart.png')
      expect(mockLink.href).toBe('data:image/png;base64,test')
      expect(mockLink.click).toHaveBeenCalled()
      
      createElementSpy.mockRestore()
    })

    it('应该清空图表', () => {
      wrapper.vm.clear()
      expect(mockChart.clear).toHaveBeenCalled()
    })

    it('应该刷新图表', () => {
      wrapper.vm.refresh()
      expect(mockChart.resize).toHaveBeenCalled()
      expect(mockChart.setOption).toHaveBeenCalled()
    })
  })

  describe('生命周期', () => {
    it('应该在销毁时清理资源', () => {
      wrapper.destroy()
      expect(mockChart.dispose).toHaveBeenCalled()
    })
  })

  describe('选项变化', () => {
    it('应该响应选项变化', async () => {
      const newOption = {
        title: { text: 'New Chart' },
        series: [{ type: 'bar', data: [4, 5, 6] }]
      }
      
      wrapper.setProps({ option: newOption })
      await wrapper.vm.$nextTick()
      
      expect(mockChart.setOption).toHaveBeenCalledTimes(2) // 初始化 + 更新
    })
  })

  describe('主题切换', () => {
    it('应该设置图表主题', async () => {
      wrapper.vm.setTheme('dark')
      await wrapper.vm.$nextTick()
      
      expect(mockChart.dispose).toHaveBeenCalled()
      expect(echarts.init).toHaveBeenCalledTimes(2) // 初始化 + 主题切换
    })
  })

  describe('错误处理', () => {
    it('应该处理图表初始化失败', () => {
      echarts.init.mockImplementation(() => {
        throw new Error('Init failed')
      })
      
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
      
      expect(() => {
        wrapper.vm.initChart()
      }).not.toThrow()
      
      consoleSpy.mockRestore()
    })

    it('应该处理空的图表容器', () => {
      wrapper.vm.$refs.chartContainer = null
      
      expect(() => {
        wrapper.vm.initChart()
      }).not.toThrow()
    })
  })
})
