<template>
  <div class="app-container">
    <!-- 快速查询区域 - 增加模块选择下拉框 -->
    <el-card class="search-card" shadow="hover">
      <div slot="header" class="clearfix">
        <span><i class="el-icon-search"></i> 物料快速查询</span>
      </div>
      
      <el-form :model="queryParams" ref="searchForm" size="small" :inline="true" label-width="100px">
        <!-- 添加查询模块选择 -->
        <el-form-item label="查询模块" prop="queryModule">
          <el-select v-model="queryParams.queryModule" placeholder="请选择查询模块" style="width: 180px" @change="handleModuleChange">
            <el-option label="物料信息" value="material" />
            <el-option label="物料清单" value="list" />
            <el-option label="物料库存" value="stock" />
            <el-option label="重量信息" value="weight" />
            <el-option label="验收审批" value="approval" />
          </el-select>
        </el-form-item>
        
        <!-- 保留核心查询字段 - 通用字段 -->
        <el-form-item label="物料编码" prop="materialCode">
          <el-input
            v-model="queryParams.materialCode"
            placeholder="请输入内部物料编码"
            clearable
            prefix-icon="el-icon-document"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="物料名称" prop="materialName">
          <el-input
            v-model="queryParams.materialName"
            placeholder="请输入物料名称"
            clearable
            prefix-icon="el-icon-goods"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        
        <!-- 物料信息特有字段 -->
        <template v-if="queryParams.queryModule === 'material'">
          <el-form-item label="外部系统编码" prop="externalCode">
            <el-input
              v-model="queryParams.externalCode"
              placeholder="请输入外部系统编码"
              clearable
              prefix-icon="el-icon-link"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="外部系统标识" prop="externalSystem">
            <el-input
              v-model="queryParams.externalSystem"
              placeholder="请输入外部系统标识"
              clearable
              prefix-icon="el-icon-coin"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
        </template>
        
        <!-- 物料库存特有字段 -->
        <template v-if="queryParams.queryModule === 'stock'">
          <el-form-item label="仓库名称" prop="warehouseName">
            <el-input
              v-model="queryParams.warehouseName"
              placeholder="请输入仓库名称"
              clearable
              prefix-icon="el-icon-office-building"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="批次号" prop="batchNo">
            <el-input
              v-model="queryParams.batchNo"
              placeholder="请输入批次号"
              clearable
              prefix-icon="el-icon-tickets"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
        </template>
        
        <!-- 物料清单特有字段 -->
        <template v-if="queryParams.queryModule === 'list'">
          <el-form-item label="清单编号" prop="billCode">
            <el-input
              v-model="queryParams.billCode"
              placeholder="请输入清单编号"
              clearable
              prefix-icon="el-icon-tickets"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
        </template>
        
        <!-- 重量信息特有字段 -->
        <template v-if="queryParams.queryModule === 'weight'">
          <el-form-item label="重量范围" prop="weightRange">
            <el-input-number v-model="queryParams.minWeight" placeholder="最小值" size="small" style="width: 100px" controls-position="right"></el-input-number>
            <span class="el-range-separator">-</span>
            <el-input-number v-model="queryParams.maxWeight" placeholder="最大值" size="small" style="width: 100px" controls-position="right"></el-input-number>
          </el-form-item>
        </template>
        
        <!-- 验收审批特有字段 -->
        <template v-if="queryParams.queryModule === 'approval'">
          <el-form-item label="审批状态" prop="approvalStatus">
            <el-select v-model="queryParams.approvalStatus" placeholder="请选择审批状态" clearable style="width: 180px">
              <el-option label="待审批" value="0" />
              <el-option label="已通过" value="1" />
              <el-option label="已驳回" value="2" />
            </el-select>
          </el-form-item>
        </template>
        
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格区域 -->
    <el-card shadow="hover" v-loading="loading">
      <!-- 物料信息表格 -->
      <el-table
        v-if="queryParams.queryModule === 'material'"
        :data="tableData"
        border
        stripe
        highlight-current-row
        max-height="500"
        style="width: 100%">
        <el-table-column type="index" label="序号" width="55" align="center" />
        <el-table-column label="物料编码" prop="materialCode" width="120" align="center" :show-overflow-tooltip="true" />
        <el-table-column label="物料名称" prop="materialName" width="150" align="center" :show-overflow-tooltip="true" />
        <el-table-column label="规格型号" prop="specification" width="120" align="center" :show-overflow-tooltip="true" />
        <el-table-column label="外部系统编码" prop="externalCode" width="120" align="center" :show-overflow-tooltip="true" />
        <el-table-column label="外部系统标识" prop="externalSystem" width="120" align="center" :show-overflow-tooltip="true" />
        <el-table-column label="计量单位" prop="unit" width="100" align="center" />
        <el-table-column label="状态" align="center" prop="status" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === '0' ? 'success' : scope.row.status === '1' ? 'warning' : 'danger'">
              {{ scope.row.status === '0' ? '正常' : scope.row.status === '1' ? '锁定' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="100">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
            >查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 物料清单表格 -->
      <el-table
        v-else-if="queryParams.queryModule === 'list'"
        :data="tableData"
        border
        stripe
        highlight-current-row
        max-height="500"
        style="width: 100%">
        <el-table-column type="index" label="序号" width="55" align="center" />
        <el-table-column label="清单编号" prop="billCode" width="120" align="center" :show-overflow-tooltip="true" />
        <el-table-column label="清单名称" prop="billName" width="150" align="center" :show-overflow-tooltip="true" />
        <el-table-column label="物料数量" prop="itemCount" width="100" align="center" />
        <el-table-column label="创建时间" prop="createTime" width="150" align="center" />
        <el-table-column label="状态" align="center" prop="status" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === '0' ? 'info' : scope.row.status === '1' ? 'success' : 'danger'">
              {{ scope.row.status === '0' ? '草稿' : scope.row.status === '1' ? '已审批' : '已拒绝' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="100">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
            >查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 物料库存表格 -->
      <el-table
        v-else-if="queryParams.queryModule === 'stock'"
        :data="tableData"
        border
        stripe
        highlight-current-row
        max-height="500"
        style="width: 100%">
        <el-table-column type="index" label="序号" width="55" align="center" />
        <el-table-column label="物料编码" prop="materialCode" width="120" align="center" :show-overflow-tooltip="true" />
        <el-table-column label="物料名称" prop="materialName" width="150" align="center" :show-overflow-tooltip="true" />
        <el-table-column label="批次号" prop="batchNo" width="120" align="center" :show-overflow-tooltip="true" />
        <el-table-column label="仓库名称" prop="warehouseName" width="120" align="center" :show-overflow-tooltip="true" />
        <el-table-column label="库位名称" prop="locationName" width="120" align="center" :show-overflow-tooltip="true" />
        <el-table-column label="数量" prop="quantity" width="80" align="center" />
        <el-table-column label="状态" align="center" prop="status" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === '0' ? 'success' : scope.row.status === '1' ? 'warning' : 'danger'">
              {{ scope.row.status === '0' ? '正常' : scope.row.status === '1' ? '锁定' : '过期' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="100">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
            >查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 重量信息表格 -->
      <el-table
        v-else-if="queryParams.queryModule === 'weight'"
        :data="tableData"
        border
        stripe
        highlight-current-row
        max-height="500"
        style="width: 100%">
        <el-table-column type="index" label="序号" width="55" align="center" />
        <el-table-column label="物料编码" prop="materialCode" width="120" align="center" :show-overflow-tooltip="true" />
        <el-table-column label="物料名称" prop="materialName" width="150" align="center" :show-overflow-tooltip="true" />
        <el-table-column label="标准重量" prop="standardWeight" width="100" align="center" />
        <el-table-column label="实际重量" prop="actualWeight" width="100" align="center" />
        <el-table-column label="偏差值" prop="weightDeviation" width="100" align="center" />
        <el-table-column label="偏差百分比" prop="weightDeviationPercent" width="100" align="center">
          <template slot-scope="scope">
            {{ scope.row.weightDeviationPercent }}%
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="100">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
            >查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 验收审批表格 -->
      <el-table
        v-else-if="queryParams.queryModule === 'approval'"
        :data="tableData"
        border
        stripe
        highlight-current-row
        max-height="500"
        style="width: 100%">
        <el-table-column type="index" label="序号" width="55" align="center" />
        <el-table-column label="物料编码" prop="materialCode" width="120" align="center" :show-overflow-tooltip="true" />
        <el-table-column label="物料名称" prop="materialName" width="150" align="center" :show-overflow-tooltip="true" />
        <el-table-column label="申请人" prop="applyUser" width="100" align="center" />
        <el-table-column label="申请时间" prop="applyTime" width="150" align="center" />
        <el-table-column label="审批人" prop="approver" width="100" align="center" />
        <el-table-column label="审批状态" align="center" prop="approvalStatus" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.approvalStatus === '0' ? 'info' : scope.row.approvalStatus === '1' ? 'success' : 'danger'">
              {{ scope.row.approvalStatus === '0' ? '待审批' : scope.row.approvalStatus === '1' ? '已通过' : '已驳回' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="100">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
            >查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 查看详情对话框 -->
    <el-dialog :title="getDetailTitle" :visible.sync="viewDialogVisible" width="700px" append-to-body>
      <!-- 物料信息详情 -->
      <el-descriptions v-if="queryParams.queryModule === 'material'" :column="2" border>
        <el-descriptions-item label="物料编码">{{ form.materialCode }}</el-descriptions-item>
        <el-descriptions-item label="物料名称">{{ form.materialName }}</el-descriptions-item>
        <el-descriptions-item label="规格型号">{{ form.specification }}</el-descriptions-item>
        <el-descriptions-item label="计量单位">{{ form.unit }}</el-descriptions-item>
        <el-descriptions-item label="外部系统编码">{{ form.externalCode }}</el-descriptions-item>
        <el-descriptions-item label="外部系统标识">{{ form.externalSystem }}</el-descriptions-item>
        <el-descriptions-item label="备注">{{ form.remark }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="form.status === '0' ? 'success' : form.status === '1' ? 'warning' : 'danger'">
            {{ form.status === '0' ? '正常' : form.status === '1' ? '锁定' : '停用' }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
      
      <!-- 物料清单详情 -->
      <template v-else-if="queryParams.queryModule === 'list'">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="清单编号">{{ form.billCode }}</el-descriptions-item>
          <el-descriptions-item label="清单名称">{{ form.billName }}</el-descriptions-item>
          <el-descriptions-item label="物料数量">{{ form.itemCount }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ form.createTime }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="form.status === '0' ? 'info' : form.status === '1' ? 'success' : 'danger'">
              {{ form.status === '0' ? '草稿' : form.status === '1' ? '已审批' : '已拒绝' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="备注">{{ form.remark }}</el-descriptions-item>
        </el-descriptions>
        <div class="mt10">物料清单明细：</div>
        <el-table :data="form.items || []" border stripe style="width: 100%; margin-top: 10px">
          <el-table-column label="物料编码" prop="materialCode" width="120" align="center" />
          <el-table-column label="物料名称" prop="materialName" width="150" align="center" />
          <el-table-column label="数量" prop="quantity" width="80" align="center" />
        </el-table>
      </template>
      
      <!-- 物料库存详情 -->
      <el-descriptions v-else-if="queryParams.queryModule === 'stock'" :column="2" border>
        <el-descriptions-item label="物料编码">{{ form.materialCode }}</el-descriptions-item>
        <el-descriptions-item label="物料名称">{{ form.materialName }}</el-descriptions-item>
        <el-descriptions-item label="仓库名称">{{ form.warehouseName }}</el-descriptions-item>
        <el-descriptions-item label="库位名称">{{ form.locationName }}</el-descriptions-item>
        <el-descriptions-item label="批次号">{{ form.batchNo }}</el-descriptions-item>
        <el-descriptions-item label="数量">{{ form.quantity }}</el-descriptions-item>
        <el-descriptions-item label="生产日期">{{ parseTime(form.productionDate, '{y}-{m}-{d}') }}</el-descriptions-item>
        <el-descriptions-item label="过期日期">{{ parseTime(form.expiryDate, '{y}-{m}-{d}') }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="form.status === '0' ? 'success' : form.status === '1' ? 'warning' : 'danger'">
            {{ form.status === '0' ? '正常' : form.status === '1' ? '锁定' : '过期' }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
      
      <!-- 重量信息详情 -->
      <el-descriptions v-else-if="queryParams.queryModule === 'weight'" :column="2" border>
        <el-descriptions-item label="物料编码">{{ form.materialCode }}</el-descriptions-item>
        <el-descriptions-item label="物料名称">{{ form.materialName }}</el-descriptions-item>
        <el-descriptions-item label="标准重量">{{ form.standardWeight }}</el-descriptions-item>
        <el-descriptions-item label="实际重量">{{ form.actualWeight }}</el-descriptions-item>
        <el-descriptions-item label="偏差值">{{ form.weightDeviation }}</el-descriptions-item>
        <el-descriptions-item label="偏差百分比">{{ form.weightDeviationPercent }}%</el-descriptions-item>
        <el-descriptions-item label="计量单位">{{ form.unit }}</el-descriptions-item>
        <el-descriptions-item label="测量时间">{{ parseTime(form.measureTime, '{y}-{m}-{d}') }}</el-descriptions-item>
      </el-descriptions>
      
      <!-- 审批详情 -->
      <el-descriptions v-else-if="queryParams.queryModule === 'approval'" :column="2" border>
        <el-descriptions-item label="物料编码">{{ form.materialCode }}</el-descriptions-item>
        <el-descriptions-item label="物料名称">{{ form.materialName }}</el-descriptions-item>
        <el-descriptions-item label="申请人">{{ form.applyUser }}</el-descriptions-item>
        <el-descriptions-item label="申请时间">{{ form.applyTime }}</el-descriptions-item>
        <el-descriptions-item label="审批人">{{ form.approver }}</el-descriptions-item>
        <el-descriptions-item label="审批时间">{{ form.approvalTime }}</el-descriptions-item>
        <el-descriptions-item label="审批状态">
          <el-tag :type="form.approvalStatus === '0' ? 'info' : form.approvalStatus === '1' ? 'success' : 'danger'">
            {{ form.approvalStatus === '0' ? '待审批' : form.approvalStatus === '1' ? '已通过' : '已驳回' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="审批意见">{{ form.approvalRemark }}</el-descriptions-item>
      </el-descriptions>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  listQuery, 
  getQuery, 
  listMaterialInfo, 
  // listMaterialBill, // 物料清单功能已删除
  listMaterialStock, 
  listMaterialWeight, 
  listMaterialApproval, 
  getMaterialApproval 
} from "@/api/material/query"

export default {
  name: "Query",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 查看详情对话框是否可见
      viewDialogVisible: false,
      // 总条数
      total: 0,
      // 表格数据
      tableData: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        queryModule: "material",  // 默认为物料信息模块
        materialCode: null,
        materialName: null,
        externalCode: null,
        externalSystem: null,
        warehouseName: null,
        billCode: null,         // 物料清单编号
        minWeight: null,        // 最小重量
        maxWeight: null,        // 最大重量
        approvalStatus: null,   // 审批状态
        batchNo: null           // 批次号
      },
      // 表单参数
      form: {}
    }
  },
  computed: {
    // 根据不同的模块显示不同的详情标题
    getDetailTitle() {
      const moduleMap = {
        'material': '物料信息详情',
        'list': '物料清单详情',
        'stock': '物料库存详情',
        'weight': '物料重量信息详情',
        'approval': '物料验收审批详情'
      };
      return moduleMap[this.queryParams.queryModule] || '物料详情';
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 处理模块变更 */
    handleModuleChange(val) {
      // 切换模块时重置分页
      this.queryParams.pageNum = 1;
      
      // 根据模块清空特有的查询字段
      this.resetQueryByModule(val);
      
      // 重新加载数据
      this.getList();
    },
    
    /** 根据模块重置特定查询字段 */
    resetQueryByModule(module) {
      // 默认清空所有特定模块的查询字段
      this.queryParams.externalCode = null;
      this.queryParams.externalSystem = null;
      this.queryParams.warehouseName = null;
      this.queryParams.billCode = null;
      this.queryParams.minWeight = null;
      this.queryParams.maxWeight = null;
      this.queryParams.approvalStatus = null;
      this.queryParams.batchNo = null;
      
      // 保留通用字段：materialCode和materialName
    },
    
    /** 查询列表数据 */
    getList() {
      this.loading = true;
      
      // 根据不同模块调用不同的API
      let apiFunction;
      switch(this.queryParams.queryModule) {
        case 'material':
          apiFunction = listMaterialInfo;
          break;
        case 'list':
          // 物料清单功能已删除，使用默认查询
          apiFunction = listQuery;
          break;
        case 'stock':
          apiFunction = listMaterialStock;
          break;
        case 'weight':
          apiFunction = listMaterialWeight;
          break;
        case 'approval':
          apiFunction = listMaterialApproval;
          break;
        default:
          apiFunction = listQuery;
      }
      
      // 调用API获取数据
      apiFunction(this.queryParams).then(response => {
        this.tableData = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        console.error("获取数据失败:", error);
        this.loading = false;
        this.$message.error("获取数据失败，请稍后重试");
      });
    },
    
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    
    /** 重置全部搜索条件按钮操作 */
    resetQuery() {
      this.resetForm("searchForm");
      // 保持当前选择的模块
      const currentModule = this.queryParams.queryModule;
      this.queryParams.queryModule = currentModule;
      // 重置当前模块的特定字段
      this.resetQueryByModule(currentModule);
      this.handleQuery();
    },
    
    /** 查看按钮操作 */
    handleView(row) {
      const id = row.id;
      
      // 根据不同模块调用不同的获取详情API
      let apiFunction;
      switch(this.queryParams.queryModule) {
        case 'approval':
          apiFunction = getMaterialApproval;
          break;
        default:
          apiFunction = getQuery;
      }
      
      apiFunction(id).then(response => {
        this.form = response.data;
        this.viewDialogVisible = true;
      }).catch(error => {
        console.error("获取详情失败:", error);
        this.$message.error("获取详情失败，请稍后重试");
      });
    }
  }
}
</script>

<style scoped>
.search-card {
  margin-bottom: 15px;
}
.el-range-separator {
  padding: 0 5px;
}
.mt10 {
  margin-top: 10px;
}
</style>
