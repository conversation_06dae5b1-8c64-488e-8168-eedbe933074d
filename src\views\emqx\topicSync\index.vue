<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>EMQX主题同步管理</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="refreshOverview">刷新</el-button>
      </div>

      <!-- 概览信息 -->
      <el-row :gutter="20" class="overview-section">
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="overview-item">
              <div class="overview-number">{{ overview.configuredCount || 0 }}</div>
              <div class="overview-label">配置主题数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="overview-item">
              <div class="overview-number">{{ overview.actualCount || 0 }}</div>
              <div class="overview-label">EMQX主题数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="overview-item">
              <div class="overview-number">{{ overview.matchingCount || 0 }}</div>
              <div class="overview-label">匹配主题数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="overview-item">
              <div class="overview-number">{{ (overview.syncRate * 100).toFixed(1) || 0 }}%</div>
              <div class="overview-label">同步率</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 操作按钮 -->
      <el-row class="action-section">
        <el-col :span="24">
          <el-button type="primary" @click="oneClickFix" :loading="loading.oneClickFix">
            <i class="el-icon-magic-stick"></i> 一键修复
          </el-button>
          <el-button type="success" @click="syncTopics" :loading="loading.sync">
            <i class="el-icon-refresh"></i> 同步主题
          </el-button>
          <el-button type="info" @click="subscribeSystem" :loading="loading.subscribe">
            <i class="el-icon-connection"></i> 系统订阅
          </el-button>
          <el-button type="warning" @click="compareTopics" :loading="loading.compare">
            <i class="el-icon-s-data"></i> 比较主题
          </el-button>
          <el-button @click="getActualStatus" :loading="loading.status">
            <i class="el-icon-view"></i> 查看状态
          </el-button>
          <el-button type="primary" plain @click="showClientSelector">
            <i class="el-icon-user"></i> 选择客户端
          </el-button>
          <el-button v-if="selectedClient" type="success" plain @click="batchSubscribeToClient">
            <i class="el-icon-connection"></i> 批量订阅到 {{ selectedClient.clientId }}
          </el-button>
        </el-col>
      </el-row>

      <!-- 缺失主题列表 -->
      <el-card v-if="missingTopics.length > 0" class="missing-topics-card">
        <div slot="header" class="clearfix">
          <span>缺失的主题 ({{ missingTopics.length }})</span>
          <el-button style="float: right;" size="small" type="primary" @click="fixMissingTopics">
            修复缺失主题
          </el-button>
        </div>
        <el-tag v-for="topic in missingTopics" :key="topic" type="danger" class="topic-tag">
          {{ topic }}
        </el-tag>
      </el-card>

      <!-- 主题状态表格 -->
      <el-table :data="topicStatusList" v-loading="loading.table" class="topic-table">
        <el-table-column prop="topic" label="主题名称" min-width="200">
          <template slot-scope="scope">
            <el-tag :type="getTopicTagType(scope.row)">{{ scope.row.topic }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="active" label="状态" width="100">
          <template slot-scope="scope">
            <el-badge :value="scope.row.subscriberCount || 0" class="item">
              <el-tag :type="scope.row.active ? 'success' : 'danger'">
                {{ scope.row.active ? '活跃' : '未激活' }}
              </el-tag>
            </el-badge>
          </template>
        </el-table-column>
        <el-table-column prop="subscriberCount" label="订阅者数量" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.subscriberCount || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="node" label="节点" width="150">
          <template slot-scope="scope">
            <el-tag size="small">{{ scope.row.node || 'N/A' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template slot-scope="scope">
            <el-button size="mini" @click="publishTestMessage(scope.row.topic)">
              发布测试
            </el-button>
            <el-button size="mini" type="info" @click="viewTopicDetails(scope.row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 操作结果对话框 -->
    <el-dialog :title="resultDialog.title" :visible.sync="resultDialog.visible" width="60%">
      <pre class="result-content">{{ resultDialog.content }}</pre>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resultDialog.visible = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 主题详情对话框 -->
    <el-dialog title="主题详情" :visible.sync="detailDialog.visible" width="50%">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="主题名称">{{ detailDialog.topic.topic }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="detailDialog.topic.active ? 'success' : 'danger'">
            {{ detailDialog.topic.active ? '活跃' : '未激活' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="订阅者数量">{{ detailDialog.topic.subscriberCount || 0 }}</el-descriptions-item>
        <el-descriptions-item label="节点">{{ detailDialog.topic.node || 'N/A' }}</el-descriptions-item>
      </el-descriptions>
      
      <div v-if="detailDialog.topic.subscribers && detailDialog.topic.subscribers.length > 0" class="subscribers-section">
        <h4>订阅者列表</h4>
        <el-table :data="detailDialog.topic.subscribers" size="small">
          <el-table-column prop="clientid" label="客户端ID" min-width="150"></el-table-column>
          <el-table-column prop="qos" label="QoS" width="80"></el-table-column>
          <el-table-column prop="nl" label="No Local" width="80"></el-table-column>
          <el-table-column prop="rap" label="Retain as Published" width="120"></el-table-column>
        </el-table>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialog.visible = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 客户端选择器 -->
    <ClientSelector
      :visible.sync="clientSelector.visible"
      :topic="clientSelector.topic"
      :device-type="clientSelector.deviceType"
      @client-selected="onClientSelected"
    />
  </div>
</template>

<script>
import {
  syncTopicsToEmqx,
  verifyTopicsInEmqx,
  subscribeSystemTopics,
  createTestSubscriptions,
  publishTestMessage,
  getActualTopicStatus,
  compareTopicsWithEmqx,
  fixTopicSyncIssues,
  oneClickFix,
  getTopicSyncOverview
} from '@/api/emqx/topicSync'
import {
  getAvailableClients,
  subscribeTopicToClient,
  batchSubscribeTopicsToClient,
  getTopicConfigFullStatus
} from '@/api/emqx/topicClientMapping'
import ClientSelector from './components/ClientSelector.vue'

export default {
  name: 'EmqxTopicSync',
  components: {
    ClientSelector
  },
  data() {
    return {
      overview: {
        configuredCount: 0,
        actualCount: 0,
        matchingCount: 0,
        missingCount: 0,
        syncRate: 0
      },
      topicStatusList: [],
      missingTopics: [],
      loading: {
        oneClickFix: false,
        sync: false,
        subscribe: false,
        compare: false,
        status: false,
        table: false
      },
      resultDialog: {
        visible: false,
        title: '',
        content: ''
      },
      detailDialog: {
        visible: false,
        topic: {}
      },
      // 客户端相关
      clientSelector: {
        visible: false,
        topic: '',
        deviceType: ''
      },
      availableClients: [],
      selectedClient: null
    }
  },
  created() {
    this.refreshOverview()
  },
  methods: {
    // 刷新概览
    async refreshOverview() {
      try {
        this.loading.table = true
        const response = await getTopicSyncOverview()
        if (response.code === 200) {
          const data = response.data
          this.overview = data.comparison || {}
          this.topicStatusList = data.actualTopics || []
          this.missingTopics = this.overview.missingInEmqx || []
        }
      } catch (error) {
        this.$message.error('获取概览失败: ' + error.message)
      } finally {
        this.loading.table = false
      }
    },

    // 一键修复
    async oneClickFix() {
      try {
        this.loading.oneClickFix = true
        const response = await oneClickFix()
        if (response.code === 200) {
          this.$message.success('一键修复完成')
          this.showResult('一键修复结果', response.data)
          this.refreshOverview()
        } else {
          this.$message.error('一键修复失败: ' + response.msg)
        }
      } catch (error) {
        this.$message.error('一键修复失败: ' + error.message)
      } finally {
        this.loading.oneClickFix = false
      }
    },

    // 同步主题
    async syncTopics() {
      try {
        this.loading.sync = true
        const response = await syncTopicsToEmqx()
        if (response.code === 200) {
          this.$message.success('主题同步完成')
          this.showResult('同步结果', response.data)
          this.refreshOverview()
        } else {
          this.$message.error('主题同步失败: ' + response.msg)
        }
      } catch (error) {
        this.$message.error('主题同步失败: ' + error.message)
      } finally {
        this.loading.sync = false
      }
    },

    // 系统订阅
    async subscribeSystem() {
      try {
        this.loading.subscribe = true
        const response = await subscribeSystemTopics()
        if (response.code === 200) {
          this.$message.success('系统订阅完成')
          this.showResult('订阅结果', response.data)
          this.refreshOverview()
        } else {
          this.$message.error('系统订阅失败: ' + response.msg)
        }
      } catch (error) {
        this.$message.error('系统订阅失败: ' + error.message)
      } finally {
        this.loading.subscribe = false
      }
    },

    // 比较主题
    async compareTopics() {
      try {
        this.loading.compare = true
        const response = await compareTopicsWithEmqx()
        if (response.code === 200) {
          this.showResult('比较结果', response.data)
          this.overview = response.data
          this.missingTopics = response.data.missingInEmqx || []
        } else {
          this.$message.error('比较失败: ' + response.msg)
        }
      } catch (error) {
        this.$message.error('比较失败: ' + error.message)
      } finally {
        this.loading.compare = false
      }
    },

    // 获取实际状态
    async getActualStatus() {
      try {
        this.loading.status = true
        const response = await getActualTopicStatus()
        if (response.code === 200) {
          this.topicStatusList = response.data
          this.$message.success('状态获取完成')
        } else {
          this.$message.error('获取状态失败: ' + response.msg)
        }
      } catch (error) {
        this.$message.error('获取状态失败: ' + error.message)
      } finally {
        this.loading.status = false
      }
    },

    // 修复缺失主题
    async fixMissingTopics() {
      try {
        const response = await createTestSubscriptions(this.missingTopics)
        if (response.code === 200) {
          this.$message.success('缺失主题修复完成')
          this.showResult('修复结果', response.data)
          this.refreshOverview()
        } else {
          this.$message.error('修复失败: ' + response.msg)
        }
      } catch (error) {
        this.$message.error('修复失败: ' + error.message)
      }
    },

    // 发布测试消息
    async publishTestMessage(topic) {
      try {
        const payload = JSON.stringify({
          type: 'manual_test',
          timestamp: new Date().toISOString(),
          message: '手动测试消息'
        })
        
        const response = await publishTestMessage(topic, payload)
        if (response.code === 200) {
          this.$message.success('测试消息发布成功')
        } else {
          this.$message.error('发布失败: ' + response.msg)
        }
      } catch (error) {
        this.$message.error('发布失败: ' + error.message)
      }
    },

    // 查看主题详情
    viewTopicDetails(topic) {
      this.detailDialog.topic = topic
      this.detailDialog.visible = true
    },

    // 显示结果
    showResult(title, data) {
      this.resultDialog.title = title
      this.resultDialog.content = JSON.stringify(data, null, 2)
      this.resultDialog.visible = true
    },

    // 获取主题标签类型
    getTopicTagType(row) {
      if (row.active) {
        return 'success'
      } else if (row.subscriberCount > 0) {
        return 'warning'
      } else {
        return 'danger'
      }
    },

    // ==================== 客户端管理相关方法 ====================

    // 显示客户端选择器
    showClientSelector() {
      this.clientSelector.visible = true
      this.clientSelector.topic = 'dwms/device/+/data' // 示例主题
      this.clientSelector.deviceType = 'weight' // 示例设备类型
    },

    // 客户端选择回调
    onClientSelected(client) {
      this.selectedClient = client
      this.$message.success(`已选择客户端: ${client.clientId}`)

      // 可以在这里自动加载客户端状态等信息
      this.loadClientStatus(client)
    },

    // 加载客户端状态
    async loadClientStatus(client) {
      try {
        // 这里可以获取客户端的详细状态信息
        console.log('加载客户端状态:', client)
      } catch (error) {
        console.error('加载客户端状态失败:', error)
      }
    },

    // 批量订阅到选中的客户端
    async batchSubscribeToClient() {
      if (!this.selectedClient) {
        this.$message.warning('请先选择一个客户端')
        return
      }

      try {
        this.loading.subscribe = true

        // 获取所有缺失的主题配置ID（这里需要根据实际情况调整）
        const topicConfigIds = this.missingTopics.map((topic, index) => index + 1) // 示例ID

        const response = await batchSubscribeTopicsToClient(topicConfigIds, this.selectedClient.clientId)

        if (response.code === 200) {
          const result = response.data
          this.$message.success(
            `批量订阅完成: 成功 ${result.successCount}/${result.totalTopics} 个主题`
          )
          this.showResult('批量订阅结果', result)
          this.refreshOverview()
        } else {
          this.$message.error('批量订阅失败: ' + response.msg)
        }
      } catch (error) {
        this.$message.error('批量订阅失败: ' + error.message)
      } finally {
        this.loading.subscribe = false
      }
    },

    // 为单个主题订阅到客户端
    async subscribeTopicToSelectedClient(topic, topicConfigId = 1) {
      if (!this.selectedClient) {
        this.$message.warning('请先选择一个客户端')
        return
      }

      try {
        const response = await subscribeTopicToClient(
          topicConfigId,
          this.selectedClient.clientId,
          topic,
          1
        )

        if (response.code === 200 && response.data.success) {
          this.$message.success(`主题 ${topic} 订阅成功`)
          this.refreshOverview()
        } else {
          this.$message.error(`主题 ${topic} 订阅失败: ${response.data.message}`)
        }
      } catch (error) {
        this.$message.error(`订阅失败: ${error.message}`)
      }
    },

    // 获取主题配置的完整状态
    async getTopicFullStatus(topicConfigId) {
      try {
        const response = await getTopicConfigFullStatus(topicConfigId)
        if (response.code === 200) {
          this.showResult('主题完整状态', response.data)
        } else {
          this.$message.error('获取主题状态失败: ' + response.msg)
        }
      } catch (error) {
        this.$message.error('获取主题状态失败: ' + error.message)
      }
    }
  }
}
</script>

<style scoped>
.overview-section {
  margin-bottom: 20px;
}

.overview-card {
  text-align: center;
}

.overview-item {
  padding: 10px;
}

.overview-number {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}

.overview-label {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

.action-section {
  margin-bottom: 20px;
  text-align: center;
}

.missing-topics-card {
  margin-bottom: 20px;
}

.topic-tag {
  margin: 2px;
}

.topic-table {
  margin-top: 20px;
}

.result-content {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  max-height: 400px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.subscribers-section {
  margin-top: 20px;
}

.subscribers-section h4 {
  margin-bottom: 10px;
  color: #333;
}
</style>
