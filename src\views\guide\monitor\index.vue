<template>
  <div class="guide-monitor">
    <!-- 顶部状态卡片 -->
    <el-row :gutter="20" class="monitor-cards">
      <el-col :span="6">
        <el-card shadow="hover" class="status-card">
          <div class="status-header">
            <i class="el-icon-cpu"></i>
            <span>设备总数</span>
          </div>
          <div class="status-value">{{ deviceStats.total }}</div>
          <div class="status-footer">
            <el-tag size="mini" type="success">在线: {{ deviceStats.online }}</el-tag>
            <el-tag size="mini" type="danger">离线: {{ deviceStats.offline }}</el-tag>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="status-card">
          <div class="status-header">
            <i class="el-icon-headset"></i>
            <span>语音设备</span>
          </div>
          <div class="status-value">{{ deviceStats.voice.total }}</div>
          <div class="status-footer">
            <el-tag size="mini" type="success">正常: {{ deviceStats.voice.normal }}</el-tag>
            <el-tag size="mini" type="danger">异常: {{ deviceStats.voice.error }}</el-tag>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="status-card">
          <div class="status-header">
            <i class="el-icon-light-rain"></i>
            <span>灯光设备</span>
          </div>
          <div class="status-value">{{ deviceStats.light.total }}</div>
          <div class="status-footer">
            <el-tag size="mini" type="success">正常: {{ deviceStats.light.normal }}</el-tag>
            <el-tag size="mini" type="danger">异常: {{ deviceStats.light.error }}</el-tag>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="status-card">
          <div class="status-header">
            <i class="el-icon-view"></i>
            <span>AR设备</span>
          </div>
          <div class="status-value">{{ deviceStats.ar.total }}</div>
          <div class="status-footer">
            <el-tag size="mini" type="success">正常: {{ deviceStats.ar.normal }}</el-tag>
            <el-tag size="mini" type="danger">异常: {{ deviceStats.ar.error }}</el-tag>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 仓库地图和设备状态 -->
    <el-row :gutter="20" class="monitor-main">
      <el-col :span="16">
        <el-card shadow="hover" class="warehouse-map">
          <div slot="header" class="clearfix">
            <span>仓库地图视图</span>
            <el-dropdown style="float: right" @command="handleMapCommand">
              <el-button type="text">
                切换仓库 <i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-for="warehouse in warehouses" :key="warehouse.id" :command="warehouse.id">{{ warehouse.name }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <div class="map-container">
            <div class="map-legend">
              <div class="legend-item">
                <span class="legend-icon online"></span>
                <span class="legend-label">在线设备</span>
              </div>
              <div class="legend-item">
                <span class="legend-icon offline"></span>
                <span class="legend-label">离线设备</span>
              </div>
              <div class="legend-item">
                <span class="legend-icon active"></span>
                <span class="legend-label">活动中</span>
              </div>
            </div>
            <div class="map-content" ref="mapContainer">
              <!-- 地图和设备标记将在mounted中通过Canvas绘制 -->
            </div>
            <div class="map-controls">
              <el-button-group>
                <el-button size="mini" icon="el-icon-zoom-in" @click="zoomIn"></el-button>
                <el-button size="mini" icon="el-icon-zoom-out" @click="zoomOut"></el-button>
                <el-button size="mini" icon="el-icon-refresh" @click="resetMap"></el-button>
              </el-button-group>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card shadow="hover" class="device-status">
          <div slot="header" class="clearfix">
            <span>设备实时状态</span>
            <el-button style="float: right" type="text" icon="el-icon-refresh" @click="refreshDeviceStatus">刷新</el-button>
          </div>
          <div class="status-filters">
            <el-radio-group v-model="statusFilter" size="small">
              <el-radio-button label="all">全部</el-radio-button>
              <el-radio-button label="online">在线</el-radio-button>
              <el-radio-button label="offline">离线</el-radio-button>
              <el-radio-button label="active">活动中</el-radio-button>
            </el-radio-group>
          </div>
          <div class="device-list">
            <el-table
              :data="filteredDevices"
              style="width: 100%"
              height="400"
              @row-click="handleDeviceClick"
              :row-class-name="getDeviceRowClass"
            >
              <el-table-column prop="name" label="设备名称" min-width="120"></el-table-column>
              <el-table-column prop="type" label="类型" width="80">
                <template slot-scope="scope">
                  <el-tag size="mini" :type="getDeviceTypeTag(scope.row.type)">{{ scope.row.type }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="80">
                <template slot-scope="scope">
                  <el-tag size="mini" :type="scope.row.status === 'online' ? 'success' : 'danger'">
                    {{ scope.row.status === 'online' ? '在线' : '离线' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center">
                <template slot-scope="scope">
                  <el-button size="mini" type="text" icon="el-icon-more" @click.stop="showDeviceOperations(scope.row)"></el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 活动日志和告警 -->
    <el-row :gutter="20" class="monitor-logs">
      <el-col :span="12">
        <el-card shadow="hover" class="activity-log">
          <div slot="header" class="clearfix">
            <span>实时活动日志</span>
            <div style="float: right">
              <el-switch
                v-model="autoRefresh"
                active-text="自动刷新"
                inactive-text="手动刷新"
                @change="toggleAutoRefresh"
              ></el-switch>
            </div>
          </div>
          <div class="log-content" ref="logContainer">
            <div v-for="(log, index) in activityLogs" :key="index" class="log-item" :class="{ 'log-error': log.type === 'error' }">
              <div class="log-time">{{ formatTime(log.time) }}</div>
              <div class="log-message">{{ log.message }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card shadow="hover" class="alerts">
          <div slot="header" class="clearfix">
            <span>设备告警</span>
            <el-button style="float: right" type="text" @click="acknowledgeAllAlerts">一键确认</el-button>
          </div>
          <div class="alert-list">
            <el-table
              :data="alerts"
              style="width: 100%"
              :row-class-name="getAlertRowClass"
            >
              <el-table-column prop="time" label="时间" width="180">
                <template slot-scope="scope">
                  {{ formatDateTime(scope.row.time) }}
                </template>
              </el-table-column>
              <el-table-column prop="device" label="设备" min-width="120"></el-table-column>
              <el-table-column prop="level" label="等级" width="80">
                <template slot-scope="scope">
                  <el-tag size="mini" :type="getAlertLevelType(scope.row.level)">{{ getAlertLevelText(scope.row.level) }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="message" label="告警内容" min-width="180"></el-table-column>
              <el-table-column label="操作" width="120" align="center">
                <template slot-scope="scope">
                  <el-button 
                    size="mini" 
                    type="text" 
                    icon="el-icon-check" 
                    @click="acknowledgeAlert(scope.row)"
                    :disabled="scope.row.acknowledged"
                  >确认</el-button>
                  <el-button size="mini" type="text" icon="el-icon-view" @click="viewAlertDetail(scope.row)">详情</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 设备操作弹窗 -->
    <el-dialog title="设备操作" :visible.sync="deviceOperationVisible" width="500px" append-to-body>
      <div v-if="selectedDevice" class="device-operation">
        <div class="device-info">
          <h3>{{ selectedDevice.name }}</h3>
          <div class="device-meta">
            <el-tag size="small">{{ selectedDevice.type }}</el-tag>
            <el-tag size="small" :type="selectedDevice.status === 'online' ? 'success' : 'danger'">
              {{ selectedDevice.status === 'online' ? '在线' : '离线' }}
            </el-tag>
          </div>
        </div>
        
        <el-divider></el-divider>
        
        <div class="operation-panel">
          <div v-if="selectedDevice.type === '语音设备'">
            <h4>语音播报控制</h4>
            <el-form label-width="100px">
              <el-form-item label="播报内容">
                <el-input type="textarea" v-model="voiceContent" rows="3" placeholder="请输入要播报的内容"></el-input>
              </el-form-item>
              <el-form-item label="音量">
                <el-slider v-model="voiceVolume" :min="1" :max="10" show-stops></el-slider>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" :disabled="!voiceContent" @click="controlVoiceDevice">发送播报</el-button>
                <el-button type="info" @click="testVoiceDevice">测试设备</el-button>
              </el-form-item>
            </el-form>
          </div>
          
          <div v-if="selectedDevice.type === '灯光设备'">
            <h4>灯光控制</h4>
            <el-form label-width="100px">
              <el-form-item label="灯光颜色">
                <el-color-picker v-model="lightColor"></el-color-picker>
              </el-form-item>
              <el-form-item label="亮度">
                <el-slider v-model="lightBrightness" :min="1" :max="100" show-stops></el-slider>
              </el-form-item>
              <el-form-item label="闪烁模式">
                <el-radio-group v-model="lightMode">
                  <el-radio label="constant">常亮</el-radio>
                  <el-radio label="slow">慢闪</el-radio>
                  <el-radio label="fast">快闪</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="controlLightDevice">控制灯光</el-button>
                <el-button type="info" @click="testLightDevice">测试设备</el-button>
              </el-form-item>
            </el-form>
          </div>
          
          <div v-if="selectedDevice.type === 'AR设备'">
            <h4>AR导航控制</h4>
            <el-form label-width="100px">
              <el-form-item label="导航目标">
                <el-select v-model="arTarget" placeholder="选择导航目标">
                  <el-option v-for="target in arTargets" :key="target.id" :label="target.name" :value="target.id"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="导航样式">
                <el-select v-model="arStyle" placeholder="选择导航样式">
                  <el-option label="箭头指引" value="arrow"></el-option>
                  <el-option label="路径高亮" value="path"></el-option>
                  <el-option label="3D模型" value="3d"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" :disabled="!arTarget" @click="controlARDevice">发送导航</el-button>
                <el-button type="info" @click="testARDevice">测试设备</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "GuideMonitor",
  data() {
    return {
      // 设备统计
      deviceStats: {
        total: 127,
        online: 98,
        offline: 29,
        voice: {
          total: 45,
          normal: 42,
          error: 3
        },
        light: {
          total: 68,
          normal: 65,
          error: 3
        },
        ar: {
          total: 14,
          normal: 12,
          error: 2
        }
      },
      // 仓库列表
      warehouses: [
        { id: 1, name: "主仓库" },
        { id: 2, name: "A区仓库" },
        { id: 3, name: "B区仓库" }
      ],
      // 当前选择的仓库
      currentWarehouse: 1,
      // 地图缩放
      mapZoom: 1,
      // 设备列表
      devices: [
        { id: 1, name: '语音播报器A1', type: '语音设备', status: 'online', position: {x: 120, y: 150}, active: true },
        { id: 2, name: '语音播报器A2', type: '语音设备', status: 'online', position: {x: 250, y: 180}, active: false },
        { id: 3, name: '灯光设备B1', type: '灯光设备', status: 'online', position: {x: 180, y: 220}, active: true },
        { id: 4, name: '灯光设备B2', type: '灯光设备', status: 'offline', position: {x: 320, y: 240}, active: false },
        { id: 5, name: 'AR导航终端C1', type: 'AR设备', status: 'online', position: {x: 220, y: 320}, active: false }
      ],
      // 状态筛选
      statusFilter: 'all',
      // 选中的设备
      selectedDevice: null,
      // 设备操作弹窗
      deviceOperationVisible: false,
      // 语音播报配置
      voiceContent: '',
      voiceVolume: 7,
      // 灯光控制配置
      lightColor: '#FF9900',
      lightBrightness: 80,
      lightMode: 'constant',
      // AR导航配置
      arTarget: '',
      arStyle: 'arrow',
      arTargets: [
        { id: 'A12', name: 'A12货架' },
        { id: 'B35', name: 'B35货架' },
        { id: 'C18', name: 'C18货架' },
        { id: 'EXIT', name: '出口' }
      ],
      // 活动日志
      activityLogs: [],
      // 自动刷新
      autoRefresh: true,
      refreshTimer: null,
      // 告警信息
      alerts: [
        { id: 1, time: new Date().getTime() - 5 * 60000, device: '语音播报器A3', level: 'warning', message: '设备响应超时', acknowledged: false },
        { id: 2, time: new Date().getTime() - 35 * 60000, device: '灯光设备B5', level: 'error', message: '设备离线', acknowledged: false },
        { id: 3, time: new Date().getTime() - 120 * 60000, device: 'AR终端C2', level: 'info', message: '电池电量低', acknowledged: true }
      ]
    }
  },
  computed: {
    filteredDevices() {
      if (this.statusFilter === 'all') {
        return this.devices;
      } else if (this.statusFilter === 'active') {
        return this.devices.filter(device => device.active);
      } else {
        return this.devices.filter(device => device.status === this.statusFilter);
      }
    }
  },
  mounted() {
    this.initMap();
    this.startAutoRefresh();
    this.generateActivityLogs(); // 模拟生成活动日志
  },
  beforeDestroy() {
    this.stopAutoRefresh();
  },
  methods: {
    // 地图相关方法
    initMap() {
      const canvas = document.createElement('canvas');
      const container = this.$refs.mapContainer;
      container.innerHTML = '';
      container.appendChild(canvas);
      
      canvas.width = container.clientWidth;
      canvas.height = container.clientHeight;
      
      const ctx = canvas.getContext('2d');
      
      // 绘制仓库地图 - 简单示例
      ctx.fillStyle = '#f5f7fa';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      // 绘制货架
      ctx.fillStyle = '#dcdfe6';
      for(let i = 0; i < 5; i++) {
        for(let j = 0; j < 3; j++) {
          ctx.fillRect(100 + i * 80, 100 + j * 80, 50, 20);
        }
      }
      
      // 绘制设备位置
      this.devices.forEach(device => {
        ctx.beginPath();
        ctx.arc(device.position.x, device.position.y, 10, 0, Math.PI * 2);
        
        if (device.status === 'offline') {
          ctx.fillStyle = '#909399';
        } else if (device.active) {
          ctx.fillStyle = '#E6A23C';
        } else {
          ctx.fillStyle = '#67C23A';
        }
        
        ctx.fill();
        
        // 添加设备标签
        ctx.font = '12px Arial';
        ctx.fillStyle = '#606266';
        ctx.fillText(device.name, device.position.x - 20, device.position.y - 15);
      });
    },
    handleMapCommand(id) {
      this.currentWarehouse = id;
      this.$message.success(`已切换到${this.warehouses.find(w => w.id === id).name}`);
      this.initMap();
    },
    zoomIn() {
      if (this.mapZoom < 2) {
        this.mapZoom += 0.2;
        this.updateMapZoom();
      }
    },
    zoomOut() {
      if (this.mapZoom > 0.5) {
        this.mapZoom -= 0.2;
        this.updateMapZoom();
      }
    },
    resetMap() {
      this.mapZoom = 1;
      this.updateMapZoom();
    },
    updateMapZoom() {
      const container = this.$refs.mapContainer;
      const canvas = container.querySelector('canvas');
      if (canvas) {
        canvas.style.transform = `scale(${this.mapZoom})`;
        canvas.style.transformOrigin = 'center center';
      }
    },
    
    // 设备状态相关
    refreshDeviceStatus() {
      // 模拟刷新设备状态
      this.$message.success('设备状态已刷新');
      // 随机更改一些设备状态，模拟刷新效果
      const randomDevice = this.devices[Math.floor(Math.random() * this.devices.length)];
      randomDevice.active = !randomDevice.active;
      this.initMap();
    },
    handleDeviceClick(row) {
      this.selectedDevice = row;
      this.deviceOperationVisible = true;
      
      // 根据设备类型初始化相关控制参数
      if (row.type === '语音设备') {
        this.voiceContent = '';
        this.voiceVolume = 7;
      } else if (row.type === '灯光设备') {
        this.lightColor = '#FF9900';
        this.lightBrightness = 80;
        this.lightMode = 'constant';
      } else if (row.type === 'AR设备') {
        this.arTarget = '';
        this.arStyle = 'arrow';
      }
    },
    getDeviceRowClass({row}) {
      if (row.active) return 'device-active';
      if (row.status === 'offline') return 'device-offline';
      return '';
    },
    getDeviceTypeTag(type) {
      const typeMap = {
        '语音设备': 'primary',
        '灯光设备': 'warning',
        'AR设备': 'info'
      };
      return typeMap[type] || '';
    },
    showDeviceOperations(device) {
      this.selectedDevice = device;
      this.deviceOperationVisible = true;
    },
    
    // 设备控制方法
    controlVoiceDevice() {
      this.addActivityLog('info', `发送语音播报到 ${this.selectedDevice.name}: "${this.voiceContent}"`);
      this.$message.success('语音播报指令已发送');
      this.deviceOperationVisible = false;
    },
    testVoiceDevice() {
      this.addActivityLog('info', `测试 ${this.selectedDevice.name}`);
      this.$message.info('测试音频已发送到设备');
    },
    controlLightDevice() {
      const modeText = this.lightMode === 'constant' ? '常亮' : this.lightMode === 'slow' ? '慢闪' : '快闪';
      this.addActivityLog('info', `控制 ${this.selectedDevice.name} 灯光：${modeText}，亮度 ${this.lightBrightness}%`);
      this.$message.success('灯光控制指令已发送');
      this.deviceOperationVisible = false;
    },
    testLightDevice() {
      this.addActivityLog('info', `测试 ${this.selectedDevice.name}`);
      this.$message.info('测试指令已发送到灯光设备');
    },
    controlARDevice() {
      const target = this.arTargets.find(t => t.id === this.arTarget);
      this.addActivityLog('info', `发送AR导航指令到 ${this.selectedDevice.name}，目标: ${target.name}`);
      this.$message.success('AR导航指令已发送');
      this.deviceOperationVisible = false;
    },
    testARDevice() {
      this.addActivityLog('info', `测试 ${this.selectedDevice.name}`);
      this.$message.info('测试指令已发送到AR设备');
    },
    
    // 活动日志相关
    addActivityLog(type, message) {
      this.activityLogs.unshift({
        type,
        time: new Date().getTime(),
        message
      });
      
      // 保留最近100条日志
      if (this.activityLogs.length > 100) {
        this.activityLogs.pop();
      }
      
      // 滚动到顶部
      this.$nextTick(() => {
        if (this.$refs.logContainer) {
          this.$refs.logContainer.scrollTop = 0;
        }
      });
    },
    generateActivityLogs() {
      // 生成一些随机的活动日志用于展示
      const activities = [
        '设备状态检测',
        '语音播报请求',
        '灯光控制请求',
        'AR导航请求',
        '设备离线告警',
        '设备恢复在线'
      ];
      
      const types = ['info', 'warning', 'error'];
      
      for (let i = 0; i < 20; i++) {
        const activity = activities[Math.floor(Math.random() * activities.length)];
        const type = types[Math.floor(Math.random() * types.length)];
        const time = new Date().getTime() - i * 30000 - Math.random() * 30000;
        
        this.activityLogs.push({
          type,
          time,
          message: `${this.devices[i % this.devices.length].name}: ${activity}`
        });
      }
    },
    formatTime(timestamp) {
      const date = new Date(timestamp);
      return date.getHours().toString().padStart(2, '0') + ':' +
             date.getMinutes().toString().padStart(2, '0') + ':' +
             date.getSeconds().toString().padStart(2, '0');
    },
    formatDateTime(timestamp) {
      const date = new Date(timestamp);
      return date.getFullYear() + '-' +
             (date.getMonth() + 1).toString().padStart(2, '0') + '-' +
             date.getDate().toString().padStart(2, '0') + ' ' +
             date.getHours().toString().padStart(2, '0') + ':' +
             date.getMinutes().toString().padStart(2, '0');
    },
    toggleAutoRefresh(value) {
      if (value) {
        this.startAutoRefresh();
      } else {
        this.stopAutoRefresh();
      }
    },
    startAutoRefresh() {
      this.refreshTimer = setInterval(() => {
        // 模拟随机生成活动日志
        const randomDevice = this.devices[Math.floor(Math.random() * this.devices.length)];
        const randomAction = Math.random() > 0.7 ? '状态变更' : Math.random() > 0.3 ? '数据上报' : '操作执行';
        this.addActivityLog('info', `${randomDevice.name}: ${randomAction}`);
      }, 8000); // 每8秒生成一条日志
    },
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer);
      }
    },
    
    // 告警相关
    getAlertRowClass({row}) {
      return row.acknowledged ? 'alert-acknowledged' : `alert-${row.level}`;
    },
    getAlertLevelType(level) {
      const levelMap = {
        'error': 'danger',
        'warning': 'warning',
        'info': 'info'
      };
      return levelMap[level] || 'info';
    },
    getAlertLevelText(level) {
      const textMap = {
        'error': '错误',
        'warning': '警告',
        'info': '提示'
      };
      return textMap[level] || level;
    },
    acknowledgeAlert(alert) {
      alert.acknowledged = true;
      this.$message.success(`已确认告警: ${alert.message}`);
    },
    acknowledgeAllAlerts() {
      this.alerts.forEach(alert => {
        alert.acknowledged = true;
      });
      this.$message.success('已确认全部告警');
    },
    viewAlertDetail(alert) {
      this.$alert(`设备: ${alert.device}\n时间: ${this.formatDateTime(alert.time)}\n级别: ${this.getAlertLevelText(alert.level)}\n内容: ${alert.message}`, '告警详情', {
        confirmButtonText: '关闭'
      });
    }
  }
}
</script>

<style scoped>
.guide-monitor {
  padding: 20px 0;
}
.monitor-cards {
  margin-bottom: 20px;
}
.status-card {
  height: 120px;
}
.status-header {
  display: flex;
  align-items: center;
  color: #606266;
}
.status-header i {
  font-size: 24px;
  margin-right: 10px;
}
.status-value {
  font-size: 36px;
  font-weight: bold;
  margin: 10px 0;
}
.status-footer {
  display: flex;
  gap: 10px;
}
.monitor-main {
  margin-bottom: 20px;
}
.warehouse-map, .device-status {
  height: 550px;
}
.map-container {
  position: relative;
  height: 480px;
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}
.map-legend {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(255,255,255,0.9);
  padding: 10px;
  border-radius: 4px;
  z-index: 100;
}
.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}
.legend-icon {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 5px;
}
.legend-icon.online {
  background-color: #67C23A;
}
.legend-icon.offline {
  background-color: #909399;
}
.legend-icon.active {
  background-color: #E6A23C;
  animation: pulse 1.5s infinite;
}
.map-content {
  width: 100%;
  height: 100%;
}
.map-controls {
  position: absolute;
  bottom: 10px;
  right: 10px;
}
.monitor-logs {
  margin-bottom: 20px;
}
.activity-log, .alerts {
  height: 400px;
}
.log-content {
  height: 340px;
  overflow-y: auto;
}
.log-item {
  padding: 8px;
  border-bottom: 1px solid #ebeef5;
  font-size: 14px;
}
.log-item:last-child {
  border-bottom: none;
}
.log-time {
  font-size: 12px;
  color: #909399;
  margin-bottom: 3px;
}
.log-error .log-message {
  color: #F56C6C;
}
.device-operation .device-info {
  text-align: center;
  margin-bottom: 20px;
}
.device-operation h3 {
  margin: 0 0 10px;
}
.device-meta {
  display: flex;
  justify-content: center;
  gap: 10px;
}

/* 设备状态高亮样式 */
.device-active td {
  background-color: rgba(230, 162, 60, 0.1) !important;
}
.device-offline td {
  background-color: rgba(144, 147, 153, 0.1) !important;
  color: #909399;
}

/* 告警样式 */
.alert-error td {
  background-color: rgba(245, 108, 108, 0.1) !important;
}
.alert-warning td {
  background-color: rgba(230, 162, 60, 0.1) !important;
}
.alert-acknowledged td {
  color: #909399;
}

/* 状态筛选和搜索 */
.status-filters {
  margin-bottom: 15px;
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}
</style>
</template>

<script>
export default {
  name: "GuideMonitor",
  data() {
    return {
      // 设备统计
      deviceStats: {
        total: 127,
        online: 98,
        offline: 29,
        voice: {
          total: 45,
          normal: 42,
          error: 3
        },
        light: {
          total: 68,
          normal: 65,
          error: 3
        },
        ar: {
          total: 14,
          normal: 12,
          error: 2
        }
      },
      // 仓库列表
      warehouses: [
        { id: 1, name: "主仓库" },
        { id: 2, name: "A区仓库" },
        { id: 3, name: "B区仓库" }
      ],
      // 当前选择的仓库
      currentWarehouse: 1,
      // 地图缩放
      mapZoom: 1,
      // 设备列表
      devices: [
        { id: 1, name: '语音播报器A1', type: '语音设备', status: 'online', position: {x: 120, y: 150}, active: true },
        { id: 2, name: '语音播报器A2', type: '语音设备', status: 'online', position: {x: 250, y: 180}, active: false },
        { id: 3, name: '灯光设备B1', type: '灯光设备', status: 'online', position: {x: 180, y: 220}, active: true },
        { id: 4, name: '灯光设备B2', type: '灯光设备', status: 'offline', position: {x: 320, y: 240}, active: false },
        { id: 5, name: 'AR导航终端C1', type: 'AR设备', status: 'online', position: {x: 220, y: 320}, active: false }
      ],
      // 状态筛选
      statusFilter: 'all',
      // 选中的设备
      selectedDevice: null,
      // 设备操作弹窗
      deviceOperationVisible: false,
      // 语音播报配置
      voiceContent: '',
      voiceVolume: 7,
      // 灯光控制配置
      lightColor: '#FF9900',
      lightBrightness: 80,
      lightMode: 'constant',
      // AR导航配置
      arTarget: '',
      arStyle: 'arrow',
      arTargets: [
        { id: 'A12', name: 'A12货架' },
        { id: 'B35', name: 'B35货架' },
        { id: 'C18', name: 'C18货架' },
        { id: 'EXIT', name: '出口' }
      ],
      // 活动日志
      activityLogs: [],
      // 自动刷新
      autoRefresh: true,
      refreshTimer: null,
      // 告警信息
      alerts: [
        { id: 1, time: new Date().getTime() - 5 * 60000, device: '语音播报器A3', level: 'warning', message: '设备响应超时', acknowledged: false },
        { id: 2, time: new Date().getTime() - 35 * 60000, device: '灯光设备B5', level: 'error', message: '设备离线', acknowledged: false },
        { id: 3, time: new Date().getTime() - 120 * 60000, device: 'AR终端C2', level: 'info', message: '电池电量低', acknowledged: true }
      ]
    }
  },
  computed: {
    filteredDevices() {
      if (this.statusFilter === 'all') {
        return this.devices;
      } else if (this.statusFilter === 'active') {
        return this.devices.filter(device => device.active);
      } else {
        return this.devices.filter(device => device.status === this.statusFilter);
      }
    }
  },
  mounted() {
    this.initMap();
    this.startAutoRefresh();
    this.generateActivityLogs(); // 模拟生成活动日志
  },
  beforeDestroy() {
    this.stopAutoRefresh();
  },
  methods: {
    // 地图相关方法
    initMap() {
      const canvas = document.createElement('canvas');
      const container = this.$refs.mapContainer;
      container.innerHTML = '';
      container.appendChild(canvas);
      
      canvas.width = container.clientWidth;
      canvas.height = container.clientHeight;
      
      const ctx = canvas.getContext('2d');
      
      // 绘制仓库地图 - 简单示例
      ctx.fillStyle = '#f5f7fa';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      // 绘制货架
      ctx.fillStyle = '#dcdfe6';
      for(let i = 0; i < 5; i++) {
        for(let j = 0; j < 3; j++) {
          ctx.fillRect(100 + i * 80, 100 + j * 80, 50, 20);
        }
      }
      
      // 绘制设备位置
      this.devices.forEach(device => {
        ctx.beginPath();
        ctx.arc(device.position.x, device.position.y, 10, 0, Math.PI * 2);
        
        if (device.status === 'offline') {
          ctx.fillStyle = '#909399';
        } else if (device.active) {
          ctx.fillStyle = '#E6A23C';
        } else {
          ctx.fillStyle = '#67C23A';
        }
        
        ctx.fill();
        
        // 添加设备标签
        ctx.font = '12px Arial';
        ctx.fillStyle = '#606266';
        ctx.fillText(device.name, device.position.x - 20, device.position.y - 15);
      });
    },
    handleMapCommand(id) {
      this.currentWarehouse = id;
      this.$message.success(`已切换到${this.warehouses.find(w => w.id === id).name}`);
      this.initMap();
    },
    zoomIn() {
      if (this.mapZoom < 2) {
        this.mapZoom += 0.2;
        this.updateMapZoom();
      }
    },
    zoomOut() {
      if (this.mapZoom > 0.5) {
        this.mapZoom -= 0.2;
        this.updateMapZoom();
      }
    },
    resetMap() {
      this.mapZoom = 1;
      this.updateMapZoom();
    },
    updateMapZoom() {
      const container = this.$refs.mapContainer;
      const canvas = container.querySelector('canvas');
      if (canvas) {
        canvas.style.transform = `scale(${this.mapZoom})`;
        canvas.style.transformOrigin = 'center center';
      }
    },
    
    // 设备状态相关
    refreshDeviceStatus() {
      // 模拟刷新设备状态
      this.$message.success('设备状态已刷新');
      // 随机更改一些设备状态，模拟刷新效果
      const randomDevice = this.devices[Math.floor(Math.random() * this.devices.length)];
      randomDevice.active = !randomDevice.active;
      this.initMap();
    },
    handleDeviceClick(row) {
      this.selectedDevice = row;
      this.deviceOperationVisible = true;
      
      // 根据设备类型初始化相关控制参数
      if (row.type === '语音设备') {
        this.voiceContent = '';
        this.voiceVolume = 7;
      } else if (row.type === '灯光设备') {
        this.lightColor = '#FF9900';
        this.lightBrightness = 80;
        this.lightMode = 'constant';
      } else if (row.type === 'AR设备') {
        this.arTarget = '';
        this.arStyle = 'arrow';
      }
    },
    getDeviceRowClass({row}) {
      if (row.active) return 'device-active';
      if (row.status === 'offline') return 'device-offline';
      return '';
    },
    getDeviceTypeTag(type) {
      const typeMap = {
        '语音设备': 'primary',
        '灯光设备': 'warning',
        'AR设备': 'info'
      };
      return typeMap[type] || '';
    },
    showDeviceOperations(device) {
      this.selectedDevice = device;
      this.deviceOperationVisible = true;
    },
    
    // 设备控制方法
    controlVoiceDevice() {
      this.addActivityLog('info', `发送语音播报到 ${this.selectedDevice.name}: "${this.voiceContent}"`);
      this.$message.success('语音播报指令已发送');
      this.deviceOperationVisible = false;
    },
    testVoiceDevice() {
      this.addActivityLog('info', `测试 ${this.selectedDevice.name}`);
      this.$message.info('测试音频已发送到设备');
    },
    controlLightDevice() {
      const modeText = this.lightMode === 'constant' ? '常亮' : this.lightMode === 'slow' ? '慢闪' : '快闪';
      this.addActivityLog('info', `控制 ${this.selectedDevice.name} 灯光：${modeText}，亮度 ${this.lightBrightness}%`);
      this.$message.success('灯光控制指令已发送');
      this.deviceOperationVisible = false;
    },
    testLightDevice() {
      this.addActivityLog('info', `测试 ${this.selectedDevice.name}`);
      this.$message.info('测试指令已发送到灯光设备');
    },
    controlARDevice() {
      const target = this.arTargets.find(t => t.id === this.arTarget);
      this.addActivityLog('info', `发送AR导航指令到 ${this.selectedDevice.name}，目标: ${target.name}`);
      this.$message.success('AR导航指令已发送');
      this.deviceOperationVisible = false;
    },
    testARDevice() {
      this.addActivityLog('info', `测试 ${this.selectedDevice.name}`);
      this.$message.info('测试指令已发送到AR设备');
    },
    
    // 活动日志相关
    addActivityLog(type, message) {
      this.activityLogs.unshift({
        type,
        time: new Date().getTime(),
        message
      });
      
      // 保留最近100条日志
      if (this.activityLogs.length > 100) {
        this.activityLogs.pop();
      }
      
      // 滚动到顶部
      this.$nextTick(() => {
        if (this.$refs.logContainer) {
          this.$refs.logContainer.scrollTop = 0;
        }
      });
    },
    generateActivityLogs() {
      // 生成一些随机的活动日志用于展示
      const activities = [
        '设备状态检测',
        '语音播报请求',
        '灯光控制请求',
        'AR导航请求',
        '设备离线告警',
        '设备恢复在线'
      ];
      
      const types = ['info', 'warning', 'error'];
      
      for (let i = 0; i < 20; i++) {
        const activity = activities[Math.floor(Math.random() * activities.length)];
        const type = types[Math.floor(Math.random() * types.length)];
        const time = new Date().getTime() - i * 30000 - Math.random() * 30000;
        
        this.activityLogs.push({
          type,
          time,
          message: `${this.devices[i % this.devices.length].name}: ${activity}`
        });
      }
    },
    formatTime(timestamp) {
      const date = new Date(timestamp);
      return date.getHours().toString().padStart(2, '0') + ':' +
             date.getMinutes().toString().padStart(2, '0') + ':' +
             date.getSeconds().toString().padStart(2, '0');
    },
    formatDateTime(timestamp) {
      const date = new Date(timestamp);
      return date.getFullYear() + '-' +
             (date.getMonth() + 1).toString().padStart(2, '0') + '-' +
             date.getDate().toString().padStart(2, '0') + ' ' +
             date.getHours().toString().padStart(2, '0') + ':' +
             date.getMinutes().toString().padStart(2, '0');
    },
    toggleAutoRefresh(value) {
      if (value) {
        this.startAutoRefresh();
      } else {
        this.stopAutoRefresh();
      }
    },
    startAutoRefresh() {
      this.refreshTimer = setInterval(() => {
        // 模拟随机生成活动日志
        const randomDevice = this.devices[Math.floor(Math.random() * this.devices.length)];
        const randomAction = Math.random() > 0.7 ? '状态变更' : Math.random() > 0.3 ? '数据上报' : '操作执行';
        this.addActivityLog('info', `${randomDevice.name}: ${randomAction}`);
      }, 8000); // 每8秒生成一条日志
    },
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer);
      }
    },
    
    // 告警相关
    getAlertRowClass({row}) {
      return row.acknowledged ? 'alert-acknowledged' : `alert-${row.level}`;
    },
    getAlertLevelType(level) {
      const levelMap = {
        'error': 'danger',
        'warning': 'warning',
        'info': 'info'
      };
      return levelMap[level] || 'info';
    },
    getAlertLevelText(level) {
      const textMap = {
        'error': '错误',
        'warning': '警告',
        'info': '提示'
      };
      return textMap[level] || level;
    },
    acknowledgeAlert(alert) {
      alert.acknowledged = true;
      this.$message.success(`已确认告警: ${alert.message}`);
    },
    acknowledgeAllAlerts() {
      this.alerts.forEach(alert => {
        alert.acknowledged = true;
      });
      this.$message.success('已确认全部告警');
    },
    viewAlertDetail(alert) {
      this.$alert(`设备: ${alert.device}\n时间: ${this.formatDateTime(alert.time)}\n级别: ${this.getAlertLevelText(alert.level)}\n内容: ${alert.message}`, '告警详情', {
        confirmButtonText: '关闭'
      });
    }
  }
}
</script>

<style scoped>
.guide-monitor {
  padding: 20px 0;
}
.monitor-cards {
  margin-bottom: 20px;
}
.status-card {
  height: 120px;
}
.status-header {
  display: flex;
  align-items: center;
  color: #606266;
}
.status-header i {
  font-size: 24px;
  margin-right: 10px;
}
.status-value {
  font-size: 36px;
  font-weight: bold;
  margin: 10px 0;
}
.status-footer {
  display: flex;
  gap: 10px;
}
.monitor-main {
  margin-bottom: 20px;
}
.warehouse-map, .device-status {
  height: 550px;
}
.map-container {
  position: relative;
  height: 480px;
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}
.map-legend {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(255,255,255,0.9);
  padding: 10px;
  border-radius: 4px;
  z-index: 100;
}
.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}
.legend-icon {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 5px;
}
.legend-icon.online {
  background-color: #67C23A;
}
.legend-icon.offline {
  background-color: #909399;
}
.legend-icon.active {
  background-color: #E6A23C;
  animation: pulse 1.5s infinite;
}
.map-content {
  width: 100%;
  height: 100%;
}
.map-controls {
  position: absolute;
  bottom: 10px;
  right: 10px;
}
.monitor-logs {
  margin-bottom: 20px;
}
.activity-log, .alerts {
  height: 400px;
}
.log-content {
  height: 340px;
  overflow-y: auto;
}
.log-item {
  padding: 8px;
  border-bottom: 1px solid #ebeef5;
  font-size: 14px;
}
.log-item:last-child {
  border-bottom: none;
}
.log-time {
  font-size: 12px;
  color: #909399;
  margin-bottom: 3px;
}
.log-error .log-message {
  color: #F56C6C;
}
.device-operation .device-info {
  text-align: center;
  margin-bottom: 20px;
}
.device-operation h3 {
  margin: 0 0 10px;
}
.device-meta {
  display: flex;
  justify-content: center;
  gap: 10px;
}

/* 设备状态高亮样式 */
.device-active td {
  background-color: rgba(230, 162, 60, 0.1) !important;
}
.device-offline td {
  background-color: rgba(144, 147, 153, 0.1) !important;
  color: #909399;
}

/* 告警样式 */
.alert-error td {
  background-color: rgba(245, 108, 108, 0.1) !important;
}
.alert-warning td {
  background-color: rgba(230, 162, 60, 0.1) !important;
}
.alert-acknowledged td {
  color: #909399;
}

/* 状态筛选和搜索 */
.status-filters {
  margin-bottom: 15px;
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}
</style>
</template>

<script>
export default {
  name: "GuideMonitor",
  data() {
    return {
      // 设备统计
      deviceStats: {
        total: 127,
        online: 98,
        offline: 29,
        voice: {
          total: 45,
          normal: 42,
          error: 3
        },
        light: {
          total: 68,
          normal: 65,
          error: 3
        },
        ar: {
          total: 14,
          normal: 12,
          error: 2
        }
      },
      // 仓库列表
      warehouses: [
        { id: 1, name: "主仓库" },
        { id: 2, name: "A区仓库" },
        { id: 3, name: "B区仓库" }
      ],
      // 当前选择的仓库
      currentWarehouse: 1,
      // 地图缩放
      mapZoom: 1,
      // 设备列表
      devices: [
        { id: 1, name: '语音播报器A1', type: '语音设备', status: 'online', position: {x: 120, y: 150}, active: true },
        { id: 2, name: '语音播报器A2', type: '语音设备', status: 'online', position: {x: 250, y: 180}, active: false },
        { id: 3, name: '灯光设备B1', type: '灯光设备', status: 'online', position: {x: 180, y: 220}, active: true },
        { id: 4, name: '灯光设备B2', type: '灯光设备', status: 'offline', position: {x: 320, y: 240}, active: false },
        { id: 5, name: 'AR导航终端C1', type: 'AR设备', status: 'online', position: {x: 220, y: 320}, active: false }
      ],
      // 状态筛选
      statusFilter: 'all',
      // 选中的设备
      selectedDevice: null,
      // 设备操作弹窗
      deviceOperationVisible: false,
      // 语音播报配置
      voiceContent: '',
      voiceVolume: 7,
      // 灯光控制配置
      lightColor: '#FF9900',
      lightBrightness: 80,
      lightMode: 'constant',
      // AR导航配置
      arTarget: '',
      arStyle: 'arrow',
      arTargets: [
        { id: 'A12', name: 'A12货架' },
        { id: 'B35', name: 'B35货架' },
        { id: 'C18', name: 'C18货架' },
        { id: 'EXIT', name: '出口' }
      ],
      // 活动日志
      activityLogs: [],
      // 自动刷新
      autoRefresh: true,
      refreshTimer: null,
      // 告警信息
      alerts: [
        { id: 1, time: new Date().getTime() - 5 * 60000, device: '语音播报器A3', level: 'warning', message: '设备响应超时', acknowledged: false },
        { id: 2, time: new Date().getTime() - 35 * 60000, device: '灯光设备B5', level: 'error', message: '设备离线', acknowledged: false },
        { id: 3, time: new Date().getTime() - 120 * 60000, device: 'AR终端C2', level: 'info', message: '电池电量低', acknowledged: true }
      ]
    }
  },
  computed: {
    filteredDevices() {
      if (this.statusFilter === 'all') {
        return this.devices;
      } else if (this.statusFilter === 'active') {
        return this.devices.filter(device => device.active);
      } else {
        return this.devices.filter(device => device.status === this.statusFilter);
      }
    }
  },
  mounted() {
    this.initMap();
    this.startAutoRefresh();
    this.generateActivityLogs(); // 模拟生成活动日志
  },
  beforeDestroy() {
    this.stopAutoRefresh();
  },
  methods: {
    // 地图相关方法
    initMap() {
      const canvas = document.createElement('canvas');
      const container = this.$refs.mapContainer;
      container.innerHTML = '';
      container.appendChild(canvas);
      
      canvas.width = container.clientWidth;
      canvas.height = container.clientHeight;
      
      const ctx = canvas.getContext('2d');
      
      // 绘制仓库地图 - 简单示例
      ctx.fillStyle = '#f5f7fa';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      // 绘制货架
      ctx.fillStyle = '#dcdfe6';
      for(let i = 0; i < 5; i++) {
        for(let j = 0; j < 3; j++) {
          ctx.fillRect(100 + i * 80, 100 + j * 80, 50, 20);
        }
      }
      
      // 绘制设备位置
      this.devices.forEach(device => {
        ctx.beginPath();
        ctx.arc(device.position.x, device.position.y, 10, 0, Math.PI * 2);
        
        if (device.status === 'offline') {
          ctx.fillStyle = '#909399';
        } else if (device.active) {
          ctx.fillStyle = '#E6A23C';
        } else {
          ctx.fillStyle = '#67C23A';
        }
        
        ctx.fill();
        
        // 添加设备标签
        ctx.font = '12px Arial';
        ctx.fillStyle = '#606266';
        ctx.fillText(device.name, device.position.x - 20, device.position.y - 15);
      });
    },
    handleMapCommand(id) {
      this.currentWarehouse = id;
      this.$message.success(`已切换到${this.warehouses.find(w => w.id === id).name}`);
      this.initMap();
    },
    zoomIn() {
      if (this.mapZoom < 2) {
        this.mapZoom += 0.2;
        this.updateMapZoom();
      }
    },
    zoomOut() {
      if (this.mapZoom > 0.5) {
        this.mapZoom -= 0.2;
        this.updateMapZoom();
      }
    },
    resetMap() {
      this.mapZoom = 1;
      this.updateMapZoom();
    },
    updateMapZoom() {
      const container = this.$refs.mapContainer;
      const canvas = container.querySelector('canvas');
      if (canvas) {
        canvas.style.transform = `scale(${this.mapZoom})`;
        canvas.style.transformOrigin = 'center center';
      }
    },
    
    // 设备状态相关
    refreshDeviceStatus() {
      // 模拟刷新设备状态
      this.$message.success('设备状态已刷新');
      // 随机更改一些设备状态，模拟刷新效果
      const randomDevice = this.devices[Math.floor(Math.random() * this.devices.length)];
      randomDevice.active = !randomDevice.active;
      this.initMap();
    },
    handleDeviceClick(row) {
      this.selectedDevice = row;
      this.deviceOperationVisible = true;
      
      // 根据设备类型初始化相关控制参数
      if (row.type === '语音设备') {
        this.voiceContent = '';
        this.voiceVolume = 7;
      } else if (row.type === '灯光设备') {
        this.lightColor = '#FF9900';
        this.lightBrightness = 80;
        this.lightMode = 'constant';
      } else if (row.type === 'AR设备') {
        this.arTarget = '';
        this.arStyle = 'arrow';
      }
    },
    getDeviceRowClass({row}) {
      if (row.active) return 'device-active';
      if (row.status === 'offline') return 'device-offline';
      return '';
    },
    getDeviceTypeTag(type) {
      const typeMap = {
        '语音设备': 'primary',
        '灯光设备': 'warning',
        'AR设备': 'info'
      };
      return typeMap[type] || '';
    },
    showDeviceOperations(device) {
      this.selectedDevice = device;
      this.deviceOperationVisible = true;
    },
    
    // 设备控制方法
    controlVoiceDevice() {
      this.addActivityLog('info', `发送语音播报到 ${this.selectedDevice.name}: "${this.voiceContent}"`);
      this.$message.success('语音播报指令已发送');
      this.deviceOperationVisible = false;
    },
    testVoiceDevice() {
      this.addActivityLog('info', `测试 ${this.selectedDevice.name}`);
      this.$message.info('测试音频已发送到设备');
    },
    controlLightDevice() {
      const modeText = this.lightMode === 'constant' ? '常亮' : this.lightMode === 'slow' ? '慢闪' : '快闪';
      this.addActivityLog('info', `控制 ${this.selectedDevice.name} 灯光：${modeText}，亮度 ${this.lightBrightness}%`);
      this.$message.success('灯光控制指令已发送');
      this.deviceOperationVisible = false;
    },
    testLightDevice() {
      this.addActivityLog('info', `测试 ${this.selectedDevice.name}`);
      this.$message.info('测试指令已发送到灯光设备');
    },
    controlARDevice() {
      const target = this.arTargets.find(t => t.id === this.arTarget);
      this.addActivityLog('info', `发送AR导航指令到 ${this.selectedDevice.name}，目标: ${target.name}`);
      this.$message.success('AR导航指令已发送');
      this.deviceOperationVisible = false;
    },
    testARDevice() {
      this.addActivityLog('info', `测试 ${this.selectedDevice.name}`);
      this.$message.info('测试指令已发送到AR设备');
    },
    
    // 活动日志相关
    addActivityLog(type, message) {
      this.activityLogs.unshift({
        type,
        time: new Date().getTime(),
        message
      });
      
      // 保留最近100条日志
      if (this.activityLogs.length > 100) {
        this.activityLogs.pop();
      }
      
      // 滚动到顶部
      this.$nextTick(() => {
        if (this.$refs.logContainer) {
          this.$refs.logContainer.scrollTop = 0;
        }
      });
    },
    generateActivityLogs() {
      // 生成一些随机的活动日志用于展示
      const activities = [
        '设备状态检测',
        '语音播报请求',
        '灯光控制请求',
        'AR导航请求',
        '设备离线告警',
        '设备恢复在线'
      ];
      
      const types = ['info', 'warning', 'error'];
      
      for (let i = 0; i < 20; i++) {
        const activity = activities[Math.floor(Math.random() * activities.length)];
        const type = types[Math.floor(Math.random() * types.length)];
        const time = new Date().getTime() - i * 30000 - Math.random() * 30000;
        
        this.activityLogs.push({
          type,
          time,
          message: `${this.devices[i % this.devices.length].name}: ${activity}`
        });
      }
    },
    formatTime(timestamp) {
      const date = new Date(timestamp);
      return date.getHours().toString().padStart(2, '0') + ':' +
             date.getMinutes().toString().padStart(2, '0') + ':' +
             date.getSeconds().toString().padStart(2, '0');
    },
    formatDateTime(timestamp) {
      const date = new Date(timestamp);
      return date.getFullYear() + '-' +
             (date.getMonth() + 1).toString().padStart(2, '0') + '-' +
             date.getDate().toString().padStart(2, '0') + ' ' +
             date.getHours().toString().padStart(2, '0') + ':' +
             date.getMinutes().toString().padStart(2, '0');
    },
    toggleAutoRefresh(value) {
      if (value) {
        this.startAutoRefresh();
      } else {
        this.stopAutoRefresh();
      }
    },
    startAutoRefresh() {
      this.refreshTimer = setInterval(() => {
        // 模拟随机生成活动日志
        const randomDevice = this.devices[Math.floor(Math.random() * this.devices.length)];
        const randomAction = Math.random() > 0.7 ? '状态变更' : Math.random() > 0.3 ? '数据上报' : '操作执行';
        this.addActivityLog('info', `${randomDevice.name}: ${randomAction}`);
      }, 8000); // 每8秒生成一条日志
    },
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer);
      }
    },
    
    // 告警相关
    getAlertRowClass({row}) {
      return row.acknowledged ? 'alert-acknowledged' : `alert-${row.level}`;
    },
    getAlertLevelType(level) {
      const levelMap = {
        'error': 'danger',
        'warning': 'warning',
        'info': 'info'
      };
      return levelMap[level] || 'info';
    },
    getAlertLevelText(level) {
      const textMap = {
        'error': '错误',
        'warning': '警告',
        'info': '提示'
      };
      return textMap[level] || level;
    },
    acknowledgeAlert(alert) {
      alert.acknowledged = true;
      this.$message.success(`已确认告警: ${alert.message}`);
    },
    acknowledgeAllAlerts() {
      this.alerts.forEach(alert => {
        alert.acknowledged = true;
      });
      this.$message.success('已确认全部告警');
    },
    viewAlertDetail(alert) {
      this.$alert(`设备: ${alert.device}\n时间: ${this.formatDateTime(alert.time)}\n级别: ${this.getAlertLevelText(alert.level)}\n内容: ${alert.message}`, '告警详情', {
        confirmButtonText: '关闭'
      });
    }
  }
}
</script>

<style scoped>
.guide-monitor {
  padding: 20px 0;
}
.monitor-cards {
  margin-bottom: 20px;
}
.status-card {
  height: 120px;
}
.status-header {
  display: flex;
  align-items: center;
  color: #606266;
}
.status-header i {
  font-size: 24px;
  margin-right: 10px;
}
.status-value {
  font-size: 36px;
  font-weight: bold;
  margin: 10px 0;
}
.status-footer {
  display: flex;
  gap: 10px;
}
.monitor-main {
  margin-bottom: 20px;
}
.warehouse-map, .device-status {
  height: 550px;
}
.map-container {
  position: relative;
  height: 480px;
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}
.map-legend {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(255,255,255,0.9);
  padding: 10px;
  border-radius: 4px;
  z-index: 100;
}
.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}
.legend-icon {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 5px;
}
.legend-icon.online {
  background-color: #67C23A;
}
.legend-icon.offline {
  background-color: #909399;
}
.legend-icon.active {
  background-color: #E6A23C;
  animation: pulse 1.5s infinite;
}
.map-content {
  width: 100%;
  height: 100%;
}
.map-controls {
  position: absolute;
  bottom: 10px;
  right: 10px;
}
.monitor-logs {
  margin-bottom: 20px;
}
.activity-log, .alerts {
  height: 400px;
}
.log-content {
  height: 340px;
  overflow-y: auto;
}
.log-item {
  padding: 8px;
  border-bottom: 1px solid #ebeef5;
  font-size: 14px;
}
.log-item:last-child {
  border-bottom: none;
}
.log-time {
  font-size: 12px;
  color: #909399;
  margin-bottom: 3px;
}
.log-error .log-message {
  color: #F56C6C;
}
.device-operation .device-info {
  text-align: center;
  margin-bottom: 20px;
}
.device-operation h3 {
  margin: 0 0 10px;
}
.device-meta {
  display: flex;
  justify-content: center;
  gap: 10px;
}

/* 设备状态高亮样式 */
.device-active td {
  background-color: rgba(230, 162, 60, 0.1) !important;
}
.device-offline td {
  background-color: rgba(144, 147, 153, 0.1) !important;
  color: #909399;
}

/* 告警样式 */
.alert-error td {
  background-color: rgba(245, 108, 108, 0.1) !important;
}
.alert-warning td {
  background-color: rgba(230, 162, 60, 0.1) !important;
}
.alert-acknowledged td {
  color: #909399;
}

/* 状态筛选和搜索 */
.status-filters {
  margin-bottom: 15px;
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}
</style>
</template>

<script>
export default {
  name: "GuideMonitor",
  data() {
    return {
      // 设备统计
      deviceStats: {
        total: 127,
        online: 98,
        offline: 29,
        voice: {
          total: 45,
          normal: 42,
          error: 3
        },
        light: {
          total: 68,
          normal: 65,
          error: 3
        },
        ar: {
          total: 14,
          normal: 12,
          error: 2
        }
      },
      // 仓库列表
      warehouses: [
        { id: 1, name: "主仓库" },
        { id: 2, name: "A区仓库" },
        { id: 3, name: "B区仓库" }
      ],
      // 当前选择的仓库
      currentWarehouse: 1,
      // 地图缩放
      mapZoom: 1,
      // 设备列表
      devices: [
        { id: 1, name: '语音播报器A1', type: '语音设备', status: 'online', position: {x: 120, y: 150}, active: true },
        { id: 2, name: '语音播报器A2', type: '语音设备', status: 'online', position: {x: 250, y: 180}, active: false },
        { id: 3, name: '灯光设备B1', type: '灯光设备', status: 'online', position: {x: 180, y: 220}, active: true },
        { id: 4, name: '灯光设备B2', type: '灯光设备', status: 'offline', position: {x: 320, y: 240}, active: false },
        { id: 5, name: 'AR导航终端C1', type: 'AR设备', status: 'online', position: {x: 220, y: 320}, active: false }
      ],
      // 状态筛选
      statusFilter: 'all',
      // 选中的设备
      selectedDevice: null,
      // 设备操作弹窗
      deviceOperationVisible: false,
      // 语音播报配置
      voiceContent: '',
      voiceVolume: 7,
      // 灯光控制配置
      lightColor: '#FF9900',
      lightBrightness: 80,
      lightMode: 'constant',
      // AR导航配置
      arTarget: '',
      arStyle: 'arrow',
      arTargets: [
        { id: 'A12', name: 'A12货架' },
        { id: 'B35', name: 'B35货架' },
        { id: 'C18', name: 'C18货架' },
        { id: 'EXIT', name: '出口' }
      ],
      // 活动日志
      activityLogs: [],
      // 自动刷新
      autoRefresh: true,
      refreshTimer: null,
      // 告警信息
      alerts: [
        { id: 1, time: new Date().getTime() - 5 * 60000, device: '语音播报器A3', level: 'warning', message: '设备响应超时', acknowledged: false },
        { id: 2, time: new Date().getTime() - 35 * 60000, device: '灯光设备B5', level: 'error', message: '设备离线', acknowledged: false },
        { id: 3, time: new Date().getTime() - 120 * 60000, device: 'AR终端C2', level: 'info', message: '电池电量低', acknowledged: true }
      ]
    }
  },
  computed: {
    filteredDevices() {
      if (this.statusFilter === 'all') {
        return this.devices;
      } else if (this.statusFilter === 'active') {
        return this.devices.filter(device => device.active);
      } else {
        return this.devices.filter(device => device.status === this.statusFilter);
      }
    }
  },
  mounted() {
    this.initMap();
    this.startAutoRefresh();
    this.generateActivityLogs(); // 模拟生成活动日志
  },
  beforeDestroy() {
    this.stopAutoRefresh();
  },
  methods: {
    // 地图相关方法
    initMap() {
      const canvas = document.createElement('canvas');
      const container = this.$refs.mapContainer;
      container.innerHTML = '';
      container.appendChild(canvas);
      
      canvas.width = container.clientWidth;
      canvas.height = container.clientHeight;
      
      const ctx = canvas.getContext('2d');
      
      // 绘制仓库地图 - 简单示例
      ctx.fillStyle = '#f5f7fa';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      // 绘制货架
      ctx.fillStyle = '#dcdfe6';
      for(let i = 0; i < 5; i++) {
        for(let j = 0; j < 3; j++) {
          ctx.fillRect(100 + i * 80, 100 + j * 80, 50, 20);
        }
      }
      
      // 绘制设备位置
      this.devices.forEach(device => {
        ctx.beginPath();
        ctx.arc(device.position.x, device.position.y, 10, 0, Math.PI * 2);
        
        if (device.status === 'offline') {
          ctx.fillStyle = '#909399';
        } else if (device.active) {
          ctx.fillStyle = '#E6A23C';
        } else {
          ctx.fillStyle = '#67C23A';
        }
        
        ctx.fill();
        
        // 添加设备标签
        ctx.font = '12px Arial';
        ctx.fillStyle = '#606266';
        ctx.fillText(device.name, device.position.x - 20, device.position.y - 15);
      });
    },
    handleMapCommand(id) {
      this.currentWarehouse = id;
      this.$message.success(`已切换到${this.warehouses.find(w => w.id === id).name}`);
      this.initMap();
    },
    zoomIn() {
      if (this.mapZoom < 2) {
        this.mapZoom += 0.2;
        this.updateMapZoom();
      }
    },
    zoomOut() {
      if (this.mapZoom > 0.5) {
        this.mapZoom -= 0.2;
        this.updateMapZoom();
      }
    },
    resetMap() {
      this.mapZoom = 1;
      this.updateMapZoom();
    },
    updateMapZoom() {
      const container = this.$refs.mapContainer;
      const canvas = container.querySelector('canvas');
      if (canvas) {
        canvas.style.transform = `scale(${this.mapZoom})`;
        canvas.style.transformOrigin = 'center center';
      }
    },
    
    // 设备状态相关
    refreshDeviceStatus() {
      // 模拟刷新设备状态
      this.$message.success('设备状态已刷新');
      // 随机更改一些设备状态，模拟刷新效果
      const randomDevice = this.devices[Math.floor(Math.random() * this.devices.length)];
      randomDevice.active = !randomDevice.active;
      this.initMap();
    },
    handleDeviceClick(row) {
      this.selectedDevice = row;
      this.deviceOperationVisible = true;
      
      // 根据设备类型初始化相关控制参数
      if (row.type === '语音设备') {
        this.voiceContent = '';
        this.voiceVolume = 7;
      } else if (row.type === '灯光设备') {
        this.lightColor = '#FF9900';
        this.lightBrightness = 80;
        this.lightMode = 'constant';
      } else if (row.type === 'AR设备') {
        this.arTarget = '';
        this.arStyle = 'arrow';
      }
    },
    getDeviceRowClass({row}) {
      if (row.active) return 'device-active';
      if (row.status === 'offline') return 'device-offline';
      return '';
    },
    getDeviceTypeTag(type) {
      const typeMap = {
        '语音设备': 'primary',
        '灯光设备': 'warning',
        'AR设备': 'info'
      };
      return typeMap[type] || '';
    },
    showDeviceOperations(device) {
      this.selectedDevice = device;
      this.deviceOperationVisible = true;
    },
    
    // 设备控制方法
    controlVoiceDevice() {
      this.addActivityLog('info', `发送语音播报到 ${this.selectedDevice.name}: "${this.voiceContent}"`);
      this.$message.success('语音播报指令已发送');
      this.deviceOperationVisible = false;
    },
    testVoiceDevice() {
      this.addActivityLog('info', `测试 ${this.selectedDevice.name}`);
      this.$message.info('测试音频已发送到设备');
    },
    controlLightDevice() {
      const modeText = this.lightMode === 'constant' ? '常亮' : this.lightMode === 'slow' ? '慢闪' : '快闪';
      this.addActivityLog('info', `控制 ${this.selectedDevice.name} 灯光：${modeText}，亮度 ${this.lightBrightness}%`);
      this.$message.success('灯光控制指令已发送');
      this.deviceOperationVisible = false;
    },
    testLightDevice() {
      this.addActivityLog('info', `测试 ${this.selectedDevice.name}`);
      this.$message.info('测试指令已发送到灯光设备');
    },
    controlARDevice() {
      const target = this.arTargets.find(t => t.id === this.arTarget);
      this.addActivityLog('info', `发送AR导航指令到 ${this.selectedDevice.name}，目标: ${target.name}`);
      this.$message.success('AR导航指令已发送');
      this.deviceOperationVisible = false;
    },
    testARDevice() {
      this.addActivityLog('info', `测试 ${this.selectedDevice.name}`);
      this.$message.info('测试指令已发送到AR设备');
    },
    
    // 活动日志相关
    addActivityLog(type, message) {
      this.activityLogs.unshift({
        type,
        time: new Date().getTime(),
        message
      });
      
      // 保留最近100条日志
      if (this.activityLogs.length > 100) {
        this.activityLogs.pop();
      }
      
      // 滚动到顶部
      this.$nextTick(() => {
        if (this.$refs.logContainer) {
          this.$refs.logContainer.scrollTop = 0;
        }
      });
    },
    generateActivityLogs() {
      // 生成一些随机的活动日志用于展示
      const activities = [
        '设备状态检测',
        '语音播报请求',
        '灯光控制请求',
        'AR导航请求',
        '设备离线告警',
        '设备恢复在线'
      ];
      
      const types = ['info', 'warning', 'error'];
      
      for (let i = 0; i < 20; i++) {
        const activity = activities[Math.floor(Math.random() * activities.length)];
        const type = types[Math.floor(Math.random() * types.length)];
        const time = new Date().getTime() - i * 30000 - Math.random() * 30000;
        
        this.activityLogs.push({
          type,
          time,
          message: `${this.devices[i % this.devices.length].name}: ${activity}`
        });
      }
    },
    formatTime(timestamp) {
      const date = new Date(timestamp);
      return date.getHours().toString().padStart(2, '0') + ':' +
             date.getMinutes().toString().padStart(2, '0') + ':' +
             date.getSeconds().toString().padStart(2, '0');
    },
    formatDateTime(timestamp) {
      const date = new Date(timestamp);
      return date.getFullYear() + '-' +
             (date.getMonth() + 1).toString().padStart(2, '0') + '-' +
             date.getDate().toString().padStart(2, '0') + ' ' +
             date.getHours().toString().padStart(2, '0') + ':' +
             date.getMinutes().toString().padStart(2, '0');
    },
    toggleAutoRefresh(value) {
      if (value) {
        this.startAutoRefresh();
      } else {
        this.stopAutoRefresh();
      }
    },
    startAutoRefresh() {
      this.refreshTimer = setInterval(() => {
        // 模拟随机生成活动日志
        const randomDevice = this.devices[Math.floor(Math.random() * this.devices.length)];
        const randomAction = Math.random() > 0.7 ? '状态变更' : Math.random() > 0.3 ? '数据上报' : '操作执行';
        this.addActivityLog('info', `${randomDevice.name}: ${randomAction}`);