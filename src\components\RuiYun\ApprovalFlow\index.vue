<template>
  <div class="approval-flow-container">
    <!-- 发起审批对话框 -->
    <el-dialog :title="title" :visible.sync="dialogVisible" width="650px" append-to-body>
      <el-form ref="approvalForm" :model="form" :rules="rules" label-width="120px">
        <!-- 工作流选择 -->
        <el-form-item label="审批流程" prop="workflowCode">
          <el-select v-model="form.workflowCode" placeholder="请选择审批流程" clearable style="width: 100%">
            <el-option
              v-for="item in workflowOptions"
              :key="item.workflowCode"
              :label="item.workflowName"
              :value="item.workflowCode">
            </el-option>
          </el-select>
        </el-form-item>
        
        <!-- 标题 -->
        <el-form-item label="审批标题" prop="businessTitle">
          <el-input v-model="form.businessTitle" placeholder="请输入审批标题" />
        </el-form-item>
        
        <!-- 优先级 -->
        <el-form-item label="优先级" prop="priority">
          <el-select v-model="form.priority" placeholder="请选择优先级">
            <el-option label="高" value="high" />
            <el-option label="中" value="medium" />
            <el-option label="低" value="low" />
          </el-select>
        </el-form-item>
        
        <!-- 备注 -->
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" :rows="3" />
        </el-form-item>
        
        <!-- 业务数据展示区域，由插槽实现 -->
        <slot name="business-form"></slot>
      </el-form>
      
      <!-- 流程图预览 -->
      <div v-if="showDiagram && selectedWorkflow" class="workflow-diagram">
        <h4>流程图预览</h4>
        <div id="workflow-preview" style="width: 100%; height: 200px; border: 1px solid #ddd; background-color: #f5f7fa;"></div>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button v-if="showPreview" type="primary" @click="preview">预 览</el-button>
        <el-button type="success" @click="submit">提 交</el-button>
      </div>
    </el-dialog>
    
    <!-- 审批记录对话框 -->
    <el-dialog title="审批记录" :visible.sync="recordsDialogVisible" width="700px" append-to-body>
      <el-tabs v-model="recordsActiveTab">
        <el-tab-pane label="审批记录" name="records">
          <el-timeline>
            <el-timeline-item v-for="(record, index) in approvalRecords" :key="index" 
              :type="getTimelineItemType(record.operationType)" 
              :timestamp="record.operationTime">
              <el-card shadow="hover" size="small">
                <h4>{{ getOperationTypeText(record.operationType) }}</h4>
                <p>操作人: {{ record.operationUserName }}</p>
                <p v-if="record.approvalOpinion">意见: {{ record.approvalOpinion }}</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </el-tab-pane>
        
        <el-tab-pane label="流程图" name="diagram">
          <div id="instance-diagram" style="width: 100%; height: 300px; border: 1px solid #ddd;"></div>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
    
    <!-- 审批状态标签组件 -->
    <span v-if="isDisplayMode" :class="['approval-status', getStatusClass(approvalStatus)]">
      <i :class="getStatusIcon(approvalStatus)"></i>
      {{ getStatusText(approvalStatus) }}
      <el-button v-if="approvalStatus !== 'approved' && approvalStatus !== 'canceled'" type="text" @click="showApprovalRecords">查看详情</el-button>
    </span>
  </div>
</template>

<script>
import { 
  startProcess, 
  getWorkflowOptions, 
  getWorkflowDiagram, 
  getApprovalHistory,
  getInstanceDiagram,
  getLatestInstance
} from "@/api/approval/workflow";
import * as d3 from 'd3';

export default {
  name: "ApprovalFlow",
  props: {
    // 显示模式：'form' 表示发起审批表单模式，'display' 表示仅显示审批状态模式
    mode: {
      type: String,
      default: 'form'
    },
    // 业务ID
    businessId: {
      type: String,
      required: true
    },
    // 业务类型
    businessType: {
      type: String,
      required: true
    },
    // 对话框标题
    title: {
      type: String,
      default: '发起审批'
    },
    // 是否显示预览按钮
    showPreview: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 对话框可见性
      dialogVisible: false,
      // 表单数据
      form: {
        businessId: '',
        businessType: '',
        businessTitle: '',
        workflowCode: '',
        priority: 'medium',
        remark: ''
      },
      // 表单校验规则
      rules: {
        workflowCode: [
          { required: true, message: '请选择审批流程', trigger: 'change' }
        ],
        businessTitle: [
          { required: true, message: '请输入审批标题', trigger: 'blur' }
        ]
      },
      // 工作流选项
      workflowOptions: [],
      // 选中的工作流
      selectedWorkflow: null,
      // 是否显示流程图
      showDiagram: false,
      // 审批记录对话框
      recordsDialogVisible: false,
      recordsActiveTab: 'records',
      approvalRecords: [],
      // 审批状态
      approvalStatus: '',
      // 审批实例
      approvalInstance: null,
      // 流程图数据
      diagramData: null
    };
  },
  computed: {
    // 是否处于显示模式
    isDisplayMode() {
      return this.mode === 'display';
    }
  },
  watch: {
    // 监听工作流选择变化
    'form.workflowCode': {
      handler(val) {
        if (val) {
          this.loadWorkflowDiagram(val);
        } else {
          this.selectedWorkflow = null;
          this.showDiagram = false;
        }
      }
    }
  },
  created() {
    // 如果是显示模式，加载审批状态
    if (this.isDisplayMode) {
      this.loadApprovalStatus();
    }
    
    // 加载工作流选项
    this.loadWorkflowOptions();
  },
  methods: {
    /**
     * 显示发起审批对话框
     * @param {Object} businessData 业务数据
     */
    showDialog(businessData = {}) {
      // 初始化表单
      this.form = {
        businessId: this.businessId,
        businessType: this.businessType,
        businessTitle: businessData.title || '',
        workflowCode: '',
        priority: 'medium',
        remark: ''
      };
      
      this.dialogVisible = true;
    },
    
    /**
     * 加载工作流选项
     */
    loadWorkflowOptions() {
      getWorkflowOptions().then(res => {
        if (res.code === 200) {
          // 使用Map对象确保每个业务类型只出现一次
          const workflowMap = new Map();
          
          // 只保留当前业务类型的选项
          res.data.filter(item => item.businessType === this.businessType).forEach(item => {
            if (!workflowMap.has(item.workflowCode)) {
              workflowMap.set(item.workflowCode, item);
            }
          });
          
          // 将Map转换回数组
          this.workflowOptions = Array.from(workflowMap.values());
        }
      });
    },
    
    /**
     * 加载工作流程图
     */
    loadWorkflowDiagram(workflowCode) {
      const workflow = this.workflowOptions.find(item => item.workflowCode === workflowCode);
      if (workflow) {
        this.selectedWorkflow = workflow;
        
        getWorkflowDiagram(workflow.workflowId).then(res => {
          if (res.code === 200) {
            this.diagramData = res.data;
            this.showDiagram = true;
            
            this.$nextTick(() => {
              this.renderWorkflowDiagram();
            });
          }
        });
      }
    },
    
    /**
     * 加载审批状态
     */
    loadApprovalStatus() {
      getLatestInstance({
        businessId: this.businessId,
        businessType: this.businessType
      }).then(res => {
        if (res.code === 200 && res.data) {
          this.approvalInstance = res.data;
          this.approvalStatus = res.data.status;
        } else {
          this.approvalStatus = 'none';
        }
      });
    },
    
    /**
     * 预览审批流程
     */
    preview() {
      this.$refs.approvalForm.validate((valid) => {
        if (valid) {
          // 触发预览事件，由父组件处理
          this.$emit('preview', this.form);
        }
      });
    },
    
    /**
     * 提交审批
     */
    submit() {
      this.$refs.approvalForm.validate((valid) => {
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '正在提交审批...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          
          startProcess(this.form).then(res => {
            loading.close();
            
            if (res.code === 200) {
              this.$modal.msgSuccess('审批流程启动成功');
              this.dialogVisible = false;
              
              // 触发提交成功事件
              this.$emit('submit-success', {
                instanceId: res.data,
                form: this.form
              });
              
              // 重新加载审批状态
              this.loadApprovalStatus();
            } else {
              this.$modal.msgError(res.msg || '提交失败');
            }
          }).catch(() => {
            loading.close();
            this.$modal.msgError('提交失败，请稍后重试');
          });
        }
      });
    },
    
    /**
     * 取消
     */
    cancel() {
      this.dialogVisible = false;
      this.$emit('cancel');
    },
    
    /**
     * 显示审批记录
     */
    showApprovalRecords() {
      // 加载审批记录
      getApprovalHistory({
        businessId: this.businessId,
        businessType: this.businessType
      }).then(res => {
        if (res.code === 200) {
          this.approvalRecords = res.data.records || [];
          this.diagramData = res.data.diagramData || null;
          
          this.recordsDialogVisible = true;
          this.recordsActiveTab = 'records';
          
          this.$nextTick(() => {
            if (this.diagramData && this.diagramData.nodes) {
              this.renderInstanceDiagram();
            }
          });
        }
      });
    },
    
    /**
     * 获取审批状态文本
     */
    getStatusText(status) {
      switch (status) {
        case '0': return '审批中';
        case '1': return '已通过';
        case '2': return '已驳回';
        case '3': return '已撤销';
        case '4': return '已终止';
        case 'approved': return '已通过';
        case 'rejected': return '已驳回';
        case 'pending': return '审批中';
        case 'canceled': return '已撤销';
        case 'none': return '未提交';
        default: return '未知状态';
      }
    },
    
    /**
     * 获取审批状态类名
     */
    getStatusClass(status) {
      switch (status) {
        case '0': 
        case 'pending': return 'pending';
        case '1':
        case 'approved': return 'approved';
        case '2':
        case 'rejected': return 'rejected';
        case '3':
        case '4':
        case 'canceled': return 'canceled';
        case 'none': return 'none';
        default: return '';
      }
    },
    
    /**
     * 获取审批状态图标
     */
    getStatusIcon(status) {
      switch (status) {
        case '0':
        case 'pending': return 'el-icon-loading';
        case '1':
        case 'approved': return 'el-icon-success';
        case '2':
        case 'rejected': return 'el-icon-error';
        case '3':
        case '4':
        case 'canceled': return 'el-icon-circle-close';
        case 'none': return 'el-icon-info';
        default: return 'el-icon-info';
      }
    },
    
    /**
     * 获取操作类型文本
     */
    getOperationTypeText(operationType) {
      switch (operationType) {
        case '0': return '提交申请';
        case '1': return '审批通过';
        case '2': return '审批驳回';
        case '3': return '撤销申请';
        case '4': return '转交审批';
        case '5': return '催办';
        default: return '未知操作';
      }
    },
    
    /**
     * 获取时间线项目类型
     */
    getTimelineItemType(operationType) {
      switch (operationType) {
        case '0': return 'primary';
        case '1': return 'success';
        case '2': return 'danger';
        case '3': return 'info';
        case '4': return 'warning';
        case '5': return '';
        default: return '';
      }
    },
    
    /**
     * 渲染工作流程图
     */
    renderWorkflowDiagram() {
      if (!this.diagramData || !this.diagramData.nodes || this.diagramData.nodes.length === 0) {
        return;
      }
      
      const container = document.getElementById('workflow-preview');
      if (!container) return;
      
      container.innerHTML = '';
      
      // 绘制流程图
      this.renderDiagram(container, this.diagramData);
    },
    
    /**
     * 渲染实例流程图
     */
    renderInstanceDiagram() {
      if (!this.diagramData || !this.diagramData.nodes || this.diagramData.nodes.length === 0) {
        return;
      }
      
      const container = document.getElementById('instance-diagram');
      if (!container) return;
      
      container.innerHTML = '';
      
      // 绘制流程图
      this.renderDiagram(container, this.diagramData, true);
    },
    
    /**
     * 通用流程图渲染
     */
    renderDiagram(container, diagramData, isInstance = false) {
      // 使用D3.js渲染流程图 - 圆点样式
      const width = container.clientWidth;
      const height = container.clientHeight;
      const nodeWidth = 120;   // 节点宽度
      const nodeHeight = 80;   // 节点高度
      const circleRadius = 18; // 圆点半径
      const textHeight = 30;   // 文字区域高度
      const circleY = textHeight + 25; // 圆点Y位置（文字下方）

      const svg = d3.select(container)
        .append('svg')
        .attr('width', width)
        .attr('height', height);

      const nodes = diagramData.nodes;
      const links = diagramData.links || [];

      // 计算节点位置
      const nodePositions = [];
      const xGap = Math.min(140, width / (nodes.length + 1));

      nodes.forEach((node, index) => {
        nodePositions.push({
          x: xGap * (index + 1),
          y: height / 2
        });
      });
      
      // 绘制连线
      const lineGenerator = d3.line()
        .curve(d3.curveBasis);

      links.forEach((link) => {
        const sourceIndex = nodes.findIndex(n => n.nodeId === link.source);
        const targetIndex = nodes.findIndex(n => n.nodeId === link.target);

        if (sourceIndex > -1 && targetIndex > -1) {
          const sourcePos = nodePositions[sourceIndex];
          const targetPos = nodePositions[targetIndex];
          const sourceNode = nodes[sourceIndex];
          const targetNode = nodes[targetIndex];

          // 连接线从圆点中心到圆点中心
          const sourceCircleX = sourcePos.x + nodeWidth / 2;
          const targetCircleX = targetPos.x + nodeWidth / 2;
          const sourceCircleY = sourcePos.y + circleY; // 使用定义的圆点Y位置
          const targetCircleY = targetPos.y + circleY; // 使用定义的圆点Y位置

          const points = [
            [sourceCircleX + circleRadius, sourceCircleY],
            [sourceCircleX + (targetCircleX - sourceCircleX) / 2, sourceCircleY],
            [sourceCircleX + (targetCircleX - sourceCircleX) / 2, targetCircleY],
            [targetCircleX - circleRadius, targetCircleY]
          ];

          // 获取连接线样式
          const lineStyle = this.getConnectionLineStyle(sourceNode, targetNode, isInstance);

          svg.append('path')
            .attr('d', lineGenerator(points))
            .attr('stroke', lineStyle.color)
            .attr('stroke-width', 2)
            .attr('stroke-dasharray', lineStyle.dashArray)
            .attr('fill', 'none')
            .attr('marker-end', 'url(#arrowhead)');
        }
      });
      
      // 添加箭头标记
      svg.append('defs').append('marker')
        .attr('id', 'arrowhead')
        .attr('viewBox', '0 -5 10 10')
        .attr('refX', 8)
        .attr('refY', 0)
        .attr('orient', 'auto')
        .attr('markerWidth', 6)
        .attr('markerHeight', 6)
        .append('path')
        .attr('d', 'M0,-5L10,0L0,5')
        .attr('fill', '#999');
      
      // 绘制节点
      nodes.forEach((node, index) => {
        const g = svg.append('g')
          .attr('transform', `translate(${nodePositions[index].x - nodeWidth / 2}, ${nodePositions[index].y - nodeHeight / 2})`);

        // 获取节点颜色
        let nodeColor;

        if (isInstance) {
          // 实例流程图，根据节点状态显示颜色
          switch (node.status) {
            case 'APPROVED':
              nodeColor = '#67C23A';  // 已通过 - 绿色
              break;
            case 'ACTIVE':
              nodeColor = '#409EFF';  // 当前节点 - 蓝色
              break;
            case 'REJECTED':
              nodeColor = '#F56C6C';  // 已驳回 - 红色
              break;
            case 'CANCELED':
              nodeColor = '#909399';  // 已撤销 - 灰色
              break;
            case 'WAITING':
            default:
              nodeColor = '#C0C4CC';  // 等待中 - 浅灰色
              break;
          }
        } else {
          // 普通流程图，根据节点类型显示颜色
          nodeColor = this.getNodeColor(node.nodeType);
        }

        // 节点文本（上方）
        g.append('text')
          .attr('x', nodeWidth / 2)
          .attr('y', 12)
          .attr('text-anchor', 'middle')
          .attr('dominant-baseline', 'middle')
          .attr('fill', '#333')
          .attr('font-size', '12px')
          .attr('font-weight', 'bold')
          .text(node.nodeName);

        // 圆点（下方）
        const circleRadius = 18;
        const circleY = textHeight + 25;

        const circle = g.append('circle')
          .attr('cx', nodeWidth / 2)
          .attr('cy', circleY)
          .attr('r', circleRadius)
          .attr('fill', nodeColor)
          .attr('stroke', '#fff')
          .attr('stroke-width', 2);

        // 为开始节点添加脉冲动画效果
        if (!isInstance && node.nodeType === '0') {
          // 添加脉冲外圈
          const pulseRing = g.append('circle')
            .attr('cx', nodeWidth / 2)
            .attr('cy', circleY)
            .attr('r', circleRadius)
            .attr('fill', 'none')
            .attr('stroke', nodeColor)
            .attr('stroke-width', 2)
            .attr('opacity', 0.8);

          // 创建脉冲动画函数
          const createPulseAnimation = (element) => {
            element
              .transition()
              .duration(1500)
              .ease(d3.easeLinear)
              .attr('r', circleRadius + 8)
              .attr('opacity', 0)
              .on('end', () => {
                // 动画结束后重新开始
                element
                  .attr('r', circleRadius)
                  .attr('opacity', 0.8);
                // 递归调用创建循环动画
                createPulseAnimation(element);
              });
          };

          // 启动脉冲动画
          createPulseAnimation(pulseRing);
        }

        // 为审批节点添加旋转动画效果
        if (!isInstance && node.nodeType === '1') {
          // 添加旋转外圈
          const rotateRing = g.append('circle')
            .attr('cx', nodeWidth / 2)
            .attr('cy', circleY)
            .attr('r', circleRadius + 3)
            .attr('fill', 'none')
            .attr('stroke', nodeColor)
            .attr('stroke-width', 2)
            .attr('stroke-dasharray', '5,3')
            .attr('opacity', 0.6);

          // 创建旋转动画函数
          const createRotateAnimation = (element) => {
            element
              .transition()
              .duration(3000)
              .ease(d3.easeLinear)
              .attrTween('transform', () => {
                return d3.interpolateString(
                  `rotate(0 ${nodeWidth / 2} ${circleY})`,
                  `rotate(360 ${nodeWidth / 2} ${circleY})`
                );
              })
              .on('end', () => {
                // 动画结束后重新开始
                createRotateAnimation(element);
              });
          };

          // 启动旋转动画
          createRotateAnimation(rotateRing);
        }

        // 状态图标（圆点内）
        let statusIcon = '';
        if (isInstance) {
          // 实例流程图，根据节点状态显示图标
          switch (node.status) {
            case 'APPROVED':
              statusIcon = '✓';  // 已通过
              break;
            case 'ACTIVE':
              statusIcon = '●';  // 当前节点
              break;
            case 'REJECTED':
              statusIcon = '✗';  // 已驳回
              break;
            case 'CANCELED':
              statusIcon = '⊘';  // 已撤销
              break;
            case 'WAITING':
            default:
              statusIcon = '○';  // 等待中
              break;
          }
        } else {
          // 普通流程图，根据节点类型显示不同图标
          switch (node.nodeType) {
            case '0': statusIcon = '▶'; break; // 开始节点
            case '1': statusIcon = '●'; break; // 审批节点
            case '2': statusIcon = '■'; break; // 结束节点
            default: statusIcon = '●';
          }
        }

        g.append('text')
          .attr('x', nodeWidth / 2)
          .attr('y', circleY + 1)
          .attr('text-anchor', 'middle')
          .attr('dominant-baseline', 'middle')
          .attr('fill', '#fff')
          .attr('font-size', '12px')
          .attr('font-weight', 'bold')
          .text(statusIcon);

        // 如果是实例流程图且是当前节点，添加一个外圈动画
        if (isInstance && node.nodeId === this.approvalInstance?.currentNodeId) {
          const activeRing = g.append('circle')
            .attr('cx', nodeWidth / 2)
            .attr('cy', circleY)
            .attr('r', circleRadius + 3)
            .attr('fill', 'none')
            .attr('stroke', '#409EFF')
            .attr('stroke-width', 2)
            .attr('stroke-dasharray', '5,3')
            .attr('opacity', 0.8);

          // 当前节点脉冲动画
          activeRing
            .transition()
            .duration(2000)
            .ease(d3.easeLinear)
            .attr('r', circleRadius + 8)
            .attr('opacity', 0)
            .on('end', function() {
              d3.select(this)
                .attr('r', circleRadius + 3)
                .attr('opacity', 0.8)
                .transition()
                .duration(2000)
                .ease(d3.easeLinear)
                .attr('r', circleRadius + 8)
                .attr('opacity', 0)
                .on('end', arguments.callee);
            });
        }
      });
    },
    
    /**
     * 获取节点颜色
     */
    getNodeColor(nodeType) {
      switch (nodeType) {
        case '0': return '#409EFF'; // 开始节点
        case '1': return '#909399'; // 审批节点
        case '2': return '#67C23A'; // 结束节点
        default: return '#909399';
      }
    },

    /**
     * 获取连接线样式
     */
    getConnectionLineStyle(sourceNode, targetNode, isInstance = false) {
      if (!isInstance) {
        // 普通流程图，使用默认样式
        return {
          color: '#999',
          dashArray: ''
        };
      }

      // 实例流程图，根据节点状态设置连接线样式
      const sourceCompleted = sourceNode.status === 'APPROVED';
      const targetCompleted = targetNode.status === 'APPROVED';
      const targetIsActive = targetNode.status === 'ACTIVE';
      const targetIsEnd = targetNode.nodeType === '2'; // 结束节点

      // 如果源节点已完成
      if (sourceCompleted) {
        // 如果目标节点也已完成
        if (targetCompleted) {
          return {
            color: '#67C23A', // 绿色实线 - 已完成的连接
            dashArray: ''
          };
        }
        // 如果目标节点是当前活动节点
        else if (targetIsActive) {
          return {
            color: '#409EFF', // 蓝色虚线 - 进行中的连接
            dashArray: '5,5'
          };
        }
        // 如果目标节点是结束节点且所有审批节点都已完成
        else if (targetIsEnd && this.isAllApprovalNodesCompleted()) {
          return {
            color: '#67C23A', // 绿色实线 - 流程完成
            dashArray: ''
          };
        }
      }

      // 默认样式（源节点未完成或其他情况）
      return {
        color: '#C0C4CC', // 浅灰色实线 - 未激活的连接
        dashArray: ''
      };
    },

    /**
     * 检查所有审批节点是否都已完成
     */
    isAllApprovalNodesCompleted() {
      if (!this.diagramData || !this.diagramData.nodes) {
        return false;
      }

      // 获取所有审批节点（nodeType='1'）
      const approvalNodes = this.diagramData.nodes.filter(node => node.nodeType === '1');

      // 检查是否所有审批节点都已完成
      return approvalNodes.every(node => node.status === 'APPROVED');
    }
  }
};
</script>

<style scoped>
.workflow-diagram {
  margin-top: 20px;
  border-top: 1px dashed #ddd;
  padding-top: 15px;
}

.approval-status {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.approval-status i {
  margin-right: 4px;
}

.approval-status.pending {
  background-color: #e6f1fc;
  color: #409EFF;
}

.approval-status.approved {
  background-color: #e1f3d8;
  color: #67C23A;
}

.approval-status.rejected {
  background-color: #fde2e2;
  color: #F56C6C;
}

.approval-status.canceled {
  background-color: #f4f4f5;
  color: #909399;
}

.approval-status.none {
  background-color: #f4f4f5;
  color: #909399;
}
</style> 