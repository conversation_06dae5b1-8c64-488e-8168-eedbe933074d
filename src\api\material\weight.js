import request from '@/utils/request'

// 查询物料重量信息列表
export function listWeight(query) {
  return request({
    url: '/material/weight/list',
    method: 'get',
    params: query
  })
}

// 查询物料重量信息详细
export function getWeight(id) {
  return request({
    url: '/material/weight/' + id,
    method: 'get'
  })
}

// 根据物料编码查询物料重量信息
export function getWeightByMaterialCode(materialCode) {
  return request({
    url: '/material/weight/byCode/' + materialCode,
    method: 'get'
  })
}

// 根据物料ID查询物料重量信息
export function getWeightByMaterialId(materialId) {
  return request({
    url: '/material/weight/byMaterialId/' + materialId,
    method: 'get'
  })
}

// 获取最新动态重量信息
export function getDynamicWeight(materialId) {
  return request({
    url: '/material/weight/dynamic/' + materialId,
    method: 'get'
  })
}

// 新增物料重量信息
export function addWeight(data) {
  return request({
    url: '/material/weight',
    method: 'post',
    data: data
  })
}

// 修改物料重量信息
export function updateWeight(data) {
  return request({
    url: '/material/weight',
    method: 'put',
    data: data
  })
}

// 校准物料重量
export function calibrateWeight(data) {
  return request({
    url: '/material/weight/calibrate',
    method: 'put',
    data: data
  })
}

// 删除物料重量信息
export function delWeight(id) {
  return request({
    url: '/material/weight/' + id,
    method: 'delete'
  })
}
