<template>
  <div class="app-container">
    <!-- 报表筛选条件 -->
    <el-card class="filter-container" shadow="hover">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="100px">
        <el-row>
          <el-col :span="6">
            <el-form-item label="盘点时间" prop="dateRange">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                style="width: 100%"
                @change="handleDateRangeChange">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="盘点类型" prop="inventoryType">
              <el-select v-model="queryParams.inventoryType" placeholder="请选择盘点类型" clearable style="width: 100%">
                <el-option label="全面盘点" value="0" />
                <el-option label="抽样盘点" value="1" />
                <el-option label="动态盘点" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="仓库" prop="warehouseId">
              <el-select v-model="queryParams.warehouseId" placeholder="请选择仓库" clearable style="width: 100%">
                <el-option
                  v-for="warehouse in warehouseOptions"
                  :key="warehouse.id"
                  :label="warehouse.name"
                  :value="warehouse.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="generateReport">生成报表</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 报表概览 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon total">
              <i class="el-icon-files"></i>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ reportData.totalTasks }}</div>
              <div class="stat-label">盘点任务</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon materials">
              <i class="el-icon-goods"></i>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ reportData.totalMaterials }}</div>
              <div class="stat-label">盘点物料</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon accuracy">
              <i class="el-icon-success"></i>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ reportData.accuracy }}%</div>
              <div class="stat-label">盘点准确率</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon differences">
              <i class="el-icon-warning"></i>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ reportData.differenceItems }}</div>
              <div class="stat-label">差异物料</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表展示 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="12">
        <el-card class="chart-card" shadow="hover">
          <div slot="header" class="chart-header">
            <span>盘点完成趋势</span>
            <el-button type="text" icon="el-icon-download" @click="exportChart('trend')">导出</el-button>
          </div>
          <div id="trendChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card" shadow="hover">
          <div slot="header" class="chart-header">
            <span>差异分布</span>
            <el-button type="text" icon="el-icon-download" @click="exportChart('difference')">导出</el-button>
          </div>
          <div id="differenceChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细报表 -->
    <el-card class="table-container" shadow="hover">
      <div slot="header" class="table-header">
        <span>盘点详细报表</span>
        <div class="table-actions">
          <el-button type="primary" icon="el-icon-download" size="mini" @click="exportReport">导出报表</el-button>
          <el-button type="success" icon="el-icon-printer" size="mini" @click="printReport">打印报表</el-button>
        </div>
      </div>
      
      <el-table v-loading="loading" :data="reportList" border stripe>
        <el-table-column label="序号" type="index" width="60" align="center" />
        <el-table-column label="盘点单号" align="center" prop="inventoryNo" min-width="140" />
        <el-table-column label="盘点类型" align="center" prop="inventoryType" width="100">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.inventoryType == '0'" type="primary" size="mini">全面盘点</el-tag>
            <el-tag v-else-if="scope.row.inventoryType == '1'" type="success" size="mini">抽样盘点</el-tag>
            <el-tag v-else-if="scope.row.inventoryType == '2'" type="warning" size="mini">动态盘点</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="盘点范围" align="center" min-width="160" />
        <el-table-column label="盘点物料数" align="center" prop="materialCount" width="100" />
        <el-table-column label="差异物料数" align="center" prop="differenceCount" width="100">
          <template slot-scope="scope">
            <span :style="{color: scope.row.differenceCount > 0 ? '#F56C6C' : '#67C23A'}">
              {{ scope.row.differenceCount }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="准确率" align="center" prop="accuracy" width="100">
          <template slot-scope="scope">
            <el-progress 
              :percentage="scope.row.accuracy" 
              :status="scope.row.accuracy >= 95 ? 'success' : (scope.row.accuracy >= 80 ? null : 'exception')"
              :stroke-width="8"
              :show-text="false">
            </el-progress>
            <div style="margin-top: 4px; font-size: 12px;">{{ scope.row.accuracy }}%</div>
          </template>
        </el-table-column>
        <el-table-column label="盘点时间" align="center" min-width="160">
          <template slot-scope="scope">
            <div>{{ parseTime(scope.row.startTime, '{y}-{m}-{d}') }}</div>
            <div style="color: #909399; font-size: 12px;">{{ parseTime(scope.row.endTime, '{y}-{m}-{d}') }}</div>
          </template>
        </el-table-column>
        <el-table-column label="操作人员" align="center" prop="operatorName" width="100" />
        <el-table-column label="操作" align="center" width="120">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="viewDetails(scope.row)">查看详情</el-button>
            <el-button size="mini" type="text" @click="exportSingle(scope.row)">导出</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="generateReport"
      />
    </el-card>
  </div>
</template>

<script>
export default {
  name: "InventoryReport",
  data() {
    return {
      loading: false,
      dateRange: [],
      warehouseOptions: [
        { id: 1, name: '主仓库' },
        { id: 2, name: '备件仓库' },
        { id: 3, name: '工具仓库' }
      ],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        startTime: null,
        endTime: null,
        inventoryType: null,
        warehouseId: null
      },
      reportData: {
        totalTasks: 0,
        totalMaterials: 0,
        accuracy: 0,
        differenceItems: 0
      },
      reportList: [],
      total: 0
    };
  },
  created() {
    this.initDefaultDate();
    this.generateReport();
  },
  methods: {
    /** 初始化默认日期 */
    initDefaultDate() {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30); // 30天前
      this.dateRange = [
        start.toISOString().split('T')[0],
        end.toISOString().split('T')[0]
      ];
      this.handleDateRangeChange(this.dateRange);
    },
    /** 日期范围变化 */
    handleDateRangeChange(dates) {
      if (dates && dates.length === 2) {
        this.queryParams.startTime = dates[0];
        this.queryParams.endTime = dates[1];
      } else {
        this.queryParams.startTime = null;
        this.queryParams.endTime = null;
      }
    },
    /** 生成报表 */
    generateReport() {
      this.loading = true;
      // 模拟数据
      setTimeout(() => {
        this.reportData = {
          totalTasks: 15,
          totalMaterials: 1250,
          accuracy: 96.8,
          differenceItems: 42
        };
        this.reportList = [
          {
            inventoryNo: 'PD202501130001',
            inventoryType: '0',
            materialCount: 150,
            differenceCount: 5,
            accuracy: 96.7,
            startTime: '2025-01-13 09:00:00',
            endTime: '2025-01-13 17:30:00',
            operatorName: '张三'
          }
        ];
        this.total = 1;
        this.loading = false;
        this.initCharts();
      }, 1000);
    },
    /** 初始化图表 */
    initCharts() {
      // 这里可以使用 ECharts 初始化图表
      console.log('初始化图表');
    },
    /** 重置查询 */
    resetQuery() {
      this.resetForm("queryForm");
      this.initDefaultDate();
      this.generateReport();
    },
    /** 导出报表 */
    exportReport() {
      this.$modal.msgSuccess("报表导出成功");
    },
    /** 打印报表 */
    printReport() {
      window.print();
    },
    /** 导出图表 */
    exportChart(type) {
      this.$modal.msgSuccess(`${type}图表导出成功`);
    },
    /** 查看详情 */
    viewDetails(row) {
      this.$router.push({
        path: '/inout/inventory-detail',
        query: { inventoryId: row.id, inventoryNo: row.inventoryNo }
      });
    },
    /** 导出单个 */
    exportSingle(row) {
      this.$modal.msgSuccess(`${row.inventoryNo}导出成功`);
    }
  }
};
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}

/* 统计卡片样式 */
.stat-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.materials {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.accuracy {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.differences {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

/* 图表卡片样式 */
.chart-card {
  height: 380px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-actions {
  display: flex;
  gap: 10px;
}
</style>
