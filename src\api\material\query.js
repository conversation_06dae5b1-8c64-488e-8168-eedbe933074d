import request from '@/utils/request'

// 查询物料库存快查列表
export function listQuery(query) {
  return request({
    url: '/material/query/list',
    method: 'get',
    params: query
  })
}

// 查询物料库存快查详细
export function getQuery(id) {
  return request({
    url: '/material/query/' + id,
    method: 'get'
  })
}

// 新增物料库存快查
export function addQuery(data) {
  return request({
    url: '/material/query',
    method: 'post',
    data: data
  })
}

// 修改物料库存快查
export function updateQuery(data) {
  return request({
    url: '/material/query',
    method: 'put',
    data: data
  })
}

// 删除物料库存快查
export function delQuery(id) {
  return request({
    url: '/material/query/' + id,
    method: 'delete'
  })
}

// 查询物料信息列表
export function listMaterialInfo(query) {
  return request({
    url: '/material/material/list',
    method: 'get',
    params: query
  })
}

// 查询物料清单列表
export function listMaterialBill(query) {
  return request({
    url: '/material/bill/list',
    method: 'get',
    params: query
  })
}

// 查询物料库存列表
export function listMaterialStock(query) {
  return request({
    url: '/material/stock/list',
    method: 'get',
    params: query
  })
}

// 查询物料审批列表
export function listMaterialApproval(query) {
  return request({
    url: '/material/approval/list',
    method: 'get',
    params: query
  })
}

// 查询物料审批详情
export function getMaterialApproval(id) {
  return request({
    url: '/material/approval/' + id,
    method: 'get'
  })
}

// 查询物料重量信息列表
export function listMaterialWeight(query) {
  return request({
    url: '/material/weight/list',
    method: 'get',
    params: {
      ...query,
      includeWeight: true
    }
  })
}
