import request from '@/utils/request'

// 查询人脸识别结果列表
export function listFaceResult(query) {
  return request({
    url: '/access/faceResult/list',
    method: 'get',
    params: query
  })
}

// 查询人脸识别结果详细
export function getFaceResult(resultId) {
  return request({
    url: '/access/faceResult/' + resultId,
    method: 'get'
  })
}

// 删除人脸识别结果
export function delFaceResult(resultId) {
  return request({
    url: '/access/faceResult/' + resultId,
    method: 'delete'
  })
}

// 导出人脸识别结果
export function exportFaceResult(query) {
  return request({
    url: '/access/faceResult/export',
    method: 'get',
    params: query
  })
}

// 清理人脸识别记录
export function cleanupFaceResult(data) {
  return request({
    url: '/access/faceResult/cleanup',
    method: 'post',
    data: data
  })
}

// 获取识别统计数据
export function getFaceResultStatistics(query) {
  return request({
    url: '/access/faceResult/statistics',
    method: 'get',
    params: query
  })
}

// 获取实时识别数据
export function getRealTimeFaceResult(query) {
  return request({
    url: '/access/faceResult/realtime',
    method: 'get',
    params: query
  })
}
