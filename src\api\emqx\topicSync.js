import request from '@/utils/request'

// 同步主题配置到EMQX
export function syncTopicsToEmqx() {
  return request({
    url: '/emqx/topic/sync/syncToEmqx',
    method: 'post'
  })
}

// 验证主题在EMQX中的状态
export function verifyTopicsInEmqx(topics) {
  return request({
    url: '/emqx/topic/sync/verify',
    method: 'post',
    data: topics
  })
}

// 为系统客户端订阅所有配置的主题
export function subscribeSystemTopics() {
  return request({
    url: '/emqx/topic/sync/subscribeSystem',
    method: 'post'
  })
}

// 创建测试订阅以激活主题
export function createTestSubscriptions(topics) {
  return request({
    url: '/emqx/topic/sync/createTestSubscriptions',
    method: 'post',
    data: topics
  })
}

// 发布测试消息到主题
export function publishTestMessage(topic, payload) {
  return request({
    url: '/emqx/topic/sync/publishTest',
    method: 'post',
    params: {
      topic: topic,
      payload: payload
    }
  })
}

// 获取主题在EMQX中的实际状态
export function getActualTopicStatus() {
  return request({
    url: '/emqx/topic/sync/actualStatus',
    method: 'get'
  })
}

// 比较配置的主题和EMQX中的主题
export function compareTopicsWithEmqx() {
  return request({
    url: '/emqx/topic/sync/compare',
    method: 'get'
  })
}

// 修复主题同步问题
export function fixTopicSyncIssues() {
  return request({
    url: '/emqx/topic/sync/fix',
    method: 'post'
  })
}

// 一键解决主题同步问题
export function oneClickFix() {
  return request({
    url: '/emqx/topic/sync/oneClickFix',
    method: 'post'
  })
}

// 获取主题同步状态概览
export function getTopicSyncOverview() {
  return request({
    url: '/emqx/topic/sync/overview',
    method: 'get'
  })
}

// 诊断主题格式问题
export function diagnoseTopicFormats() {
  return request({
    url: '/emqx/topic/sync/diagnose',
    method: 'get'
  })
}

// 转换主题格式
export function convertTopicFormat(data) {
  return request({
    url: '/emqx/topic/sync/convertFormat',
    method: 'post',
    data: data
  })
}
