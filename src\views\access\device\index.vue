<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards" v-if="showStats">
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon">
            <i class="el-icon-s-platform"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ statistics.total || 0 }}</div>
            <div class="stat-label">设备总数</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon success">
            <i class="el-icon-connection"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ statistics.online || 0 }}</div>
            <div class="stat-label">在线设备</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon warning">
            <i class="el-icon-warning"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ statistics.offline || 0 }}</div>
            <div class="stat-label">离线设备</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon info">
            <i class="el-icon-refresh"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ refreshTime }}</div>
            <div class="stat-label">刷新时间</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 设备类型统计 -->
    <el-row :gutter="20" class="stats-cards" v-if="showStats">
      <el-col :span="8">
        <div class="stat-card">
          <div class="stat-icon primary">
            <i class="el-icon-user"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ statistics.face_access || 0 }}</div>
            <div class="stat-label">人脸门禁</div>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="stat-card">
          <div class="stat-icon success">
            <i class="el-icon-lock"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ statistics.ip_lock || 0 }}</div>
            <div class="stat-label">IP磁力锁</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon info">
            <i class="el-icon-credit-card"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ statistics.card_reader || 0 }}</div>
            <div class="stat-label">刷卡器</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon warning">
            <i class="el-icon-s-platform"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ statistics.access_controller || 0 }}</div>
            <div class="stat-label">控制器</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="设备编号" prop="deviceCode">
        <el-input
          v-model="queryParams.deviceCode"
          placeholder="请输入设备编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备名称" prop="deviceName">
        <el-input
          v-model="queryParams.deviceName"
          placeholder="请输入设备名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备类型" prop="deviceType">
        <el-select v-model="queryParams.deviceType" placeholder="请选择设备类型" clearable>
          <el-option
            v-for="dict in dict.type.access_device_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="在线状态" prop="onlineStatus">
        <el-select v-model="queryParams.onlineStatus" placeholder="请选择在线状态" clearable>
          <el-option
            v-for="dict in dict.type.access_online_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="安装位置" prop="installLocation">
        <el-input
          v-model="queryParams.installLocation"
          placeholder="请输入安装位置"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="IP地址" prop="ipAddress">
        <el-input
          v-model="queryParams.ipAddress"
          placeholder="请输入IP地址"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['access:device:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['access:device:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['access:device:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['access:device:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-refresh"
          size="mini"
          @click="refreshData"
        >刷新</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-s-data"
          size="mini"
          @click="toggleStats"
        >{{ showStats ? '隐藏统计' : '显示统计' }}</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="deviceList" @selection-change="handleSelectionChange" row-key="id">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" width="80" />
      <el-table-column label="设备编号" align="center" prop="deviceCode" width="120" />
      <el-table-column label="设备名称" align="center" prop="deviceName" width="150" :show-overflow-tooltip="true" />
      <el-table-column label="设备类型" align="center" prop="deviceType" width="120">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.access_device_type" :value="scope.row.deviceType"/>
        </template>
      </el-table-column>
      <el-table-column label="安装位置" align="center" prop="installLocation" width="150" :show-overflow-tooltip="true" />
      <el-table-column label="在线状态" align="center" prop="onlineStatus" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.access_online_status" :value="scope.row.onlineStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="IP地址" align="center" prop="ipAddress" width="120" />
      <el-table-column label="MQTT客户端ID" align="center" prop="mqttClientId" width="150" :show-overflow-tooltip="true" />
      <el-table-column label="最后心跳时间" align="center" prop="lastHeartbeat" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.lastHeartbeat, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="300">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['access:device:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['access:device:remove']"
          >删除</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-connection"
            @click="handleTest(scope.row)"
            v-hasPermi="['access:device:test']"
          >测试</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="handleRestart(scope.row)"
            v-hasPermi="['access:device:restart']"
          >重启</el-button>
          <el-dropdown @command="(command) => handleControl(command, scope.row)" v-hasPermi="['access:device:control']">
            <el-button size="mini" type="text" icon="el-icon-s-operation">
              控制<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="open">开门</el-dropdown-item>
              <el-dropdown-item command="close">关门</el-dropdown-item>
              <el-dropdown-item command="unlock">解锁</el-dropdown-item>
              <el-dropdown-item command="lock">上锁</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改门禁设备对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备编号" prop="deviceCode">
              <el-input v-model="form.deviceCode" placeholder="请输入设备编号" :disabled="form.id != null" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备名称" prop="deviceName">
              <el-input v-model="form.deviceName" placeholder="请输入设备名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备类型" prop="deviceType">
              <el-select v-model="form.deviceType" placeholder="请选择设备类型">
                <el-option
                  v-for="dict in dict.type.access_device_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="安装位置" prop="installLocation">
              <el-input v-model="form.installLocation" placeholder="请输入安装位置" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="IP地址" prop="ipAddress">
              <el-input v-model="form.ipAddress" placeholder="请输入IP地址" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="端口号" prop="port">
              <el-input v-model="form.port" placeholder="请输入端口号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="MQTT用户名" prop="mqttUsername">
              <el-input v-model="form.mqttUsername" placeholder="请输入MQTT用户名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="MQTT密码" prop="mqttPassword">
              <el-input v-model="form.mqttPassword" placeholder="请输入MQTT密码" show-password />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="MQTT客户端ID" prop="mqttClientId">
              <el-input v-model="form.mqttClientId" placeholder="请输入MQTT客户端ID" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="授权码" prop="authCode">
              <el-input v-model="form.authCode" placeholder="请输入授权码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属库房" prop="warehouseId">
              <el-select v-model="form.warehouseId" placeholder="请选择所属库房">
                <el-option
                  v-for="item in warehouseOptions"
                  :key="item.warehouseId"
                  :label="item.warehouseName"
                  :value="item.warehouseId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备厂商" prop="manufacturer">
              <el-input v-model="form.manufacturer" placeholder="请输入设备厂商" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备型号" prop="deviceModel">
              <el-input v-model="form.deviceModel" placeholder="请输入设备型号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="固件版本" prop="firmwareVersion">
              <el-input v-model="form.firmwareVersion" placeholder="请输入固件版本" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in dict.type.sys_normal_disable"
                  :key="dict.value"
                  :label="parseInt(dict.value)"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDevice, getDevice, delDevice, addDevice, updateDevice, getDeviceStatistics, testDevice, restartDevice, controlDevice } from "@/api/access/device";
import { listWarehouse } from "@/api/warehouse/warehouse";

export default {
  name: "Device",
  dicts: ['sys_normal_disable', 'access_device_type', 'access_online_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 显示统计信息
      showStats: true,
      // 总条数
      total: 0,
      // 门禁设备表格数据
      deviceList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 库房选项
      warehouseOptions: [],
      // 统计数据
      statistics: {},
      // 刷新时间
      refreshTime: this.parseTime(new Date()),
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceCode: null,
        deviceName: null,
        deviceType: null,
        installLocation: null,
        onlineStatus: null,
        ipAddress: null,
        mqttClientId: null,
        warehouseId: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        deviceCode: [
          { required: true, message: "设备编号不能为空", trigger: "blur" }
        ],
        deviceName: [
          { required: true, message: "设备名称不能为空", trigger: "blur" }
        ],
        deviceType: [
          { required: true, message: "设备类型不能为空", trigger: "change" }
        ],
        ipAddress: [
          { pattern: /^(\d{1,3}\.){3}\d{1,3}$/, message: "请输入正确的IP地址", trigger: "blur" }
        ],
        port: [
          { pattern: /^\d+$/, message: "端口号必须为数字", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getWarehouseOptions();
    this.getStatistics();
  },
  methods: {
    /** 数据去重和验证 */
    validateAndDeduplicateData(data, keyField = 'id') {
      if (!Array.isArray(data)) return [];

      // 去重并确保每个项都有唯一的key
      const uniqueData = [];
      const seenKeys = new Set();

      data.forEach((item, index) => {
        let key = item[keyField];

        // 如果没有key或key重复，生成新的key
        if (!key || seenKeys.has(key)) {
          key = `${keyField}_${index}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
          item[keyField] = key;
        }

        seenKeys.add(key);
        uniqueData.push(item);
      });

      return uniqueData;
    },
    /** 查询门禁设备列表 */
    getList() {
      this.loading = true;
      listDevice(this.queryParams).then(response => {
        // 使用验证和去重函数处理数据
        this.deviceList = this.validateAndDeduplicateData(response.rows || [], 'id');
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        console.error('获取设备列表失败:', error);
        this.deviceList = [];
        this.total = 0;
        this.loading = false;
      });
    },
    /** 获取库房选项 */
    getWarehouseOptions() {
      listWarehouse().then(response => {
        // 使用验证和去重函数处理库房数据
        this.warehouseOptions = this.validateAndDeduplicateData(response.rows || [], 'warehouseId');
      }).catch(error => {
        console.error('获取库房选项失败:', error);
        this.warehouseOptions = [];
      });
    },
    /** 获取统计信息 */
    getStatistics() {
      getDeviceStatistics().then(response => {
        this.statistics = response.data;
        this.refreshTime = this.parseTime(new Date());
      });
    },
    /** 刷新数据 */
    refreshData() {
      this.getList();
      this.getStatistics();
      this.$message.success("数据已刷新");
    },
    /** 切换统计显示 */
    toggleStats() {
      this.showStats = !this.showStats;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        deviceCode: null,
        deviceName: null,
        deviceType: null,
        installLocation: null,
        onlineStatus: 0,
        ipAddress: null,
        port: null,
        mqttUsername: null,
        mqttPassword: null,
        mqttClientId: null,
        authCode: null,
        warehouseId: null,
        manufacturer: null,
        deviceModel: null,
        firmwareVersion: null,
        deviceConfig: null,
        status: 1,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加门禁设备";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getDevice(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改门禁设备";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateDevice(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
              this.getStatistics();
            });
          } else {
            addDevice(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
              this.getStatistics();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除门禁设备编号为"' + (row.deviceCode || '') + '"的数据项?').then(function() {
        return delDevice(ids);
      }).then(() => {
        this.getList();
        this.getStatistics();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 测试设备连接 */
    handleTest(row) {
      this.$modal.confirm('是否确认测试设备"' + row.deviceName + '"的连接?').then(function() {
        return testDevice(row.deviceCode);
      }).then(response => {
        this.$modal.msgSuccess("连接测试成功");
      }).catch(() => {});
    },
    /** 重启设备 */
    handleRestart(row) {
      this.$modal.confirm('是否确认重启设备"' + row.deviceName + '"?').then(function() {
        return restartDevice(row.deviceCode);
      }).then(response => {
        this.$modal.msgSuccess("重启指令已发送");
      }).catch(() => {});
    },
    /** 控制设备 */
    handleControl(command, row) {
      let actionName = '';
      switch (command) {
        case 'open': actionName = '开门'; break;
        case 'close': actionName = '关门'; break;
        case 'unlock': actionName = '解锁'; break;
        case 'lock': actionName = '上锁'; break;
        default: actionName = command;
      }
      
      this.$modal.confirm('是否确认对设备"' + row.deviceName + '"执行' + actionName + '操作?').then(function() {
        return controlDevice(row.deviceCode, { action: command });
      }).then(response => {
        this.$modal.msgSuccess(actionName + "指令已发送");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('access/device/export', {
        ...this.queryParams
      }, `门禁设备_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped>
.stats-cards {
  margin-bottom: 20px;
}
.stat-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  align-items: center;
}
.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #409EFF;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}
.stat-icon i {
  font-size: 30px;
}
.stat-content {
  flex: 1;
}
.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}
.stat-label {
  font-size: 14px;
  color: #606266;
}
.stat-icon.success {
  background-color: #67C23A;
}
.stat-icon.warning {
  background-color: #E6A23C;
}
.stat-icon.danger {
  background-color: #F56C6C;
}
.stat-icon.info {
  background-color: #909399;
}
.stat-icon.primary {
  background-color: #409EFF;
}
</style>
