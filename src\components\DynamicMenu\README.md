# 动态菜单图标系统

## 概述

这个动态菜单图标系统根据数据库中的菜单数据自动为每个菜单项分配相应的SVG图标。系统支持中文菜单名称到图标的智能映射，并提供了完整的图标库。

## 功能特性

- 🎯 **智能映射**: 根据菜单名称自动匹配相应的图标
- 🎨 **丰富图标**: 包含仓库管理、物料管理、门禁管理等专业图标
- 🔄 **动态更新**: 支持从数据库动态获取菜单数据
- 📱 **响应式**: 支持菜单折叠和展开
- 🛠️ **易扩展**: 可轻松添加新的图标映射

## 文件结构

```
src/components/DynamicMenu/
├── index.vue              # 动态图标映射工具
├── README.md             # 说明文档
src/assets/icons/svg/
├── warehouse.svg         # 仓库管理图标
├── material.svg          # 物料管理图标
├── approval.svg          # 审批管理图标
├── inout.svg            # 出入库管理图标
├── outmis.svg           # 出库防错图标
├── access.svg           # 门禁管理图标
├── video.svg            # 视频监控图标
├── video-camera.svg     # SIP视频服务图标
├── emqx.svg             # EMQX管理图标
└── dwms.svg             # 库房组态图标
```

## 使用方法

### 1. 在组件中使用

```javascript
import DynamicMenuIconMapper from '@/components/DynamicMenu'

export default {
  mixins: [DynamicMenuIconMapper],
  methods: {
    getIconForMenu(menuName) {
      return this.getMenuIcon(menuName)
    }
  }
}
```

### 2. 在模板中使用

```vue
<template>
  <svg-icon :icon-class="getMenuIcon(menuName)" />
</template>
```

## 图标映射表

### 主菜单图标

| 菜单名称 | 图标名称 | 描述 |
|---------|---------|------|
| 库房组态 | dwms | 库房组态管理 |
| 仓库管理 | warehouse | 仓库信息管理 |
| 物料管理 | material | 物料信息管理 |
| 审批管理 | approval | 审批流程管理 |
| 出入库管理 | inout | 出入库操作 |
| 出库防错 | outmis | 出库防错系统 |
| 智能导寻 | guide | 智能导寻系统 |
| 门禁管理 | access | 门禁控制系统 |
| 视频监控 | video | 视频监控系统 |
| EMQX管理 | emqx | MQTT消息管理 |
| SIP视频服务 | video-camera | SIP视频服务 |
| 系统工具 | tool | 系统工具集 |
| 系统监控 | monitor | 系统监控 |
| 系统管理 | system | 系统管理 |

### 子菜单图标

#### 系统管理子菜单
| 菜单名称 | 图标名称 | 描述 |
|---------|---------|------|
| 用户管理 | user | 用户信息管理 |
| 角色管理 | peoples | 角色权限管理 |
| 菜单管理 | tree-table | 菜单结构管理 |
| 部门管理 | tree | 部门组织管理 |
| 岗位管理 | post | 岗位信息管理 |
| 字典管理 | dict | 数据字典管理 |
| 参数设置 | edit | 系统参数配置 |
| 通知公告 | message | 公告信息管理 |
| 日志管理 | log | 系统日志管理 |

#### 仓库管理子菜单
| 菜单名称 | 图标名称 | 描述 |
|---------|---------|------|
| 仓库信息 | warehouse-info | 仓库基础信息 |
| 区域信息 | area | 仓库区域划分 |
| 货架信息 | shelf | 货架管理 |
| 货位信息 | location | 货位定位管理 |
| 容器信息 | container | 容器管理 |

#### 物料管理子菜单
| 菜单名称 | 图标名称 | 描述 |
|---------|---------|------|
| 物料库存快查 | inventory-search | 库存快速查询 |
| 物料编码映射 | code-mapping | 编码映射关系 |
| 物料信息 | material-info | 物料基础信息 |
| 物料库存 | inventory | 库存管理 |
| 物料分类 | category | 物料分类管理 |
| 物料重量信息 | weight | 重量信息管理 |

#### 门禁管理子菜单
| 菜单名称 | 图标名称 | 描述 |
|---------|---------|------|
| 设备管理 | device | 门禁设备管理 |
| 门禁记录 | access-record | 门禁通行记录 |
| 人脸授权 | face-auth | 人脸识别授权 |
| 识别记录 | recognition | 识别记录查询 |

#### 智能导寻子菜单
| 菜单名称 | 图标名称 | 描述 |
|---------|---------|------|
| 物品导寻 | item-search | 物品查找导航 |
| 导寻配置 | guide-config | 导寻系统配置 |
| 管控日志 | control-log | 管控操作日志 |
| 物品管理 | item-manage | 物品信息管理 |
| 协议管理 | protocol | 通信协议管理 |
| 命令管理 | command | 指令管理 |
| 连接管理 | connection | 连接状态管理 |

#### 出入库管理子菜单
| 菜单名称 | 图标名称 | 描述 |
|---------|---------|------|
| 出入库申请 | inout-apply | 出入库申请单 |
| 出入库执行 | inout-execute | 出入库执行 |
| 库房盘点 | inventory-check | 库存盘点 |
| 单据打印 | print | 单据打印管理 |
| 日志清理管理 | clean | 日志清理配置 |
| 打印模板管理 | template | 打印模板设计 |
| 单据打印日志 | print-log | 打印历史记录 |

## 添加新图标

### 1. 添加SVG图标文件

将新的SVG图标文件放入 `src/assets/icons/svg/` 目录。

### 2. 更新图标映射

在 `src/components/DynamicMenu/index.vue` 中的 `getMenuIcon` 方法中添加新的映射：

```javascript
const iconMap = {
  // 现有映射...
  '新菜单名称': 'new-icon-name'
}
```

### 3. 后端图标映射

在后端 `SysMenuServiceImpl.java` 的 `getDynamicIcon` 方法中也需要添加相应的映射。

## 测试

访问测试页面查看所有图标映射效果：
```
/test/dynamic-menu
```

## 注意事项

1. 图标文件名应使用小写字母和连字符
2. SVG图标应保持简洁，适合在菜单中显示
3. 新增图标映射时，前后端都需要同步更新
4. 默认图标为 `table`，当找不到匹配的图标时使用

## 技术实现

- **前端**: Vue.js + Element UI + SVG图标系统
- **后端**: Spring Boot + MyBatis + MySQL
- **图标**: 自定义SVG图标库
- **映射**: 基于菜单名称的智能匹配算法
