// 导入所有组件
import AdvancedGauge from './charts/AdvancedGauge.vue'
import RealtimeTrend from './charts/RealtimeTrend.vue'
import Device3D from './models/Device3D.vue'
import DataTable from './tables/DataTable.vue'

// 导入之前的组件（如果存在）
let Gauge, LineChart, BarChart, PieChart, StatusIndicator, ControlButton, DeviceIcon

try {
  Gauge = require('./charts/Gauge.vue').default
} catch (e) {
  console.warn('Gauge component not found')
}

try {
  LineChart = require('./charts/LineChart.vue').default
} catch (e) {
  console.warn('LineChart component not found')
}

try {
  BarChart = require('./charts/BarChart.vue').default
} catch (e) {
  console.warn('BarChart component not found')
}

try {
  PieChart = require('./charts/PieChart.vue').default
} catch (e) {
  console.warn('PieChart component not found')
}

try {
  StatusIndicator = require('./status/StatusIndicator.vue').default
} catch (e) {
  console.warn('StatusIndicator component not found')
}

try {
  ControlButton = require('./controls/ControlButton.vue').default
} catch (e) {
  console.warn('ControlButton component not found')
}

try {
  DeviceIcon = require('./devices/DeviceIcon.vue').default
} catch (e) {
  console.warn('DeviceIcon component not found')
}

// 组件配置
export const componentConfigs = {
  // 高级图表组件
  advancedGauge: {
    name: 'AdvancedGauge',
    component: AdvancedGauge,
    category: 'charts',
    title: '高级仪表盘',
    icon: 'el-icon-odometer',
    defaultProps: {
      width: 250,
      height: 250,
      config: {
        min: 0,
        max: 100,
        title: '高级仪表盘',
        unit: '',
        showValue: true,
        colors: [
          [0.3, '#67e0e3'],
          [0.7, '#37a2da'],
          [1, '#fd666d']
        ],
        animationDuration: 1000,
        showBackground: true,
        backgroundColor: '#e6e6e6'
      },
      value: 0
    },
    configSchema: [
      { prop: 'min', label: '最小值', type: 'number', default: 0 },
      { prop: 'max', label: '最大值', type: 'number', default: 100 },
      { prop: 'title', label: '标题', type: 'string', default: '高级仪表盘' },
      { prop: 'unit', label: '单位', type: 'string', default: '' },
      { prop: 'showValue', label: '显示数值', type: 'boolean', default: true },
      { prop: 'animationDuration', label: '动画时长(ms)', type: 'number', default: 1000 },
      { prop: 'showBackground', label: '显示背景', type: 'boolean', default: true }
    ]
  },
  
  realtimeTrend: {
    name: 'RealtimeTrend',
    component: RealtimeTrend,
    category: 'charts',
    title: '实时趋势图',
    icon: 'el-icon-data-line',
    defaultProps: {
      width: 500,
      height: 350,
      config: {
        title: '实时趋势图',
        showControls: true,
        yAxisName: '数值',
        unit: '',
        series: [
          { 
            name: '数据1', 
            color: '#409EFF', 
            baseValue: 50, 
            variance: 20,
            smooth: true,
            showArea: false
          }
        ]
      },
      dataSource: '',
      maxDataPoints: 100
    },
    configSchema: [
      { prop: 'title', label: '标题', type: 'string', default: '实时趋势图' },
      { prop: 'showControls', label: '显示控制器', type: 'boolean', default: true },
      { prop: 'yAxisName', label: 'Y轴名称', type: 'string', default: '数值' },
      { prop: 'unit', label: '单位', type: 'string', default: '' },
      { prop: 'dataSource', label: '数据源', type: 'string', default: '' },
      { prop: 'maxDataPoints', label: '最大数据点', type: 'number', default: 100 }
    ]
  },
  
  device3D: {
    name: 'Device3D',
    component: Device3D,
    category: 'models',
    title: '3D设备模型',
    icon: 'el-icon-box',
    defaultProps: {
      width: 300,
      height: 300,
      config: {
        deviceName: '3D设备',
        modelType: 'box',
        showControls: true,
        showInfo: true,
        autoRotate: false,
        backgroundColor: 0xf0f0f0,
        deviceColor: 0x409EFF,
        groundColor: 0xcccccc,
        scale: 1
      },
      status: 'online',
      temperature: 25
    },
    configSchema: [
      { prop: 'deviceName', label: '设备名称', type: 'string', default: '3D设备' },
      { prop: 'modelType', label: '模型类型', type: 'select', options: [
        { label: '立方体', value: 'box' },
        { label: '球体', value: 'sphere' },
        { label: '圆柱体', value: 'cylinder' },
        { label: '圆锥体', value: 'cone' },
        { label: 'GLTF模型', value: 'gltf' }
      ], default: 'box' },
      { prop: 'showControls', label: '显示控制器', type: 'boolean', default: true },
      { prop: 'showInfo', label: '显示信息', type: 'boolean', default: true },
      { prop: 'autoRotate', label: '自动旋转', type: 'boolean', default: false },
      { prop: 'modelUrl', label: '模型地址', type: 'string', default: '', condition: { prop: 'modelType', value: 'gltf' } },
      { prop: 'scale', label: '缩放比例', type: 'number', default: 1, min: 0.1, max: 5, step: 0.1 }
    ]
  },
  
  dataTable: {
    name: 'DataTable',
    component: DataTable,
    category: 'tables',
    title: '数据表格',
    icon: 'el-icon-s-grid',
    defaultProps: {
      width: 600,
      height: 400,
      config: {
        title: '数据表格',
        stripe: true,
        border: true,
        showSelection: false,
        showIndex: true,
        showPagination: true,
        showRefresh: true,
        showExport: true,
        size: 'small'
      },
      data: [
        { name: '设备1', value: 85, status: 'online' },
        { name: '设备2', value: 92, status: 'online' },
        { name: '设备3', value: 0, status: 'offline' }
      ],
      columns: [
        { prop: 'name', label: '名称', width: 120 },
        { prop: 'value', label: '数值', width: 100, type: 'number' },
        { prop: 'status', label: '状态', width: 100, type: 'status' }
      ]
    },
    configSchema: [
      { prop: 'title', label: '表格标题', type: 'string', default: '数据表格' },
      { prop: 'stripe', label: '斑马纹', type: 'boolean', default: true },
      { prop: 'border', label: '边框', type: 'boolean', default: true },
      { prop: 'showSelection', label: '显示选择', type: 'boolean', default: false },
      { prop: 'showIndex', label: '显示序号', type: 'boolean', default: true },
      { prop: 'showPagination', label: '显示分页', type: 'boolean', default: true },
      { prop: 'showRefresh', label: '显示刷新', type: 'boolean', default: true },
      { prop: 'showExport', label: '显示导出', type: 'boolean', default: true },
      { prop: 'size', label: '表格尺寸', type: 'select', options: [
        { label: '大', value: 'large' },
        { label: '中', value: 'medium' },
        { label: '小', value: 'small' },
        { label: '迷你', value: 'mini' }
      ], default: 'small' }
    ]
  }
}

// 如果基础组件存在，添加到配置中
if (Gauge) {
  componentConfigs.gauge = {
    name: 'Gauge',
    component: Gauge,
    category: 'charts',
    title: '仪表盘',
    icon: 'el-icon-odometer',
    defaultProps: {
      width: 200,
      height: 200,
      min: 0,
      max: 100,
      value: 0,
      unit: '',
      title: '仪表盘'
    }
  }
}

if (LineChart) {
  componentConfigs.lineChart = {
    name: 'LineChart',
    component: LineChart,
    category: 'charts',
    title: '折线图',
    icon: 'el-icon-data-line',
    defaultProps: {
      width: 400,
      height: 300,
      title: '折线图',
      xAxisData: [],
      seriesData: []
    }
  }
}

if (BarChart) {
  componentConfigs.barChart = {
    name: 'BarChart',
    component: BarChart,
    category: 'charts',
    title: '柱状图',
    icon: 'el-icon-data-board',
    defaultProps: {
      width: 400,
      height: 300,
      title: '柱状图',
      xAxisData: [],
      seriesData: []
    }
  }
}

if (PieChart) {
  componentConfigs.pieChart = {
    name: 'PieChart',
    component: PieChart,
    category: 'charts',
    title: '饼图',
    icon: 'el-icon-pie-chart',
    defaultProps: {
      width: 300,
      height: 300,
      title: '饼图',
      data: []
    }
  }
}

if (StatusIndicator) {
  componentConfigs.statusIndicator = {
    name: 'StatusIndicator',
    component: StatusIndicator,
    category: 'status',
    title: '状态指示器',
    icon: 'el-icon-success',
    defaultProps: {
      width: 100,
      height: 100,
      status: 'normal',
      label: '状态'
    }
  }
}

if (ControlButton) {
  componentConfigs.controlButton = {
    name: 'ControlButton',
    component: ControlButton,
    category: 'controls',
    title: '控制按钮',
    icon: 'el-icon-switch-button',
    defaultProps: {
      width: 120,
      height: 40,
      label: '控制按钮',
      type: 'primary'
    }
  }
}

if (DeviceIcon) {
  componentConfigs.deviceIcon = {
    name: 'DeviceIcon',
    component: DeviceIcon,
    category: 'devices',
    title: '设备图标',
    icon: 'el-icon-cpu',
    defaultProps: {
      width: 80,
      height: 80,
      deviceType: 'generic',
      status: 'normal'
    }
  }
}

// 组件分类
export const componentCategories = [
  {
    id: 'charts',
    name: '图表组件',
    icon: 'el-icon-data-analysis',
    description: '各种数据可视化图表组件'
  },
  {
    id: 'status',
    name: '状态组件',
    icon: 'el-icon-info',
    description: '设备和系统状态显示组件'
  },
  {
    id: 'controls',
    name: '控制组件',
    icon: 'el-icon-setting',
    description: '设备控制和操作组件'
  },
  {
    id: 'devices',
    name: '设备组件',
    icon: 'el-icon-cpu',
    description: '设备图标和显示组件'
  },
  {
    id: 'tables',
    name: '表格组件',
    icon: 'el-icon-s-grid',
    description: '数据表格和列表组件'
  },
  {
    id: 'models',
    name: '3D模型',
    icon: 'el-icon-box',
    description: '三维模型和可视化组件'
  }
]

// 获取指定分类的组件
export function getComponentsByCategory(categoryId) {
  return Object.keys(componentConfigs)
    .filter(key => componentConfigs[key].category === categoryId)
    .map(key => ({ key, ...componentConfigs[key] }))
}

// 获取所有组件
export function getAllComponents() {
  return Object.keys(componentConfigs)
    .map(key => ({ key, ...componentConfigs[key] }))
}

// 根据组件key获取组件配置
export function getComponentConfig(componentKey) {
  return componentConfigs[componentKey]
}

// 注册组件到Vue
export function registerComponents(Vue) {
  Object.keys(componentConfigs).forEach(key => {
    const config = componentConfigs[key]
    if (config.component) {
      Vue.component(config.name, config.component)
    }
  })
}

export default {
  componentConfigs,
  componentCategories,
  getComponentsByCategory,
  getAllComponents,
  getComponentConfig,
  registerComponents
}
