<template>
  <div class="topic-subscription-test">
    <el-card class="test-card">
      <div slot="header">
        <span>主题订阅功能测试</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="runAllTests">运行所有测试</el-button>
      </div>
      
      <div class="test-section">
        <h4>基础功能测试</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-button type="primary" @click="testAddSubscription" :loading="testLoading.add">
              测试添加订阅
            </el-button>
          </el-col>
          <el-col :span="8">
            <el-button type="warning" @click="testUpdateSubscription" :loading="testLoading.update">
              测试修改订阅
            </el-button>
          </el-col>
          <el-col :span="8">
            <el-button type="danger" @click="testRemoveSubscription" :loading="testLoading.remove">
              测试删除订阅
            </el-button>
          </el-col>
        </el-row>
      </div>
      
      <div class="test-section">
        <h4>批量操作测试</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-button type="success" @click="testBatchAdd" :loading="testLoading.batchAdd">
              测试批量添加
            </el-button>
          </el-col>
          <el-col :span="12">
            <el-button type="info" @click="testBatchRemove" :loading="testLoading.batchRemove">
              测试批量删除
            </el-button>
          </el-col>
        </el-row>
      </div>
      
      <div class="test-section">
        <h4>查询功能测试</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-button type="primary" @click="testGetSubscriptions" :loading="testLoading.get">
              测试获取订阅
            </el-button>
          </el-col>
          <el-col :span="8">
            <el-button type="info" @click="testSearchSubscriptions" :loading="testLoading.search">
              测试搜索订阅
            </el-button>
          </el-col>
          <el-col :span="8">
            <el-button type="warning" @click="testVerifySubscription" :loading="testLoading.verify">
              测试验证订阅
            </el-button>
          </el-col>
        </el-row>
      </div>
      
      <!-- 测试结果 -->
      <div class="test-results" v-if="testResults.length > 0">
        <h4>测试结果</h4>
        <el-timeline>
          <el-timeline-item
            v-for="(result, index) in testResults"
            :key="index"
            :type="result.success ? 'success' : 'danger'"
            :timestamp="result.timestamp"
          >
            <div class="test-result-item">
              <div class="test-name">{{ result.testName }}</div>
              <div class="test-message" :class="result.success ? 'success' : 'error'">
                {{ result.message }}
              </div>
              <div v-if="result.data" class="test-data">
                <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-card>
  </div>
</template>

<script>
import {
  addDynamicTopic,
  removeDynamicTopic,
  updateDynamicTopic,
  batchSubscribeTopics,
  batchUnsubscribeTopics
} from '@/api/emqx/topicManagement'
import {
  getActiveSubscriptions,
  searchSubscriptions,
  verifySubscription
} from '@/api/emqx/subscription'

export default {
  name: 'TopicSubscriptionTest',
  data() {
    return {
      testLoading: {
        add: false,
        update: false,
        remove: false,
        batchAdd: false,
        batchRemove: false,
        get: false,
        search: false,
        verify: false
      },
      testResults: [],
      testClientId: 'test_client_001',
      testTopics: [
        'dwms/test/device/001/status',
        'dwms/test/device/001/alarm',
        'dwms/test/device/002/status',
        'dwms/test/video/001/stream'
      ]
    }
  },
  
  methods: {
    // 运行所有测试
    async runAllTests() {
      this.testResults = []
      this.addTestResult('开始运行所有测试', true, '测试序列启动')
      
      await this.testAddSubscription()
      await this.sleep(1000)
      
      await this.testUpdateSubscription()
      await this.sleep(1000)
      
      await this.testGetSubscriptions()
      await this.sleep(1000)
      
      await this.testBatchAdd()
      await this.sleep(1000)
      
      await this.testSearchSubscriptions()
      await this.sleep(1000)
      
      await this.testVerifySubscription()
      await this.sleep(1000)
      
      await this.testBatchRemove()
      await this.sleep(1000)
      
      await this.testRemoveSubscription()
      
      this.addTestResult('所有测试完成', true, '测试序列结束')
    },
    
    // 测试添加订阅
    async testAddSubscription() {
      this.testLoading.add = true
      try {
        const response = await addDynamicTopic({
          clientId: this.testClientId,
          topic: this.testTopics[0],
          qos: 1,
          noLocal: false,
          retainAsPublished: false,
          retainHandling: 0
        })
        
        this.addTestResult(
          '添加订阅测试',
          response.code === 200,
          response.code === 200 ? '订阅添加成功' : `添加失败: ${response.msg}`,
          response.data
        )
      } catch (error) {
        this.addTestResult('添加订阅测试', false, `测试异常: ${error.message}`)
      } finally {
        this.testLoading.add = false
      }
    },
    
    // 测试修改订阅
    async testUpdateSubscription() {
      this.testLoading.update = true
      try {
        const response = await updateDynamicTopic({
          clientId: this.testClientId,
          topic: this.testTopics[0],
          qos: 2,
          noLocal: true,
          retainAsPublished: true,
          retainHandling: 1
        })
        
        this.addTestResult(
          '修改订阅测试',
          response.code === 200,
          response.code === 200 ? '订阅修改成功' : `修改失败: ${response.msg}`,
          response.data
        )
      } catch (error) {
        this.addTestResult('修改订阅测试', false, `测试异常: ${error.message}`)
      } finally {
        this.testLoading.update = false
      }
    },
    
    // 测试删除订阅
    async testRemoveSubscription() {
      this.testLoading.remove = true
      try {
        const response = await removeDynamicTopic({
          clientId: this.testClientId,
          topic: this.testTopics[0]
        })
        
        this.addTestResult(
          '删除订阅测试',
          response.code === 200,
          response.code === 200 ? '订阅删除成功' : `删除失败: ${response.msg}`,
          response.data
        )
      } catch (error) {
        this.addTestResult('删除订阅测试', false, `测试异常: ${error.message}`)
      } finally {
        this.testLoading.remove = false
      }
    },
    
    // 测试批量添加
    async testBatchAdd() {
      this.testLoading.batchAdd = true
      try {
        const topics = this.testTopics.slice(1, 3).map(topic => ({
          topic: topic,
          qos: 1
        }))
        
        const response = await batchSubscribeTopics({
          clientId: this.testClientId,
          topics: topics
        })
        
        this.addTestResult(
          '批量添加测试',
          response.code === 200,
          response.code === 200 ? '批量添加成功' : `批量添加失败: ${response.msg}`,
          response.data
        )
      } catch (error) {
        this.addTestResult('批量添加测试', false, `测试异常: ${error.message}`)
      } finally {
        this.testLoading.batchAdd = false
      }
    },
    
    // 测试批量删除
    async testBatchRemove() {
      this.testLoading.batchRemove = true
      try {
        const topics = this.testTopics.slice(1, 3)
        
        const response = await batchUnsubscribeTopics({
          clientId: this.testClientId,
          topics: topics
        })
        
        this.addTestResult(
          '批量删除测试',
          response.code === 200,
          response.code === 200 ? '批量删除成功' : `批量删除失败: ${response.msg}`,
          response.data
        )
      } catch (error) {
        this.addTestResult('批量删除测试', false, `测试异常: ${error.message}`)
      } finally {
        this.testLoading.batchRemove = false
      }
    },
    
    // 测试获取订阅
    async testGetSubscriptions() {
      this.testLoading.get = true
      try {
        const response = await getActiveSubscriptions({
          pageNum: 1,
          pageSize: 10
        })
        
        this.addTestResult(
          '获取订阅测试',
          response.code === 200,
          response.code === 200 ? `获取成功，共${response.total}条记录` : `获取失败: ${response.msg}`,
          { total: response.total, count: response.rows?.length || 0 }
        )
      } catch (error) {
        this.addTestResult('获取订阅测试', false, `测试异常: ${error.message}`)
      } finally {
        this.testLoading.get = false
      }
    },
    
    // 测试搜索订阅
    async testSearchSubscriptions() {
      this.testLoading.search = true
      try {
        const response = await searchSubscriptions({
          keyword: 'test',
          clientId: this.testClientId
        })
        
        this.addTestResult(
          '搜索订阅测试',
          response.code === 200,
          response.code === 200 ? `搜索成功，找到${response.data?.length || 0}条记录` : `搜索失败: ${response.msg}`,
          { count: response.data?.length || 0 }
        )
      } catch (error) {
        this.addTestResult('搜索订阅测试', false, `测试异常: ${error.message}`)
      } finally {
        this.testLoading.search = false
      }
    },
    
    // 测试验证订阅
    async testVerifySubscription() {
      this.testLoading.verify = true
      try {
        const response = await verifySubscription(this.testClientId, this.testTopics[0])
        
        this.addTestResult(
          '验证订阅测试',
          response.code === 200,
          response.code === 200 ? 
            `验证完成，订阅${response.data?.exists ? '存在' : '不存在'}` : 
            `验证失败: ${response.msg}`,
          response.data
        )
      } catch (error) {
        this.addTestResult('验证订阅测试', false, `测试异常: ${error.message}`)
      } finally {
        this.testLoading.verify = false
      }
    },
    
    // 添加测试结果
    addTestResult(testName, success, message, data = null) {
      this.testResults.push({
        testName,
        success,
        message,
        data,
        timestamp: new Date().toLocaleTimeString()
      })
    },
    
    // 延迟函数
    sleep(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    }
  }
}
</script>

<style scoped>
.topic-subscription-test {
  padding: 20px;
}

.test-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.test-section h4 {
  margin-bottom: 15px;
  color: #303133;
}

.test-results {
  margin-top: 30px;
  padding: 20px;
  background: #fff;
  border: 1px solid #ebeef5;
  border-radius: 8px;
}

.test-result-item {
  padding: 10px 0;
}

.test-name {
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.test-message.success {
  color: #67c23a;
}

.test-message.error {
  color: #f56c6c;
}

.test-data {
  margin-top: 10px;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
  font-size: 12px;
}

.test-data pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
