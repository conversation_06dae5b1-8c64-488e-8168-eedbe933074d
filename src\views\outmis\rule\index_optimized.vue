<template>
  <div class="app-container">
    <!-- 规则统计卡片 -->
    <el-row :gutter="20" class="stats-cards" style="margin-bottom: 20px;">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total">
              <i class="el-icon-guide"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.totalRules || 0 }}</div>
              <div class="stats-label">规则总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon active">
              <i class="el-icon-success"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.activeRules || 0 }}</div>
              <div class="stats-label">启用规则</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon triggered">
              <i class="el-icon-warning"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.triggeredToday || 0 }}</div>
              <div class="stats-label">今日触发</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon rate">
              <i class="el-icon-pie-chart"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.effectiveRate || '0%' }}</div>
              <div class="stats-label">有效率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="规则名称" prop="ruleName">
        <el-input
          v-model="queryParams.ruleName"
          placeholder="请输入规则名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="规则类型" prop="ruleType">
        <el-select v-model="queryParams.ruleType" placeholder="请选择规则类型" clearable>
          <el-option label="重量检查" value="weight_check" />
          <el-option label="数量检查" value="quantity_check" />
          <el-option label="物料匹配" value="material_match" />
          <el-option label="时间限制" value="time_limit" />
          <el-option label="权限验证" value="permission_check" />
        </el-select>
      </el-form-item>
      <el-form-item label="优先级" prop="priority">
        <el-select v-model="queryParams.priority" placeholder="请选择优先级" clearable>
          <el-option label="高" value="high" />
          <el-option label="中" value="medium" />
          <el-option label="低" value="low" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="启用" value="1" />
          <el-option label="禁用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['outmis:rule:add']"
        >新增规则</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['outmis:rule:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['outmis:rule:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-switch-button"
          size="mini"
          :disabled="multiple"
          @click="handleToggleStatus"
          v-hasPermi="['outmis:rule:enable']"
        >启用/禁用</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-view"
          size="mini"
          @click="handleTest"
        >规则测试</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 规则列表 -->
    <el-table v-loading="loading" :data="ruleList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="规则名称" align="center" prop="ruleName" :show-overflow-tooltip="true" />
      <el-table-column label="规则类型" align="center" prop="ruleType">
        <template slot-scope="scope">
          <el-tag :type="getRuleTypeTag(scope.row.ruleType)">
            {{ getRuleTypeLabel(scope.row.ruleType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="优先级" align="center" prop="priority">
        <template slot-scope="scope">
          <el-tag :type="scope.row.priority === 'high' ? 'danger' : scope.row.priority === 'medium' ? 'warning' : 'info'">
            {{ scope.row.priority === 'high' ? '高' : scope.row.priority === 'medium' ? '中' : '低' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="触发条件" align="center" prop="condition" :show-overflow-tooltip="true" />
      <el-table-column label="执行动作" align="center" prop="action" :show-overflow-tooltip="true" />
      <el-table-column label="触发次数" align="center" prop="triggerCount" />
      <el-table-column label="最后触发" align="center" prop="lastTriggerTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.lastTriggerTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            @change="handleStatusChange(scope.row)"
            v-hasPermi="['outmis:rule:enable']"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['outmis:rule:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['outmis:rule:remove']"
          >删除</el-button>
          <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)">
            <el-button size="mini" type="text" icon="el-icon-d-arrow-right">更多</el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="test" icon="el-icon-view">测试规则</el-dropdown-item>
              <el-dropdown-item command="copy" icon="el-icon-document-copy">复制规则</el-dropdown-item>
              <el-dropdown-item command="log" icon="el-icon-document">触发日志</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改规则对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="规则名称" prop="ruleName">
              <el-input v-model="form.ruleName" placeholder="请输入规则名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="规则类型" prop="ruleType">
              <el-select v-model="form.ruleType" placeholder="请选择规则类型" style="width: 100%">
                <el-option label="重量检查" value="weight_check" />
                <el-option label="数量检查" value="quantity_check" />
                <el-option label="物料匹配" value="material_match" />
                <el-option label="时间限制" value="time_limit" />
                <el-option label="权限验证" value="permission_check" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="form.priority" placeholder="请选择优先级" style="width: 100%">
                <el-option label="高" value="high" />
                <el-option label="中" value="medium" />
                <el-option label="低" value="low" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio :label="1">启用</el-radio>
                <el-radio :label="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="触发条件" prop="condition">
          <el-input v-model="form.condition" type="textarea" placeholder="请输入触发条件" :rows="3" />
        </el-form-item>
        <el-form-item label="执行动作" prop="action">
          <el-input v-model="form.action" type="textarea" placeholder="请输入执行动作" :rows="3" />
        </el-form-item>
        <el-form-item label="规则描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入规则描述" :rows="2" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "OutmisRule",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 规则表格数据
      ruleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 统计数据
      statistics: {
        totalRules: 0,
        activeRules: 0,
        triggeredToday: 0,
        effectiveRate: '0%'
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        ruleName: null,
        ruleType: null,
        priority: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        ruleName: [
          { required: true, message: "规则名称不能为空", trigger: "blur" }
        ],
        ruleType: [
          { required: true, message: "规则类型不能为空", trigger: "change" }
        ],
        priority: [
          { required: true, message: "优先级不能为空", trigger: "change" }
        ],
        condition: [
          { required: true, message: "触发条件不能为空", trigger: "blur" }
        ],
        action: [
          { required: true, message: "执行动作不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getStatistics();
  },
  methods: {
    /** 查询规则列表 */
    getList() {
      this.loading = true;
      // 模拟数据
      this.ruleList = [
        {
          ruleId: 1,
          ruleName: "重量超限检查",
          ruleType: "weight_check",
          priority: "high",
          condition: "weight > 100kg",
          action: "发送警告，停止出库",
          triggerCount: 25,
          lastTriggerTime: new Date(),
          status: 1,
          description: "检查出库物料重量是否超过限制"
        },
        {
          ruleId: 2,
          ruleName: "物料匹配验证",
          ruleType: "material_match",
          priority: "medium",
          condition: "materialCode != expectedCode",
          action: "提示错误，要求重新扫码",
          triggerCount: 12,
          lastTriggerTime: new Date(Date.now() - 3600000),
          status: 1,
          description: "验证出库物料是否与计划一致"
        }
      ];
      this.total = this.ruleList.length;
      this.loading = false;
    },
    /** 获取统计数据 */
    getStatistics() {
      this.statistics = {
        totalRules: 15,
        activeRules: 12,
        triggeredToday: 37,
        effectiveRate: '95.2%'
      };
    },
    /** 获取规则类型标签 */
    getRuleTypeTag(type) {
      const tags = {
        'weight_check': 'danger',
        'quantity_check': 'warning',
        'material_match': 'primary',
        'time_limit': 'info',
        'permission_check': 'success'
      };
      return tags[type] || 'info';
    },
    /** 获取规则类型标签 */
    getRuleTypeLabel(type) {
      const labels = {
        'weight_check': '重量检查',
        'quantity_check': '数量检查',
        'material_match': '物料匹配',
        'time_limit': '时间限制',
        'permission_check': '权限验证'
      };
      return labels[type] || type;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        ruleId: null,
        ruleName: null,
        ruleType: null,
        priority: 'medium',
        condition: null,
        action: null,
        status: 1,
        description: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.ruleId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加防错规则";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.form = Object.assign({}, row);
      this.open = true;
      this.title = "修改防错规则";
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.ruleId != null) {
            console.log('修改规则:', this.form);
            this.$modal.msgSuccess("修改成功");
          } else {
            console.log('新增规则:', this.form);
            this.$modal.msgSuccess("新增成功");
          }
          this.open = false;
          this.getList();
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ruleIds = row.ruleId || this.ids;
      this.$modal.confirm('是否确认删除规则编号为"' + ruleIds + '"的数据项？').then(function() {
        console.log('删除规则:', ruleIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 状态修改 */
    handleStatusChange(row) {
      let text = row.status === 1 ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '""' + row.ruleName + '"规则吗？').then(function() {
        console.log('状态修改:', row.ruleId, row.status);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function() {
        row.status = row.status === 0 ? 1 : 0;
      });
    },
    /** 批量启用/禁用 */
    handleToggleStatus() {
      const ruleIds = this.ids;
      this.$modal.confirm('是否确认切换选中规则的状态?').then(function() {
        console.log('批量状态切换:', ruleIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("状态切换成功");
      }).catch(() => {});
    },
    /** 规则测试 */
    handleTest() {
      this.$modal.msgInfo("规则测试功能开发中...");
    },
    /** 更多操作 */
    handleCommand(command, row) {
      switch (command) {
        case 'test':
          this.$modal.msgInfo('测试规则: ' + row.ruleName);
          break;
        case 'copy':
          this.form = Object.assign({}, row);
          this.form.ruleId = null;
          this.form.ruleName = row.ruleName + '_副本';
          this.open = true;
          this.title = "复制防错规则";
          break;
        case 'log':
          this.$router.push({
            path: '/outmis/record',
            query: { ruleId: row.ruleId }
          });
          break;
      }
    }
  }
};
</script>

<style scoped>
.stats-cards {
  margin-bottom: 20px;
}
.stats-card {
  border-radius: 8px;
}
.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}
.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}
.stats-icon.total { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.stats-icon.active { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.stats-icon.triggered { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.stats-icon.rate { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
.stats-info {
  flex: 1;
}
.stats-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}
.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}
</style>
