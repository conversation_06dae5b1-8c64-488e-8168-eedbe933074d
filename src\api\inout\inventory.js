import request from '@/utils/request'

// 查询库房盘点列表
export function listInventory(query) {
  return request({
    url: '/inout/inventory/list',
    method: 'get',
    params: query
  })
}

// 查询库房盘点详细
export function getInventory(id) {
  return request({
    url: '/inout/inventory/' + id,
    method: 'get'
  })
}

// 新增库房盘点
export function addInventory(data) {
  return request({
    url: '/inout/inventory',
    method: 'post',
    data: data
  })
}

// 修改库房盘点
export function updateInventory(data) {
  return request({
    url: '/inout/inventory',
    method: 'put',
    data: data
  })
}

// 删除库房盘点
export function delInventory(id) {
  return request({
    url: '/inout/inventory/' + id,
    method: 'delete'
  })
}
