import request from '@/utils/request'

// 客户端清理管理API

/**
 * 预览清理重复客户端
 */
export function previewCleanup() {
  return request({
    url: '/emqx/cleanup/preview',
    method: 'get'
  })
}

/**
 * 执行清理重复客户端
 */
export function executeCleanup(confirm = false) {
  return request({
    url: '/emqx/cleanup/execute',
    method: 'post',
    params: {
      confirm
    }
  })
}

/**
 * 建立唯一性约束
 */
export function establishUniqueConstraints() {
  return request({
    url: '/emqx/cleanup/establish-constraints',
    method: 'post'
  })
}

/**
 * 获取清理历史记录
 */
export function getCleanupHistory() {
  return request({
    url: '/emqx/cleanup/history',
    method: 'get'
  })
}

/**
 * 获取指定清理记录详情
 */
export function getCleanupRecord(cleanupId) {
  return request({
    url: `/emqx/cleanup/history/${cleanupId}`,
    method: 'get'
  })
}

/**
 * 检查系统中的重复客户端
 */
export function checkDuplicates() {
  return request({
    url: '/emqx/cleanup/check-duplicates',
    method: 'get'
  })
}

/**
 * 获取清理操作的安全检查
 */
export function performSafetyCheck() {
  return request({
    url: '/emqx/cleanup/safety-check',
    method: 'get'
  })
}
