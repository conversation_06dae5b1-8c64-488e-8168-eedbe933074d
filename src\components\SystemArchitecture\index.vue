<template>
  <div class="system-architecture">
    <div class="architecture-header">
      <h3>系统架构图</h3>
      <p class="architecture-desc">睿云管理系统采用分层架构设计，支持多种通信协议和业务模块</p>
    </div>
    
    <div class="architecture-diagram">
      <div id="mermaid-diagram" class="mermaid-container">
        <!-- Mermaid 图表将在这里渲染 -->
      </div>
    </div>
    
    <div class="architecture-legend">
      <div class="legend-item">
        <span class="legend-color frontend"></span>
        <span class="legend-text">前端层</span>
      </div>
      <div class="legend-item">
        <span class="legend-color business"></span>
        <span class="legend-text">业务模块层</span>
      </div>
      <div class="legend-item">
        <span class="legend-color backend"></span>
        <span class="legend-text">后端服务层</span>
      </div>
      <div class="legend-item">
        <span class="legend-color infrastructure"></span>
        <span class="legend-text">基础设施层</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SystemArchitecture',
  data() {
    return {
      mermaidDiagram: `graph TB
    subgraph "前端层 Frontend Layer"
        UI[Vue.js 用户界面]
        Router[Vue Router 路由管理]
        Store[Vuex 状态管理]
        UI --> Router
        UI --> Store
    end

    subgraph "业务模块层 Business Modules"
        Dashboard[系统首页]
        SystemMgmt[系统管理]
        ApprovalFlow[审批流程]
        MQTT[MQTT Broker]
        SIP[SIP Server]
        EMQX[EMQX管理]
        AccessControl[门禁管理]
        MaterialMgmt[物料管理]
        ToolGen[代码生成]
    end

    subgraph "系统管理模块 System Management"
        UserMgmt[用户管理]
        RoleMgmt[角色管理]
        MenuMgmt[菜单管理]
        DeptMgmt[部门管理]
        DictMgmt[字典管理]
        ConfigMgmt[参数配置]
        LogMgmt[日志管理]
    end

    subgraph "MQTT 通信模块 MQTT Communication"
        MQTTDashboard[系统概览]
        MQTTClients[客户端管理]
        MQTTMessages[消息管理]
        MQTTAuth[认证管理]
        MQTTTopics[主题管理]
        MQTTMonitor[实时监控]
    end

    subgraph "SIP 通信模块 SIP Communication"
        SIPDashboard[系统概览]
        SIPDevices[设备管理]
        SIPSessions[会话管理]
        SIPMedia[媒体管理]
        SIPMessages[消息管理]
        SIPMonitor[性能监控]
    end

    subgraph "审批流程模块 Approval Workflow"
        WorkflowDef[流程定义]
        TodoApproval[待我审批]
        MyApproval[我的申请]
        ApprovalMgmt[审批管理]
    end

    subgraph "后端服务层 Backend Services"
        SpringBoot[Spring Boot 应用]
        Security[Spring Security 安全]
        JWT[JWT 认证]
        MyBatis[MyBatis 数据访问]
        Database[(MySQL 数据库)]
    end

    subgraph "基础设施层 Infrastructure"
        Redis[(Redis 缓存)]
        MQTTBroker[MQTT Broker 服务]
        SIPServer[SIP Server 服务]
        EMQXServer[EMQX 服务器]
    end

    %% 连接关系
    UI --> Dashboard
    UI --> SystemMgmt
    UI --> ApprovalFlow
    UI --> MQTT
    UI --> SIP
    UI --> EMQX
    UI --> AccessControl
    UI --> MaterialMgmt
    UI --> ToolGen

    SystemMgmt --> UserMgmt
    SystemMgmt --> RoleMgmt
    SystemMgmt --> MenuMgmt
    SystemMgmt --> DeptMgmt
    SystemMgmt --> DictMgmt
    SystemMgmt --> ConfigMgmt
    SystemMgmt --> LogMgmt

    MQTT --> MQTTDashboard
    MQTT --> MQTTClients
    MQTT --> MQTTMessages
    MQTT --> MQTTAuth
    MQTT --> MQTTTopics
    MQTT --> MQTTMonitor

    SIP --> SIPDashboard
    SIP --> SIPDevices
    SIP --> SIPSessions
    SIP --> SIPMedia
    SIP --> SIPMessages
    SIP --> SIPMonitor

    ApprovalFlow --> WorkflowDef
    ApprovalFlow --> TodoApproval
    ApprovalFlow --> MyApproval
    ApprovalFlow --> ApprovalMgmt

    Dashboard --> SpringBoot
    SystemMgmt --> SpringBoot
    ApprovalFlow --> SpringBoot
    MQTT --> SpringBoot
    SIP --> SpringBoot
    EMQX --> SpringBoot

    SpringBoot --> Security
    SpringBoot --> JWT
    SpringBoot --> MyBatis
    MyBatis --> Database

    SpringBoot --> Redis
    MQTT --> MQTTBroker
    SIP --> SIPServer
    EMQX --> EMQXServer

    %% 样式定义
    classDef frontend fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef business fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef backend fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef infrastructure fill:#fff3e0,stroke:#e65100,stroke-width:2px

    class UI,Router,Store frontend
    class Dashboard,SystemMgmt,ApprovalFlow,MQTT,SIP,EMQX,AccessControl,MaterialMgmt,ToolGen business
    class SpringBoot,Security,JWT,MyBatis,Database backend
    class Redis,MQTTBroker,SIPServer,EMQXServer infrastructure`
    }
  },
  mounted() {
    this.renderMermaid()
  },
  methods: {
    async renderMermaid() {
      try {
        // 动态导入 mermaid
        const mermaid = await import('mermaid')
        
        // 配置 mermaid
        mermaid.default.initialize({
          startOnLoad: true,
          theme: 'default',
          flowchart: {
            useMaxWidth: true,
            htmlLabels: true
          }
        })
        
        // 渲染图表
        const element = document.getElementById('mermaid-diagram')
        if (element) {
          element.innerHTML = this.mermaidDiagram
          element.classList.add('mermaid')
          mermaid.default.init(undefined, element)
        }
      } catch (error) {
        console.error('Mermaid rendering failed:', error)
        // 如果 mermaid 加载失败，显示备用内容
        this.showFallbackDiagram()
      }
    },
    
    showFallbackDiagram() {
      const element = document.getElementById('mermaid-diagram')
      if (element) {
        element.innerHTML = `
          <div class="fallback-diagram">
            <div class="diagram-layer frontend-layer">
              <h4>前端层</h4>
              <div class="layer-items">
                <span>Vue.js</span>
                <span>Vue Router</span>
                <span>Vuex</span>
              </div>
            </div>
            <div class="diagram-layer business-layer">
              <h4>业务模块层</h4>
              <div class="layer-items">
                <span>系统管理</span>
                <span>MQTT Broker</span>
                <span>SIP Server</span>
                <span>审批流程</span>
                <span>EMQX管理</span>
              </div>
            </div>
            <div class="diagram-layer backend-layer">
              <h4>后端服务层</h4>
              <div class="layer-items">
                <span>Spring Boot</span>
                <span>Spring Security</span>
                <span>MyBatis</span>
                <span>MySQL</span>
              </div>
            </div>
            <div class="diagram-layer infrastructure-layer">
              <h4>基础设施层</h4>
              <div class="layer-items">
                <span>Redis</span>
                <span>MQTT Broker</span>
                <span>SIP Server</span>
                <span>EMQX</span>
              </div>
            </div>
          </div>
        `
      }
    }
  }
}
</script>

<style scoped lang="scss">
.system-architecture {
  .architecture-header {
    text-align: center;
    margin-bottom: 30px;
    
    h3 {
      font-size: 24px;
      color: #303133;
      margin-bottom: 10px;
    }
    
    .architecture-desc {
      color: #606266;
      font-size: 14px;
      line-height: 1.6;
    }
  }
  
  .architecture-diagram {
    margin-bottom: 30px;
    
    .mermaid-container {
      text-align: center;
      background: #fff;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      padding: 20px;
      min-height: 400px;
      
      // Mermaid 样式覆盖
      :deep(.mermaid) {
        svg {
          max-width: 100%;
          height: auto;
        }
      }
    }
    
    // 备用图表样式
    .fallback-diagram {
      display: flex;
      flex-direction: column;
      gap: 20px;
      
      .diagram-layer {
        padding: 15px;
        border-radius: 8px;
        text-align: center;
        
        h4 {
          margin: 0 0 10px 0;
          font-size: 16px;
          font-weight: 600;
        }
        
        .layer-items {
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          gap: 10px;
          
          span {
            padding: 4px 12px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 4px;
            font-size: 12px;
            border: 1px solid rgba(0, 0, 0, 0.1);
          }
        }
        
        &.frontend-layer {
          background: #e1f5fe;
          border: 1px solid #01579b;
          
          h4 { color: #01579b; }
        }
        
        &.business-layer {
          background: #f3e5f5;
          border: 1px solid #4a148c;
          
          h4 { color: #4a148c; }
        }
        
        &.backend-layer {
          background: #e8f5e8;
          border: 1px solid #1b5e20;
          
          h4 { color: #1b5e20; }
        }
        
        &.infrastructure-layer {
          background: #fff3e0;
          border: 1px solid #e65100;
          
          h4 { color: #e65100; }
        }
      }
    }
  }
  
  .architecture-legend {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 20px;
    
    .legend-item {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .legend-color {
        width: 16px;
        height: 16px;
        border-radius: 4px;
        border: 1px solid rgba(0, 0, 0, 0.1);
        
        &.frontend { background: #e1f5fe; }
        &.business { background: #f3e5f5; }
        &.backend { background: #e8f5e8; }
        &.infrastructure { background: #fff3e0; }
      }
      
      .legend-text {
        font-size: 14px;
        color: #606266;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .system-architecture {
    .architecture-diagram {
      .mermaid-container {
        padding: 10px;
      }
      
      .fallback-diagram {
        .diagram-layer {
          .layer-items {
            span {
              font-size: 11px;
              padding: 3px 8px;
            }
          }
        }
      }
    }
    
    .architecture-legend {
      gap: 15px;
      
      .legend-item {
        .legend-text {
          font-size: 12px;
        }
      }
    }
  }
}
</style>
