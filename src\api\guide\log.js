import request from '@/utils/request'

// 查询管控日志列表
export function listLog(query) {
  return request({
    url: '/guide/log/list',
    method: 'get',
    params: query
  })
}

// 查询管控日志详细
export function getLog(id) {
  return request({
    url: '/guide/log/' + id,
    method: 'get'
  })
}

// 新增管控日志
export function addLog(data) {
  return request({
    url: '/guide/log',
    method: 'post',
    data: data
  })
}

// 修改管控日志
export function updateLog(data) {
  return request({
    url: '/guide/log',
    method: 'put',
    data: data
  })
}

// 删除管控日志
export function delLog(id) {
  return request({
    url: '/guide/log/' + id,
    method: 'delete'
  })
}
