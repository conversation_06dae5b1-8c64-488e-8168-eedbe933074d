<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="page-title">
        <h2><i class="el-icon-data-line"></i> 数据分流监控</h2>
        <p>实时监控MQTT数据分流状态，查看各设备类型的数据流量统计</p>
      </div>
      <div class="page-actions">
        <el-button type="primary" icon="el-icon-refresh" @click="refreshData" :loading="loading">
          刷新数据
        </el-button>
        <el-button type="success" icon="el-icon-upload2" @click="showSimulateDialog">
          模拟数据
        </el-button>
        <el-button type="warning" icon="el-icon-delete" @click="cleanExpiredData">
          清理过期
        </el-button>
      </div>
    </div>

    <!-- 实时统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <i class="el-icon-data-analysis"></i>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ totalStats.totalStreams }}</div>
              <div class="stat-label">活跃数据流</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon messages">
              <i class="el-icon-message"></i>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ formatNumber(totalStats.totalMessages) }}</div>
              <div class="stat-label">总消息数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon devices">
              <i class="el-icon-mobile-phone"></i>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ totalStats.activeDevices }}</div>
              <div class="stat-label">活跃设备</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon rate">
              <i class="el-icon-timer"></i>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ totalStats.messageRate }}/s</div>
              <div class="stat-label">消息速率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 设备类型分布图表 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card class="chart-card">
          <div slot="header">
            <span><i class="el-icon-pie-chart"></i> 设备类型分布</span>
          </div>
          <div id="deviceTypeChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card">
          <div slot="header">
            <span><i class="el-icon-data-line"></i> 消息流量趋势</span>
          </div>
          <div id="messageFlowChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据流列表 -->
    <el-card class="stream-list-card">
      <div slot="header" class="card-header">
        <span><i class="el-icon-connection"></i> 数据流详情</span>
        <div class="header-actions">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索设备类型或服务器"
            prefix-icon="el-icon-search"
            style="width: 250px; margin-right: 10px;"
            clearable
            @input="filterStreams"
          />
          <el-select
            v-model="deviceTypeFilter"
            placeholder="设备类型筛选"
            style="width: 150px;"
            clearable
            @change="filterStreams"
          >
            <el-option
              v-for="type in deviceTypeOptions"
              :key="type"
              :label="type"
              :value="type"
            />
          </el-select>
        </div>
      </div>

      <el-table
        :data="filteredStreams"
        v-loading="loading"
        element-loading-text="加载中..."
        style="width: 100%"
        @row-click="viewStreamDetail"
      >
        <el-table-column prop="deviceType" label="设备类型" width="150">
          <template slot-scope="scope">
            <el-tag :type="getDeviceTypeTag(scope.row.deviceType)" size="small">
              {{ scope.row.deviceType }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="serverClientId" label="归属服务器" width="200">
          <template slot-scope="scope">
            <span class="server-id">{{ scope.row.serverClientId }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="totalMessages" label="消息总数" width="120">
          <template slot-scope="scope">
            <span class="message-count">{{ formatNumber(scope.row.totalMessages) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="messageRate" label="消息速率" width="120">
          <template slot-scope="scope">
            <span class="message-rate">{{ scope.row.messageRate || 0 }}/s</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="lastMessageTime" label="最后消息时间" width="160">
          <template slot-scope="scope">
            <span :class="getTimeClass(scope.row.lastMessageTime)">
              {{ formatTime(scope.row.lastMessageTime) }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column prop="lastTopic" label="最后主题" min-width="200">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.lastTopic" placement="top">
              <span class="topic-text">{{ scope.row.lastTopic || '-' }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row)" size="mini">
              {{ getStatusText(scope.row) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-view"
              @click.stop="viewStreamDetail(scope.row)"
            >
              详情
            </el-button>
            <el-button
              size="mini"
              type="success"
              icon="el-icon-refresh"
              @click.stop="refreshStream(scope.row)"
            >
              刷新
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 数据流详情对话框 -->
    <el-dialog
      title="数据流详情"
      :visible.sync="detailDialogVisible"
      width="800px"
      :before-close="closeDetailDialog"
    >
      <div v-if="selectedStream" class="stream-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="设备类型">
            <el-tag :type="getDeviceTypeTag(selectedStream.deviceType)">
              {{ selectedStream.deviceType }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="归属服务器">
            {{ selectedStream.serverClientId }}
          </el-descriptions-item>
          <el-descriptions-item label="消息总数">
            {{ formatNumber(selectedStream.totalMessages) }}
          </el-descriptions-item>
          <el-descriptions-item label="消息速率">
            {{ selectedStream.messageRate || 0 }}/s
          </el-descriptions-item>
          <el-descriptions-item label="最后消息时间" :span="2">
            {{ formatTime(selectedStream.lastMessageTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="最后主题" :span="2">
            {{ selectedStream.lastTopic || '-' }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 最后消息内容 -->
        <div class="message-content">
          <h4><i class="el-icon-document"></i> 最后消息内容</h4>
          <el-input
            v-model="selectedStream.lastPayload"
            type="textarea"
            :rows="6"
            readonly
            placeholder="暂无消息内容"
          />
        </div>

        <!-- 消息历史图表 -->
        <div class="message-history">
          <h4><i class="el-icon-data-line"></i> 消息历史趋势</h4>
          <div id="streamHistoryChart" style="height: 200px;"></div>
        </div>
      </div>
    </el-dialog>

    <!-- 模拟数据对话框 -->
    <el-dialog
      title="模拟设备数据上报"
      :visible.sync="simulateDialogVisible"
      width="600px"
      :before-close="closeSimulateDialog"
    >
      <el-form
        ref="simulateForm"
        :model="simulateForm"
        :rules="simulateRules"
        label-width="120px"
      >
        <el-form-item label="设备类型" prop="deviceType">
          <el-select v-model="simulateForm.deviceType" placeholder="请选择设备类型">
            <el-option
              v-for="type in deviceTypeOptions"
              :key="type"
              :label="type"
              :value="type"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="设备ID" prop="deviceId">
          <el-input v-model="simulateForm.deviceId" placeholder="请输入设备ID" />
        </el-form-item>
        <el-form-item label="数据类型" prop="dataType">
          <el-select v-model="simulateForm.dataType" placeholder="请选择数据类型">
            <el-option label="状态数据" value="status" />
            <el-option label="心跳数据" value="heartbeat" />
            <el-option label="业务数据" value="data" />
          </el-select>
        </el-form-item>
        <el-form-item label="消息内容">
          <el-input
            v-model="simulateForm.data"
            type="textarea"
            :rows="4"
            placeholder="请输入JSON格式的消息内容，留空将自动生成"
          />
        </el-form-item>
        <el-form-item label="发送次数">
          <el-input-number
            v-model="simulateForm.count"
            :min="1"
            :max="100"
            placeholder="发送次数"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeSimulateDialog">取消</el-button>
        <el-button type="primary" @click="submitSimulate" :loading="simulateLoading">
          发送
        </el-button>
      </div>
    </el-dialog>

    <!-- 批量处理对话框 -->
    <el-dialog
      title="批量处理消息"
      :visible.sync="batchDialogVisible"
      width="700px"
      :before-close="closeBatchDialog"
    >
      <div class="batch-process">
        <el-alert
          title="批量处理说明"
          type="info"
          :closable="false"
          style="margin-bottom: 20px;"
        >
          <div slot="description">
            <p>支持批量处理多条MQTT消息，用于测试数据分流功能</p>
          </div>
        </el-alert>

        <el-input
          v-model="batchMessages"
          type="textarea"
          :rows="8"
          placeholder="请输入JSON格式的消息数组"
        />

        <div class="batch-actions" style="margin-top: 20px;">
          <el-button @click="validateBatchMessages">验证格式</el-button>
          <el-button type="primary" @click="submitBatchProcess" :loading="batchLoading">
            批量处理
          </el-button>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeBatchDialog">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getDataStreamStatistics,
  simulateDeviceData,
  batchProcessMessages,
  cleanExpiredStatistics
} from '@/api/emqx/dataStream'

// 引入ECharts
import * as echarts from 'echarts'

export default {
  name: 'DataStream',
  data() {
    return {
      loading: false,
      streams: [],
      filteredStreams: [],
      searchKeyword: '',
      deviceTypeFilter: '',
      
      // 统计数据
      totalStats: {
        totalStreams: 0,
        totalMessages: 0,
        activeDevices: 0,
        messageRate: 0
      },
      
      // 设备类型选项
      deviceTypeOptions: [
        '出库防错客户端',
        '门禁设备客户端',
        '视频监控客户端',
        '智能导寻客户端',
        '库房组态客户端',
        '称重设备客户端'
      ],
      
      // 详情对话框
      detailDialogVisible: false,
      selectedStream: null,
      
      // 模拟数据对话框
      simulateDialogVisible: false,
      simulateLoading: false,
      simulateForm: {
        deviceType: '',
        deviceId: '',
        dataType: '',
        data: '',
        count: 1
      },
      simulateRules: {
        deviceType: [
          { required: true, message: '请选择设备类型', trigger: 'change' }
        ],
        deviceId: [
          { required: true, message: '请输入设备ID', trigger: 'blur' }
        ],
        dataType: [
          { required: true, message: '请选择数据类型', trigger: 'change' }
        ]
      },
      
      // 批量处理对话框
      batchDialogVisible: false,
      batchLoading: false,
      batchMessages: '',
      
      // 图表实例
      deviceTypeChart: null,
      messageFlowChart: null,
      streamHistoryChart: null,
      
      // 定时器
      refreshTimer: null
    }
  },
  
  created() {
    this.loadData()
    this.startAutoRefresh()
  },
  
  beforeDestroy() {
    this.stopAutoRefresh()
    this.destroyCharts()
  },
  
  methods: {
    async loadData() {
      this.loading = true
      try {
        await this.loadStreamStatistics()
        this.filterStreams()
        this.$nextTick(() => {
          this.initCharts()
        })
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },
    
    async loadStreamStatistics() {
      try {
        const response = await getDataStreamStatistics()
        if (response.code === 200) {
          const data = response.data
          this.streams = data.statistics || []
          this.totalStats = {
            totalStreams: data.totalStreams || 0,
            totalMessages: data.totalMessages || 0,
            activeDevices: data.activeDevices || 0,
            messageRate: data.messageRate || 0
          }
        }
      } catch (error) {
        console.error('加载数据流统计失败:', error)
      }
    },
    
    filterStreams() {
      let filtered = this.streams
      
      // 关键词搜索
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        filtered = filtered.filter(stream =>
          stream.deviceType.toLowerCase().includes(keyword) ||
          stream.serverClientId.toLowerCase().includes(keyword)
        )
      }
      
      // 设备类型筛选
      if (this.deviceTypeFilter) {
        filtered = filtered.filter(stream => stream.deviceType === this.deviceTypeFilter)
      }
      
      this.filteredStreams = filtered
    },
    
    refreshData() {
      this.loadData()
    },
    
    startAutoRefresh() {
      this.refreshTimer = setInterval(() => {
        this.loadStreamStatistics()
        this.updateCharts()
      }, 30000) // 30秒刷新一次
    },
    
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },
    
    viewStreamDetail(row) {
      this.selectedStream = row
      this.detailDialogVisible = true
      this.$nextTick(() => {
        this.initStreamHistoryChart()
      })
    },
    
    closeDetailDialog() {
      this.detailDialogVisible = false
      this.selectedStream = null
      if (this.streamHistoryChart) {
        this.streamHistoryChart.dispose()
        this.streamHistoryChart = null
      }
    },
    
    refreshStream(row) {
      this.$message.success('数据流已刷新')
      this.loadStreamStatistics()
    },
    
    showSimulateDialog() {
      this.simulateDialogVisible = true
    },
    
    closeSimulateDialog() {
      this.simulateDialogVisible = false
      this.resetSimulateForm()
    },
    
    resetSimulateForm() {
      this.$refs.simulateForm?.resetFields()
      this.simulateForm = {
        deviceType: '',
        deviceId: '',
        dataType: '',
        data: '',
        count: 1
      }
    },
    
    submitSimulate() {
      this.$refs.simulateForm.validate(async (valid) => {
        if (valid) {
          this.simulateLoading = true
          try {
            for (let i = 0; i < this.simulateForm.count; i++) {
              const response = await simulateDeviceData(this.simulateForm)
              if (response.code !== 200) {
                this.$message.error(`第${i + 1}次发送失败: ` + response.msg)
                break
              }
            }
            this.$message.success(`成功发送 ${this.simulateForm.count} 条模拟数据`)
            this.closeSimulateDialog()
            this.loadData()
          } catch (error) {
            this.$message.error('发送模拟数据失败')
          } finally {
            this.simulateLoading = false
          }
        }
      })
    },
    
    showBatchDialog() {
      this.batchDialogVisible = true
    },
    
    closeBatchDialog() {
      this.batchDialogVisible = false
      this.batchMessages = ''
    },
    
    validateBatchMessages() {
      try {
        const messages = JSON.parse(this.batchMessages)
        if (!Array.isArray(messages)) {
          this.$message.error('数据格式错误，请输入数组格式')
          return
        }
        
        let validCount = 0
        for (const msg of messages) {
          if (msg.clientId && msg.topic && msg.payload) {
            validCount++
          }
        }
        
        this.$message.success(`格式验证通过，共 ${messages.length} 条消息，其中 ${validCount} 条有效`)
      } catch (error) {
        this.$message.error('JSON格式错误: ' + error.message)
      }
    },
    
    async submitBatchProcess() {
      if (!this.batchMessages) {
        this.$message.error('请输入批量消息数据')
        return
      }
      
      try {
        const messages = JSON.parse(this.batchMessages)
        this.batchLoading = true
        
        const response = await batchProcessMessages({ messages })
        if (response.code === 200) {
          const result = response.data
          this.$message.success(`批量处理完成：成功 ${result.success} 条，失败 ${result.failure} 条`)
          this.closeBatchDialog()
          this.loadData()
        } else {
          this.$message.error('批量处理失败: ' + response.msg)
        }
      } catch (error) {
        this.$message.error('批量处理失败')
      } finally {
        this.batchLoading = false
      }
    },
    
    async cleanExpiredData() {
      this.$confirm('确定要清理过期的统计数据吗？（默认清理24小时前的数据）', '确认操作', {
        type: 'warning'
      }).then(async () => {
        try {
          const response = await cleanExpiredStatistics(24)
          if (response.code === 200) {
            this.$message.success('过期数据清理完成')
            this.loadData()
          } else {
            this.$message.error('清理失败: ' + response.msg)
          }
        } catch (error) {
          this.$message.error('清理失败')
        }
      })
    },
    
    // 图表相关方法
    initCharts() {
      this.initDeviceTypeChart()
      this.initMessageFlowChart()
    },
    
    initDeviceTypeChart() {
      const chartDom = document.getElementById('deviceTypeChart')
      if (!chartDom) return
      
      this.deviceTypeChart = echarts.init(chartDom)
      
      const deviceTypeData = this.deviceTypeOptions.map(type => {
        const count = this.streams.filter(s => s.deviceType === type).length
        return { name: type, value: count }
      }).filter(item => item.value > 0)
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: '设备类型',
            type: 'pie',
            radius: '50%',
            data: deviceTypeData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      
      this.deviceTypeChart.setOption(option)
    },
    
    initMessageFlowChart() {
      const chartDom = document.getElementById('messageFlowChart')
      if (!chartDom) return
      
      this.messageFlowChart = echarts.init(chartDom)
      
      // 生成模拟的时间序列数据
      const times = []
      const values = []
      const now = new Date()
      
      for (let i = 23; i >= 0; i--) {
        const time = new Date(now.getTime() - i * 60 * 60 * 1000)
        times.push(time.getHours() + ':00')
        values.push(Math.floor(Math.random() * 1000) + 100)
      }
      
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: times
        },
        yAxis: {
          type: 'value',
          name: '消息数'
        },
        series: [
          {
            name: '消息流量',
            type: 'line',
            smooth: true,
            data: values,
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(64, 158, 255, 0.8)' },
                { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
              ])
            }
          }
        ]
      }
      
      this.messageFlowChart.setOption(option)
    },
    
    initStreamHistoryChart() {
      const chartDom = document.getElementById('streamHistoryChart')
      if (!chartDom) return
      
      this.streamHistoryChart = echarts.init(chartDom)
      
      // 生成模拟的历史数据
      const times = []
      const values = []
      const now = new Date()
      
      for (let i = 11; i >= 0; i--) {
        const time = new Date(now.getTime() - i * 60 * 60 * 1000)
        times.push(time.getHours() + ':00')
        values.push(Math.floor(Math.random() * 100) + 10)
      }
      
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: times
        },
        yAxis: {
          type: 'value',
          name: '消息数'
        },
        series: [
          {
            name: '消息数量',
            type: 'bar',
            data: values,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#83bff6' },
                { offset: 0.5, color: '#188df0' },
                { offset: 1, color: '#188df0' }
              ])
            }
          }
        ]
      }
      
      this.streamHistoryChart.setOption(option)
    },
    
    updateCharts() {
      if (this.deviceTypeChart) {
        this.initDeviceTypeChart()
      }
      if (this.messageFlowChart) {
        this.initMessageFlowChart()
      }
    },
    
    destroyCharts() {
      if (this.deviceTypeChart) {
        this.deviceTypeChart.dispose()
        this.deviceTypeChart = null
      }
      if (this.messageFlowChart) {
        this.messageFlowChart.dispose()
        this.messageFlowChart = null
      }
      if (this.streamHistoryChart) {
        this.streamHistoryChart.dispose()
        this.streamHistoryChart = null
      }
    },
    
    // 工具方法
    getDeviceTypeTag(deviceType) {
      const tagMap = {
        '出库防错客户端': 'danger',
        '门禁设备客户端': 'success',
        '视频监控客户端': 'primary',
        '智能导寻客户端': 'warning',
        '库房组态客户端': 'info',
        '称重设备客户端': 'danger'
      }
      return tagMap[deviceType] || ''
    },
    
    getStatusType(stream) {
      const now = Date.now()
      const lastTime = stream.lastMessageTime
      if (!lastTime) return 'info'
      
      const diff = now - lastTime
      if (diff < 5 * 60 * 1000) return 'success' // 5分钟内
      if (diff < 30 * 60 * 1000) return 'warning' // 30分钟内
      return 'danger' // 超过30分钟
    },
    
    getStatusText(stream) {
      const now = Date.now()
      const lastTime = stream.lastMessageTime
      if (!lastTime) return '未知'
      
      const diff = now - lastTime
      if (diff < 5 * 60 * 1000) return '活跃'
      if (diff < 30 * 60 * 1000) return '空闲'
      return '离线'
    },
    
    getTimeClass(time) {
      const now = Date.now()
      if (!time) return 'time-unknown'
      
      const diff = now - time
      if (diff < 5 * 60 * 1000) return 'time-recent'
      if (diff < 30 * 60 * 1000) return 'time-normal'
      return 'time-old'
    },
    
    formatNumber(num) {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num?.toString() || '0'
    },
    
    formatTime(time) {
      if (!time) return '-'
      return new Date(time).toLocaleString()
    }
  }
}
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title h2 {
  margin: 0;
  color: #303133;
}

.page-title p {
  margin: 5px 0 0 0;
  color: #909399;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  margin-right: 15px;
}

.stat-icon.total { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.stat-icon.messages { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.stat-icon.devices { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.stat-icon.rate { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.chart-card, .stream-list-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
}

.server-id, .topic-text {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.message-count, .message-rate {
  font-weight: bold;
}

.time-recent { color: #67c23a; }
.time-normal { color: #e6a23c; }
.time-old { color: #f56c6c; }
.time-unknown { color: #909399; }

.stream-detail {
  padding: 10px 0;
}

.message-content, .message-history {
  margin-top: 20px;
}

.message-content h4, .message-history h4 {
  margin-bottom: 10px;
  color: #303133;
}

.batch-process {
  padding: 10px 0;
}
</style>
