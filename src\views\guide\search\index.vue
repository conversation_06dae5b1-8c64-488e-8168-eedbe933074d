<template>
  <div class="app-container">
    <el-card class="search-card">
      <div slot="header" class="clearfix">
        <span>智能导寻搜索</span>
      </div>
      
      <!-- 搜索表单 -->
      <el-form :model="searchForm" ref="searchForm" size="small" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="搜索关键词" prop="keyword">
              <el-input
                v-model="searchForm.keyword"
                placeholder="请输入物品名称、编码或位置"
                clearable
                @keyup.enter.native="handleSearch"
              >
                <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="搜索类型" prop="searchType">
              <el-select v-model="searchForm.searchType" placeholder="请选择搜索类型" clearable>
                <el-option label="物品名称" value="name" />
                <el-option label="物品编码" value="code" />
                <el-option label="存储位置" value="location" />
                <el-option label="全部" value="all" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button @click="resetSearch">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 搜索结果 -->
    <el-card class="result-card" v-if="searchResults.length > 0">
      <div slot="header" class="clearfix">
        <span>搜索结果 ({{ total }} 条)</span>
      </div>
      
      <el-table v-loading="loading" :data="searchResults">
        <el-table-column label="物品编码" align="center" prop="itemCode" />
        <el-table-column label="物品名称" align="center" prop="itemName" />
        <el-table-column label="存储位置" align="center" prop="location" />
        <el-table-column label="数量" align="center" prop="quantity" />
        <el-table-column label="单位" align="center" prop="unit" />
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '正常' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-location"
              @click="handleGuide(scope.row)"
            >开始导寻</el-button>
            <el-button
              size="mini"
              type="success"
              icon="el-icon-view"
              @click="handleView(scope.row)"
            >查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="handleSearch"
      />
    </el-card>

    <!-- 无搜索结果 -->
    <el-card class="no-result-card" v-if="searched && searchResults.length === 0">
      <div class="no-result">
        <i class="el-icon-search"></i>
        <p>未找到相关结果</p>
        <p>请尝试其他关键词或搜索类型</p>
      </div>
    </el-card>

    <!-- 导寻路径显示 -->
    <el-dialog title="导寻路径" :visible.sync="guideDialogVisible" width="80%" append-to-body>
      <div class="guide-content">
        <el-steps :active="currentStep" direction="vertical">
          <el-step
            v-for="(step, index) in guideSteps"
            :key="index"
            :title="step.title"
            :description="step.description"
          ></el-step>
        </el-steps>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="guideDialogVisible = false">关 闭</el-button>
        <el-button type="primary" @click="startGuide">开始导寻</el-button>
      </div>
    </el-dialog>

    <!-- 物品详情对话框 -->
    <el-dialog title="物品详情" :visible.sync="detailDialogVisible" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="物品编码">{{ currentItem.itemCode }}</el-descriptions-item>
        <el-descriptions-item label="物品名称">{{ currentItem.itemName }}</el-descriptions-item>
        <el-descriptions-item label="存储位置">{{ currentItem.location }}</el-descriptions-item>
        <el-descriptions-item label="数量">{{ currentItem.quantity }}</el-descriptions-item>
        <el-descriptions-item label="单位">{{ currentItem.unit }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="currentItem.status === 1 ? 'success' : 'danger'">
            {{ currentItem.status === 1 ? '正常' : '停用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间" :span="2">{{ currentItem.createTime }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ currentItem.remark || '无' }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { searchItems } from "@/api/guide/search";

export default {
  name: "GuideSearch",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 是否已搜索
      searched: false,
      // 总条数
      total: 0,
      // 搜索表单
      searchForm: {
        keyword: '',
        searchType: 'all'
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      // 搜索结果
      searchResults: [],
      // 导寻对话框
      guideDialogVisible: false,
      // 详情对话框
      detailDialogVisible: false,
      // 当前物品
      currentItem: {},
      // 当前步骤
      currentStep: 0,
      // 导寻步骤
      guideSteps: []
    };
  },
  methods: {
    /** 搜索操作 */
    handleSearch() {
      if (!this.searchForm.keyword) {
        this.$modal.msgWarning("请输入搜索关键词");
        return;
      }
      
      this.loading = true;
      this.searched = true;
      
      const params = {
        ...this.queryParams,
        keyword: this.searchForm.keyword,
        searchType: this.searchForm.searchType
      };
      
      // 暂时使用模拟数据，避免API权限问题
      setTimeout(() => {
        this.searchResults = [
          {
            itemId: 1,
            itemCode: 'ITEM001',
            itemName: '螺丝刀',
            itemCategory: '工具',
            itemSpec: '十字头 6mm',
            locations: [
              {
                locationCode: 'A01-01-01',
                locationName: 'A区1排1层1位',
                quantity: 10,
                unit: '把'
              }
            ]
          }
        ];
        this.total = 1;
        this.loading = false;
      }, 500);
    },
    
    /** 重置搜索 */
    resetSearch() {
      this.searchForm = {
        keyword: '',
        searchType: 'all'
      };
      this.queryParams = {
        pageNum: 1,
        pageSize: 10
      };
      this.searchResults = [];
      this.searched = false;
      this.total = 0;
    },
    
    /** 开始导寻 */
    handleGuide(row) {
      this.currentItem = row;
      this.generateGuideSteps(row);
      this.guideDialogVisible = true;
    },
    
    /** 查看详情 */
    handleView(row) {
      this.currentItem = row;
      this.detailDialogVisible = true;
    },
    
    /** 生成导寻步骤 */
    generateGuideSteps(item) {
      this.guideSteps = [
        {
          title: "准备阶段",
          description: "确认目标物品：" + item.itemName
        },
        {
          title: "路径规划",
          description: "计算到达位置：" + item.location + " 的最优路径"
        },
        {
          title: "开始导寻",
          description: "跟随指示灯前往目标位置"
        },
        {
          title: "到达目标",
          description: "已到达目标位置，请确认物品"
        }
      ];
      this.currentStep = 0;
    },
    
    /** 开始导寻 */
    startGuide() {
      this.$modal.msgSuccess("导寻已开始，请跟随指示灯前往目标位置");
      this.guideDialogVisible = false;
      
      // 模拟导寻过程
      this.simulateGuideProcess();
    },
    
    /** 模拟导寻过程 */
    simulateGuideProcess() {
      const interval = setInterval(() => {
        this.currentStep++;
        if (this.currentStep >= this.guideSteps.length) {
          clearInterval(interval);
          this.$modal.msgSuccess("导寻完成！");
        }
      }, 2000);
    }
  }
};
</script>

<style scoped>
.search-card {
  margin-bottom: 20px;
}

.result-card {
  margin-bottom: 20px;
}

.no-result-card {
  text-align: center;
  padding: 40px 0;
}

.no-result {
  color: #909399;
}

.no-result i {
  font-size: 48px;
  margin-bottom: 16px;
}

.no-result p {
  margin: 8px 0;
  font-size: 14px;
}

.guide-content {
  max-height: 400px;
  overflow-y: auto;
}
</style>
