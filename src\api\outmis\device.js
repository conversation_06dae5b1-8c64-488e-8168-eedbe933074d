import request from '@/utils/request'

// 查询出库防错设备管理列表
export function listDevice(query) {
  return request({
    url: '/outmis/device/list',
    method: 'get',
    params: query
  })
}

// 查询出库防错设备管理详细
export function getDevice(deviceId) {
  return request({
    url: '/outmis/device/' + deviceId,
    method: 'get'
  })
}

// 根据设备编码查询设备信息
export function getDeviceByCode(deviceCode) {
  return request({
    url: '/outmis/device/code/' + deviceCode,
    method: 'get'
  })
}

// 查询在线设备列表
export function listOnlineDevice(query) {
  return request({
    url: '/outmis/device/online',
    method: 'get',
    params: query
  })
}

// 新增出库防错设备管理
export function addDevice(data) {
  return request({
    url: '/outmis/device',
    method: 'post',
    data: data
  })
}

// 修改出库防错设备管理
export function updateDevice(data) {
  return request({
    url: '/outmis/device',
    method: 'put',
    data: data
  })
}

// 删除出库防错设备管理
export function delDevice(deviceId) {
  return request({
    url: '/outmis/device/' + deviceId,
    method: 'delete'
  })
}

// 更新设备在线状态
export function updateDeviceStatus(deviceId, onlineStatus) {
  return request({
    url: '/outmis/device/status/' + deviceId + '/' + onlineStatus,
    method: 'put'
  })
}

// 更新设备心跳
export function updateDeviceHeartbeat(deviceId) {
  return request({
    url: '/outmis/device/heartbeat/' + deviceId,
    method: 'put'
  })
}

// 获取设备统计信息
export function getDeviceStatistics() {
  return request({
    url: '/outmis/device/stats/overview',
    method: 'get'
  })
}

// 测试设备连接
export function testDeviceConnection(deviceId) {
  return request({
    url: '/outmis/device/test/' + deviceId,
    method: 'post'
  })
}

// 重启设备连接
export function restartDevice(deviceCode) {
  return request({
    url: '/outmis/device/restart/' + deviceCode,
    method: 'post'
  })
}

// 校准设备
export function calibrateDevice(deviceId) {
  return request({
    url: '/outmis/device/calibrate/' + deviceId,
    method: 'post'
  })
}

// 获取设备实时数据
export function getDeviceRealTimeData(deviceId) {
  return request({
    url: '/outmis/device/realtime/' + deviceId,
    method: 'get'
  })
}

// 断开设备连接
export function disconnectDevice(deviceCode) {
  return request({
    url: '/outmis/device/disconnect/' + deviceCode,
    method: 'post'
  })
}

// 获取设备实时状态
export function getDeviceStatus(deviceCode) {
  return request({
    url: '/outmis/device/status/' + deviceCode,
    method: 'get'
  })
}

// 手动同步设备信息
export function syncDevices() {
  return request({
    url: '/outmis/device/sync',
    method: 'post'
  })
}
