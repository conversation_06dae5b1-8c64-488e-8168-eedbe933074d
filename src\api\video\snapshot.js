import request from '@/utils/request'

// 查询截图记录列表
export function listSnapshot(query) {
  return request({
    url: '/video/snapshot/list',
    method: 'get',
    params: query
  })
}

// 查询截图记录详细
export function getSnapshot(id) {
  return request({
    url: '/video/snapshot/' + id,
    method: 'get'
  })
}

// 删除截图记录
export function delSnapshot(id) {
  return request({
    url: '/video/snapshot/' + id,
    method: 'delete'
  })
}

// 手动截图
export function captureSnapshot(data) {
  return request({
    url: '/video/snapshot/capture',
    method: 'post',
    data: data
  })
}

// 下载截图
export function downloadSnapshot(id) {
  return request({
    url: '/video/snapshot/download/' + id,
    method: 'get',
    responseType: 'blob'
  })
}

// 批量下载截图
export function batchDownloadSnapshot(ids) {
  return request({
    url: '/video/snapshot/batch/download',
    method: 'post',
    data: { ids: ids },
    responseType: 'blob'
  })
}

// 获取截图预览地址
export function getSnapshotPreview(id) {
  return request({
    url: '/video/snapshot/preview/' + id,
    method: 'get'
  })
}
