/**
 * 视频流状态监控工具
 */

class VideoStreamMonitor {
  constructor() {
    this.streams = new Map(); // 存储流状态信息
    this.callbacks = new Map(); // 存储回调函数
    this.checkInterval = 5000; // 检查间隔（毫秒）
    this.timer = null;
  }

  /**
   * 添加流监控
   * @param {string} streamId 流ID
   * @param {string} url 流地址
   * @param {Function} callback 状态变化回调
   */
  addStream(streamId, url, callback) {
    this.streams.set(streamId, {
      id: streamId,
      url: url,
      status: 'unknown', // unknown, online, offline, error
      lastCheck: null,
      errorCount: 0,
      startTime: Date.now()
    });

    if (callback) {
      this.callbacks.set(streamId, callback);
    }

    // 立即检查一次
    this.checkStream(streamId);

    // 启动定时检查
    this.startMonitoring();
  }

  /**
   * 移除流监控
   * @param {string} streamId 流ID
   */
  removeStream(streamId) {
    this.streams.delete(streamId);
    this.callbacks.delete(streamId);

    // 如果没有流需要监控，停止定时器
    if (this.streams.size === 0) {
      this.stopMonitoring();
    }
  }

  /**
   * 获取流状态
   * @param {string} streamId 流ID
   * @returns {Object} 流状态信息
   */
  getStreamStatus(streamId) {
    return this.streams.get(streamId);
  }

  /**
   * 获取所有流状态
   * @returns {Array} 所有流状态信息
   */
  getAllStreamStatus() {
    return Array.from(this.streams.values());
  }

  /**
   * 检查单个流状态
   * @param {string} streamId 流ID
   */
  async checkStream(streamId) {
    const stream = this.streams.get(streamId);
    if (!stream) return;

    try {
      const isOnline = await this.pingStream(stream.url);
      const oldStatus = stream.status;
      
      if (isOnline) {
        stream.status = 'online';
        stream.errorCount = 0;
      } else {
        stream.errorCount++;
        stream.status = stream.errorCount >= 3 ? 'offline' : 'error';
      }

      stream.lastCheck = Date.now();

      // 如果状态发生变化，触发回调
      if (oldStatus !== stream.status) {
        this.notifyStatusChange(streamId, stream);
      }

    } catch (error) {
      console.error(`检查流状态失败: ${streamId}`, error);
      stream.status = 'error';
      stream.errorCount++;
      stream.lastCheck = Date.now();
      
      this.notifyStatusChange(streamId, stream);
    }
  }

  /**
   * 检测流是否在线
   * @param {string} url 流地址
   * @returns {Promise<boolean>} 是否在线
   */
  async pingStream(url) {
    try {
      // 对于HTTP流，可以尝试HEAD请求
      if (url.startsWith('http')) {
        const response = await fetch(url, { 
          method: 'HEAD',
          timeout: 3000 
        });
        return response.ok;
      }
      
      // 对于其他协议，可以使用WebSocket或其他方式检测
      // 这里简化处理，实际应用中需要根据具体协议实现
      return true;
      
    } catch (error) {
      console.warn(`流检测失败: ${url}`, error);
      return false;
    }
  }

  /**
   * 通知状态变化
   * @param {string} streamId 流ID
   * @param {Object} stream 流信息
   */
  notifyStatusChange(streamId, stream) {
    const callback = this.callbacks.get(streamId);
    if (callback) {
      try {
        callback(stream);
      } catch (error) {
        console.error(`状态变化回调执行失败: ${streamId}`, error);
      }
    }
  }

  /**
   * 启动监控
   */
  startMonitoring() {
    if (this.timer) return;

    this.timer = setInterval(() => {
      this.streams.forEach((stream, streamId) => {
        this.checkStream(streamId);
      });
    }, this.checkInterval);
  }

  /**
   * 停止监控
   */
  stopMonitoring() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  }

  /**
   * 设置检查间隔
   * @param {number} interval 间隔时间（毫秒）
   */
  setCheckInterval(interval) {
    this.checkInterval = interval;
    
    // 重启监控以应用新间隔
    if (this.timer) {
      this.stopMonitoring();
      this.startMonitoring();
    }
  }

  /**
   * 获取流统计信息
   * @param {string} streamId 流ID
   * @returns {Object} 统计信息
   */
  getStreamStats(streamId) {
    const stream = this.streams.get(streamId);
    if (!stream) return null;

    const now = Date.now();
    const uptime = now - stream.startTime;
    const lastCheckAgo = stream.lastCheck ? now - stream.lastCheck : null;

    return {
      id: streamId,
      status: stream.status,
      uptime: uptime,
      lastCheck: lastCheckAgo,
      errorCount: stream.errorCount,
      url: stream.url
    };
  }

  /**
   * 重置流错误计数
   * @param {string} streamId 流ID
   */
  resetErrorCount(streamId) {
    const stream = this.streams.get(streamId);
    if (stream) {
      stream.errorCount = 0;
    }
  }

  /**
   * 销毁监控器
   */
  destroy() {
    this.stopMonitoring();
    this.streams.clear();
    this.callbacks.clear();
  }
}

// 创建全局实例
const videoStreamMonitor = new VideoStreamMonitor();

export default videoStreamMonitor;

// 导出类以便创建多个实例
export { VideoStreamMonitor };

/**
 * 使用示例：
 * 
 * import videoStreamMonitor from '@/utils/videoStreamMonitor';
 * 
 * // 添加流监控
 * videoStreamMonitor.addStream('stream1', 'http://localhost:8080/live/stream1.flv', (stream) => {
 *   console.log('流状态变化:', stream);
 * });
 * 
 * // 获取流状态
 * const status = videoStreamMonitor.getStreamStatus('stream1');
 * 
 * // 移除流监控
 * videoStreamMonitor.removeStream('stream1');
 */
