import request from '@/utils/request'

// 查询打印记录列表
export function listPrintRecord(query) {
  return request({
    url: '/inout/printRecord/list',
    method: 'get',
    params: query
  })
}

// 查询打印记录详细
export function getPrintRecord(id) {
  return request({
    url: '/inout/printRecord/' + id,
    method: 'get'
  })
}

// 新增打印记录
export function addPrintRecord(data) {
  return request({
    url: '/inout/printRecord',
    method: 'post',
    data: data
  })
}

// 修改打印记录
export function updatePrintRecord(data) {
  return request({
    url: '/inout/printRecord',
    method: 'put',
    data: data
  })
}

// 删除打印记录
export function delPrintRecord(recordId) {
  return request({
    url: '/inout/printRecord/' + recordId,
    method: 'delete'
  })
}

// 导出打印记录
export function exportPrintRecord(query) {
  return request({
    url: '/inout/printRecord/export',
    method: 'get',
    params: query
  })
}
