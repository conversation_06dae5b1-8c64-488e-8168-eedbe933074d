import request from '@/utils/request'

// 查询出库预警列表
export function listWarn(query) {
  return request({
    url: '/outmis/warn/list',
    method: 'get',
    params: query
  })
}

// 查询未处理的出库预警列表
export function listUnprocessedWarn(query) {
  return request({
    url: '/outmis/warn/unprocessed',
    method: 'get',
    params: query
  })
}

// 查询出库预警详细
export function getWarn(warnId) {
  return request({
    url: '/outmis/warn/' + warnId,
    method: 'get'
  })
}

// 新增出库预警
export function addWarn(data) {
  return request({
    url: '/outmis/warn',
    method: 'post',
    data: data
  })
}

// 修改出库预警
export function updateWarn(data) {
  return request({
    url: '/outmis/warn',
    method: 'put',
    data: data
  })
}

// 删除出库预警
export function delWarn(warnId) {
  return request({
    url: '/outmis/warn/' + warnId,
    method: 'delete'
  })
}

// 处理出库预警
export function processWarn(warnId, processRemark) {
  return request({
    url: '/outmis/warn/process/' + warnId,
    method: 'put',
    params: {
      processRemark: processRemark
    }
  })
}

// 批量处理出库预警
export function batchProcessWarn(warnIds, processRemark) {
  return request({
    url: '/outmis/warn/batch/process',
    method: 'put',
    params: {
      warnIds: warnIds,
      processRemark: processRemark
    }
  })
}
