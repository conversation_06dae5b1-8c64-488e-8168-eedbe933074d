import request from '@/utils/request'

// 获取门禁配置信息
export function getConfigInfo() {
  return request({
    url: '/access/config/info',
    method: 'get'
  })
}

// 获取配置摘要
export function getConfigSummary() {
  return request({
    url: '/access/config/summary',
    method: 'get'
  })
}

// 验证配置有效性
export function validateConfig() {
  return request({
    url: '/access/config/validate',
    method: 'get'
  })
}

// 重新加载配置
export function reloadConfig() {
  return request({
    url: '/access/config/reload',
    method: 'post'
  })
}

// 更新SDK配置
export function updateSdkConfig(data) {
  return request({
    url: '/access/config/sdk',
    method: 'put',
    data: data
  })
}

// 更新人脸识别配置
export function updateFaceConfig(data) {
  return request({
    url: '/access/config/face',
    method: 'put',
    data: data
  })
}

// 获取默认配置模板
export function getConfigTemplate() {
  return request({
    url: '/access/config/template',
    method: 'get'
  })
}

// 导出配置
export function exportConfig() {
  return request({
    url: '/access/config/export',
    method: 'get',
    responseType: 'blob'
  })
}

// 导入配置
export function importConfig(data) {
  return request({
    url: '/access/config/import',
    method: 'post',
    data: data
  })
}
