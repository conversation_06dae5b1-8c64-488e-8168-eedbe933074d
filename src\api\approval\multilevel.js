import request from '@/utils/request'

// 获取多级审批模板
export function getTemplate(levels) {
  return request({
    url: '/approval/multilevel/template/' + levels,
    method: 'get'
  })
}

// 创建多级审批工作流
export function createMultilevelWorkflow(data) {
  return request({
    url: '/approval/multilevel/workflow',
    method: 'post',
    data: data
  })
}

// 更新多级审批工作流
export function updateWorkflow(workflowId, levels) {
  return request({
    url: '/approval/multilevel/update/' + workflowId,
    method: 'put',
    data: levels
  })
}

// 获取工作流的审批级别信息
export function getWorkflowLevels(workflowId) {
  return request({
    url: '/approval/multilevel/levels/' + workflowId,
    method: 'get'
  })
}

// 计算业务审批级别
export function calculateApprovalLevel(data) {
  return request({
    url: '/approval/multilevel/calculate',
    method: 'post',
    data: data
  })
}

// 获取用户列表（用于审批人选择）
export function getUsers() {
  return request({
    url: '/approval/multilevel/users',
    method: 'get'
  })
}

// 获取角色列表（用于审批角色选择）
export function getRoles() {
  return request({
    url: '/approval/multilevel/roles',
    method: 'get'
  })
}

// 获取部门列表（用于审批部门选择）
export function getDepts() {
  return request({
    url: '/approval/multilevel/depts',
    method: 'get'
  })
}

// 预览审批流程
export function previewWorkflow(levels) {
  return request({
    url: '/approval/multilevel/preview',
    method: 'post',
    data: levels
  })
}

// 验证审批级别配置
export function validateConfig(config) {
  return request({
    url: '/approval/multilevel/validate-config',
    method: 'post',
    data: config
  })
}

// 导出多级审批配置
export function exportConfig(workflowId) {
  return request({
    url: '/approval/multilevel/export',
    method: 'post',
    data: { workflowId: workflowId },
    responseType: 'blob'
  })
}
