<template>
  <div class="level-config">
    <el-form :model="levelData" :rules="rules" ref="levelForm" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="级别名称" prop="levelName">
            <el-input v-model="levelData.levelName" placeholder="请输入级别名称" @input="handleUpdate" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="审批类型" prop="approvalType">
            <el-select v-model="levelData.approvalType" placeholder="请选择审批类型" @change="handleUpdate" style="width: 100%">
              <el-option label="指定人员" value="0" />
              <el-option label="指定角色" value="1" />
              <el-option label="指定部门" value="2" />
              <el-option label="部门主管" value="3" />
              <el-option label="上级主管" value="4" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 指定人员 -->
      <el-row v-if="levelData.approvalType === '0'" :gutter="20">
        <el-col :span="24">
          <el-form-item label="审批人员" prop="approvers">
            <el-select
              v-model="selectedUsers"
              multiple
              filterable
              placeholder="请选择审批人员"
              @change="handleUsersChange"
              style="width: 100%"
            >
              <el-option
                v-for="user in userList"
                :key="user.userId"
                :label="user.nickName"
                :value="user.userId"
              >
                <span style="float: left">{{ user.nickName }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ user.userName }}</span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 指定角色 -->
      <el-row v-if="levelData.approvalType === '1'" :gutter="20">
        <el-col :span="24">
          <el-form-item label="审批角色" prop="roleId">
            <el-select v-model="levelData.roleId" placeholder="请选择审批角色" @change="handleUpdate" style="width: 100%">
              <el-option
                v-for="role in roleList"
                :key="role.roleId"
                :label="role.roleName"
                :value="role.roleId"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 指定部门 -->
      <el-row v-if="levelData.approvalType === '2'" :gutter="20">
        <el-col :span="24">
          <el-form-item label="审批部门" prop="deptId">
            <el-cascader
              v-model="levelData.deptId"
              :options="deptOptions"
              :props="{ checkStrictly: true, value: 'id', label: 'label', children: 'children' }"
              placeholder="请选择审批部门"
              @change="handleUpdate"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="审批条件" prop="approvalCondition">
            <el-select v-model="levelData.approvalCondition" placeholder="请选择审批条件" @change="handleUpdate" style="width: 100%">
              <el-option label="任意一人同意" value="ANY" />
              <el-option label="全部人员同意" value="ALL" />
              <el-option label="按比例同意" value="RATIO" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="levelData.approvalCondition === 'RATIO'">
          <el-form-item label="同意比例" prop="approvalRatio">
            <el-input-number
              v-model="levelData.approvalRatio"
              :min="1"
              :max="100"
              placeholder="请输入同意比例"
              @change="handleUpdate"
              style="width: 100%"
            />
            <span style="margin-left: 8px;">%</span>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="超时处理" prop="timeoutAction">
            <el-select v-model="levelData.timeoutAction" placeholder="请选择超时处理方式" @change="handleUpdate" style="width: 100%">
              <el-option label="自动通过" value="1" />
              <el-option label="自动拒绝" value="2" />
              <el-option label="转交上级" value="3" />
              <el-option label="无操作" value="0" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="超时时间" prop="timeoutHours">
            <el-input-number
              v-model="levelData.timeoutHours"
              :min="1"
              :max="720"
              placeholder="请输入超时时间"
              @change="handleUpdate"
              style="width: 100%"
            />
            <span style="margin-left: 8px;">小时</span>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="级别描述">
            <el-input
              v-model="levelData.description"
              type="textarea"
              :rows="2"
              placeholder="请输入级别描述"
              @input="handleUpdate"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 审批人员预览 -->
      <el-row v-if="approverPreview.length > 0">
        <el-col :span="24">
          <el-form-item label="审批人员预览">
            <div class="approver-preview">
              <el-tag
                v-for="approver in approverPreview"
                :key="approver.id"
                type="info"
                size="small"
                style="margin-right: 8px; margin-bottom: 4px;"
              >
                {{ approver.name }}
              </el-tag>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { getUsers, getRoles, getDepts } from "@/api/approval/multilevel";

export default {
  name: "LevelConfig",
  props: {
    levelData: {
      type: Object,
      required: true
    },
    levelIndex: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      rules: {
        levelName: [
          { required: true, message: '请输入级别名称', trigger: 'blur' }
        ],
        approvalType: [
          { required: true, message: '请选择审批类型', trigger: 'change' }
        ],
        approvers: [
          { required: true, message: '请选择审批人员', trigger: 'change' }
        ],
        roleId: [
          { required: true, message: '请选择审批角色', trigger: 'change' }
        ],
        deptId: [
          { required: true, message: '请选择审批部门', trigger: 'change' }
        ]
      },
      userList: [],
      roleList: [],
      deptOptions: [],
      selectedUsers: [],
      approverPreview: []
    };
  },
  watch: {
    'levelData.approvers': {
      handler(newVal) {
        if (newVal && this.levelData.approvalType === '0') {
          this.selectedUsers = newVal.split(',').map(id => parseInt(id)).filter(id => !isNaN(id));
          this.updateApproverPreview();
        }
      },
      immediate: true
    },
    'levelData.roleId': {
      handler() {
        this.updateApproverPreview();
      }
    },
    'levelData.deptId': {
      handler() {
        this.updateApproverPreview();
      }
    }
  },
  created() {
    this.loadUsers();
    this.loadRoles();
    this.loadDepts();
  },
  methods: {
    /** 加载用户列表 */
    loadUsers() {
      getUsers().then(response => {
        this.userList = response.rows || [];
      });
    },

    /** 加载角色列表 */
    loadRoles() {
      getRoles().then(response => {
        this.roleList = response.rows || [];
      });
    },

    /** 加载部门列表 */
    loadDepts() {
      getDepts().then(response => {
        this.deptOptions = this.buildDeptTree(response.data || []);
      });
    },

    /** 构建部门树 */
    buildDeptTree(depts) {
      const tree = [];
      const map = {};

      // 创建映射
      depts.forEach(dept => {
        map[dept.deptId] = {
          id: dept.deptId,
          label: dept.deptName,
          children: []
        };
      });

      // 构建树结构
      depts.forEach(dept => {
        if (dept.parentId === 0) {
          tree.push(map[dept.deptId]);
        } else if (map[dept.parentId]) {
          map[dept.parentId].children.push(map[dept.deptId]);
        }
      });

      return tree;
    },

    /** 处理用户选择变化 */
    handleUsersChange(userIds) {
      this.levelData.approvers = userIds.join(',');
      this.handleUpdate();
    },

    /** 更新审批人员预览 */
    updateApproverPreview() {
      this.approverPreview = [];

      if (this.levelData.approvalType === '0' && this.selectedUsers.length > 0) {
        // 指定人员
        this.selectedUsers.forEach(userId => {
          const user = this.userList.find(u => u.userId === userId);
          if (user) {
            this.approverPreview.push({
              id: user.userId,
              name: user.nickName
            });
          }
        });
      } else if (this.levelData.approvalType === '1' && this.levelData.roleId) {
        // 指定角色
        const role = this.roleList.find(r => r.roleId === this.levelData.roleId);
        if (role) {
          this.approverPreview.push({
            id: role.roleId,
            name: `角色: ${role.roleName}`
          });
        }
      } else if (this.levelData.approvalType === '2' && this.levelData.deptId) {
        // 指定部门
        this.approverPreview.push({
          id: this.levelData.deptId,
          name: `部门: ${this.getDeptName(this.levelData.deptId)}`
        });
      } else if (this.levelData.approvalType === '3') {
        // 部门主管
        this.approverPreview.push({
          id: 'dept_manager',
          name: '部门主管'
        });
      } else if (this.levelData.approvalType === '4') {
        // 上级主管
        this.approverPreview.push({
          id: 'superior_manager',
          name: '上级主管'
        });
      }
    },

    /** 获取部门名称 */
    getDeptName(deptId) {
      const findDept = (options, id) => {
        for (const option of options) {
          if (option.id === id) {
            return option.label;
          }
          if (option.children && option.children.length > 0) {
            const found = findDept(option.children, id);
            if (found) return found;
          }
        }
        return null;
      };
      return findDept(this.deptOptions, deptId) || '未知部门';
    },

    /** 处理数据更新 */
    handleUpdate() {
      this.$emit('update', this.levelIndex, { ...this.levelData });
      this.updateApproverPreview();
    }
  }
};
</script>

<style scoped>
.level-config {
  padding: 20px;
}

.approver-preview {
  min-height: 32px;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}
</style>
