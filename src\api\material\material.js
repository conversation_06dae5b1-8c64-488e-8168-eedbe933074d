import request from '@/utils/request'

// 查询物料列表
export function listMaterial(query) {
  return request({
    url: '/material/material/list',
    method: 'get',
    params: query
  })
}

// 查询物料详细
export function getMaterial(id) {
  return request({
    url: '/material/material/' + id,
    method: 'get'
  })
}

// 根据物料编码查询
export function getMaterialByCode(code) {
  return request({
    url: '/material/material/code',
    method: 'get',
    params: { code: code }
  })
}

// 根据外部系统编码查询
export function getMaterialByExternalCode(code, system) {
  return request({
    url: '/material/material/external',
    method: 'get',
    params: {
      code: code,
      system: system
    }
  })
}

// 通过映射信息查询物料
export function getMaterialByMapping(externalCode, externalSystem) {
  return request({
    url: '/material/material/byMapping',
    method: 'get',
    params: {
      externalCode: externalCode,
      externalSystem: externalSystem
    }
  })
}

// 获取物料详细信息(包含类别、重量等信息)
export function getMaterialDetail(id) {
  return request({
    url: '/material/material/detail/' + id,
    method: 'get'
  }).catch(error => {
    console.warn('获取物料详情失败，尝试降级到基本信息接口', error);
    // 如果详情接口失败，尝试降级到基本信息接口
    return getMaterial(id);
  });
}

// 新增物料
export function addMaterial(data) {
  return request({
    url: '/material/material',
    method: 'post',
    data: data
  })
}

// 修改物料
export function updateMaterial(data) {
  return request({
    url: '/material/material',
    method: 'put',
    data: data
  })
}

// 修改物料状态
export function changeMaterialStatus(data) {
  return request({
    url: '/material/material/changeStatus',
    method: 'put',
    data: data
  })
}

// 删除物料
export function delMaterial(id) {
  return request({
    url: '/material/material/' + id,
    method: 'delete'
  })
}



// 导出物料
export function exportMaterial(query) {
  return request({
    url: '/material/material/export',
    method: 'get',
    params: query
  })
}

// 获取导入模板
export function importTemplate() {
  return request({
    url: '/material/material/importTemplate',
    method: 'get',
    responseType: 'blob'
  })
}

// 物料审批通过
export function approveMaterial(id) {
  return request({
    url: '/material/material/approve/' + id,
    method: 'put'
  })
}

// 物料审批驳回
export function rejectMaterial(data) {
  return request({
    url: '/material/material/reject',
    method: 'put',
    data: data
  })
}

// 物料验收完成
export function completeMaterial(id) {
  return request({
    url: '/material/material/complete/' + id,
    method: 'put'
  })
}

// 批量物料审批
export function batchApproveMaterial(data) {
  return request({
    url: '/material/material/batchApprove',
    method: 'put',
    data: data
  })
}
