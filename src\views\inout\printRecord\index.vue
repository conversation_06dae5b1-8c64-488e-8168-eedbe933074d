<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="业务类型" prop="businessType">
        <el-select v-model="queryParams.businessType" placeholder="请选择业务类型" clearable>
          <el-option label="出入库" value="inout" />
          <el-option label="盘点" value="inventory" />
        </el-select>
      </el-form-item>
      <el-form-item label="业务单号" prop="businessNo">
        <el-input
          v-model="queryParams.businessNo"
          placeholder="请输入业务单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="打印用户" prop="printUserName">
        <el-input
          v-model="queryParams.printUserName"
          placeholder="请输入打印用户"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="打印时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['inout:printRecord:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="printRecordList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="记录ID" align="center" prop="id" />
      <el-table-column label="业务类型" align="center" prop="businessType">
        <template slot-scope="scope">
          <span v-if="scope.row.businessType === 'inout'">出入库</span>
          <span v-else-if="scope.row.businessType === 'inventory'">盘点</span>
          <span v-else>{{ scope.row.businessType }}</span>
        </template>
      </el-table-column>
      <el-table-column label="业务单号" align="center" prop="businessNo" />
      <el-table-column label="模板名称" align="center" prop="templateName" />
      <el-table-column label="打印用户" align="center" prop="printUserName" />
      <el-table-column label="打印时间" align="center" prop="printTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.printTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >查看内容</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看打印内容对话框 -->
    <el-dialog title="打印内容" :visible.sync="viewOpen" width="80%" append-to-body>
      <div v-html="printContent" style="max-height: 500px; overflow-y: auto; border: 1px solid #ddd; padding: 20px;"></div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPrintRecord, exportPrintRecord } from "@/api/inout/printRecord";

export default {
  name: "PrintRecord",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 打印记录表格数据
      printRecordList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查看内容弹出层
      viewOpen: false,
      // 打印内容
      printContent: "",
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        businessType: null,
        businessNo: null,
        printUserName: null,
      },
      // 表单参数
      form: {
        id: null,
        businessId: null,
        businessType: null,
        businessNo: null,
        templateId: null,
        templateName: null,
        printContent: null,
        printUserName: null,
        printTime: null
      },
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询打印记录列表 */
    getList() {
      this.loading = true;
      listPrintRecord(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.printRecordList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        businessId: null,
        businessType: null,
        businessNo: null,
        templateId: null,
        templateName: null,
        printContent: null,
        printUserName: null,
        printTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 查看打印内容 */
    handleView(row) {
      this.printContent = row.printContent;
      this.viewOpen = true;
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('inout/printRecord/export', {
        ...this.queryParams
      }, `printRecord_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
