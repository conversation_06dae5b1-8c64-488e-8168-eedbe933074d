import request from '@/utils/request'

// MQTT Broker 管理 API

/**
 * 获取 MQTT Broker 状态
 */
export function getBrokerStatus() {
  return request({
    url: '/api/mqtt/status',
    method: 'get'
  })
}

/**
 * 获取 MQTT Broker 统计信息
 */
export function getBrokerStatistics() {
  return request({
    url: '/api/mqtt/statistics',
    method: 'get'
  })
}

/**
 * 获取 MQTT Broker 配置
 */
export function getBrokerConfig() {
  return request({
    url: '/api/mqtt/config',
    method: 'get'
  })
}

/**
 * 更新 MQTT Broker 配置
 */
export function updateBrokerConfig(data) {
  return request({
    url: '/api/mqtt/config',
    method: 'put',
    data
  })
}

/**
 * 重启 MQTT Broker
 */
export function restartBroker() {
  return request({
    url: '/api/mqtt/restart',
    method: 'post'
  })
}

/**
 * 停止 MQTT Broker
 */
export function stopBroker() {
  return request({
    url: '/api/mqtt/stop',
    method: 'post'
  })
}

/**
 * 启动 MQTT Broker
 */
export function startBroker() {
  return request({
    url: '/api/mqtt/start',
    method: 'post'
  })
}

/**
 * 获取系统健康状态
 */
export function getHealthStatus() {
  return request({
    url: '/api/mqtt/health',
    method: 'get'
  })
}

/**
 * 获取系统日志
 */
export function getSystemLogs(params) {
  return request({
    url: '/api/mqtt/logs',
    method: 'get',
    params
  })
}

/**
 * 清理系统日志
 */
export function clearSystemLogs() {
  return request({
    url: '/api/mqtt/logs/clear',
    method: 'delete'
  })
}
